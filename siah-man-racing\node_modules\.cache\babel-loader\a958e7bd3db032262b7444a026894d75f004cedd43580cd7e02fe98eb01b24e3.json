{"ast": null, "code": "import _extends from '@babel/runtime/helpers/esm/extends';\nimport * as React from 'react';\nimport { useThree, use<PERSON>rame, createPortal, applyProps, extend } from '@react-three/fiber';\nimport { Scene, WebGLCubeRenderTarget, HalfFloatType } from 'three';\nimport { GroundProjectedEnv } from 'three-stdlib';\nimport { useEnvironment } from './useEnvironment.js';\nconst isRef = obj => obj.current && obj.current.isScene;\nconst resolveScene = scene => isRef(scene) ? scene.current : scene;\nfunction setEnvProps(background, scene, defaultScene, texture, sceneProps = {}) {\n  var _target$backgroundRot, _target$backgroundRot2, _target$environmentRo, _target$environmentRo2;\n  // defaults\n  sceneProps = {\n    backgroundBlurriness: 0,\n    backgroundIntensity: 1,\n    backgroundRotation: [0, 0, 0],\n    environmentIntensity: 1,\n    environmentRotation: [0, 0, 0],\n    ...sceneProps\n  };\n  const target = resolveScene(scene || defaultScene);\n  const oldbg = target.background;\n  const oldenv = target.environment;\n  const oldSceneProps = {\n    // @ts-ignore\n    backgroundBlurriness: target.backgroundBlurriness,\n    // @ts-ignore\n    backgroundIntensity: target.backgroundIntensity,\n    // @ts-ignore\n    backgroundRotation: (_target$backgroundRot = (_target$backgroundRot2 = target.backgroundRotation) == null || _target$backgroundRot2.clone == null ? void 0 : _target$backgroundRot2.clone()) !== null && _target$backgroundRot !== void 0 ? _target$backgroundRot : [0, 0, 0],\n    // @ts-ignore\n    environmentIntensity: target.environmentIntensity,\n    // @ts-ignore\n    environmentRotation: (_target$environmentRo = (_target$environmentRo2 = target.environmentRotation) == null || _target$environmentRo2.clone == null ? void 0 : _target$environmentRo2.clone()) !== null && _target$environmentRo !== void 0 ? _target$environmentRo : [0, 0, 0]\n  };\n  if (background !== 'only') target.environment = texture;\n  if (background) target.background = texture;\n  applyProps(target, sceneProps);\n  return () => {\n    if (background !== 'only') target.environment = oldenv;\n    if (background) target.background = oldbg;\n    applyProps(target, oldSceneProps);\n  };\n}\nfunction EnvironmentMap({\n  scene,\n  background = false,\n  map,\n  ...config\n}) {\n  const defaultScene = useThree(state => state.scene);\n  React.useLayoutEffect(() => {\n    if (map) return setEnvProps(background, scene, defaultScene, map, config);\n  });\n  return null;\n}\nfunction EnvironmentCube({\n  background = false,\n  scene,\n  blur,\n  backgroundBlurriness,\n  backgroundIntensity,\n  backgroundRotation,\n  environmentIntensity,\n  environmentRotation,\n  ...rest\n}) {\n  const texture = useEnvironment(rest);\n  const defaultScene = useThree(state => state.scene);\n  React.useLayoutEffect(() => {\n    return setEnvProps(background, scene, defaultScene, texture, {\n      backgroundBlurriness: blur !== null && blur !== void 0 ? blur : backgroundBlurriness,\n      backgroundIntensity,\n      backgroundRotation,\n      environmentIntensity,\n      environmentRotation\n    });\n  });\n  React.useEffect(() => {\n    return () => {\n      texture.dispose();\n    };\n  }, [texture]);\n  return null;\n}\nfunction EnvironmentPortal({\n  children,\n  near = 0.1,\n  far = 1000,\n  resolution = 256,\n  frames = 1,\n  map,\n  background = false,\n  blur,\n  backgroundBlurriness,\n  backgroundIntensity,\n  backgroundRotation,\n  environmentIntensity,\n  environmentRotation,\n  scene,\n  files,\n  path,\n  preset = undefined,\n  extensions\n}) {\n  const gl = useThree(state => state.gl);\n  const defaultScene = useThree(state => state.scene);\n  const camera = React.useRef(null);\n  const [virtualScene] = React.useState(() => new Scene());\n  const fbo = React.useMemo(() => {\n    const fbo = new WebGLCubeRenderTarget(resolution);\n    fbo.texture.type = HalfFloatType;\n    return fbo;\n  }, [resolution]);\n  React.useEffect(() => {\n    return () => {\n      fbo.dispose();\n    };\n  }, [fbo]);\n  React.useLayoutEffect(() => {\n    if (frames === 1) {\n      const autoClear = gl.autoClear;\n      gl.autoClear = true;\n      camera.current.update(gl, virtualScene);\n      gl.autoClear = autoClear;\n    }\n    return setEnvProps(background, scene, defaultScene, fbo.texture, {\n      backgroundBlurriness: blur !== null && blur !== void 0 ? blur : backgroundBlurriness,\n      backgroundIntensity,\n      backgroundRotation,\n      environmentIntensity,\n      environmentRotation\n    });\n  }, [children, virtualScene, fbo.texture, scene, defaultScene, background, frames, gl]);\n  let count = 1;\n  useFrame(() => {\n    if (frames === Infinity || count < frames) {\n      const autoClear = gl.autoClear;\n      gl.autoClear = true;\n      camera.current.update(gl, virtualScene);\n      gl.autoClear = autoClear;\n      count++;\n    }\n  });\n  return /*#__PURE__*/React.createElement(React.Fragment, null, createPortal(/*#__PURE__*/React.createElement(React.Fragment, null, children, /*#__PURE__*/React.createElement(\"cubeCamera\", {\n    ref: camera,\n    args: [near, far, fbo]\n  }), files || preset ? /*#__PURE__*/React.createElement(EnvironmentCube, {\n    background: true,\n    files: files,\n    preset: preset,\n    path: path,\n    extensions: extensions\n  }) : map ? /*#__PURE__*/React.createElement(EnvironmentMap, {\n    background: true,\n    map: map,\n    extensions: extensions\n  }) : null), virtualScene));\n}\nfunction EnvironmentGround(props) {\n  var _props$ground, _props$ground2, _scale, _props$ground3;\n  const textureDefault = useEnvironment(props);\n  const texture = props.map || textureDefault;\n  React.useMemo(() => extend({\n    GroundProjectedEnvImpl: GroundProjectedEnv\n  }), []);\n  React.useEffect(() => {\n    return () => {\n      textureDefault.dispose();\n    };\n  }, [textureDefault]);\n  const args = React.useMemo(() => [texture], [texture]);\n  const height = (_props$ground = props.ground) == null ? void 0 : _props$ground.height;\n  const radius = (_props$ground2 = props.ground) == null ? void 0 : _props$ground2.radius;\n  const scale = (_scale = (_props$ground3 = props.ground) == null ? void 0 : _props$ground3.scale) !== null && _scale !== void 0 ? _scale : 1000;\n  return /*#__PURE__*/React.createElement(React.Fragment, null, /*#__PURE__*/React.createElement(EnvironmentMap, _extends({}, props, {\n    map: texture\n  })), /*#__PURE__*/React.createElement(\"groundProjectedEnvImpl\", {\n    args: args,\n    scale: scale,\n    height: height,\n    radius: radius\n  }));\n}\nfunction Environment(props) {\n  return props.ground ? /*#__PURE__*/React.createElement(EnvironmentGround, props) : props.map ? /*#__PURE__*/React.createElement(EnvironmentMap, props) : props.children ? /*#__PURE__*/React.createElement(EnvironmentPortal, props) : /*#__PURE__*/React.createElement(EnvironmentCube, props);\n}\nexport { Environment, EnvironmentCube, EnvironmentMap, EnvironmentPortal };", "map": {"version": 3, "names": ["_extends", "React", "useThree", "useFrame", "createPortal", "applyProps", "extend", "Scene", "WebGLCubeRenderTarget", "HalfFloatType", "GroundProjectedEnv", "useEnvironment", "isRef", "obj", "current", "isScene", "resolveScene", "scene", "setEnvProps", "background", "defaultScene", "texture", "sceneProps", "_target$backgroundRot", "_target$backgroundRot2", "_target$environmentRo", "_target$environmentRo2", "backgroundBlurriness", "backgroundIntensity", "backgroundRotation", "environmentIntensity", "environmentRotation", "target", "oldbg", "oldenv", "environment", "oldSceneProps", "clone", "EnvironmentMap", "map", "config", "state", "useLayoutEffect", "EnvironmentCube", "blur", "rest", "useEffect", "dispose", "EnvironmentPortal", "children", "near", "far", "resolution", "frames", "files", "path", "preset", "undefined", "extensions", "gl", "camera", "useRef", "virtualScene", "useState", "fbo", "useMemo", "type", "autoClear", "update", "count", "Infinity", "createElement", "Fragment", "ref", "args", "EnvironmentGround", "props", "_props$ground", "_props$ground2", "_scale", "_props$ground3", "textureDefault", "GroundProjectedEnvImpl", "height", "ground", "radius", "scale", "Environment"], "sources": ["C:/Users/<USER>/Documents/Apps_Creations/SiahMan_Racing4Speed_WebGame/siah-man-racing/node_modules/@react-three/drei/core/Environment.js"], "sourcesContent": ["import _extends from '@babel/runtime/helpers/esm/extends';\nimport * as React from 'react';\nimport { useThree, use<PERSON>rame, createPortal, applyProps, extend } from '@react-three/fiber';\nimport { Scene, WebGLCubeRenderTarget, HalfFloatType } from 'three';\nimport { GroundProjectedEnv } from 'three-stdlib';\nimport { useEnvironment } from './useEnvironment.js';\n\nconst isRef = obj => obj.current && obj.current.isScene;\nconst resolveScene = scene => isRef(scene) ? scene.current : scene;\nfunction setEnvProps(background, scene, defaultScene, texture, sceneProps = {}) {\n  var _target$backgroundRot, _target$backgroundRot2, _target$environmentRo, _target$environmentRo2;\n  // defaults\n  sceneProps = {\n    backgroundBlurriness: 0,\n    backgroundIntensity: 1,\n    backgroundRotation: [0, 0, 0],\n    environmentIntensity: 1,\n    environmentRotation: [0, 0, 0],\n    ...sceneProps\n  };\n  const target = resolveScene(scene || defaultScene);\n  const oldbg = target.background;\n  const oldenv = target.environment;\n  const oldSceneProps = {\n    // @ts-ignore\n    backgroundBlurriness: target.backgroundBlurriness,\n    // @ts-ignore\n    backgroundIntensity: target.backgroundIntensity,\n    // @ts-ignore\n    backgroundRotation: (_target$backgroundRot = (_target$backgroundRot2 = target.backgroundRotation) == null || _target$backgroundRot2.clone == null ? void 0 : _target$backgroundRot2.clone()) !== null && _target$backgroundRot !== void 0 ? _target$backgroundRot : [0, 0, 0],\n    // @ts-ignore\n    environmentIntensity: target.environmentIntensity,\n    // @ts-ignore\n    environmentRotation: (_target$environmentRo = (_target$environmentRo2 = target.environmentRotation) == null || _target$environmentRo2.clone == null ? void 0 : _target$environmentRo2.clone()) !== null && _target$environmentRo !== void 0 ? _target$environmentRo : [0, 0, 0]\n  };\n  if (background !== 'only') target.environment = texture;\n  if (background) target.background = texture;\n  applyProps(target, sceneProps);\n  return () => {\n    if (background !== 'only') target.environment = oldenv;\n    if (background) target.background = oldbg;\n    applyProps(target, oldSceneProps);\n  };\n}\nfunction EnvironmentMap({\n  scene,\n  background = false,\n  map,\n  ...config\n}) {\n  const defaultScene = useThree(state => state.scene);\n  React.useLayoutEffect(() => {\n    if (map) return setEnvProps(background, scene, defaultScene, map, config);\n  });\n  return null;\n}\nfunction EnvironmentCube({\n  background = false,\n  scene,\n  blur,\n  backgroundBlurriness,\n  backgroundIntensity,\n  backgroundRotation,\n  environmentIntensity,\n  environmentRotation,\n  ...rest\n}) {\n  const texture = useEnvironment(rest);\n  const defaultScene = useThree(state => state.scene);\n  React.useLayoutEffect(() => {\n    return setEnvProps(background, scene, defaultScene, texture, {\n      backgroundBlurriness: blur !== null && blur !== void 0 ? blur : backgroundBlurriness,\n      backgroundIntensity,\n      backgroundRotation,\n      environmentIntensity,\n      environmentRotation\n    });\n  });\n  React.useEffect(() => {\n    return () => {\n      texture.dispose();\n    };\n  }, [texture]);\n  return null;\n}\nfunction EnvironmentPortal({\n  children,\n  near = 0.1,\n  far = 1000,\n  resolution = 256,\n  frames = 1,\n  map,\n  background = false,\n  blur,\n  backgroundBlurriness,\n  backgroundIntensity,\n  backgroundRotation,\n  environmentIntensity,\n  environmentRotation,\n  scene,\n  files,\n  path,\n  preset = undefined,\n  extensions\n}) {\n  const gl = useThree(state => state.gl);\n  const defaultScene = useThree(state => state.scene);\n  const camera = React.useRef(null);\n  const [virtualScene] = React.useState(() => new Scene());\n  const fbo = React.useMemo(() => {\n    const fbo = new WebGLCubeRenderTarget(resolution);\n    fbo.texture.type = HalfFloatType;\n    return fbo;\n  }, [resolution]);\n  React.useEffect(() => {\n    return () => {\n      fbo.dispose();\n    };\n  }, [fbo]);\n  React.useLayoutEffect(() => {\n    if (frames === 1) {\n      const autoClear = gl.autoClear;\n      gl.autoClear = true;\n      camera.current.update(gl, virtualScene);\n      gl.autoClear = autoClear;\n    }\n    return setEnvProps(background, scene, defaultScene, fbo.texture, {\n      backgroundBlurriness: blur !== null && blur !== void 0 ? blur : backgroundBlurriness,\n      backgroundIntensity,\n      backgroundRotation,\n      environmentIntensity,\n      environmentRotation\n    });\n  }, [children, virtualScene, fbo.texture, scene, defaultScene, background, frames, gl]);\n  let count = 1;\n  useFrame(() => {\n    if (frames === Infinity || count < frames) {\n      const autoClear = gl.autoClear;\n      gl.autoClear = true;\n      camera.current.update(gl, virtualScene);\n      gl.autoClear = autoClear;\n      count++;\n    }\n  });\n  return /*#__PURE__*/React.createElement(React.Fragment, null, createPortal(/*#__PURE__*/React.createElement(React.Fragment, null, children, /*#__PURE__*/React.createElement(\"cubeCamera\", {\n    ref: camera,\n    args: [near, far, fbo]\n  }), files || preset ? /*#__PURE__*/React.createElement(EnvironmentCube, {\n    background: true,\n    files: files,\n    preset: preset,\n    path: path,\n    extensions: extensions\n  }) : map ? /*#__PURE__*/React.createElement(EnvironmentMap, {\n    background: true,\n    map: map,\n    extensions: extensions\n  }) : null), virtualScene));\n}\nfunction EnvironmentGround(props) {\n  var _props$ground, _props$ground2, _scale, _props$ground3;\n  const textureDefault = useEnvironment(props);\n  const texture = props.map || textureDefault;\n  React.useMemo(() => extend({\n    GroundProjectedEnvImpl: GroundProjectedEnv\n  }), []);\n  React.useEffect(() => {\n    return () => {\n      textureDefault.dispose();\n    };\n  }, [textureDefault]);\n  const args = React.useMemo(() => [texture], [texture]);\n  const height = (_props$ground = props.ground) == null ? void 0 : _props$ground.height;\n  const radius = (_props$ground2 = props.ground) == null ? void 0 : _props$ground2.radius;\n  const scale = (_scale = (_props$ground3 = props.ground) == null ? void 0 : _props$ground3.scale) !== null && _scale !== void 0 ? _scale : 1000;\n  return /*#__PURE__*/React.createElement(React.Fragment, null, /*#__PURE__*/React.createElement(EnvironmentMap, _extends({}, props, {\n    map: texture\n  })), /*#__PURE__*/React.createElement(\"groundProjectedEnvImpl\", {\n    args: args,\n    scale: scale,\n    height: height,\n    radius: radius\n  }));\n}\nfunction Environment(props) {\n  return props.ground ? /*#__PURE__*/React.createElement(EnvironmentGround, props) : props.map ? /*#__PURE__*/React.createElement(EnvironmentMap, props) : props.children ? /*#__PURE__*/React.createElement(EnvironmentPortal, props) : /*#__PURE__*/React.createElement(EnvironmentCube, props);\n}\n\nexport { Environment, EnvironmentCube, EnvironmentMap, EnvironmentPortal };\n"], "mappings": "AAAA,OAAOA,QAAQ,MAAM,oCAAoC;AACzD,OAAO,KAAKC,KAAK,MAAM,OAAO;AAC9B,SAASC,QAAQ,EAAEC,QAAQ,EAAEC,YAAY,EAAEC,UAAU,EAAEC,MAAM,QAAQ,oBAAoB;AACzF,SAASC,KAAK,EAAEC,qBAAqB,EAAEC,aAAa,QAAQ,OAAO;AACnE,SAASC,kBAAkB,QAAQ,cAAc;AACjD,SAASC,cAAc,QAAQ,qBAAqB;AAEpD,MAAMC,KAAK,GAAGC,GAAG,IAAIA,GAAG,CAACC,OAAO,IAAID,GAAG,CAACC,OAAO,CAACC,OAAO;AACvD,MAAMC,YAAY,GAAGC,KAAK,IAAIL,KAAK,CAACK,KAAK,CAAC,GAAGA,KAAK,CAACH,OAAO,GAAGG,KAAK;AAClE,SAASC,WAAWA,CAACC,UAAU,EAAEF,KAAK,EAAEG,YAAY,EAAEC,OAAO,EAAEC,UAAU,GAAG,CAAC,CAAC,EAAE;EAC9E,IAAIC,qBAAqB,EAAEC,sBAAsB,EAAEC,qBAAqB,EAAEC,sBAAsB;EAChG;EACAJ,UAAU,GAAG;IACXK,oBAAoB,EAAE,CAAC;IACvBC,mBAAmB,EAAE,CAAC;IACtBC,kBAAkB,EAAE,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC;IAC7BC,oBAAoB,EAAE,CAAC;IACvBC,mBAAmB,EAAE,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC;IAC9B,GAAGT;EACL,CAAC;EACD,MAAMU,MAAM,GAAGhB,YAAY,CAACC,KAAK,IAAIG,YAAY,CAAC;EAClD,MAAMa,KAAK,GAAGD,MAAM,CAACb,UAAU;EAC/B,MAAMe,MAAM,GAAGF,MAAM,CAACG,WAAW;EACjC,MAAMC,aAAa,GAAG;IACpB;IACAT,oBAAoB,EAAEK,MAAM,CAACL,oBAAoB;IACjD;IACAC,mBAAmB,EAAEI,MAAM,CAACJ,mBAAmB;IAC/C;IACAC,kBAAkB,EAAE,CAACN,qBAAqB,GAAG,CAACC,sBAAsB,GAAGQ,MAAM,CAACH,kBAAkB,KAAK,IAAI,IAAIL,sBAAsB,CAACa,KAAK,IAAI,IAAI,GAAG,KAAK,CAAC,GAAGb,sBAAsB,CAACa,KAAK,CAAC,CAAC,MAAM,IAAI,IAAId,qBAAqB,KAAK,KAAK,CAAC,GAAGA,qBAAqB,GAAG,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC;IAC7Q;IACAO,oBAAoB,EAAEE,MAAM,CAACF,oBAAoB;IACjD;IACAC,mBAAmB,EAAE,CAACN,qBAAqB,GAAG,CAACC,sBAAsB,GAAGM,MAAM,CAACD,mBAAmB,KAAK,IAAI,IAAIL,sBAAsB,CAACW,KAAK,IAAI,IAAI,GAAG,KAAK,CAAC,GAAGX,sBAAsB,CAACW,KAAK,CAAC,CAAC,MAAM,IAAI,IAAIZ,qBAAqB,KAAK,KAAK,CAAC,GAAGA,qBAAqB,GAAG,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC;EAChR,CAAC;EACD,IAAIN,UAAU,KAAK,MAAM,EAAEa,MAAM,CAACG,WAAW,GAAGd,OAAO;EACvD,IAAIF,UAAU,EAAEa,MAAM,CAACb,UAAU,GAAGE,OAAO;EAC3ChB,UAAU,CAAC2B,MAAM,EAAEV,UAAU,CAAC;EAC9B,OAAO,MAAM;IACX,IAAIH,UAAU,KAAK,MAAM,EAAEa,MAAM,CAACG,WAAW,GAAGD,MAAM;IACtD,IAAIf,UAAU,EAAEa,MAAM,CAACb,UAAU,GAAGc,KAAK;IACzC5B,UAAU,CAAC2B,MAAM,EAAEI,aAAa,CAAC;EACnC,CAAC;AACH;AACA,SAASE,cAAcA,CAAC;EACtBrB,KAAK;EACLE,UAAU,GAAG,KAAK;EAClBoB,GAAG;EACH,GAAGC;AACL,CAAC,EAAE;EACD,MAAMpB,YAAY,GAAGlB,QAAQ,CAACuC,KAAK,IAAIA,KAAK,CAACxB,KAAK,CAAC;EACnDhB,KAAK,CAACyC,eAAe,CAAC,MAAM;IAC1B,IAAIH,GAAG,EAAE,OAAOrB,WAAW,CAACC,UAAU,EAAEF,KAAK,EAAEG,YAAY,EAAEmB,GAAG,EAAEC,MAAM,CAAC;EAC3E,CAAC,CAAC;EACF,OAAO,IAAI;AACb;AACA,SAASG,eAAeA,CAAC;EACvBxB,UAAU,GAAG,KAAK;EAClBF,KAAK;EACL2B,IAAI;EACJjB,oBAAoB;EACpBC,mBAAmB;EACnBC,kBAAkB;EAClBC,oBAAoB;EACpBC,mBAAmB;EACnB,GAAGc;AACL,CAAC,EAAE;EACD,MAAMxB,OAAO,GAAGV,cAAc,CAACkC,IAAI,CAAC;EACpC,MAAMzB,YAAY,GAAGlB,QAAQ,CAACuC,KAAK,IAAIA,KAAK,CAACxB,KAAK,CAAC;EACnDhB,KAAK,CAACyC,eAAe,CAAC,MAAM;IAC1B,OAAOxB,WAAW,CAACC,UAAU,EAAEF,KAAK,EAAEG,YAAY,EAAEC,OAAO,EAAE;MAC3DM,oBAAoB,EAAEiB,IAAI,KAAK,IAAI,IAAIA,IAAI,KAAK,KAAK,CAAC,GAAGA,IAAI,GAAGjB,oBAAoB;MACpFC,mBAAmB;MACnBC,kBAAkB;MAClBC,oBAAoB;MACpBC;IACF,CAAC,CAAC;EACJ,CAAC,CAAC;EACF9B,KAAK,CAAC6C,SAAS,CAAC,MAAM;IACpB,OAAO,MAAM;MACXzB,OAAO,CAAC0B,OAAO,CAAC,CAAC;IACnB,CAAC;EACH,CAAC,EAAE,CAAC1B,OAAO,CAAC,CAAC;EACb,OAAO,IAAI;AACb;AACA,SAAS2B,iBAAiBA,CAAC;EACzBC,QAAQ;EACRC,IAAI,GAAG,GAAG;EACVC,GAAG,GAAG,IAAI;EACVC,UAAU,GAAG,GAAG;EAChBC,MAAM,GAAG,CAAC;EACVd,GAAG;EACHpB,UAAU,GAAG,KAAK;EAClByB,IAAI;EACJjB,oBAAoB;EACpBC,mBAAmB;EACnBC,kBAAkB;EAClBC,oBAAoB;EACpBC,mBAAmB;EACnBd,KAAK;EACLqC,KAAK;EACLC,IAAI;EACJC,MAAM,GAAGC,SAAS;EAClBC;AACF,CAAC,EAAE;EACD,MAAMC,EAAE,GAAGzD,QAAQ,CAACuC,KAAK,IAAIA,KAAK,CAACkB,EAAE,CAAC;EACtC,MAAMvC,YAAY,GAAGlB,QAAQ,CAACuC,KAAK,IAAIA,KAAK,CAACxB,KAAK,CAAC;EACnD,MAAM2C,MAAM,GAAG3D,KAAK,CAAC4D,MAAM,CAAC,IAAI,CAAC;EACjC,MAAM,CAACC,YAAY,CAAC,GAAG7D,KAAK,CAAC8D,QAAQ,CAAC,MAAM,IAAIxD,KAAK,CAAC,CAAC,CAAC;EACxD,MAAMyD,GAAG,GAAG/D,KAAK,CAACgE,OAAO,CAAC,MAAM;IAC9B,MAAMD,GAAG,GAAG,IAAIxD,qBAAqB,CAAC4C,UAAU,CAAC;IACjDY,GAAG,CAAC3C,OAAO,CAAC6C,IAAI,GAAGzD,aAAa;IAChC,OAAOuD,GAAG;EACZ,CAAC,EAAE,CAACZ,UAAU,CAAC,CAAC;EAChBnD,KAAK,CAAC6C,SAAS,CAAC,MAAM;IACpB,OAAO,MAAM;MACXkB,GAAG,CAACjB,OAAO,CAAC,CAAC;IACf,CAAC;EACH,CAAC,EAAE,CAACiB,GAAG,CAAC,CAAC;EACT/D,KAAK,CAACyC,eAAe,CAAC,MAAM;IAC1B,IAAIW,MAAM,KAAK,CAAC,EAAE;MAChB,MAAMc,SAAS,GAAGR,EAAE,CAACQ,SAAS;MAC9BR,EAAE,CAACQ,SAAS,GAAG,IAAI;MACnBP,MAAM,CAAC9C,OAAO,CAACsD,MAAM,CAACT,EAAE,EAAEG,YAAY,CAAC;MACvCH,EAAE,CAACQ,SAAS,GAAGA,SAAS;IAC1B;IACA,OAAOjD,WAAW,CAACC,UAAU,EAAEF,KAAK,EAAEG,YAAY,EAAE4C,GAAG,CAAC3C,OAAO,EAAE;MAC/DM,oBAAoB,EAAEiB,IAAI,KAAK,IAAI,IAAIA,IAAI,KAAK,KAAK,CAAC,GAAGA,IAAI,GAAGjB,oBAAoB;MACpFC,mBAAmB;MACnBC,kBAAkB;MAClBC,oBAAoB;MACpBC;IACF,CAAC,CAAC;EACJ,CAAC,EAAE,CAACkB,QAAQ,EAAEa,YAAY,EAAEE,GAAG,CAAC3C,OAAO,EAAEJ,KAAK,EAAEG,YAAY,EAAED,UAAU,EAAEkC,MAAM,EAAEM,EAAE,CAAC,CAAC;EACtF,IAAIU,KAAK,GAAG,CAAC;EACblE,QAAQ,CAAC,MAAM;IACb,IAAIkD,MAAM,KAAKiB,QAAQ,IAAID,KAAK,GAAGhB,MAAM,EAAE;MACzC,MAAMc,SAAS,GAAGR,EAAE,CAACQ,SAAS;MAC9BR,EAAE,CAACQ,SAAS,GAAG,IAAI;MACnBP,MAAM,CAAC9C,OAAO,CAACsD,MAAM,CAACT,EAAE,EAAEG,YAAY,CAAC;MACvCH,EAAE,CAACQ,SAAS,GAAGA,SAAS;MACxBE,KAAK,EAAE;IACT;EACF,CAAC,CAAC;EACF,OAAO,aAAapE,KAAK,CAACsE,aAAa,CAACtE,KAAK,CAACuE,QAAQ,EAAE,IAAI,EAAEpE,YAAY,CAAC,aAAaH,KAAK,CAACsE,aAAa,CAACtE,KAAK,CAACuE,QAAQ,EAAE,IAAI,EAAEvB,QAAQ,EAAE,aAAahD,KAAK,CAACsE,aAAa,CAAC,YAAY,EAAE;IACzLE,GAAG,EAAEb,MAAM;IACXc,IAAI,EAAE,CAACxB,IAAI,EAAEC,GAAG,EAAEa,GAAG;EACvB,CAAC,CAAC,EAAEV,KAAK,IAAIE,MAAM,GAAG,aAAavD,KAAK,CAACsE,aAAa,CAAC5B,eAAe,EAAE;IACtExB,UAAU,EAAE,IAAI;IAChBmC,KAAK,EAAEA,KAAK;IACZE,MAAM,EAAEA,MAAM;IACdD,IAAI,EAAEA,IAAI;IACVG,UAAU,EAAEA;EACd,CAAC,CAAC,GAAGnB,GAAG,GAAG,aAAatC,KAAK,CAACsE,aAAa,CAACjC,cAAc,EAAE;IAC1DnB,UAAU,EAAE,IAAI;IAChBoB,GAAG,EAAEA,GAAG;IACRmB,UAAU,EAAEA;EACd,CAAC,CAAC,GAAG,IAAI,CAAC,EAAEI,YAAY,CAAC,CAAC;AAC5B;AACA,SAASa,iBAAiBA,CAACC,KAAK,EAAE;EAChC,IAAIC,aAAa,EAAEC,cAAc,EAAEC,MAAM,EAAEC,cAAc;EACzD,MAAMC,cAAc,GAAGtE,cAAc,CAACiE,KAAK,CAAC;EAC5C,MAAMvD,OAAO,GAAGuD,KAAK,CAACrC,GAAG,IAAI0C,cAAc;EAC3ChF,KAAK,CAACgE,OAAO,CAAC,MAAM3D,MAAM,CAAC;IACzB4E,sBAAsB,EAAExE;EAC1B,CAAC,CAAC,EAAE,EAAE,CAAC;EACPT,KAAK,CAAC6C,SAAS,CAAC,MAAM;IACpB,OAAO,MAAM;MACXmC,cAAc,CAAClC,OAAO,CAAC,CAAC;IAC1B,CAAC;EACH,CAAC,EAAE,CAACkC,cAAc,CAAC,CAAC;EACpB,MAAMP,IAAI,GAAGzE,KAAK,CAACgE,OAAO,CAAC,MAAM,CAAC5C,OAAO,CAAC,EAAE,CAACA,OAAO,CAAC,CAAC;EACtD,MAAM8D,MAAM,GAAG,CAACN,aAAa,GAAGD,KAAK,CAACQ,MAAM,KAAK,IAAI,GAAG,KAAK,CAAC,GAAGP,aAAa,CAACM,MAAM;EACrF,MAAME,MAAM,GAAG,CAACP,cAAc,GAAGF,KAAK,CAACQ,MAAM,KAAK,IAAI,GAAG,KAAK,CAAC,GAAGN,cAAc,CAACO,MAAM;EACvF,MAAMC,KAAK,GAAG,CAACP,MAAM,GAAG,CAACC,cAAc,GAAGJ,KAAK,CAACQ,MAAM,KAAK,IAAI,GAAG,KAAK,CAAC,GAAGJ,cAAc,CAACM,KAAK,MAAM,IAAI,IAAIP,MAAM,KAAK,KAAK,CAAC,GAAGA,MAAM,GAAG,IAAI;EAC9I,OAAO,aAAa9E,KAAK,CAACsE,aAAa,CAACtE,KAAK,CAACuE,QAAQ,EAAE,IAAI,EAAE,aAAavE,KAAK,CAACsE,aAAa,CAACjC,cAAc,EAAEtC,QAAQ,CAAC,CAAC,CAAC,EAAE4E,KAAK,EAAE;IACjIrC,GAAG,EAAElB;EACP,CAAC,CAAC,CAAC,EAAE,aAAapB,KAAK,CAACsE,aAAa,CAAC,wBAAwB,EAAE;IAC9DG,IAAI,EAAEA,IAAI;IACVY,KAAK,EAAEA,KAAK;IACZH,MAAM,EAAEA,MAAM;IACdE,MAAM,EAAEA;EACV,CAAC,CAAC,CAAC;AACL;AACA,SAASE,WAAWA,CAACX,KAAK,EAAE;EAC1B,OAAOA,KAAK,CAACQ,MAAM,GAAG,aAAanF,KAAK,CAACsE,aAAa,CAACI,iBAAiB,EAAEC,KAAK,CAAC,GAAGA,KAAK,CAACrC,GAAG,GAAG,aAAatC,KAAK,CAACsE,aAAa,CAACjC,cAAc,EAAEsC,KAAK,CAAC,GAAGA,KAAK,CAAC3B,QAAQ,GAAG,aAAahD,KAAK,CAACsE,aAAa,CAACvB,iBAAiB,EAAE4B,KAAK,CAAC,GAAG,aAAa3E,KAAK,CAACsE,aAAa,CAAC5B,eAAe,EAAEiC,KAAK,CAAC;AACjS;AAEA,SAASW,WAAW,EAAE5C,eAAe,EAAEL,cAAc,EAAEU,iBAAiB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}