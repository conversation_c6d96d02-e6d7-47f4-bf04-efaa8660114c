// Text-to-Speech System for Super Siah Man Racing
// Provides character voices for game dialogue and events

import { How<PERSON>, How<PERSON> } from 'howler';

export interface VoiceProfile {
  id: string;
  name: string;
  gender: 'male' | 'female' | 'neutral';
  pitch: number;       // 0.5-2.0
  rate: number;        // 0.5-2.0
  volume: number;      // 0-1
  voiceURI?: string;   // For Web Speech API
  accent?: string;     // Description of accent
  character: string;   // Character this voice belongs to
  audioSrc?: string;   // Optional: Source for character-specific audio file
}

export interface TTSMessage {
  text: string;
  voiceId: string;
  priority: 'low' | 'medium' | 'high';
  category: 'dialogue' | 'race_event' | 'tutorial' | 'menu' | 'achievement' | 'gameplay';
  onStart?: () => void;
  onEnd?: () => void;
  audioSrc?: string; // Optional: Source for this specific message's audio file
}

export class TTSSystem {
  private static instance: TTSSystem;
  private voiceProfiles: Map<string, VoiceProfile>;
  private speechSynthesis: SpeechSynthesis | null;
  private voices: SpeechSynthesisVoice[] = [];
  private messageQueue: TTSMessage[] = [];
  private isSpeaking: boolean = false;
  private isEnabled: boolean = true;
  private volume: number = 1.0;
  private currentUtterance: SpeechSynthesisUtterance | null = null;
  private currentHowl: Howl | null = null;
  
  // Audio pool management to prevent WebMediaPlayer errors
  private readonly MAX_AUDIO_INSTANCES = 8;
  private audioPool: Howl[] = [];
  private activeAudioCount: number = 0;
  private audioInstanceMap: Map<string, Howl> = new Map();
  
  private constructor() {
    this.voiceProfiles = new Map();
    
    // Check if speech synthesis is available
    if (typeof window !== 'undefined' && window.speechSynthesis) {
      this.speechSynthesis = window.speechSynthesis;
      
      // Load available voices
      this.loadVoices();
      
      // Some browsers need a delay to load voices
      if (this.voices.length === 0) {
        setTimeout(() => this.loadVoices(), 1000);
      }
    } else {
      this.speechSynthesis = null;
      console.warn('Speech synthesis not supported in this browser. Character audio will still work if provided.');
    }
    
    // Initialize default voice profiles
    this.initializeDefaultVoices();
  }
  
  public static getInstance(): TTSSystem {
    if (!TTSSystem.instance) {
      TTSSystem.instance = new TTSSystem();
    }
    return TTSSystem.instance;
  }
  
  /**
   * Load available voices from the browser
   */
  private loadVoices(): void {
    if (!this.speechSynthesis) return;
    
    // Ensure voices are loaded before trying to get them
    this.speechSynthesis.onvoiceschanged = () => {
      this.voices = this.speechSynthesis!.getVoices();
      console.log(`Loaded ${this.voices.length} voices from speech synthesis API`);
    };
    
    // Attempt to load voices immediately in case they are already available
    this.voices = this.speechSynthesis.getVoices();
    if (this.voices.length > 0) {
        console.log(`Loaded ${this.voices.length} voices from speech synthesis API`);
    }
  }
  
  /**
   * Initialize default voice profiles for game characters
   */
  private initializeDefaultVoices(): void {
    // Main character - Siah Man
    this.addVoiceProfile({
      id: 'siah_man',
      name: 'Siah Man',
      gender: 'male',
      pitch: 1.0,
      rate: 1.0,
      volume: 1.0,
      accent: 'heroic',
      character: 'Siah Man'
    });
    
    // Dr. Neal
    this.addVoiceProfile({
      id: 'dr_neal',
      name: 'Dr. Neal',
      gender: 'male',
      pitch: 0.9,
      rate: 1.1,
      volume: 1.0,
      accent: 'scientific',
      character: 'Dr. Neal'
    });
    
    // AI Opponents
    this.addVoiceProfile({
      id: 'max_velocity',
      name: 'Max Velocity',
      gender: 'male',
      pitch: 1.1,
      rate: 1.2,
      volume: 0.9,
      accent: 'confident',
      character: 'Max Velocity'
    });
    
    this.addVoiceProfile({
      id: 'turbo_tina',
      name: 'Turbo Tina',
      gender: 'female',
      pitch: 1.2,
      rate: 1.1,
      volume: 0.9,
      accent: 'energetic',
      character: 'Turbo Tina'
    });
    
    this.addVoiceProfile({
      id: 'drift_king',
      name: 'Drift King',
      gender: 'male',
      pitch: 0.9,
      rate: 0.9,
      volume: 1.0,
      accent: 'smooth',
      character: 'Drift King'
    });
    
    // Race announcer
    this.addVoiceProfile({
      id: 'announcer',
      name: 'Race Announcer',
      gender: 'male',
      pitch: 1.0,
      rate: 1.3,
      volume: 1.0,
      accent: 'announcer',
      character: 'Race Announcer'
    });
    
    // Navigation system
    this.addVoiceProfile({
      id: 'nav_system',
      name: 'Navigation System',
      gender: 'female',
      pitch: 1.1,
      rate: 1.0,
      volume: 0.8,
      accent: 'robotic',
      character: 'Navigation System'
    });
    
    // Car Whisperer
    this.addVoiceProfile({
      id: 'car_whisperer',
      name: 'Car Whisperer',
      gender: 'neutral',
      pitch: 0.8,
      rate: 0.9,
      volume: 0.7,
      accent: 'mystical',
      character: 'Car Whisperer'
    });
  }
  
  /**
   * Add a new voice profile
   */
  public addVoiceProfile(profile: VoiceProfile): void {
    if (this.voiceProfiles.has(profile.id)) {
      console.warn(`Voice profile with ID ${profile.id} already exists. Overwriting.`);
    }
    
    // Find a matching system voice if available
    if (this.speechSynthesis && !profile.voiceURI) {
      const matchingVoice = this.findMatchingSystemVoice(profile);
      if (matchingVoice) {
        profile.voiceURI = matchingVoice.voiceURI;
      }
    }
    
    this.voiceProfiles.set(profile.id, profile);
  }
  
  /**
   * Find a matching system voice based on profile preferences
   */
  private findMatchingSystemVoice(profile: VoiceProfile): SpeechSynthesisVoice | null {
    if (!this.speechSynthesis || this.voices.length === 0) return null;
    
    // Filter voices by gender if specified
    let matchingVoices = this.voices;
    
    if (profile.gender === 'male') {
      matchingVoices = matchingVoices.filter(v => v.name.toLowerCase().includes('male') || 
                                               v.name.toLowerCase().includes('guy') || 
                                               v.name.toLowerCase().includes('man'));
    } else if (profile.gender === 'female') {
      matchingVoices = matchingVoices.filter(v => v.name.toLowerCase().includes('female') || 
                                               v.name.toLowerCase().includes('woman') || 
                                               v.name.toLowerCase().includes('girl'));
    }
    
    // If we have accent info, try to match that too
    if (profile.accent && matchingVoices.length > 1) {
      const accentMatches = matchingVoices.filter(v => 
        v.name.toLowerCase().includes(profile.accent!.toLowerCase())
      );
      
      if (accentMatches.length > 0) {
        matchingVoices = accentMatches;
      }
    }
    
    // Return the first matching voice or null if none found
    return matchingVoices.length > 0 ? matchingVoices[0] : null;
  }
  
  /**
   * Speak a message using TTS
   */
  public speak(message: TTSMessage): void {
    if (!this.isEnabled) return;
    
    // Add to queue based on priority
    this.addToQueue(message);
    
    // Start processing the queue if not already speaking
    if (!this.isSpeaking) {
      this.processQueue();
    }
  }
  
  /**
   * Set the AudioSystem instance for synchronized voice playback
   */
  public setAudioSystem(audioSystem: any): void {
    this.audioSystem = audioSystem;
  }

  /**
   * Play character voice with proper synchronization
   */
  public async playCharacterVoice(character: string, dialogueId: string, text: string, onComplete?: () => void): Promise<void> {
    if (this.audioSystem) {
      try {
        // Try to play the actual voice file first
        await this.audioSystem.playVoiceDialogue(dialogueId, onComplete);
        console.log(`Playing character voice: ${character} - "${text}"`);
      } catch (error) {
        console.warn(`Voice file not found for ${dialogueId}, falling back to TTS`);
        // Fallback to TTS if voice file is not available
        this.speak({
          text,
          voiceId: character,
          priority: 'high',
          category: 'dialogue',
          onEnd: onComplete
        });
      }
    } else {
      // No AudioSystem available, use TTS
      this.speak({
        text,
        voiceId: character,
        priority: 'high',
        category: 'dialogue',
        onEnd: onComplete
      });
    }
  }

  /**
   * Play synchronized story dialogue with character voices
   */
  public async playStoryDialogue(character: string, text: string, audioSrc?: string, onComplete?: () => void): Promise<void> {
    if (audioSrc && this.audioSystem) {
      // Create a temporary dialogue entry
      const dialogueId = `temp_${character}_${Date.now()}`;
      const dialogue = {
        id: dialogueId,
        character,
        text,
        audioSrc,
        priority: 'high' as const
      };

      try {
        await this.audioSystem.loadVoiceDialogue(dialogue);
        await this.audioSystem.playVoiceDialogue(dialogueId, onComplete);
        console.log(`Playing story dialogue: ${character} - "${text}"`);
      } catch (error) {
        console.warn(`Failed to play voice file for ${character}, falling back to TTS:`, error);
        this.speak({
          text,
          voiceId: character,
          priority: 'high',
          category: 'dialogue',
          onEnd: onComplete
        });
      }
    } else {
      // Fallback to TTS
      this.speak({
        text,
        voiceId: character,
        priority: 'high',
        category: 'dialogue',
        onEnd: onComplete
      });
    }
  }

  /**
   * Add a message to the queue based on priority
   */
  private addToQueue(message: TTSMessage): void {
    if (!message.text || message.text.trim() === '') {
      console.warn('Empty message text, ignoring');
      return;
    }
    
    // Ensure the voice profile exists
    if (!this.voiceProfiles.has(message.voiceId)) {
      console.warn(`Voice profile not found for ID: ${message.voiceId}, using default`);
      message.voiceId = 'announcer'; // Default fallback
    }
    
    // Add to queue based on priority
    if (message.priority === 'high') {
      // High priority - add to front of queue
      this.messageQueue.unshift(message);
    } else if (message.priority === 'medium') {
      // Medium priority - add after other high priority messages
      const lastHighPriorityIndex = this.messageQueue.findIndex(m => m.priority !== 'high');
      
      if (lastHighPriorityIndex === -1) {
        // No high priority messages, add to end
        this.messageQueue.push(message);
      } else {
        // Insert after last high priority message
        this.messageQueue.splice(lastHighPriorityIndex, 0, message);
      }
    } else {
      // Low priority - add to end of queue
      this.messageQueue.push(message);
    }
  }
  
  /**
   * Get an available audio instance from the pool or create a new one
   */
  private getAudioInstance(src: string, options: any): Howl | null {
    // Check if we already have an instance for this source that's not playing
    if (this.audioInstanceMap.has(src)) {
      const existingHowl = this.audioInstanceMap.get(src)!;
      if (!existingHowl.playing()) {
        return existingHowl;
      }
    }
    
    // Check if we've reached the maximum number of instances
    if (this.activeAudioCount >= this.MAX_AUDIO_INSTANCES) {
      // Find any non-playing instance to reuse
      for (const howl of this.audioPool) {
        if (!howl.playing()) {
          howl.unload();
          this.audioPool = this.audioPool.filter(h => h !== howl);
          this.activeAudioCount--;
          
          // Also remove from the instance map
          for (const [key, value] of this.audioInstanceMap.entries()) {
            if (value === howl) {
              this.audioInstanceMap.delete(key);
              break;
            }
          }
          break;
        }
      }
      
      // If we still have too many active instances, we can't create a new one
      if (this.activeAudioCount >= this.MAX_AUDIO_INSTANCES) {
        console.warn('All audio instances are in use. Skipping audio playback to prevent WebMediaPlayer error.');
        return null;
      }
    }
    
    // Create a new instance
    const newInstance = new Howl(options);
    this.audioPool.push(newInstance);
    this.audioInstanceMap.set(src, newInstance);
    this.activeAudioCount++;
    return newInstance;
  }
  
  /**
   * Release an audio instance back to the pool
   */
  private releaseAudioInstance(howl: Howl, src?: string): void {
    // Stop the audio if it's playing
    if (howl.playing()) {
      howl.stop();
    }
    
    // If we have too many instances, remove this one
    if (this.audioPool.length > this.MAX_AUDIO_INSTANCES * 1.5) {
      howl.unload();
      this.audioPool = this.audioPool.filter(h => h !== howl);
      this.activeAudioCount--;
      
      // Remove from the instance map if we have the source
      if (src && this.audioInstanceMap.get(src) === howl) {
        this.audioInstanceMap.delete(src);
      }
    }
  }
  
  /**
   * Process the message queue
   */
  private processQueue(): void {
    if (!this.isEnabled || this.isSpeaking || this.messageQueue.length === 0) {
      return;
    }
    
    // Get the next message
    const message = this.messageQueue.shift()!;
    
    // Get the voice profile
    const profile = this.voiceProfiles.get(message.voiceId);
    if (!profile) {
      console.error(`Voice profile not found for ID: ${message.voiceId}`);
      this.processQueue(); // Move to next message
      return;
    }
    
    // Determine audio source - message specific or from profile
    const audioSource = message.audioSrc || profile.audioSrc;
    
    // If there's an audio file for this message, use it
    if (audioSource) {
      // Configure audio options
      const audioOptions = {
        src: [audioSource],
        volume: profile.volume * this.volume,
        html5: true, // Use HTML5 Audio to prevent WebMediaPlayer issues
        onplay: () => {
          this.isSpeaking = true;
          if (message.onStart) message.onStart();
        },
        onend: () => {
          this.isSpeaking = false;
          if (this.currentHowl) {
            this.releaseAudioInstance(this.currentHowl, audioSource);
            this.currentHowl = null;
          }
          if (message.onEnd) message.onEnd();
          
          // Process next message after a short delay
          setTimeout(() => this.processQueue(), 100);
        },
        onloaderror: (id: number, error: any) => {
          console.error(`Error loading audio: ${error}`);
          this.isSpeaking = false;
          if (this.currentHowl) {
            this.releaseAudioInstance(this.currentHowl, audioSource);
            this.currentHowl = null;
          }
          
          // Process next message
          setTimeout(() => this.processQueue(), 100);
        },
        onplayerror: (id: number, error: any) => {
          console.error(`Error playing audio: ${error}`);
          this.isSpeaking = false;
          if (this.currentHowl) {
            this.releaseAudioInstance(this.currentHowl, audioSource);
            this.currentHowl = null;
          }
          
          // Process next message
          setTimeout(() => this.processQueue(), 100);
        }
      };
      
      // Get an audio instance from the pool
      const howl = this.getAudioInstance(audioSource, audioOptions);
      if (howl) {
        this.currentHowl = howl;
        howl.play();
      } else {
        // If we couldn't get an audio instance, skip to next message
        console.warn('Could not play audio due to WebMediaPlayer limitations. Skipping message.');
        this.processQueue();
      }
    } else if (this.speechSynthesis) {
      // Use browser's built-in TTS if no audio file is provided
      const utterance = new SpeechSynthesisUtterance(message.text);
      
      // Set voice if available
      if (profile.voiceURI) {
        const systemVoice = this.voices.find(v => v.voiceURI === profile.voiceURI);
        if (systemVoice) {
          utterance.voice = systemVoice;
        }
      } else {
        // Try to find a matching voice based on gender and accent
        const matchingVoice = this.findMatchingSystemVoice(profile);
        if (matchingVoice) {
          utterance.voice = matchingVoice;
        }
      }
      
      // Set voice properties
      utterance.pitch = profile.pitch;
      utterance.rate = profile.rate;
      utterance.volume = profile.volume * this.volume;
      
      // Set callbacks
      utterance.onstart = () => {
        this.isSpeaking = true;
        if (message.onStart) message.onStart();
      };
      
      utterance.onend = () => {
        this.isSpeaking = false;
        this.currentUtterance = null;
        if (message.onEnd) message.onEnd();
        
        // Process next message
        setTimeout(() => this.processQueue(), 100);
      };
      
      utterance.onerror = (event) => {
        console.error('TTS error:', event);
        this.isSpeaking = false;
        this.currentUtterance = null;
        
        // Process next message
        setTimeout(() => this.processQueue(), 100);
      };
      
      // Store current utterance
      this.currentUtterance = utterance;
      
      // Speak
      this.speechSynthesis.speak(utterance);
    } else {
      console.warn('No audio source provided and speech synthesis not supported.');
      this.processQueue(); // Move to the next message
    }
  }
  
  /**
   * Stop all speech and audio
   */
  public stop(): void {
    if (this.speechSynthesis) {
      this.speechSynthesis.cancel();
    }
    
    // Stop and release all audio instances
    this.audioPool.forEach(howl => {
      if (howl.playing()) {
        howl.stop();
      }
    });
    
    // Clear the current howl reference
    this.currentHowl = null;
    
    // Reset state
    this.isSpeaking = false;
    this.currentUtterance = null;
    this.messageQueue = []; // Clear the queue on stop
  }
  
  /**
   * Clean up audio resources
   */
  public cleanup(): void {
    // Unload all audio instances to free up memory
    this.audioPool.forEach(howl => {
      howl.unload();
    });
    this.audioPool = [];
    this.audioInstanceMap.clear();
    this.activeAudioCount = 0;
    
    // Stop any ongoing speech
    this.stop();
  }
  
  /**
   * Pause speech or audio
   */
  public pause(): void {
    if (this.speechSynthesis) {
      this.speechSynthesis.pause();
    }
    
    if (this.currentHowl && this.currentHowl.playing()) {
      this.currentHowl.pause();
    }
  }
  
  /**
   * Resume speech
   */
  public resume(): void {
    if (this.speechSynthesis) {
      this.speechSynthesis.resume();
    }
    
    if (this.currentHowl) {
      this.currentHowl.play();
    }
  }
  
  /**
   * Enable/disable TTS
   */
  public setEnabled(enabled: boolean): void {
    this.isEnabled = enabled;
    
    if (!enabled) {
      this.stop();
      this.messageQueue = [];
    }
  }
  
  /**
   * Set global TTS volume
   */
  public setVolume(volume: number): void {
    this.volume = Math.max(0, Math.min(1, volume));
    
    // Update current utterance if speaking
    if (this.currentUtterance) {
      this.currentUtterance.volume = this.volume;
    }
    
    // Update current Howl if playing
    if (this.currentHowl) {
      this.currentHowl.volume(this.volume);
    }
  }
  
  /**
   * Get all voice profiles
   */
  public getAllVoiceProfiles(): VoiceProfile[] {
    return Array.from(this.voiceProfiles.values());
  }
  
  /**
   * Get a voice profile by ID
   */
  public getVoiceProfile(id: string): VoiceProfile | undefined {
    return this.voiceProfiles.get(id);
  }
  
  /**
   * Speak a dialogue line for a character
   */
  public speakDialogue(characterId: string, text: string, priority: 'low' | 'medium' | 'high' = 'medium'): void {
    this.speak({
      text,
      voiceId: characterId,
      priority,
      category: 'dialogue'
    });
  }
  
  /**
   * Announce a race event
   */
  public announceRaceEvent(text: string, priority: 'low' | 'medium' | 'high' = 'medium'): void {
    this.speak({
      text,
      voiceId: 'announcer',
      priority,
      category: 'race_event'
    });
  }
  
  /**
   * Speak a tutorial instruction
   */
  public speakTutorial(text: string, priority: 'low' | 'medium' | 'high' = 'medium'): void {
    this.speak({
      text,
      voiceId: 'nav_system',
      priority,
      category: 'tutorial'
    });
  }
  
  /**
   * Announce an achievement
   */
  public announceAchievement(text: string): void {
    this.speak({
      text,
      voiceId: 'announcer',
      priority: 'high',
      category: 'achievement'
    });
  }
}
