{"ast": null, "code": "import { createSlice } from '@reduxjs/toolkit';\n\n// Using Track interface from GameTypes.ts\n\nconst initialState = {\n  // Navigation\n  currentScreen: 'mainMenu',\n  // Game status\n  isPlaying: false,\n  gameMode: 'race',\n  // Race state\n  raceState: {\n    isRaceStarted: false,\n    isRaceComplete: false,\n    currentLap: 0,\n    position: 0,\n    raceTime: '00:00.000',\n    bestLapTime: null,\n    currentLapTime: '00:00.000',\n    experienceReward: 0,\n    currencyReward: 0,\n    speed: 0\n  },\n  vehicleState: {\n    x: 100,\n    y: 100,\n    speed: 0,\n    acceleration: 0,\n    direction: 0,\n    angularVelocity: 0,\n    isDrifting: false,\n    isOffroadSurface: false,\n    nitroFuel: 100,\n    isNitroActive: false,\n    // Enhanced arcade features - initial values\n    isJumping: false,\n    jumpHeight: 0,\n    airTime: 0,\n    driftTime: 0,\n    driftScore: 0,\n    comboMultiplier: 1,\n    lastComboTime: 0,\n    totalScore: 0,\n    coinsCollected: 0,\n    activePowerUps: [],\n    isInvincible: false,\n    invincibilityUntil: 0,\n    speedBoostUntil: 0,\n    coinMagnetUntil: 0,\n    doublePointsUntil: 0\n  },\n  controls: {\n    up: false,\n    down: false,\n    left: false,\n    right: false,\n    nitro: false,\n    brake: false\n  },\n  // Track data\n  currentTrackId: null,\n  availableTracks: [{\n    id: 'beginner_track',\n    name: '🌈 Rainbow Speedway',\n    type: 'beginner',\n    length: 2000,\n    difficulty: 'easy',\n    unlocked: true,\n    description: 'Perfect for young racers! Colorful and fun!',\n    theme: 'rainbow',\n    specialFeatures: ['lots_of_coins', 'easy_turns', 'speed_pads'],\n    thumbnail: '/assets/images/track_rainbow.jpg'\n  }, {\n    id: 'candy_land',\n    name: '🍭 Candy Land Circuit',\n    type: 'fantasy',\n    length: 2500,\n    difficulty: 'easy',\n    unlocked: true,\n    description: 'Sweet racing through candy wonderland!',\n    theme: 'candy',\n    specialFeatures: ['jump_ramps', 'bouncy_obstacles', 'sugar_rush_zones'],\n    thumbnail: '/assets/images/track_candy.jpg'\n  }, {\n    id: 'space_adventure',\n    name: '🚀 Space Adventure',\n    type: 'space',\n    length: 3000,\n    difficulty: 'medium',\n    unlocked: true,\n    description: 'Race among the stars with zero gravity zones!',\n    theme: 'space',\n    specialFeatures: ['zero_gravity', 'asteroid_obstacles', 'warp_tunnels'],\n    thumbnail: '/assets/images/track_space.jpg'\n  }, {\n    id: 'underwater_world',\n    name: '🐠 Underwater World',\n    type: 'underwater',\n    length: 2800,\n    difficulty: 'medium',\n    unlocked: false,\n    description: 'Dive deep and race with sea creatures!',\n    theme: 'underwater',\n    specialFeatures: ['bubble_boosts', 'sea_creatures', 'coral_obstacles'],\n    thumbnail: '/assets/images/track_underwater.jpg'\n  }, {\n    id: 'mountain_track',\n    name: '⛰️ Mountain Adventure',\n    type: 'mountain',\n    length: 3500,\n    difficulty: 'medium',\n    unlocked: false,\n    description: 'Climb high and race through mountain peaks!',\n    theme: 'mountain',\n    specialFeatures: ['steep_climbs', 'scenic_views', 'mountain_animals'],\n    thumbnail: '/assets/images/track_mountain.jpg'\n  }, {\n    id: 'jungle_safari',\n    name: '🦁 Jungle Safari',\n    type: 'jungle',\n    length: 3200,\n    difficulty: 'medium',\n    unlocked: false,\n    description: 'Race through wild jungles with animal friends!',\n    theme: 'jungle',\n    specialFeatures: ['animal_crossings', 'vine_swings', 'waterfall_jumps'],\n    thumbnail: '/assets/images/track_jungle.jpg'\n  }, {\n    id: 'ice_kingdom',\n    name: '❄️ Ice Kingdom',\n    type: 'ice',\n    length: 2600,\n    difficulty: 'hard',\n    unlocked: false,\n    description: 'Slippery ice racing with penguin friends!',\n    theme: 'ice',\n    specialFeatures: ['ice_slides', 'penguin_obstacles', 'aurora_lights'],\n    thumbnail: '/assets/images/track_ice.jpg'\n  }, {\n    id: 'volcano_valley',\n    name: '🌋 Volcano Valley',\n    type: 'volcano',\n    length: 3800,\n    difficulty: 'hard',\n    unlocked: false,\n    description: 'Race through fiery lava flows and hot springs!',\n    theme: 'volcano',\n    specialFeatures: ['lava_jumps', 'hot_springs', 'fire_obstacles'],\n    thumbnail: '/assets/images/track_volcano.jpg'\n  }, {\n    id: 'pirate_cove',\n    name: '🏴‍☠️ Pirate Cove',\n    type: 'pirate',\n    length: 2900,\n    difficulty: 'medium',\n    unlocked: false,\n    description: 'Ahoy! Race with pirates and treasure!',\n    theme: 'pirate',\n    specialFeatures: ['treasure_chests', 'pirate_ships', 'cannon_obstacles'],\n    thumbnail: '/assets/images/track_pirate.jpg'\n  }, {\n    id: 'rainbow_bridge',\n    name: '🌈 Rainbow Bridge',\n    type: 'fantasy',\n    length: 2200,\n    difficulty: 'easy',\n    unlocked: false,\n    description: 'Race across magical rainbow bridges!',\n    theme: 'rainbow',\n    specialFeatures: ['rainbow_boosts', 'cloud_platforms', 'unicorn_friends'],\n    thumbnail: '/assets/images/track_rainbow_bridge.jpg'\n  }],\n  // Race state\n  activeRace: null,\n  raceHistory: [],\n  // Challenge mode\n  availableChallenges: [{\n    id: 'challenge1',\n    name: 'Speed Challenge',\n    description: 'Complete a lap in under 60 seconds',\n    trackId: 'oval_track',\n    objective: {\n      type: 'time',\n      target: 60,\n      timeLimit: 120\n    },\n    reward: {\n      money: 1000,\n      experience: 500\n    },\n    difficulty: 'easy'\n  }, {\n    id: 'challenge2',\n    name: 'Overtake Challenge',\n    description: 'Overtake 10 vehicles in 2 minutes',\n    trackId: 'lake_track',\n    objective: {\n      type: 'overtake',\n      target: 10,\n      timeLimit: 120\n    },\n    reward: {\n      money: 1500,\n      experience: 700\n    },\n    difficulty: 'medium'\n  }, {\n    id: 'challenge3',\n    name: 'Mountain Master',\n    description: 'Complete 3 laps of the mountain track',\n    trackId: 'mountain_track',\n    objective: {\n      type: 'position',\n      target: 1,\n      timeLimit: 300\n    },\n    reward: {\n      money: 2000,\n      experience: 1000,\n      unlockTrack: 'city_track'\n    },\n    difficulty: 'hard'\n  }, {\n    id: 'challenge4',\n    name: 'Forest Frenzy',\n    description: 'Complete 2 laps of the forest track without crashing',\n    trackId: 'forest_track',\n    objective: {\n      type: 'position',\n      target: 1,\n      timeLimit: 240\n    },\n    reward: {\n      money: 2500,\n      experience: 1200\n    },\n    difficulty: 'medium'\n  }],\n  completedChallenges: [],\n  // Player progression\n  storyProgress: 0,\n  unlockedAbilities: [],\n  experience: 0,\n  level: 1,\n  currency: 1000,\n  // Game settings\n  selectedTimeOfDay: 'noon',\n  selectedWeather: 'clear',\n  difficulty: 'medium',\n  // UI state\n  loading: false,\n  error: null,\n  uiAssets: {\n    tachometer_image: '/assets/images/TahometerInside.png',\n    tachometer_arrow: '/assets/images/TahometerArrow.png'\n  }\n};\nexport const gameSlice = createSlice({\n  name: 'game',\n  initialState,\n  reducers: {\n    // Navigation actions\n    setCurrentScreen: (state, action) => {\n      state.currentScreen = action.payload;\n    },\n    // Game mode actions\n    setGameMode: (state, action) => {\n      state.gameMode = action.payload;\n    },\n    // Race actions\n    startRace: (state, action) => {\n      const {\n        trackId,\n        vehicleId,\n        gameMode,\n        opponents = 3,\n        laps = 3,\n        weather = state.selectedWeather,\n        timeOfDay = state.selectedTimeOfDay,\n        difficulty = state.difficulty,\n        challengeId\n      } = action.payload;\n      state.isPlaying = true;\n      state.currentTrackId = trackId;\n      state.gameMode = gameMode;\n\n      // Create checkpoints based on track type\n      const track = state.availableTracks.find(t => t.id === trackId);\n      const checkpoints = [];\n\n      // Simple checkpoint generation (would be more complex in real implementation)\n      if (track) {\n        const checkpointCount = Math.floor(track.length / 500);\n        for (let i = 0; i < checkpointCount; i++) {\n          checkpoints.push({\n            id: i,\n            position: [Math.cos(i / checkpointCount * Math.PI * 2) * 40, 0, Math.sin(i / checkpointCount * Math.PI * 2) * 40],\n            passed: false\n          });\n        }\n      }\n\n      // Helper function to generate opponent vehicles\n      const generateOpponents = (count, trackId, mode) => {\n        const opponentVehicles = [];\n\n        // Generate different opponents based on difficulty and game mode\n        for (let i = 0; i < count; i++) {\n          const vehicleTypes = ['sports', 'muscle', 'rally', 'supercar', 'concept'];\n          const randomType = vehicleTypes[Math.floor(Math.random() * vehicleTypes.length)];\n          opponentVehicles.push({\n            id: `opponent-${i}`,\n            vehicleId: `vehicle-${randomType}-${i}`,\n            name: `AI Driver ${i + 1}`,\n            position: i + 1,\n            currentLap: 1,\n            lapTimes: [],\n            currentSpeed: 0,\n            difficulty: state.difficulty\n          });\n        }\n        return opponentVehicles;\n      };\n\n      // Initialize race state\n      state.activeRace = {\n        mode: gameMode,\n        trackId,\n        vehicleId,\n        opponents: generateOpponents(opponents, trackId, gameMode),\n        playerPosition: opponents,\n        currentLap: 1,\n        totalLaps: laps,\n        raceTime: 0,\n        bestLapTime: 0,\n        currentLapTime: 0,\n        checkpoints,\n        weather: weather,\n        timeOfDay: timeOfDay,\n        difficulty: difficulty,\n        paused: false,\n        finished: false,\n        challengeId: challengeId\n      };\n    },\n    updateRaceState: (state, action) => {\n      state.raceState = {\n        ...state.raceState,\n        ...action.payload\n      };\n    },\n    updateVehicleState: (state, action) => {\n      state.vehicleState = {\n        ...state.vehicleState,\n        ...action.payload\n      };\n    },\n    setControls: (state, action) => {\n      state.controls = {\n        ...state.controls,\n        ...action.payload\n      };\n    },\n    endRace: (state, action) => {\n      state.raceState = {\n        ...state.raceState,\n        isRaceComplete: true,\n        finalPosition: action.payload.finalPosition,\n        experienceReward: action.payload.experienceReward,\n        currencyReward: action.payload.currencyReward\n      };\n      state.isPlaying = false;\n\n      // Also update the old race state format for backward compatibility\n      if (!state.activeRace) return;\n      const {\n        playerPosition,\n        currentLap,\n        raceTime,\n        currentLapTime,\n        checkpointId\n      } = action.payload;\n      if (playerPosition !== undefined) {\n        state.activeRace.playerPosition = playerPosition;\n      }\n      if (currentLap !== undefined) {\n        state.activeRace.currentLap = currentLap;\n      }\n      if (raceTime !== undefined) {\n        state.activeRace.raceTime = raceTime;\n      }\n      if (currentLapTime !== undefined) {\n        state.activeRace.currentLapTime = currentLapTime;\n\n        // Update best lap time if this is a new best\n        if (state.activeRace.bestLapTime === 0 || currentLapTime < state.activeRace.bestLapTime) {\n          state.activeRace.bestLapTime = currentLapTime;\n        }\n      }\n      if (checkpointId !== undefined) {\n        const checkpoint = state.activeRace.checkpoints.find(c => c.id === checkpointId);\n        if (checkpoint) {\n          checkpoint.passed = true;\n          checkpoint.passTime = state.activeRace.raceTime;\n        }\n      }\n\n      // Check if race is finished\n      if (state.activeRace.currentLap >= state.activeRace.totalLaps) {\n        state.activeRace.finished = true;\n      }\n    },\n    finishRace: (state, action) => {\n      if (!state.activeRace) return;\n      state.isPlaying = false;\n      state.activeRace.finished = true;\n      state.activeRace.raceResult = action.payload;\n      state.raceHistory.push(action.payload);\n\n      // Update best time for the track\n      const track = state.availableTracks.find(t => t.id === state.currentTrackId);\n      if (track && state.activeRace.vehicleId) {\n        const vehicleId = state.activeRace.vehicleId;\n        if (!track.bestTimes[vehicleId] || action.payload.totalTime < track.bestTimes[vehicleId]) {\n          track.bestTimes[vehicleId] = action.payload.totalTime;\n        }\n      }\n\n      // Add rewards\n      state.currency += action.payload.moneyEarned;\n      state.experience += action.payload.experienceEarned;\n      state.level = Math.floor(state.experience / 1000) + 1;\n    },\n    pauseRace: (state, action) => {\n      if (state.activeRace) {\n        state.activeRace.paused = action.payload;\n      }\n    },\n    resetRace: state => {\n      if (state.activeRace) {\n        state.activeRace.currentLap = 0;\n        state.activeRace.raceTime = 0;\n        state.activeRace.currentLapTime = 0;\n        state.activeRace.playerPosition = state.activeRace.opponents.length + 1;\n        state.activeRace.checkpoints.forEach(c => c.passed = false);\n        state.activeRace.finished = false;\n        state.activeRace.raceResult = undefined;\n      }\n    },\n    // Challenge mode actions\n    startChallenge: (state, action) => {\n      const challengeId = action.payload;\n      const challenge = state.availableChallenges.find(c => c.id === challengeId);\n      if (challenge) {\n        state.gameMode = 'challenge';\n        state.isPlaying = true;\n        state.currentTrackId = challenge.trackId;\n\n        // Initialize race state for challenge\n        // This would be more complex in a real implementation\n      }\n    },\n    completeChallenge: (state, action) => {\n      const {\n        challengeId,\n        success,\n        time\n      } = action.payload;\n      if (success && !state.completedChallenges.includes(challengeId)) {\n        state.completedChallenges.push(challengeId);\n\n        // Award challenge rewards\n        const challenge = state.availableChallenges.find(c => c.id === challengeId);\n        if (challenge) {\n          state.currency += challenge.reward.money;\n          state.experience += challenge.reward.experience;\n\n          // Unlock track if specified in reward\n          if (challenge.reward.unlockTrack) {\n            const track = state.availableTracks.find(t => t.id === challenge.reward.unlockTrack);\n            if (track) {\n              track.unlocked = true;\n            }\n          }\n        }\n      }\n      state.isPlaying = false;\n    },\n    // Track actions\n    setCurrentTrack: (state, action) => {\n      state.currentTrackId = action.payload;\n    },\n    unlockTrack: (state, action) => {\n      const track = state.availableTracks.find(t => t.id === action.payload);\n      if (track) {\n        track.unlocked = true;\n      }\n    },\n    // Environment settings\n    setTimeOfDay: (state, action) => {\n      state.selectedTimeOfDay = action.payload;\n    },\n    setWeather: (state, action) => {\n      state.selectedWeather = action.payload;\n    },\n    setDifficulty: (state, action) => {\n      state.difficulty = action.payload;\n    },\n    // Player progression\n    advanceStory: state => {\n      state.storyProgress += 1;\n    },\n    unlockAbility: (state, action) => {\n      if (!state.unlockedAbilities.includes(action.payload)) {\n        state.unlockedAbilities.push(action.payload);\n      }\n    },\n    addExperience: (state, action) => {\n      state.experience += action.payload;\n      state.level = Math.floor(state.experience / 1000) + 1;\n    },\n    // Currency management\n    addCurrency: (state, action) => {\n      state.currency += action.payload;\n    },\n    spendCurrency: (state, action) => {\n      state.currency = Math.max(0, state.currency - action.payload);\n    },\n    // UI state\n    setLoading: (state, action) => {\n      state.loading = action.payload;\n    },\n    setError: (state, action) => {\n      state.error = action.payload;\n    }\n  }\n});\nexport const {\n  // Navigation\n  setCurrentScreen,\n  // Game mode\n  setGameMode,\n  // Race actions\n  startRace,\n  updateRaceState,\n  finishRace,\n  pauseRace,\n  resetRace,\n  // Challenge mode\n  startChallenge,\n  completeChallenge,\n  // Track actions\n  setCurrentTrack,\n  unlockTrack,\n  // Environment settings\n  setTimeOfDay,\n  setWeather,\n  setDifficulty,\n  // Player progression\n  advanceStory,\n  unlockAbility,\n  addExperience,\n  // Currency management\n  addCurrency,\n  spendCurrency,\n  // UI state\n  setLoading,\n  setError\n} = gameSlice.actions;\nexport default gameSlice.reducer;", "map": {"version": 3, "names": ["createSlice", "initialState", "currentScreen", "isPlaying", "gameMode", "raceState", "isRaceStarted", "isRaceComplete", "currentLap", "position", "raceTime", "bestLapTime", "currentLapTime", "experienceReward", "currencyReward", "speed", "vehicleState", "x", "y", "acceleration", "direction", "angularVelocity", "isDrifting", "isOffroadSurface", "nitroFuel", "isNitroActive", "isJumping", "jumpHeight", "airTime", "driftTime", "driftScore", "comboMultiplier", "lastComboTime", "totalScore", "coinsCollected", "activePowerUps", "isInvincible", "invincibilityUntil", "speedBoostUntil", "coinMagnetUntil", "doublePointsUntil", "controls", "up", "down", "left", "right", "nitro", "brake", "currentTrackId", "availableTracks", "id", "name", "type", "length", "difficulty", "unlocked", "description", "theme", "specialFeatures", "thumbnail", "activeRace", "raceHistory", "availableChallenges", "trackId", "objective", "target", "timeLimit", "reward", "money", "experience", "unlockTrack", "completedChallenges", "storyProgress", "unlockedAbilities", "level", "currency", "selectedTimeOfDay", "<PERSON><PERSON><PERSON><PERSON>", "loading", "error", "uiAssets", "tachometer_image", "tachometer_arrow", "gameSlice", "reducers", "setCurrentScreen", "state", "action", "payload", "setGameMode", "startRace", "vehicleId", "opponents", "laps", "weather", "timeOfDay", "challengeId", "track", "find", "t", "checkpoints", "checkpointCount", "Math", "floor", "i", "push", "cos", "PI", "sin", "passed", "generateOpponents", "count", "mode", "opponent<PERSON><PERSON><PERSON>", "vehicleTypes", "randomType", "random", "lapTimes", "currentSpeed", "playerPosition", "totalLaps", "paused", "finished", "updateRaceState", "updateVehicleState", "setControls", "endRace", "finalPosition", "checkpointId", "undefined", "checkpoint", "c", "passTime", "finishRace", "raceResult", "bestTimes", "totalTime", "moneyEarned", "experienceEarned", "pauseRace", "resetRace", "for<PERSON>ach", "startChallenge", "challenge", "completeChallenge", "success", "time", "includes", "setCurrentTrack", "setTimeOfDay", "<PERSON><PERSON><PERSON><PERSON>", "set<PERSON><PERSON><PERSON><PERSON><PERSON>", "advanceStory", "unlockAbility", "addExperience", "addCurrency", "spendCurrency", "max", "setLoading", "setError", "actions", "reducer"], "sources": ["C:/Users/<USER>/Documents/Apps_Creations/SiahMan_Racing4Speed_WebGame/siah-man-racing/src/store/gameSlice.ts"], "sourcesContent": ["import { createSlice, PayloadAction } from '@reduxjs/toolkit';\nimport { \n  Track, GameMode, TimeOfDay, Weather, \n  DifficultyLevel, RaceState, Checkpoint,\n  RaceResult, Challenge, OpponentVehicle, VehicleType\n} from '../types/GameTypes';\n\n// Using Track interface from GameTypes.ts\n\nexport interface GameState { \n  // Navigation\n  currentScreen: 'mainMenu' | 'garage' | 'trackSelect' | 'race' | 'results' | 'shop' | 'challenges' | 'settings';\n  \n  // Game status\n  isPlaying: boolean;\n  gameMode: GameMode;\n  \n  // Track data\n  currentTrackId: string | null;\n  availableTracks: Track[];\n  \n  // Race state\n  activeRace: RaceState | null;\n  raceState: {\n    isRaceStarted: boolean;\n    isRaceComplete: boolean;\n    currentLap: number;\n    position: number;\n    raceTime: string;\n    bestLapTime: string | null;\n    currentLapTime: string;\n    experienceReward: number;\n    currencyReward: number;\n    finalPosition?: number;\n    speed: number;\n  };\n  vehicleState: {\n    x: number;\n    y: number;\n    speed: number;\n    acceleration: number;\n    direction: number;\n    angularVelocity: number;\n    isDrifting: boolean;\n    isOffroadSurface: boolean;\n    nitroFuel: number;\n    isNitroActive: boolean;\n\n    // Enhanced arcade features\n    isJumping?: boolean;\n    jumpHeight?: number;\n    airTime?: number;\n    driftTime?: number;\n    driftScore?: number;\n    comboMultiplier?: number;\n    lastComboTime?: number;\n    totalScore?: number;\n    coinsCollected?: number;\n    activePowerUps?: Array<{\n      type: string;\n      expiresAt: number;\n      effect: any;\n    }>;\n    isInvincible?: boolean;\n    invincibilityUntil?: number;\n    speedBoostUntil?: number;\n    coinMagnetUntil?: number;\n    doublePointsUntil?: number;\n  };\n  controls: {\n    up: boolean;\n    down: boolean;\n    left: boolean;\n    right: boolean;\n    nitro: boolean;\n    brake: boolean;\n  };\n  raceHistory: RaceResult[];\n  \n  // Challenge mode\n  availableChallenges: Challenge[];\n  completedChallenges: string[];\n  \n  // Player progression\n  storyProgress: number;\n  unlockedAbilities: string[];\n  experience: number;\n  level: number;\n  currency: number;\n  \n  // Game settings\n  selectedTimeOfDay: TimeOfDay;\n  selectedWeather: Weather;\n  difficulty: DifficultyLevel;\n  \n  // UI state\n  loading: boolean;\n  error: string | null;\n  uiAssets: Record<string, string>; // Added field for UI assets\n}\n\nconst initialState: GameState = {\n  // Navigation\n  currentScreen: 'mainMenu',\n  \n  // Game status\n  isPlaying: false,\n  gameMode: 'race',\n  \n  // Race state\n  raceState: {\n    isRaceStarted: false,\n    isRaceComplete: false,\n    currentLap: 0,\n    position: 0,\n    raceTime: '00:00.000',\n    bestLapTime: null,\n    currentLapTime: '00:00.000',\n    experienceReward: 0,\n    currencyReward: 0,\n    speed: 0\n  },\n  vehicleState: {\n    x: 100,\n    y: 100,\n    speed: 0,\n    acceleration: 0,\n    direction: 0,\n    angularVelocity: 0,\n    isDrifting: false,\n    isOffroadSurface: false,\n    nitroFuel: 100,\n    isNitroActive: false,\n\n    // Enhanced arcade features - initial values\n    isJumping: false,\n    jumpHeight: 0,\n    airTime: 0,\n    driftTime: 0,\n    driftScore: 0,\n    comboMultiplier: 1,\n    lastComboTime: 0,\n    totalScore: 0,\n    coinsCollected: 0,\n    activePowerUps: [],\n    isInvincible: false,\n    invincibilityUntil: 0,\n    speedBoostUntil: 0,\n    coinMagnetUntil: 0,\n    doublePointsUntil: 0\n  },\n  controls: {\n    up: false,\n    down: false,\n    left: false,\n    right: false,\n    nitro: false,\n    brake: false\n  },\n  \n  // Track data\n  currentTrackId: null,\n  availableTracks: [\n    {\n      id: 'beginner_track',\n      name: '🌈 Rainbow Speedway',\n      type: 'beginner',\n      length: 2000,\n      difficulty: 'easy',\n      unlocked: true,\n      description: 'Perfect for young racers! Colorful and fun!',\n      theme: 'rainbow',\n      specialFeatures: ['lots_of_coins', 'easy_turns', 'speed_pads'],\n      thumbnail: '/assets/images/track_rainbow.jpg'\n    },\n    {\n      id: 'candy_land',\n      name: '🍭 Candy Land Circuit',\n      type: 'fantasy',\n      length: 2500,\n      difficulty: 'easy',\n      unlocked: true,\n      description: 'Sweet racing through candy wonderland!',\n      theme: 'candy',\n      specialFeatures: ['jump_ramps', 'bouncy_obstacles', 'sugar_rush_zones'],\n      thumbnail: '/assets/images/track_candy.jpg'\n    },\n    {\n      id: 'space_adventure',\n      name: '🚀 Space Adventure',\n      type: 'space',\n      length: 3000,\n      difficulty: 'medium',\n      unlocked: true,\n      description: 'Race among the stars with zero gravity zones!',\n      theme: 'space',\n      specialFeatures: ['zero_gravity', 'asteroid_obstacles', 'warp_tunnels'],\n      thumbnail: '/assets/images/track_space.jpg'\n    },\n    {\n      id: 'underwater_world',\n      name: '🐠 Underwater World',\n      type: 'underwater',\n      length: 2800,\n      difficulty: 'medium',\n      unlocked: false,\n      description: 'Dive deep and race with sea creatures!',\n      theme: 'underwater',\n      specialFeatures: ['bubble_boosts', 'sea_creatures', 'coral_obstacles'],\n      thumbnail: '/assets/images/track_underwater.jpg'\n    },\n    {\n      id: 'mountain_track',\n      name: '⛰️ Mountain Adventure',\n      type: 'mountain',\n      length: 3500,\n      difficulty: 'medium',\n      unlocked: false,\n      description: 'Climb high and race through mountain peaks!',\n      theme: 'mountain',\n      specialFeatures: ['steep_climbs', 'scenic_views', 'mountain_animals'],\n      thumbnail: '/assets/images/track_mountain.jpg'\n    },\n    {\n      id: 'jungle_safari',\n      name: '🦁 Jungle Safari',\n      type: 'jungle',\n      length: 3200,\n      difficulty: 'medium',\n      unlocked: false,\n      description: 'Race through wild jungles with animal friends!',\n      theme: 'jungle',\n      specialFeatures: ['animal_crossings', 'vine_swings', 'waterfall_jumps'],\n      thumbnail: '/assets/images/track_jungle.jpg'\n    },\n    {\n      id: 'ice_kingdom',\n      name: '❄️ Ice Kingdom',\n      type: 'ice',\n      length: 2600,\n      difficulty: 'hard',\n      unlocked: false,\n      description: 'Slippery ice racing with penguin friends!',\n      theme: 'ice',\n      specialFeatures: ['ice_slides', 'penguin_obstacles', 'aurora_lights'],\n      thumbnail: '/assets/images/track_ice.jpg'\n    },\n    {\n      id: 'volcano_valley',\n      name: '🌋 Volcano Valley',\n      type: 'volcano',\n      length: 3800,\n      difficulty: 'hard',\n      unlocked: false,\n      description: 'Race through fiery lava flows and hot springs!',\n      theme: 'volcano',\n      specialFeatures: ['lava_jumps', 'hot_springs', 'fire_obstacles'],\n      thumbnail: '/assets/images/track_volcano.jpg'\n    },\n    {\n      id: 'pirate_cove',\n      name: '🏴‍☠️ Pirate Cove',\n      type: 'pirate',\n      length: 2900,\n      difficulty: 'medium',\n      unlocked: false,\n      description: 'Ahoy! Race with pirates and treasure!',\n      theme: 'pirate',\n      specialFeatures: ['treasure_chests', 'pirate_ships', 'cannon_obstacles'],\n      thumbnail: '/assets/images/track_pirate.jpg'\n    },\n    {\n      id: 'rainbow_bridge',\n      name: '🌈 Rainbow Bridge',\n      type: 'fantasy',\n      length: 2200,\n      difficulty: 'easy',\n      unlocked: false,\n      description: 'Race across magical rainbow bridges!',\n      theme: 'rainbow',\n      specialFeatures: ['rainbow_boosts', 'cloud_platforms', 'unicorn_friends'],\n      thumbnail: '/assets/images/track_rainbow_bridge.jpg'\n    },\n  ],\n  \n  // Race state\n  activeRace: null,\n  raceHistory: [],\n  \n  // Challenge mode\n  availableChallenges: [\n    {\n      id: 'challenge1',\n      name: 'Speed Challenge',\n      description: 'Complete a lap in under 60 seconds',\n      trackId: 'oval_track',\n      objective: {\n        type: 'time',\n        target: 60,\n        timeLimit: 120\n      },\n      reward: {\n        money: 1000,\n        experience: 500\n      },\n      difficulty: 'easy'\n    },\n    {\n      id: 'challenge2',\n      name: 'Overtake Challenge',\n      description: 'Overtake 10 vehicles in 2 minutes',\n      trackId: 'lake_track',\n      objective: {\n        type: 'overtake',\n        target: 10,\n        timeLimit: 120\n      },\n      reward: {\n        money: 1500,\n        experience: 700\n      },\n      difficulty: 'medium'\n    },\n    {\n      id: 'challenge3',\n      name: 'Mountain Master',\n      description: 'Complete 3 laps of the mountain track',\n      trackId: 'mountain_track',\n      objective: {\n        type: 'position',\n        target: 1,\n        timeLimit: 300\n      },\n      reward: {\n        money: 2000,\n        experience: 1000,\n        unlockTrack: 'city_track'\n      },\n      difficulty: 'hard'\n    },\n    {\n      id: 'challenge4',\n      name: 'Forest Frenzy',\n      description: 'Complete 2 laps of the forest track without crashing',\n      trackId: 'forest_track',\n      objective: {\n        type: 'position',\n        target: 1,\n        timeLimit: 240\n      },\n      reward: {\n        money: 2500,\n        experience: 1200\n      },\n      difficulty: 'medium'\n    }\n  ],\n  completedChallenges: [],\n  \n  // Player progression\n  storyProgress: 0,\n  unlockedAbilities: [],\n  experience: 0,\n  level: 1,\n  currency: 1000,\n  \n  // Game settings\n  selectedTimeOfDay: 'noon',\n  selectedWeather: 'clear',\n  difficulty: 'medium',\n  \n  // UI state\n  loading: false,\n  error: null,\n  uiAssets: {\n    tachometer_image: '/assets/images/TahometerInside.png',\n    tachometer_arrow: '/assets/images/TahometerArrow.png'\n  }\n};\n\nexport const gameSlice = createSlice({\n  name: 'game',\n  initialState,\n  reducers: {\n    // Navigation actions\n    setCurrentScreen: (state, action: PayloadAction<GameState['currentScreen']>) => {\n      state.currentScreen = action.payload;\n    },\n    \n    // Game mode actions\n    setGameMode: (state, action: PayloadAction<GameMode>) => {\n      state.gameMode = action.payload;\n    },\n    \n    // Race actions\n    startRace: (state, action: PayloadAction<{\n      trackId: string;\n      vehicleId: string;\n      gameMode: GameMode;\n      opponents?: number;\n      laps?: number;\n      weather?: Weather;\n      timeOfDay?: TimeOfDay;\n      difficulty?: DifficultyLevel;\n      challengeId?: string;\n    }>) => {\n      const { \n        trackId, \n        vehicleId, \n        gameMode, \n        opponents = 3, \n        laps = 3, \n        weather = state.selectedWeather, \n        timeOfDay = state.selectedTimeOfDay, \n        difficulty = state.difficulty,\n        challengeId \n      } = action.payload;\n      \n      state.isPlaying = true;\n      state.currentTrackId = trackId;\n      state.gameMode = gameMode;\n      \n      // Create checkpoints based on track type\n      const track = state.availableTracks.find(t => t.id === trackId);\n      const checkpoints: Checkpoint[] = [];\n      \n      // Simple checkpoint generation (would be more complex in real implementation)\n      if (track) {\n        const checkpointCount = Math.floor(track.length / 500);\n        for (let i = 0; i < checkpointCount; i++) {\n          checkpoints.push({\n            id: i,\n            position: [Math.cos(i / checkpointCount * Math.PI * 2) * 40, 0, Math.sin(i / checkpointCount * Math.PI * 2) * 40],\n            passed: false\n          });\n        }\n      }\n      \n      // Helper function to generate opponent vehicles\n      const generateOpponents = (count: number, trackId: string, mode: GameMode): OpponentVehicle[] => {\n        const opponentVehicles: OpponentVehicle[] = [];\n        \n        // Generate different opponents based on difficulty and game mode\n        for (let i = 0; i < count; i++) {\n          const vehicleTypes: VehicleType[] = ['sports', 'muscle', 'rally', 'supercar', 'concept'];\n          const randomType = vehicleTypes[Math.floor(Math.random() * vehicleTypes.length)];\n          \n          opponentVehicles.push({\n            id: `opponent-${i}`,\n            vehicleId: `vehicle-${randomType}-${i}`,\n            name: `AI Driver ${i + 1}`,\n            position: i + 1,\n            currentLap: 1,\n            lapTimes: [],\n            currentSpeed: 0,\n            difficulty: state.difficulty\n          });\n        }\n        \n        return opponentVehicles;\n      };\n      \n      // Initialize race state\n      state.activeRace = {\n        mode: gameMode,\n        trackId,\n        vehicleId,\n        opponents: generateOpponents(opponents, trackId, gameMode),\n        playerPosition: opponents,\n        currentLap: 1,\n        totalLaps: laps,\n        raceTime: 0,\n        bestLapTime: 0,\n        currentLapTime: 0,\n        checkpoints,\n        weather: weather,\n        timeOfDay: timeOfDay,\n        difficulty: difficulty,\n        paused: false,\n        finished: false,\n        challengeId: challengeId\n      };\n    },\n    \n    updateRaceState: (state, action: PayloadAction<Partial<{\n      isRaceStarted: boolean;\n      isRaceComplete: boolean;\n      currentLap: number;\n      position: number;\n      raceTime: string;\n      bestLapTime: string | null;\n      currentLapTime: string;\n      speed: number;\n    }>>) => {\n      state.raceState = {\n        ...state.raceState,\n        ...action.payload\n      };\n    },\n    \n    updateVehicleState: (state, action: PayloadAction<Partial<typeof initialState.vehicleState>>) => {\n      state.vehicleState = {\n        ...state.vehicleState,\n        ...action.payload\n      };\n    },\n    \n    setControls: (state, action: PayloadAction<Partial<{\n      up: boolean;\n      down: boolean;\n      left: boolean;\n      right: boolean;\n      nitro: boolean;\n      brake: boolean;\n    }>>) => {\n      state.controls = {\n        ...state.controls,\n        ...action.payload\n      };\n    },\n    \n    endRace: (state, action: PayloadAction<{\n      finalPosition: number;\n      finalTime: string;\n      experienceReward: number;\n      currencyReward: number;\n    }>) => {\n      state.raceState = {\n        ...state.raceState,\n        isRaceComplete: true,\n        finalPosition: action.payload.finalPosition,\n        experienceReward: action.payload.experienceReward,\n        currencyReward: action.payload.currencyReward\n      };\n      \n      state.isPlaying = false;\n      \n      // Also update the old race state format for backward compatibility\n      if (!state.activeRace) return;\n      \n      const { playerPosition, currentLap, raceTime, currentLapTime, checkpointId } = action.payload;\n      \n      if (playerPosition !== undefined) {\n        state.activeRace.playerPosition = playerPosition;\n      }\n      \n      if (currentLap !== undefined) {\n        state.activeRace.currentLap = currentLap;\n      }\n      \n      if (raceTime !== undefined) {\n        state.activeRace.raceTime = raceTime;\n      }\n      \n      if (currentLapTime !== undefined) {\n        state.activeRace.currentLapTime = currentLapTime;\n        \n        // Update best lap time if this is a new best\n        if (state.activeRace.bestLapTime === 0 || currentLapTime < state.activeRace.bestLapTime) {\n          state.activeRace.bestLapTime = currentLapTime;\n        }\n      }\n      \n      if (checkpointId !== undefined) {\n        const checkpoint = state.activeRace.checkpoints.find(c => c.id === checkpointId);\n        if (checkpoint) {\n          checkpoint.passed = true;\n          checkpoint.passTime = state.activeRace.raceTime;\n        }\n      }\n      \n      // Check if race is finished\n      if (state.activeRace.currentLap >= state.activeRace.totalLaps) {\n        state.activeRace.finished = true;\n      }\n    },\n    \n    finishRace: (state, action: PayloadAction<RaceResult>) => {\n      if (!state.activeRace) return;\n      \n      state.isPlaying = false;\n      state.activeRace.finished = true;\n      state.activeRace.raceResult = action.payload;\n      state.raceHistory.push(action.payload);\n      \n      // Update best time for the track\n      const track = state.availableTracks.find(t => t.id === state.currentTrackId);\n      if (track && state.activeRace.vehicleId) {\n        const vehicleId = state.activeRace.vehicleId;\n        if (!track.bestTimes[vehicleId] || action.payload.totalTime < track.bestTimes[vehicleId]) {\n          track.bestTimes[vehicleId] = action.payload.totalTime;\n        }\n      }\n      \n      // Add rewards\n      state.currency += action.payload.moneyEarned;\n      state.experience += action.payload.experienceEarned;\n      state.level = Math.floor(state.experience / 1000) + 1;\n    },\n    \n    pauseRace: (state, action: PayloadAction<boolean>) => {\n      if (state.activeRace) {\n        state.activeRace.paused = action.payload;\n      }\n    },\n    \n    resetRace: (state) => {\n      if (state.activeRace) {\n        state.activeRace.currentLap = 0;\n        state.activeRace.raceTime = 0;\n        state.activeRace.currentLapTime = 0;\n        state.activeRace.playerPosition = state.activeRace.opponents.length + 1;\n        state.activeRace.checkpoints.forEach(c => c.passed = false);\n        state.activeRace.finished = false;\n        state.activeRace.raceResult = undefined;\n      }\n    },\n    \n    // Challenge mode actions\n    startChallenge: (state, action: PayloadAction<string>) => {\n      const challengeId = action.payload;\n      const challenge = state.availableChallenges.find(c => c.id === challengeId);\n      \n      if (challenge) {\n        state.gameMode = 'challenge';\n        state.isPlaying = true;\n        state.currentTrackId = challenge.trackId;\n        \n        // Initialize race state for challenge\n        // This would be more complex in a real implementation\n      }\n    },\n    \n    completeChallenge: (state, action: PayloadAction<{\n      challengeId: string;\n      success: boolean;\n      time: number;\n    }>) => {\n      const { challengeId, success, time } = action.payload;\n      \n      if (success && !state.completedChallenges.includes(challengeId)) {\n        state.completedChallenges.push(challengeId);\n        \n        // Award challenge rewards\n        const challenge = state.availableChallenges.find(c => c.id === challengeId);\n        if (challenge) {\n          state.currency += challenge.reward.money;\n          state.experience += challenge.reward.experience;\n          \n          // Unlock track if specified in reward\n          if (challenge.reward.unlockTrack) {\n            const track = state.availableTracks.find(t => t.id === challenge.reward.unlockTrack);\n            if (track) {\n              track.unlocked = true;\n            }\n          }\n        }\n      }\n      \n      state.isPlaying = false;\n    },\n    \n    // Track actions\n    setCurrentTrack: (state, action: PayloadAction<string>) => {\n      state.currentTrackId = action.payload;\n    },\n    \n    unlockTrack: (state, action: PayloadAction<string>) => {\n      const track = state.availableTracks.find(t => t.id === action.payload);\n      if (track) {\n        track.unlocked = true;\n      }\n    },\n    \n    // Environment settings\n    setTimeOfDay: (state, action: PayloadAction<TimeOfDay>) => {\n      state.selectedTimeOfDay = action.payload;\n    },\n    \n    setWeather: (state, action: PayloadAction<Weather>) => {\n      state.selectedWeather = action.payload;\n    },\n    \n    setDifficulty: (state, action: PayloadAction<DifficultyLevel>) => {\n      state.difficulty = action.payload;\n    },\n    \n    // Player progression\n    advanceStory: (state) => {\n      state.storyProgress += 1;\n    },\n    \n    unlockAbility: (state, action: PayloadAction<string>) => {\n      if (!state.unlockedAbilities.includes(action.payload)) {\n        state.unlockedAbilities.push(action.payload);\n      }\n    },\n    \n    addExperience: (state, action: PayloadAction<number>) => {\n      state.experience += action.payload;\n      state.level = Math.floor(state.experience / 1000) + 1;\n    },\n    \n    // Currency management\n    addCurrency: (state, action: PayloadAction<number>) => {\n      state.currency += action.payload;\n    },\n    \n    spendCurrency: (state, action: PayloadAction<number>) => {\n      state.currency = Math.max(0, state.currency - action.payload);\n    },\n    \n    // UI state\n    setLoading: (state, action: PayloadAction<boolean>) => {\n      state.loading = action.payload;\n    },\n    \n    setError: (state, action: PayloadAction<string | null>) => {\n      state.error = action.payload;\n    },\n  },\n});\n\nexport const {\n  // Navigation\n  setCurrentScreen,\n  \n  // Game mode\n  setGameMode,\n  \n  // Race actions\n  startRace,\n  updateRaceState,\n  finishRace,\n  pauseRace,\n  resetRace,\n  \n  // Challenge mode\n  startChallenge,\n  completeChallenge,\n  \n  // Track actions\n  setCurrentTrack,\n  unlockTrack,\n  \n  // Environment settings\n  setTimeOfDay,\n  setWeather,\n  setDifficulty,\n  \n  // Player progression\n  advanceStory,\n  unlockAbility,\n  addExperience,\n  \n  // Currency management\n  addCurrency,\n  spendCurrency,\n  \n  // UI state\n  setLoading,\n  setError,\n} = gameSlice.actions;\n\nexport default gameSlice.reducer;\n"], "mappings": "AAAA,SAASA,WAAW,QAAuB,kBAAkB;;AAO7D;;AA8FA,MAAMC,YAAuB,GAAG;EAC9B;EACAC,aAAa,EAAE,UAAU;EAEzB;EACAC,SAAS,EAAE,KAAK;EAChBC,QAAQ,EAAE,MAAM;EAEhB;EACAC,SAAS,EAAE;IACTC,aAAa,EAAE,KAAK;IACpBC,cAAc,EAAE,KAAK;IACrBC,UAAU,EAAE,CAAC;IACbC,QAAQ,EAAE,CAAC;IACXC,QAAQ,EAAE,WAAW;IACrBC,WAAW,EAAE,IAAI;IACjBC,cAAc,EAAE,WAAW;IAC3BC,gBAAgB,EAAE,CAAC;IACnBC,cAAc,EAAE,CAAC;IACjBC,KAAK,EAAE;EACT,CAAC;EACDC,YAAY,EAAE;IACZC,CAAC,EAAE,GAAG;IACNC,CAAC,EAAE,GAAG;IACNH,KAAK,EAAE,CAAC;IACRI,YAAY,EAAE,CAAC;IACfC,SAAS,EAAE,CAAC;IACZC,eAAe,EAAE,CAAC;IAClBC,UAAU,EAAE,KAAK;IACjBC,gBAAgB,EAAE,KAAK;IACvBC,SAAS,EAAE,GAAG;IACdC,aAAa,EAAE,KAAK;IAEpB;IACAC,SAAS,EAAE,KAAK;IAChBC,UAAU,EAAE,CAAC;IACbC,OAAO,EAAE,CAAC;IACVC,SAAS,EAAE,CAAC;IACZC,UAAU,EAAE,CAAC;IACbC,eAAe,EAAE,CAAC;IAClBC,aAAa,EAAE,CAAC;IAChBC,UAAU,EAAE,CAAC;IACbC,cAAc,EAAE,CAAC;IACjBC,cAAc,EAAE,EAAE;IAClBC,YAAY,EAAE,KAAK;IACnBC,kBAAkB,EAAE,CAAC;IACrBC,eAAe,EAAE,CAAC;IAClBC,eAAe,EAAE,CAAC;IAClBC,iBAAiB,EAAE;EACrB,CAAC;EACDC,QAAQ,EAAE;IACRC,EAAE,EAAE,KAAK;IACTC,IAAI,EAAE,KAAK;IACXC,IAAI,EAAE,KAAK;IACXC,KAAK,EAAE,KAAK;IACZC,KAAK,EAAE,KAAK;IACZC,KAAK,EAAE;EACT,CAAC;EAED;EACAC,cAAc,EAAE,IAAI;EACpBC,eAAe,EAAE,CACf;IACEC,EAAE,EAAE,gBAAgB;IACpBC,IAAI,EAAE,qBAAqB;IAC3BC,IAAI,EAAE,UAAU;IAChBC,MAAM,EAAE,IAAI;IACZC,UAAU,EAAE,MAAM;IAClBC,QAAQ,EAAE,IAAI;IACdC,WAAW,EAAE,6CAA6C;IAC1DC,KAAK,EAAE,SAAS;IAChBC,eAAe,EAAE,CAAC,eAAe,EAAE,YAAY,EAAE,YAAY,CAAC;IAC9DC,SAAS,EAAE;EACb,CAAC,EACD;IACET,EAAE,EAAE,YAAY;IAChBC,IAAI,EAAE,uBAAuB;IAC7BC,IAAI,EAAE,SAAS;IACfC,MAAM,EAAE,IAAI;IACZC,UAAU,EAAE,MAAM;IAClBC,QAAQ,EAAE,IAAI;IACdC,WAAW,EAAE,wCAAwC;IACrDC,KAAK,EAAE,OAAO;IACdC,eAAe,EAAE,CAAC,YAAY,EAAE,kBAAkB,EAAE,kBAAkB,CAAC;IACvEC,SAAS,EAAE;EACb,CAAC,EACD;IACET,EAAE,EAAE,iBAAiB;IACrBC,IAAI,EAAE,oBAAoB;IAC1BC,IAAI,EAAE,OAAO;IACbC,MAAM,EAAE,IAAI;IACZC,UAAU,EAAE,QAAQ;IACpBC,QAAQ,EAAE,IAAI;IACdC,WAAW,EAAE,+CAA+C;IAC5DC,KAAK,EAAE,OAAO;IACdC,eAAe,EAAE,CAAC,cAAc,EAAE,oBAAoB,EAAE,cAAc,CAAC;IACvEC,SAAS,EAAE;EACb,CAAC,EACD;IACET,EAAE,EAAE,kBAAkB;IACtBC,IAAI,EAAE,qBAAqB;IAC3BC,IAAI,EAAE,YAAY;IAClBC,MAAM,EAAE,IAAI;IACZC,UAAU,EAAE,QAAQ;IACpBC,QAAQ,EAAE,KAAK;IACfC,WAAW,EAAE,wCAAwC;IACrDC,KAAK,EAAE,YAAY;IACnBC,eAAe,EAAE,CAAC,eAAe,EAAE,eAAe,EAAE,iBAAiB,CAAC;IACtEC,SAAS,EAAE;EACb,CAAC,EACD;IACET,EAAE,EAAE,gBAAgB;IACpBC,IAAI,EAAE,uBAAuB;IAC7BC,IAAI,EAAE,UAAU;IAChBC,MAAM,EAAE,IAAI;IACZC,UAAU,EAAE,QAAQ;IACpBC,QAAQ,EAAE,KAAK;IACfC,WAAW,EAAE,6CAA6C;IAC1DC,KAAK,EAAE,UAAU;IACjBC,eAAe,EAAE,CAAC,cAAc,EAAE,cAAc,EAAE,kBAAkB,CAAC;IACrEC,SAAS,EAAE;EACb,CAAC,EACD;IACET,EAAE,EAAE,eAAe;IACnBC,IAAI,EAAE,kBAAkB;IACxBC,IAAI,EAAE,QAAQ;IACdC,MAAM,EAAE,IAAI;IACZC,UAAU,EAAE,QAAQ;IACpBC,QAAQ,EAAE,KAAK;IACfC,WAAW,EAAE,gDAAgD;IAC7DC,KAAK,EAAE,QAAQ;IACfC,eAAe,EAAE,CAAC,kBAAkB,EAAE,aAAa,EAAE,iBAAiB,CAAC;IACvEC,SAAS,EAAE;EACb,CAAC,EACD;IACET,EAAE,EAAE,aAAa;IACjBC,IAAI,EAAE,gBAAgB;IACtBC,IAAI,EAAE,KAAK;IACXC,MAAM,EAAE,IAAI;IACZC,UAAU,EAAE,MAAM;IAClBC,QAAQ,EAAE,KAAK;IACfC,WAAW,EAAE,2CAA2C;IACxDC,KAAK,EAAE,KAAK;IACZC,eAAe,EAAE,CAAC,YAAY,EAAE,mBAAmB,EAAE,eAAe,CAAC;IACrEC,SAAS,EAAE;EACb,CAAC,EACD;IACET,EAAE,EAAE,gBAAgB;IACpBC,IAAI,EAAE,mBAAmB;IACzBC,IAAI,EAAE,SAAS;IACfC,MAAM,EAAE,IAAI;IACZC,UAAU,EAAE,MAAM;IAClBC,QAAQ,EAAE,KAAK;IACfC,WAAW,EAAE,gDAAgD;IAC7DC,KAAK,EAAE,SAAS;IAChBC,eAAe,EAAE,CAAC,YAAY,EAAE,aAAa,EAAE,gBAAgB,CAAC;IAChEC,SAAS,EAAE;EACb,CAAC,EACD;IACET,EAAE,EAAE,aAAa;IACjBC,IAAI,EAAE,mBAAmB;IACzBC,IAAI,EAAE,QAAQ;IACdC,MAAM,EAAE,IAAI;IACZC,UAAU,EAAE,QAAQ;IACpBC,QAAQ,EAAE,KAAK;IACfC,WAAW,EAAE,uCAAuC;IACpDC,KAAK,EAAE,QAAQ;IACfC,eAAe,EAAE,CAAC,iBAAiB,EAAE,cAAc,EAAE,kBAAkB,CAAC;IACxEC,SAAS,EAAE;EACb,CAAC,EACD;IACET,EAAE,EAAE,gBAAgB;IACpBC,IAAI,EAAE,mBAAmB;IACzBC,IAAI,EAAE,SAAS;IACfC,MAAM,EAAE,IAAI;IACZC,UAAU,EAAE,MAAM;IAClBC,QAAQ,EAAE,KAAK;IACfC,WAAW,EAAE,sCAAsC;IACnDC,KAAK,EAAE,SAAS;IAChBC,eAAe,EAAE,CAAC,gBAAgB,EAAE,iBAAiB,EAAE,iBAAiB,CAAC;IACzEC,SAAS,EAAE;EACb,CAAC,CACF;EAED;EACAC,UAAU,EAAE,IAAI;EAChBC,WAAW,EAAE,EAAE;EAEf;EACAC,mBAAmB,EAAE,CACnB;IACEZ,EAAE,EAAE,YAAY;IAChBC,IAAI,EAAE,iBAAiB;IACvBK,WAAW,EAAE,oCAAoC;IACjDO,OAAO,EAAE,YAAY;IACrBC,SAAS,EAAE;MACTZ,IAAI,EAAE,MAAM;MACZa,MAAM,EAAE,EAAE;MACVC,SAAS,EAAE;IACb,CAAC;IACDC,MAAM,EAAE;MACNC,KAAK,EAAE,IAAI;MACXC,UAAU,EAAE;IACd,CAAC;IACDf,UAAU,EAAE;EACd,CAAC,EACD;IACEJ,EAAE,EAAE,YAAY;IAChBC,IAAI,EAAE,oBAAoB;IAC1BK,WAAW,EAAE,mCAAmC;IAChDO,OAAO,EAAE,YAAY;IACrBC,SAAS,EAAE;MACTZ,IAAI,EAAE,UAAU;MAChBa,MAAM,EAAE,EAAE;MACVC,SAAS,EAAE;IACb,CAAC;IACDC,MAAM,EAAE;MACNC,KAAK,EAAE,IAAI;MACXC,UAAU,EAAE;IACd,CAAC;IACDf,UAAU,EAAE;EACd,CAAC,EACD;IACEJ,EAAE,EAAE,YAAY;IAChBC,IAAI,EAAE,iBAAiB;IACvBK,WAAW,EAAE,uCAAuC;IACpDO,OAAO,EAAE,gBAAgB;IACzBC,SAAS,EAAE;MACTZ,IAAI,EAAE,UAAU;MAChBa,MAAM,EAAE,CAAC;MACTC,SAAS,EAAE;IACb,CAAC;IACDC,MAAM,EAAE;MACNC,KAAK,EAAE,IAAI;MACXC,UAAU,EAAE,IAAI;MAChBC,WAAW,EAAE;IACf,CAAC;IACDhB,UAAU,EAAE;EACd,CAAC,EACD;IACEJ,EAAE,EAAE,YAAY;IAChBC,IAAI,EAAE,eAAe;IACrBK,WAAW,EAAE,sDAAsD;IACnEO,OAAO,EAAE,cAAc;IACvBC,SAAS,EAAE;MACTZ,IAAI,EAAE,UAAU;MAChBa,MAAM,EAAE,CAAC;MACTC,SAAS,EAAE;IACb,CAAC;IACDC,MAAM,EAAE;MACNC,KAAK,EAAE,IAAI;MACXC,UAAU,EAAE;IACd,CAAC;IACDf,UAAU,EAAE;EACd,CAAC,CACF;EACDiB,mBAAmB,EAAE,EAAE;EAEvB;EACAC,aAAa,EAAE,CAAC;EAChBC,iBAAiB,EAAE,EAAE;EACrBJ,UAAU,EAAE,CAAC;EACbK,KAAK,EAAE,CAAC;EACRC,QAAQ,EAAE,IAAI;EAEd;EACAC,iBAAiB,EAAE,MAAM;EACzBC,eAAe,EAAE,OAAO;EACxBvB,UAAU,EAAE,QAAQ;EAEpB;EACAwB,OAAO,EAAE,KAAK;EACdC,KAAK,EAAE,IAAI;EACXC,QAAQ,EAAE;IACRC,gBAAgB,EAAE,oCAAoC;IACtDC,gBAAgB,EAAE;EACpB;AACF,CAAC;AAED,OAAO,MAAMC,SAAS,GAAGnF,WAAW,CAAC;EACnCmD,IAAI,EAAE,MAAM;EACZlD,YAAY;EACZmF,QAAQ,EAAE;IACR;IACAC,gBAAgB,EAAEA,CAACC,KAAK,EAAEC,MAAiD,KAAK;MAC9ED,KAAK,CAACpF,aAAa,GAAGqF,MAAM,CAACC,OAAO;IACtC,CAAC;IAED;IACAC,WAAW,EAAEA,CAACH,KAAK,EAAEC,MAA+B,KAAK;MACvDD,KAAK,CAAClF,QAAQ,GAAGmF,MAAM,CAACC,OAAO;IACjC,CAAC;IAED;IACAE,SAAS,EAAEA,CAACJ,KAAK,EAAEC,MAUjB,KAAK;MACL,MAAM;QACJxB,OAAO;QACP4B,SAAS;QACTvF,QAAQ;QACRwF,SAAS,GAAG,CAAC;QACbC,IAAI,GAAG,CAAC;QACRC,OAAO,GAAGR,KAAK,CAACT,eAAe;QAC/BkB,SAAS,GAAGT,KAAK,CAACV,iBAAiB;QACnCtB,UAAU,GAAGgC,KAAK,CAAChC,UAAU;QAC7B0C;MACF,CAAC,GAAGT,MAAM,CAACC,OAAO;MAElBF,KAAK,CAACnF,SAAS,GAAG,IAAI;MACtBmF,KAAK,CAACtC,cAAc,GAAGe,OAAO;MAC9BuB,KAAK,CAAClF,QAAQ,GAAGA,QAAQ;;MAEzB;MACA,MAAM6F,KAAK,GAAGX,KAAK,CAACrC,eAAe,CAACiD,IAAI,CAACC,CAAC,IAAIA,CAAC,CAACjD,EAAE,KAAKa,OAAO,CAAC;MAC/D,MAAMqC,WAAyB,GAAG,EAAE;;MAEpC;MACA,IAAIH,KAAK,EAAE;QACT,MAAMI,eAAe,GAAGC,IAAI,CAACC,KAAK,CAACN,KAAK,CAAC5C,MAAM,GAAG,GAAG,CAAC;QACtD,KAAK,IAAImD,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGH,eAAe,EAAEG,CAAC,EAAE,EAAE;UACxCJ,WAAW,CAACK,IAAI,CAAC;YACfvD,EAAE,EAAEsD,CAAC;YACL/F,QAAQ,EAAE,CAAC6F,IAAI,CAACI,GAAG,CAACF,CAAC,GAAGH,eAAe,GAAGC,IAAI,CAACK,EAAE,GAAG,CAAC,CAAC,GAAG,EAAE,EAAE,CAAC,EAAEL,IAAI,CAACM,GAAG,CAACJ,CAAC,GAAGH,eAAe,GAAGC,IAAI,CAACK,EAAE,GAAG,CAAC,CAAC,GAAG,EAAE,CAAC;YACjHE,MAAM,EAAE;UACV,CAAC,CAAC;QACJ;MACF;;MAEA;MACA,MAAMC,iBAAiB,GAAGA,CAACC,KAAa,EAAEhD,OAAe,EAAEiD,IAAc,KAAwB;QAC/F,MAAMC,gBAAmC,GAAG,EAAE;;QAE9C;QACA,KAAK,IAAIT,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGO,KAAK,EAAEP,CAAC,EAAE,EAAE;UAC9B,MAAMU,YAA2B,GAAG,CAAC,QAAQ,EAAE,QAAQ,EAAE,OAAO,EAAE,UAAU,EAAE,SAAS,CAAC;UACxF,MAAMC,UAAU,GAAGD,YAAY,CAACZ,IAAI,CAACC,KAAK,CAACD,IAAI,CAACc,MAAM,CAAC,CAAC,GAAGF,YAAY,CAAC7D,MAAM,CAAC,CAAC;UAEhF4D,gBAAgB,CAACR,IAAI,CAAC;YACpBvD,EAAE,EAAE,YAAYsD,CAAC,EAAE;YACnBb,SAAS,EAAE,WAAWwB,UAAU,IAAIX,CAAC,EAAE;YACvCrD,IAAI,EAAE,aAAaqD,CAAC,GAAG,CAAC,EAAE;YAC1B/F,QAAQ,EAAE+F,CAAC,GAAG,CAAC;YACfhG,UAAU,EAAE,CAAC;YACb6G,QAAQ,EAAE,EAAE;YACZC,YAAY,EAAE,CAAC;YACfhE,UAAU,EAAEgC,KAAK,CAAChC;UACpB,CAAC,CAAC;QACJ;QAEA,OAAO2D,gBAAgB;MACzB,CAAC;;MAED;MACA3B,KAAK,CAAC1B,UAAU,GAAG;QACjBoD,IAAI,EAAE5G,QAAQ;QACd2D,OAAO;QACP4B,SAAS;QACTC,SAAS,EAAEkB,iBAAiB,CAAClB,SAAS,EAAE7B,OAAO,EAAE3D,QAAQ,CAAC;QAC1DmH,cAAc,EAAE3B,SAAS;QACzBpF,UAAU,EAAE,CAAC;QACbgH,SAAS,EAAE3B,IAAI;QACfnF,QAAQ,EAAE,CAAC;QACXC,WAAW,EAAE,CAAC;QACdC,cAAc,EAAE,CAAC;QACjBwF,WAAW;QACXN,OAAO,EAAEA,OAAO;QAChBC,SAAS,EAAEA,SAAS;QACpBzC,UAAU,EAAEA,UAAU;QACtBmE,MAAM,EAAE,KAAK;QACbC,QAAQ,EAAE,KAAK;QACf1B,WAAW,EAAEA;MACf,CAAC;IACH,CAAC;IAED2B,eAAe,EAAEA,CAACrC,KAAK,EAAEC,MAStB,KAAK;MACND,KAAK,CAACjF,SAAS,GAAG;QAChB,GAAGiF,KAAK,CAACjF,SAAS;QAClB,GAAGkF,MAAM,CAACC;MACZ,CAAC;IACH,CAAC;IAEDoC,kBAAkB,EAAEA,CAACtC,KAAK,EAAEC,MAAgE,KAAK;MAC/FD,KAAK,CAACtE,YAAY,GAAG;QACnB,GAAGsE,KAAK,CAACtE,YAAY;QACrB,GAAGuE,MAAM,CAACC;MACZ,CAAC;IACH,CAAC;IAEDqC,WAAW,EAAEA,CAACvC,KAAK,EAAEC,MAOlB,KAAK;MACND,KAAK,CAAC7C,QAAQ,GAAG;QACf,GAAG6C,KAAK,CAAC7C,QAAQ;QACjB,GAAG8C,MAAM,CAACC;MACZ,CAAC;IACH,CAAC;IAEDsC,OAAO,EAAEA,CAACxC,KAAK,EAAEC,MAKf,KAAK;MACLD,KAAK,CAACjF,SAAS,GAAG;QAChB,GAAGiF,KAAK,CAACjF,SAAS;QAClBE,cAAc,EAAE,IAAI;QACpBwH,aAAa,EAAExC,MAAM,CAACC,OAAO,CAACuC,aAAa;QAC3ClH,gBAAgB,EAAE0E,MAAM,CAACC,OAAO,CAAC3E,gBAAgB;QACjDC,cAAc,EAAEyE,MAAM,CAACC,OAAO,CAAC1E;MACjC,CAAC;MAEDwE,KAAK,CAACnF,SAAS,GAAG,KAAK;;MAEvB;MACA,IAAI,CAACmF,KAAK,CAAC1B,UAAU,EAAE;MAEvB,MAAM;QAAE2D,cAAc;QAAE/G,UAAU;QAAEE,QAAQ;QAAEE,cAAc;QAAEoH;MAAa,CAAC,GAAGzC,MAAM,CAACC,OAAO;MAE7F,IAAI+B,cAAc,KAAKU,SAAS,EAAE;QAChC3C,KAAK,CAAC1B,UAAU,CAAC2D,cAAc,GAAGA,cAAc;MAClD;MAEA,IAAI/G,UAAU,KAAKyH,SAAS,EAAE;QAC5B3C,KAAK,CAAC1B,UAAU,CAACpD,UAAU,GAAGA,UAAU;MAC1C;MAEA,IAAIE,QAAQ,KAAKuH,SAAS,EAAE;QAC1B3C,KAAK,CAAC1B,UAAU,CAAClD,QAAQ,GAAGA,QAAQ;MACtC;MAEA,IAAIE,cAAc,KAAKqH,SAAS,EAAE;QAChC3C,KAAK,CAAC1B,UAAU,CAAChD,cAAc,GAAGA,cAAc;;QAEhD;QACA,IAAI0E,KAAK,CAAC1B,UAAU,CAACjD,WAAW,KAAK,CAAC,IAAIC,cAAc,GAAG0E,KAAK,CAAC1B,UAAU,CAACjD,WAAW,EAAE;UACvF2E,KAAK,CAAC1B,UAAU,CAACjD,WAAW,GAAGC,cAAc;QAC/C;MACF;MAEA,IAAIoH,YAAY,KAAKC,SAAS,EAAE;QAC9B,MAAMC,UAAU,GAAG5C,KAAK,CAAC1B,UAAU,CAACwC,WAAW,CAACF,IAAI,CAACiC,CAAC,IAAIA,CAAC,CAACjF,EAAE,KAAK8E,YAAY,CAAC;QAChF,IAAIE,UAAU,EAAE;UACdA,UAAU,CAACrB,MAAM,GAAG,IAAI;UACxBqB,UAAU,CAACE,QAAQ,GAAG9C,KAAK,CAAC1B,UAAU,CAAClD,QAAQ;QACjD;MACF;;MAEA;MACA,IAAI4E,KAAK,CAAC1B,UAAU,CAACpD,UAAU,IAAI8E,KAAK,CAAC1B,UAAU,CAAC4D,SAAS,EAAE;QAC7DlC,KAAK,CAAC1B,UAAU,CAAC8D,QAAQ,GAAG,IAAI;MAClC;IACF,CAAC;IAEDW,UAAU,EAAEA,CAAC/C,KAAK,EAAEC,MAAiC,KAAK;MACxD,IAAI,CAACD,KAAK,CAAC1B,UAAU,EAAE;MAEvB0B,KAAK,CAACnF,SAAS,GAAG,KAAK;MACvBmF,KAAK,CAAC1B,UAAU,CAAC8D,QAAQ,GAAG,IAAI;MAChCpC,KAAK,CAAC1B,UAAU,CAAC0E,UAAU,GAAG/C,MAAM,CAACC,OAAO;MAC5CF,KAAK,CAACzB,WAAW,CAAC4C,IAAI,CAAClB,MAAM,CAACC,OAAO,CAAC;;MAEtC;MACA,MAAMS,KAAK,GAAGX,KAAK,CAACrC,eAAe,CAACiD,IAAI,CAACC,CAAC,IAAIA,CAAC,CAACjD,EAAE,KAAKoC,KAAK,CAACtC,cAAc,CAAC;MAC5E,IAAIiD,KAAK,IAAIX,KAAK,CAAC1B,UAAU,CAAC+B,SAAS,EAAE;QACvC,MAAMA,SAAS,GAAGL,KAAK,CAAC1B,UAAU,CAAC+B,SAAS;QAC5C,IAAI,CAACM,KAAK,CAACsC,SAAS,CAAC5C,SAAS,CAAC,IAAIJ,MAAM,CAACC,OAAO,CAACgD,SAAS,GAAGvC,KAAK,CAACsC,SAAS,CAAC5C,SAAS,CAAC,EAAE;UACxFM,KAAK,CAACsC,SAAS,CAAC5C,SAAS,CAAC,GAAGJ,MAAM,CAACC,OAAO,CAACgD,SAAS;QACvD;MACF;;MAEA;MACAlD,KAAK,CAACX,QAAQ,IAAIY,MAAM,CAACC,OAAO,CAACiD,WAAW;MAC5CnD,KAAK,CAACjB,UAAU,IAAIkB,MAAM,CAACC,OAAO,CAACkD,gBAAgB;MACnDpD,KAAK,CAACZ,KAAK,GAAG4B,IAAI,CAACC,KAAK,CAACjB,KAAK,CAACjB,UAAU,GAAG,IAAI,CAAC,GAAG,CAAC;IACvD,CAAC;IAEDsE,SAAS,EAAEA,CAACrD,KAAK,EAAEC,MAA8B,KAAK;MACpD,IAAID,KAAK,CAAC1B,UAAU,EAAE;QACpB0B,KAAK,CAAC1B,UAAU,CAAC6D,MAAM,GAAGlC,MAAM,CAACC,OAAO;MAC1C;IACF,CAAC;IAEDoD,SAAS,EAAGtD,KAAK,IAAK;MACpB,IAAIA,KAAK,CAAC1B,UAAU,EAAE;QACpB0B,KAAK,CAAC1B,UAAU,CAACpD,UAAU,GAAG,CAAC;QAC/B8E,KAAK,CAAC1B,UAAU,CAAClD,QAAQ,GAAG,CAAC;QAC7B4E,KAAK,CAAC1B,UAAU,CAAChD,cAAc,GAAG,CAAC;QACnC0E,KAAK,CAAC1B,UAAU,CAAC2D,cAAc,GAAGjC,KAAK,CAAC1B,UAAU,CAACgC,SAAS,CAACvC,MAAM,GAAG,CAAC;QACvEiC,KAAK,CAAC1B,UAAU,CAACwC,WAAW,CAACyC,OAAO,CAACV,CAAC,IAAIA,CAAC,CAACtB,MAAM,GAAG,KAAK,CAAC;QAC3DvB,KAAK,CAAC1B,UAAU,CAAC8D,QAAQ,GAAG,KAAK;QACjCpC,KAAK,CAAC1B,UAAU,CAAC0E,UAAU,GAAGL,SAAS;MACzC;IACF,CAAC;IAED;IACAa,cAAc,EAAEA,CAACxD,KAAK,EAAEC,MAA6B,KAAK;MACxD,MAAMS,WAAW,GAAGT,MAAM,CAACC,OAAO;MAClC,MAAMuD,SAAS,GAAGzD,KAAK,CAACxB,mBAAmB,CAACoC,IAAI,CAACiC,CAAC,IAAIA,CAAC,CAACjF,EAAE,KAAK8C,WAAW,CAAC;MAE3E,IAAI+C,SAAS,EAAE;QACbzD,KAAK,CAAClF,QAAQ,GAAG,WAAW;QAC5BkF,KAAK,CAACnF,SAAS,GAAG,IAAI;QACtBmF,KAAK,CAACtC,cAAc,GAAG+F,SAAS,CAAChF,OAAO;;QAExC;QACA;MACF;IACF,CAAC;IAEDiF,iBAAiB,EAAEA,CAAC1D,KAAK,EAAEC,MAIzB,KAAK;MACL,MAAM;QAAES,WAAW;QAAEiD,OAAO;QAAEC;MAAK,CAAC,GAAG3D,MAAM,CAACC,OAAO;MAErD,IAAIyD,OAAO,IAAI,CAAC3D,KAAK,CAACf,mBAAmB,CAAC4E,QAAQ,CAACnD,WAAW,CAAC,EAAE;QAC/DV,KAAK,CAACf,mBAAmB,CAACkC,IAAI,CAACT,WAAW,CAAC;;QAE3C;QACA,MAAM+C,SAAS,GAAGzD,KAAK,CAACxB,mBAAmB,CAACoC,IAAI,CAACiC,CAAC,IAAIA,CAAC,CAACjF,EAAE,KAAK8C,WAAW,CAAC;QAC3E,IAAI+C,SAAS,EAAE;UACbzD,KAAK,CAACX,QAAQ,IAAIoE,SAAS,CAAC5E,MAAM,CAACC,KAAK;UACxCkB,KAAK,CAACjB,UAAU,IAAI0E,SAAS,CAAC5E,MAAM,CAACE,UAAU;;UAE/C;UACA,IAAI0E,SAAS,CAAC5E,MAAM,CAACG,WAAW,EAAE;YAChC,MAAM2B,KAAK,GAAGX,KAAK,CAACrC,eAAe,CAACiD,IAAI,CAACC,CAAC,IAAIA,CAAC,CAACjD,EAAE,KAAK6F,SAAS,CAAC5E,MAAM,CAACG,WAAW,CAAC;YACpF,IAAI2B,KAAK,EAAE;cACTA,KAAK,CAAC1C,QAAQ,GAAG,IAAI;YACvB;UACF;QACF;MACF;MAEA+B,KAAK,CAACnF,SAAS,GAAG,KAAK;IACzB,CAAC;IAED;IACAiJ,eAAe,EAAEA,CAAC9D,KAAK,EAAEC,MAA6B,KAAK;MACzDD,KAAK,CAACtC,cAAc,GAAGuC,MAAM,CAACC,OAAO;IACvC,CAAC;IAEDlB,WAAW,EAAEA,CAACgB,KAAK,EAAEC,MAA6B,KAAK;MACrD,MAAMU,KAAK,GAAGX,KAAK,CAACrC,eAAe,CAACiD,IAAI,CAACC,CAAC,IAAIA,CAAC,CAACjD,EAAE,KAAKqC,MAAM,CAACC,OAAO,CAAC;MACtE,IAAIS,KAAK,EAAE;QACTA,KAAK,CAAC1C,QAAQ,GAAG,IAAI;MACvB;IACF,CAAC;IAED;IACA8F,YAAY,EAAEA,CAAC/D,KAAK,EAAEC,MAAgC,KAAK;MACzDD,KAAK,CAACV,iBAAiB,GAAGW,MAAM,CAACC,OAAO;IAC1C,CAAC;IAED8D,UAAU,EAAEA,CAAChE,KAAK,EAAEC,MAA8B,KAAK;MACrDD,KAAK,CAACT,eAAe,GAAGU,MAAM,CAACC,OAAO;IACxC,CAAC;IAED+D,aAAa,EAAEA,CAACjE,KAAK,EAAEC,MAAsC,KAAK;MAChED,KAAK,CAAChC,UAAU,GAAGiC,MAAM,CAACC,OAAO;IACnC,CAAC;IAED;IACAgE,YAAY,EAAGlE,KAAK,IAAK;MACvBA,KAAK,CAACd,aAAa,IAAI,CAAC;IAC1B,CAAC;IAEDiF,aAAa,EAAEA,CAACnE,KAAK,EAAEC,MAA6B,KAAK;MACvD,IAAI,CAACD,KAAK,CAACb,iBAAiB,CAAC0E,QAAQ,CAAC5D,MAAM,CAACC,OAAO,CAAC,EAAE;QACrDF,KAAK,CAACb,iBAAiB,CAACgC,IAAI,CAAClB,MAAM,CAACC,OAAO,CAAC;MAC9C;IACF,CAAC;IAEDkE,aAAa,EAAEA,CAACpE,KAAK,EAAEC,MAA6B,KAAK;MACvDD,KAAK,CAACjB,UAAU,IAAIkB,MAAM,CAACC,OAAO;MAClCF,KAAK,CAACZ,KAAK,GAAG4B,IAAI,CAACC,KAAK,CAACjB,KAAK,CAACjB,UAAU,GAAG,IAAI,CAAC,GAAG,CAAC;IACvD,CAAC;IAED;IACAsF,WAAW,EAAEA,CAACrE,KAAK,EAAEC,MAA6B,KAAK;MACrDD,KAAK,CAACX,QAAQ,IAAIY,MAAM,CAACC,OAAO;IAClC,CAAC;IAEDoE,aAAa,EAAEA,CAACtE,KAAK,EAAEC,MAA6B,KAAK;MACvDD,KAAK,CAACX,QAAQ,GAAG2B,IAAI,CAACuD,GAAG,CAAC,CAAC,EAAEvE,KAAK,CAACX,QAAQ,GAAGY,MAAM,CAACC,OAAO,CAAC;IAC/D,CAAC;IAED;IACAsE,UAAU,EAAEA,CAACxE,KAAK,EAAEC,MAA8B,KAAK;MACrDD,KAAK,CAACR,OAAO,GAAGS,MAAM,CAACC,OAAO;IAChC,CAAC;IAEDuE,QAAQ,EAAEA,CAACzE,KAAK,EAAEC,MAAoC,KAAK;MACzDD,KAAK,CAACP,KAAK,GAAGQ,MAAM,CAACC,OAAO;IAC9B;EACF;AACF,CAAC,CAAC;AAEF,OAAO,MAAM;EACX;EACAH,gBAAgB;EAEhB;EACAI,WAAW;EAEX;EACAC,SAAS;EACTiC,eAAe;EACfU,UAAU;EACVM,SAAS;EACTC,SAAS;EAET;EACAE,cAAc;EACdE,iBAAiB;EAEjB;EACAI,eAAe;EACf9E,WAAW;EAEX;EACA+E,YAAY;EACZC,UAAU;EACVC,aAAa;EAEb;EACAC,YAAY;EACZC,aAAa;EACbC,aAAa;EAEb;EACAC,WAAW;EACXC,aAAa;EAEb;EACAE,UAAU;EACVC;AACF,CAAC,GAAG5E,SAAS,CAAC6E,OAAO;AAErB,eAAe7E,SAAS,CAAC8E,OAAO", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}