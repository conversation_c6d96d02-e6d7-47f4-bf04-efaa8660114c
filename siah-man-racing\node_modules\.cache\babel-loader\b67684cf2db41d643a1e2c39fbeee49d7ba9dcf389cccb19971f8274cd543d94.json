{"ast": null, "code": "import { Vector3 } from \"three\";\nconst _v1 = /* @__PURE__ */new Vector3();\nconst _v2 = /* @__PURE__ */new Vector3();\nconst _v3 = /* @__PURE__ */new Vector3();\nconst EPS = 1e-10;\nclass Capsule {\n  constructor(start = new Vector3(0, 0, 0), end = new Vector3(0, 1, 0), radius = 1) {\n    this.start = start;\n    this.end = end;\n    this.radius = radius;\n  }\n  clone() {\n    return new Capsule(this.start.clone(), this.end.clone(), this.radius);\n  }\n  set(start, end, radius) {\n    this.start.copy(start);\n    this.end.copy(end);\n    this.radius = radius;\n  }\n  copy(capsule) {\n    this.start.copy(capsule.start);\n    this.end.copy(capsule.end);\n    this.radius = capsule.radius;\n  }\n  getCenter(target) {\n    return target.copy(this.end).add(this.start).multiplyScalar(0.5);\n  }\n  translate(v) {\n    this.start.add(v);\n    this.end.add(v);\n  }\n  checkAABBAxis(p1x, p1y, p2x, p2y, minx, maxx, miny, maxy, radius) {\n    return (minx - p1x < radius || minx - p2x < radius) && (p1x - maxx < radius || p2x - maxx < radius) && (miny - p1y < radius || miny - p2y < radius) && (p1y - maxy < radius || p2y - maxy < radius);\n  }\n  intersectsBox(box) {\n    return this.checkAABBAxis(this.start.x, this.start.y, this.end.x, this.end.y, box.min.x, box.max.x, box.min.y, box.max.y, this.radius) && this.checkAABBAxis(this.start.x, this.start.z, this.end.x, this.end.z, box.min.x, box.max.x, box.min.z, box.max.z, this.radius) && this.checkAABBAxis(this.start.y, this.start.z, this.end.y, this.end.z, box.min.y, box.max.y, box.min.z, box.max.z, this.radius);\n  }\n  lineLineMinimumPoints(line1, line2) {\n    const r = _v1.copy(line1.end).sub(line1.start);\n    const s = _v2.copy(line2.end).sub(line2.start);\n    const w = _v3.copy(line2.start).sub(line1.start);\n    const a = r.dot(s),\n      b = r.dot(r),\n      c = s.dot(s),\n      d = s.dot(w),\n      e = r.dot(w);\n    let t1, t2;\n    const divisor = b * c - a * a;\n    if (Math.abs(divisor) < EPS) {\n      const d1 = -d / c;\n      const d2 = (a - d) / c;\n      if (Math.abs(d1 - 0.5) < Math.abs(d2 - 0.5)) {\n        t1 = 0;\n        t2 = d1;\n      } else {\n        t1 = 1;\n        t2 = d2;\n      }\n    } else {\n      t1 = (d * a + e * c) / divisor;\n      t2 = (t1 * a - d) / c;\n    }\n    t2 = Math.max(0, Math.min(1, t2));\n    t1 = Math.max(0, Math.min(1, t1));\n    const point1 = r.multiplyScalar(t1).add(line1.start);\n    const point2 = s.multiplyScalar(t2).add(line2.start);\n    return [point1, point2];\n  }\n}\nexport { Capsule };", "map": {"version": 3, "names": ["_v1", "Vector3", "_v2", "_v3", "EPS", "Capsule", "constructor", "start", "end", "radius", "clone", "set", "copy", "capsule", "getCenter", "target", "add", "multiplyScalar", "translate", "v", "checkAABBAxis", "p1x", "p1y", "p2x", "p2y", "minx", "maxx", "miny", "maxy", "intersectsBox", "box", "x", "y", "min", "max", "z", "lineLineMinimumPoints", "line1", "line2", "r", "sub", "s", "w", "a", "dot", "b", "c", "d", "e", "t1", "t2", "divisor", "Math", "abs", "d1", "d2", "point1", "point2"], "sources": ["C:\\Users\\<USER>\\Documents\\Apps_Creations\\SiahMan_Racing4Speed_WebGame\\siah-man-racing\\node_modules\\src\\math\\Capsule.js"], "sourcesContent": ["import { Vector3 } from 'three'\n\nconst _v1 = /* @__PURE__ */ new Vector3()\nconst _v2 = /* @__PURE__ */ new Vector3()\nconst _v3 = /* @__PURE__ */ new Vector3()\n\nconst EPS = 1e-10\n\nclass Capsule {\n  constructor(start = new Vector3(0, 0, 0), end = new Vector3(0, 1, 0), radius = 1) {\n    this.start = start\n    this.end = end\n    this.radius = radius\n  }\n\n  clone() {\n    return new Capsule(this.start.clone(), this.end.clone(), this.radius)\n  }\n\n  set(start, end, radius) {\n    this.start.copy(start)\n    this.end.copy(end)\n    this.radius = radius\n  }\n\n  copy(capsule) {\n    this.start.copy(capsule.start)\n    this.end.copy(capsule.end)\n    this.radius = capsule.radius\n  }\n\n  getCenter(target) {\n    return target.copy(this.end).add(this.start).multiplyScalar(0.5)\n  }\n\n  translate(v) {\n    this.start.add(v)\n    this.end.add(v)\n  }\n\n  checkAABBAxis(p1x, p1y, p2x, p2y, minx, maxx, miny, maxy, radius) {\n    return (\n      (minx - p1x < radius || minx - p2x < radius) &&\n      (p1x - maxx < radius || p2x - maxx < radius) &&\n      (miny - p1y < radius || miny - p2y < radius) &&\n      (p1y - maxy < radius || p2y - maxy < radius)\n    )\n  }\n\n  intersectsBox(box) {\n    return (\n      this.checkAABBAxis(\n        this.start.x,\n        this.start.y,\n        this.end.x,\n        this.end.y,\n        box.min.x,\n        box.max.x,\n        box.min.y,\n        box.max.y,\n        this.radius,\n      ) &&\n      this.checkAABBAxis(\n        this.start.x,\n        this.start.z,\n        this.end.x,\n        this.end.z,\n        box.min.x,\n        box.max.x,\n        box.min.z,\n        box.max.z,\n        this.radius,\n      ) &&\n      this.checkAABBAxis(\n        this.start.y,\n        this.start.z,\n        this.end.y,\n        this.end.z,\n        box.min.y,\n        box.max.y,\n        box.min.z,\n        box.max.z,\n        this.radius,\n      )\n    )\n  }\n\n  lineLineMinimumPoints(line1, line2) {\n    const r = _v1.copy(line1.end).sub(line1.start)\n    const s = _v2.copy(line2.end).sub(line2.start)\n    const w = _v3.copy(line2.start).sub(line1.start)\n\n    const a = r.dot(s),\n      b = r.dot(r),\n      c = s.dot(s),\n      d = s.dot(w),\n      e = r.dot(w)\n\n    let t1, t2\n    const divisor = b * c - a * a\n\n    if (Math.abs(divisor) < EPS) {\n      const d1 = -d / c\n      const d2 = (a - d) / c\n\n      if (Math.abs(d1 - 0.5) < Math.abs(d2 - 0.5)) {\n        t1 = 0\n        t2 = d1\n      } else {\n        t1 = 1\n        t2 = d2\n      }\n    } else {\n      t1 = (d * a + e * c) / divisor\n      t2 = (t1 * a - d) / c\n    }\n\n    t2 = Math.max(0, Math.min(1, t2))\n    t1 = Math.max(0, Math.min(1, t1))\n\n    const point1 = r.multiplyScalar(t1).add(line1.start)\n    const point2 = s.multiplyScalar(t2).add(line2.start)\n\n    return [point1, point2]\n  }\n}\n\nexport { Capsule }\n"], "mappings": ";AAEA,MAAMA,GAAA,GAAsB,mBAAIC,OAAA,CAAS;AACzC,MAAMC,GAAA,GAAsB,mBAAID,OAAA,CAAS;AACzC,MAAME,GAAA,GAAsB,mBAAIF,OAAA,CAAS;AAEzC,MAAMG,GAAA,GAAM;AAEZ,MAAMC,OAAA,CAAQ;EACZC,YAAYC,KAAA,GAAQ,IAAIN,OAAA,CAAQ,GAAG,GAAG,CAAC,GAAGO,GAAA,GAAM,IAAIP,OAAA,CAAQ,GAAG,GAAG,CAAC,GAAGQ,MAAA,GAAS,GAAG;IAChF,KAAKF,KAAA,GAAQA,KAAA;IACb,KAAKC,GAAA,GAAMA,GAAA;IACX,KAAKC,MAAA,GAASA,MAAA;EACf;EAEDC,MAAA,EAAQ;IACN,OAAO,IAAIL,OAAA,CAAQ,KAAKE,KAAA,CAAMG,KAAA,CAAK,GAAI,KAAKF,GAAA,CAAIE,KAAA,IAAS,KAAKD,MAAM;EACrE;EAEDE,IAAIJ,KAAA,EAAOC,GAAA,EAAKC,MAAA,EAAQ;IACtB,KAAKF,KAAA,CAAMK,IAAA,CAAKL,KAAK;IACrB,KAAKC,GAAA,CAAII,IAAA,CAAKJ,GAAG;IACjB,KAAKC,MAAA,GAASA,MAAA;EACf;EAEDG,KAAKC,OAAA,EAAS;IACZ,KAAKN,KAAA,CAAMK,IAAA,CAAKC,OAAA,CAAQN,KAAK;IAC7B,KAAKC,GAAA,CAAII,IAAA,CAAKC,OAAA,CAAQL,GAAG;IACzB,KAAKC,MAAA,GAASI,OAAA,CAAQJ,MAAA;EACvB;EAEDK,UAAUC,MAAA,EAAQ;IAChB,OAAOA,MAAA,CAAOH,IAAA,CAAK,KAAKJ,GAAG,EAAEQ,GAAA,CAAI,KAAKT,KAAK,EAAEU,cAAA,CAAe,GAAG;EAChE;EAEDC,UAAUC,CAAA,EAAG;IACX,KAAKZ,KAAA,CAAMS,GAAA,CAAIG,CAAC;IAChB,KAAKX,GAAA,CAAIQ,GAAA,CAAIG,CAAC;EACf;EAEDC,cAAcC,GAAA,EAAKC,GAAA,EAAKC,GAAA,EAAKC,GAAA,EAAKC,IAAA,EAAMC,IAAA,EAAMC,IAAA,EAAMC,IAAA,EAAMnB,MAAA,EAAQ;IAChE,QACGgB,IAAA,GAAOJ,GAAA,GAAMZ,MAAA,IAAUgB,IAAA,GAAOF,GAAA,GAAMd,MAAA,MACpCY,GAAA,GAAMK,IAAA,GAAOjB,MAAA,IAAUc,GAAA,GAAMG,IAAA,GAAOjB,MAAA,MACpCkB,IAAA,GAAOL,GAAA,GAAMb,MAAA,IAAUkB,IAAA,GAAOH,GAAA,GAAMf,MAAA,MACpCa,GAAA,GAAMM,IAAA,GAAOnB,MAAA,IAAUe,GAAA,GAAMI,IAAA,GAAOnB,MAAA;EAExC;EAEDoB,cAAcC,GAAA,EAAK;IACjB,OACE,KAAKV,aAAA,CACH,KAAKb,KAAA,CAAMwB,CAAA,EACX,KAAKxB,KAAA,CAAMyB,CAAA,EACX,KAAKxB,GAAA,CAAIuB,CAAA,EACT,KAAKvB,GAAA,CAAIwB,CAAA,EACTF,GAAA,CAAIG,GAAA,CAAIF,CAAA,EACRD,GAAA,CAAII,GAAA,CAAIH,CAAA,EACRD,GAAA,CAAIG,GAAA,CAAID,CAAA,EACRF,GAAA,CAAII,GAAA,CAAIF,CAAA,EACR,KAAKvB,MACN,KACD,KAAKW,aAAA,CACH,KAAKb,KAAA,CAAMwB,CAAA,EACX,KAAKxB,KAAA,CAAM4B,CAAA,EACX,KAAK3B,GAAA,CAAIuB,CAAA,EACT,KAAKvB,GAAA,CAAI2B,CAAA,EACTL,GAAA,CAAIG,GAAA,CAAIF,CAAA,EACRD,GAAA,CAAII,GAAA,CAAIH,CAAA,EACRD,GAAA,CAAIG,GAAA,CAAIE,CAAA,EACRL,GAAA,CAAII,GAAA,CAAIC,CAAA,EACR,KAAK1B,MACN,KACD,KAAKW,aAAA,CACH,KAAKb,KAAA,CAAMyB,CAAA,EACX,KAAKzB,KAAA,CAAM4B,CAAA,EACX,KAAK3B,GAAA,CAAIwB,CAAA,EACT,KAAKxB,GAAA,CAAI2B,CAAA,EACTL,GAAA,CAAIG,GAAA,CAAID,CAAA,EACRF,GAAA,CAAII,GAAA,CAAIF,CAAA,EACRF,GAAA,CAAIG,GAAA,CAAIE,CAAA,EACRL,GAAA,CAAII,GAAA,CAAIC,CAAA,EACR,KAAK1B,MACN;EAEJ;EAED2B,sBAAsBC,KAAA,EAAOC,KAAA,EAAO;IAClC,MAAMC,CAAA,GAAIvC,GAAA,CAAIY,IAAA,CAAKyB,KAAA,CAAM7B,GAAG,EAAEgC,GAAA,CAAIH,KAAA,CAAM9B,KAAK;IAC7C,MAAMkC,CAAA,GAAIvC,GAAA,CAAIU,IAAA,CAAK0B,KAAA,CAAM9B,GAAG,EAAEgC,GAAA,CAAIF,KAAA,CAAM/B,KAAK;IAC7C,MAAMmC,CAAA,GAAIvC,GAAA,CAAIS,IAAA,CAAK0B,KAAA,CAAM/B,KAAK,EAAEiC,GAAA,CAAIH,KAAA,CAAM9B,KAAK;IAE/C,MAAMoC,CAAA,GAAIJ,CAAA,CAAEK,GAAA,CAAIH,CAAC;MACfI,CAAA,GAAIN,CAAA,CAAEK,GAAA,CAAIL,CAAC;MACXO,CAAA,GAAIL,CAAA,CAAEG,GAAA,CAAIH,CAAC;MACXM,CAAA,GAAIN,CAAA,CAAEG,GAAA,CAAIF,CAAC;MACXM,CAAA,GAAIT,CAAA,CAAEK,GAAA,CAAIF,CAAC;IAEb,IAAIO,EAAA,EAAIC,EAAA;IACR,MAAMC,OAAA,GAAUN,CAAA,GAAIC,CAAA,GAAIH,CAAA,GAAIA,CAAA;IAE5B,IAAIS,IAAA,CAAKC,GAAA,CAAIF,OAAO,IAAI/C,GAAA,EAAK;MAC3B,MAAMkD,EAAA,GAAK,CAACP,CAAA,GAAID,CAAA;MAChB,MAAMS,EAAA,IAAMZ,CAAA,GAAII,CAAA,IAAKD,CAAA;MAErB,IAAIM,IAAA,CAAKC,GAAA,CAAIC,EAAA,GAAK,GAAG,IAAIF,IAAA,CAAKC,GAAA,CAAIE,EAAA,GAAK,GAAG,GAAG;QAC3CN,EAAA,GAAK;QACLC,EAAA,GAAKI,EAAA;MACb,OAAa;QACLL,EAAA,GAAK;QACLC,EAAA,GAAKK,EAAA;MACN;IACP,OAAW;MACLN,EAAA,IAAMF,CAAA,GAAIJ,CAAA,GAAIK,CAAA,GAAIF,CAAA,IAAKK,OAAA;MACvBD,EAAA,IAAMD,EAAA,GAAKN,CAAA,GAAII,CAAA,IAAKD,CAAA;IACrB;IAEDI,EAAA,GAAKE,IAAA,CAAKlB,GAAA,CAAI,GAAGkB,IAAA,CAAKnB,GAAA,CAAI,GAAGiB,EAAE,CAAC;IAChCD,EAAA,GAAKG,IAAA,CAAKlB,GAAA,CAAI,GAAGkB,IAAA,CAAKnB,GAAA,CAAI,GAAGgB,EAAE,CAAC;IAEhC,MAAMO,MAAA,GAASjB,CAAA,CAAEtB,cAAA,CAAegC,EAAE,EAAEjC,GAAA,CAAIqB,KAAA,CAAM9B,KAAK;IACnD,MAAMkD,MAAA,GAAShB,CAAA,CAAExB,cAAA,CAAeiC,EAAE,EAAElC,GAAA,CAAIsB,KAAA,CAAM/B,KAAK;IAEnD,OAAO,CAACiD,MAAA,EAAQC,MAAM;EACvB;AACH", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}