{"ast": null, "code": "import { BoxGeometry, Vector3 } from \"three\";\nconst tempNormal = /* @__PURE__ */new Vector3();\nfunction getUv(faceDirVector, normal, uvAxis, projectionAxis, radius, sideLength) {\n  const totArcLength = 2 * Math.PI * radius / 4;\n  const centerLength = Math.max(sideLength - 2 * radius, 0);\n  const halfArc = Math.PI / 4;\n  tempNormal.copy(normal);\n  tempNormal[projectionAxis] = 0;\n  tempNormal.normalize();\n  const arcUvRatio = 0.5 * totArcLength / (totArcLength + centerLength);\n  const arcAngleRatio = 1 - tempNormal.angleTo(faceDirVector) / halfArc;\n  if (Math.sign(tempNormal[uvAxis]) === 1) {\n    return arcAngleRatio * arcUvRatio;\n  } else {\n    const lenUv = centerLength / (totArcLength + centerLength);\n    return lenUv + arcUvRatio + arcUvRatio * (1 - arcAngleRatio);\n  }\n}\nclass RoundedBoxGeometry extends BoxGeometry {\n  constructor(width = 1, height = 1, depth = 1, segments = 2, radius = 0.1) {\n    segments = segments * 2 + 1;\n    radius = Math.min(width / 2, height / 2, depth / 2, radius);\n    super(1, 1, 1, segments, segments, segments);\n    if (segments === 1) return;\n    const geometry2 = this.toNonIndexed();\n    this.index = null;\n    this.attributes.position = geometry2.attributes.position;\n    this.attributes.normal = geometry2.attributes.normal;\n    this.attributes.uv = geometry2.attributes.uv;\n    const position = new Vector3();\n    const normal = new Vector3();\n    const box = new Vector3(width, height, depth).divideScalar(2).subScalar(radius);\n    const positions = this.attributes.position.array;\n    const normals = this.attributes.normal.array;\n    const uvs = this.attributes.uv.array;\n    const faceTris = positions.length / 6;\n    const faceDirVector = new Vector3();\n    const halfSegmentSize = 0.5 / segments;\n    for (let i = 0, j = 0; i < positions.length; i += 3, j += 2) {\n      position.fromArray(positions, i);\n      normal.copy(position);\n      normal.x -= Math.sign(normal.x) * halfSegmentSize;\n      normal.y -= Math.sign(normal.y) * halfSegmentSize;\n      normal.z -= Math.sign(normal.z) * halfSegmentSize;\n      normal.normalize();\n      positions[i + 0] = box.x * Math.sign(position.x) + normal.x * radius;\n      positions[i + 1] = box.y * Math.sign(position.y) + normal.y * radius;\n      positions[i + 2] = box.z * Math.sign(position.z) + normal.z * radius;\n      normals[i + 0] = normal.x;\n      normals[i + 1] = normal.y;\n      normals[i + 2] = normal.z;\n      const side = Math.floor(i / faceTris);\n      switch (side) {\n        case 0:\n          faceDirVector.set(1, 0, 0);\n          uvs[j + 0] = getUv(faceDirVector, normal, \"z\", \"y\", radius, depth);\n          uvs[j + 1] = 1 - getUv(faceDirVector, normal, \"y\", \"z\", radius, height);\n          break;\n        case 1:\n          faceDirVector.set(-1, 0, 0);\n          uvs[j + 0] = 1 - getUv(faceDirVector, normal, \"z\", \"y\", radius, depth);\n          uvs[j + 1] = 1 - getUv(faceDirVector, normal, \"y\", \"z\", radius, height);\n          break;\n        case 2:\n          faceDirVector.set(0, 1, 0);\n          uvs[j + 0] = 1 - getUv(faceDirVector, normal, \"x\", \"z\", radius, width);\n          uvs[j + 1] = getUv(faceDirVector, normal, \"z\", \"x\", radius, depth);\n          break;\n        case 3:\n          faceDirVector.set(0, -1, 0);\n          uvs[j + 0] = 1 - getUv(faceDirVector, normal, \"x\", \"z\", radius, width);\n          uvs[j + 1] = 1 - getUv(faceDirVector, normal, \"z\", \"x\", radius, depth);\n          break;\n        case 4:\n          faceDirVector.set(0, 0, 1);\n          uvs[j + 0] = 1 - getUv(faceDirVector, normal, \"x\", \"y\", radius, width);\n          uvs[j + 1] = 1 - getUv(faceDirVector, normal, \"y\", \"x\", radius, height);\n          break;\n        case 5:\n          faceDirVector.set(0, 0, -1);\n          uvs[j + 0] = getUv(faceDirVector, normal, \"x\", \"y\", radius, width);\n          uvs[j + 1] = 1 - getUv(faceDirVector, normal, \"y\", \"x\", radius, height);\n          break;\n      }\n    }\n  }\n}\nexport { RoundedBoxGeometry };", "map": {"version": 3, "names": ["tempNormal", "Vector3", "getUv", "faceDirVector", "normal", "uvAxis", "projectionAxis", "radius", "sideLength", "totArc<PERSON>ength", "Math", "PI", "center<PERSON><PERSON><PERSON>", "max", "halfArc", "copy", "normalize", "arcUvRatio", "arcAngleRatio", "angleTo", "sign", "lenUv", "RoundedBoxGeometry", "BoxGeometry", "constructor", "width", "height", "depth", "segments", "min", "geometry2", "toNonIndexed", "index", "attributes", "position", "uv", "box", "divideScalar", "subScalar", "positions", "array", "normals", "uvs", "faceTris", "length", "halfSegmentSize", "i", "j", "fromArray", "x", "y", "z", "side", "floor", "set"], "sources": ["C:\\Users\\<USER>\\Documents\\Apps_Creations\\SiahMan_Racing4Speed_WebGame\\siah-man-racing\\node_modules\\src\\geometries\\RoundedBoxGeometry.js"], "sourcesContent": ["import { BoxGeometry, Vector3 } from 'three'\n\nconst tempNormal = /* @__PURE__ */ new Vector3()\n\nfunction getUv(faceDirVector, normal, uvAxis, projectionAxis, radius, sideLength) {\n  const totArcLength = (2 * Math.PI * radius) / 4\n\n  // length of the planes between the arcs on each axis\n  const centerLength = Math.max(sideLength - 2 * radius, 0)\n  const halfArc = Math.PI / 4\n\n  // Get the vector projected onto the Y plane\n  tempNormal.copy(normal)\n  tempNormal[projectionAxis] = 0\n  tempNormal.normalize()\n\n  // total amount of UV space alloted to a single arc\n  const arcUvRatio = (0.5 * totArcLength) / (totArcLength + centerLength)\n\n  // the distance along one arc the point is at\n  const arcAngleRatio = 1.0 - tempNormal.angleTo(faceDirVector) / halfArc\n\n  if (Math.sign(tempNormal[uvAxis]) === 1) {\n    return arcAngleRatio * arcUvRatio\n  } else {\n    // total amount of UV space alloted to the plane between the arcs\n    const lenUv = centerLength / (totArcLength + centerLength)\n    return lenUv + arcUvRatio + arcUvRatio * (1.0 - arcAngleRatio)\n  }\n}\n\nclass RoundedBoxGeometry extends BoxGeometry {\n  constructor(width = 1, height = 1, depth = 1, segments = 2, radius = 0.1) {\n    // ensure segments is odd so we have a plane connecting the rounded corners\n    segments = segments * 2 + 1\n\n    // ensure radius isn't bigger than shortest side\n    radius = Math.min(width / 2, height / 2, depth / 2, radius)\n\n    super(1, 1, 1, segments, segments, segments)\n\n    // if we just have one segment we're the same as a regular box\n    if (segments === 1) return\n\n    const geometry2 = this.toNonIndexed()\n\n    this.index = null\n    this.attributes.position = geometry2.attributes.position\n    this.attributes.normal = geometry2.attributes.normal\n    this.attributes.uv = geometry2.attributes.uv\n\n    //\n\n    const position = new Vector3()\n    const normal = new Vector3()\n\n    const box = new Vector3(width, height, depth).divideScalar(2).subScalar(radius)\n\n    const positions = this.attributes.position.array\n    const normals = this.attributes.normal.array\n    const uvs = this.attributes.uv.array\n\n    const faceTris = positions.length / 6\n    const faceDirVector = new Vector3()\n    const halfSegmentSize = 0.5 / segments\n\n    for (let i = 0, j = 0; i < positions.length; i += 3, j += 2) {\n      position.fromArray(positions, i)\n      normal.copy(position)\n      normal.x -= Math.sign(normal.x) * halfSegmentSize\n      normal.y -= Math.sign(normal.y) * halfSegmentSize\n      normal.z -= Math.sign(normal.z) * halfSegmentSize\n      normal.normalize()\n\n      positions[i + 0] = box.x * Math.sign(position.x) + normal.x * radius\n      positions[i + 1] = box.y * Math.sign(position.y) + normal.y * radius\n      positions[i + 2] = box.z * Math.sign(position.z) + normal.z * radius\n\n      normals[i + 0] = normal.x\n      normals[i + 1] = normal.y\n      normals[i + 2] = normal.z\n\n      const side = Math.floor(i / faceTris)\n\n      switch (side) {\n        case 0: // right\n          // generate UVs along Z then Y\n          faceDirVector.set(1, 0, 0)\n          uvs[j + 0] = getUv(faceDirVector, normal, 'z', 'y', radius, depth)\n          uvs[j + 1] = 1.0 - getUv(faceDirVector, normal, 'y', 'z', radius, height)\n          break\n\n        case 1: // left\n          // generate UVs along Z then Y\n          faceDirVector.set(-1, 0, 0)\n          uvs[j + 0] = 1.0 - getUv(faceDirVector, normal, 'z', 'y', radius, depth)\n          uvs[j + 1] = 1.0 - getUv(faceDirVector, normal, 'y', 'z', radius, height)\n          break\n\n        case 2: // top\n          // generate UVs along X then Z\n          faceDirVector.set(0, 1, 0)\n          uvs[j + 0] = 1.0 - getUv(faceDirVector, normal, 'x', 'z', radius, width)\n          uvs[j + 1] = getUv(faceDirVector, normal, 'z', 'x', radius, depth)\n          break\n\n        case 3: // bottom\n          // generate UVs along X then Z\n          faceDirVector.set(0, -1, 0)\n          uvs[j + 0] = 1.0 - getUv(faceDirVector, normal, 'x', 'z', radius, width)\n          uvs[j + 1] = 1.0 - getUv(faceDirVector, normal, 'z', 'x', radius, depth)\n          break\n\n        case 4: // front\n          // generate UVs along X then Y\n          faceDirVector.set(0, 0, 1)\n          uvs[j + 0] = 1.0 - getUv(faceDirVector, normal, 'x', 'y', radius, width)\n          uvs[j + 1] = 1.0 - getUv(faceDirVector, normal, 'y', 'x', radius, height)\n          break\n\n        case 5: // back\n          // generate UVs along X then Y\n          faceDirVector.set(0, 0, -1)\n          uvs[j + 0] = getUv(faceDirVector, normal, 'x', 'y', radius, width)\n          uvs[j + 1] = 1.0 - getUv(faceDirVector, normal, 'y', 'x', radius, height)\n          break\n      }\n    }\n  }\n}\n\nexport { RoundedBoxGeometry }\n"], "mappings": ";AAEA,MAAMA,UAAA,GAA6B,mBAAIC,OAAA,CAAS;AAEhD,SAASC,MAAMC,aAAA,EAAeC,MAAA,EAAQC,MAAA,EAAQC,cAAA,EAAgBC,MAAA,EAAQC,UAAA,EAAY;EAChF,MAAMC,YAAA,GAAgB,IAAIC,IAAA,CAAKC,EAAA,GAAKJ,MAAA,GAAU;EAG9C,MAAMK,YAAA,GAAeF,IAAA,CAAKG,GAAA,CAAIL,UAAA,GAAa,IAAID,MAAA,EAAQ,CAAC;EACxD,MAAMO,OAAA,GAAUJ,IAAA,CAAKC,EAAA,GAAK;EAG1BX,UAAA,CAAWe,IAAA,CAAKX,MAAM;EACtBJ,UAAA,CAAWM,cAAc,IAAI;EAC7BN,UAAA,CAAWgB,SAAA,CAAW;EAGtB,MAAMC,UAAA,GAAc,MAAMR,YAAA,IAAiBA,YAAA,GAAeG,YAAA;EAG1D,MAAMM,aAAA,GAAgB,IAAMlB,UAAA,CAAWmB,OAAA,CAAQhB,aAAa,IAAIW,OAAA;EAEhE,IAAIJ,IAAA,CAAKU,IAAA,CAAKpB,UAAA,CAAWK,MAAM,CAAC,MAAM,GAAG;IACvC,OAAOa,aAAA,GAAgBD,UAAA;EAC3B,OAAS;IAEL,MAAMI,KAAA,GAAQT,YAAA,IAAgBH,YAAA,GAAeG,YAAA;IAC7C,OAAOS,KAAA,GAAQJ,UAAA,GAAaA,UAAA,IAAc,IAAMC,aAAA;EACjD;AACH;AAEA,MAAMI,kBAAA,SAA2BC,WAAA,CAAY;EAC3CC,YAAYC,KAAA,GAAQ,GAAGC,MAAA,GAAS,GAAGC,KAAA,GAAQ,GAAGC,QAAA,GAAW,GAAGrB,MAAA,GAAS,KAAK;IAExEqB,QAAA,GAAWA,QAAA,GAAW,IAAI;IAG1BrB,MAAA,GAASG,IAAA,CAAKmB,GAAA,CAAIJ,KAAA,GAAQ,GAAGC,MAAA,GAAS,GAAGC,KAAA,GAAQ,GAAGpB,MAAM;IAE1D,MAAM,GAAG,GAAG,GAAGqB,QAAA,EAAUA,QAAA,EAAUA,QAAQ;IAG3C,IAAIA,QAAA,KAAa,GAAG;IAEpB,MAAME,SAAA,GAAY,KAAKC,YAAA,CAAc;IAErC,KAAKC,KAAA,GAAQ;IACb,KAAKC,UAAA,CAAWC,QAAA,GAAWJ,SAAA,CAAUG,UAAA,CAAWC,QAAA;IAChD,KAAKD,UAAA,CAAW7B,MAAA,GAAS0B,SAAA,CAAUG,UAAA,CAAW7B,MAAA;IAC9C,KAAK6B,UAAA,CAAWE,EAAA,GAAKL,SAAA,CAAUG,UAAA,CAAWE,EAAA;IAI1C,MAAMD,QAAA,GAAW,IAAIjC,OAAA,CAAS;IAC9B,MAAMG,MAAA,GAAS,IAAIH,OAAA,CAAS;IAE5B,MAAMmC,GAAA,GAAM,IAAInC,OAAA,CAAQwB,KAAA,EAAOC,MAAA,EAAQC,KAAK,EAAEU,YAAA,CAAa,CAAC,EAAEC,SAAA,CAAU/B,MAAM;IAE9E,MAAMgC,SAAA,GAAY,KAAKN,UAAA,CAAWC,QAAA,CAASM,KAAA;IAC3C,MAAMC,OAAA,GAAU,KAAKR,UAAA,CAAW7B,MAAA,CAAOoC,KAAA;IACvC,MAAME,GAAA,GAAM,KAAKT,UAAA,CAAWE,EAAA,CAAGK,KAAA;IAE/B,MAAMG,QAAA,GAAWJ,SAAA,CAAUK,MAAA,GAAS;IACpC,MAAMzC,aAAA,GAAgB,IAAIF,OAAA,CAAS;IACnC,MAAM4C,eAAA,GAAkB,MAAMjB,QAAA;IAE9B,SAASkB,CAAA,GAAI,GAAGC,CAAA,GAAI,GAAGD,CAAA,GAAIP,SAAA,CAAUK,MAAA,EAAQE,CAAA,IAAK,GAAGC,CAAA,IAAK,GAAG;MAC3Db,QAAA,CAASc,SAAA,CAAUT,SAAA,EAAWO,CAAC;MAC/B1C,MAAA,CAAOW,IAAA,CAAKmB,QAAQ;MACpB9B,MAAA,CAAO6C,CAAA,IAAKvC,IAAA,CAAKU,IAAA,CAAKhB,MAAA,CAAO6C,CAAC,IAAIJ,eAAA;MAClCzC,MAAA,CAAO8C,CAAA,IAAKxC,IAAA,CAAKU,IAAA,CAAKhB,MAAA,CAAO8C,CAAC,IAAIL,eAAA;MAClCzC,MAAA,CAAO+C,CAAA,IAAKzC,IAAA,CAAKU,IAAA,CAAKhB,MAAA,CAAO+C,CAAC,IAAIN,eAAA;MAClCzC,MAAA,CAAOY,SAAA,CAAW;MAElBuB,SAAA,CAAUO,CAAA,GAAI,CAAC,IAAIV,GAAA,CAAIa,CAAA,GAAIvC,IAAA,CAAKU,IAAA,CAAKc,QAAA,CAASe,CAAC,IAAI7C,MAAA,CAAO6C,CAAA,GAAI1C,MAAA;MAC9DgC,SAAA,CAAUO,CAAA,GAAI,CAAC,IAAIV,GAAA,CAAIc,CAAA,GAAIxC,IAAA,CAAKU,IAAA,CAAKc,QAAA,CAASgB,CAAC,IAAI9C,MAAA,CAAO8C,CAAA,GAAI3C,MAAA;MAC9DgC,SAAA,CAAUO,CAAA,GAAI,CAAC,IAAIV,GAAA,CAAIe,CAAA,GAAIzC,IAAA,CAAKU,IAAA,CAAKc,QAAA,CAASiB,CAAC,IAAI/C,MAAA,CAAO+C,CAAA,GAAI5C,MAAA;MAE9DkC,OAAA,CAAQK,CAAA,GAAI,CAAC,IAAI1C,MAAA,CAAO6C,CAAA;MACxBR,OAAA,CAAQK,CAAA,GAAI,CAAC,IAAI1C,MAAA,CAAO8C,CAAA;MACxBT,OAAA,CAAQK,CAAA,GAAI,CAAC,IAAI1C,MAAA,CAAO+C,CAAA;MAExB,MAAMC,IAAA,GAAO1C,IAAA,CAAK2C,KAAA,CAAMP,CAAA,GAAIH,QAAQ;MAEpC,QAAQS,IAAA;QACN,KAAK;UAEHjD,aAAA,CAAcmD,GAAA,CAAI,GAAG,GAAG,CAAC;UACzBZ,GAAA,CAAIK,CAAA,GAAI,CAAC,IAAI7C,KAAA,CAAMC,aAAA,EAAeC,MAAA,EAAQ,KAAK,KAAKG,MAAA,EAAQoB,KAAK;UACjEe,GAAA,CAAIK,CAAA,GAAI,CAAC,IAAI,IAAM7C,KAAA,CAAMC,aAAA,EAAeC,MAAA,EAAQ,KAAK,KAAKG,MAAA,EAAQmB,MAAM;UACxE;QAEF,KAAK;UAEHvB,aAAA,CAAcmD,GAAA,CAAI,IAAI,GAAG,CAAC;UAC1BZ,GAAA,CAAIK,CAAA,GAAI,CAAC,IAAI,IAAM7C,KAAA,CAAMC,aAAA,EAAeC,MAAA,EAAQ,KAAK,KAAKG,MAAA,EAAQoB,KAAK;UACvEe,GAAA,CAAIK,CAAA,GAAI,CAAC,IAAI,IAAM7C,KAAA,CAAMC,aAAA,EAAeC,MAAA,EAAQ,KAAK,KAAKG,MAAA,EAAQmB,MAAM;UACxE;QAEF,KAAK;UAEHvB,aAAA,CAAcmD,GAAA,CAAI,GAAG,GAAG,CAAC;UACzBZ,GAAA,CAAIK,CAAA,GAAI,CAAC,IAAI,IAAM7C,KAAA,CAAMC,aAAA,EAAeC,MAAA,EAAQ,KAAK,KAAKG,MAAA,EAAQkB,KAAK;UACvEiB,GAAA,CAAIK,CAAA,GAAI,CAAC,IAAI7C,KAAA,CAAMC,aAAA,EAAeC,MAAA,EAAQ,KAAK,KAAKG,MAAA,EAAQoB,KAAK;UACjE;QAEF,KAAK;UAEHxB,aAAA,CAAcmD,GAAA,CAAI,GAAG,IAAI,CAAC;UAC1BZ,GAAA,CAAIK,CAAA,GAAI,CAAC,IAAI,IAAM7C,KAAA,CAAMC,aAAA,EAAeC,MAAA,EAAQ,KAAK,KAAKG,MAAA,EAAQkB,KAAK;UACvEiB,GAAA,CAAIK,CAAA,GAAI,CAAC,IAAI,IAAM7C,KAAA,CAAMC,aAAA,EAAeC,MAAA,EAAQ,KAAK,KAAKG,MAAA,EAAQoB,KAAK;UACvE;QAEF,KAAK;UAEHxB,aAAA,CAAcmD,GAAA,CAAI,GAAG,GAAG,CAAC;UACzBZ,GAAA,CAAIK,CAAA,GAAI,CAAC,IAAI,IAAM7C,KAAA,CAAMC,aAAA,EAAeC,MAAA,EAAQ,KAAK,KAAKG,MAAA,EAAQkB,KAAK;UACvEiB,GAAA,CAAIK,CAAA,GAAI,CAAC,IAAI,IAAM7C,KAAA,CAAMC,aAAA,EAAeC,MAAA,EAAQ,KAAK,KAAKG,MAAA,EAAQmB,MAAM;UACxE;QAEF,KAAK;UAEHvB,aAAA,CAAcmD,GAAA,CAAI,GAAG,GAAG,EAAE;UAC1BZ,GAAA,CAAIK,CAAA,GAAI,CAAC,IAAI7C,KAAA,CAAMC,aAAA,EAAeC,MAAA,EAAQ,KAAK,KAAKG,MAAA,EAAQkB,KAAK;UACjEiB,GAAA,CAAIK,CAAA,GAAI,CAAC,IAAI,IAAM7C,KAAA,CAAMC,aAAA,EAAeC,MAAA,EAAQ,KAAK,KAAKG,MAAA,EAAQmB,MAAM;UACxE;MACH;IACF;EACF;AACH", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}