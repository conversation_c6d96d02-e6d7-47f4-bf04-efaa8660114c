{"ast": null, "code": "import * as React from 'react';\nimport { use<PERSON>rame } from '@react-three/fiber';\nimport { Color, AdditiveBlending, ShaderMaterial, Vector3, Spherical } from 'three';\nimport { version } from '../helpers/constants.js';\nclass StarfieldMaterial extends ShaderMaterial {\n  constructor() {\n    super({\n      uniforms: {\n        time: {\n          value: 0.0\n        },\n        fade: {\n          value: 1.0\n        }\n      },\n      vertexShader: /* glsl */`\n      uniform float time;\n      attribute float size;\n      varying vec3 vColor;\n      void main() {\n        vColor = color;\n        vec4 mvPosition = modelViewMatrix * vec4(position, 0.5);\n        gl_PointSize = size * (30.0 / -mvPosition.z) * (3.0 + sin(time + 100.0));\n        gl_Position = projectionMatrix * mvPosition;\n      }`,\n      fragmentShader: /* glsl */`\n      uniform sampler2D pointTexture;\n      uniform float fade;\n      varying vec3 vColor;\n      void main() {\n        float opacity = 1.0;\n        if (fade == 1.0) {\n          float d = distance(gl_PointCoord, vec2(0.5, 0.5));\n          opacity = 1.0 / (1.0 + exp(16.0 * (d - 0.25)));\n        }\n        gl_FragColor = vec4(vColor, opacity);\n\n        #include <tonemapping_fragment>\n\t      #include <${version >= 154 ? 'colorspace_fragment' : 'encodings_fragment'}>\n      }`\n    });\n  }\n}\nconst genStar = r => {\n  return new Vector3().setFromSpherical(new Spherical(r, Math.acos(1 - Math.random() * 2), Math.random() * 2 * Math.PI));\n};\nconst Stars = /* @__PURE__ */React.forwardRef(({\n  radius = 100,\n  depth = 50,\n  count = 5000,\n  saturation = 0,\n  factor = 4,\n  fade = false,\n  speed = 1\n}, ref) => {\n  const material = React.useRef(null);\n  const [position, color, size] = React.useMemo(() => {\n    const positions = [];\n    const colors = [];\n    const sizes = Array.from({\n      length: count\n    }, () => (0.5 + 0.5 * Math.random()) * factor);\n    const color = new Color();\n    let r = radius + depth;\n    const increment = depth / count;\n    for (let i = 0; i < count; i++) {\n      r -= increment * Math.random();\n      positions.push(...genStar(r).toArray());\n      color.setHSL(i / count, saturation, 0.9);\n      colors.push(color.r, color.g, color.b);\n    }\n    return [new Float32Array(positions), new Float32Array(colors), new Float32Array(sizes)];\n  }, [count, depth, factor, radius, saturation]);\n  useFrame(state => material.current && (material.current.uniforms.time.value = state.clock.elapsedTime * speed));\n  const [starfieldMaterial] = React.useState(() => new StarfieldMaterial());\n  return /*#__PURE__*/React.createElement(\"points\", {\n    ref: ref\n  }, /*#__PURE__*/React.createElement(\"bufferGeometry\", null, /*#__PURE__*/React.createElement(\"bufferAttribute\", {\n    attach: \"attributes-position\",\n    args: [position, 3]\n  }), /*#__PURE__*/React.createElement(\"bufferAttribute\", {\n    attach: \"attributes-color\",\n    args: [color, 3]\n  }), /*#__PURE__*/React.createElement(\"bufferAttribute\", {\n    attach: \"attributes-size\",\n    args: [size, 1]\n  })), /*#__PURE__*/React.createElement(\"primitive\", {\n    ref: material,\n    object: starfieldMaterial,\n    attach: \"material\",\n    blending: AdditiveBlending,\n    \"uniforms-fade-value\": fade,\n    depthWrite: false,\n    transparent: true,\n    vertexColors: true\n  }));\n});\nexport { Stars };", "map": {"version": 3, "names": ["React", "useFrame", "Color", "AdditiveBlending", "ShaderMaterial", "Vector3", "Spherical", "version", "StarfieldMaterial", "constructor", "uniforms", "time", "value", "fade", "vertexShader", "fragmentShader", "genStar", "r", "setFromSpherical", "Math", "acos", "random", "PI", "Stars", "forwardRef", "radius", "depth", "count", "saturation", "factor", "speed", "ref", "material", "useRef", "position", "color", "size", "useMemo", "positions", "colors", "sizes", "Array", "from", "length", "increment", "i", "push", "toArray", "setHSL", "g", "b", "Float32Array", "state", "current", "clock", "elapsedTime", "starfieldMaterial", "useState", "createElement", "attach", "args", "object", "blending", "depthWrite", "transparent", "vertexColors"], "sources": ["C:/Users/<USER>/Documents/Apps_Creations/SiahMan_Racing4Speed_WebGame/siah-man-racing/node_modules/@react-three/drei/core/Stars.js"], "sourcesContent": ["import * as React from 'react';\nimport { use<PERSON>rame } from '@react-three/fiber';\nimport { Color, AdditiveBlending, ShaderMaterial, Vector3, Spherical } from 'three';\nimport { version } from '../helpers/constants.js';\n\nclass StarfieldMaterial extends ShaderMaterial {\n  constructor() {\n    super({\n      uniforms: {\n        time: {\n          value: 0.0\n        },\n        fade: {\n          value: 1.0\n        }\n      },\n      vertexShader: /* glsl */`\n      uniform float time;\n      attribute float size;\n      varying vec3 vColor;\n      void main() {\n        vColor = color;\n        vec4 mvPosition = modelViewMatrix * vec4(position, 0.5);\n        gl_PointSize = size * (30.0 / -mvPosition.z) * (3.0 + sin(time + 100.0));\n        gl_Position = projectionMatrix * mvPosition;\n      }`,\n      fragmentShader: /* glsl */`\n      uniform sampler2D pointTexture;\n      uniform float fade;\n      varying vec3 vColor;\n      void main() {\n        float opacity = 1.0;\n        if (fade == 1.0) {\n          float d = distance(gl_PointCoord, vec2(0.5, 0.5));\n          opacity = 1.0 / (1.0 + exp(16.0 * (d - 0.25)));\n        }\n        gl_FragColor = vec4(vColor, opacity);\n\n        #include <tonemapping_fragment>\n\t      #include <${version >= 154 ? 'colorspace_fragment' : 'encodings_fragment'}>\n      }`\n    });\n  }\n}\nconst genStar = r => {\n  return new Vector3().setFromSpherical(new Spherical(r, Math.acos(1 - Math.random() * 2), Math.random() * 2 * Math.PI));\n};\nconst Stars = /* @__PURE__ */React.forwardRef(({\n  radius = 100,\n  depth = 50,\n  count = 5000,\n  saturation = 0,\n  factor = 4,\n  fade = false,\n  speed = 1\n}, ref) => {\n  const material = React.useRef(null);\n  const [position, color, size] = React.useMemo(() => {\n    const positions = [];\n    const colors = [];\n    const sizes = Array.from({\n      length: count\n    }, () => (0.5 + 0.5 * Math.random()) * factor);\n    const color = new Color();\n    let r = radius + depth;\n    const increment = depth / count;\n    for (let i = 0; i < count; i++) {\n      r -= increment * Math.random();\n      positions.push(...genStar(r).toArray());\n      color.setHSL(i / count, saturation, 0.9);\n      colors.push(color.r, color.g, color.b);\n    }\n    return [new Float32Array(positions), new Float32Array(colors), new Float32Array(sizes)];\n  }, [count, depth, factor, radius, saturation]);\n  useFrame(state => material.current && (material.current.uniforms.time.value = state.clock.elapsedTime * speed));\n  const [starfieldMaterial] = React.useState(() => new StarfieldMaterial());\n  return /*#__PURE__*/React.createElement(\"points\", {\n    ref: ref\n  }, /*#__PURE__*/React.createElement(\"bufferGeometry\", null, /*#__PURE__*/React.createElement(\"bufferAttribute\", {\n    attach: \"attributes-position\",\n    args: [position, 3]\n  }), /*#__PURE__*/React.createElement(\"bufferAttribute\", {\n    attach: \"attributes-color\",\n    args: [color, 3]\n  }), /*#__PURE__*/React.createElement(\"bufferAttribute\", {\n    attach: \"attributes-size\",\n    args: [size, 1]\n  })), /*#__PURE__*/React.createElement(\"primitive\", {\n    ref: material,\n    object: starfieldMaterial,\n    attach: \"material\",\n    blending: AdditiveBlending,\n    \"uniforms-fade-value\": fade,\n    depthWrite: false,\n    transparent: true,\n    vertexColors: true\n  }));\n});\n\nexport { Stars };\n"], "mappings": "AAAA,OAAO,KAAKA,KAAK,MAAM,OAAO;AAC9B,SAASC,QAAQ,QAAQ,oBAAoB;AAC7C,SAASC,KAAK,EAAEC,gBAAgB,EAAEC,cAAc,EAAEC,OAAO,EAAEC,SAAS,QAAQ,OAAO;AACnF,SAASC,OAAO,QAAQ,yBAAyB;AAEjD,MAAMC,iBAAiB,SAASJ,cAAc,CAAC;EAC7CK,WAAWA,CAAA,EAAG;IACZ,KAAK,CAAC;MACJC,QAAQ,EAAE;QACRC,IAAI,EAAE;UACJC,KAAK,EAAE;QACT,CAAC;QACDC,IAAI,EAAE;UACJD,KAAK,EAAE;QACT;MACF,CAAC;MACDE,YAAY,EAAE,UAAU;AAC9B;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,QAAQ;MACFC,cAAc,EAAE,UAAU;AAChC;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,mBAAmBR,OAAO,IAAI,GAAG,GAAG,qBAAqB,GAAG,oBAAoB;AAChF;IACI,CAAC,CAAC;EACJ;AACF;AACA,MAAMS,OAAO,GAAGC,CAAC,IAAI;EACnB,OAAO,IAAIZ,OAAO,CAAC,CAAC,CAACa,gBAAgB,CAAC,IAAIZ,SAAS,CAACW,CAAC,EAAEE,IAAI,CAACC,IAAI,CAAC,CAAC,GAAGD,IAAI,CAACE,MAAM,CAAC,CAAC,GAAG,CAAC,CAAC,EAAEF,IAAI,CAACE,MAAM,CAAC,CAAC,GAAG,CAAC,GAAGF,IAAI,CAACG,EAAE,CAAC,CAAC;AACxH,CAAC;AACD,MAAMC,KAAK,GAAG,eAAevB,KAAK,CAACwB,UAAU,CAAC,CAAC;EAC7CC,MAAM,GAAG,GAAG;EACZC,KAAK,GAAG,EAAE;EACVC,KAAK,GAAG,IAAI;EACZC,UAAU,GAAG,CAAC;EACdC,MAAM,GAAG,CAAC;EACVhB,IAAI,GAAG,KAAK;EACZiB,KAAK,GAAG;AACV,CAAC,EAAEC,GAAG,KAAK;EACT,MAAMC,QAAQ,GAAGhC,KAAK,CAACiC,MAAM,CAAC,IAAI,CAAC;EACnC,MAAM,CAACC,QAAQ,EAAEC,KAAK,EAAEC,IAAI,CAAC,GAAGpC,KAAK,CAACqC,OAAO,CAAC,MAAM;IAClD,MAAMC,SAAS,GAAG,EAAE;IACpB,MAAMC,MAAM,GAAG,EAAE;IACjB,MAAMC,KAAK,GAAGC,KAAK,CAACC,IAAI,CAAC;MACvBC,MAAM,EAAEhB;IACV,CAAC,EAAE,MAAM,CAAC,GAAG,GAAG,GAAG,GAAGR,IAAI,CAACE,MAAM,CAAC,CAAC,IAAIQ,MAAM,CAAC;IAC9C,MAAMM,KAAK,GAAG,IAAIjC,KAAK,CAAC,CAAC;IACzB,IAAIe,CAAC,GAAGQ,MAAM,GAAGC,KAAK;IACtB,MAAMkB,SAAS,GAAGlB,KAAK,GAAGC,KAAK;IAC/B,KAAK,IAAIkB,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGlB,KAAK,EAAEkB,CAAC,EAAE,EAAE;MAC9B5B,CAAC,IAAI2B,SAAS,GAAGzB,IAAI,CAACE,MAAM,CAAC,CAAC;MAC9BiB,SAAS,CAACQ,IAAI,CAAC,GAAG9B,OAAO,CAACC,CAAC,CAAC,CAAC8B,OAAO,CAAC,CAAC,CAAC;MACvCZ,KAAK,CAACa,MAAM,CAACH,CAAC,GAAGlB,KAAK,EAAEC,UAAU,EAAE,GAAG,CAAC;MACxCW,MAAM,CAACO,IAAI,CAACX,KAAK,CAAClB,CAAC,EAAEkB,KAAK,CAACc,CAAC,EAAEd,KAAK,CAACe,CAAC,CAAC;IACxC;IACA,OAAO,CAAC,IAAIC,YAAY,CAACb,SAAS,CAAC,EAAE,IAAIa,YAAY,CAACZ,MAAM,CAAC,EAAE,IAAIY,YAAY,CAACX,KAAK,CAAC,CAAC;EACzF,CAAC,EAAE,CAACb,KAAK,EAAED,KAAK,EAAEG,MAAM,EAAEJ,MAAM,EAAEG,UAAU,CAAC,CAAC;EAC9C3B,QAAQ,CAACmD,KAAK,IAAIpB,QAAQ,CAACqB,OAAO,KAAKrB,QAAQ,CAACqB,OAAO,CAAC3C,QAAQ,CAACC,IAAI,CAACC,KAAK,GAAGwC,KAAK,CAACE,KAAK,CAACC,WAAW,GAAGzB,KAAK,CAAC,CAAC;EAC/G,MAAM,CAAC0B,iBAAiB,CAAC,GAAGxD,KAAK,CAACyD,QAAQ,CAAC,MAAM,IAAIjD,iBAAiB,CAAC,CAAC,CAAC;EACzE,OAAO,aAAaR,KAAK,CAAC0D,aAAa,CAAC,QAAQ,EAAE;IAChD3B,GAAG,EAAEA;EACP,CAAC,EAAE,aAAa/B,KAAK,CAAC0D,aAAa,CAAC,gBAAgB,EAAE,IAAI,EAAE,aAAa1D,KAAK,CAAC0D,aAAa,CAAC,iBAAiB,EAAE;IAC9GC,MAAM,EAAE,qBAAqB;IAC7BC,IAAI,EAAE,CAAC1B,QAAQ,EAAE,CAAC;EACpB,CAAC,CAAC,EAAE,aAAalC,KAAK,CAAC0D,aAAa,CAAC,iBAAiB,EAAE;IACtDC,MAAM,EAAE,kBAAkB;IAC1BC,IAAI,EAAE,CAACzB,KAAK,EAAE,CAAC;EACjB,CAAC,CAAC,EAAE,aAAanC,KAAK,CAAC0D,aAAa,CAAC,iBAAiB,EAAE;IACtDC,MAAM,EAAE,iBAAiB;IACzBC,IAAI,EAAE,CAACxB,IAAI,EAAE,CAAC;EAChB,CAAC,CAAC,CAAC,EAAE,aAAapC,KAAK,CAAC0D,aAAa,CAAC,WAAW,EAAE;IACjD3B,GAAG,EAAEC,QAAQ;IACb6B,MAAM,EAAEL,iBAAiB;IACzBG,MAAM,EAAE,UAAU;IAClBG,QAAQ,EAAE3D,gBAAgB;IAC1B,qBAAqB,EAAEU,IAAI;IAC3BkD,UAAU,EAAE,KAAK;IACjBC,WAAW,EAAE,IAAI;IACjBC,YAAY,EAAE;EAChB,CAAC,CAAC,CAAC;AACL,CAAC,CAAC;AAEF,SAAS1C,KAAK", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}