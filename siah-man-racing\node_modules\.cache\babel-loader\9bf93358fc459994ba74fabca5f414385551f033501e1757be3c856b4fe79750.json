{"ast": null, "code": "var __defProp = Object.defineProperty;\nvar __defNormalProp = (obj, key, value) => key in obj ? __defProp(obj, key, {\n  enumerable: true,\n  configurable: true,\n  writable: true,\n  value\n}) : obj[key] = value;\nvar __publicField = (obj, key, value) => {\n  __defNormalProp(obj, typeof key !== \"symbol\" ? key + \"\" : key, value);\n  return value;\n};\nimport { Vector3, BufferAttribute, BufferGeometry } from \"three\";\nimport * as BufferGeometryUtils from \"../utils/BufferGeometryUtils.js\";\nimport { mergeVertices } from \"../utils/BufferGeometryUtils.js\";\nclass EdgeSplitModifier {\n  constructor() {\n    __publicField(this, \"A\", new Vector3());\n    __publicField(this, \"B\", new Vector3());\n    __publicField(this, \"C\", new Vector3());\n    __publicField(this, \"positions\", []);\n    __publicField(this, \"normals\", new Float32Array());\n    __publicField(this, \"indexes\", []);\n    __publicField(this, \"pointToIndexMap\", []);\n    __publicField(this, \"splitIndexes\", []);\n    __publicField(this, \"oldNormals\", []);\n    __publicField(this, \"computeNormals\", () => {\n      this.normals = new Float32Array(this.indexes.length * 3);\n      for (let i = 0; i < this.indexes.length; i += 3) {\n        let index = this.indexes[i];\n        this.A.set(this.positions[3 * index], this.positions[3 * index + 1], this.positions[3 * index + 2]);\n        index = this.indexes[i + 1];\n        this.B.set(this.positions[3 * index], this.positions[3 * index + 1], this.positions[3 * index + 2]);\n        index = this.indexes[i + 2];\n        this.C.set(this.positions[3 * index], this.positions[3 * index + 1], this.positions[3 * index + 2]);\n        this.C.sub(this.B);\n        this.A.sub(this.B);\n        const normal = this.C.cross(this.A).normalize();\n        for (let j = 0; j < 3; j++) {\n          this.normals[3 * (i + j)] = normal.x;\n          this.normals[3 * (i + j) + 1] = normal.y;\n          this.normals[3 * (i + j) + 2] = normal.z;\n        }\n      }\n    });\n    __publicField(this, \"mapPositionsToIndexes\", () => {\n      this.pointToIndexMap = Array(this.positions.length / 3);\n      for (let i = 0; i < this.indexes.length; i++) {\n        const index = this.indexes[i];\n        if (this.pointToIndexMap[index] == null) {\n          this.pointToIndexMap[index] = [];\n        }\n        this.pointToIndexMap[index].push(i);\n      }\n    });\n    __publicField(this, \"edgeSplitToGroups\", (indexes, cutOff, firstIndex) => {\n      this.A.set(this.normals[3 * firstIndex], this.normals[3 * firstIndex + 1], this.normals[3 * firstIndex + 2]).normalize();\n      const result = {\n        splitGroup: [],\n        currentGroup: [firstIndex]\n      };\n      for (let j of indexes) {\n        if (j !== firstIndex) {\n          this.B.set(this.normals[3 * j], this.normals[3 * j + 1], this.normals[3 * j + 2]).normalize();\n          if (this.B.dot(this.A) < cutOff) {\n            result.splitGroup.push(j);\n          } else {\n            result.currentGroup.push(j);\n          }\n        }\n      }\n      return result;\n    });\n    __publicField(this, \"edgeSplit\", (indexes, cutOff, original = null) => {\n      if (indexes.length === 0) return;\n      const groupResults = [];\n      for (let index of indexes) {\n        groupResults.push(this.edgeSplitToGroups(indexes, cutOff, index));\n      }\n      let result = groupResults[0];\n      for (let groupResult of groupResults) {\n        if (groupResult.currentGroup.length > result.currentGroup.length) {\n          result = groupResult;\n        }\n      }\n      if (original != null) {\n        this.splitIndexes.push({\n          original,\n          indexes: result.currentGroup\n        });\n      }\n      if (result.splitGroup.length) {\n        this.edgeSplit(result.splitGroup, cutOff, original || result.currentGroup[0]);\n      }\n    });\n    __publicField(this, \"modify\", (geometry, cutOffAngle, tryKeepNormals = true) => {\n      let hadNormals = false;\n      if (geometry.attributes.normal) {\n        hadNormals = true;\n        geometry = geometry.clone();\n        if (tryKeepNormals === true && geometry.index !== null) {\n          this.oldNormals = geometry.attributes.normal.array;\n        }\n        geometry.deleteAttribute(\"normal\");\n      }\n      if (geometry.index == null) {\n        if (BufferGeometryUtils === void 0) {\n          throw \"THREE.EdgeSplitModifier relies on BufferGeometryUtils\";\n        }\n        geometry = mergeVertices(geometry);\n      }\n      this.indexes = geometry.index.array;\n      this.positions = geometry.getAttribute(\"position\").array;\n      this.computeNormals();\n      this.mapPositionsToIndexes();\n      this.splitIndexes = [];\n      for (let vertexIndexes of this.pointToIndexMap) {\n        this.edgeSplit(vertexIndexes, Math.cos(cutOffAngle) - 1e-3);\n      }\n      const newAttributes = {};\n      for (let name of Object.keys(geometry.attributes)) {\n        const oldAttribute = geometry.attributes[name];\n        const newArray = new oldAttribute.array.constructor((this.indexes.length + this.splitIndexes.length) * oldAttribute.itemSize);\n        newArray.set(oldAttribute.array);\n        newAttributes[name] = new BufferAttribute(newArray, oldAttribute.itemSize, oldAttribute.normalized);\n      }\n      const newIndexes = new Uint32Array(this.indexes.length);\n      newIndexes.set(this.indexes);\n      for (let i = 0; i < this.splitIndexes.length; i++) {\n        const split = this.splitIndexes[i];\n        const index = this.indexes[split.original];\n        for (let attribute of Object.values(newAttributes)) {\n          for (let j = 0; j < attribute.itemSize; j++) {\n            attribute.array[(this.indexes.length + i) * attribute.itemSize + j] = attribute.array[index * attribute.itemSize + j];\n          }\n        }\n        for (let j of split.indexes) {\n          newIndexes[j] = this.indexes.length + i;\n        }\n      }\n      geometry = new BufferGeometry();\n      geometry.setIndex(new BufferAttribute(newIndexes, 1));\n      for (let name of Object.keys(newAttributes)) {\n        geometry.setAttribute(name, newAttributes[name]);\n      }\n      if (hadNormals) {\n        geometry.computeVertexNormals();\n        if (this.oldNormals !== null) {\n          const changedNormals = new Array(this.oldNormals.length / 3).fill(false);\n          for (let splitData of this.splitIndexes) changedNormals[splitData.original] = true;\n          for (let i = 0; i < changedNormals.length; i++) {\n            if (changedNormals[i] === false) {\n              for (let j = 0; j < 3; j++) {\n                geometry.attributes.normal.array[3 * i + j] = this.oldNormals[3 * i + j];\n              }\n            }\n          }\n        }\n      }\n      return geometry;\n    });\n  }\n}\nexport { EdgeSplitModifier };", "map": {"version": 3, "names": ["EdgeSplitModifier", "constructor", "__publicField", "Vector3", "Float32Array", "normals", "indexes", "length", "i", "index", "A", "set", "positions", "B", "C", "sub", "normal", "cross", "normalize", "j", "x", "y", "z", "pointToIndexMap", "Array", "push", "cutOff", "firstIndex", "result", "splitGroup", "currentGroup", "dot", "original", "groupResults", "edgeSplitToGroups", "groupResult", "splitIndexes", "edgeSplit", "geometry", "cutOffAngle", "tryKeepNormals", "hadNormals", "attributes", "clone", "oldNormals", "array", "deleteAttribute", "BufferGeometryUtils", "mergeVertices", "getAttribute", "computeNormals", "mapPositionsToIndexes", "vertexIndexes", "Math", "cos", "newAttributes", "name", "Object", "keys", "oldAttribute", "newArray", "itemSize", "BufferAttribute", "normalized", "newIndexes", "Uint32Array", "split", "attribute", "values", "BufferGeometry", "setIndex", "setAttribute", "computeVertexNormals", "changedNormals", "fill", "splitData"], "sources": ["C:\\Users\\<USER>\\Documents\\Apps_Creations\\SiahMan_Racing4Speed_WebGame\\siah-man-racing\\node_modules\\src\\modifiers\\EdgeSplitModifier.ts"], "sourcesContent": ["import { BufferAttribute, BufferGeometry, Vector3 } from 'three'\nimport * as BufferGeometryUtils from '../utils/BufferGeometryUtils'\n\ninterface EdgeSplitToGroupsResult {\n  splitGroup: number[]\n  currentGroup: number[]\n}\n\ninterface SplitIndexes {\n  original: number\n  indexes: number[]\n}\n\nclass EdgeSplitModifier {\n  private A = new Vector3()\n  private B = new Vector3()\n  private C = new Vector3()\n\n  private positions: ArrayLike<number> = []\n  private normals: Float32Array = new Float32Array()\n  private indexes: ArrayLike<number> = []\n  private pointToIndexMap: number[][] = []\n  private splitIndexes: SplitIndexes[] = []\n  private oldNormals: ArrayLike<number> = []\n\n  constructor() {}\n\n  private computeNormals = (): void => {\n    this.normals = new Float32Array(this.indexes.length * 3)\n\n    for (let i = 0; i < this.indexes.length; i += 3) {\n      let index = this.indexes[i]\n\n      this.A.set(this.positions[3 * index], this.positions[3 * index + 1], this.positions[3 * index + 2])\n\n      index = this.indexes[i + 1]\n      this.B.set(this.positions[3 * index], this.positions[3 * index + 1], this.positions[3 * index + 2])\n\n      index = this.indexes[i + 2]\n      this.C.set(this.positions[3 * index], this.positions[3 * index + 1], this.positions[3 * index + 2])\n\n      this.C.sub(this.B)\n      this.A.sub(this.B)\n\n      const normal = this.C.cross(this.A).normalize()\n\n      for (let j = 0; j < 3; j++) {\n        this.normals[3 * (i + j)] = normal.x\n        this.normals[3 * (i + j) + 1] = normal.y\n        this.normals[3 * (i + j) + 2] = normal.z\n      }\n    }\n  }\n\n  private mapPositionsToIndexes = (): void => {\n    this.pointToIndexMap = Array(this.positions.length / 3)\n\n    for (let i = 0; i < this.indexes.length; i++) {\n      const index = this.indexes[i]\n\n      if (this.pointToIndexMap[index] == null) {\n        this.pointToIndexMap[index] = []\n      }\n\n      this.pointToIndexMap[index].push(i)\n    }\n  }\n\n  private edgeSplitToGroups = (indexes: number[], cutOff: number, firstIndex: number): EdgeSplitToGroupsResult => {\n    this.A.set(\n      this.normals[3 * firstIndex],\n      this.normals[3 * firstIndex + 1],\n      this.normals[3 * firstIndex + 2],\n    ).normalize()\n\n    const result: EdgeSplitToGroupsResult = {\n      splitGroup: [],\n      currentGroup: [firstIndex],\n    }\n\n    for (let j of indexes) {\n      if (j !== firstIndex) {\n        this.B.set(this.normals[3 * j], this.normals[3 * j + 1], this.normals[3 * j + 2]).normalize()\n\n        if (this.B.dot(this.A) < cutOff) {\n          result.splitGroup.push(j)\n        } else {\n          result.currentGroup.push(j)\n        }\n      }\n    }\n\n    return result\n  }\n\n  private edgeSplit = (indexes: number[], cutOff: number, original: number | null = null): void => {\n    if (indexes.length === 0) return\n\n    const groupResults: EdgeSplitToGroupsResult[] = []\n\n    for (let index of indexes) {\n      groupResults.push(this.edgeSplitToGroups(indexes, cutOff, index))\n    }\n\n    let result = groupResults[0]\n\n    for (let groupResult of groupResults) {\n      if (groupResult.currentGroup.length > result.currentGroup.length) {\n        result = groupResult\n      }\n    }\n\n    if (original != null) {\n      this.splitIndexes.push({\n        original,\n        indexes: result.currentGroup,\n      })\n    }\n\n    if (result.splitGroup.length) {\n      this.edgeSplit(result.splitGroup, cutOff, original || result.currentGroup[0])\n    }\n  }\n\n  public modify = (geometry: BufferGeometry, cutOffAngle: number, tryKeepNormals = true): BufferGeometry => {\n    let hadNormals = false\n\n    if (geometry.attributes.normal) {\n      hadNormals = true\n\n      geometry = geometry.clone()\n\n      if (tryKeepNormals === true && geometry.index !== null) {\n        this.oldNormals = geometry.attributes.normal.array\n      }\n\n      geometry.deleteAttribute('normal')\n    }\n\n    if (geometry.index == null) {\n      if (BufferGeometryUtils === undefined) {\n        throw 'THREE.EdgeSplitModifier relies on BufferGeometryUtils'\n      }\n\n      geometry = BufferGeometryUtils.mergeVertices(geometry)\n    }\n\n    this.indexes = (geometry.index as BufferAttribute).array\n    this.positions = geometry.getAttribute('position').array\n\n    this.computeNormals()\n    this.mapPositionsToIndexes()\n\n    this.splitIndexes = []\n\n    for (let vertexIndexes of this.pointToIndexMap) {\n      this.edgeSplit(vertexIndexes, Math.cos(cutOffAngle) - 0.001)\n    }\n\n    const newAttributes: {\n      [key: string]: BufferAttribute\n    } = {}\n    for (let name of Object.keys(geometry.attributes)) {\n      const oldAttribute = geometry.attributes[name]\n      // @ts-ignore\n      const newArray = new oldAttribute.array.constructor(\n        (this.indexes.length + this.splitIndexes.length) * oldAttribute.itemSize,\n      )\n\n      newArray.set(oldAttribute.array)\n      newAttributes[name] = new BufferAttribute(newArray, oldAttribute.itemSize, oldAttribute.normalized)\n    }\n\n    const newIndexes = new Uint32Array(this.indexes.length)\n    newIndexes.set(this.indexes)\n\n    for (let i = 0; i < this.splitIndexes.length; i++) {\n      const split = this.splitIndexes[i]\n      const index = this.indexes[split.original]\n\n      for (let attribute of Object.values(newAttributes)) {\n        for (let j = 0; j < attribute.itemSize; j++) {\n          // @ts-ignore ArrayLike can't be mutated, but this works – https://github.com/three-types/three-ts-types/issues/35\n          attribute.array[(this.indexes.length + i) * attribute.itemSize + j] =\n            attribute.array[index * attribute.itemSize + j]\n        }\n      }\n\n      for (let j of split.indexes) {\n        newIndexes[j] = this.indexes.length + i\n      }\n    }\n\n    geometry = new BufferGeometry()\n    geometry.setIndex(new BufferAttribute(newIndexes, 1))\n\n    for (let name of Object.keys(newAttributes)) {\n      geometry.setAttribute(name, newAttributes[name])\n    }\n\n    if (hadNormals) {\n      geometry.computeVertexNormals()\n\n      if (this.oldNormals !== null) {\n        const changedNormals = new Array(this.oldNormals.length / 3).fill(false)\n\n        for (let splitData of this.splitIndexes) changedNormals[splitData.original] = true\n\n        for (let i = 0; i < changedNormals.length; i++) {\n          if (changedNormals[i] === false) {\n            for (let j = 0; j < 3; j++) {\n              // @ts-ignore ArrayLike can't be mutated, but this works – https://github.com/three-types/three-ts-types/issues/35\n              geometry.attributes.normal.array[3 * i + j] = this.oldNormals[3 * i + j]\n            }\n          }\n        }\n      }\n    }\n\n    return geometry\n  }\n}\n\nexport { EdgeSplitModifier }\n"], "mappings": ";;;;;;;;;;;;;;AAaA,MAAMA,iBAAA,CAAkB;EAYtBC,YAAA,EAAc;IAXNC,aAAA,YAAI,IAAIC,OAAA;IACRD,aAAA,YAAI,IAAIC,OAAA;IACRD,aAAA,YAAI,IAAIC,OAAA;IAERD,aAAA,oBAA+B;IAC/BA,aAAA,kBAAwB,IAAIE,YAAA;IAC5BF,aAAA,kBAA6B;IAC7BA,aAAA,0BAA8B;IAC9BA,aAAA,uBAA+B;IAC/BA,aAAA,qBAAgC;IAIhCA,aAAA,yBAAiB,MAAY;MACnC,KAAKG,OAAA,GAAU,IAAID,YAAA,CAAa,KAAKE,OAAA,CAAQC,MAAA,GAAS,CAAC;MAEvD,SAASC,CAAA,GAAI,GAAGA,CAAA,GAAI,KAAKF,OAAA,CAAQC,MAAA,EAAQC,CAAA,IAAK,GAAG;QAC3C,IAAAC,KAAA,GAAQ,KAAKH,OAAA,CAAQE,CAAC;QAE1B,KAAKE,CAAA,CAAEC,GAAA,CAAI,KAAKC,SAAA,CAAU,IAAIH,KAAK,GAAG,KAAKG,SAAA,CAAU,IAAIH,KAAA,GAAQ,CAAC,GAAG,KAAKG,SAAA,CAAU,IAAIH,KAAA,GAAQ,CAAC,CAAC;QAE1FA,KAAA,QAAKH,OAAA,CAAQE,CAAA,GAAI,CAAC;QAC1B,KAAKK,CAAA,CAAEF,GAAA,CAAI,KAAKC,SAAA,CAAU,IAAIH,KAAK,GAAG,KAAKG,SAAA,CAAU,IAAIH,KAAA,GAAQ,CAAC,GAAG,KAAKG,SAAA,CAAU,IAAIH,KAAA,GAAQ,CAAC,CAAC;QAE1FA,KAAA,QAAKH,OAAA,CAAQE,CAAA,GAAI,CAAC;QAC1B,KAAKM,CAAA,CAAEH,GAAA,CAAI,KAAKC,SAAA,CAAU,IAAIH,KAAK,GAAG,KAAKG,SAAA,CAAU,IAAIH,KAAA,GAAQ,CAAC,GAAG,KAAKG,SAAA,CAAU,IAAIH,KAAA,GAAQ,CAAC,CAAC;QAE7F,KAAAK,CAAA,CAAEC,GAAA,CAAI,KAAKF,CAAC;QACZ,KAAAH,CAAA,CAAEK,GAAA,CAAI,KAAKF,CAAC;QAEjB,MAAMG,MAAA,GAAS,KAAKF,CAAA,CAAEG,KAAA,CAAM,KAAKP,CAAC,EAAEQ,SAAA;QAEpC,SAASC,CAAA,GAAI,GAAGA,CAAA,GAAI,GAAGA,CAAA,IAAK;UAC1B,KAAKd,OAAA,CAAQ,KAAKG,CAAA,GAAIW,CAAA,CAAE,IAAIH,MAAA,CAAOI,CAAA;UACnC,KAAKf,OAAA,CAAQ,KAAKG,CAAA,GAAIW,CAAA,IAAK,CAAC,IAAIH,MAAA,CAAOK,CAAA;UACvC,KAAKhB,OAAA,CAAQ,KAAKG,CAAA,GAAIW,CAAA,IAAK,CAAC,IAAIH,MAAA,CAAOM,CAAA;QACzC;MACF;IAAA;IAGMpB,aAAA,gCAAwB,MAAY;MAC1C,KAAKqB,eAAA,GAAkBC,KAAA,CAAM,KAAKZ,SAAA,CAAUL,MAAA,GAAS,CAAC;MAEtD,SAASC,CAAA,GAAI,GAAGA,CAAA,GAAI,KAAKF,OAAA,CAAQC,MAAA,EAAQC,CAAA,IAAK;QACtC,MAAAC,KAAA,GAAQ,KAAKH,OAAA,CAAQE,CAAC;QAE5B,IAAI,KAAKe,eAAA,CAAgBd,KAAK,KAAK,MAAM;UAClC,KAAAc,eAAA,CAAgBd,KAAK,IAAI;QAChC;QAEA,KAAKc,eAAA,CAAgBd,KAAK,EAAEgB,IAAA,CAAKjB,CAAC;MACpC;IAAA;IAGMN,aAAA,4BAAoB,CAACI,OAAA,EAAmBoB,MAAA,EAAgBC,UAAA,KAAgD;MAC9G,KAAKjB,CAAA,CAAEC,GAAA,CACL,KAAKN,OAAA,CAAQ,IAAIsB,UAAU,GAC3B,KAAKtB,OAAA,CAAQ,IAAIsB,UAAA,GAAa,CAAC,GAC/B,KAAKtB,OAAA,CAAQ,IAAIsB,UAAA,GAAa,CAAC,GAC/BT,SAAA,CAAU;MAEZ,MAAMU,MAAA,GAAkC;QACtCC,UAAA,EAAY,EAAC;QACbC,YAAA,EAAc,CAACH,UAAU;MAAA;MAG3B,SAASR,CAAA,IAAKb,OAAA,EAAS;QACrB,IAAIa,CAAA,KAAMQ,UAAA,EAAY;UACf,KAAAd,CAAA,CAAEF,GAAA,CAAI,KAAKN,OAAA,CAAQ,IAAIc,CAAC,GAAG,KAAKd,OAAA,CAAQ,IAAIc,CAAA,GAAI,CAAC,GAAG,KAAKd,OAAA,CAAQ,IAAIc,CAAA,GAAI,CAAC,CAAC,EAAED,SAAA;UAElF,IAAI,KAAKL,CAAA,CAAEkB,GAAA,CAAI,KAAKrB,CAAC,IAAIgB,MAAA,EAAQ;YACxBE,MAAA,CAAAC,UAAA,CAAWJ,IAAA,CAAKN,CAAC;UAAA,OACnB;YACES,MAAA,CAAAE,YAAA,CAAaL,IAAA,CAAKN,CAAC;UAC5B;QACF;MACF;MAEO,OAAAS,MAAA;IAAA;IAGD1B,aAAA,oBAAY,CAACI,OAAA,EAAmBoB,MAAA,EAAgBM,QAAA,GAA0B,SAAe;MAC/F,IAAI1B,OAAA,CAAQC,MAAA,KAAW,GAAG;MAE1B,MAAM0B,YAAA,GAA0C;MAEhD,SAASxB,KAAA,IAASH,OAAA,EAAS;QACzB2B,YAAA,CAAaR,IAAA,CAAK,KAAKS,iBAAA,CAAkB5B,OAAA,EAASoB,MAAA,EAAQjB,KAAK,CAAC;MAClE;MAEI,IAAAmB,MAAA,GAASK,YAAA,CAAa,CAAC;MAE3B,SAASE,WAAA,IAAeF,YAAA,EAAc;QACpC,IAAIE,WAAA,CAAYL,YAAA,CAAavB,MAAA,GAASqB,MAAA,CAAOE,YAAA,CAAavB,MAAA,EAAQ;UACvDqB,MAAA,GAAAO,WAAA;QACX;MACF;MAEA,IAAIH,QAAA,IAAY,MAAM;QACpB,KAAKI,YAAA,CAAaX,IAAA,CAAK;UACrBO,QAAA;UACA1B,OAAA,EAASsB,MAAA,CAAOE;QAAA,CACjB;MACH;MAEI,IAAAF,MAAA,CAAOC,UAAA,CAAWtB,MAAA,EAAQ;QACvB,KAAA8B,SAAA,CAAUT,MAAA,CAAOC,UAAA,EAAYH,MAAA,EAAQM,QAAA,IAAYJ,MAAA,CAAOE,YAAA,CAAa,CAAC,CAAC;MAC9E;IAAA;IAGK5B,aAAA,iBAAS,CAACoC,QAAA,EAA0BC,WAAA,EAAqBC,cAAA,GAAiB,SAAyB;MACxG,IAAIC,UAAA,GAAa;MAEb,IAAAH,QAAA,CAASI,UAAA,CAAW1B,MAAA,EAAQ;QACjByB,UAAA;QAEbH,QAAA,GAAWA,QAAA,CAASK,KAAA;QAEpB,IAAIH,cAAA,KAAmB,QAAQF,QAAA,CAAS7B,KAAA,KAAU,MAAM;UACjD,KAAAmC,UAAA,GAAaN,QAAA,CAASI,UAAA,CAAW1B,MAAA,CAAO6B,KAAA;QAC/C;QAEAP,QAAA,CAASQ,eAAA,CAAgB,QAAQ;MACnC;MAEI,IAAAR,QAAA,CAAS7B,KAAA,IAAS,MAAM;QAC1B,IAAIsC,mBAAA,KAAwB,QAAW;UAC/B;QACR;QAEWT,QAAA,GAAAU,aAAA,CAAkCV,QAAQ;MACvD;MAEK,KAAAhC,OAAA,GAAWgC,QAAA,CAAS7B,KAAA,CAA0BoC,KAAA;MACnD,KAAKjC,SAAA,GAAY0B,QAAA,CAASW,YAAA,CAAa,UAAU,EAAEJ,KAAA;MAEnD,KAAKK,cAAA,CAAe;MACpB,KAAKC,qBAAA,CAAsB;MAE3B,KAAKf,YAAA,GAAe;MAEX,SAAAgB,aAAA,IAAiB,KAAK7B,eAAA,EAAiB;QAC9C,KAAKc,SAAA,CAAUe,aAAA,EAAeC,IAAA,CAAKC,GAAA,CAAIf,WAAW,IAAI,IAAK;MAC7D;MAEA,MAAMgB,aAAA,GAEF;MACJ,SAASC,IAAA,IAAQC,MAAA,CAAOC,IAAA,CAAKpB,QAAA,CAASI,UAAU,GAAG;QAC3C,MAAAiB,YAAA,GAAerB,QAAA,CAASI,UAAA,CAAWc,IAAI;QAEvC,MAAAI,QAAA,GAAW,IAAID,YAAA,CAAad,KAAA,CAAM5C,WAAA,EACrC,KAAKK,OAAA,CAAQC,MAAA,GAAS,KAAK6B,YAAA,CAAa7B,MAAA,IAAUoD,YAAA,CAAaE,QAAA;QAGzDD,QAAA,CAAAjD,GAAA,CAAIgD,YAAA,CAAad,KAAK;QACjBU,aAAA,CAAAC,IAAI,IAAI,IAAIM,eAAA,CAAgBF,QAAA,EAAUD,YAAA,CAAaE,QAAA,EAAUF,YAAA,CAAaI,UAAU;MACpG;MAEA,MAAMC,UAAA,GAAa,IAAIC,WAAA,CAAY,KAAK3D,OAAA,CAAQC,MAAM;MAC3CyD,UAAA,CAAArD,GAAA,CAAI,KAAKL,OAAO;MAE3B,SAASE,CAAA,GAAI,GAAGA,CAAA,GAAI,KAAK4B,YAAA,CAAa7B,MAAA,EAAQC,CAAA,IAAK;QAC3C,MAAA0D,KAAA,GAAQ,KAAK9B,YAAA,CAAa5B,CAAC;QACjC,MAAMC,KAAA,GAAQ,KAAKH,OAAA,CAAQ4D,KAAA,CAAMlC,QAAQ;QAEzC,SAASmC,SAAA,IAAaV,MAAA,CAAOW,MAAA,CAAOb,aAAa,GAAG;UAClD,SAASpC,CAAA,GAAI,GAAGA,CAAA,GAAIgD,SAAA,CAAUN,QAAA,EAAU1C,CAAA,IAAK;YAE3CgD,SAAA,CAAUtB,KAAA,EAAO,KAAKvC,OAAA,CAAQC,MAAA,GAASC,CAAA,IAAK2D,SAAA,CAAUN,QAAA,GAAW1C,CAAC,IAChEgD,SAAA,CAAUtB,KAAA,CAAMpC,KAAA,GAAQ0D,SAAA,CAAUN,QAAA,GAAW1C,CAAC;UAClD;QACF;QAES,SAAAA,CAAA,IAAK+C,KAAA,CAAM5D,OAAA,EAAS;UAC3B0D,UAAA,CAAW7C,CAAC,IAAI,KAAKb,OAAA,CAAQC,MAAA,GAASC,CAAA;QACxC;MACF;MAEA8B,QAAA,GAAW,IAAI+B,cAAA;MACf/B,QAAA,CAASgC,QAAA,CAAS,IAAIR,eAAA,CAAgBE,UAAA,EAAY,CAAC,CAAC;MAEpD,SAASR,IAAA,IAAQC,MAAA,CAAOC,IAAA,CAAKH,aAAa,GAAG;QAC3CjB,QAAA,CAASiC,YAAA,CAAaf,IAAA,EAAMD,aAAA,CAAcC,IAAI,CAAC;MACjD;MAEA,IAAIf,UAAA,EAAY;QACdH,QAAA,CAASkC,oBAAA,CAAqB;QAE1B,SAAK5B,UAAA,KAAe,MAAM;UACtB,MAAA6B,cAAA,GAAiB,IAAIjD,KAAA,CAAM,KAAKoB,UAAA,CAAWrC,MAAA,GAAS,CAAC,EAAEmE,IAAA,CAAK,KAAK;UAEvE,SAASC,SAAA,IAAa,KAAKvC,YAAA,EAA6BqC,cAAA,CAAAE,SAAA,CAAU3C,QAAQ,IAAI;UAE9E,SAASxB,CAAA,GAAI,GAAGA,CAAA,GAAIiE,cAAA,CAAelE,MAAA,EAAQC,CAAA,IAAK;YAC1C,IAAAiE,cAAA,CAAejE,CAAC,MAAM,OAAO;cAC/B,SAASW,CAAA,GAAI,GAAGA,CAAA,GAAI,GAAGA,CAAA,IAAK;gBAEjBmB,QAAA,CAAAI,UAAA,CAAW1B,MAAA,CAAO6B,KAAA,CAAM,IAAIrC,CAAA,GAAIW,CAAC,IAAI,KAAKyB,UAAA,CAAW,IAAIpC,CAAA,GAAIW,CAAC;cACzE;YACF;UACF;QACF;MACF;MAEO,OAAAmB,QAAA;IAAA;EAlMM;AAoMjB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}