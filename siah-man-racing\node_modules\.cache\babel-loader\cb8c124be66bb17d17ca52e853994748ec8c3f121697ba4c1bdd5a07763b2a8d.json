{"ast": null, "code": "import { _ as _classCallCheck } from './classCallCheck-9098b006.esm.js';\nimport { _ as _setPrototypeOf, a as _isNativeReflectConstruct } from './isNativeReflectConstruct-5594d075.esm.js';\nimport * as THREE from 'three';\nfunction _inherits(subClass, superClass) {\n  if (typeof superClass !== \"function\" && superClass !== null) {\n    throw new TypeError(\"Super expression must either be null or a function\");\n  }\n  subClass.prototype = Object.create(superClass && superClass.prototype, {\n    constructor: {\n      value: subClass,\n      writable: true,\n      configurable: true\n    }\n  });\n  if (superClass) _setPrototypeOf(subClass, superClass);\n}\nfunction _getPrototypeOf(o) {\n  _getPrototypeOf = Object.setPrototypeOf ? Object.getPrototypeOf : function _getPrototypeOf(o) {\n    return o.__proto__ || Object.getPrototypeOf(o);\n  };\n  return _getPrototypeOf(o);\n}\nfunction _assertThisInitialized(self) {\n  if (self === void 0) {\n    throw new ReferenceError(\"this hasn't been initialised - super() hasn't been called\");\n  }\n  return self;\n}\nfunction _possibleConstructorReturn(self, call) {\n  if (call && (typeof call === \"object\" || typeof call === \"function\")) {\n    return call;\n  } else if (call !== void 0) {\n    throw new TypeError(\"Derived constructors may only return object or undefined\");\n  }\n  return _assertThisInitialized(self);\n}\nfunction _createSuper(Derived) {\n  var hasNativeReflectConstruct = _isNativeReflectConstruct();\n  return function _createSuperInternal() {\n    var Super = _getPrototypeOf(Derived),\n      result;\n    if (hasNativeReflectConstruct) {\n      var NewTarget = _getPrototypeOf(this).constructor;\n      result = Reflect.construct(Super, arguments, NewTarget);\n    } else {\n      result = Super.apply(this, arguments);\n    }\n    return _possibleConstructorReturn(this, result);\n  };\n}\nvar RoundedPlaneGeometry = /*#__PURE__*/function (_THREE$BufferGeometry) {\n  _inherits(RoundedPlaneGeometry, _THREE$BufferGeometry);\n  var _super = _createSuper(RoundedPlaneGeometry);\n  function RoundedPlaneGeometry() {\n    var _this;\n    var width = arguments.length > 0 && arguments[0] !== undefined ? arguments[0] : 2;\n    var height = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : 1;\n    var radius = arguments.length > 2 && arguments[2] !== undefined ? arguments[2] : 0.2;\n    var segments = arguments.length > 3 && arguments[3] !== undefined ? arguments[3] : 16;\n    _classCallCheck(this, RoundedPlaneGeometry);\n    _this = _super.call(this);\n    _this.parameters = {\n      width: width,\n      height: height,\n      radius: radius,\n      segments: segments\n    }; // helper const's\n\n    var wi = width / 2 - radius; // inner width\n\n    var hi = height / 2 - radius; // inner height\n\n    var ul = radius / width; // u left\n\n    var ur = (width - radius) / width; // u right\n\n    var vl = radius / height; // v low\n\n    var vh = (height - radius) / height; // v high\n\n    var positions = [wi, hi, 0, -wi, hi, 0, -wi, -hi, 0, wi, -hi, 0];\n    var uvs = [ur, vh, ul, vh, ul, vl, ur, vl];\n    var n = [3 * (segments + 1) + 3, 3 * (segments + 1) + 4, segments + 4, segments + 5, 2 * (segments + 1) + 4, 2, 1, 2 * (segments + 1) + 3, 3, 4 * (segments + 1) + 3, 4, 0];\n    var indices = [n[0], n[1], n[2], n[0], n[2], n[3], n[4], n[5], n[6], n[4], n[6], n[7], n[8], n[9], n[10], n[8], n[10], n[11]];\n    var phi, cos, sin, xc, yc, uc, vc, idx;\n    for (var i = 0; i < 4; i++) {\n      xc = i < 1 || i > 2 ? wi : -wi;\n      yc = i < 2 ? hi : -hi;\n      uc = i < 1 || i > 2 ? ur : ul;\n      vc = i < 2 ? vh : vl;\n      for (var j = 0; j <= segments; j++) {\n        phi = Math.PI / 2 * (i + j / segments);\n        cos = Math.cos(phi);\n        sin = Math.sin(phi);\n        positions.push(xc + radius * cos, yc + radius * sin, 0);\n        uvs.push(uc + ul * cos, vc + vl * sin);\n        if (j < segments) {\n          idx = (segments + 1) * i + j + 4;\n          indices.push(i, idx, idx + 1);\n        }\n      }\n    }\n    _this.setIndex(new THREE.BufferAttribute(new Uint32Array(indices), 1));\n    _this.setAttribute(\"position\", new THREE.BufferAttribute(new Float32Array(positions), 3));\n    _this.setAttribute(\"uv\", new THREE.BufferAttribute(new Float32Array(uvs), 2));\n    return _this;\n  }\n  return RoundedPlaneGeometry;\n}(THREE.BufferGeometry); // Author: https://stackoverflow.com/users/128511/gman\n// https://stackoverflow.com/questions/34958072/programmatically-generate-simple-uv-mapping-for-models\n\nfunction applyCylindricalUV(bufferGeometry) {\n  var uvs = [];\n  for (var i = 0; i < bufferGeometry.attributes.position.array.length / 3; i++) {\n    var x = bufferGeometry.attributes.position.array[i * 3 + 0];\n    var y = bufferGeometry.attributes.position.array[i * 3 + 1];\n    var z = bufferGeometry.attributes.position.array[i * 3 + 2];\n    uvs.push(Math.atan2(x, z) / Math.PI * 0.5 + 0.5, y / Math.PI * 0.5 + 0.5);\n  }\n  if (bufferGeometry.attributes.uv) delete bufferGeometry.attributes.uv;\n  bufferGeometry.setAttribute(\"uv\", new THREE.Float32BufferAttribute(uvs, 2));\n  bufferGeometry.attributes.uv.needsUpdate = true;\n  return bufferGeometry;\n} // Author: https://stackoverflow.com/users/268905/knee-cola\n// https://stackoverflow.com/questions/20774648/three-js-generate-uv-coordinate\n\nfunction applySphereUV(bufferGeometry) {\n  var uvs = [];\n  var vertices = [];\n  for (var i = 0; i < bufferGeometry.attributes.position.array.length / 3; i++) {\n    var x = bufferGeometry.attributes.position.array[i * 3 + 0];\n    var y = bufferGeometry.attributes.position.array[i * 3 + 1];\n    var z = bufferGeometry.attributes.position.array[i * 3 + 2];\n    vertices.push(new THREE.Vector3(x, y, z));\n  }\n  var polarVertices = vertices.map(cartesian2polar);\n  for (var _i = 0; _i < polarVertices.length / 3; _i++) {\n    var tri = new THREE.Triangle(vertices[_i * 3 + 0], vertices[_i * 3 + 1], vertices[_i * 3 + 2]);\n    var normal = tri.getNormal(new THREE.Vector3());\n    for (var f = 0; f < 3; f++) {\n      var vertex = polarVertices[_i * 3 + f];\n      if (vertex.theta === 0 && (vertex.phi === 0 || vertex.phi === Math.PI)) {\n        var alignedVertice = vertex.phi === 0 ? _i * 3 + 1 : _i * 3 + 0;\n        vertex = {\n          r: vertex.r,\n          phi: vertex.phi,\n          theta: polarVertices[alignedVertice].theta\n        };\n      }\n      if (vertex.theta === Math.PI && cartesian2polar(normal).theta < Math.PI / 2) {\n        vertex.theta = -Math.PI;\n      }\n      var canvasPoint = polar2canvas(vertex);\n      uvs.push(1 - canvasPoint.x, 1 - canvasPoint.y);\n    }\n  }\n  if (bufferGeometry.attributes.uv) delete bufferGeometry.attributes.uv;\n  bufferGeometry.setAttribute(\"uv\", new THREE.Float32BufferAttribute(uvs, 2));\n  bufferGeometry.attributes.uv.needsUpdate = true;\n  return bufferGeometry;\n}\nfunction cartesian2polar(position) {\n  var r = Math.sqrt(position.x * position.x + position.z * position.z + position.y * position.y);\n  return {\n    r: r,\n    phi: Math.acos(position.y / r),\n    theta: Math.atan2(position.z, position.x)\n  };\n}\nfunction polar2canvas(polarPoint) {\n  return {\n    y: polarPoint.phi / Math.PI,\n    x: (polarPoint.theta + Math.PI) / (2 * Math.PI)\n  };\n} // Author: Alex Khoroshylov (https://stackoverflow.com/users/8742287/alex-khoroshylov)\n// https://stackoverflow.com/questions/20774648/three-js-generate-uv-coordinate\n\nfunction applyBoxUV(bufferGeometry) {\n  bufferGeometry.computeBoundingBox();\n  var bboxSize = bufferGeometry.boundingBox.getSize(new THREE.Vector3());\n  var boxSize = Math.min(bboxSize.x, bboxSize.y, bboxSize.z);\n  var boxGeometry = new THREE.BoxGeometry(boxSize, boxSize, boxSize);\n  var cube = new THREE.Mesh(boxGeometry);\n  cube.rotation.set(0, 0, 0);\n  cube.updateWorldMatrix(true, false);\n  var transformMatrix = cube.matrix.clone().invert();\n  var uvBbox = new THREE.Box3(new THREE.Vector3(-boxSize / 2, -boxSize / 2, -boxSize / 2), new THREE.Vector3(boxSize / 2, boxSize / 2, boxSize / 2));\n  _applyBoxUV(bufferGeometry, transformMatrix, uvBbox, boxSize);\n  bufferGeometry.attributes.uv.needsUpdate = true;\n  return bufferGeometry;\n}\nfunction _applyBoxUV(geom, transformMatrix, bbox, bbox_max_size) {\n  var coords = [];\n  coords.length = 2 * geom.attributes.position.array.length / 3; //maps 3 verts of 1 face on the better side of the cube\n  //side of the cube can be XY, XZ or YZ\n\n  var makeUVs = function makeUVs(v0, v1, v2) {\n    //pre-rotate the model so that cube sides match world axis\n    v0.applyMatrix4(transformMatrix);\n    v1.applyMatrix4(transformMatrix);\n    v2.applyMatrix4(transformMatrix); //get normal of the face, to know into which cube side it maps better\n\n    var n = new THREE.Vector3();\n    n.crossVectors(v1.clone().sub(v0), v1.clone().sub(v2)).normalize();\n    n.x = Math.abs(n.x);\n    n.y = Math.abs(n.y);\n    n.z = Math.abs(n.z);\n    var uv0 = new THREE.Vector2();\n    var uv1 = new THREE.Vector2();\n    var uv2 = new THREE.Vector2(); // xz mapping\n\n    if (n.y > n.x && n.y > n.z) {\n      uv0.x = (v0.x - bbox.min.x) / bbox_max_size;\n      uv0.y = (bbox.max.z - v0.z) / bbox_max_size;\n      uv1.x = (v1.x - bbox.min.x) / bbox_max_size;\n      uv1.y = (bbox.max.z - v1.z) / bbox_max_size;\n      uv2.x = (v2.x - bbox.min.x) / bbox_max_size;\n      uv2.y = (bbox.max.z - v2.z) / bbox_max_size;\n    } else if (n.x > n.y && n.x > n.z) {\n      uv0.x = (v0.z - bbox.min.z) / bbox_max_size;\n      uv0.y = (v0.y - bbox.min.y) / bbox_max_size;\n      uv1.x = (v1.z - bbox.min.z) / bbox_max_size;\n      uv1.y = (v1.y - bbox.min.y) / bbox_max_size;\n      uv2.x = (v2.z - bbox.min.z) / bbox_max_size;\n      uv2.y = (v2.y - bbox.min.y) / bbox_max_size;\n    } else if (n.z > n.y && n.z > n.x) {\n      uv0.x = (v0.x - bbox.min.x) / bbox_max_size;\n      uv0.y = (v0.y - bbox.min.y) / bbox_max_size;\n      uv1.x = (v1.x - bbox.min.x) / bbox_max_size;\n      uv1.y = (v1.y - bbox.min.y) / bbox_max_size;\n      uv2.x = (v2.x - bbox.min.x) / bbox_max_size;\n      uv2.y = (v2.y - bbox.min.y) / bbox_max_size;\n    }\n    return {\n      uv0: uv0,\n      uv1: uv1,\n      uv2: uv2\n    };\n  };\n  if (geom.index) {\n    // is it indexed buffer geometry?\n    for (var vi = 0; vi < geom.index.array.length; vi += 3) {\n      var idx0 = geom.index.array[vi];\n      var idx1 = geom.index.array[vi + 1];\n      var idx2 = geom.index.array[vi + 2];\n      var vx0 = geom.attributes.position.array[3 * idx0];\n      var vy0 = geom.attributes.position.array[3 * idx0 + 1];\n      var vz0 = geom.attributes.position.array[3 * idx0 + 2];\n      var vx1 = geom.attributes.position.array[3 * idx1];\n      var vy1 = geom.attributes.position.array[3 * idx1 + 1];\n      var vz1 = geom.attributes.position.array[3 * idx1 + 2];\n      var vx2 = geom.attributes.position.array[3 * idx2];\n      var vy2 = geom.attributes.position.array[3 * idx2 + 1];\n      var vz2 = geom.attributes.position.array[3 * idx2 + 2];\n      var v0 = new THREE.Vector3(vx0, vy0, vz0);\n      var v1 = new THREE.Vector3(vx1, vy1, vz1);\n      var v2 = new THREE.Vector3(vx2, vy2, vz2);\n      var uvs = makeUVs(v0, v1, v2);\n      coords[2 * idx0] = uvs.uv0.x;\n      coords[2 * idx0 + 1] = uvs.uv0.y;\n      coords[2 * idx1] = uvs.uv1.x;\n      coords[2 * idx1 + 1] = uvs.uv1.y;\n      coords[2 * idx2] = uvs.uv2.x;\n      coords[2 * idx2 + 1] = uvs.uv2.y;\n    }\n  } else {\n    for (var _vi = 0; _vi < geom.attributes.position.array.length; _vi += 9) {\n      var _vx = geom.attributes.position.array[_vi];\n      var _vy = geom.attributes.position.array[_vi + 1];\n      var _vz = geom.attributes.position.array[_vi + 2];\n      var _vx2 = geom.attributes.position.array[_vi + 3];\n      var _vy2 = geom.attributes.position.array[_vi + 4];\n      var _vz2 = geom.attributes.position.array[_vi + 5];\n      var _vx3 = geom.attributes.position.array[_vi + 6];\n      var _vy3 = geom.attributes.position.array[_vi + 7];\n      var _vz3 = geom.attributes.position.array[_vi + 8];\n      var _v = new THREE.Vector3(_vx, _vy, _vz);\n      var _v2 = new THREE.Vector3(_vx2, _vy2, _vz2);\n      var _v3 = new THREE.Vector3(_vx3, _vy3, _vz3);\n      var _uvs = makeUVs(_v, _v2, _v3);\n      var _idx = _vi / 3;\n      var _idx2 = _idx + 1;\n      var _idx3 = _idx + 2;\n      coords[2 * _idx] = _uvs.uv0.x;\n      coords[2 * _idx + 1] = _uvs.uv0.y;\n      coords[2 * _idx2] = _uvs.uv1.x;\n      coords[2 * _idx2 + 1] = _uvs.uv1.y;\n      coords[2 * _idx3] = _uvs.uv2.x;\n      coords[2 * _idx3 + 1] = _uvs.uv2.y;\n    }\n  }\n  if (geom.attributes.uv) delete geom.attributes.uv;\n  geom.setAttribute(\"uv\", new THREE.Float32BufferAttribute(coords, 2));\n}\nvar geometry = /*#__PURE__*/Object.freeze({\n  __proto__: null,\n  RoundedPlaneGeometry: RoundedPlaneGeometry,\n  applyCylindricalUV: applyCylindricalUV,\n  applySphereUV: applySphereUV,\n  applyBoxUV: applyBoxUV\n});\nexport { RoundedPlaneGeometry as R, applyCylindricalUV as a, applySphereUV as b, applyBoxUV as c, geometry as g };", "map": {"version": 3, "names": ["_", "_classCallCheck", "_setPrototypeOf", "a", "_isNativeReflectConstruct", "THREE", "_inherits", "subClass", "superClass", "TypeError", "prototype", "Object", "create", "constructor", "value", "writable", "configurable", "_getPrototypeOf", "o", "setPrototypeOf", "getPrototypeOf", "__proto__", "_assertThisInitialized", "self", "ReferenceError", "_possibleConstructorReturn", "call", "_createSuper", "Derived", "hasNativeReflectConstruct", "_createSuperInternal", "Super", "result", "<PERSON><PERSON><PERSON><PERSON>", "Reflect", "construct", "arguments", "apply", "RoundedPlaneGeometry", "_THREE$BufferGeometry", "_super", "_this", "width", "length", "undefined", "height", "radius", "segments", "parameters", "wi", "hi", "ul", "ur", "vl", "vh", "positions", "uvs", "n", "indices", "phi", "cos", "sin", "xc", "yc", "uc", "vc", "idx", "i", "j", "Math", "PI", "push", "setIndex", "BufferAttribute", "Uint32Array", "setAttribute", "Float32Array", "BufferGeometry", "applyCylindricalUV", "bufferGeometry", "attributes", "position", "array", "x", "y", "z", "atan2", "uv", "Float32BufferAttribute", "needsUpdate", "applySphereUV", "vertices", "Vector3", "polarVertices", "map", "cartesian2polar", "_i", "tri", "Triangle", "normal", "getNormal", "f", "vertex", "theta", "alignedVertice", "r", "canvasPoint", "polar2canvas", "sqrt", "acos", "polarPoint", "applyBoxUV", "computeBoundingBox", "bboxSize", "boundingBox", "getSize", "boxSize", "min", "boxGeometry", "BoxGeometry", "cube", "<PERSON><PERSON>", "rotation", "set", "updateWorldMatrix", "transformMatrix", "matrix", "clone", "invert", "uvBbox", "Box3", "_applyBoxUV", "geom", "bbox", "bbox_max_size", "coords", "makeUVs", "v0", "v1", "v2", "applyMatrix4", "crossVectors", "sub", "normalize", "abs", "uv0", "Vector2", "uv1", "uv2", "max", "index", "vi", "idx0", "idx1", "idx2", "vx0", "vy0", "vz0", "vx1", "vy1", "vz1", "vx2", "vy2", "vz2", "_vi", "_vx", "_vy", "_vz", "_vx2", "_vy2", "_vz2", "_vx3", "_vy3", "_vz3", "_v", "_v2", "_v3", "_uvs", "_idx", "_idx2", "_idx3", "geometry", "freeze", "R", "b", "c", "g"], "sources": ["C:/Users/<USER>/Documents/Apps_Creations/SiahMan_Racing4Speed_WebGame/siah-man-racing/node_modules/maath/dist/geometry-0fb11825.esm.js"], "sourcesContent": ["import { _ as _classCallCheck } from './classCallCheck-9098b006.esm.js';\nimport { _ as _setPrototypeOf, a as _isNativeReflectConstruct } from './isNativeReflectConstruct-5594d075.esm.js';\nimport * as THREE from 'three';\n\nfunction _inherits(subClass, superClass) {\n  if (typeof superClass !== \"function\" && superClass !== null) {\n    throw new TypeError(\"Super expression must either be null or a function\");\n  }\n\n  subClass.prototype = Object.create(superClass && superClass.prototype, {\n    constructor: {\n      value: subClass,\n      writable: true,\n      configurable: true\n    }\n  });\n  if (superClass) _setPrototypeOf(subClass, superClass);\n}\n\nfunction _getPrototypeOf(o) {\n  _getPrototypeOf = Object.setPrototypeOf ? Object.getPrototypeOf : function _getPrototypeOf(o) {\n    return o.__proto__ || Object.getPrototypeOf(o);\n  };\n  return _getPrototypeOf(o);\n}\n\nfunction _assertThisInitialized(self) {\n  if (self === void 0) {\n    throw new ReferenceError(\"this hasn't been initialised - super() hasn't been called\");\n  }\n\n  return self;\n}\n\nfunction _possibleConstructorReturn(self, call) {\n  if (call && (typeof call === \"object\" || typeof call === \"function\")) {\n    return call;\n  } else if (call !== void 0) {\n    throw new TypeError(\"Derived constructors may only return object or undefined\");\n  }\n\n  return _assertThisInitialized(self);\n}\n\nfunction _createSuper(Derived) {\n  var hasNativeReflectConstruct = _isNativeReflectConstruct();\n  return function _createSuperInternal() {\n    var Super = _getPrototypeOf(Derived),\n        result;\n\n    if (hasNativeReflectConstruct) {\n      var NewTarget = _getPrototypeOf(this).constructor;\n      result = Reflect.construct(Super, arguments, NewTarget);\n    } else {\n      result = Super.apply(this, arguments);\n    }\n\n    return _possibleConstructorReturn(this, result);\n  };\n}\n\nvar RoundedPlaneGeometry = /*#__PURE__*/function (_THREE$BufferGeometry) {\n  _inherits(RoundedPlaneGeometry, _THREE$BufferGeometry);\n\n  var _super = _createSuper(RoundedPlaneGeometry);\n\n  function RoundedPlaneGeometry() {\n    var _this;\n\n    var width = arguments.length > 0 && arguments[0] !== undefined ? arguments[0] : 2;\n    var height = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : 1;\n    var radius = arguments.length > 2 && arguments[2] !== undefined ? arguments[2] : 0.2;\n    var segments = arguments.length > 3 && arguments[3] !== undefined ? arguments[3] : 16;\n\n    _classCallCheck(this, RoundedPlaneGeometry);\n\n    _this = _super.call(this);\n    _this.parameters = {\n      width: width,\n      height: height,\n      radius: radius,\n      segments: segments\n    }; // helper const's\n\n    var wi = width / 2 - radius; // inner width\n\n    var hi = height / 2 - radius; // inner height\n\n    var ul = radius / width; // u left\n\n    var ur = (width - radius) / width; // u right\n\n    var vl = radius / height; // v low\n\n    var vh = (height - radius) / height; // v high\n\n    var positions = [wi, hi, 0, -wi, hi, 0, -wi, -hi, 0, wi, -hi, 0];\n    var uvs = [ur, vh, ul, vh, ul, vl, ur, vl];\n    var n = [3 * (segments + 1) + 3, 3 * (segments + 1) + 4, segments + 4, segments + 5, 2 * (segments + 1) + 4, 2, 1, 2 * (segments + 1) + 3, 3, 4 * (segments + 1) + 3, 4, 0];\n    var indices = [n[0], n[1], n[2], n[0], n[2], n[3], n[4], n[5], n[6], n[4], n[6], n[7], n[8], n[9], n[10], n[8], n[10], n[11]];\n    var phi, cos, sin, xc, yc, uc, vc, idx;\n\n    for (var i = 0; i < 4; i++) {\n      xc = i < 1 || i > 2 ? wi : -wi;\n      yc = i < 2 ? hi : -hi;\n      uc = i < 1 || i > 2 ? ur : ul;\n      vc = i < 2 ? vh : vl;\n\n      for (var j = 0; j <= segments; j++) {\n        phi = Math.PI / 2 * (i + j / segments);\n        cos = Math.cos(phi);\n        sin = Math.sin(phi);\n        positions.push(xc + radius * cos, yc + radius * sin, 0);\n        uvs.push(uc + ul * cos, vc + vl * sin);\n\n        if (j < segments) {\n          idx = (segments + 1) * i + j + 4;\n          indices.push(i, idx, idx + 1);\n        }\n      }\n    }\n\n    _this.setIndex(new THREE.BufferAttribute(new Uint32Array(indices), 1));\n\n    _this.setAttribute(\"position\", new THREE.BufferAttribute(new Float32Array(positions), 3));\n\n    _this.setAttribute(\"uv\", new THREE.BufferAttribute(new Float32Array(uvs), 2));\n\n    return _this;\n  }\n\n  return RoundedPlaneGeometry;\n}(THREE.BufferGeometry); // Author: https://stackoverflow.com/users/128511/gman\n// https://stackoverflow.com/questions/34958072/programmatically-generate-simple-uv-mapping-for-models\n\nfunction applyCylindricalUV(bufferGeometry) {\n  var uvs = [];\n\n  for (var i = 0; i < bufferGeometry.attributes.position.array.length / 3; i++) {\n    var x = bufferGeometry.attributes.position.array[i * 3 + 0];\n    var y = bufferGeometry.attributes.position.array[i * 3 + 1];\n    var z = bufferGeometry.attributes.position.array[i * 3 + 2];\n    uvs.push(Math.atan2(x, z) / Math.PI * 0.5 + 0.5, y / Math.PI * 0.5 + 0.5);\n  }\n\n  if (bufferGeometry.attributes.uv) delete bufferGeometry.attributes.uv;\n  bufferGeometry.setAttribute(\"uv\", new THREE.Float32BufferAttribute(uvs, 2));\n  bufferGeometry.attributes.uv.needsUpdate = true;\n  return bufferGeometry;\n} // Author: https://stackoverflow.com/users/268905/knee-cola\n// https://stackoverflow.com/questions/20774648/three-js-generate-uv-coordinate\n\nfunction applySphereUV(bufferGeometry) {\n  var uvs = [];\n  var vertices = [];\n\n  for (var i = 0; i < bufferGeometry.attributes.position.array.length / 3; i++) {\n    var x = bufferGeometry.attributes.position.array[i * 3 + 0];\n    var y = bufferGeometry.attributes.position.array[i * 3 + 1];\n    var z = bufferGeometry.attributes.position.array[i * 3 + 2];\n    vertices.push(new THREE.Vector3(x, y, z));\n  }\n\n  var polarVertices = vertices.map(cartesian2polar);\n\n  for (var _i = 0; _i < polarVertices.length / 3; _i++) {\n    var tri = new THREE.Triangle(vertices[_i * 3 + 0], vertices[_i * 3 + 1], vertices[_i * 3 + 2]);\n    var normal = tri.getNormal(new THREE.Vector3());\n\n    for (var f = 0; f < 3; f++) {\n      var vertex = polarVertices[_i * 3 + f];\n\n      if (vertex.theta === 0 && (vertex.phi === 0 || vertex.phi === Math.PI)) {\n        var alignedVertice = vertex.phi === 0 ? _i * 3 + 1 : _i * 3 + 0;\n        vertex = {\n          r: vertex.r,\n          phi: vertex.phi,\n          theta: polarVertices[alignedVertice].theta\n        };\n      }\n\n      if (vertex.theta === Math.PI && cartesian2polar(normal).theta < Math.PI / 2) {\n        vertex.theta = -Math.PI;\n      }\n\n      var canvasPoint = polar2canvas(vertex);\n      uvs.push(1 - canvasPoint.x, 1 - canvasPoint.y);\n    }\n  }\n\n  if (bufferGeometry.attributes.uv) delete bufferGeometry.attributes.uv;\n  bufferGeometry.setAttribute(\"uv\", new THREE.Float32BufferAttribute(uvs, 2));\n  bufferGeometry.attributes.uv.needsUpdate = true;\n  return bufferGeometry;\n}\n\nfunction cartesian2polar(position) {\n  var r = Math.sqrt(position.x * position.x + position.z * position.z + position.y * position.y);\n  return {\n    r: r,\n    phi: Math.acos(position.y / r),\n    theta: Math.atan2(position.z, position.x)\n  };\n}\n\nfunction polar2canvas(polarPoint) {\n  return {\n    y: polarPoint.phi / Math.PI,\n    x: (polarPoint.theta + Math.PI) / (2 * Math.PI)\n  };\n} // Author: Alex Khoroshylov (https://stackoverflow.com/users/8742287/alex-khoroshylov)\n// https://stackoverflow.com/questions/20774648/three-js-generate-uv-coordinate\n\n\nfunction applyBoxUV(bufferGeometry) {\n  bufferGeometry.computeBoundingBox();\n  var bboxSize = bufferGeometry.boundingBox.getSize(new THREE.Vector3());\n  var boxSize = Math.min(bboxSize.x, bboxSize.y, bboxSize.z);\n  var boxGeometry = new THREE.BoxGeometry(boxSize, boxSize, boxSize);\n  var cube = new THREE.Mesh(boxGeometry);\n  cube.rotation.set(0, 0, 0);\n  cube.updateWorldMatrix(true, false);\n  var transformMatrix = cube.matrix.clone().invert();\n  var uvBbox = new THREE.Box3(new THREE.Vector3(-boxSize / 2, -boxSize / 2, -boxSize / 2), new THREE.Vector3(boxSize / 2, boxSize / 2, boxSize / 2));\n\n  _applyBoxUV(bufferGeometry, transformMatrix, uvBbox, boxSize);\n\n  bufferGeometry.attributes.uv.needsUpdate = true;\n  return bufferGeometry;\n}\n\nfunction _applyBoxUV(geom, transformMatrix, bbox, bbox_max_size) {\n  var coords = [];\n  coords.length = 2 * geom.attributes.position.array.length / 3; //maps 3 verts of 1 face on the better side of the cube\n  //side of the cube can be XY, XZ or YZ\n\n  var makeUVs = function makeUVs(v0, v1, v2) {\n    //pre-rotate the model so that cube sides match world axis\n    v0.applyMatrix4(transformMatrix);\n    v1.applyMatrix4(transformMatrix);\n    v2.applyMatrix4(transformMatrix); //get normal of the face, to know into which cube side it maps better\n\n    var n = new THREE.Vector3();\n    n.crossVectors(v1.clone().sub(v0), v1.clone().sub(v2)).normalize();\n    n.x = Math.abs(n.x);\n    n.y = Math.abs(n.y);\n    n.z = Math.abs(n.z);\n    var uv0 = new THREE.Vector2();\n    var uv1 = new THREE.Vector2();\n    var uv2 = new THREE.Vector2(); // xz mapping\n\n    if (n.y > n.x && n.y > n.z) {\n      uv0.x = (v0.x - bbox.min.x) / bbox_max_size;\n      uv0.y = (bbox.max.z - v0.z) / bbox_max_size;\n      uv1.x = (v1.x - bbox.min.x) / bbox_max_size;\n      uv1.y = (bbox.max.z - v1.z) / bbox_max_size;\n      uv2.x = (v2.x - bbox.min.x) / bbox_max_size;\n      uv2.y = (bbox.max.z - v2.z) / bbox_max_size;\n    } else if (n.x > n.y && n.x > n.z) {\n      uv0.x = (v0.z - bbox.min.z) / bbox_max_size;\n      uv0.y = (v0.y - bbox.min.y) / bbox_max_size;\n      uv1.x = (v1.z - bbox.min.z) / bbox_max_size;\n      uv1.y = (v1.y - bbox.min.y) / bbox_max_size;\n      uv2.x = (v2.z - bbox.min.z) / bbox_max_size;\n      uv2.y = (v2.y - bbox.min.y) / bbox_max_size;\n    } else if (n.z > n.y && n.z > n.x) {\n      uv0.x = (v0.x - bbox.min.x) / bbox_max_size;\n      uv0.y = (v0.y - bbox.min.y) / bbox_max_size;\n      uv1.x = (v1.x - bbox.min.x) / bbox_max_size;\n      uv1.y = (v1.y - bbox.min.y) / bbox_max_size;\n      uv2.x = (v2.x - bbox.min.x) / bbox_max_size;\n      uv2.y = (v2.y - bbox.min.y) / bbox_max_size;\n    }\n\n    return {\n      uv0: uv0,\n      uv1: uv1,\n      uv2: uv2\n    };\n  };\n\n  if (geom.index) {\n    // is it indexed buffer geometry?\n    for (var vi = 0; vi < geom.index.array.length; vi += 3) {\n      var idx0 = geom.index.array[vi];\n      var idx1 = geom.index.array[vi + 1];\n      var idx2 = geom.index.array[vi + 2];\n      var vx0 = geom.attributes.position.array[3 * idx0];\n      var vy0 = geom.attributes.position.array[3 * idx0 + 1];\n      var vz0 = geom.attributes.position.array[3 * idx0 + 2];\n      var vx1 = geom.attributes.position.array[3 * idx1];\n      var vy1 = geom.attributes.position.array[3 * idx1 + 1];\n      var vz1 = geom.attributes.position.array[3 * idx1 + 2];\n      var vx2 = geom.attributes.position.array[3 * idx2];\n      var vy2 = geom.attributes.position.array[3 * idx2 + 1];\n      var vz2 = geom.attributes.position.array[3 * idx2 + 2];\n      var v0 = new THREE.Vector3(vx0, vy0, vz0);\n      var v1 = new THREE.Vector3(vx1, vy1, vz1);\n      var v2 = new THREE.Vector3(vx2, vy2, vz2);\n      var uvs = makeUVs(v0, v1, v2);\n      coords[2 * idx0] = uvs.uv0.x;\n      coords[2 * idx0 + 1] = uvs.uv0.y;\n      coords[2 * idx1] = uvs.uv1.x;\n      coords[2 * idx1 + 1] = uvs.uv1.y;\n      coords[2 * idx2] = uvs.uv2.x;\n      coords[2 * idx2 + 1] = uvs.uv2.y;\n    }\n  } else {\n    for (var _vi = 0; _vi < geom.attributes.position.array.length; _vi += 9) {\n      var _vx = geom.attributes.position.array[_vi];\n      var _vy = geom.attributes.position.array[_vi + 1];\n      var _vz = geom.attributes.position.array[_vi + 2];\n      var _vx2 = geom.attributes.position.array[_vi + 3];\n      var _vy2 = geom.attributes.position.array[_vi + 4];\n      var _vz2 = geom.attributes.position.array[_vi + 5];\n      var _vx3 = geom.attributes.position.array[_vi + 6];\n      var _vy3 = geom.attributes.position.array[_vi + 7];\n      var _vz3 = geom.attributes.position.array[_vi + 8];\n\n      var _v = new THREE.Vector3(_vx, _vy, _vz);\n\n      var _v2 = new THREE.Vector3(_vx2, _vy2, _vz2);\n\n      var _v3 = new THREE.Vector3(_vx3, _vy3, _vz3);\n\n      var _uvs = makeUVs(_v, _v2, _v3);\n\n      var _idx = _vi / 3;\n\n      var _idx2 = _idx + 1;\n\n      var _idx3 = _idx + 2;\n\n      coords[2 * _idx] = _uvs.uv0.x;\n      coords[2 * _idx + 1] = _uvs.uv0.y;\n      coords[2 * _idx2] = _uvs.uv1.x;\n      coords[2 * _idx2 + 1] = _uvs.uv1.y;\n      coords[2 * _idx3] = _uvs.uv2.x;\n      coords[2 * _idx3 + 1] = _uvs.uv2.y;\n    }\n  }\n\n  if (geom.attributes.uv) delete geom.attributes.uv;\n  geom.setAttribute(\"uv\", new THREE.Float32BufferAttribute(coords, 2));\n}\n\nvar geometry = /*#__PURE__*/Object.freeze({\n  __proto__: null,\n  RoundedPlaneGeometry: RoundedPlaneGeometry,\n  applyCylindricalUV: applyCylindricalUV,\n  applySphereUV: applySphereUV,\n  applyBoxUV: applyBoxUV\n});\n\nexport { RoundedPlaneGeometry as R, applyCylindricalUV as a, applySphereUV as b, applyBoxUV as c, geometry as g };\n"], "mappings": "AAAA,SAASA,CAAC,IAAIC,eAAe,QAAQ,kCAAkC;AACvE,SAASD,CAAC,IAAIE,eAAe,EAAEC,CAAC,IAAIC,yBAAyB,QAAQ,4CAA4C;AACjH,OAAO,KAAKC,KAAK,MAAM,OAAO;AAE9B,SAASC,SAASA,CAACC,QAAQ,EAAEC,UAAU,EAAE;EACvC,IAAI,OAAOA,UAAU,KAAK,UAAU,IAAIA,UAAU,KAAK,IAAI,EAAE;IAC3D,MAAM,IAAIC,SAAS,CAAC,oDAAoD,CAAC;EAC3E;EAEAF,QAAQ,CAACG,SAAS,GAAGC,MAAM,CAACC,MAAM,CAACJ,UAAU,IAAIA,UAAU,CAACE,SAAS,EAAE;IACrEG,WAAW,EAAE;MACXC,KAAK,EAAEP,QAAQ;MACfQ,QAAQ,EAAE,IAAI;MACdC,YAAY,EAAE;IAChB;EACF,CAAC,CAAC;EACF,IAAIR,UAAU,EAAEN,eAAe,CAACK,QAAQ,EAAEC,UAAU,CAAC;AACvD;AAEA,SAASS,eAAeA,CAACC,CAAC,EAAE;EAC1BD,eAAe,GAAGN,MAAM,CAACQ,cAAc,GAAGR,MAAM,CAACS,cAAc,GAAG,SAASH,eAAeA,CAACC,CAAC,EAAE;IAC5F,OAAOA,CAAC,CAACG,SAAS,IAAIV,MAAM,CAACS,cAAc,CAACF,CAAC,CAAC;EAChD,CAAC;EACD,OAAOD,eAAe,CAACC,CAAC,CAAC;AAC3B;AAEA,SAASI,sBAAsBA,CAACC,IAAI,EAAE;EACpC,IAAIA,IAAI,KAAK,KAAK,CAAC,EAAE;IACnB,MAAM,IAAIC,cAAc,CAAC,2DAA2D,CAAC;EACvF;EAEA,OAAOD,IAAI;AACb;AAEA,SAASE,0BAA0BA,CAACF,IAAI,EAAEG,IAAI,EAAE;EAC9C,IAAIA,IAAI,KAAK,OAAOA,IAAI,KAAK,QAAQ,IAAI,OAAOA,IAAI,KAAK,UAAU,CAAC,EAAE;IACpE,OAAOA,IAAI;EACb,CAAC,MAAM,IAAIA,IAAI,KAAK,KAAK,CAAC,EAAE;IAC1B,MAAM,IAAIjB,SAAS,CAAC,0DAA0D,CAAC;EACjF;EAEA,OAAOa,sBAAsB,CAACC,IAAI,CAAC;AACrC;AAEA,SAASI,YAAYA,CAACC,OAAO,EAAE;EAC7B,IAAIC,yBAAyB,GAAGzB,yBAAyB,CAAC,CAAC;EAC3D,OAAO,SAAS0B,oBAAoBA,CAAA,EAAG;IACrC,IAAIC,KAAK,GAAGd,eAAe,CAACW,OAAO,CAAC;MAChCI,MAAM;IAEV,IAAIH,yBAAyB,EAAE;MAC7B,IAAII,SAAS,GAAGhB,eAAe,CAAC,IAAI,CAAC,CAACJ,WAAW;MACjDmB,MAAM,GAAGE,OAAO,CAACC,SAAS,CAACJ,KAAK,EAAEK,SAAS,EAAEH,SAAS,CAAC;IACzD,CAAC,MAAM;MACLD,MAAM,GAAGD,KAAK,CAACM,KAAK,CAAC,IAAI,EAAED,SAAS,CAAC;IACvC;IAEA,OAAOX,0BAA0B,CAAC,IAAI,EAAEO,MAAM,CAAC;EACjD,CAAC;AACH;AAEA,IAAIM,oBAAoB,GAAG,aAAa,UAAUC,qBAAqB,EAAE;EACvEjC,SAAS,CAACgC,oBAAoB,EAAEC,qBAAqB,CAAC;EAEtD,IAAIC,MAAM,GAAGb,YAAY,CAACW,oBAAoB,CAAC;EAE/C,SAASA,oBAAoBA,CAAA,EAAG;IAC9B,IAAIG,KAAK;IAET,IAAIC,KAAK,GAAGN,SAAS,CAACO,MAAM,GAAG,CAAC,IAAIP,SAAS,CAAC,CAAC,CAAC,KAAKQ,SAAS,GAAGR,SAAS,CAAC,CAAC,CAAC,GAAG,CAAC;IACjF,IAAIS,MAAM,GAAGT,SAAS,CAACO,MAAM,GAAG,CAAC,IAAIP,SAAS,CAAC,CAAC,CAAC,KAAKQ,SAAS,GAAGR,SAAS,CAAC,CAAC,CAAC,GAAG,CAAC;IAClF,IAAIU,MAAM,GAAGV,SAAS,CAACO,MAAM,GAAG,CAAC,IAAIP,SAAS,CAAC,CAAC,CAAC,KAAKQ,SAAS,GAAGR,SAAS,CAAC,CAAC,CAAC,GAAG,GAAG;IACpF,IAAIW,QAAQ,GAAGX,SAAS,CAACO,MAAM,GAAG,CAAC,IAAIP,SAAS,CAAC,CAAC,CAAC,KAAKQ,SAAS,GAAGR,SAAS,CAAC,CAAC,CAAC,GAAG,EAAE;IAErFnC,eAAe,CAAC,IAAI,EAAEqC,oBAAoB,CAAC;IAE3CG,KAAK,GAAGD,MAAM,CAACd,IAAI,CAAC,IAAI,CAAC;IACzBe,KAAK,CAACO,UAAU,GAAG;MACjBN,KAAK,EAAEA,KAAK;MACZG,MAAM,EAAEA,MAAM;MACdC,MAAM,EAAEA,MAAM;MACdC,QAAQ,EAAEA;IACZ,CAAC,CAAC,CAAC;;IAEH,IAAIE,EAAE,GAAGP,KAAK,GAAG,CAAC,GAAGI,MAAM,CAAC,CAAC;;IAE7B,IAAII,EAAE,GAAGL,MAAM,GAAG,CAAC,GAAGC,MAAM,CAAC,CAAC;;IAE9B,IAAIK,EAAE,GAAGL,MAAM,GAAGJ,KAAK,CAAC,CAAC;;IAEzB,IAAIU,EAAE,GAAG,CAACV,KAAK,GAAGI,MAAM,IAAIJ,KAAK,CAAC,CAAC;;IAEnC,IAAIW,EAAE,GAAGP,MAAM,GAAGD,MAAM,CAAC,CAAC;;IAE1B,IAAIS,EAAE,GAAG,CAACT,MAAM,GAAGC,MAAM,IAAID,MAAM,CAAC,CAAC;;IAErC,IAAIU,SAAS,GAAG,CAACN,EAAE,EAAEC,EAAE,EAAE,CAAC,EAAE,CAACD,EAAE,EAAEC,EAAE,EAAE,CAAC,EAAE,CAACD,EAAE,EAAE,CAACC,EAAE,EAAE,CAAC,EAAED,EAAE,EAAE,CAACC,EAAE,EAAE,CAAC,CAAC;IAChE,IAAIM,GAAG,GAAG,CAACJ,EAAE,EAAEE,EAAE,EAAEH,EAAE,EAAEG,EAAE,EAAEH,EAAE,EAAEE,EAAE,EAAED,EAAE,EAAEC,EAAE,CAAC;IAC1C,IAAII,CAAC,GAAG,CAAC,CAAC,IAAIV,QAAQ,GAAG,CAAC,CAAC,GAAG,CAAC,EAAE,CAAC,IAAIA,QAAQ,GAAG,CAAC,CAAC,GAAG,CAAC,EAAEA,QAAQ,GAAG,CAAC,EAAEA,QAAQ,GAAG,CAAC,EAAE,CAAC,IAAIA,QAAQ,GAAG,CAAC,CAAC,GAAG,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,IAAIA,QAAQ,GAAG,CAAC,CAAC,GAAG,CAAC,EAAE,CAAC,EAAE,CAAC,IAAIA,QAAQ,GAAG,CAAC,CAAC,GAAG,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC;IAC3K,IAAIW,OAAO,GAAG,CAACD,CAAC,CAAC,CAAC,CAAC,EAAEA,CAAC,CAAC,CAAC,CAAC,EAAEA,CAAC,CAAC,CAAC,CAAC,EAAEA,CAAC,CAAC,CAAC,CAAC,EAAEA,CAAC,CAAC,CAAC,CAAC,EAAEA,CAAC,CAAC,CAAC,CAAC,EAAEA,CAAC,CAAC,CAAC,CAAC,EAAEA,CAAC,CAAC,CAAC,CAAC,EAAEA,CAAC,CAAC,CAAC,CAAC,EAAEA,CAAC,CAAC,CAAC,CAAC,EAAEA,CAAC,CAAC,CAAC,CAAC,EAAEA,CAAC,CAAC,CAAC,CAAC,EAAEA,CAAC,CAAC,CAAC,CAAC,EAAEA,CAAC,CAAC,CAAC,CAAC,EAAEA,CAAC,CAAC,EAAE,CAAC,EAAEA,CAAC,CAAC,CAAC,CAAC,EAAEA,CAAC,CAAC,EAAE,CAAC,EAAEA,CAAC,CAAC,EAAE,CAAC,CAAC;IAC7H,IAAIE,GAAG,EAAEC,GAAG,EAAEC,GAAG,EAAEC,EAAE,EAAEC,EAAE,EAAEC,EAAE,EAAEC,EAAE,EAAEC,GAAG;IAEtC,KAAK,IAAIC,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAG,CAAC,EAAEA,CAAC,EAAE,EAAE;MAC1BL,EAAE,GAAGK,CAAC,GAAG,CAAC,IAAIA,CAAC,GAAG,CAAC,GAAGlB,EAAE,GAAG,CAACA,EAAE;MAC9Bc,EAAE,GAAGI,CAAC,GAAG,CAAC,GAAGjB,EAAE,GAAG,CAACA,EAAE;MACrBc,EAAE,GAAGG,CAAC,GAAG,CAAC,IAAIA,CAAC,GAAG,CAAC,GAAGf,EAAE,GAAGD,EAAE;MAC7Bc,EAAE,GAAGE,CAAC,GAAG,CAAC,GAAGb,EAAE,GAAGD,EAAE;MAEpB,KAAK,IAAIe,CAAC,GAAG,CAAC,EAAEA,CAAC,IAAIrB,QAAQ,EAAEqB,CAAC,EAAE,EAAE;QAClCT,GAAG,GAAGU,IAAI,CAACC,EAAE,GAAG,CAAC,IAAIH,CAAC,GAAGC,CAAC,GAAGrB,QAAQ,CAAC;QACtCa,GAAG,GAAGS,IAAI,CAACT,GAAG,CAACD,GAAG,CAAC;QACnBE,GAAG,GAAGQ,IAAI,CAACR,GAAG,CAACF,GAAG,CAAC;QACnBJ,SAAS,CAACgB,IAAI,CAACT,EAAE,GAAGhB,MAAM,GAAGc,GAAG,EAAEG,EAAE,GAAGjB,MAAM,GAAGe,GAAG,EAAE,CAAC,CAAC;QACvDL,GAAG,CAACe,IAAI,CAACP,EAAE,GAAGb,EAAE,GAAGS,GAAG,EAAEK,EAAE,GAAGZ,EAAE,GAAGQ,GAAG,CAAC;QAEtC,IAAIO,CAAC,GAAGrB,QAAQ,EAAE;UAChBmB,GAAG,GAAG,CAACnB,QAAQ,GAAG,CAAC,IAAIoB,CAAC,GAAGC,CAAC,GAAG,CAAC;UAChCV,OAAO,CAACa,IAAI,CAACJ,CAAC,EAAED,GAAG,EAAEA,GAAG,GAAG,CAAC,CAAC;QAC/B;MACF;IACF;IAEAzB,KAAK,CAAC+B,QAAQ,CAAC,IAAInE,KAAK,CAACoE,eAAe,CAAC,IAAIC,WAAW,CAAChB,OAAO,CAAC,EAAE,CAAC,CAAC,CAAC;IAEtEjB,KAAK,CAACkC,YAAY,CAAC,UAAU,EAAE,IAAItE,KAAK,CAACoE,eAAe,CAAC,IAAIG,YAAY,CAACrB,SAAS,CAAC,EAAE,CAAC,CAAC,CAAC;IAEzFd,KAAK,CAACkC,YAAY,CAAC,IAAI,EAAE,IAAItE,KAAK,CAACoE,eAAe,CAAC,IAAIG,YAAY,CAACpB,GAAG,CAAC,EAAE,CAAC,CAAC,CAAC;IAE7E,OAAOf,KAAK;EACd;EAEA,OAAOH,oBAAoB;AAC7B,CAAC,CAACjC,KAAK,CAACwE,cAAc,CAAC,CAAC,CAAC;AACzB;;AAEA,SAASC,kBAAkBA,CAACC,cAAc,EAAE;EAC1C,IAAIvB,GAAG,GAAG,EAAE;EAEZ,KAAK,IAAIW,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGY,cAAc,CAACC,UAAU,CAACC,QAAQ,CAACC,KAAK,CAACvC,MAAM,GAAG,CAAC,EAAEwB,CAAC,EAAE,EAAE;IAC5E,IAAIgB,CAAC,GAAGJ,cAAc,CAACC,UAAU,CAACC,QAAQ,CAACC,KAAK,CAACf,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC;IAC3D,IAAIiB,CAAC,GAAGL,cAAc,CAACC,UAAU,CAACC,QAAQ,CAACC,KAAK,CAACf,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC;IAC3D,IAAIkB,CAAC,GAAGN,cAAc,CAACC,UAAU,CAACC,QAAQ,CAACC,KAAK,CAACf,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC;IAC3DX,GAAG,CAACe,IAAI,CAACF,IAAI,CAACiB,KAAK,CAACH,CAAC,EAAEE,CAAC,CAAC,GAAGhB,IAAI,CAACC,EAAE,GAAG,GAAG,GAAG,GAAG,EAAEc,CAAC,GAAGf,IAAI,CAACC,EAAE,GAAG,GAAG,GAAG,GAAG,CAAC;EAC3E;EAEA,IAAIS,cAAc,CAACC,UAAU,CAACO,EAAE,EAAE,OAAOR,cAAc,CAACC,UAAU,CAACO,EAAE;EACrER,cAAc,CAACJ,YAAY,CAAC,IAAI,EAAE,IAAItE,KAAK,CAACmF,sBAAsB,CAAChC,GAAG,EAAE,CAAC,CAAC,CAAC;EAC3EuB,cAAc,CAACC,UAAU,CAACO,EAAE,CAACE,WAAW,GAAG,IAAI;EAC/C,OAAOV,cAAc;AACvB,CAAC,CAAC;AACF;;AAEA,SAASW,aAAaA,CAACX,cAAc,EAAE;EACrC,IAAIvB,GAAG,GAAG,EAAE;EACZ,IAAImC,QAAQ,GAAG,EAAE;EAEjB,KAAK,IAAIxB,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGY,cAAc,CAACC,UAAU,CAACC,QAAQ,CAACC,KAAK,CAACvC,MAAM,GAAG,CAAC,EAAEwB,CAAC,EAAE,EAAE;IAC5E,IAAIgB,CAAC,GAAGJ,cAAc,CAACC,UAAU,CAACC,QAAQ,CAACC,KAAK,CAACf,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC;IAC3D,IAAIiB,CAAC,GAAGL,cAAc,CAACC,UAAU,CAACC,QAAQ,CAACC,KAAK,CAACf,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC;IAC3D,IAAIkB,CAAC,GAAGN,cAAc,CAACC,UAAU,CAACC,QAAQ,CAACC,KAAK,CAACf,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC;IAC3DwB,QAAQ,CAACpB,IAAI,CAAC,IAAIlE,KAAK,CAACuF,OAAO,CAACT,CAAC,EAAEC,CAAC,EAAEC,CAAC,CAAC,CAAC;EAC3C;EAEA,IAAIQ,aAAa,GAAGF,QAAQ,CAACG,GAAG,CAACC,eAAe,CAAC;EAEjD,KAAK,IAAIC,EAAE,GAAG,CAAC,EAAEA,EAAE,GAAGH,aAAa,CAAClD,MAAM,GAAG,CAAC,EAAEqD,EAAE,EAAE,EAAE;IACpD,IAAIC,GAAG,GAAG,IAAI5F,KAAK,CAAC6F,QAAQ,CAACP,QAAQ,CAACK,EAAE,GAAG,CAAC,GAAG,CAAC,CAAC,EAAEL,QAAQ,CAACK,EAAE,GAAG,CAAC,GAAG,CAAC,CAAC,EAAEL,QAAQ,CAACK,EAAE,GAAG,CAAC,GAAG,CAAC,CAAC,CAAC;IAC9F,IAAIG,MAAM,GAAGF,GAAG,CAACG,SAAS,CAAC,IAAI/F,KAAK,CAACuF,OAAO,CAAC,CAAC,CAAC;IAE/C,KAAK,IAAIS,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAG,CAAC,EAAEA,CAAC,EAAE,EAAE;MAC1B,IAAIC,MAAM,GAAGT,aAAa,CAACG,EAAE,GAAG,CAAC,GAAGK,CAAC,CAAC;MAEtC,IAAIC,MAAM,CAACC,KAAK,KAAK,CAAC,KAAKD,MAAM,CAAC3C,GAAG,KAAK,CAAC,IAAI2C,MAAM,CAAC3C,GAAG,KAAKU,IAAI,CAACC,EAAE,CAAC,EAAE;QACtE,IAAIkC,cAAc,GAAGF,MAAM,CAAC3C,GAAG,KAAK,CAAC,GAAGqC,EAAE,GAAG,CAAC,GAAG,CAAC,GAAGA,EAAE,GAAG,CAAC,GAAG,CAAC;QAC/DM,MAAM,GAAG;UACPG,CAAC,EAAEH,MAAM,CAACG,CAAC;UACX9C,GAAG,EAAE2C,MAAM,CAAC3C,GAAG;UACf4C,KAAK,EAAEV,aAAa,CAACW,cAAc,CAAC,CAACD;QACvC,CAAC;MACH;MAEA,IAAID,MAAM,CAACC,KAAK,KAAKlC,IAAI,CAACC,EAAE,IAAIyB,eAAe,CAACI,MAAM,CAAC,CAACI,KAAK,GAAGlC,IAAI,CAACC,EAAE,GAAG,CAAC,EAAE;QAC3EgC,MAAM,CAACC,KAAK,GAAG,CAAClC,IAAI,CAACC,EAAE;MACzB;MAEA,IAAIoC,WAAW,GAAGC,YAAY,CAACL,MAAM,CAAC;MACtC9C,GAAG,CAACe,IAAI,CAAC,CAAC,GAAGmC,WAAW,CAACvB,CAAC,EAAE,CAAC,GAAGuB,WAAW,CAACtB,CAAC,CAAC;IAChD;EACF;EAEA,IAAIL,cAAc,CAACC,UAAU,CAACO,EAAE,EAAE,OAAOR,cAAc,CAACC,UAAU,CAACO,EAAE;EACrER,cAAc,CAACJ,YAAY,CAAC,IAAI,EAAE,IAAItE,KAAK,CAACmF,sBAAsB,CAAChC,GAAG,EAAE,CAAC,CAAC,CAAC;EAC3EuB,cAAc,CAACC,UAAU,CAACO,EAAE,CAACE,WAAW,GAAG,IAAI;EAC/C,OAAOV,cAAc;AACvB;AAEA,SAASgB,eAAeA,CAACd,QAAQ,EAAE;EACjC,IAAIwB,CAAC,GAAGpC,IAAI,CAACuC,IAAI,CAAC3B,QAAQ,CAACE,CAAC,GAAGF,QAAQ,CAACE,CAAC,GAAGF,QAAQ,CAACI,CAAC,GAAGJ,QAAQ,CAACI,CAAC,GAAGJ,QAAQ,CAACG,CAAC,GAAGH,QAAQ,CAACG,CAAC,CAAC;EAC9F,OAAO;IACLqB,CAAC,EAAEA,CAAC;IACJ9C,GAAG,EAAEU,IAAI,CAACwC,IAAI,CAAC5B,QAAQ,CAACG,CAAC,GAAGqB,CAAC,CAAC;IAC9BF,KAAK,EAAElC,IAAI,CAACiB,KAAK,CAACL,QAAQ,CAACI,CAAC,EAAEJ,QAAQ,CAACE,CAAC;EAC1C,CAAC;AACH;AAEA,SAASwB,YAAYA,CAACG,UAAU,EAAE;EAChC,OAAO;IACL1B,CAAC,EAAE0B,UAAU,CAACnD,GAAG,GAAGU,IAAI,CAACC,EAAE;IAC3Ba,CAAC,EAAE,CAAC2B,UAAU,CAACP,KAAK,GAAGlC,IAAI,CAACC,EAAE,KAAK,CAAC,GAAGD,IAAI,CAACC,EAAE;EAChD,CAAC;AACH,CAAC,CAAC;AACF;;AAGA,SAASyC,UAAUA,CAAChC,cAAc,EAAE;EAClCA,cAAc,CAACiC,kBAAkB,CAAC,CAAC;EACnC,IAAIC,QAAQ,GAAGlC,cAAc,CAACmC,WAAW,CAACC,OAAO,CAAC,IAAI9G,KAAK,CAACuF,OAAO,CAAC,CAAC,CAAC;EACtE,IAAIwB,OAAO,GAAG/C,IAAI,CAACgD,GAAG,CAACJ,QAAQ,CAAC9B,CAAC,EAAE8B,QAAQ,CAAC7B,CAAC,EAAE6B,QAAQ,CAAC5B,CAAC,CAAC;EAC1D,IAAIiC,WAAW,GAAG,IAAIjH,KAAK,CAACkH,WAAW,CAACH,OAAO,EAAEA,OAAO,EAAEA,OAAO,CAAC;EAClE,IAAII,IAAI,GAAG,IAAInH,KAAK,CAACoH,IAAI,CAACH,WAAW,CAAC;EACtCE,IAAI,CAACE,QAAQ,CAACC,GAAG,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC;EAC1BH,IAAI,CAACI,iBAAiB,CAAC,IAAI,EAAE,KAAK,CAAC;EACnC,IAAIC,eAAe,GAAGL,IAAI,CAACM,MAAM,CAACC,KAAK,CAAC,CAAC,CAACC,MAAM,CAAC,CAAC;EAClD,IAAIC,MAAM,GAAG,IAAI5H,KAAK,CAAC6H,IAAI,CAAC,IAAI7H,KAAK,CAACuF,OAAO,CAAC,CAACwB,OAAO,GAAG,CAAC,EAAE,CAACA,OAAO,GAAG,CAAC,EAAE,CAACA,OAAO,GAAG,CAAC,CAAC,EAAE,IAAI/G,KAAK,CAACuF,OAAO,CAACwB,OAAO,GAAG,CAAC,EAAEA,OAAO,GAAG,CAAC,EAAEA,OAAO,GAAG,CAAC,CAAC,CAAC;EAElJe,WAAW,CAACpD,cAAc,EAAE8C,eAAe,EAAEI,MAAM,EAAEb,OAAO,CAAC;EAE7DrC,cAAc,CAACC,UAAU,CAACO,EAAE,CAACE,WAAW,GAAG,IAAI;EAC/C,OAAOV,cAAc;AACvB;AAEA,SAASoD,WAAWA,CAACC,IAAI,EAAEP,eAAe,EAAEQ,IAAI,EAAEC,aAAa,EAAE;EAC/D,IAAIC,MAAM,GAAG,EAAE;EACfA,MAAM,CAAC5F,MAAM,GAAG,CAAC,GAAGyF,IAAI,CAACpD,UAAU,CAACC,QAAQ,CAACC,KAAK,CAACvC,MAAM,GAAG,CAAC,CAAC,CAAC;EAC/D;;EAEA,IAAI6F,OAAO,GAAG,SAASA,OAAOA,CAACC,EAAE,EAAEC,EAAE,EAAEC,EAAE,EAAE;IACzC;IACAF,EAAE,CAACG,YAAY,CAACf,eAAe,CAAC;IAChCa,EAAE,CAACE,YAAY,CAACf,eAAe,CAAC;IAChCc,EAAE,CAACC,YAAY,CAACf,eAAe,CAAC,CAAC,CAAC;;IAElC,IAAIpE,CAAC,GAAG,IAAIpD,KAAK,CAACuF,OAAO,CAAC,CAAC;IAC3BnC,CAAC,CAACoF,YAAY,CAACH,EAAE,CAACX,KAAK,CAAC,CAAC,CAACe,GAAG,CAACL,EAAE,CAAC,EAAEC,EAAE,CAACX,KAAK,CAAC,CAAC,CAACe,GAAG,CAACH,EAAE,CAAC,CAAC,CAACI,SAAS,CAAC,CAAC;IAClEtF,CAAC,CAAC0B,CAAC,GAAGd,IAAI,CAAC2E,GAAG,CAACvF,CAAC,CAAC0B,CAAC,CAAC;IACnB1B,CAAC,CAAC2B,CAAC,GAAGf,IAAI,CAAC2E,GAAG,CAACvF,CAAC,CAAC2B,CAAC,CAAC;IACnB3B,CAAC,CAAC4B,CAAC,GAAGhB,IAAI,CAAC2E,GAAG,CAACvF,CAAC,CAAC4B,CAAC,CAAC;IACnB,IAAI4D,GAAG,GAAG,IAAI5I,KAAK,CAAC6I,OAAO,CAAC,CAAC;IAC7B,IAAIC,GAAG,GAAG,IAAI9I,KAAK,CAAC6I,OAAO,CAAC,CAAC;IAC7B,IAAIE,GAAG,GAAG,IAAI/I,KAAK,CAAC6I,OAAO,CAAC,CAAC,CAAC,CAAC;;IAE/B,IAAIzF,CAAC,CAAC2B,CAAC,GAAG3B,CAAC,CAAC0B,CAAC,IAAI1B,CAAC,CAAC2B,CAAC,GAAG3B,CAAC,CAAC4B,CAAC,EAAE;MAC1B4D,GAAG,CAAC9D,CAAC,GAAG,CAACsD,EAAE,CAACtD,CAAC,GAAGkD,IAAI,CAAChB,GAAG,CAAClC,CAAC,IAAImD,aAAa;MAC3CW,GAAG,CAAC7D,CAAC,GAAG,CAACiD,IAAI,CAACgB,GAAG,CAAChE,CAAC,GAAGoD,EAAE,CAACpD,CAAC,IAAIiD,aAAa;MAC3Ca,GAAG,CAAChE,CAAC,GAAG,CAACuD,EAAE,CAACvD,CAAC,GAAGkD,IAAI,CAAChB,GAAG,CAAClC,CAAC,IAAImD,aAAa;MAC3Ca,GAAG,CAAC/D,CAAC,GAAG,CAACiD,IAAI,CAACgB,GAAG,CAAChE,CAAC,GAAGqD,EAAE,CAACrD,CAAC,IAAIiD,aAAa;MAC3Cc,GAAG,CAACjE,CAAC,GAAG,CAACwD,EAAE,CAACxD,CAAC,GAAGkD,IAAI,CAAChB,GAAG,CAAClC,CAAC,IAAImD,aAAa;MAC3Cc,GAAG,CAAChE,CAAC,GAAG,CAACiD,IAAI,CAACgB,GAAG,CAAChE,CAAC,GAAGsD,EAAE,CAACtD,CAAC,IAAIiD,aAAa;IAC7C,CAAC,MAAM,IAAI7E,CAAC,CAAC0B,CAAC,GAAG1B,CAAC,CAAC2B,CAAC,IAAI3B,CAAC,CAAC0B,CAAC,GAAG1B,CAAC,CAAC4B,CAAC,EAAE;MACjC4D,GAAG,CAAC9D,CAAC,GAAG,CAACsD,EAAE,CAACpD,CAAC,GAAGgD,IAAI,CAAChB,GAAG,CAAChC,CAAC,IAAIiD,aAAa;MAC3CW,GAAG,CAAC7D,CAAC,GAAG,CAACqD,EAAE,CAACrD,CAAC,GAAGiD,IAAI,CAAChB,GAAG,CAACjC,CAAC,IAAIkD,aAAa;MAC3Ca,GAAG,CAAChE,CAAC,GAAG,CAACuD,EAAE,CAACrD,CAAC,GAAGgD,IAAI,CAAChB,GAAG,CAAChC,CAAC,IAAIiD,aAAa;MAC3Ca,GAAG,CAAC/D,CAAC,GAAG,CAACsD,EAAE,CAACtD,CAAC,GAAGiD,IAAI,CAAChB,GAAG,CAACjC,CAAC,IAAIkD,aAAa;MAC3Cc,GAAG,CAACjE,CAAC,GAAG,CAACwD,EAAE,CAACtD,CAAC,GAAGgD,IAAI,CAAChB,GAAG,CAAChC,CAAC,IAAIiD,aAAa;MAC3Cc,GAAG,CAAChE,CAAC,GAAG,CAACuD,EAAE,CAACvD,CAAC,GAAGiD,IAAI,CAAChB,GAAG,CAACjC,CAAC,IAAIkD,aAAa;IAC7C,CAAC,MAAM,IAAI7E,CAAC,CAAC4B,CAAC,GAAG5B,CAAC,CAAC2B,CAAC,IAAI3B,CAAC,CAAC4B,CAAC,GAAG5B,CAAC,CAAC0B,CAAC,EAAE;MACjC8D,GAAG,CAAC9D,CAAC,GAAG,CAACsD,EAAE,CAACtD,CAAC,GAAGkD,IAAI,CAAChB,GAAG,CAAClC,CAAC,IAAImD,aAAa;MAC3CW,GAAG,CAAC7D,CAAC,GAAG,CAACqD,EAAE,CAACrD,CAAC,GAAGiD,IAAI,CAAChB,GAAG,CAACjC,CAAC,IAAIkD,aAAa;MAC3Ca,GAAG,CAAChE,CAAC,GAAG,CAACuD,EAAE,CAACvD,CAAC,GAAGkD,IAAI,CAAChB,GAAG,CAAClC,CAAC,IAAImD,aAAa;MAC3Ca,GAAG,CAAC/D,CAAC,GAAG,CAACsD,EAAE,CAACtD,CAAC,GAAGiD,IAAI,CAAChB,GAAG,CAACjC,CAAC,IAAIkD,aAAa;MAC3Cc,GAAG,CAACjE,CAAC,GAAG,CAACwD,EAAE,CAACxD,CAAC,GAAGkD,IAAI,CAAChB,GAAG,CAAClC,CAAC,IAAImD,aAAa;MAC3Cc,GAAG,CAAChE,CAAC,GAAG,CAACuD,EAAE,CAACvD,CAAC,GAAGiD,IAAI,CAAChB,GAAG,CAACjC,CAAC,IAAIkD,aAAa;IAC7C;IAEA,OAAO;MACLW,GAAG,EAAEA,GAAG;MACRE,GAAG,EAAEA,GAAG;MACRC,GAAG,EAAEA;IACP,CAAC;EACH,CAAC;EAED,IAAIhB,IAAI,CAACkB,KAAK,EAAE;IACd;IACA,KAAK,IAAIC,EAAE,GAAG,CAAC,EAAEA,EAAE,GAAGnB,IAAI,CAACkB,KAAK,CAACpE,KAAK,CAACvC,MAAM,EAAE4G,EAAE,IAAI,CAAC,EAAE;MACtD,IAAIC,IAAI,GAAGpB,IAAI,CAACkB,KAAK,CAACpE,KAAK,CAACqE,EAAE,CAAC;MAC/B,IAAIE,IAAI,GAAGrB,IAAI,CAACkB,KAAK,CAACpE,KAAK,CAACqE,EAAE,GAAG,CAAC,CAAC;MACnC,IAAIG,IAAI,GAAGtB,IAAI,CAACkB,KAAK,CAACpE,KAAK,CAACqE,EAAE,GAAG,CAAC,CAAC;MACnC,IAAII,GAAG,GAAGvB,IAAI,CAACpD,UAAU,CAACC,QAAQ,CAACC,KAAK,CAAC,CAAC,GAAGsE,IAAI,CAAC;MAClD,IAAII,GAAG,GAAGxB,IAAI,CAACpD,UAAU,CAACC,QAAQ,CAACC,KAAK,CAAC,CAAC,GAAGsE,IAAI,GAAG,CAAC,CAAC;MACtD,IAAIK,GAAG,GAAGzB,IAAI,CAACpD,UAAU,CAACC,QAAQ,CAACC,KAAK,CAAC,CAAC,GAAGsE,IAAI,GAAG,CAAC,CAAC;MACtD,IAAIM,GAAG,GAAG1B,IAAI,CAACpD,UAAU,CAACC,QAAQ,CAACC,KAAK,CAAC,CAAC,GAAGuE,IAAI,CAAC;MAClD,IAAIM,GAAG,GAAG3B,IAAI,CAACpD,UAAU,CAACC,QAAQ,CAACC,KAAK,CAAC,CAAC,GAAGuE,IAAI,GAAG,CAAC,CAAC;MACtD,IAAIO,GAAG,GAAG5B,IAAI,CAACpD,UAAU,CAACC,QAAQ,CAACC,KAAK,CAAC,CAAC,GAAGuE,IAAI,GAAG,CAAC,CAAC;MACtD,IAAIQ,GAAG,GAAG7B,IAAI,CAACpD,UAAU,CAACC,QAAQ,CAACC,KAAK,CAAC,CAAC,GAAGwE,IAAI,CAAC;MAClD,IAAIQ,GAAG,GAAG9B,IAAI,CAACpD,UAAU,CAACC,QAAQ,CAACC,KAAK,CAAC,CAAC,GAAGwE,IAAI,GAAG,CAAC,CAAC;MACtD,IAAIS,GAAG,GAAG/B,IAAI,CAACpD,UAAU,CAACC,QAAQ,CAACC,KAAK,CAAC,CAAC,GAAGwE,IAAI,GAAG,CAAC,CAAC;MACtD,IAAIjB,EAAE,GAAG,IAAIpI,KAAK,CAACuF,OAAO,CAAC+D,GAAG,EAAEC,GAAG,EAAEC,GAAG,CAAC;MACzC,IAAInB,EAAE,GAAG,IAAIrI,KAAK,CAACuF,OAAO,CAACkE,GAAG,EAAEC,GAAG,EAAEC,GAAG,CAAC;MACzC,IAAIrB,EAAE,GAAG,IAAItI,KAAK,CAACuF,OAAO,CAACqE,GAAG,EAAEC,GAAG,EAAEC,GAAG,CAAC;MACzC,IAAI3G,GAAG,GAAGgF,OAAO,CAACC,EAAE,EAAEC,EAAE,EAAEC,EAAE,CAAC;MAC7BJ,MAAM,CAAC,CAAC,GAAGiB,IAAI,CAAC,GAAGhG,GAAG,CAACyF,GAAG,CAAC9D,CAAC;MAC5BoD,MAAM,CAAC,CAAC,GAAGiB,IAAI,GAAG,CAAC,CAAC,GAAGhG,GAAG,CAACyF,GAAG,CAAC7D,CAAC;MAChCmD,MAAM,CAAC,CAAC,GAAGkB,IAAI,CAAC,GAAGjG,GAAG,CAAC2F,GAAG,CAAChE,CAAC;MAC5BoD,MAAM,CAAC,CAAC,GAAGkB,IAAI,GAAG,CAAC,CAAC,GAAGjG,GAAG,CAAC2F,GAAG,CAAC/D,CAAC;MAChCmD,MAAM,CAAC,CAAC,GAAGmB,IAAI,CAAC,GAAGlG,GAAG,CAAC4F,GAAG,CAACjE,CAAC;MAC5BoD,MAAM,CAAC,CAAC,GAAGmB,IAAI,GAAG,CAAC,CAAC,GAAGlG,GAAG,CAAC4F,GAAG,CAAChE,CAAC;IAClC;EACF,CAAC,MAAM;IACL,KAAK,IAAIgF,GAAG,GAAG,CAAC,EAAEA,GAAG,GAAGhC,IAAI,CAACpD,UAAU,CAACC,QAAQ,CAACC,KAAK,CAACvC,MAAM,EAAEyH,GAAG,IAAI,CAAC,EAAE;MACvE,IAAIC,GAAG,GAAGjC,IAAI,CAACpD,UAAU,CAACC,QAAQ,CAACC,KAAK,CAACkF,GAAG,CAAC;MAC7C,IAAIE,GAAG,GAAGlC,IAAI,CAACpD,UAAU,CAACC,QAAQ,CAACC,KAAK,CAACkF,GAAG,GAAG,CAAC,CAAC;MACjD,IAAIG,GAAG,GAAGnC,IAAI,CAACpD,UAAU,CAACC,QAAQ,CAACC,KAAK,CAACkF,GAAG,GAAG,CAAC,CAAC;MACjD,IAAII,IAAI,GAAGpC,IAAI,CAACpD,UAAU,CAACC,QAAQ,CAACC,KAAK,CAACkF,GAAG,GAAG,CAAC,CAAC;MAClD,IAAIK,IAAI,GAAGrC,IAAI,CAACpD,UAAU,CAACC,QAAQ,CAACC,KAAK,CAACkF,GAAG,GAAG,CAAC,CAAC;MAClD,IAAIM,IAAI,GAAGtC,IAAI,CAACpD,UAAU,CAACC,QAAQ,CAACC,KAAK,CAACkF,GAAG,GAAG,CAAC,CAAC;MAClD,IAAIO,IAAI,GAAGvC,IAAI,CAACpD,UAAU,CAACC,QAAQ,CAACC,KAAK,CAACkF,GAAG,GAAG,CAAC,CAAC;MAClD,IAAIQ,IAAI,GAAGxC,IAAI,CAACpD,UAAU,CAACC,QAAQ,CAACC,KAAK,CAACkF,GAAG,GAAG,CAAC,CAAC;MAClD,IAAIS,IAAI,GAAGzC,IAAI,CAACpD,UAAU,CAACC,QAAQ,CAACC,KAAK,CAACkF,GAAG,GAAG,CAAC,CAAC;MAElD,IAAIU,EAAE,GAAG,IAAIzK,KAAK,CAACuF,OAAO,CAACyE,GAAG,EAAEC,GAAG,EAAEC,GAAG,CAAC;MAEzC,IAAIQ,GAAG,GAAG,IAAI1K,KAAK,CAACuF,OAAO,CAAC4E,IAAI,EAAEC,IAAI,EAAEC,IAAI,CAAC;MAE7C,IAAIM,GAAG,GAAG,IAAI3K,KAAK,CAACuF,OAAO,CAAC+E,IAAI,EAAEC,IAAI,EAAEC,IAAI,CAAC;MAE7C,IAAII,IAAI,GAAGzC,OAAO,CAACsC,EAAE,EAAEC,GAAG,EAAEC,GAAG,CAAC;MAEhC,IAAIE,IAAI,GAAGd,GAAG,GAAG,CAAC;MAElB,IAAIe,KAAK,GAAGD,IAAI,GAAG,CAAC;MAEpB,IAAIE,KAAK,GAAGF,IAAI,GAAG,CAAC;MAEpB3C,MAAM,CAAC,CAAC,GAAG2C,IAAI,CAAC,GAAGD,IAAI,CAAChC,GAAG,CAAC9D,CAAC;MAC7BoD,MAAM,CAAC,CAAC,GAAG2C,IAAI,GAAG,CAAC,CAAC,GAAGD,IAAI,CAAChC,GAAG,CAAC7D,CAAC;MACjCmD,MAAM,CAAC,CAAC,GAAG4C,KAAK,CAAC,GAAGF,IAAI,CAAC9B,GAAG,CAAChE,CAAC;MAC9BoD,MAAM,CAAC,CAAC,GAAG4C,KAAK,GAAG,CAAC,CAAC,GAAGF,IAAI,CAAC9B,GAAG,CAAC/D,CAAC;MAClCmD,MAAM,CAAC,CAAC,GAAG6C,KAAK,CAAC,GAAGH,IAAI,CAAC7B,GAAG,CAACjE,CAAC;MAC9BoD,MAAM,CAAC,CAAC,GAAG6C,KAAK,GAAG,CAAC,CAAC,GAAGH,IAAI,CAAC7B,GAAG,CAAChE,CAAC;IACpC;EACF;EAEA,IAAIgD,IAAI,CAACpD,UAAU,CAACO,EAAE,EAAE,OAAO6C,IAAI,CAACpD,UAAU,CAACO,EAAE;EACjD6C,IAAI,CAACzD,YAAY,CAAC,IAAI,EAAE,IAAItE,KAAK,CAACmF,sBAAsB,CAAC+C,MAAM,EAAE,CAAC,CAAC,CAAC;AACtE;AAEA,IAAI8C,QAAQ,GAAG,aAAa1K,MAAM,CAAC2K,MAAM,CAAC;EACxCjK,SAAS,EAAE,IAAI;EACfiB,oBAAoB,EAAEA,oBAAoB;EAC1CwC,kBAAkB,EAAEA,kBAAkB;EACtCY,aAAa,EAAEA,aAAa;EAC5BqB,UAAU,EAAEA;AACd,CAAC,CAAC;AAEF,SAASzE,oBAAoB,IAAIiJ,CAAC,EAAEzG,kBAAkB,IAAI3E,CAAC,EAAEuF,aAAa,IAAI8F,CAAC,EAAEzE,UAAU,IAAI0E,CAAC,EAAEJ,QAAQ,IAAIK,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}