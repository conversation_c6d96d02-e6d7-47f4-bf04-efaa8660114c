{"ast": null, "code": "import { Object3D } from \"three\";\nimport { XRHandPrimitiveModel } from \"./XRHandPrimitiveModel.js\";\nimport { XRHandMeshModel } from \"./XRHandMeshModel.js\";\nclass XRHandModel extends Object3D {\n  constructor(controller) {\n    super();\n    this.controller = controller;\n    this.motionController = null;\n    this.envMap = null;\n    this.mesh = null;\n  }\n  updateMatrixWorld(force) {\n    super.updateMatrixWorld(force);\n    if (this.motionController) {\n      this.motionController.updateMesh();\n    }\n  }\n}\nclass XRHandModelFactory {\n  constructor() {\n    this.path = null;\n  }\n  setPath(path) {\n    this.path = path;\n    return this;\n  }\n  createHandModel(controller, profile) {\n    const handModel = new XRHandModel(controller);\n    controller.addEventListener(\"connected\", event => {\n      const xrInputSource = event.data;\n      if (xrInputSource.hand && !handModel.motionController) {\n        handModel.xrInputSource = xrInputSource;\n        if (profile === void 0 || profile === \"spheres\") {\n          handModel.motionController = new XRHandPrimitiveModel(handModel, controller, this.path, xrInputSource.handedness, {\n            primitive: \"sphere\"\n          });\n        } else if (profile === \"boxes\") {\n          handModel.motionController = new XRHandPrimitiveModel(handModel, controller, this.path, xrInputSource.handedness, {\n            primitive: \"box\"\n          });\n        } else if (profile === \"mesh\") {\n          handModel.motionController = new XRHandMeshModel(handModel, controller, this.path, xrInputSource.handedness);\n        }\n      }\n    });\n    controller.addEventListener(\"disconnected\", () => {});\n    return handModel;\n  }\n}\nexport { XRHandModelFactory };", "map": {"version": 3, "names": ["XRHandModel", "Object3D", "constructor", "controller", "motionController", "envMap", "mesh", "updateMatrixWorld", "force", "updateMesh", "XRHandModelFactory", "path", "set<PERSON>ath", "createHandModel", "profile", "handModel", "addEventListener", "event", "xrInputSource", "data", "hand", "XRHandPrimitiveModel", "handedness", "primitive", "XRHandMeshModel"], "sources": ["C:\\Users\\<USER>\\Documents\\Apps_Creations\\SiahMan_Racing4Speed_WebGame\\siah-man-racing\\node_modules\\src\\webxr\\XRHandModelFactory.js"], "sourcesContent": ["import { Object3D } from 'three'\nimport { XRHandPrimitiveModel } from './XRHandPrimitiveModel'\nimport { XRHandMeshModel } from './XRHandMeshModel'\n\nclass XRHandModel extends Object3D {\n  constructor(controller) {\n    super()\n\n    this.controller = controller\n    this.motionController = null\n    this.envMap = null\n\n    this.mesh = null\n  }\n\n  updateMatrixWorld(force) {\n    super.updateMatrixWorld(force)\n\n    if (this.motionController) {\n      this.motionController.updateMesh()\n    }\n  }\n}\n\nclass XRHandModelFactory {\n  constructor() {\n    this.path = null\n  }\n\n  setPath(path) {\n    this.path = path\n\n    return this\n  }\n\n  createHandModel(controller, profile) {\n    const handModel = new XRHandModel(controller)\n\n    controller.addEventListener('connected', (event) => {\n      const xrInputSource = event.data\n\n      if (xrInputSource.hand && !handModel.motionController) {\n        handModel.xrInputSource = xrInputSource\n\n        // @todo Detect profile if not provided\n        if (profile === undefined || profile === 'spheres') {\n          handModel.motionController = new XRHandPrimitiveModel(\n            handModel,\n            controller,\n            this.path,\n            xrInputSource.handedness,\n            { primitive: 'sphere' },\n          )\n        } else if (profile === 'boxes') {\n          handModel.motionController = new XRHandPrimitiveModel(\n            handModel,\n            controller,\n            this.path,\n            xrInputSource.handedness,\n            { primitive: 'box' },\n          )\n        } else if (profile === 'mesh') {\n          handModel.motionController = new XRHandMeshModel(handModel, controller, this.path, xrInputSource.handedness)\n        }\n      }\n    })\n\n    controller.addEventListener('disconnected', () => {\n      // handModel.motionController = null;\n      // handModel.remove( scene );\n      // scene = null;\n    })\n\n    return handModel\n  }\n}\n\nexport { XRHandModelFactory }\n"], "mappings": ";;;AAIA,MAAMA,WAAA,SAAoBC,QAAA,CAAS;EACjCC,YAAYC,UAAA,EAAY;IACtB,MAAO;IAEP,KAAKA,UAAA,GAAaA,UAAA;IAClB,KAAKC,gBAAA,GAAmB;IACxB,KAAKC,MAAA,GAAS;IAEd,KAAKC,IAAA,GAAO;EACb;EAEDC,kBAAkBC,KAAA,EAAO;IACvB,MAAMD,iBAAA,CAAkBC,KAAK;IAE7B,IAAI,KAAKJ,gBAAA,EAAkB;MACzB,KAAKA,gBAAA,CAAiBK,UAAA,CAAY;IACnC;EACF;AACH;AAEA,MAAMC,kBAAA,CAAmB;EACvBR,YAAA,EAAc;IACZ,KAAKS,IAAA,GAAO;EACb;EAEDC,QAAQD,IAAA,EAAM;IACZ,KAAKA,IAAA,GAAOA,IAAA;IAEZ,OAAO;EACR;EAEDE,gBAAgBV,UAAA,EAAYW,OAAA,EAAS;IACnC,MAAMC,SAAA,GAAY,IAAIf,WAAA,CAAYG,UAAU;IAE5CA,UAAA,CAAWa,gBAAA,CAAiB,aAAcC,KAAA,IAAU;MAClD,MAAMC,aAAA,GAAgBD,KAAA,CAAME,IAAA;MAE5B,IAAID,aAAA,CAAcE,IAAA,IAAQ,CAACL,SAAA,CAAUX,gBAAA,EAAkB;QACrDW,SAAA,CAAUG,aAAA,GAAgBA,aAAA;QAG1B,IAAIJ,OAAA,KAAY,UAAaA,OAAA,KAAY,WAAW;UAClDC,SAAA,CAAUX,gBAAA,GAAmB,IAAIiB,oBAAA,CAC/BN,SAAA,EACAZ,UAAA,EACA,KAAKQ,IAAA,EACLO,aAAA,CAAcI,UAAA,EACd;YAAEC,SAAA,EAAW;UAAU,CACxB;QACX,WAAmBT,OAAA,KAAY,SAAS;UAC9BC,SAAA,CAAUX,gBAAA,GAAmB,IAAIiB,oBAAA,CAC/BN,SAAA,EACAZ,UAAA,EACA,KAAKQ,IAAA,EACLO,aAAA,CAAcI,UAAA,EACd;YAAEC,SAAA,EAAW;UAAO,CACrB;QACX,WAAmBT,OAAA,KAAY,QAAQ;UAC7BC,SAAA,CAAUX,gBAAA,GAAmB,IAAIoB,eAAA,CAAgBT,SAAA,EAAWZ,UAAA,EAAY,KAAKQ,IAAA,EAAMO,aAAA,CAAcI,UAAU;QAC5G;MACF;IACP,CAAK;IAEDnB,UAAA,CAAWa,gBAAA,CAAiB,gBAAgB,MAAM,CAItD,CAAK;IAED,OAAOD,SAAA;EACR;AACH", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}