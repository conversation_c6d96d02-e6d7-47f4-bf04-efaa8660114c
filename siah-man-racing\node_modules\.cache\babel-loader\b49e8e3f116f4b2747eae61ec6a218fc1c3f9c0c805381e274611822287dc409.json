{"ast": null, "code": "var _AudioSystem;\n// Dynamic Music System for Super Siah Man Racing\nimport { How<PERSON>, How<PERSON> } from 'howler';\nexport class AudioSystem {\n  constructor() {\n    this.musicTracks = void 0;\n    this.soundEffects = void 0;\n    this.voiceDialogues = void 0;\n    this.currentMusic = void 0;\n    this.nextMusic = void 0;\n    this.currentVoice = void 0;\n    this.voiceQueue = void 0;\n    this.fadeDuration = void 0;\n    this.musicVolume = void 0;\n    this.sfxVolume = void 0;\n    this.voiceVolume = void 0;\n    this.isMusicMuted = void 0;\n    this.isSfxMuted = void 0;\n    this.isVoiceMuted = void 0;\n    this.raceIntensity = void 0;\n    // 0-1 value representing race intensity\n    this.customizationMood = void 0;\n    // Mood based on vehicle customization\n    this.storyChapter = void 0;\n    // Current story chapter\n    // Audio processing settings\n    this.audioContext = null;\n    this.masterCompressor = null;\n    this.voiceCompressor = null;\n    this.musicEQ = [];\n    this.voiceEQ = [];\n    this.sfxEQ = [];\n    this.musicTracks = new Map();\n    this.soundEffects = new Map();\n    this.voiceDialogues = new Map();\n    this.currentMusic = null;\n    this.nextMusic = null;\n    this.currentVoice = null;\n    this.voiceQueue = [];\n    this.fadeDuration = 2000; // 2 seconds fade duration\n    this.musicVolume = 0.7;\n    this.sfxVolume = 0.8;\n    this.voiceVolume = 0.9;\n    this.isMusicMuted = false;\n    this.isSfxMuted = false;\n    this.isVoiceMuted = false;\n    this.raceIntensity = 0;\n    this.customizationMood = 'sporty';\n    this.storyChapter = 1;\n    this.initializeAudioProcessing();\n\n    // Set global Howler settings\n    Howler.autoUnlock = true;\n    Howler.html5PoolSize = 10;\n  }\n  static getInstance() {\n    if (!AudioSystem.instance) {\n      AudioSystem.instance = new AudioSystem();\n    }\n    return AudioSystem.instance;\n  }\n\n  // Load music tracks\n  loadMusicTrack(trackInfo) {\n    return new Promise((resolve, reject) => {\n      try {\n        const howl = new Howl({\n          src: [trackInfo.src],\n          loop: trackInfo.loop,\n          volume: 0,\n          // Start at 0 volume for crossfading\n          html5: true,\n          onload: () => {\n            this.musicTracks.set(trackInfo.id, {\n              howl,\n              info: trackInfo\n            });\n            resolve();\n          },\n          onloaderror: (id, error) => {\n            reject(`Error loading music track ${trackInfo.name}: ${error}`);\n          }\n        });\n      } catch (error) {\n        reject(`Error creating Howl for ${trackInfo.name}: ${error}`);\n      }\n    });\n  }\n\n  // Load sound effect\n  loadSoundEffect(effectInfo) {\n    return new Promise((resolve, reject) => {\n      try {\n        const howl = new Howl({\n          src: [effectInfo.src],\n          loop: effectInfo.loop,\n          volume: effectInfo.volume * this.sfxVolume,\n          html5: false,\n          // WebAudio for sound effects for better performance\n          onload: () => {\n            this.soundEffects.set(effectInfo.id, {\n              howl,\n              info: effectInfo\n            });\n            resolve();\n          },\n          onloaderror: (id, error) => {\n            reject(`Error loading sound effect ${effectInfo.name}: ${error}`);\n          }\n        });\n      } catch (error) {\n        reject(`Error creating Howl for ${effectInfo.name}: ${error}`);\n      }\n    });\n  }\n\n  // Play music with crossfade\n  playMusic(trackId) {\n    if (!this.musicTracks.has(trackId)) {\n      console.error(`Music track ${trackId} not found`);\n      return;\n    }\n    if (this.currentMusic === trackId) {\n      return; // Already playing this track\n    }\n    this.nextMusic = trackId;\n\n    // If no music is currently playing, start immediately\n    if (this.currentMusic === null) {\n      this.startMusic(trackId);\n      return;\n    }\n\n    // Otherwise crossfade\n    this.crossfade(this.currentMusic, trackId);\n  }\n\n  // Start playing music\n  startMusic(trackId) {\n    const track = this.musicTracks.get(trackId);\n    if (!track) return;\n    const {\n      howl,\n      info\n    } = track;\n\n    // Set volume based on mute state\n    const targetVolume = this.isMusicMuted ? 0 : info.volume * this.musicVolume;\n    howl.volume(targetVolume);\n    howl.play();\n    this.currentMusic = trackId;\n    this.nextMusic = null;\n  }\n\n  // Crossfade between tracks\n  crossfade(fromTrackId, toTrackId) {\n    const fromTrack = this.musicTracks.get(fromTrackId);\n    const toTrack = this.musicTracks.get(toTrackId);\n    if (!fromTrack || !toTrack) return;\n    const {\n      howl: fromHowl\n    } = fromTrack;\n    const {\n      howl: toHowl,\n      info: toInfo\n    } = toTrack; // 'toInfo' might be used later if track properties influence crossfade\n\n    // Calculate target volumes\n    const fromTargetVolume = 0;\n    const toTargetVolume = this.isMusicMuted ? 0 : toInfo.volume * this.musicVolume;\n\n    // Start the new track\n    toHowl.volume(0);\n    toHowl.play();\n\n    // Fade out the current track\n    fromHowl.fade(fromHowl.volume(), fromTargetVolume, this.fadeDuration);\n\n    // Fade in the new track\n    toHowl.fade(0, toTargetVolume, this.fadeDuration);\n\n    // Update current track after fade duration\n    setTimeout(() => {\n      fromHowl.stop();\n      this.currentMusic = toTrackId;\n      this.nextMusic = null;\n    }, this.fadeDuration);\n  }\n\n  // Play sound effect\n  playSoundEffect(effectId) {\n    if (this.isSfxMuted) return -1;\n    const effect = this.soundEffects.get(effectId);\n    if (!effect) {\n      console.error(`Sound effect ${effectId} not found`);\n      return -1;\n    }\n    const {\n      howl\n    } = effect;\n    return howl.play();\n  }\n\n  // Stop sound effect\n  stopSoundEffect(effectId, id) {\n    const effect = this.soundEffects.get(effectId);\n    if (!effect) return;\n    if (id !== undefined) {\n      effect.howl.stop(id);\n    } else {\n      effect.howl.stop();\n    }\n  }\n\n  // Set music volume\n  setMusicVolume(volume) {\n    this.musicVolume = Math.max(0, Math.min(1, volume));\n    if (this.currentMusic && !this.isMusicMuted) {\n      const track = this.musicTracks.get(this.currentMusic);\n      if (track) {\n        track.howl.volume(track.info.volume * this.musicVolume);\n      }\n    }\n  }\n\n  // Set sound effects volume\n  setSfxVolume(volume) {\n    this.sfxVolume = Math.max(0, Math.min(1, volume));\n    if (!this.isSfxMuted) {\n      this.soundEffects.forEach(({\n        howl,\n        info\n      }) => {\n        howl.volume(info.volume * this.sfxVolume);\n      });\n    }\n  }\n\n  // Mute/unmute music\n  toggleMuteMusic() {\n    this.isMusicMuted = !this.isMusicMuted;\n    if (this.currentMusic) {\n      const track = this.musicTracks.get(this.currentMusic);\n      if (track) {\n        track.howl.volume(this.isMusicMuted ? 0 : track.info.volume * this.musicVolume);\n      }\n    }\n    return this.isMusicMuted;\n  }\n\n  // Mute/unmute sound effects\n  toggleMuteSfx() {\n    this.isSfxMuted = !this.isSfxMuted;\n    this.soundEffects.forEach(({\n      howl,\n      info\n    }) => {\n      howl.volume(this.isSfxMuted ? 0 : info.volume * this.sfxVolume);\n    });\n    return this.isSfxMuted;\n  }\n\n  // Set race intensity (0-1) for adaptive music\n  setRaceIntensity(intensity) {\n    this.raceIntensity = Math.max(0, Math.min(1, intensity));\n    this.updateAdaptiveMusic();\n  }\n\n  // Set customization mood for adaptive music\n  setCustomizationMood(mood) {\n    this.customizationMood = mood;\n    this.updateAdaptiveMusic();\n  }\n\n  // Set story chapter for adaptive music\n  setStoryChapter(chapter) {\n    this.storyChapter = chapter;\n    this.updateAdaptiveMusic();\n  }\n\n  // Update adaptive music based on current game state\n  updateAdaptiveMusic() {\n    // Get current game screen from the current music track\n    if (!this.currentMusic) return;\n    const currentTrack = this.musicTracks.get(this.currentMusic);\n    if (!currentTrack) return;\n    const category = currentTrack.info.category;\n\n    // Select appropriate music based on current state\n    let targetTrackId = null;\n    switch (category) {\n      case 'race':\n        // Select race music based on intensity\n        if (this.raceIntensity < 0.3) {\n          targetTrackId = this.findTrackByIntensity('race', 'low');\n        } else if (this.raceIntensity < 0.7) {\n          targetTrackId = this.findTrackByIntensity('race', 'medium');\n        } else {\n          targetTrackId = this.findTrackByIntensity('race', 'high');\n        }\n        break;\n      case 'lab':\n        // Select lab music based on customization mood\n        targetTrackId = this.findTrackByMood(this.customizationMood);\n        break;\n      case 'story':\n        // Select story music based on chapter\n        targetTrackId = this.findTrackByChapter(this.storyChapter);\n        break;\n    }\n\n    // Play the selected track if different from current\n    if (targetTrackId && targetTrackId !== this.currentMusic && targetTrackId !== this.nextMusic) {\n      this.playMusic(targetTrackId);\n    }\n  }\n\n  // Find a track by intensity and category\n  findTrackByIntensity(category, intensity) {\n    for (const [id, {\n      info\n    }] of this.musicTracks.entries()) {\n      if (info.category === category && info.intensity === intensity) {\n        return id;\n      }\n    }\n    return null;\n  }\n\n  // Find a track by customization mood\n  findTrackByMood(mood) {\n    // This would require additional metadata on tracks\n    // For now, we'll use a simple mapping\n    const moodToIntensity = {\n      'sporty': 'medium',\n      'elegant': 'low',\n      'aggressive': 'high',\n      'mystical': 'medium'\n    };\n    return this.findTrackByIntensity('lab', moodToIntensity[mood]);\n  }\n\n  // Find a track by story chapter\n  findTrackByChapter(chapter) {\n    // This would require additional metadata on tracks\n    // For now, we'll use a simple approach based on intensity\n    const chapterToIntensity = {\n      1: 'low',\n      2: 'medium',\n      3: 'high',\n      4: 'high',\n      5: 'high'\n    };\n    const intensity = chapterToIntensity[chapter] || 'medium';\n    return this.findTrackByIntensity('story', intensity);\n  }\n\n  // Stop all audio\n  stopAll() {\n    this.musicTracks.forEach(({\n      howl\n    }) => {\n      howl.stop();\n      howl.unload(); // Unload each music track\n    });\n    this.soundEffects.forEach(({\n      howl\n    }) => {\n      howl.stop();\n      howl.unload(); // Unload each sound effect\n    });\n    Howler.stop(); // Global stop as a final measure\n    this.currentMusic = null;\n    this.nextMusic = null;\n  }\n\n  // Clean up resources\n  dispose() {\n    this.stopAll(); // stopAll now also unloads\n    this.musicTracks.clear();\n    this.soundEffects.clear();\n    // Howler.unload(); // Consider if a global unload is needed here, but stopAll is quite thorough.\n  }\n}\n_AudioSystem = AudioSystem;\nAudioSystem.instance = void 0;", "map": {"version": 3, "names": ["Howl", "Howler", "AudioSystem", "constructor", "musicTracks", "soundEffects", "voiceDialogues", "currentMusic", "nextMusic", "currentVoice", "voiceQueue", "fadeDuration", "musicVolume", "sfxVolume", "voiceVolume", "isMusicMuted", "isSfxMuted", "isVoiceMuted", "raceIntensity", "customizationMood", "story<PERSON><PERSON><PERSON>er", "audioContext", "masterCompressor", "voiceCompressor", "musicEQ", "voiceEQ", "sfxEQ", "Map", "initializeAudioProcessing", "autoUnlock", "html5PoolSize", "getInstance", "instance", "loadMusicTrack", "trackInfo", "Promise", "resolve", "reject", "howl", "src", "loop", "volume", "html5", "onload", "set", "id", "info", "onloaderror", "error", "name", "loadSoundEffect", "effectInfo", "playMusic", "trackId", "has", "console", "startMusic", "crossfade", "track", "get", "targetVolume", "play", "fromTrackId", "toTrackId", "fromTrack", "toTrack", "fromHowl", "toHowl", "toInfo", "fromTargetVolume", "toTargetVolume", "fade", "setTimeout", "stop", "playSoundEffect", "effectId", "effect", "stopSoundEffect", "undefined", "setMusicVolume", "Math", "max", "min", "setSfxVolume", "for<PERSON>ach", "toggleMuteMusic", "toggleMuteSfx", "setRaceIntensity", "intensity", "updateAdaptiveMusic", "setCustomizationMood", "mood", "setStoryChapter", "chapter", "currentTrack", "category", "targetTrackId", "findTrackByIntensity", "findTrackByMood", "findTrackByChapter", "entries", "moodToIntensity", "chapterToIntensity", "stopAll", "unload", "dispose", "clear", "_AudioSystem"], "sources": ["C:/Users/<USER>/Documents/Apps_Creations/SiahMan_Racing4Speed_WebGame/siah-man-racing/src/game/AudioSystem.ts"], "sourcesContent": ["// Dynamic Music System for Super Siah Man Racing\nimport { Howl, Howler } from 'howler';\n\nexport interface TrackInfo {\n  id: string;\n  name: string;\n  src: string;\n  intensity: 'low' | 'medium' | 'high';\n  loop: boolean;\n  volume: number;\n  category: 'menu' | 'race' | 'lab' | 'story' | 'customization';\n}\n\nexport interface SoundEffect {\n  id: string;\n  name: string;\n  src: string;\n  volume: number;\n  loop: boolean;\n}\n\nexport interface VoiceDialogue {\n  id: string;\n  character: string;\n  text: string;\n  audioSrc: string;\n  duration?: number;\n  priority: 'low' | 'medium' | 'high';\n}\n\nexport class AudioSystem {\n  private static instance: AudioSystem;\n  private musicTracks: Map<string, { howl: Howl, info: TrackInfo }>;\n  private soundEffects: Map<string, { howl: Howl, info: SoundEffect }>;\n  private voiceDialogues: Map<string, { howl: Howl, info: VoiceDialogue }>;\n  private currentMusic: string | null;\n  private nextMusic: string | null;\n  private currentVoice: string | null;\n  private voiceQueue: VoiceDialogue[];\n  private fadeDuration: number;\n  private musicVolume: number;\n  private sfxVolume: number;\n  private voiceVolume: number;\n  private isMusicMuted: boolean;\n  private isSfxMuted: boolean;\n  private isVoiceMuted: boolean;\n  private raceIntensity: number; // 0-1 value representing race intensity\n  private customizationMood: 'sporty' | 'elegant' | 'aggressive' | 'mystical'; // Mood based on vehicle customization\n  private storyChapter: number; // Current story chapter\n\n  // Audio processing settings\n  private audioContext: AudioContext | null = null;\n  private masterCompressor: DynamicsCompressorNode | null = null;\n  private voiceCompressor: DynamicsCompressorNode | null = null;\n  private musicEQ: BiquadFilterNode[] = [];\n  private voiceEQ: BiquadFilterNode[] = [];\n  private sfxEQ: BiquadFilterNode[] = [];\n  \n  private constructor() {\n    this.musicTracks = new Map();\n    this.soundEffects = new Map();\n    this.voiceDialogues = new Map();\n    this.currentMusic = null;\n    this.nextMusic = null;\n    this.currentVoice = null;\n    this.voiceQueue = [];\n    this.fadeDuration = 2000; // 2 seconds fade duration\n    this.musicVolume = 0.7;\n    this.sfxVolume = 0.8;\n    this.voiceVolume = 0.9;\n    this.isMusicMuted = false;\n    this.isSfxMuted = false;\n    this.isVoiceMuted = false;\n    this.raceIntensity = 0;\n    this.customizationMood = 'sporty';\n    this.storyChapter = 1;\n\n    this.initializeAudioProcessing();\n    \n    // Set global Howler settings\n    Howler.autoUnlock = true;\n    Howler.html5PoolSize = 10;\n  }\n  \n  public static getInstance(): AudioSystem {\n    if (!AudioSystem.instance) {\n      AudioSystem.instance = new AudioSystem();\n    }\n    return AudioSystem.instance;\n  }\n  \n  // Load music tracks\n  public loadMusicTrack(trackInfo: TrackInfo): Promise<void> {\n    return new Promise((resolve, reject) => {\n      try {\n        const howl = new Howl({\n          src: [trackInfo.src],\n          loop: trackInfo.loop,\n          volume: 0, // Start at 0 volume for crossfading\n          html5: true,\n          onload: () => {\n            this.musicTracks.set(trackInfo.id, { howl, info: trackInfo });\n            resolve();\n          },\n          onloaderror: (id: number | undefined, error: any) => {\n            reject(`Error loading music track ${trackInfo.name}: ${error}`);\n          }\n        });\n      } catch (error) {\n        reject(`Error creating Howl for ${trackInfo.name}: ${error}`);\n      }\n    });\n  }\n  \n  // Load sound effect\n  public loadSoundEffect(effectInfo: SoundEffect): Promise<void> {\n    return new Promise((resolve, reject) => {\n      try {\n        const howl = new Howl({\n          src: [effectInfo.src],\n          loop: effectInfo.loop,\n          volume: effectInfo.volume * this.sfxVolume,\n          html5: false, // WebAudio for sound effects for better performance\n          onload: () => {\n            this.soundEffects.set(effectInfo.id, { howl, info: effectInfo });\n            resolve();\n          },\n          onloaderror: (id: number | undefined, error: any) => {\n            reject(`Error loading sound effect ${effectInfo.name}: ${error}`);\n          }\n        });\n      } catch (error) {\n        reject(`Error creating Howl for ${effectInfo.name}: ${error}`);\n      }\n    });\n  }\n  \n  // Play music with crossfade\n  public playMusic(trackId: string): void {\n    if (!this.musicTracks.has(trackId)) {\n      console.error(`Music track ${trackId} not found`);\n      return;\n    }\n    \n    if (this.currentMusic === trackId) {\n      return; // Already playing this track\n    }\n    \n    this.nextMusic = trackId;\n    \n    // If no music is currently playing, start immediately\n    if (this.currentMusic === null) {\n      this.startMusic(trackId);\n      return;\n    }\n    \n    // Otherwise crossfade\n    this.crossfade(this.currentMusic, trackId);\n  }\n  \n  // Start playing music\n  private startMusic(trackId: string): void {\n    const track = this.musicTracks.get(trackId);\n    if (!track) return;\n    \n    const { howl, info } = track;\n    \n    // Set volume based on mute state\n    const targetVolume = this.isMusicMuted ? 0 : info.volume * this.musicVolume;\n    \n    howl.volume(targetVolume);\n    howl.play();\n    \n    this.currentMusic = trackId;\n    this.nextMusic = null;\n  }\n  \n  // Crossfade between tracks\n  private crossfade(fromTrackId: string, toTrackId: string): void {\n    const fromTrack = this.musicTracks.get(fromTrackId);\n    const toTrack = this.musicTracks.get(toTrackId);\n    \n    if (!fromTrack || !toTrack) return;\n    \n    const { howl: fromHowl } = fromTrack;\n    const { howl: toHowl, info: toInfo } = toTrack; // 'toInfo' might be used later if track properties influence crossfade\n    \n    // Calculate target volumes\n    const fromTargetVolume = 0;\n    const toTargetVolume = this.isMusicMuted ? 0 : toInfo.volume * this.musicVolume;\n    \n    // Start the new track\n    toHowl.volume(0);\n    toHowl.play();\n    \n    // Fade out the current track\n    fromHowl.fade(fromHowl.volume(), fromTargetVolume, this.fadeDuration);\n    \n    // Fade in the new track\n    toHowl.fade(0, toTargetVolume, this.fadeDuration);\n    \n    // Update current track after fade duration\n    setTimeout(() => {\n      fromHowl.stop();\n      this.currentMusic = toTrackId;\n      this.nextMusic = null;\n    }, this.fadeDuration);\n  }\n  \n  // Play sound effect\n  public playSoundEffect(effectId: string): number {\n    if (this.isSfxMuted) return -1;\n    \n    const effect = this.soundEffects.get(effectId);\n    if (!effect) {\n      console.error(`Sound effect ${effectId} not found`);\n      return -1;\n    }\n    \n    const { howl } = effect;\n    return howl.play();\n  }\n  \n  // Stop sound effect\n  public stopSoundEffect(effectId: string, id?: number): void {\n    const effect = this.soundEffects.get(effectId);\n    if (!effect) return;\n    \n    if (id !== undefined) {\n      effect.howl.stop(id);\n    } else {\n      effect.howl.stop();\n    }\n  }\n  \n  // Set music volume\n  public setMusicVolume(volume: number): void {\n    this.musicVolume = Math.max(0, Math.min(1, volume));\n    \n    if (this.currentMusic && !this.isMusicMuted) {\n      const track = this.musicTracks.get(this.currentMusic);\n      if (track) {\n        track.howl.volume(track.info.volume * this.musicVolume);\n      }\n    }\n  }\n  \n  // Set sound effects volume\n  public setSfxVolume(volume: number): void {\n    this.sfxVolume = Math.max(0, Math.min(1, volume));\n    \n    if (!this.isSfxMuted) {\n      this.soundEffects.forEach(({ howl, info }) => {\n        howl.volume(info.volume * this.sfxVolume);\n      });\n    }\n  }\n  \n  // Mute/unmute music\n  public toggleMuteMusic(): boolean {\n    this.isMusicMuted = !this.isMusicMuted;\n    \n    if (this.currentMusic) {\n      const track = this.musicTracks.get(this.currentMusic);\n      if (track) {\n        track.howl.volume(this.isMusicMuted ? 0 : track.info.volume * this.musicVolume);\n      }\n    }\n    \n    return this.isMusicMuted;\n  }\n  \n  // Mute/unmute sound effects\n  public toggleMuteSfx(): boolean {\n    this.isSfxMuted = !this.isSfxMuted;\n    \n    this.soundEffects.forEach(({ howl, info }) => {\n      howl.volume(this.isSfxMuted ? 0 : info.volume * this.sfxVolume);\n    });\n    \n    return this.isSfxMuted;\n  }\n  \n  // Set race intensity (0-1) for adaptive music\n  public setRaceIntensity(intensity: number): void {\n    this.raceIntensity = Math.max(0, Math.min(1, intensity));\n    this.updateAdaptiveMusic();\n  }\n  \n  // Set customization mood for adaptive music\n  public setCustomizationMood(mood: 'sporty' | 'elegant' | 'aggressive' | 'mystical'): void {\n    this.customizationMood = mood;\n    this.updateAdaptiveMusic();\n  }\n  \n  // Set story chapter for adaptive music\n  public setStoryChapter(chapter: number): void {\n    this.storyChapter = chapter;\n    this.updateAdaptiveMusic();\n  }\n  \n  // Update adaptive music based on current game state\n  private updateAdaptiveMusic(): void {\n    // Get current game screen from the current music track\n    if (!this.currentMusic) return;\n    \n    const currentTrack = this.musicTracks.get(this.currentMusic);\n    if (!currentTrack) return;\n    \n    const category = currentTrack.info.category;\n    \n    // Select appropriate music based on current state\n    let targetTrackId: string | null = null;\n    \n    switch (category) {\n      case 'race':\n        // Select race music based on intensity\n        if (this.raceIntensity < 0.3) {\n          targetTrackId = this.findTrackByIntensity('race', 'low');\n        } else if (this.raceIntensity < 0.7) {\n          targetTrackId = this.findTrackByIntensity('race', 'medium');\n        } else {\n          targetTrackId = this.findTrackByIntensity('race', 'high');\n        }\n        break;\n        \n      case 'lab':\n        // Select lab music based on customization mood\n        targetTrackId = this.findTrackByMood(this.customizationMood);\n        break;\n        \n      case 'story':\n        // Select story music based on chapter\n        targetTrackId = this.findTrackByChapter(this.storyChapter);\n        break;\n    }\n    \n    // Play the selected track if different from current\n    if (targetTrackId && targetTrackId !== this.currentMusic && targetTrackId !== this.nextMusic) {\n      this.playMusic(targetTrackId);\n    }\n  }\n  \n  // Find a track by intensity and category\n  private findTrackByIntensity(category: 'menu' | 'race' | 'lab' | 'story', intensity: 'low' | 'medium' | 'high'): string | null {\n    for (const [id, { info }] of this.musicTracks.entries()) {\n      if (info.category === category && info.intensity === intensity) {\n        return id;\n      }\n    }\n    return null;\n  }\n  \n  // Find a track by customization mood\n  private findTrackByMood(mood: 'sporty' | 'elegant' | 'aggressive' | 'mystical'): string | null {\n    // This would require additional metadata on tracks\n    // For now, we'll use a simple mapping\n    const moodToIntensity: { [key: string]: 'low' | 'medium' | 'high' } = {\n      'sporty': 'medium',\n      'elegant': 'low',\n      'aggressive': 'high',\n      'mystical': 'medium'\n    };\n    \n    return this.findTrackByIntensity('lab', moodToIntensity[mood]);\n  }\n  \n  // Find a track by story chapter\n  private findTrackByChapter(chapter: number): string | null {\n    // This would require additional metadata on tracks\n    // For now, we'll use a simple approach based on intensity\n    const chapterToIntensity: { [key: number]: 'low' | 'medium' | 'high' } = {\n      1: 'low',\n      2: 'medium',\n      3: 'high',\n      4: 'high',\n      5: 'high'\n    };\n    \n    const intensity = chapterToIntensity[chapter] || 'medium';\n    return this.findTrackByIntensity('story', intensity);\n  }\n  \n  // Stop all audio\n  public stopAll(): void {\n    this.musicTracks.forEach(({ howl }) => {\n      howl.stop();\n      howl.unload(); // Unload each music track\n    });\n    this.soundEffects.forEach(({ howl }) => {\n      howl.stop();\n      howl.unload(); // Unload each sound effect\n    });\n    Howler.stop(); // Global stop as a final measure\n    this.currentMusic = null;\n    this.nextMusic = null;\n  }\n  \n  // Clean up resources\n  public dispose(): void {\n    this.stopAll(); // stopAll now also unloads\n    this.musicTracks.clear();\n    this.soundEffects.clear();\n    // Howler.unload(); // Consider if a global unload is needed here, but stopAll is quite thorough.\n  }\n}\n"], "mappings": ";AAAA;AACA,SAASA,IAAI,EAAEC,MAAM,QAAQ,QAAQ;AA6BrC,OAAO,MAAMC,WAAW,CAAC;EA4BfC,WAAWA,CAAA,EAAG;IAAA,KA1BdC,WAAW;IAAA,KACXC,YAAY;IAAA,KACZC,cAAc;IAAA,KACdC,YAAY;IAAA,KACZC,SAAS;IAAA,KACTC,YAAY;IAAA,KACZC,UAAU;IAAA,KACVC,YAAY;IAAA,KACZC,WAAW;IAAA,KACXC,SAAS;IAAA,KACTC,WAAW;IAAA,KACXC,YAAY;IAAA,KACZC,UAAU;IAAA,KACVC,YAAY;IAAA,KACZC,aAAa;IAAU;IAAA,KACvBC,iBAAiB;IAAoD;IAAA,KACrEC,YAAY;IAAU;IAE9B;IAAA,KACQC,YAAY,GAAwB,IAAI;IAAA,KACxCC,gBAAgB,GAAkC,IAAI;IAAA,KACtDC,eAAe,GAAkC,IAAI;IAAA,KACrDC,OAAO,GAAuB,EAAE;IAAA,KAChCC,OAAO,GAAuB,EAAE;IAAA,KAChCC,KAAK,GAAuB,EAAE;IAGpC,IAAI,CAACtB,WAAW,GAAG,IAAIuB,GAAG,CAAC,CAAC;IAC5B,IAAI,CAACtB,YAAY,GAAG,IAAIsB,GAAG,CAAC,CAAC;IAC7B,IAAI,CAACrB,cAAc,GAAG,IAAIqB,GAAG,CAAC,CAAC;IAC/B,IAAI,CAACpB,YAAY,GAAG,IAAI;IACxB,IAAI,CAACC,SAAS,GAAG,IAAI;IACrB,IAAI,CAACC,YAAY,GAAG,IAAI;IACxB,IAAI,CAACC,UAAU,GAAG,EAAE;IACpB,IAAI,CAACC,YAAY,GAAG,IAAI,CAAC,CAAC;IAC1B,IAAI,CAACC,WAAW,GAAG,GAAG;IACtB,IAAI,CAACC,SAAS,GAAG,GAAG;IACpB,IAAI,CAACC,WAAW,GAAG,GAAG;IACtB,IAAI,CAACC,YAAY,GAAG,KAAK;IACzB,IAAI,CAACC,UAAU,GAAG,KAAK;IACvB,IAAI,CAACC,YAAY,GAAG,KAAK;IACzB,IAAI,CAACC,aAAa,GAAG,CAAC;IACtB,IAAI,CAACC,iBAAiB,GAAG,QAAQ;IACjC,IAAI,CAACC,YAAY,GAAG,CAAC;IAErB,IAAI,CAACQ,yBAAyB,CAAC,CAAC;;IAEhC;IACA3B,MAAM,CAAC4B,UAAU,GAAG,IAAI;IACxB5B,MAAM,CAAC6B,aAAa,GAAG,EAAE;EAC3B;EAEA,OAAcC,WAAWA,CAAA,EAAgB;IACvC,IAAI,CAAC7B,WAAW,CAAC8B,QAAQ,EAAE;MACzB9B,WAAW,CAAC8B,QAAQ,GAAG,IAAI9B,WAAW,CAAC,CAAC;IAC1C;IACA,OAAOA,WAAW,CAAC8B,QAAQ;EAC7B;;EAEA;EACOC,cAAcA,CAACC,SAAoB,EAAiB;IACzD,OAAO,IAAIC,OAAO,CAAC,CAACC,OAAO,EAAEC,MAAM,KAAK;MACtC,IAAI;QACF,MAAMC,IAAI,GAAG,IAAItC,IAAI,CAAC;UACpBuC,GAAG,EAAE,CAACL,SAAS,CAACK,GAAG,CAAC;UACpBC,IAAI,EAAEN,SAAS,CAACM,IAAI;UACpBC,MAAM,EAAE,CAAC;UAAE;UACXC,KAAK,EAAE,IAAI;UACXC,MAAM,EAAEA,CAAA,KAAM;YACZ,IAAI,CAACvC,WAAW,CAACwC,GAAG,CAACV,SAAS,CAACW,EAAE,EAAE;cAAEP,IAAI;cAAEQ,IAAI,EAAEZ;YAAU,CAAC,CAAC;YAC7DE,OAAO,CAAC,CAAC;UACX,CAAC;UACDW,WAAW,EAAEA,CAACF,EAAsB,EAAEG,KAAU,KAAK;YACnDX,MAAM,CAAC,6BAA6BH,SAAS,CAACe,IAAI,KAAKD,KAAK,EAAE,CAAC;UACjE;QACF,CAAC,CAAC;MACJ,CAAC,CAAC,OAAOA,KAAK,EAAE;QACdX,MAAM,CAAC,2BAA2BH,SAAS,CAACe,IAAI,KAAKD,KAAK,EAAE,CAAC;MAC/D;IACF,CAAC,CAAC;EACJ;;EAEA;EACOE,eAAeA,CAACC,UAAuB,EAAiB;IAC7D,OAAO,IAAIhB,OAAO,CAAC,CAACC,OAAO,EAAEC,MAAM,KAAK;MACtC,IAAI;QACF,MAAMC,IAAI,GAAG,IAAItC,IAAI,CAAC;UACpBuC,GAAG,EAAE,CAACY,UAAU,CAACZ,GAAG,CAAC;UACrBC,IAAI,EAAEW,UAAU,CAACX,IAAI;UACrBC,MAAM,EAAEU,UAAU,CAACV,MAAM,GAAG,IAAI,CAAC5B,SAAS;UAC1C6B,KAAK,EAAE,KAAK;UAAE;UACdC,MAAM,EAAEA,CAAA,KAAM;YACZ,IAAI,CAACtC,YAAY,CAACuC,GAAG,CAACO,UAAU,CAACN,EAAE,EAAE;cAAEP,IAAI;cAAEQ,IAAI,EAAEK;YAAW,CAAC,CAAC;YAChEf,OAAO,CAAC,CAAC;UACX,CAAC;UACDW,WAAW,EAAEA,CAACF,EAAsB,EAAEG,KAAU,KAAK;YACnDX,MAAM,CAAC,8BAA8Bc,UAAU,CAACF,IAAI,KAAKD,KAAK,EAAE,CAAC;UACnE;QACF,CAAC,CAAC;MACJ,CAAC,CAAC,OAAOA,KAAK,EAAE;QACdX,MAAM,CAAC,2BAA2Bc,UAAU,CAACF,IAAI,KAAKD,KAAK,EAAE,CAAC;MAChE;IACF,CAAC,CAAC;EACJ;;EAEA;EACOI,SAASA,CAACC,OAAe,EAAQ;IACtC,IAAI,CAAC,IAAI,CAACjD,WAAW,CAACkD,GAAG,CAACD,OAAO,CAAC,EAAE;MAClCE,OAAO,CAACP,KAAK,CAAC,eAAeK,OAAO,YAAY,CAAC;MACjD;IACF;IAEA,IAAI,IAAI,CAAC9C,YAAY,KAAK8C,OAAO,EAAE;MACjC,OAAO,CAAC;IACV;IAEA,IAAI,CAAC7C,SAAS,GAAG6C,OAAO;;IAExB;IACA,IAAI,IAAI,CAAC9C,YAAY,KAAK,IAAI,EAAE;MAC9B,IAAI,CAACiD,UAAU,CAACH,OAAO,CAAC;MACxB;IACF;;IAEA;IACA,IAAI,CAACI,SAAS,CAAC,IAAI,CAAClD,YAAY,EAAE8C,OAAO,CAAC;EAC5C;;EAEA;EACQG,UAAUA,CAACH,OAAe,EAAQ;IACxC,MAAMK,KAAK,GAAG,IAAI,CAACtD,WAAW,CAACuD,GAAG,CAACN,OAAO,CAAC;IAC3C,IAAI,CAACK,KAAK,EAAE;IAEZ,MAAM;MAAEpB,IAAI;MAAEQ;IAAK,CAAC,GAAGY,KAAK;;IAE5B;IACA,MAAME,YAAY,GAAG,IAAI,CAAC7C,YAAY,GAAG,CAAC,GAAG+B,IAAI,CAACL,MAAM,GAAG,IAAI,CAAC7B,WAAW;IAE3E0B,IAAI,CAACG,MAAM,CAACmB,YAAY,CAAC;IACzBtB,IAAI,CAACuB,IAAI,CAAC,CAAC;IAEX,IAAI,CAACtD,YAAY,GAAG8C,OAAO;IAC3B,IAAI,CAAC7C,SAAS,GAAG,IAAI;EACvB;;EAEA;EACQiD,SAASA,CAACK,WAAmB,EAAEC,SAAiB,EAAQ;IAC9D,MAAMC,SAAS,GAAG,IAAI,CAAC5D,WAAW,CAACuD,GAAG,CAACG,WAAW,CAAC;IACnD,MAAMG,OAAO,GAAG,IAAI,CAAC7D,WAAW,CAACuD,GAAG,CAACI,SAAS,CAAC;IAE/C,IAAI,CAACC,SAAS,IAAI,CAACC,OAAO,EAAE;IAE5B,MAAM;MAAE3B,IAAI,EAAE4B;IAAS,CAAC,GAAGF,SAAS;IACpC,MAAM;MAAE1B,IAAI,EAAE6B,MAAM;MAAErB,IAAI,EAAEsB;IAAO,CAAC,GAAGH,OAAO,CAAC,CAAC;;IAEhD;IACA,MAAMI,gBAAgB,GAAG,CAAC;IAC1B,MAAMC,cAAc,GAAG,IAAI,CAACvD,YAAY,GAAG,CAAC,GAAGqD,MAAM,CAAC3B,MAAM,GAAG,IAAI,CAAC7B,WAAW;;IAE/E;IACAuD,MAAM,CAAC1B,MAAM,CAAC,CAAC,CAAC;IAChB0B,MAAM,CAACN,IAAI,CAAC,CAAC;;IAEb;IACAK,QAAQ,CAACK,IAAI,CAACL,QAAQ,CAACzB,MAAM,CAAC,CAAC,EAAE4B,gBAAgB,EAAE,IAAI,CAAC1D,YAAY,CAAC;;IAErE;IACAwD,MAAM,CAACI,IAAI,CAAC,CAAC,EAAED,cAAc,EAAE,IAAI,CAAC3D,YAAY,CAAC;;IAEjD;IACA6D,UAAU,CAAC,MAAM;MACfN,QAAQ,CAACO,IAAI,CAAC,CAAC;MACf,IAAI,CAAClE,YAAY,GAAGwD,SAAS;MAC7B,IAAI,CAACvD,SAAS,GAAG,IAAI;IACvB,CAAC,EAAE,IAAI,CAACG,YAAY,CAAC;EACvB;;EAEA;EACO+D,eAAeA,CAACC,QAAgB,EAAU;IAC/C,IAAI,IAAI,CAAC3D,UAAU,EAAE,OAAO,CAAC,CAAC;IAE9B,MAAM4D,MAAM,GAAG,IAAI,CAACvE,YAAY,CAACsD,GAAG,CAACgB,QAAQ,CAAC;IAC9C,IAAI,CAACC,MAAM,EAAE;MACXrB,OAAO,CAACP,KAAK,CAAC,gBAAgB2B,QAAQ,YAAY,CAAC;MACnD,OAAO,CAAC,CAAC;IACX;IAEA,MAAM;MAAErC;IAAK,CAAC,GAAGsC,MAAM;IACvB,OAAOtC,IAAI,CAACuB,IAAI,CAAC,CAAC;EACpB;;EAEA;EACOgB,eAAeA,CAACF,QAAgB,EAAE9B,EAAW,EAAQ;IAC1D,MAAM+B,MAAM,GAAG,IAAI,CAACvE,YAAY,CAACsD,GAAG,CAACgB,QAAQ,CAAC;IAC9C,IAAI,CAACC,MAAM,EAAE;IAEb,IAAI/B,EAAE,KAAKiC,SAAS,EAAE;MACpBF,MAAM,CAACtC,IAAI,CAACmC,IAAI,CAAC5B,EAAE,CAAC;IACtB,CAAC,MAAM;MACL+B,MAAM,CAACtC,IAAI,CAACmC,IAAI,CAAC,CAAC;IACpB;EACF;;EAEA;EACOM,cAAcA,CAACtC,MAAc,EAAQ;IAC1C,IAAI,CAAC7B,WAAW,GAAGoE,IAAI,CAACC,GAAG,CAAC,CAAC,EAAED,IAAI,CAACE,GAAG,CAAC,CAAC,EAAEzC,MAAM,CAAC,CAAC;IAEnD,IAAI,IAAI,CAAClC,YAAY,IAAI,CAAC,IAAI,CAACQ,YAAY,EAAE;MAC3C,MAAM2C,KAAK,GAAG,IAAI,CAACtD,WAAW,CAACuD,GAAG,CAAC,IAAI,CAACpD,YAAY,CAAC;MACrD,IAAImD,KAAK,EAAE;QACTA,KAAK,CAACpB,IAAI,CAACG,MAAM,CAACiB,KAAK,CAACZ,IAAI,CAACL,MAAM,GAAG,IAAI,CAAC7B,WAAW,CAAC;MACzD;IACF;EACF;;EAEA;EACOuE,YAAYA,CAAC1C,MAAc,EAAQ;IACxC,IAAI,CAAC5B,SAAS,GAAGmE,IAAI,CAACC,GAAG,CAAC,CAAC,EAAED,IAAI,CAACE,GAAG,CAAC,CAAC,EAAEzC,MAAM,CAAC,CAAC;IAEjD,IAAI,CAAC,IAAI,CAACzB,UAAU,EAAE;MACpB,IAAI,CAACX,YAAY,CAAC+E,OAAO,CAAC,CAAC;QAAE9C,IAAI;QAAEQ;MAAK,CAAC,KAAK;QAC5CR,IAAI,CAACG,MAAM,CAACK,IAAI,CAACL,MAAM,GAAG,IAAI,CAAC5B,SAAS,CAAC;MAC3C,CAAC,CAAC;IACJ;EACF;;EAEA;EACOwE,eAAeA,CAAA,EAAY;IAChC,IAAI,CAACtE,YAAY,GAAG,CAAC,IAAI,CAACA,YAAY;IAEtC,IAAI,IAAI,CAACR,YAAY,EAAE;MACrB,MAAMmD,KAAK,GAAG,IAAI,CAACtD,WAAW,CAACuD,GAAG,CAAC,IAAI,CAACpD,YAAY,CAAC;MACrD,IAAImD,KAAK,EAAE;QACTA,KAAK,CAACpB,IAAI,CAACG,MAAM,CAAC,IAAI,CAAC1B,YAAY,GAAG,CAAC,GAAG2C,KAAK,CAACZ,IAAI,CAACL,MAAM,GAAG,IAAI,CAAC7B,WAAW,CAAC;MACjF;IACF;IAEA,OAAO,IAAI,CAACG,YAAY;EAC1B;;EAEA;EACOuE,aAAaA,CAAA,EAAY;IAC9B,IAAI,CAACtE,UAAU,GAAG,CAAC,IAAI,CAACA,UAAU;IAElC,IAAI,CAACX,YAAY,CAAC+E,OAAO,CAAC,CAAC;MAAE9C,IAAI;MAAEQ;IAAK,CAAC,KAAK;MAC5CR,IAAI,CAACG,MAAM,CAAC,IAAI,CAACzB,UAAU,GAAG,CAAC,GAAG8B,IAAI,CAACL,MAAM,GAAG,IAAI,CAAC5B,SAAS,CAAC;IACjE,CAAC,CAAC;IAEF,OAAO,IAAI,CAACG,UAAU;EACxB;;EAEA;EACOuE,gBAAgBA,CAACC,SAAiB,EAAQ;IAC/C,IAAI,CAACtE,aAAa,GAAG8D,IAAI,CAACC,GAAG,CAAC,CAAC,EAAED,IAAI,CAACE,GAAG,CAAC,CAAC,EAAEM,SAAS,CAAC,CAAC;IACxD,IAAI,CAACC,mBAAmB,CAAC,CAAC;EAC5B;;EAEA;EACOC,oBAAoBA,CAACC,IAAsD,EAAQ;IACxF,IAAI,CAACxE,iBAAiB,GAAGwE,IAAI;IAC7B,IAAI,CAACF,mBAAmB,CAAC,CAAC;EAC5B;;EAEA;EACOG,eAAeA,CAACC,OAAe,EAAQ;IAC5C,IAAI,CAACzE,YAAY,GAAGyE,OAAO;IAC3B,IAAI,CAACJ,mBAAmB,CAAC,CAAC;EAC5B;;EAEA;EACQA,mBAAmBA,CAAA,EAAS;IAClC;IACA,IAAI,CAAC,IAAI,CAAClF,YAAY,EAAE;IAExB,MAAMuF,YAAY,GAAG,IAAI,CAAC1F,WAAW,CAACuD,GAAG,CAAC,IAAI,CAACpD,YAAY,CAAC;IAC5D,IAAI,CAACuF,YAAY,EAAE;IAEnB,MAAMC,QAAQ,GAAGD,YAAY,CAAChD,IAAI,CAACiD,QAAQ;;IAE3C;IACA,IAAIC,aAA4B,GAAG,IAAI;IAEvC,QAAQD,QAAQ;MACd,KAAK,MAAM;QACT;QACA,IAAI,IAAI,CAAC7E,aAAa,GAAG,GAAG,EAAE;UAC5B8E,aAAa,GAAG,IAAI,CAACC,oBAAoB,CAAC,MAAM,EAAE,KAAK,CAAC;QAC1D,CAAC,MAAM,IAAI,IAAI,CAAC/E,aAAa,GAAG,GAAG,EAAE;UACnC8E,aAAa,GAAG,IAAI,CAACC,oBAAoB,CAAC,MAAM,EAAE,QAAQ,CAAC;QAC7D,CAAC,MAAM;UACLD,aAAa,GAAG,IAAI,CAACC,oBAAoB,CAAC,MAAM,EAAE,MAAM,CAAC;QAC3D;QACA;MAEF,KAAK,KAAK;QACR;QACAD,aAAa,GAAG,IAAI,CAACE,eAAe,CAAC,IAAI,CAAC/E,iBAAiB,CAAC;QAC5D;MAEF,KAAK,OAAO;QACV;QACA6E,aAAa,GAAG,IAAI,CAACG,kBAAkB,CAAC,IAAI,CAAC/E,YAAY,CAAC;QAC1D;IACJ;;IAEA;IACA,IAAI4E,aAAa,IAAIA,aAAa,KAAK,IAAI,CAACzF,YAAY,IAAIyF,aAAa,KAAK,IAAI,CAACxF,SAAS,EAAE;MAC5F,IAAI,CAAC4C,SAAS,CAAC4C,aAAa,CAAC;IAC/B;EACF;;EAEA;EACQC,oBAAoBA,CAACF,QAA2C,EAAEP,SAAoC,EAAiB;IAC7H,KAAK,MAAM,CAAC3C,EAAE,EAAE;MAAEC;IAAK,CAAC,CAAC,IAAI,IAAI,CAAC1C,WAAW,CAACgG,OAAO,CAAC,CAAC,EAAE;MACvD,IAAItD,IAAI,CAACiD,QAAQ,KAAKA,QAAQ,IAAIjD,IAAI,CAAC0C,SAAS,KAAKA,SAAS,EAAE;QAC9D,OAAO3C,EAAE;MACX;IACF;IACA,OAAO,IAAI;EACb;;EAEA;EACQqD,eAAeA,CAACP,IAAsD,EAAiB;IAC7F;IACA;IACA,MAAMU,eAA6D,GAAG;MACpE,QAAQ,EAAE,QAAQ;MAClB,SAAS,EAAE,KAAK;MAChB,YAAY,EAAE,MAAM;MACpB,UAAU,EAAE;IACd,CAAC;IAED,OAAO,IAAI,CAACJ,oBAAoB,CAAC,KAAK,EAAEI,eAAe,CAACV,IAAI,CAAC,CAAC;EAChE;;EAEA;EACQQ,kBAAkBA,CAACN,OAAe,EAAiB;IACzD;IACA;IACA,MAAMS,kBAAgE,GAAG;MACvE,CAAC,EAAE,KAAK;MACR,CAAC,EAAE,QAAQ;MACX,CAAC,EAAE,MAAM;MACT,CAAC,EAAE,MAAM;MACT,CAAC,EAAE;IACL,CAAC;IAED,MAAMd,SAAS,GAAGc,kBAAkB,CAACT,OAAO,CAAC,IAAI,QAAQ;IACzD,OAAO,IAAI,CAACI,oBAAoB,CAAC,OAAO,EAAET,SAAS,CAAC;EACtD;;EAEA;EACOe,OAAOA,CAAA,EAAS;IACrB,IAAI,CAACnG,WAAW,CAACgF,OAAO,CAAC,CAAC;MAAE9C;IAAK,CAAC,KAAK;MACrCA,IAAI,CAACmC,IAAI,CAAC,CAAC;MACXnC,IAAI,CAACkE,MAAM,CAAC,CAAC,CAAC,CAAC;IACjB,CAAC,CAAC;IACF,IAAI,CAACnG,YAAY,CAAC+E,OAAO,CAAC,CAAC;MAAE9C;IAAK,CAAC,KAAK;MACtCA,IAAI,CAACmC,IAAI,CAAC,CAAC;MACXnC,IAAI,CAACkE,MAAM,CAAC,CAAC,CAAC,CAAC;IACjB,CAAC,CAAC;IACFvG,MAAM,CAACwE,IAAI,CAAC,CAAC,CAAC,CAAC;IACf,IAAI,CAAClE,YAAY,GAAG,IAAI;IACxB,IAAI,CAACC,SAAS,GAAG,IAAI;EACvB;;EAEA;EACOiG,OAAOA,CAAA,EAAS;IACrB,IAAI,CAACF,OAAO,CAAC,CAAC,CAAC,CAAC;IAChB,IAAI,CAACnG,WAAW,CAACsG,KAAK,CAAC,CAAC;IACxB,IAAI,CAACrG,YAAY,CAACqG,KAAK,CAAC,CAAC;IACzB;EACF;AACF;AAACC,YAAA,GAvXYzG,WAAW;AAAXA,WAAW,CACP8B,QAAQ", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}