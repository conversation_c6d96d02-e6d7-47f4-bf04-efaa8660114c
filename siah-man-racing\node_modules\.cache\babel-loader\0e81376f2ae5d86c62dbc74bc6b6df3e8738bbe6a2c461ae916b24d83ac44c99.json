{"ast": null, "code": "import { IMPORT, LAYER, COMMENT, RULESET, DECLARATION, KEYFRAMES } from './Enum.js';\nimport { strlen } from './Utility.js';\n\n/**\n * @param {object[]} children\n * @param {function} callback\n * @return {string}\n */\nexport function serialize(children, callback) {\n  var output = '';\n  for (var i = 0; i < children.length; i++) output += callback(children[i], i, children, callback) || '';\n  return output;\n}\n\n/**\n * @param {object} element\n * @param {number} index\n * @param {object[]} children\n * @param {function} callback\n * @return {string}\n */\nexport function stringify(element, index, children, callback) {\n  switch (element.type) {\n    case LAYER:\n      if (element.children.length) break;\n    case IMPORT:\n    case DECLARATION:\n      return element.return = element.return || element.value;\n    case COMMENT:\n      return '';\n    case KEYFRAMES:\n      return element.return = element.value + '{' + serialize(element.children, callback) + '}';\n    case RULESET:\n      if (!strlen(element.value = element.props.join(','))) return '';\n  }\n  return strlen(children = serialize(element.children, callback)) ? element.return = element.value + '{' + children + '}' : '';\n}", "map": {"version": 3, "names": ["IMPORT", "LAYER", "COMMENT", "RULESET", "DECLARATION", "KEYFRAMES", "strlen", "serialize", "children", "callback", "output", "i", "length", "stringify", "element", "index", "type", "return", "value", "props", "join"], "sources": ["C:/Users/<USER>/Documents/Apps_Creations/SiahMan_Racing4Speed_WebGame/siah-man-racing/node_modules/stylis/src/Serializer.js"], "sourcesContent": ["import {IMPOR<PERSON>, LAYER, COMMENT, RULESET, DECLARATION, KEYFRAMES} from './Enum.js'\nimport {strlen} from './Utility.js'\n\n/**\n * @param {object[]} children\n * @param {function} callback\n * @return {string}\n */\nexport function serialize (children, callback) {\n\tvar output = ''\n\n\tfor (var i = 0; i < children.length; i++)\n\t\toutput += callback(children[i], i, children, callback) || ''\n\n\treturn output\n}\n\n/**\n * @param {object} element\n * @param {number} index\n * @param {object[]} children\n * @param {function} callback\n * @return {string}\n */\nexport function stringify (element, index, children, callback) {\n\tswitch (element.type) {\n\t\tcase LAYER: if (element.children.length) break\n\t\tcase IMPORT: case DECLARATION: return element.return = element.return || element.value\n\t\tcase COMMENT: return ''\n\t\tcase KEYFRAMES: return element.return = element.value + '{' + serialize(element.children, callback) + '}'\n\t\tcase RULESET: if (!strlen(element.value = element.props.join(','))) return ''\n\t}\n\n\treturn strlen(children = serialize(element.children, callback)) ? element.return = element.value + '{' + children + '}' : ''\n}\n"], "mappings": "AAAA,SAAQA,MAAM,EAAEC,KAAK,EAAEC,OAAO,EAAEC,OAAO,EAAEC,WAAW,EAAEC,SAAS,QAAO,WAAW;AACjF,SAAQC,MAAM,QAAO,cAAc;;AAEnC;AACA;AACA;AACA;AACA;AACA,OAAO,SAASC,SAASA,CAAEC,QAAQ,EAAEC,QAAQ,EAAE;EAC9C,IAAIC,MAAM,GAAG,EAAE;EAEf,KAAK,IAAIC,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGH,QAAQ,CAACI,MAAM,EAAED,CAAC,EAAE,EACvCD,MAAM,IAAID,QAAQ,CAACD,QAAQ,CAACG,CAAC,CAAC,EAAEA,CAAC,EAAEH,QAAQ,EAAEC,QAAQ,CAAC,IAAI,EAAE;EAE7D,OAAOC,MAAM;AACd;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO,SAASG,SAASA,CAAEC,OAAO,EAAEC,KAAK,EAAEP,QAAQ,EAAEC,QAAQ,EAAE;EAC9D,QAAQK,OAAO,CAACE,IAAI;IACnB,KAAKf,KAAK;MAAE,IAAIa,OAAO,CAACN,QAAQ,CAACI,MAAM,EAAE;IACzC,KAAKZ,MAAM;IAAE,KAAKI,WAAW;MAAE,OAAOU,OAAO,CAACG,MAAM,GAAGH,OAAO,CAACG,MAAM,IAAIH,OAAO,CAACI,KAAK;IACtF,KAAKhB,OAAO;MAAE,OAAO,EAAE;IACvB,KAAKG,SAAS;MAAE,OAAOS,OAAO,CAACG,MAAM,GAAGH,OAAO,CAACI,KAAK,GAAG,GAAG,GAAGX,SAAS,CAACO,OAAO,CAACN,QAAQ,EAAEC,QAAQ,CAAC,GAAG,GAAG;IACzG,KAAKN,OAAO;MAAE,IAAI,CAACG,MAAM,CAACQ,OAAO,CAACI,KAAK,GAAGJ,OAAO,CAACK,KAAK,CAACC,IAAI,CAAC,GAAG,CAAC,CAAC,EAAE,OAAO,EAAE;EAC9E;EAEA,OAAOd,MAAM,CAACE,QAAQ,GAAGD,SAAS,CAACO,OAAO,CAACN,QAAQ,EAAEC,QAAQ,CAAC,CAAC,GAAGK,OAAO,CAACG,MAAM,GAAGH,OAAO,CAACI,KAAK,GAAG,GAAG,GAAGV,QAAQ,GAAG,GAAG,GAAG,EAAE;AAC7H", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}