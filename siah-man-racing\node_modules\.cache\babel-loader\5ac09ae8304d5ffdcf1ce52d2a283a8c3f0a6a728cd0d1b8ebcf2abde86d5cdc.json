{"ast": null, "code": "import React from 'react';\nimport { shallow } from 'zustand/vanilla/shallow';\nfunction useShallow(selector) {\n  const prev = React.useRef(void 0);\n  return state => {\n    const next = selector(state);\n    return shallow(prev.current, next) ? prev.current : prev.current = next;\n  };\n}\nexport { useShallow };", "map": {"version": 3, "names": ["React", "shallow", "useShallow", "selector", "prev", "useRef", "state", "next", "current"], "sources": ["C:/Users/<USER>/Documents/Apps_Creations/SiahMan_Racing4Speed_WebGame/siah-man-racing/node_modules/zustand/esm/react/shallow.mjs"], "sourcesContent": ["import React from 'react';\nimport { shallow } from 'zustand/vanilla/shallow';\n\nfunction useShallow(selector) {\n  const prev = React.useRef(void 0);\n  return (state) => {\n    const next = selector(state);\n    return shallow(prev.current, next) ? prev.current : prev.current = next;\n  };\n}\n\nexport { useShallow };\n"], "mappings": "AAAA,OAAOA,KAAK,MAAM,OAAO;AACzB,SAASC,OAAO,QAAQ,yBAAyB;AAEjD,SAASC,UAAUA,CAACC,QAAQ,EAAE;EAC5B,MAAMC,IAAI,GAAGJ,KAAK,CAACK,MAAM,CAAC,KAAK,CAAC,CAAC;EACjC,OAAQC,KAAK,IAAK;IAChB,MAAMC,IAAI,GAAGJ,QAAQ,CAACG,KAAK,CAAC;IAC5B,OAAOL,OAAO,CAACG,IAAI,CAACI,OAAO,EAAED,IAAI,CAAC,GAAGH,IAAI,CAACI,OAAO,GAAGJ,IAAI,CAACI,OAAO,GAAGD,IAAI;EACzE,CAAC;AACH;AAEA,SAASL,UAAU", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}