{"ast": null, "code": "import { BufferGeometry, BufferAttribute, InterleavedBuffer, InterleavedBufferAttribute, TrianglesDrawMode, TriangleFanDrawMode, TriangleStripDrawMode, Vector3, Float32BufferAttribute } from \"three\";\nimport { getWith<PERSON>ey } from \"../types/helpers.js\";\nconst mergeBufferGeometries = (geometries, useGroups) => {\n  const isIndexed = geometries[0].index !== null;\n  const attributesUsed = new Set(Object.keys(geometries[0].attributes));\n  const morphAttributesUsed = new Set(Object.keys(geometries[0].morphAttributes));\n  const attributes = {};\n  const morphAttributes = {};\n  const morphTargetsRelative = geometries[0].morphTargetsRelative;\n  const mergedGeometry = new BufferGeometry();\n  let offset = 0;\n  geometries.forEach((geom, i) => {\n    let attributesCount = 0;\n    if (isIndexed !== (geom.index !== null)) {\n      console.error(\"THREE.BufferGeometryUtils: .mergeBufferGeometries() failed with geometry at index \" + i + \". All geometries must have compatible attributes; make sure index attribute exists among all geometries, or in none of them.\");\n      return null;\n    }\n    for (let name in geom.attributes) {\n      if (!attributesUsed.has(name)) {\n        console.error(\"THREE.BufferGeometryUtils: .mergeBufferGeometries() failed with geometry at index \" + i + '. All geometries must have compatible attributes; make sure \"' + name + '\" attribute exists among all geometries, or in none of them.');\n        return null;\n      }\n      if (attributes[name] === void 0) {\n        attributes[name] = [];\n      }\n      attributes[name].push(geom.attributes[name]);\n      attributesCount++;\n    }\n    if (attributesCount !== attributesUsed.size) {\n      console.error(\"THREE.BufferGeometryUtils: .mergeBufferGeometries() failed with geometry at index \" + i + \". Make sure all geometries have the same number of attributes.\");\n      return null;\n    }\n    if (morphTargetsRelative !== geom.morphTargetsRelative) {\n      console.error(\"THREE.BufferGeometryUtils: .mergeBufferGeometries() failed with geometry at index \" + i + \". .morphTargetsRelative must be consistent throughout all geometries.\");\n      return null;\n    }\n    for (let name in geom.morphAttributes) {\n      if (!morphAttributesUsed.has(name)) {\n        console.error(\"THREE.BufferGeometryUtils: .mergeBufferGeometries() failed with geometry at index \" + i + \".  .morphAttributes must be consistent throughout all geometries.\");\n        return null;\n      }\n      if (morphAttributes[name] === void 0) morphAttributes[name] = [];\n      morphAttributes[name].push(geom.morphAttributes[name]);\n    }\n    mergedGeometry.userData.mergedUserData = mergedGeometry.userData.mergedUserData || [];\n    mergedGeometry.userData.mergedUserData.push(geom.userData);\n    if (useGroups) {\n      let count;\n      if (geom.index) {\n        count = geom.index.count;\n      } else if (geom.attributes.position !== void 0) {\n        count = geom.attributes.position.count;\n      } else {\n        console.error(\"THREE.BufferGeometryUtils: .mergeBufferGeometries() failed with geometry at index \" + i + \". The geometry must have either an index or a position attribute\");\n        return null;\n      }\n      mergedGeometry.addGroup(offset, count, i);\n      offset += count;\n    }\n  });\n  if (isIndexed) {\n    let indexOffset = 0;\n    const mergedIndex = [];\n    geometries.forEach(geom => {\n      const index = geom.index;\n      for (let j = 0; j < index.count; ++j) {\n        mergedIndex.push(index.getX(j) + indexOffset);\n      }\n      indexOffset += geom.attributes.position.count;\n    });\n    mergedGeometry.setIndex(mergedIndex);\n  }\n  for (let name in attributes) {\n    const mergedAttribute = mergeBufferAttributes(attributes[name]);\n    if (!mergedAttribute) {\n      console.error(\"THREE.BufferGeometryUtils: .mergeBufferGeometries() failed while trying to merge the \" + name + \" attribute.\");\n      return null;\n    }\n    mergedGeometry.setAttribute(name, mergedAttribute);\n  }\n  for (let name in morphAttributes) {\n    const numMorphTargets = morphAttributes[name][0].length;\n    if (numMorphTargets === 0) break;\n    mergedGeometry.morphAttributes = mergedGeometry.morphAttributes || {};\n    mergedGeometry.morphAttributes[name] = [];\n    for (let i = 0; i < numMorphTargets; ++i) {\n      const morphAttributesToMerge = [];\n      for (let j = 0; j < morphAttributes[name].length; ++j) {\n        morphAttributesToMerge.push(morphAttributes[name][j][i]);\n      }\n      const mergedMorphAttribute = mergeBufferAttributes(morphAttributesToMerge);\n      if (!mergedMorphAttribute) {\n        console.error(\"THREE.BufferGeometryUtils: .mergeBufferGeometries() failed while trying to merge the \" + name + \" morphAttribute.\");\n        return null;\n      }\n      mergedGeometry.morphAttributes[name].push(mergedMorphAttribute);\n    }\n  }\n  return mergedGeometry;\n};\nconst mergeBufferAttributes = attributes => {\n  let TypedArray = void 0;\n  let itemSize = void 0;\n  let normalized = void 0;\n  let arrayLength = 0;\n  attributes.forEach(attr => {\n    if (TypedArray === void 0) {\n      TypedArray = attr.array.constructor;\n    }\n    if (TypedArray !== attr.array.constructor) {\n      console.error(\"THREE.BufferGeometryUtils: .mergeBufferAttributes() failed. BufferAttribute.array must be of consistent array types across matching attributes.\");\n      return null;\n    }\n    if (itemSize === void 0) itemSize = attr.itemSize;\n    if (itemSize !== attr.itemSize) {\n      console.error(\"THREE.BufferGeometryUtils: .mergeBufferAttributes() failed. BufferAttribute.itemSize must be consistent across matching attributes.\");\n      return null;\n    }\n    if (normalized === void 0) normalized = attr.normalized;\n    if (normalized !== attr.normalized) {\n      console.error(\"THREE.BufferGeometryUtils: .mergeBufferAttributes() failed. BufferAttribute.normalized must be consistent across matching attributes.\");\n      return null;\n    }\n    arrayLength += attr.array.length;\n  });\n  if (TypedArray && itemSize) {\n    const array = new TypedArray(arrayLength);\n    let offset = 0;\n    attributes.forEach(attr => {\n      array.set(attr.array, offset);\n      offset += attr.array.length;\n    });\n    return new BufferAttribute(array, itemSize, normalized);\n  }\n};\nconst interleaveAttributes = attributes => {\n  let TypedArray = void 0;\n  let arrayLength = 0;\n  let stride = 0;\n  for (let i = 0, l = attributes.length; i < l; ++i) {\n    const attribute = attributes[i];\n    if (TypedArray === void 0) TypedArray = attribute.array.constructor;\n    if (TypedArray !== attribute.array.constructor) {\n      console.error(\"AttributeBuffers of different types cannot be interleaved\");\n      return null;\n    }\n    arrayLength += attribute.array.length;\n    stride += attribute.itemSize;\n  }\n  const interleavedBuffer = new InterleavedBuffer(new TypedArray(arrayLength), stride);\n  let offset = 0;\n  const res = [];\n  const getters = [\"getX\", \"getY\", \"getZ\", \"getW\"];\n  const setters = [\"setX\", \"setY\", \"setZ\", \"setW\"];\n  for (let j = 0, l = attributes.length; j < l; j++) {\n    const attribute = attributes[j];\n    const itemSize = attribute.itemSize;\n    const count = attribute.count;\n    const iba = new InterleavedBufferAttribute(interleavedBuffer, itemSize, offset, attribute.normalized);\n    res.push(iba);\n    offset += itemSize;\n    for (let c = 0; c < count; c++) {\n      for (let k = 0; k < itemSize; k++) {\n        const set = getWithKey(iba, setters[k]);\n        const get = getWithKey(attribute, getters[k]);\n        set(c, get(c));\n      }\n    }\n  }\n  return res;\n};\nfunction estimateBytesUsed(geometry) {\n  let mem = 0;\n  for (let name in geometry.attributes) {\n    const attr = geometry.getAttribute(name);\n    mem += attr.count * attr.itemSize * attr.array.BYTES_PER_ELEMENT;\n  }\n  const indices = geometry.getIndex();\n  mem += indices ? indices.count * indices.itemSize * indices.array.BYTES_PER_ELEMENT : 0;\n  return mem;\n}\nfunction mergeVertices(geometry, tolerance = 1e-4) {\n  tolerance = Math.max(tolerance, Number.EPSILON);\n  const hashToIndex = {};\n  const indices = geometry.getIndex();\n  const positions = geometry.getAttribute(\"position\");\n  const vertexCount = indices ? indices.count : positions.count;\n  let nextIndex = 0;\n  const attributeNames = Object.keys(geometry.attributes);\n  const attrArrays = {};\n  const morphAttrsArrays = {};\n  const newIndices = [];\n  const getters = [\"getX\", \"getY\", \"getZ\", \"getW\"];\n  for (let i = 0, l = attributeNames.length; i < l; i++) {\n    const name = attributeNames[i];\n    attrArrays[name] = [];\n    const morphAttr = geometry.morphAttributes[name];\n    if (morphAttr) {\n      morphAttrsArrays[name] = new Array(morphAttr.length).fill(0).map(() => []);\n    }\n  }\n  const decimalShift = Math.log10(1 / tolerance);\n  const shiftMultiplier = Math.pow(10, decimalShift);\n  for (let i = 0; i < vertexCount; i++) {\n    const index = indices ? indices.getX(i) : i;\n    let hash = \"\";\n    for (let j = 0, l = attributeNames.length; j < l; j++) {\n      const name = attributeNames[j];\n      const attribute = geometry.getAttribute(name);\n      const itemSize = attribute.itemSize;\n      for (let k = 0; k < itemSize; k++) {\n        hash += `${~~(attribute[getters[k]](index) * shiftMultiplier)},`;\n      }\n    }\n    if (hash in hashToIndex) {\n      newIndices.push(hashToIndex[hash]);\n    } else {\n      for (let j = 0, l = attributeNames.length; j < l; j++) {\n        const name = attributeNames[j];\n        const attribute = geometry.getAttribute(name);\n        const morphAttr = geometry.morphAttributes[name];\n        const itemSize = attribute.itemSize;\n        const newarray = attrArrays[name];\n        const newMorphArrays = morphAttrsArrays[name];\n        for (let k = 0; k < itemSize; k++) {\n          const getterFunc = getters[k];\n          newarray.push(attribute[getterFunc](index));\n          if (morphAttr) {\n            for (let m = 0, ml = morphAttr.length; m < ml; m++) {\n              newMorphArrays[m].push(morphAttr[m][getterFunc](index));\n            }\n          }\n        }\n      }\n      hashToIndex[hash] = nextIndex;\n      newIndices.push(nextIndex);\n      nextIndex++;\n    }\n  }\n  const result = geometry.clone();\n  for (let i = 0, l = attributeNames.length; i < l; i++) {\n    const name = attributeNames[i];\n    const oldAttribute = geometry.getAttribute(name);\n    const buffer = new oldAttribute.array.constructor(attrArrays[name]);\n    const attribute = new BufferAttribute(buffer, oldAttribute.itemSize, oldAttribute.normalized);\n    result.setAttribute(name, attribute);\n    if (name in morphAttrsArrays) {\n      for (let j = 0; j < morphAttrsArrays[name].length; j++) {\n        const oldMorphAttribute = geometry.morphAttributes[name][j];\n        const buffer2 = new oldMorphAttribute.array.constructor(morphAttrsArrays[name][j]);\n        const morphAttribute = new BufferAttribute(buffer2, oldMorphAttribute.itemSize, oldMorphAttribute.normalized);\n        result.morphAttributes[name][j] = morphAttribute;\n      }\n    }\n  }\n  result.setIndex(newIndices);\n  return result;\n}\nfunction toTrianglesDrawMode(geometry, drawMode) {\n  if (drawMode === TrianglesDrawMode) {\n    console.warn(\"THREE.BufferGeometryUtils.toTrianglesDrawMode(): Geometry already defined as triangles.\");\n    return geometry;\n  }\n  if (drawMode === TriangleFanDrawMode || drawMode === TriangleStripDrawMode) {\n    let index = geometry.getIndex();\n    if (index === null) {\n      const indices = [];\n      const position = geometry.getAttribute(\"position\");\n      if (position !== void 0) {\n        for (let i = 0; i < position.count; i++) {\n          indices.push(i);\n        }\n        geometry.setIndex(indices);\n        index = geometry.getIndex();\n      } else {\n        console.error(\"THREE.BufferGeometryUtils.toTrianglesDrawMode(): Undefined position attribute. Processing not possible.\");\n        return geometry;\n      }\n    }\n    const numberOfTriangles = index.count - 2;\n    const newIndices = [];\n    if (index) {\n      if (drawMode === TriangleFanDrawMode) {\n        for (let i = 1; i <= numberOfTriangles; i++) {\n          newIndices.push(index.getX(0));\n          newIndices.push(index.getX(i));\n          newIndices.push(index.getX(i + 1));\n        }\n      } else {\n        for (let i = 0; i < numberOfTriangles; i++) {\n          if (i % 2 === 0) {\n            newIndices.push(index.getX(i));\n            newIndices.push(index.getX(i + 1));\n            newIndices.push(index.getX(i + 2));\n          } else {\n            newIndices.push(index.getX(i + 2));\n            newIndices.push(index.getX(i + 1));\n            newIndices.push(index.getX(i));\n          }\n        }\n      }\n    }\n    if (newIndices.length / 3 !== numberOfTriangles) {\n      console.error(\"THREE.BufferGeometryUtils.toTrianglesDrawMode(): Unable to generate correct amount of triangles.\");\n    }\n    const newGeometry = geometry.clone();\n    newGeometry.setIndex(newIndices);\n    newGeometry.clearGroups();\n    return newGeometry;\n  } else {\n    console.error(\"THREE.BufferGeometryUtils.toTrianglesDrawMode(): Unknown draw mode:\", drawMode);\n    return geometry;\n  }\n}\nfunction computeMorphedAttributes(object) {\n  if (object.geometry.isBufferGeometry !== true) {\n    console.error(\"THREE.BufferGeometryUtils: Geometry is not of type BufferGeometry.\");\n    return null;\n  }\n  const _vA = new Vector3();\n  const _vB = new Vector3();\n  const _vC = new Vector3();\n  const _tempA = new Vector3();\n  const _tempB = new Vector3();\n  const _tempC = new Vector3();\n  const _morphA = new Vector3();\n  const _morphB = new Vector3();\n  const _morphC = new Vector3();\n  function _calculateMorphedAttributeData(object2, material2, attribute, morphAttribute, morphTargetsRelative2, a2, b2, c2, modifiedAttributeArray) {\n    _vA.fromBufferAttribute(attribute, a2);\n    _vB.fromBufferAttribute(attribute, b2);\n    _vC.fromBufferAttribute(attribute, c2);\n    const morphInfluences = object2.morphTargetInfluences;\n    if (\n    // @ts-ignore\n    material2.morphTargets && morphAttribute && morphInfluences) {\n      _morphA.set(0, 0, 0);\n      _morphB.set(0, 0, 0);\n      _morphC.set(0, 0, 0);\n      for (let i2 = 0, il2 = morphAttribute.length; i2 < il2; i2++) {\n        const influence = morphInfluences[i2];\n        const morph = morphAttribute[i2];\n        if (influence === 0) continue;\n        _tempA.fromBufferAttribute(morph, a2);\n        _tempB.fromBufferAttribute(morph, b2);\n        _tempC.fromBufferAttribute(morph, c2);\n        if (morphTargetsRelative2) {\n          _morphA.addScaledVector(_tempA, influence);\n          _morphB.addScaledVector(_tempB, influence);\n          _morphC.addScaledVector(_tempC, influence);\n        } else {\n          _morphA.addScaledVector(_tempA.sub(_vA), influence);\n          _morphB.addScaledVector(_tempB.sub(_vB), influence);\n          _morphC.addScaledVector(_tempC.sub(_vC), influence);\n        }\n      }\n      _vA.add(_morphA);\n      _vB.add(_morphB);\n      _vC.add(_morphC);\n    }\n    if (object2.isSkinnedMesh) {\n      object2.boneTransform(a2, _vA);\n      object2.boneTransform(b2, _vB);\n      object2.boneTransform(c2, _vC);\n    }\n    modifiedAttributeArray[a2 * 3 + 0] = _vA.x;\n    modifiedAttributeArray[a2 * 3 + 1] = _vA.y;\n    modifiedAttributeArray[a2 * 3 + 2] = _vA.z;\n    modifiedAttributeArray[b2 * 3 + 0] = _vB.x;\n    modifiedAttributeArray[b2 * 3 + 1] = _vB.y;\n    modifiedAttributeArray[b2 * 3 + 2] = _vB.z;\n    modifiedAttributeArray[c2 * 3 + 0] = _vC.x;\n    modifiedAttributeArray[c2 * 3 + 1] = _vC.y;\n    modifiedAttributeArray[c2 * 3 + 2] = _vC.z;\n  }\n  const geometry = object.geometry;\n  const material = object.material;\n  let a, b, c;\n  const index = geometry.index;\n  const positionAttribute = geometry.attributes.position;\n  const morphPosition = geometry.morphAttributes.position;\n  const morphTargetsRelative = geometry.morphTargetsRelative;\n  const normalAttribute = geometry.attributes.normal;\n  const morphNormal = geometry.morphAttributes.position;\n  const groups = geometry.groups;\n  const drawRange = geometry.drawRange;\n  let i, j, il, jl;\n  let group, groupMaterial;\n  let start, end;\n  const modifiedPosition = new Float32Array(positionAttribute.count * positionAttribute.itemSize);\n  const modifiedNormal = new Float32Array(normalAttribute.count * normalAttribute.itemSize);\n  if (index !== null) {\n    if (Array.isArray(material)) {\n      for (i = 0, il = groups.length; i < il; i++) {\n        group = groups[i];\n        groupMaterial = material[group.materialIndex];\n        start = Math.max(group.start, drawRange.start);\n        end = Math.min(group.start + group.count, drawRange.start + drawRange.count);\n        for (j = start, jl = end; j < jl; j += 3) {\n          a = index.getX(j);\n          b = index.getX(j + 1);\n          c = index.getX(j + 2);\n          _calculateMorphedAttributeData(object, groupMaterial, positionAttribute, morphPosition, morphTargetsRelative, a, b, c, modifiedPosition);\n          _calculateMorphedAttributeData(object, groupMaterial, normalAttribute, morphNormal, morphTargetsRelative, a, b, c, modifiedNormal);\n        }\n      }\n    } else {\n      start = Math.max(0, drawRange.start);\n      end = Math.min(index.count, drawRange.start + drawRange.count);\n      for (i = start, il = end; i < il; i += 3) {\n        a = index.getX(i);\n        b = index.getX(i + 1);\n        c = index.getX(i + 2);\n        _calculateMorphedAttributeData(object, material, positionAttribute, morphPosition, morphTargetsRelative, a, b, c, modifiedPosition);\n        _calculateMorphedAttributeData(object, material, normalAttribute, morphNormal, morphTargetsRelative, a, b, c, modifiedNormal);\n      }\n    }\n  } else if (positionAttribute !== void 0) {\n    if (Array.isArray(material)) {\n      for (i = 0, il = groups.length; i < il; i++) {\n        group = groups[i];\n        groupMaterial = material[group.materialIndex];\n        start = Math.max(group.start, drawRange.start);\n        end = Math.min(group.start + group.count, drawRange.start + drawRange.count);\n        for (j = start, jl = end; j < jl; j += 3) {\n          a = j;\n          b = j + 1;\n          c = j + 2;\n          _calculateMorphedAttributeData(object, groupMaterial, positionAttribute, morphPosition, morphTargetsRelative, a, b, c, modifiedPosition);\n          _calculateMorphedAttributeData(object, groupMaterial, normalAttribute, morphNormal, morphTargetsRelative, a, b, c, modifiedNormal);\n        }\n      }\n    } else {\n      start = Math.max(0, drawRange.start);\n      end = Math.min(positionAttribute.count, drawRange.start + drawRange.count);\n      for (i = start, il = end; i < il; i += 3) {\n        a = i;\n        b = i + 1;\n        c = i + 2;\n        _calculateMorphedAttributeData(object, material, positionAttribute, morphPosition, morphTargetsRelative, a, b, c, modifiedPosition);\n        _calculateMorphedAttributeData(object, material, normalAttribute, morphNormal, morphTargetsRelative, a, b, c, modifiedNormal);\n      }\n    }\n  }\n  const morphedPositionAttribute = new Float32BufferAttribute(modifiedPosition, 3);\n  const morphedNormalAttribute = new Float32BufferAttribute(modifiedNormal, 3);\n  return {\n    positionAttribute,\n    normalAttribute,\n    morphedPositionAttribute,\n    morphedNormalAttribute\n  };\n}\nfunction toCreasedNormals(geometry, creaseAngle = Math.PI / 3) {\n  const creaseDot = Math.cos(creaseAngle);\n  const hashMultiplier = (1 + 1e-10) * 100;\n  const verts = [new Vector3(), new Vector3(), new Vector3()];\n  const tempVec1 = new Vector3();\n  const tempVec2 = new Vector3();\n  const tempNorm = new Vector3();\n  const tempNorm2 = new Vector3();\n  function hashVertex(v) {\n    const x = ~~(v.x * hashMultiplier);\n    const y = ~~(v.y * hashMultiplier);\n    const z = ~~(v.z * hashMultiplier);\n    return `${x},${y},${z}`;\n  }\n  const resultGeometry = geometry.index ? geometry.toNonIndexed() : geometry;\n  const posAttr = resultGeometry.attributes.position;\n  const vertexMap = {};\n  for (let i = 0, l = posAttr.count / 3; i < l; i++) {\n    const i3 = 3 * i;\n    const a = verts[0].fromBufferAttribute(posAttr, i3 + 0);\n    const b = verts[1].fromBufferAttribute(posAttr, i3 + 1);\n    const c = verts[2].fromBufferAttribute(posAttr, i3 + 2);\n    tempVec1.subVectors(c, b);\n    tempVec2.subVectors(a, b);\n    const normal = new Vector3().crossVectors(tempVec1, tempVec2).normalize();\n    for (let n = 0; n < 3; n++) {\n      const vert = verts[n];\n      const hash = hashVertex(vert);\n      if (!(hash in vertexMap)) {\n        vertexMap[hash] = [];\n      }\n      vertexMap[hash].push(normal);\n    }\n  }\n  const normalArray = new Float32Array(posAttr.count * 3);\n  const normAttr = new BufferAttribute(normalArray, 3, false);\n  for (let i = 0, l = posAttr.count / 3; i < l; i++) {\n    const i3 = 3 * i;\n    const a = verts[0].fromBufferAttribute(posAttr, i3 + 0);\n    const b = verts[1].fromBufferAttribute(posAttr, i3 + 1);\n    const c = verts[2].fromBufferAttribute(posAttr, i3 + 2);\n    tempVec1.subVectors(c, b);\n    tempVec2.subVectors(a, b);\n    tempNorm.crossVectors(tempVec1, tempVec2).normalize();\n    for (let n = 0; n < 3; n++) {\n      const vert = verts[n];\n      const hash = hashVertex(vert);\n      const otherNormals = vertexMap[hash];\n      tempNorm2.set(0, 0, 0);\n      for (let k = 0, lk = otherNormals.length; k < lk; k++) {\n        const otherNorm = otherNormals[k];\n        if (tempNorm.dot(otherNorm) > creaseDot) {\n          tempNorm2.add(otherNorm);\n        }\n      }\n      tempNorm2.normalize();\n      normAttr.setXYZ(i3 + n, tempNorm2.x, tempNorm2.y, tempNorm2.z);\n    }\n  }\n  resultGeometry.setAttribute(\"normal\", normAttr);\n  return resultGeometry;\n}\nexport { computeMorphedAttributes, estimateBytesUsed, interleaveAttributes, mergeBufferAttributes, mergeBufferGeometries, mergeVertices, toCreasedNormals, toTrianglesDrawMode };", "map": {"version": 3, "names": ["mergeBufferGeometries", "geometries", "useGroups", "isIndexed", "index", "attributesUsed", "Set", "Object", "keys", "attributes", "morphAttributesUsed", "morphAttributes", "morphTargetsRelative", "mergedGeometry", "BufferGeometry", "offset", "for<PERSON>ach", "geom", "i", "attributesCount", "console", "error", "name", "has", "push", "size", "userData", "mergedUserData", "count", "position", "addGroup", "indexOffset", "mergedIndex", "j", "getX", "setIndex", "mergedAttribute", "mergeBufferAttributes", "setAttribute", "numMorphTargets", "length", "morphAttributesToMerge", "mergedMorphAttribute", "TypedArray", "itemSize", "normalized", "array<PERSON>ength", "attr", "array", "constructor", "set", "BufferAttribute", "interleaveAttributes", "stride", "l", "attribute", "interleavedBuffer", "InterleavedBuffer", "res", "getters", "setters", "iba", "InterleavedBufferAttribute", "c", "k", "getWithKey", "get", "estimateBytesUsed", "geometry", "mem", "getAttribute", "BYTES_PER_ELEMENT", "indices", "getIndex", "mergeVertices", "tolerance", "Math", "max", "Number", "EPSILON", "hashToIndex", "positions", "vertexCount", "nextIndex", "attributeNames", "attrArrays", "morphAttrsArrays", "newIndices", "morphAttr", "Array", "fill", "map", "decimalShift", "log10", "shiftMultiplier", "pow", "hash", "newarray", "newMorphArrays", "getterFunc", "m", "ml", "result", "clone", "oldAttribute", "buffer", "oldMorphAttribute", "buffer2", "morphAttribute", "toTrianglesDrawMode", "drawMode", "TrianglesDrawMode", "warn", "TriangleFanDrawMode", "TriangleStripDrawMode", "numberOfTriangles", "newGeometry", "clearGroups", "computeMorphedAttributes", "object", "isBufferGeometry", "_vA", "Vector3", "_vB", "_vC", "_tempA", "_tempB", "_tempC", "_morphA", "_morphB", "_morphC", "_calculateMorphedAttributeData", "object2", "material2", "morphTargetsRelative2", "a2", "b2", "c2", "modifiedAttributeArray", "fromBufferAttribute", "morphInfluences", "morphTargetInfluences", "morphTargets", "i2", "il2", "influence", "morph", "addScaledVector", "sub", "add", "isSkinnedMesh", "boneTransform", "x", "y", "z", "material", "a", "b", "positionAttribute", "morphPosition", "normalAttribute", "normal", "morphNormal", "groups", "drawRange", "il", "jl", "group", "groupMaterial", "start", "end", "modifiedPosition", "Float32Array", "modifiedNormal", "isArray", "materialIndex", "min", "morphedPositionAttribute", "Float32BufferAttribute", "morphedNormalAttribute", "toCreasedNormals", "creaseAngle", "PI", "creaseDot", "cos", "hashMultiplier", "verts", "tempVec1", "tempVec2", "tempNorm", "tempNorm2", "hashVertex", "v", "resultGeometry", "toNonIndexed", "posAttr", "vertexMap", "i3", "subVectors", "crossVectors", "normalize", "n", "vert", "normalArray", "norm<PERSON>ttr", "otherNormals", "lk", "otherNorm", "dot", "setXYZ"], "sources": ["C:\\Users\\<USER>\\Documents\\Apps_Creations\\SiahMan_Racing4Speed_WebGame\\siah-man-racing\\node_modules\\src\\utils\\BufferGeometryUtils.ts"], "sourcesContent": ["import {\n  BufferAttribute,\n  BufferGeometry,\n  Float32BufferAttribute,\n  InterleavedBuffer,\n  InterleavedBufferAttribute,\n  TriangleFanDrawMode,\n  TriangleStripDrawMode,\n  TrianglesDrawMode,\n  Vector3,\n  Mesh,\n  Line,\n  Points,\n  Material,\n  SkinnedMesh,\n} from 'three'\n\nimport { getWithKey } from '../types/helpers'\nimport type { TypedArrayConstructors, TypedArray } from '../types/shared'\n\n/**\n * @param  {Array<BufferGeometry>} geometries\n * @param  {Boolean} useGroups\n * @return {BufferGeometry}\n */\nexport const mergeBufferGeometries = (geometries: BufferGeometry[], useGroups?: boolean): BufferGeometry | null => {\n  const isIndexed = geometries[0].index !== null\n\n  const attributesUsed = new Set(Object.keys(geometries[0].attributes))\n  const morphAttributesUsed = new Set(Object.keys(geometries[0].morphAttributes))\n\n  const attributes: { [key: string]: Array<InterleavedBufferAttribute | BufferAttribute> } = {}\n  const morphAttributes: { [key: string]: Array<BufferAttribute | InterleavedBufferAttribute>[] } = {}\n\n  const morphTargetsRelative = geometries[0].morphTargetsRelative\n\n  const mergedGeometry = new BufferGeometry()\n\n  let offset = 0\n\n  geometries.forEach((geom, i) => {\n    let attributesCount = 0\n\n    // ensure that all geometries are indexed, or none\n\n    if (isIndexed !== (geom.index !== null)) {\n      console.error(\n        'THREE.BufferGeometryUtils: .mergeBufferGeometries() failed with geometry at index ' +\n          i +\n          '. All geometries must have compatible attributes; make sure index attribute exists among all geometries, or in none of them.',\n      )\n      return null\n    }\n\n    // gather attributes, exit early if they're different\n\n    for (let name in geom.attributes) {\n      if (!attributesUsed.has(name)) {\n        console.error(\n          'THREE.BufferGeometryUtils: .mergeBufferGeometries() failed with geometry at index ' +\n            i +\n            '. All geometries must have compatible attributes; make sure \"' +\n            name +\n            '\" attribute exists among all geometries, or in none of them.',\n        )\n        return null\n      }\n\n      if (attributes[name] === undefined) {\n        attributes[name] = []\n      }\n\n      attributes[name].push(geom.attributes[name])\n\n      attributesCount++\n    }\n\n    // ensure geometries have the same number of attributes\n\n    if (attributesCount !== attributesUsed.size) {\n      console.error(\n        'THREE.BufferGeometryUtils: .mergeBufferGeometries() failed with geometry at index ' +\n          i +\n          '. Make sure all geometries have the same number of attributes.',\n      )\n      return null\n    }\n\n    // gather morph attributes, exit early if they're different\n\n    if (morphTargetsRelative !== geom.morphTargetsRelative) {\n      console.error(\n        'THREE.BufferGeometryUtils: .mergeBufferGeometries() failed with geometry at index ' +\n          i +\n          '. .morphTargetsRelative must be consistent throughout all geometries.',\n      )\n      return null\n    }\n\n    for (let name in geom.morphAttributes) {\n      if (!morphAttributesUsed.has(name)) {\n        console.error(\n          'THREE.BufferGeometryUtils: .mergeBufferGeometries() failed with geometry at index ' +\n            i +\n            '.  .morphAttributes must be consistent throughout all geometries.',\n        )\n        return null\n      }\n\n      if (morphAttributes[name] === undefined) morphAttributes[name] = []\n\n      morphAttributes[name].push(geom.morphAttributes[name])\n    }\n\n    // gather .userData\n\n    mergedGeometry.userData.mergedUserData = mergedGeometry.userData.mergedUserData || []\n    mergedGeometry.userData.mergedUserData.push(geom.userData)\n\n    if (useGroups) {\n      let count\n\n      if (geom.index) {\n        count = geom.index.count\n      } else if (geom.attributes.position !== undefined) {\n        count = geom.attributes.position.count\n      } else {\n        console.error(\n          'THREE.BufferGeometryUtils: .mergeBufferGeometries() failed with geometry at index ' +\n            i +\n            '. The geometry must have either an index or a position attribute',\n        )\n        return null\n      }\n\n      mergedGeometry.addGroup(offset, count, i)\n\n      offset += count\n    }\n  })\n\n  // merge indices\n\n  if (isIndexed) {\n    let indexOffset = 0\n    const mergedIndex: number[] = []\n\n    geometries.forEach((geom) => {\n      const index = geom.index as BufferAttribute\n\n      for (let j = 0; j < index.count; ++j) {\n        mergedIndex.push(index.getX(j) + indexOffset)\n      }\n\n      indexOffset += geom.attributes.position.count\n    })\n\n    mergedGeometry.setIndex(mergedIndex)\n  }\n\n  // merge attributes\n\n  for (let name in attributes) {\n    const mergedAttribute = mergeBufferAttributes(attributes[name] as BufferAttribute[])\n\n    if (!mergedAttribute) {\n      console.error(\n        'THREE.BufferGeometryUtils: .mergeBufferGeometries() failed while trying to merge the ' + name + ' attribute.',\n      )\n      return null\n    }\n\n    mergedGeometry.setAttribute(name, mergedAttribute)\n  }\n\n  // merge morph attributes\n\n  for (let name in morphAttributes) {\n    const numMorphTargets = morphAttributes[name][0].length\n\n    if (numMorphTargets === 0) break\n\n    mergedGeometry.morphAttributes = mergedGeometry.morphAttributes || {}\n    mergedGeometry.morphAttributes[name] = []\n\n    for (let i = 0; i < numMorphTargets; ++i) {\n      const morphAttributesToMerge = []\n\n      for (let j = 0; j < morphAttributes[name].length; ++j) {\n        morphAttributesToMerge.push(morphAttributes[name][j][i])\n      }\n\n      const mergedMorphAttribute = mergeBufferAttributes(morphAttributesToMerge as BufferAttribute[])\n\n      if (!mergedMorphAttribute) {\n        console.error(\n          'THREE.BufferGeometryUtils: .mergeBufferGeometries() failed while trying to merge the ' +\n            name +\n            ' morphAttribute.',\n        )\n        return null\n      }\n\n      mergedGeometry.morphAttributes[name].push(mergedMorphAttribute)\n    }\n  }\n\n  return mergedGeometry\n}\n\n/**\n * @param {Array<BufferAttribute>} attributes\n * @return {BufferAttribute}\n */\nexport const mergeBufferAttributes = (attributes: BufferAttribute[]): BufferAttribute | null | undefined => {\n  let TypedArray: TypedArrayConstructors | undefined = undefined\n  let itemSize: number | undefined = undefined\n  let normalized: boolean | undefined = undefined\n  let arrayLength = 0\n\n  attributes.forEach((attr) => {\n    if (TypedArray === undefined) {\n      TypedArray = attr.array.constructor\n    }\n    if (TypedArray !== attr.array.constructor) {\n      console.error(\n        'THREE.BufferGeometryUtils: .mergeBufferAttributes() failed. BufferAttribute.array must be of consistent array types across matching attributes.',\n      )\n      return null\n    }\n\n    if (itemSize === undefined) itemSize = attr.itemSize\n    if (itemSize !== attr.itemSize) {\n      console.error(\n        'THREE.BufferGeometryUtils: .mergeBufferAttributes() failed. BufferAttribute.itemSize must be consistent across matching attributes.',\n      )\n      return null\n    }\n\n    if (normalized === undefined) normalized = attr.normalized\n    if (normalized !== attr.normalized) {\n      console.error(\n        'THREE.BufferGeometryUtils: .mergeBufferAttributes() failed. BufferAttribute.normalized must be consistent across matching attributes.',\n      )\n      return null\n    }\n\n    arrayLength += attr.array.length\n  })\n\n  if (TypedArray && itemSize) {\n    // @ts-ignore this works in JS and TS is complaining but it's such a tiny thing I can live with the guilt\n    const array = new TypedArray(arrayLength)\n    let offset = 0\n\n    attributes.forEach((attr) => {\n      array.set(attr.array, offset)\n      offset += attr.array.length\n    })\n\n    return new BufferAttribute(array, itemSize, normalized)\n  }\n}\n\n/**\n * @param {Array<BufferAttribute>} attributes\n * @return {Array<InterleavedBufferAttribute>}\n */\nexport const interleaveAttributes = (attributes: BufferAttribute[]): InterleavedBufferAttribute[] | null => {\n  // Interleaves the provided attributes into an InterleavedBuffer and returns\n  // a set of InterleavedBufferAttributes for each attribute\n  let TypedArray: TypedArrayConstructors | undefined = undefined\n  let arrayLength = 0\n  let stride = 0\n\n  // calculate the the length and type of the interleavedBuffer\n  for (let i = 0, l = attributes.length; i < l; ++i) {\n    const attribute = attributes[i]\n\n    if (TypedArray === undefined) TypedArray = attribute.array.constructor\n    if (TypedArray !== attribute.array.constructor) {\n      console.error('AttributeBuffers of different types cannot be interleaved')\n      return null\n    }\n\n    arrayLength += attribute.array.length\n    stride += attribute.itemSize\n  }\n\n  // Create the set of buffer attributes\n  // @ts-ignore this works in JS and TS is complaining but it's such a tiny thing I can live with the guilt\n  const interleavedBuffer = new InterleavedBuffer(new TypedArray(arrayLength), stride)\n  let offset = 0\n  const res = []\n  const getters = ['getX', 'getY', 'getZ', 'getW']\n  const setters = ['setX', 'setY', 'setZ', 'setW']\n\n  for (let j = 0, l = attributes.length; j < l; j++) {\n    const attribute = attributes[j]\n    const itemSize = attribute.itemSize\n    const count = attribute.count\n    const iba = new InterleavedBufferAttribute(interleavedBuffer, itemSize, offset, attribute.normalized)\n    res.push(iba)\n\n    offset += itemSize\n\n    // Move the data for each attribute into the new interleavedBuffer\n    // at the appropriate offset\n    for (let c = 0; c < count; c++) {\n      for (let k = 0; k < itemSize; k++) {\n        const set = getWithKey(iba, setters[k] as keyof InterleavedBufferAttribute) as InterleavedBufferAttribute[\n          | 'setX'\n          | 'setY'\n          | 'setZ'\n          | 'setW']\n        const get = getWithKey(attribute, getters[k] as keyof BufferAttribute) as BufferAttribute[\n          | 'getX'\n          | 'getY'\n          | 'getZ'\n          | 'getW']\n        set(c, get(c))\n      }\n    }\n  }\n\n  return res\n}\n\n/**\n * @param {Array<BufferGeometry>} geometry\n * @return {number}\n */\nexport function estimateBytesUsed(geometry: BufferGeometry): number {\n  // Return the estimated memory used by this geometry in bytes\n  // Calculate using itemSize, count, and BYTES_PER_ELEMENT to account\n  // for InterleavedBufferAttributes.\n  let mem = 0\n  for (let name in geometry.attributes) {\n    const attr = geometry.getAttribute(name)\n    mem += attr.count * attr.itemSize * (attr.array as TypedArray).BYTES_PER_ELEMENT\n  }\n\n  const indices = geometry.getIndex()\n  mem += indices ? indices.count * indices.itemSize * (indices.array as TypedArray).BYTES_PER_ELEMENT : 0\n  return mem\n}\n\n/**\n * @param {BufferGeometry} geometry\n * @param {number} tolerance\n * @return {BufferGeometry>}\n */\nexport function mergeVertices(geometry: BufferGeometry, tolerance = 1e-4): BufferGeometry {\n  tolerance = Math.max(tolerance, Number.EPSILON)\n\n  // Generate an index buffer if the geometry doesn't have one, or optimize it\n  // if it's already available.\n  const hashToIndex: {\n    [key: string]: number\n  } = {}\n  const indices = geometry.getIndex()\n  const positions = geometry.getAttribute('position')\n  const vertexCount = indices ? indices.count : positions.count\n\n  // next value for triangle indices\n  let nextIndex = 0\n\n  // attributes and new attribute arrays\n  const attributeNames = Object.keys(geometry.attributes)\n  const attrArrays: {\n    [key: string]: []\n  } = {}\n  const morphAttrsArrays: {\n    [key: string]: Array<Array<BufferAttribute | InterleavedBufferAttribute>>\n  } = {}\n  const newIndices = []\n  const getters = ['getX', 'getY', 'getZ', 'getW']\n\n  // initialize the arrays\n  for (let i = 0, l = attributeNames.length; i < l; i++) {\n    const name = attributeNames[i]\n\n    attrArrays[name] = []\n\n    const morphAttr = geometry.morphAttributes[name]\n    if (morphAttr) {\n      morphAttrsArrays[name] = new Array(morphAttr.length).fill(0).map(() => [])\n    }\n  }\n\n  // convert the error tolerance to an amount of decimal places to truncate to\n  const decimalShift = Math.log10(1 / tolerance)\n  const shiftMultiplier = Math.pow(10, decimalShift)\n  for (let i = 0; i < vertexCount; i++) {\n    const index = indices ? indices.getX(i) : i\n\n    // Generate a hash for the vertex attributes at the current index 'i'\n    let hash = ''\n    for (let j = 0, l = attributeNames.length; j < l; j++) {\n      const name = attributeNames[j]\n      const attribute = geometry.getAttribute(name)\n      const itemSize = attribute.itemSize\n\n      for (let k = 0; k < itemSize; k++) {\n        // double tilde truncates the decimal value\n        // @ts-ignore no\n        hash += `${~~(attribute[getters[k]](index) * shiftMultiplier)},`\n      }\n    }\n\n    // Add another reference to the vertex if it's already\n    // used by another index\n    if (hash in hashToIndex) {\n      newIndices.push(hashToIndex[hash])\n    } else {\n      // copy data to the new index in the attribute arrays\n      for (let j = 0, l = attributeNames.length; j < l; j++) {\n        const name = attributeNames[j]\n        const attribute = geometry.getAttribute(name)\n        const morphAttr = geometry.morphAttributes[name]\n        const itemSize = attribute.itemSize\n        const newarray = attrArrays[name]\n        const newMorphArrays = morphAttrsArrays[name]\n\n        for (let k = 0; k < itemSize; k++) {\n          const getterFunc = getters[k]\n          // @ts-ignore\n          newarray.push(attribute[getterFunc](index))\n\n          if (morphAttr) {\n            for (let m = 0, ml = morphAttr.length; m < ml; m++) {\n              // @ts-ignore\n              newMorphArrays[m].push(morphAttr[m][getterFunc](index))\n            }\n          }\n        }\n      }\n\n      hashToIndex[hash] = nextIndex\n      newIndices.push(nextIndex)\n      nextIndex++\n    }\n  }\n\n  // Generate typed arrays from new attribute arrays and update\n  // the attributeBuffers\n  const result = geometry.clone()\n  for (let i = 0, l = attributeNames.length; i < l; i++) {\n    const name = attributeNames[i]\n    const oldAttribute = geometry.getAttribute(name)\n    //@ts-expect-error  something to do with functions and constructors and new\n    const buffer = new (oldAttribute.array as TypedArray).constructor(attrArrays[name])\n    const attribute = new BufferAttribute(buffer, oldAttribute.itemSize, oldAttribute.normalized)\n\n    result.setAttribute(name, attribute)\n\n    // Update the attribute arrays\n    if (name in morphAttrsArrays) {\n      for (let j = 0; j < morphAttrsArrays[name].length; j++) {\n        const oldMorphAttribute = geometry.morphAttributes[name][j]\n        //@ts-expect-error something to do with functions and constructors and new\n        const buffer = new (oldMorphAttribute.array as TypedArray).constructor(morphAttrsArrays[name][j])\n        const morphAttribute = new BufferAttribute(buffer, oldMorphAttribute.itemSize, oldMorphAttribute.normalized)\n        result.morphAttributes[name][j] = morphAttribute\n      }\n    }\n  }\n\n  // indices\n\n  result.setIndex(newIndices)\n\n  return result\n}\n\n/**\n * @param {BufferGeometry} geometry\n * @param {number} drawMode\n * @return {BufferGeometry}\n */\nexport function toTrianglesDrawMode(geometry: BufferGeometry, drawMode: number): BufferGeometry {\n  if (drawMode === TrianglesDrawMode) {\n    console.warn('THREE.BufferGeometryUtils.toTrianglesDrawMode(): Geometry already defined as triangles.')\n    return geometry\n  }\n\n  if (drawMode === TriangleFanDrawMode || drawMode === TriangleStripDrawMode) {\n    let index = geometry.getIndex()\n\n    // generate index if not present\n\n    if (index === null) {\n      const indices = []\n\n      const position = geometry.getAttribute('position')\n\n      if (position !== undefined) {\n        for (let i = 0; i < position.count; i++) {\n          indices.push(i)\n        }\n\n        geometry.setIndex(indices)\n        index = geometry.getIndex()\n      } else {\n        console.error(\n          'THREE.BufferGeometryUtils.toTrianglesDrawMode(): Undefined position attribute. Processing not possible.',\n        )\n        return geometry\n      }\n    }\n\n    //\n\n    const numberOfTriangles = (index as BufferAttribute).count - 2\n    const newIndices = []\n\n    if (index) {\n      if (drawMode === TriangleFanDrawMode) {\n        // gl.TRIANGLE_FAN\n\n        for (let i = 1; i <= numberOfTriangles; i++) {\n          newIndices.push(index.getX(0))\n          newIndices.push(index.getX(i))\n          newIndices.push(index.getX(i + 1))\n        }\n      } else {\n        // gl.TRIANGLE_STRIP\n\n        for (let i = 0; i < numberOfTriangles; i++) {\n          if (i % 2 === 0) {\n            newIndices.push(index.getX(i))\n            newIndices.push(index.getX(i + 1))\n            newIndices.push(index.getX(i + 2))\n          } else {\n            newIndices.push(index.getX(i + 2))\n            newIndices.push(index.getX(i + 1))\n            newIndices.push(index.getX(i))\n          }\n        }\n      }\n    }\n\n    if (newIndices.length / 3 !== numberOfTriangles) {\n      console.error('THREE.BufferGeometryUtils.toTrianglesDrawMode(): Unable to generate correct amount of triangles.')\n    }\n\n    // build final geometry\n\n    const newGeometry = geometry.clone()\n    newGeometry.setIndex(newIndices)\n    newGeometry.clearGroups()\n\n    return newGeometry\n  } else {\n    console.error('THREE.BufferGeometryUtils.toTrianglesDrawMode(): Unknown draw mode:', drawMode)\n    return geometry\n  }\n}\n\n/**\n * Calculates the morphed attributes of a morphed/skinned BufferGeometry.\n * Helpful for Raytracing or Decals.\n * @param {Mesh | Line | Points} object An instance of Mesh, Line or Points.\n * @return {Object} An Object with original position/normal attributes and morphed ones.\n */\nexport type ComputedMorphedAttribute = {\n  positionAttribute: BufferAttribute | InterleavedBufferAttribute\n  normalAttribute: BufferAttribute | InterleavedBufferAttribute\n  morphedPositionAttribute: Float32BufferAttribute\n  morphedNormalAttribute: Float32BufferAttribute\n}\n\nexport function computeMorphedAttributes(object: Mesh | Line | Points): ComputedMorphedAttribute | null {\n  if (object.geometry.isBufferGeometry !== true) {\n    console.error('THREE.BufferGeometryUtils: Geometry is not of type BufferGeometry.')\n    return null\n  }\n\n  const _vA = new Vector3()\n  const _vB = new Vector3()\n  const _vC = new Vector3()\n\n  const _tempA = new Vector3()\n  const _tempB = new Vector3()\n  const _tempC = new Vector3()\n\n  const _morphA = new Vector3()\n  const _morphB = new Vector3()\n  const _morphC = new Vector3()\n\n  function _calculateMorphedAttributeData(\n    object: Mesh | Line | Points,\n    material: Material,\n    attribute: BufferAttribute | InterleavedBufferAttribute,\n    morphAttribute: (BufferAttribute | InterleavedBufferAttribute)[],\n    morphTargetsRelative: boolean,\n    a: number,\n    b: number,\n    c: number,\n    modifiedAttributeArray: Float32Array,\n  ): void {\n    _vA.fromBufferAttribute(attribute, a)\n    _vB.fromBufferAttribute(attribute, b)\n    _vC.fromBufferAttribute(attribute, c)\n\n    const morphInfluences = object.morphTargetInfluences\n\n    if (\n      // @ts-ignore\n      material.morphTargets &&\n      morphAttribute &&\n      morphInfluences\n    ) {\n      _morphA.set(0, 0, 0)\n      _morphB.set(0, 0, 0)\n      _morphC.set(0, 0, 0)\n\n      for (let i = 0, il = morphAttribute.length; i < il; i++) {\n        const influence = morphInfluences[i]\n        const morph = morphAttribute[i]\n\n        if (influence === 0) continue\n\n        _tempA.fromBufferAttribute(morph, a)\n        _tempB.fromBufferAttribute(morph, b)\n        _tempC.fromBufferAttribute(morph, c)\n\n        if (morphTargetsRelative) {\n          _morphA.addScaledVector(_tempA, influence)\n          _morphB.addScaledVector(_tempB, influence)\n          _morphC.addScaledVector(_tempC, influence)\n        } else {\n          _morphA.addScaledVector(_tempA.sub(_vA), influence)\n          _morphB.addScaledVector(_tempB.sub(_vB), influence)\n          _morphC.addScaledVector(_tempC.sub(_vC), influence)\n        }\n      }\n\n      _vA.add(_morphA)\n      _vB.add(_morphB)\n      _vC.add(_morphC)\n    }\n\n    if ((object as SkinnedMesh).isSkinnedMesh) {\n      // @ts-ignore – https://github.com/three-types/three-ts-types/issues/37\n      object.boneTransform(a, _vA)\n      // @ts-ignore – https://github.com/three-types/three-ts-types/issues/37\n      object.boneTransform(b, _vB)\n      // @ts-ignore – https://github.com/three-types/three-ts-types/issues/37\n      object.boneTransform(c, _vC)\n    }\n\n    modifiedAttributeArray[a * 3 + 0] = _vA.x\n    modifiedAttributeArray[a * 3 + 1] = _vA.y\n    modifiedAttributeArray[a * 3 + 2] = _vA.z\n    modifiedAttributeArray[b * 3 + 0] = _vB.x\n    modifiedAttributeArray[b * 3 + 1] = _vB.y\n    modifiedAttributeArray[b * 3 + 2] = _vB.z\n    modifiedAttributeArray[c * 3 + 0] = _vC.x\n    modifiedAttributeArray[c * 3 + 1] = _vC.y\n    modifiedAttributeArray[c * 3 + 2] = _vC.z\n  }\n\n  const geometry = object.geometry\n  const material = object.material\n\n  let a, b, c\n  const index = geometry.index\n  const positionAttribute = geometry.attributes.position\n  const morphPosition = geometry.morphAttributes.position\n  const morphTargetsRelative = geometry.morphTargetsRelative\n  const normalAttribute = geometry.attributes.normal\n  const morphNormal = geometry.morphAttributes.position\n\n  const groups = geometry.groups\n  const drawRange = geometry.drawRange\n  let i, j, il, jl\n  let group, groupMaterial\n  let start, end\n\n  const modifiedPosition = new Float32Array(positionAttribute.count * positionAttribute.itemSize)\n  const modifiedNormal = new Float32Array(normalAttribute.count * normalAttribute.itemSize)\n\n  if (index !== null) {\n    // indexed buffer geometry\n\n    if (Array.isArray(material)) {\n      for (i = 0, il = groups.length; i < il; i++) {\n        group = groups[i]\n        groupMaterial = material[group.materialIndex as number]\n\n        start = Math.max(group.start, drawRange.start)\n        end = Math.min(group.start + group.count, drawRange.start + drawRange.count)\n\n        for (j = start, jl = end; j < jl; j += 3) {\n          a = index.getX(j)\n          b = index.getX(j + 1)\n          c = index.getX(j + 2)\n\n          _calculateMorphedAttributeData(\n            object,\n            groupMaterial,\n            positionAttribute,\n            morphPosition,\n            morphTargetsRelative,\n            a,\n            b,\n            c,\n            modifiedPosition,\n          )\n\n          _calculateMorphedAttributeData(\n            object,\n            groupMaterial,\n            normalAttribute,\n            morphNormal,\n            morphTargetsRelative,\n            a,\n            b,\n            c,\n            modifiedNormal,\n          )\n        }\n      }\n    } else {\n      start = Math.max(0, drawRange.start)\n      end = Math.min(index.count, drawRange.start + drawRange.count)\n\n      for (i = start, il = end; i < il; i += 3) {\n        a = index.getX(i)\n        b = index.getX(i + 1)\n        c = index.getX(i + 2)\n\n        _calculateMorphedAttributeData(\n          object,\n          material,\n          positionAttribute,\n          morphPosition,\n          morphTargetsRelative,\n          a,\n          b,\n          c,\n          modifiedPosition,\n        )\n\n        _calculateMorphedAttributeData(\n          object,\n          material,\n          normalAttribute,\n          morphNormal,\n          morphTargetsRelative,\n          a,\n          b,\n          c,\n          modifiedNormal,\n        )\n      }\n    }\n  } else if (positionAttribute !== undefined) {\n    // non-indexed buffer geometry\n\n    if (Array.isArray(material)) {\n      for (i = 0, il = groups.length; i < il; i++) {\n        group = groups[i]\n        groupMaterial = material[group.materialIndex as number]\n\n        start = Math.max(group.start, drawRange.start)\n        end = Math.min(group.start + group.count, drawRange.start + drawRange.count)\n\n        for (j = start, jl = end; j < jl; j += 3) {\n          a = j\n          b = j + 1\n          c = j + 2\n\n          _calculateMorphedAttributeData(\n            object,\n            groupMaterial,\n            positionAttribute,\n            morphPosition,\n            morphTargetsRelative,\n            a,\n            b,\n            c,\n            modifiedPosition,\n          )\n\n          _calculateMorphedAttributeData(\n            object,\n            groupMaterial,\n            normalAttribute,\n            morphNormal,\n            morphTargetsRelative,\n            a,\n            b,\n            c,\n            modifiedNormal,\n          )\n        }\n      }\n    } else {\n      start = Math.max(0, drawRange.start)\n      end = Math.min(positionAttribute.count, drawRange.start + drawRange.count)\n\n      for (i = start, il = end; i < il; i += 3) {\n        a = i\n        b = i + 1\n        c = i + 2\n\n        _calculateMorphedAttributeData(\n          object,\n          material,\n          positionAttribute,\n          morphPosition,\n          morphTargetsRelative,\n          a,\n          b,\n          c,\n          modifiedPosition,\n        )\n\n        _calculateMorphedAttributeData(\n          object,\n          material,\n          normalAttribute,\n          morphNormal,\n          morphTargetsRelative,\n          a,\n          b,\n          c,\n          modifiedNormal,\n        )\n      }\n    }\n  }\n\n  const morphedPositionAttribute = new Float32BufferAttribute(modifiedPosition, 3)\n  const morphedNormalAttribute = new Float32BufferAttribute(modifiedNormal, 3)\n\n  return {\n    positionAttribute: positionAttribute,\n    normalAttribute: normalAttribute,\n    morphedPositionAttribute: morphedPositionAttribute,\n    morphedNormalAttribute: morphedNormalAttribute,\n  }\n}\n\n/**\n * Modifies the supplied geometry if it is non-indexed, otherwise creates a new,\n * non-indexed geometry. Returns the geometry with smooth normals everywhere except\n * faces that meet at an angle greater than the crease angle.\n *\n * Backwards compatible with code such as @react-three/drei's `<RoundedBox>`\n * which uses this method to operate on the original geometry.\n *\n * As of this writing, BufferGeometry.toNonIndexed() warns if the geometry is\n * non-indexed and returns `this`, i.e. the same geometry on which it was called:\n * `BufferGeometry is already non-indexed.`\n *\n * @param geometry\n * @param creaseAngle\n */\nexport function toCreasedNormals(geometry: BufferGeometry, creaseAngle = Math.PI / 3 /* 60 degrees */): BufferGeometry {\n  const creaseDot = Math.cos(creaseAngle)\n  const hashMultiplier = (1 + 1e-10) * 1e2\n\n  // reusable vectors\n  const verts = [new Vector3(), new Vector3(), new Vector3()]\n  const tempVec1 = new Vector3()\n  const tempVec2 = new Vector3()\n  const tempNorm = new Vector3()\n  const tempNorm2 = new Vector3()\n\n  // hashes a vector\n  function hashVertex(v: Vector3): string {\n    const x = ~~(v.x * hashMultiplier)\n    const y = ~~(v.y * hashMultiplier)\n    const z = ~~(v.z * hashMultiplier)\n    return `${x},${y},${z}`\n  }\n\n  const resultGeometry = geometry.index ? geometry.toNonIndexed() : geometry\n  const posAttr = resultGeometry.attributes.position\n  const vertexMap: { [key: string]: Vector3[] } = {}\n\n  // find all the normals shared by commonly located vertices\n  for (let i = 0, l = posAttr.count / 3; i < l; i++) {\n    const i3 = 3 * i\n    const a = verts[0].fromBufferAttribute(posAttr, i3 + 0)\n    const b = verts[1].fromBufferAttribute(posAttr, i3 + 1)\n    const c = verts[2].fromBufferAttribute(posAttr, i3 + 2)\n\n    tempVec1.subVectors(c, b)\n    tempVec2.subVectors(a, b)\n\n    // add the normal to the map for all vertices\n    const normal = new Vector3().crossVectors(tempVec1, tempVec2).normalize()\n    for (let n = 0; n < 3; n++) {\n      const vert = verts[n]\n      const hash = hashVertex(vert)\n      if (!(hash in vertexMap)) {\n        vertexMap[hash] = []\n      }\n\n      vertexMap[hash].push(normal)\n    }\n  }\n\n  // average normals from all vertices that share a common location if they are within the\n  // provided crease threshold\n  const normalArray = new Float32Array(posAttr.count * 3)\n  const normAttr = new BufferAttribute(normalArray, 3, false)\n  for (let i = 0, l = posAttr.count / 3; i < l; i++) {\n    // get the face normal for this vertex\n    const i3 = 3 * i\n    const a = verts[0].fromBufferAttribute(posAttr, i3 + 0)\n    const b = verts[1].fromBufferAttribute(posAttr, i3 + 1)\n    const c = verts[2].fromBufferAttribute(posAttr, i3 + 2)\n\n    tempVec1.subVectors(c, b)\n    tempVec2.subVectors(a, b)\n\n    tempNorm.crossVectors(tempVec1, tempVec2).normalize()\n\n    // average all normals that meet the threshold and set the normal value\n    for (let n = 0; n < 3; n++) {\n      const vert = verts[n]\n      const hash = hashVertex(vert)\n      const otherNormals = vertexMap[hash]\n      tempNorm2.set(0, 0, 0)\n\n      for (let k = 0, lk = otherNormals.length; k < lk; k++) {\n        const otherNorm = otherNormals[k]\n        if (tempNorm.dot(otherNorm) > creaseDot) {\n          tempNorm2.add(otherNorm)\n        }\n      }\n\n      tempNorm2.normalize()\n      normAttr.setXYZ(i3 + n, tempNorm2.x, tempNorm2.y, tempNorm2.z)\n    }\n  }\n\n  resultGeometry.setAttribute('normal', normAttr)\n  return resultGeometry\n}\n"], "mappings": ";;AAyBa,MAAAA,qBAAA,GAAwBA,CAACC,UAAA,EAA8BC,SAAA,KAA+C;EACjH,MAAMC,SAAA,GAAYF,UAAA,CAAW,CAAC,EAAEG,KAAA,KAAU;EAEpC,MAAAC,cAAA,GAAiB,IAAIC,GAAA,CAAIC,MAAA,CAAOC,IAAA,CAAKP,UAAA,CAAW,CAAC,EAAEQ,UAAU,CAAC;EAC9D,MAAAC,mBAAA,GAAsB,IAAIJ,GAAA,CAAIC,MAAA,CAAOC,IAAA,CAAKP,UAAA,CAAW,CAAC,EAAEU,eAAe,CAAC;EAE9E,MAAMF,UAAA,GAAqF;EAC3F,MAAME,eAAA,GAA4F;EAE5F,MAAAC,oBAAA,GAAuBX,UAAA,CAAW,CAAC,EAAEW,oBAAA;EAErC,MAAAC,cAAA,GAAiB,IAAIC,cAAA;EAE3B,IAAIC,MAAA,GAAS;EAEFd,UAAA,CAAAe,OAAA,CAAQ,CAACC,IAAA,EAAMC,CAAA,KAAM;IAC9B,IAAIC,eAAA,GAAkB;IAIlB,IAAAhB,SAAA,MAAec,IAAA,CAAKb,KAAA,KAAU,OAAO;MAC/BgB,OAAA,CAAAC,KAAA,CACN,uFACEH,CAAA,GACA;MAEG;IACT;IAIS,SAAAI,IAAA,IAAQL,IAAA,CAAKR,UAAA,EAAY;MAChC,IAAI,CAACJ,cAAA,CAAekB,GAAA,CAAID,IAAI,GAAG;QACrBF,OAAA,CAAAC,KAAA,CACN,uFACEH,CAAA,GACA,kEACAI,IAAA,GACA;QAEG;MACT;MAEI,IAAAb,UAAA,CAAWa,IAAI,MAAM,QAAW;QACvBb,UAAA,CAAAa,IAAI,IAAI;MACrB;MAEAb,UAAA,CAAWa,IAAI,EAAEE,IAAA,CAAKP,IAAA,CAAKR,UAAA,CAAWa,IAAI,CAAC;MAE3CH,eAAA;IACF;IAII,IAAAA,eAAA,KAAoBd,cAAA,CAAeoB,IAAA,EAAM;MACnCL,OAAA,CAAAC,KAAA,CACN,uFACEH,CAAA,GACA;MAEG;IACT;IAII,IAAAN,oBAAA,KAAyBK,IAAA,CAAKL,oBAAA,EAAsB;MAC9CQ,OAAA,CAAAC,KAAA,CACN,uFACEH,CAAA,GACA;MAEG;IACT;IAES,SAAAI,IAAA,IAAQL,IAAA,CAAKN,eAAA,EAAiB;MACrC,IAAI,CAACD,mBAAA,CAAoBa,GAAA,CAAID,IAAI,GAAG;QAC1BF,OAAA,CAAAC,KAAA,CACN,uFACEH,CAAA,GACA;QAEG;MACT;MAEI,IAAAP,eAAA,CAAgBW,IAAI,MAAM,QAA2BX,eAAA,CAAAW,IAAI,IAAI;MAEjEX,eAAA,CAAgBW,IAAI,EAAEE,IAAA,CAAKP,IAAA,CAAKN,eAAA,CAAgBW,IAAI,CAAC;IACvD;IAIAT,cAAA,CAAea,QAAA,CAASC,cAAA,GAAiBd,cAAA,CAAea,QAAA,CAASC,cAAA,IAAkB;IACnFd,cAAA,CAAea,QAAA,CAASC,cAAA,CAAeH,IAAA,CAAKP,IAAA,CAAKS,QAAQ;IAEzD,IAAIxB,SAAA,EAAW;MACT,IAAA0B,KAAA;MAEJ,IAAIX,IAAA,CAAKb,KAAA,EAAO;QACdwB,KAAA,GAAQX,IAAA,CAAKb,KAAA,CAAMwB,KAAA;MACV,WAAAX,IAAA,CAAKR,UAAA,CAAWoB,QAAA,KAAa,QAAW;QACzCD,KAAA,GAAAX,IAAA,CAAKR,UAAA,CAAWoB,QAAA,CAASD,KAAA;MAAA,OAC5B;QACGR,OAAA,CAAAC,KAAA,CACN,uFACEH,CAAA,GACA;QAEG;MACT;MAEeL,cAAA,CAAAiB,QAAA,CAASf,MAAA,EAAQa,KAAA,EAAOV,CAAC;MAE9BH,MAAA,IAAAa,KAAA;IACZ;EAAA,CACD;EAID,IAAIzB,SAAA,EAAW;IACb,IAAI4B,WAAA,GAAc;IAClB,MAAMC,WAAA,GAAwB;IAEnB/B,UAAA,CAAAe,OAAA,CAASC,IAAA,IAAS;MAC3B,MAAMb,KAAA,GAAQa,IAAA,CAAKb,KAAA;MAEnB,SAAS6B,CAAA,GAAI,GAAGA,CAAA,GAAI7B,KAAA,CAAMwB,KAAA,EAAO,EAAEK,CAAA,EAAG;QACpCD,WAAA,CAAYR,IAAA,CAAKpB,KAAA,CAAM8B,IAAA,CAAKD,CAAC,IAAIF,WAAW;MAC9C;MAEeA,WAAA,IAAAd,IAAA,CAAKR,UAAA,CAAWoB,QAAA,CAASD,KAAA;IAAA,CACzC;IAEDf,cAAA,CAAesB,QAAA,CAASH,WAAW;EACrC;EAIA,SAASV,IAAA,IAAQb,UAAA,EAAY;IAC3B,MAAM2B,eAAA,GAAkBC,qBAAA,CAAsB5B,UAAA,CAAWa,IAAI,CAAsB;IAEnF,IAAI,CAACc,eAAA,EAAiB;MACZhB,OAAA,CAAAC,KAAA,CACN,0FAA0FC,IAAA,GAAO;MAE5F;IACT;IAEeT,cAAA,CAAAyB,YAAA,CAAahB,IAAA,EAAMc,eAAe;EACnD;EAIA,SAASd,IAAA,IAAQX,eAAA,EAAiB;IAChC,MAAM4B,eAAA,GAAkB5B,eAAA,CAAgBW,IAAI,EAAE,CAAC,EAAEkB,MAAA;IAEjD,IAAID,eAAA,KAAoB,GAAG;IAEZ1B,cAAA,CAAAF,eAAA,GAAkBE,cAAA,CAAeF,eAAA,IAAmB;IACpDE,cAAA,CAAAF,eAAA,CAAgBW,IAAI,IAAI;IAEvC,SAASJ,CAAA,GAAI,GAAGA,CAAA,GAAIqB,eAAA,EAAiB,EAAErB,CAAA,EAAG;MACxC,MAAMuB,sBAAA,GAAyB;MAEtB,SAAAR,CAAA,GAAI,GAAGA,CAAA,GAAItB,eAAA,CAAgBW,IAAI,EAAEkB,MAAA,EAAQ,EAAEP,CAAA,EAAG;QACrDQ,sBAAA,CAAuBjB,IAAA,CAAKb,eAAA,CAAgBW,IAAI,EAAEW,CAAC,EAAEf,CAAC,CAAC;MACzD;MAEM,MAAAwB,oBAAA,GAAuBL,qBAAA,CAAsBI,sBAA2C;MAE9F,IAAI,CAACC,oBAAA,EAAsB;QACjBtB,OAAA,CAAAC,KAAA,CACN,0FACEC,IAAA,GACA;QAEG;MACT;MAEAT,cAAA,CAAeF,eAAA,CAAgBW,IAAI,EAAEE,IAAA,CAAKkB,oBAAoB;IAChE;EACF;EAEO,OAAA7B,cAAA;AACT;AAMa,MAAAwB,qBAAA,GAAyB5B,UAAA,IAAsE;EAC1G,IAAIkC,UAAA,GAAiD;EACrD,IAAIC,QAAA,GAA+B;EACnC,IAAIC,UAAA,GAAkC;EACtC,IAAIC,WAAA,GAAc;EAEPrC,UAAA,CAAAO,OAAA,CAAS+B,IAAA,IAAS;IAC3B,IAAIJ,UAAA,KAAe,QAAW;MAC5BA,UAAA,GAAaI,IAAA,CAAKC,KAAA,CAAMC,WAAA;IAC1B;IACI,IAAAN,UAAA,KAAeI,IAAA,CAAKC,KAAA,CAAMC,WAAA,EAAa;MACjC7B,OAAA,CAAAC,KAAA,CACN;MAEK;IACT;IAEA,IAAIuB,QAAA,KAAa,QAAWA,QAAA,GAAWG,IAAA,CAAKH,QAAA;IACxC,IAAAA,QAAA,KAAaG,IAAA,CAAKH,QAAA,EAAU;MACtBxB,OAAA,CAAAC,KAAA,CACN;MAEK;IACT;IAEA,IAAIwB,UAAA,KAAe,QAAWA,UAAA,GAAaE,IAAA,CAAKF,UAAA;IAC5C,IAAAA,UAAA,KAAeE,IAAA,CAAKF,UAAA,EAAY;MAC1BzB,OAAA,CAAAC,KAAA,CACN;MAEK;IACT;IAEAyB,WAAA,IAAeC,IAAA,CAAKC,KAAA,CAAMR,MAAA;EAAA,CAC3B;EAED,IAAIG,UAAA,IAAcC,QAAA,EAAU;IAEpB,MAAAI,KAAA,GAAQ,IAAIL,UAAA,CAAWG,WAAW;IACxC,IAAI/B,MAAA,GAAS;IAEFN,UAAA,CAAAO,OAAA,CAAS+B,IAAA,IAAS;MACrBC,KAAA,CAAAE,GAAA,CAAIH,IAAA,CAAKC,KAAA,EAAOjC,MAAM;MAC5BA,MAAA,IAAUgC,IAAA,CAAKC,KAAA,CAAMR,MAAA;IAAA,CACtB;IAED,OAAO,IAAIW,eAAA,CAAgBH,KAAA,EAAOJ,QAAA,EAAUC,UAAU;EACxD;AACF;AAMa,MAAAO,oBAAA,GAAwB3C,UAAA,IAAuE;EAG1G,IAAIkC,UAAA,GAAiD;EACrD,IAAIG,WAAA,GAAc;EAClB,IAAIO,MAAA,GAAS;EAGJ,SAAAnC,CAAA,GAAI,GAAGoC,CAAA,GAAI7C,UAAA,CAAW+B,MAAA,EAAQtB,CAAA,GAAIoC,CAAA,EAAG,EAAEpC,CAAA,EAAG;IAC3C,MAAAqC,SAAA,GAAY9C,UAAA,CAAWS,CAAC;IAE9B,IAAIyB,UAAA,KAAe,QAAWA,UAAA,GAAaY,SAAA,CAAUP,KAAA,CAAMC,WAAA;IACvD,IAAAN,UAAA,KAAeY,SAAA,CAAUP,KAAA,CAAMC,WAAA,EAAa;MAC9C7B,OAAA,CAAQC,KAAA,CAAM,2DAA2D;MAClE;IACT;IAEAyB,WAAA,IAAeS,SAAA,CAAUP,KAAA,CAAMR,MAAA;IAC/Ba,MAAA,IAAUE,SAAA,CAAUX,QAAA;EACtB;EAIA,MAAMY,iBAAA,GAAoB,IAAIC,iBAAA,CAAkB,IAAId,UAAA,CAAWG,WAAW,GAAGO,MAAM;EACnF,IAAItC,MAAA,GAAS;EACb,MAAM2C,GAAA,GAAM;EACZ,MAAMC,OAAA,GAAU,CAAC,QAAQ,QAAQ,QAAQ,MAAM;EAC/C,MAAMC,OAAA,GAAU,CAAC,QAAQ,QAAQ,QAAQ,MAAM;EAE/C,SAAS3B,CAAA,GAAI,GAAGqB,CAAA,GAAI7C,UAAA,CAAW+B,MAAA,EAAQP,CAAA,GAAIqB,CAAA,EAAGrB,CAAA,IAAK;IAC3C,MAAAsB,SAAA,GAAY9C,UAAA,CAAWwB,CAAC;IAC9B,MAAMW,QAAA,GAAWW,SAAA,CAAUX,QAAA;IAC3B,MAAMhB,KAAA,GAAQ2B,SAAA,CAAU3B,KAAA;IACxB,MAAMiC,GAAA,GAAM,IAAIC,0BAAA,CAA2BN,iBAAA,EAAmBZ,QAAA,EAAU7B,MAAA,EAAQwC,SAAA,CAAUV,UAAU;IACpGa,GAAA,CAAIlC,IAAA,CAAKqC,GAAG;IAEF9C,MAAA,IAAA6B,QAAA;IAIV,SAASmB,CAAA,GAAI,GAAGA,CAAA,GAAInC,KAAA,EAAOmC,CAAA,IAAK;MAC9B,SAASC,CAAA,GAAI,GAAGA,CAAA,GAAIpB,QAAA,EAAUoB,CAAA,IAAK;QACjC,MAAMd,GAAA,GAAMe,UAAA,CAAWJ,GAAA,EAAKD,OAAA,CAAQI,CAAC,CAAqC;QAK1E,MAAME,GAAA,GAAMD,UAAA,CAAWV,SAAA,EAAWI,OAAA,CAAQK,CAAC,CAA0B;QAKjEd,GAAA,CAAAa,CAAA,EAAGG,GAAA,CAAIH,CAAC,CAAC;MACf;IACF;EACF;EAEO,OAAAL,GAAA;AACT;AAMO,SAASS,kBAAkBC,QAAA,EAAkC;EAIlE,IAAIC,GAAA,GAAM;EACD,SAAA/C,IAAA,IAAQ8C,QAAA,CAAS3D,UAAA,EAAY;IAC9B,MAAAsC,IAAA,GAAOqB,QAAA,CAASE,YAAA,CAAahD,IAAI;IACvC+C,GAAA,IAAOtB,IAAA,CAAKnB,KAAA,GAAQmB,IAAA,CAAKH,QAAA,GAAYG,IAAA,CAAKC,KAAA,CAAqBuB,iBAAA;EACjE;EAEM,MAAAC,OAAA,GAAUJ,QAAA,CAASK,QAAA;EACzBJ,GAAA,IAAOG,OAAA,GAAUA,OAAA,CAAQ5C,KAAA,GAAQ4C,OAAA,CAAQ5B,QAAA,GAAY4B,OAAA,CAAQxB,KAAA,CAAqBuB,iBAAA,GAAoB;EAC/F,OAAAF,GAAA;AACT;AAOgB,SAAAK,cAAcN,QAAA,EAA0BO,SAAA,GAAY,MAAsB;EACxFA,SAAA,GAAYC,IAAA,CAAKC,GAAA,CAAIF,SAAA,EAAWG,MAAA,CAAOC,OAAO;EAI9C,MAAMC,WAAA,GAEF;EACE,MAAAR,OAAA,GAAUJ,QAAA,CAASK,QAAA;EACnB,MAAAQ,SAAA,GAAYb,QAAA,CAASE,YAAA,CAAa,UAAU;EAClD,MAAMY,WAAA,GAAcV,OAAA,GAAUA,OAAA,CAAQ5C,KAAA,GAAQqD,SAAA,CAAUrD,KAAA;EAGxD,IAAIuD,SAAA,GAAY;EAGhB,MAAMC,cAAA,GAAiB7E,MAAA,CAAOC,IAAA,CAAK4D,QAAA,CAAS3D,UAAU;EACtD,MAAM4E,UAAA,GAEF;EACJ,MAAMC,gBAAA,GAEF;EACJ,MAAMC,UAAA,GAAa;EACnB,MAAM5B,OAAA,GAAU,CAAC,QAAQ,QAAQ,QAAQ,MAAM;EAG/C,SAASzC,CAAA,GAAI,GAAGoC,CAAA,GAAI8B,cAAA,CAAe5C,MAAA,EAAQtB,CAAA,GAAIoC,CAAA,EAAGpC,CAAA,IAAK;IAC/C,MAAAI,IAAA,GAAO8D,cAAA,CAAelE,CAAC;IAElBmE,UAAA,CAAA/D,IAAI,IAAI;IAEb,MAAAkE,SAAA,GAAYpB,QAAA,CAASzD,eAAA,CAAgBW,IAAI;IAC/C,IAAIkE,SAAA,EAAW;MACbF,gBAAA,CAAiBhE,IAAI,IAAI,IAAImE,KAAA,CAAMD,SAAA,CAAUhD,MAAM,EAAEkD,IAAA,CAAK,CAAC,EAAEC,GAAA,CAAI,MAAM,EAAE;IAC3E;EACF;EAGA,MAAMC,YAAA,GAAehB,IAAA,CAAKiB,KAAA,CAAM,IAAIlB,SAAS;EAC7C,MAAMmB,eAAA,GAAkBlB,IAAA,CAAKmB,GAAA,CAAI,IAAIH,YAAY;EACjD,SAAS1E,CAAA,GAAI,GAAGA,CAAA,GAAIgE,WAAA,EAAahE,CAAA,IAAK;IACpC,MAAMd,KAAA,GAAQoE,OAAA,GAAUA,OAAA,CAAQtC,IAAA,CAAKhB,CAAC,IAAIA,CAAA;IAG1C,IAAI8E,IAAA,GAAO;IACX,SAAS/D,CAAA,GAAI,GAAGqB,CAAA,GAAI8B,cAAA,CAAe5C,MAAA,EAAQP,CAAA,GAAIqB,CAAA,EAAGrB,CAAA,IAAK;MAC/C,MAAAX,IAAA,GAAO8D,cAAA,CAAenD,CAAC;MACvB,MAAAsB,SAAA,GAAYa,QAAA,CAASE,YAAA,CAAahD,IAAI;MAC5C,MAAMsB,QAAA,GAAWW,SAAA,CAAUX,QAAA;MAE3B,SAASoB,CAAA,GAAI,GAAGA,CAAA,GAAIpB,QAAA,EAAUoB,CAAA,IAAK;QAGzBgC,IAAA,OAAG,CAAC,EAAEzC,SAAA,CAAUI,OAAA,CAAQK,CAAC,CAAC,EAAE5D,KAAK,IAAI0F,eAAA;MAC/C;IACF;IAIA,IAAIE,IAAA,IAAQhB,WAAA,EAAa;MACZO,UAAA,CAAA/D,IAAA,CAAKwD,WAAA,CAAYgB,IAAI,CAAC;IAAA,OAC5B;MAEL,SAAS/D,CAAA,GAAI,GAAGqB,CAAA,GAAI8B,cAAA,CAAe5C,MAAA,EAAQP,CAAA,GAAIqB,CAAA,EAAGrB,CAAA,IAAK;QAC/C,MAAAX,IAAA,GAAO8D,cAAA,CAAenD,CAAC;QACvB,MAAAsB,SAAA,GAAYa,QAAA,CAASE,YAAA,CAAahD,IAAI;QACtC,MAAAkE,SAAA,GAAYpB,QAAA,CAASzD,eAAA,CAAgBW,IAAI;QAC/C,MAAMsB,QAAA,GAAWW,SAAA,CAAUX,QAAA;QACrB,MAAAqD,QAAA,GAAWZ,UAAA,CAAW/D,IAAI;QAC1B,MAAA4E,cAAA,GAAiBZ,gBAAA,CAAiBhE,IAAI;QAE5C,SAAS0C,CAAA,GAAI,GAAGA,CAAA,GAAIpB,QAAA,EAAUoB,CAAA,IAAK;UAC3B,MAAAmC,UAAA,GAAaxC,OAAA,CAAQK,CAAC;UAE5BiC,QAAA,CAASzE,IAAA,CAAK+B,SAAA,CAAU4C,UAAU,EAAE/F,KAAK,CAAC;UAE1C,IAAIoF,SAAA,EAAW;YACb,SAASY,CAAA,GAAI,GAAGC,EAAA,GAAKb,SAAA,CAAUhD,MAAA,EAAQ4D,CAAA,GAAIC,EAAA,EAAID,CAAA,IAAK;cAEnCF,cAAA,CAAAE,CAAC,EAAE5E,IAAA,CAAKgE,SAAA,CAAUY,CAAC,EAAED,UAAU,EAAE/F,KAAK,CAAC;YACxD;UACF;QACF;MACF;MAEA4E,WAAA,CAAYgB,IAAI,IAAIb,SAAA;MACpBI,UAAA,CAAW/D,IAAA,CAAK2D,SAAS;MACzBA,SAAA;IACF;EACF;EAIM,MAAAmB,MAAA,GAASlC,QAAA,CAASmC,KAAA;EACxB,SAASrF,CAAA,GAAI,GAAGoC,CAAA,GAAI8B,cAAA,CAAe5C,MAAA,EAAQtB,CAAA,GAAIoC,CAAA,EAAGpC,CAAA,IAAK;IAC/C,MAAAI,IAAA,GAAO8D,cAAA,CAAelE,CAAC;IACvB,MAAAsF,YAAA,GAAepC,QAAA,CAASE,YAAA,CAAahD,IAAI;IAE/C,MAAMmF,MAAA,GAAS,IAAKD,YAAA,CAAaxD,KAAA,CAAqBC,WAAA,CAAYoC,UAAA,CAAW/D,IAAI,CAAC;IAClF,MAAMiC,SAAA,GAAY,IAAIJ,eAAA,CAAgBsD,MAAA,EAAQD,YAAA,CAAa5D,QAAA,EAAU4D,YAAA,CAAa3D,UAAU;IAErFyD,MAAA,CAAAhE,YAAA,CAAahB,IAAA,EAAMiC,SAAS;IAGnC,IAAIjC,IAAA,IAAQgE,gBAAA,EAAkB;MAC5B,SAASrD,CAAA,GAAI,GAAGA,CAAA,GAAIqD,gBAAA,CAAiBhE,IAAI,EAAEkB,MAAA,EAAQP,CAAA,IAAK;QACtD,MAAMyE,iBAAA,GAAoBtC,QAAA,CAASzD,eAAA,CAAgBW,IAAI,EAAEW,CAAC;QAEpD,MAAA0E,OAAA,GAAS,IAAKD,iBAAA,CAAkB1D,KAAA,CAAqBC,WAAA,CAAYqC,gBAAA,CAAiBhE,IAAI,EAAEW,CAAC,CAAC;QAChG,MAAM2E,cAAA,GAAiB,IAAIzD,eAAA,CAAgBwD,OAAA,EAAQD,iBAAA,CAAkB9D,QAAA,EAAU8D,iBAAA,CAAkB7D,UAAU;QAC3GyD,MAAA,CAAO3F,eAAA,CAAgBW,IAAI,EAAEW,CAAC,IAAI2E,cAAA;MACpC;IACF;EACF;EAIAN,MAAA,CAAOnE,QAAA,CAASoD,UAAU;EAEnB,OAAAe,MAAA;AACT;AAOgB,SAAAO,oBAAoBzC,QAAA,EAA0B0C,QAAA,EAAkC;EAC9F,IAAIA,QAAA,KAAaC,iBAAA,EAAmB;IAClC3F,OAAA,CAAQ4F,IAAA,CAAK,yFAAyF;IAC/F,OAAA5C,QAAA;EACT;EAEI,IAAA0C,QAAA,KAAaG,mBAAA,IAAuBH,QAAA,KAAaI,qBAAA,EAAuB;IACtE,IAAA9G,KAAA,GAAQgE,QAAA,CAASK,QAAA;IAIrB,IAAIrE,KAAA,KAAU,MAAM;MAClB,MAAMoE,OAAA,GAAU;MAEV,MAAA3C,QAAA,GAAWuC,QAAA,CAASE,YAAA,CAAa,UAAU;MAEjD,IAAIzC,QAAA,KAAa,QAAW;QAC1B,SAASX,CAAA,GAAI,GAAGA,CAAA,GAAIW,QAAA,CAASD,KAAA,EAAOV,CAAA,IAAK;UACvCsD,OAAA,CAAQhD,IAAA,CAAKN,CAAC;QAChB;QAEAkD,QAAA,CAASjC,QAAA,CAASqC,OAAO;QACzBpE,KAAA,GAAQgE,QAAA,CAASK,QAAA;MAAS,OACrB;QACGrD,OAAA,CAAAC,KAAA,CACN;QAEK,OAAA+C,QAAA;MACT;IACF;IAIM,MAAA+C,iBAAA,GAAqB/G,KAAA,CAA0BwB,KAAA,GAAQ;IAC7D,MAAM2D,UAAA,GAAa;IAEnB,IAAInF,KAAA,EAAO;MACT,IAAI0G,QAAA,KAAaG,mBAAA,EAAqB;QAGpC,SAAS/F,CAAA,GAAI,GAAGA,CAAA,IAAKiG,iBAAA,EAAmBjG,CAAA,IAAK;UAC3CqE,UAAA,CAAW/D,IAAA,CAAKpB,KAAA,CAAM8B,IAAA,CAAK,CAAC,CAAC;UAC7BqD,UAAA,CAAW/D,IAAA,CAAKpB,KAAA,CAAM8B,IAAA,CAAKhB,CAAC,CAAC;UAC7BqE,UAAA,CAAW/D,IAAA,CAAKpB,KAAA,CAAM8B,IAAA,CAAKhB,CAAA,GAAI,CAAC,CAAC;QACnC;MAAA,OACK;QAGL,SAASA,CAAA,GAAI,GAAGA,CAAA,GAAIiG,iBAAA,EAAmBjG,CAAA,IAAK;UACtC,IAAAA,CAAA,GAAI,MAAM,GAAG;YACfqE,UAAA,CAAW/D,IAAA,CAAKpB,KAAA,CAAM8B,IAAA,CAAKhB,CAAC,CAAC;YAC7BqE,UAAA,CAAW/D,IAAA,CAAKpB,KAAA,CAAM8B,IAAA,CAAKhB,CAAA,GAAI,CAAC,CAAC;YACjCqE,UAAA,CAAW/D,IAAA,CAAKpB,KAAA,CAAM8B,IAAA,CAAKhB,CAAA,GAAI,CAAC,CAAC;UAAA,OAC5B;YACLqE,UAAA,CAAW/D,IAAA,CAAKpB,KAAA,CAAM8B,IAAA,CAAKhB,CAAA,GAAI,CAAC,CAAC;YACjCqE,UAAA,CAAW/D,IAAA,CAAKpB,KAAA,CAAM8B,IAAA,CAAKhB,CAAA,GAAI,CAAC,CAAC;YACjCqE,UAAA,CAAW/D,IAAA,CAAKpB,KAAA,CAAM8B,IAAA,CAAKhB,CAAC,CAAC;UAC/B;QACF;MACF;IACF;IAEI,IAAAqE,UAAA,CAAW/C,MAAA,GAAS,MAAM2E,iBAAA,EAAmB;MAC/C/F,OAAA,CAAQC,KAAA,CAAM,kGAAkG;IAClH;IAIM,MAAA+F,WAAA,GAAchD,QAAA,CAASmC,KAAA;IAC7Ba,WAAA,CAAYjF,QAAA,CAASoD,UAAU;IAC/B6B,WAAA,CAAYC,WAAA,CAAY;IAEjB,OAAAD,WAAA;EAAA,OACF;IACGhG,OAAA,CAAAC,KAAA,CAAM,uEAAuEyF,QAAQ;IACtF,OAAA1C,QAAA;EACT;AACF;AAeO,SAASkD,yBAAyBC,MAAA,EAA+D;EAClG,IAAAA,MAAA,CAAOnD,QAAA,CAASoD,gBAAA,KAAqB,MAAM;IAC7CpG,OAAA,CAAQC,KAAA,CAAM,oEAAoE;IAC3E;EACT;EAEM,MAAAoG,GAAA,GAAM,IAAIC,OAAA;EACV,MAAAC,GAAA,GAAM,IAAID,OAAA;EACV,MAAAE,GAAA,GAAM,IAAIF,OAAA;EAEV,MAAAG,MAAA,GAAS,IAAIH,OAAA;EACb,MAAAI,MAAA,GAAS,IAAIJ,OAAA;EACb,MAAAK,MAAA,GAAS,IAAIL,OAAA;EAEb,MAAAM,OAAA,GAAU,IAAIN,OAAA;EACd,MAAAO,OAAA,GAAU,IAAIP,OAAA;EACd,MAAAQ,OAAA,GAAU,IAAIR,OAAA;EAEX,SAAAS,+BACPC,OAAA,EACAC,SAAA,EACA9E,SAAA,EACAqD,cAAA,EACA0B,qBAAA,EACAC,EAAA,EACAC,EAAA,EACAC,EAAA,EACAC,sBAAA,EACM;IACFjB,GAAA,CAAAkB,mBAAA,CAAoBpF,SAAA,EAAWgF,EAAC;IAChCZ,GAAA,CAAAgB,mBAAA,CAAoBpF,SAAA,EAAWiF,EAAC;IAChCZ,GAAA,CAAAe,mBAAA,CAAoBpF,SAAA,EAAWkF,EAAC;IAEpC,MAAMG,eAAA,GAAkBR,OAAA,CAAOS,qBAAA;IAE/B;IAAA;IAEER,SAAA,CAASS,YAAA,IACTlC,cAAA,IACAgC,eAAA,EACA;MACQZ,OAAA,CAAA9E,GAAA,CAAI,GAAG,GAAG,CAAC;MACX+E,OAAA,CAAA/E,GAAA,CAAI,GAAG,GAAG,CAAC;MACXgF,OAAA,CAAAhF,GAAA,CAAI,GAAG,GAAG,CAAC;MAEnB,SAAS6F,EAAA,GAAI,GAAGC,GAAA,GAAKpC,cAAA,CAAepE,MAAA,EAAQuG,EAAA,GAAIC,GAAA,EAAID,EAAA,IAAK;QACjD,MAAAE,SAAA,GAAYL,eAAA,CAAgBG,EAAC;QAC7B,MAAAG,KAAA,GAAQtC,cAAA,CAAemC,EAAC;QAE9B,IAAIE,SAAA,KAAc,GAAG;QAEdpB,MAAA,CAAAc,mBAAA,CAAoBO,KAAA,EAAOX,EAAC;QAC5BT,MAAA,CAAAa,mBAAA,CAAoBO,KAAA,EAAOV,EAAC;QAC5BT,MAAA,CAAAY,mBAAA,CAAoBO,KAAA,EAAOT,EAAC;QAEnC,IAAIH,qBAAA,EAAsB;UAChBN,OAAA,CAAAmB,eAAA,CAAgBtB,MAAA,EAAQoB,SAAS;UACjChB,OAAA,CAAAkB,eAAA,CAAgBrB,MAAA,EAAQmB,SAAS;UACjCf,OAAA,CAAAiB,eAAA,CAAgBpB,MAAA,EAAQkB,SAAS;QAAA,OACpC;UACLjB,OAAA,CAAQmB,eAAA,CAAgBtB,MAAA,CAAOuB,GAAA,CAAI3B,GAAG,GAAGwB,SAAS;UAClDhB,OAAA,CAAQkB,eAAA,CAAgBrB,MAAA,CAAOsB,GAAA,CAAIzB,GAAG,GAAGsB,SAAS;UAClDf,OAAA,CAAQiB,eAAA,CAAgBpB,MAAA,CAAOqB,GAAA,CAAIxB,GAAG,GAAGqB,SAAS;QACpD;MACF;MAEAxB,GAAA,CAAI4B,GAAA,CAAIrB,OAAO;MACfL,GAAA,CAAI0B,GAAA,CAAIpB,OAAO;MACfL,GAAA,CAAIyB,GAAA,CAAInB,OAAO;IACjB;IAEA,IAAKE,OAAA,CAAuBkB,aAAA,EAAe;MAEzClB,OAAA,CAAOmB,aAAA,CAAchB,EAAA,EAAGd,GAAG;MAE3BW,OAAA,CAAOmB,aAAA,CAAcf,EAAA,EAAGb,GAAG;MAE3BS,OAAA,CAAOmB,aAAA,CAAcd,EAAA,EAAGb,GAAG;IAC7B;IAEAc,sBAAA,CAAuBH,EAAA,GAAI,IAAI,CAAC,IAAId,GAAA,CAAI+B,CAAA;IACxCd,sBAAA,CAAuBH,EAAA,GAAI,IAAI,CAAC,IAAId,GAAA,CAAIgC,CAAA;IACxCf,sBAAA,CAAuBH,EAAA,GAAI,IAAI,CAAC,IAAId,GAAA,CAAIiC,CAAA;IACxChB,sBAAA,CAAuBF,EAAA,GAAI,IAAI,CAAC,IAAIb,GAAA,CAAI6B,CAAA;IACxCd,sBAAA,CAAuBF,EAAA,GAAI,IAAI,CAAC,IAAIb,GAAA,CAAI8B,CAAA;IACxCf,sBAAA,CAAuBF,EAAA,GAAI,IAAI,CAAC,IAAIb,GAAA,CAAI+B,CAAA;IACxChB,sBAAA,CAAuBD,EAAA,GAAI,IAAI,CAAC,IAAIb,GAAA,CAAI4B,CAAA;IACxCd,sBAAA,CAAuBD,EAAA,GAAI,IAAI,CAAC,IAAIb,GAAA,CAAI6B,CAAA;IACxCf,sBAAA,CAAuBD,EAAA,GAAI,IAAI,CAAC,IAAIb,GAAA,CAAI8B,CAAA;EAC1C;EAEA,MAAMtF,QAAA,GAAWmD,MAAA,CAAOnD,QAAA;EACxB,MAAMuF,QAAA,GAAWpC,MAAA,CAAOoC,QAAA;EAExB,IAAIC,CAAA,EAAGC,CAAA,EAAG9F,CAAA;EACV,MAAM3D,KAAA,GAAQgE,QAAA,CAAShE,KAAA;EACjB,MAAA0J,iBAAA,GAAoB1F,QAAA,CAAS3D,UAAA,CAAWoB,QAAA;EACxC,MAAAkI,aAAA,GAAgB3F,QAAA,CAASzD,eAAA,CAAgBkB,QAAA;EAC/C,MAAMjB,oBAAA,GAAuBwD,QAAA,CAASxD,oBAAA;EAChC,MAAAoJ,eAAA,GAAkB5F,QAAA,CAAS3D,UAAA,CAAWwJ,MAAA;EACtC,MAAAC,WAAA,GAAc9F,QAAA,CAASzD,eAAA,CAAgBkB,QAAA;EAE7C,MAAMsI,MAAA,GAAS/F,QAAA,CAAS+F,MAAA;EACxB,MAAMC,SAAA,GAAYhG,QAAA,CAASgG,SAAA;EACvB,IAAAlJ,CAAA,EAAGe,CAAA,EAAGoI,EAAA,EAAIC,EAAA;EACd,IAAIC,KAAA,EAAOC,aAAA;EACX,IAAIC,KAAA,EAAOC,GAAA;EAEX,MAAMC,gBAAA,GAAmB,IAAIC,YAAA,CAAad,iBAAA,CAAkBlI,KAAA,GAAQkI,iBAAA,CAAkBlH,QAAQ;EAC9F,MAAMiI,cAAA,GAAiB,IAAID,YAAA,CAAaZ,eAAA,CAAgBpI,KAAA,GAAQoI,eAAA,CAAgBpH,QAAQ;EAExF,IAAIxC,KAAA,KAAU,MAAM;IAGd,IAAAqF,KAAA,CAAMqF,OAAA,CAAQnB,QAAQ,GAAG;MAC3B,KAAKzI,CAAA,GAAI,GAAGmJ,EAAA,GAAKF,MAAA,CAAO3H,MAAA,EAAQtB,CAAA,GAAImJ,EAAA,EAAInJ,CAAA,IAAK;QAC3CqJ,KAAA,GAAQJ,MAAA,CAAOjJ,CAAC;QACAsJ,aAAA,GAAAb,QAAA,CAASY,KAAA,CAAMQ,aAAuB;QAEtDN,KAAA,GAAQ7F,IAAA,CAAKC,GAAA,CAAI0F,KAAA,CAAME,KAAA,EAAOL,SAAA,CAAUK,KAAK;QACvCC,GAAA,GAAA9F,IAAA,CAAKoG,GAAA,CAAIT,KAAA,CAAME,KAAA,GAAQF,KAAA,CAAM3I,KAAA,EAAOwI,SAAA,CAAUK,KAAA,GAAQL,SAAA,CAAUxI,KAAK;QAE3E,KAAKK,CAAA,GAAIwI,KAAA,EAAOH,EAAA,GAAKI,GAAA,EAAKzI,CAAA,GAAIqI,EAAA,EAAIrI,CAAA,IAAK,GAAG;UACpC2H,CAAA,GAAAxJ,KAAA,CAAM8B,IAAA,CAAKD,CAAC;UACZ4H,CAAA,GAAAzJ,KAAA,CAAM8B,IAAA,CAAKD,CAAA,GAAI,CAAC;UAChB8B,CAAA,GAAA3D,KAAA,CAAM8B,IAAA,CAAKD,CAAA,GAAI,CAAC;UAEpBkG,8BAAA,CACEZ,MAAA,EACAiD,aAAA,EACAV,iBAAA,EACAC,aAAA,EACAnJ,oBAAA,EACAgJ,CAAA,EACAC,CAAA,EACA9F,CAAA,EACA4G,gBAAA;UAGFxC,8BAAA,CACEZ,MAAA,EACAiD,aAAA,EACAR,eAAA,EACAE,WAAA,EACAtJ,oBAAA,EACAgJ,CAAA,EACAC,CAAA,EACA9F,CAAA,EACA8G,cAAA;QAEJ;MACF;IAAA,OACK;MACLJ,KAAA,GAAQ7F,IAAA,CAAKC,GAAA,CAAI,GAAGuF,SAAA,CAAUK,KAAK;MACnCC,GAAA,GAAM9F,IAAA,CAAKoG,GAAA,CAAI5K,KAAA,CAAMwB,KAAA,EAAOwI,SAAA,CAAUK,KAAA,GAAQL,SAAA,CAAUxI,KAAK;MAE7D,KAAKV,CAAA,GAAIuJ,KAAA,EAAOJ,EAAA,GAAKK,GAAA,EAAKxJ,CAAA,GAAImJ,EAAA,EAAInJ,CAAA,IAAK,GAAG;QACpC0I,CAAA,GAAAxJ,KAAA,CAAM8B,IAAA,CAAKhB,CAAC;QACZ2I,CAAA,GAAAzJ,KAAA,CAAM8B,IAAA,CAAKhB,CAAA,GAAI,CAAC;QAChB6C,CAAA,GAAA3D,KAAA,CAAM8B,IAAA,CAAKhB,CAAA,GAAI,CAAC;QAEpBiH,8BAAA,CACEZ,MAAA,EACAoC,QAAA,EACAG,iBAAA,EACAC,aAAA,EACAnJ,oBAAA,EACAgJ,CAAA,EACAC,CAAA,EACA9F,CAAA,EACA4G,gBAAA;QAGFxC,8BAAA,CACEZ,MAAA,EACAoC,QAAA,EACAK,eAAA,EACAE,WAAA,EACAtJ,oBAAA,EACAgJ,CAAA,EACAC,CAAA,EACA9F,CAAA,EACA8G,cAAA;MAEJ;IACF;EAAA,WACSf,iBAAA,KAAsB,QAAW;IAGtC,IAAArE,KAAA,CAAMqF,OAAA,CAAQnB,QAAQ,GAAG;MAC3B,KAAKzI,CAAA,GAAI,GAAGmJ,EAAA,GAAKF,MAAA,CAAO3H,MAAA,EAAQtB,CAAA,GAAImJ,EAAA,EAAInJ,CAAA,IAAK;QAC3CqJ,KAAA,GAAQJ,MAAA,CAAOjJ,CAAC;QACAsJ,aAAA,GAAAb,QAAA,CAASY,KAAA,CAAMQ,aAAuB;QAEtDN,KAAA,GAAQ7F,IAAA,CAAKC,GAAA,CAAI0F,KAAA,CAAME,KAAA,EAAOL,SAAA,CAAUK,KAAK;QACvCC,GAAA,GAAA9F,IAAA,CAAKoG,GAAA,CAAIT,KAAA,CAAME,KAAA,GAAQF,KAAA,CAAM3I,KAAA,EAAOwI,SAAA,CAAUK,KAAA,GAAQL,SAAA,CAAUxI,KAAK;QAE3E,KAAKK,CAAA,GAAIwI,KAAA,EAAOH,EAAA,GAAKI,GAAA,EAAKzI,CAAA,GAAIqI,EAAA,EAAIrI,CAAA,IAAK,GAAG;UACpC2H,CAAA,GAAA3H,CAAA;UACJ4H,CAAA,GAAI5H,CAAA,GAAI;UACR8B,CAAA,GAAI9B,CAAA,GAAI;UAERkG,8BAAA,CACEZ,MAAA,EACAiD,aAAA,EACAV,iBAAA,EACAC,aAAA,EACAnJ,oBAAA,EACAgJ,CAAA,EACAC,CAAA,EACA9F,CAAA,EACA4G,gBAAA;UAGFxC,8BAAA,CACEZ,MAAA,EACAiD,aAAA,EACAR,eAAA,EACAE,WAAA,EACAtJ,oBAAA,EACAgJ,CAAA,EACAC,CAAA,EACA9F,CAAA,EACA8G,cAAA;QAEJ;MACF;IAAA,OACK;MACLJ,KAAA,GAAQ7F,IAAA,CAAKC,GAAA,CAAI,GAAGuF,SAAA,CAAUK,KAAK;MACnCC,GAAA,GAAM9F,IAAA,CAAKoG,GAAA,CAAIlB,iBAAA,CAAkBlI,KAAA,EAAOwI,SAAA,CAAUK,KAAA,GAAQL,SAAA,CAAUxI,KAAK;MAEzE,KAAKV,CAAA,GAAIuJ,KAAA,EAAOJ,EAAA,GAAKK,GAAA,EAAKxJ,CAAA,GAAImJ,EAAA,EAAInJ,CAAA,IAAK,GAAG;QACpC0I,CAAA,GAAA1I,CAAA;QACJ2I,CAAA,GAAI3I,CAAA,GAAI;QACR6C,CAAA,GAAI7C,CAAA,GAAI;QAERiH,8BAAA,CACEZ,MAAA,EACAoC,QAAA,EACAG,iBAAA,EACAC,aAAA,EACAnJ,oBAAA,EACAgJ,CAAA,EACAC,CAAA,EACA9F,CAAA,EACA4G,gBAAA;QAGFxC,8BAAA,CACEZ,MAAA,EACAoC,QAAA,EACAK,eAAA,EACAE,WAAA,EACAtJ,oBAAA,EACAgJ,CAAA,EACAC,CAAA,EACA9F,CAAA,EACA8G,cAAA;MAEJ;IACF;EACF;EAEA,MAAMI,wBAAA,GAA2B,IAAIC,sBAAA,CAAuBP,gBAAA,EAAkB,CAAC;EAC/E,MAAMQ,sBAAA,GAAyB,IAAID,sBAAA,CAAuBL,cAAA,EAAgB,CAAC;EAEpE;IACLf,iBAAA;IACAE,eAAA;IACAiB,wBAAA;IACAE;EAAA;AAEJ;AAiBO,SAASC,iBAAiBhH,QAAA,EAA0BiH,WAAA,GAAczG,IAAA,CAAK0G,EAAA,GAAK,GAAoC;EAC/G,MAAAC,SAAA,GAAY3G,IAAA,CAAK4G,GAAA,CAAIH,WAAW;EAChC,MAAAI,cAAA,IAAkB,IAAI,SAAS;EAG/B,MAAAC,KAAA,GAAQ,CAAC,IAAIhE,OAAA,CAAQ,GAAG,IAAIA,OAAA,IAAW,IAAIA,OAAA,EAAS;EACpD,MAAAiE,QAAA,GAAW,IAAIjE,OAAA;EACf,MAAAkE,QAAA,GAAW,IAAIlE,OAAA;EACf,MAAAmE,QAAA,GAAW,IAAInE,OAAA;EACf,MAAAoE,SAAA,GAAY,IAAIpE,OAAA;EAGtB,SAASqE,WAAWC,CAAA,EAAoB;IACtC,MAAMxC,CAAA,GAAI,CAAC,EAAEwC,CAAA,CAAExC,CAAA,GAAIiC,cAAA;IACnB,MAAMhC,CAAA,GAAI,CAAC,EAAEuC,CAAA,CAAEvC,CAAA,GAAIgC,cAAA;IACnB,MAAM/B,CAAA,GAAI,CAAC,EAAEsC,CAAA,CAAEtC,CAAA,GAAI+B,cAAA;IACZ,UAAGjC,CAAA,IAAKC,CAAA,IAAKC,CAAA;EACtB;EAEA,MAAMuC,cAAA,GAAiB7H,QAAA,CAAShE,KAAA,GAAQgE,QAAA,CAAS8H,YAAA,CAAiB,IAAA9H,QAAA;EAC5D,MAAA+H,OAAA,GAAUF,cAAA,CAAexL,UAAA,CAAWoB,QAAA;EAC1C,MAAMuK,SAAA,GAA0C;EAGvC,SAAAlL,CAAA,GAAI,GAAGoC,CAAA,GAAI6I,OAAA,CAAQvK,KAAA,GAAQ,GAAGV,CAAA,GAAIoC,CAAA,EAAGpC,CAAA,IAAK;IACjD,MAAMmL,EAAA,GAAK,IAAInL,CAAA;IACf,MAAM0I,CAAA,GAAI8B,KAAA,CAAM,CAAC,EAAE/C,mBAAA,CAAoBwD,OAAA,EAASE,EAAA,GAAK,CAAC;IACtD,MAAMxC,CAAA,GAAI6B,KAAA,CAAM,CAAC,EAAE/C,mBAAA,CAAoBwD,OAAA,EAASE,EAAA,GAAK,CAAC;IACtD,MAAMtI,CAAA,GAAI2H,KAAA,CAAM,CAAC,EAAE/C,mBAAA,CAAoBwD,OAAA,EAASE,EAAA,GAAK,CAAC;IAE7CV,QAAA,CAAAW,UAAA,CAAWvI,CAAA,EAAG8F,CAAC;IACf+B,QAAA,CAAAU,UAAA,CAAW1C,CAAA,EAAGC,CAAC;IAGlB,MAAAI,MAAA,GAAS,IAAIvC,OAAA,GAAU6E,YAAA,CAAaZ,QAAA,EAAUC,QAAQ,EAAEY,SAAA;IAC9D,SAASC,CAAA,GAAI,GAAGA,CAAA,GAAI,GAAGA,CAAA,IAAK;MACpB,MAAAC,IAAA,GAAOhB,KAAA,CAAMe,CAAC;MACd,MAAAzG,IAAA,GAAO+F,UAAA,CAAWW,IAAI;MACxB,MAAE1G,IAAA,IAAQoG,SAAA,GAAY;QACdA,SAAA,CAAApG,IAAI,IAAI;MACpB;MAEUoG,SAAA,CAAApG,IAAI,EAAExE,IAAA,CAAKyI,MAAM;IAC7B;EACF;EAIA,MAAM0C,WAAA,GAAc,IAAI/B,YAAA,CAAauB,OAAA,CAAQvK,KAAA,GAAQ,CAAC;EACtD,MAAMgL,QAAA,GAAW,IAAIzJ,eAAA,CAAgBwJ,WAAA,EAAa,GAAG,KAAK;EACjD,SAAAzL,CAAA,GAAI,GAAGoC,CAAA,GAAI6I,OAAA,CAAQvK,KAAA,GAAQ,GAAGV,CAAA,GAAIoC,CAAA,EAAGpC,CAAA,IAAK;IAEjD,MAAMmL,EAAA,GAAK,IAAInL,CAAA;IACf,MAAM0I,CAAA,GAAI8B,KAAA,CAAM,CAAC,EAAE/C,mBAAA,CAAoBwD,OAAA,EAASE,EAAA,GAAK,CAAC;IACtD,MAAMxC,CAAA,GAAI6B,KAAA,CAAM,CAAC,EAAE/C,mBAAA,CAAoBwD,OAAA,EAASE,EAAA,GAAK,CAAC;IACtD,MAAMtI,CAAA,GAAI2H,KAAA,CAAM,CAAC,EAAE/C,mBAAA,CAAoBwD,OAAA,EAASE,EAAA,GAAK,CAAC;IAE7CV,QAAA,CAAAW,UAAA,CAAWvI,CAAA,EAAG8F,CAAC;IACf+B,QAAA,CAAAU,UAAA,CAAW1C,CAAA,EAAGC,CAAC;IAExBgC,QAAA,CAASU,YAAA,CAAaZ,QAAA,EAAUC,QAAQ,EAAEY,SAAA,CAAU;IAGpD,SAASC,CAAA,GAAI,GAAGA,CAAA,GAAI,GAAGA,CAAA,IAAK;MACpB,MAAAC,IAAA,GAAOhB,KAAA,CAAMe,CAAC;MACd,MAAAzG,IAAA,GAAO+F,UAAA,CAAWW,IAAI;MACtB,MAAAG,YAAA,GAAeT,SAAA,CAAUpG,IAAI;MACzB8F,SAAA,CAAA5I,GAAA,CAAI,GAAG,GAAG,CAAC;MAErB,SAASc,CAAA,GAAI,GAAG8I,EAAA,GAAKD,YAAA,CAAarK,MAAA,EAAQwB,CAAA,GAAI8I,EAAA,EAAI9I,CAAA,IAAK;QAC/C,MAAA+I,SAAA,GAAYF,YAAA,CAAa7I,CAAC;QAChC,IAAI6H,QAAA,CAASmB,GAAA,CAAID,SAAS,IAAIxB,SAAA,EAAW;UACvCO,SAAA,CAAUzC,GAAA,CAAI0D,SAAS;QACzB;MACF;MAEAjB,SAAA,CAAUU,SAAA,CAAU;MACXI,QAAA,CAAAK,MAAA,CAAOZ,EAAA,GAAKI,CAAA,EAAGX,SAAA,CAAUtC,CAAA,EAAGsC,SAAA,CAAUrC,CAAA,EAAGqC,SAAA,CAAUpC,CAAC;IAC/D;EACF;EAEeuC,cAAA,CAAA3J,YAAA,CAAa,UAAUsK,QAAQ;EACvC,OAAAX,cAAA;AACT", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}