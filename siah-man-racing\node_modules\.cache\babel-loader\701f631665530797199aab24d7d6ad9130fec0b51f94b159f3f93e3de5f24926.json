{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Apps_Creations\\\\SiahMan_Racing4Speed_WebGame\\\\siah-man-racing\\\\src\\\\components\\\\ArcadeHUD.tsx\",\n  _s = $RefreshSig$();\nimport React, { useEffect, useState } from 'react';\nimport { useSelector } from 'react-redux';\nimport styled, { keyframes, css } from 'styled-components';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\n// Exciting animations for a 7-year-old!\nconst bounce = keyframes`\n  0%, 20%, 50%, 80%, 100% { transform: translateY(0); }\n  40% { transform: translateY(-10px); }\n  60% { transform: translateY(-5px); }\n`;\nconst glow = keyframes`\n  0%, 100% { box-shadow: 0 0 5px #00ff00; }\n  50% { box-shadow: 0 0 20px #00ff00, 0 0 30px #00ff00; }\n`;\nconst rainbow = keyframes`\n  0% { color: #ff0000; }\n  16% { color: #ff8000; }\n  33% { color: #ffff00; }\n  50% { color: #00ff00; }\n  66% { color: #0080ff; }\n  83% { color: #8000ff; }\n  100% { color: #ff0000; }\n`;\nconst pulse = keyframes`\n  0%, 100% { transform: scale(1); }\n  50% { transform: scale(1.1); }\n`;\n\n// Styled components for the arcade HUD\nconst HUDContainer = styled.div`\n  position: fixed;\n  top: 0;\n  left: 0;\n  right: 0;\n  bottom: 0;\n  pointer-events: none;\n  z-index: 1000;\n  font-family: 'Arial Black', Arial, sans-serif;\n`;\n_c = HUDContainer;\nconst TopBar = styled.div`\n  position: absolute;\n  top: 20px;\n  left: 20px;\n  right: 20px;\n  display: flex;\n  justify-content: space-between;\n  align-items: flex-start;\n`;\n_c2 = TopBar;\nconst ScoreDisplay = styled.div`\n  background: linear-gradient(45deg, #ff6b00, #ffd700);\n  border: 3px solid #fff;\n  border-radius: 15px;\n  padding: 10px 20px;\n  color: #fff;\n  font-size: 24px;\n  font-weight: bold;\n  text-shadow: 2px 2px 4px rgba(0,0,0,0.8);\n  animation: ${props => props.$isGlowing ? glow : 'none'} 1s ease-in-out infinite;\n  \n  .score-label {\n    font-size: 14px;\n    opacity: 0.9;\n  }\n  \n  .score-value {\n    font-size: 28px;\n    animation: ${props => props.$isGlowing ? rainbow : 'none'} 2s linear infinite;\n  }\n`;\n_c3 = ScoreDisplay;\nconst ComboDisplay = styled.div`\n  background: linear-gradient(45deg, #ff00ff, #00ffff);\n  border: 3px solid #fff;\n  border-radius: 15px;\n  padding: 8px 16px;\n  color: #fff;\n  font-size: 18px;\n  font-weight: bold;\n  text-shadow: 2px 2px 4px rgba(0,0,0,0.8);\n  animation: ${props => props.$multiplier > 1 ? bounce : 'none'} 0.6s ease-in-out infinite;\n  \n  ${props => props.$multiplier > 3 && css`\n    animation: ${pulse} 0.3s ease-in-out infinite, ${rainbow} 1s linear infinite;\n  `}\n`;\n_c4 = ComboDisplay;\nconst PowerUpBar = styled.div`\n  position: absolute;\n  top: 100px;\n  right: 20px;\n  display: flex;\n  flex-direction: column;\n  gap: 10px;\n`;\n_c5 = PowerUpBar;\nconst PowerUpIcon = styled.div`\n  width: 60px;\n  height: 60px;\n  border-radius: 50%;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  font-size: 24px;\n  font-weight: bold;\n  border: 3px solid #fff;\n  position: relative;\n  animation: ${glow} 1s ease-in-out infinite;\n  \n  ${props => {\n  switch (props.$type) {\n    case 'invincibility':\n      return css`background: linear-gradient(45deg, #ffd700, #ffff00);`;\n    case 'mega_speed':\n      return css`background: linear-gradient(45deg, #ff0000, #ff8000);`;\n    case 'coin_magnet':\n      return css`background: linear-gradient(45deg, #00ff00, #80ff00);`;\n    case 'double_points':\n      return css`background: linear-gradient(45deg, #8000ff, #ff00ff);`;\n    default:\n      return css`background: linear-gradient(45deg, #666, #999);`;\n  }\n}}\n  \n  &::after {\n    content: '';\n    position: absolute;\n    top: -3px;\n    left: -3px;\n    right: -3px;\n    bottom: -3px;\n    border-radius: 50%;\n    background: conic-gradient(\n      transparent 0deg,\n      transparent ${props => (1 - props.$timeLeft) * 360}deg,\n      rgba(255,255,255,0.8) ${props => (1 - props.$timeLeft) * 360}deg,\n      rgba(255,255,255,0.8) 360deg\n    );\n    z-index: -1;\n  }\n`;\n_c6 = PowerUpIcon;\nconst BottomBar = styled.div`\n  position: absolute;\n  bottom: 20px;\n  left: 20px;\n  right: 20px;\n  display: flex;\n  justify-content: space-between;\n  align-items: flex-end;\n`;\n_c7 = BottomBar;\nconst CoinsDisplay = styled.div`\n  background: linear-gradient(45deg, #ffd700, #ffff80);\n  border: 3px solid #fff;\n  border-radius: 15px;\n  padding: 8px 16px;\n  color: #000;\n  font-size: 20px;\n  font-weight: bold;\n  display: flex;\n  align-items: center;\n  gap: 8px;\n  \n  .coin-icon {\n    font-size: 24px;\n    animation: ${bounce} 1s ease-in-out infinite;\n  }\n`;\n_c8 = CoinsDisplay;\nconst StatusEffects = styled.div`\n  display: flex;\n  gap: 10px;\n  align-items: center;\n`;\n_c9 = StatusEffects;\nconst StatusEffect = styled.div`\n  padding: 6px 12px;\n  border-radius: 20px;\n  font-size: 14px;\n  font-weight: bold;\n  color: #fff;\n  text-shadow: 1px 1px 2px rgba(0,0,0,0.8);\n  animation: ${pulse} 1s ease-in-out infinite;\n  \n  ${props => {\n  switch (props.$type) {\n    case 'jumping':\n      return css`background: linear-gradient(45deg, #00aaff, #0080ff);`;\n    case 'drifting':\n      return css`background: linear-gradient(45deg, #ff6600, #ff8800);`;\n    case 'nitro':\n      return css`background: linear-gradient(45deg, #ff0000, #ff4400);`;\n    case 'speed_boost':\n      return css`background: linear-gradient(45deg, #ff00aa, #ff44cc);`;\n    default:\n      return css`background: linear-gradient(45deg, #666, #888);`;\n  }\n}}\n`;\n_c0 = StatusEffect;\nconst FloatingText = styled.div`\n  position: absolute;\n  left: ${props => props.$x}px;\n  top: ${props => props.$y}px;\n  font-size: 20px;\n  font-weight: bold;\n  color: #fff;\n  text-shadow: 2px 2px 4px rgba(0,0,0,0.8);\n  animation: ${bounce} 0.5s ease-out, fadeOut 2s ease-out forwards;\n  pointer-events: none;\n  z-index: 1001;\n  \n  ${props => {\n  switch (props.$type) {\n    case 'score':\n      return css`color: #ffd700; animation: ${bounce} 0.5s ease-out, ${rainbow} 2s linear infinite;`;\n    case 'combo':\n      return css`color: #ff00ff; font-size: 24px;`;\n    case 'perfect':\n      return css`color: #00ff00; font-size: 28px; animation: ${pulse} 0.3s ease-out infinite;`;\n    default:\n      return css`color: #fff;`;\n  }\n}}\n  \n  @keyframes fadeOut {\n    0% { opacity: 1; transform: translateY(0); }\n    100% { opacity: 0; transform: translateY(-50px); }\n  }\n`;\n_c1 = FloatingText;\nconst ArcadeHUD = ({\n  visible = true\n}) => {\n  _s();\n  var _vehicleState$totalSc;\n  const {\n    vehicleState,\n    raceState\n  } = useSelector(state => state.game);\n  const [floatingTexts, setFloatingTexts] = useState([]);\n  const [lastScore, setLastScore] = useState(0);\n\n  // Add floating text when score increases\n  useEffect(() => {\n    if (vehicleState.totalScore && vehicleState.totalScore > lastScore) {\n      const scoreDiff = vehicleState.totalScore - lastScore;\n      if (scoreDiff > 0) {\n        const newText = {\n          id: Date.now(),\n          x: Math.random() * 200 + 100,\n          y: Math.random() * 100 + 200,\n          text: `+${scoreDiff}`,\n          type: scoreDiff > 100 ? 'perfect' : scoreDiff > 50 ? 'combo' : 'score'\n        };\n        setFloatingTexts(prev => [...prev, newText]);\n\n        // Remove after animation\n        setTimeout(() => {\n          setFloatingTexts(prev => prev.filter(t => t.id !== newText.id));\n        }, 2000);\n      }\n      setLastScore(vehicleState.totalScore);\n    }\n  }, [vehicleState.totalScore, lastScore]);\n  if (!visible || !raceState.isRaceStarted) return null;\n  const now = Date.now();\n  const activePowerUps = [vehicleState.invincibilityUntil && now < vehicleState.invincibilityUntil && {\n    type: 'invincibility',\n    timeLeft: (vehicleState.invincibilityUntil - now) / 5000,\n    icon: '⭐'\n  }, vehicleState.speedBoostUntil && now < vehicleState.speedBoostUntil && {\n    type: 'mega_speed',\n    timeLeft: (vehicleState.speedBoostUntil - now) / 3000,\n    icon: '🚀'\n  }, vehicleState.coinMagnetUntil && now < vehicleState.coinMagnetUntil && {\n    type: 'coin_magnet',\n    timeLeft: (vehicleState.coinMagnetUntil - now) / 8000,\n    icon: '🧲'\n  }, vehicleState.doublePointsUntil && now < vehicleState.doublePointsUntil && {\n    type: 'double_points',\n    timeLeft: (vehicleState.doublePointsUntil - now) / 10000,\n    icon: '2️⃣'\n  }].filter(Boolean);\n  const activeEffects = [vehicleState.isJumping && {\n    type: 'jumping',\n    text: 'AIRBORNE!'\n  }, vehicleState.isDrifting && {\n    type: 'drifting',\n    text: 'DRIFTING!'\n  }, vehicleState.isNitroActive && {\n    type: 'nitro',\n    text: 'NITRO!'\n  }, now < (vehicleState.speedBoostUntil || 0) && {\n    type: 'speed_boost',\n    text: 'SPEED BOOST!'\n  }].filter(Boolean);\n  return /*#__PURE__*/_jsxDEV(HUDContainer, {\n    children: [/*#__PURE__*/_jsxDEV(TopBar, {\n      children: [/*#__PURE__*/_jsxDEV(ScoreDisplay, {\n        $isGlowing: !!(vehicleState.comboMultiplier && vehicleState.comboMultiplier > 2),\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"score-label\",\n          children: \"SCORE\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 314,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"score-value\",\n          children: ((_vehicleState$totalSc = vehicleState.totalScore) === null || _vehicleState$totalSc === void 0 ? void 0 : _vehicleState$totalSc.toLocaleString()) || 0\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 315,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 313,\n        columnNumber: 9\n      }, this), vehicleState.comboMultiplier && vehicleState.comboMultiplier > 1 && /*#__PURE__*/_jsxDEV(ComboDisplay, {\n        $multiplier: vehicleState.comboMultiplier,\n        children: [\"COMBO x\", vehicleState.comboMultiplier.toFixed(1)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 319,\n        columnNumber: 11\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 312,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(PowerUpBar, {\n      children: activePowerUps.map((powerUp, index) => /*#__PURE__*/_jsxDEV(PowerUpIcon, {\n        $type: powerUp.type,\n        $timeLeft: powerUp.timeLeft,\n        children: powerUp.icon\n      }, powerUp.type, false, {\n        fileName: _jsxFileName,\n        lineNumber: 327,\n        columnNumber: 11\n      }, this))\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 325,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(BottomBar, {\n      children: [/*#__PURE__*/_jsxDEV(CoinsDisplay, {\n        children: [/*#__PURE__*/_jsxDEV(\"span\", {\n          className: \"coin-icon\",\n          children: \"\\uD83E\\uDE99\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 339,\n          columnNumber: 11\n        }, this), vehicleState.coinsCollected || 0]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 338,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(StatusEffects, {\n        children: activeEffects.map((effect, index) => /*#__PURE__*/_jsxDEV(StatusEffect, {\n          $type: effect.type,\n          children: effect.text\n        }, effect.type, false, {\n          fileName: _jsxFileName,\n          lineNumber: 345,\n          columnNumber: 13\n        }, this))\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 343,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 337,\n      columnNumber: 7\n    }, this), floatingTexts.map(text => /*#__PURE__*/_jsxDEV(FloatingText, {\n      $x: text.x,\n      $y: text.y,\n      $type: text.type,\n      children: text.text\n    }, text.id, false, {\n      fileName: _jsxFileName,\n      lineNumber: 354,\n      columnNumber: 9\n    }, this))]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 311,\n    columnNumber: 5\n  }, this);\n};\n_s(ArcadeHUD, \"W5slJjeZ9CC3T4aO/PEqTSKGdp4=\", false, function () {\n  return [useSelector];\n});\n_c10 = ArcadeHUD;\nexport default ArcadeHUD;\nvar _c, _c2, _c3, _c4, _c5, _c6, _c7, _c8, _c9, _c0, _c1, _c10;\n$RefreshReg$(_c, \"HUDContainer\");\n$RefreshReg$(_c2, \"TopBar\");\n$RefreshReg$(_c3, \"ScoreDisplay\");\n$RefreshReg$(_c4, \"ComboDisplay\");\n$RefreshReg$(_c5, \"PowerUpBar\");\n$RefreshReg$(_c6, \"PowerUpIcon\");\n$RefreshReg$(_c7, \"BottomBar\");\n$RefreshReg$(_c8, \"CoinsDisplay\");\n$RefreshReg$(_c9, \"StatusEffects\");\n$RefreshReg$(_c0, \"StatusEffect\");\n$RefreshReg$(_c1, \"FloatingText\");\n$RefreshReg$(_c10, \"ArcadeHUD\");", "map": {"version": 3, "names": ["React", "useEffect", "useState", "useSelector", "styled", "keyframes", "css", "jsxDEV", "_jsxDEV", "bounce", "glow", "rainbow", "pulse", "HUDContainer", "div", "_c", "TopBar", "_c2", "ScoreDisplay", "props", "$isGlowing", "_c3", "ComboDisplay", "$multiplier", "_c4", "PowerUpBar", "_c5", "PowerUpIcon", "$type", "$timeLeft", "_c6", "BottomBar", "_c7", "CoinsDisplay", "_c8", "StatusEffects", "_c9", "StatusEffect", "_c0", "FloatingText", "$x", "$y", "_c1", "ArcadeHUD", "visible", "_s", "_vehicleState$totalSc", "vehicleState", "raceState", "state", "game", "floatingTexts", "setFloatingTexts", "lastScore", "setLastScore", "totalScore", "scoreDiff", "newText", "id", "Date", "now", "x", "Math", "random", "y", "text", "type", "prev", "setTimeout", "filter", "t", "isRaceStarted", "activePowerUps", "invincibilityUntil", "timeLeft", "icon", "speedBoostUntil", "coinMagnetUntil", "doublePointsUntil", "Boolean", "activeEffects", "isJumping", "isDrifting", "isNitroActive", "children", "comboMultiplier", "className", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "toLocaleString", "toFixed", "map", "powerUp", "index", "coinsCollected", "effect", "_c10", "$RefreshReg$"], "sources": ["C:/Users/<USER>/Documents/Apps_Creations/SiahMan_Racing4Speed_WebGame/siah-man-racing/src/components/ArcadeHUD.tsx"], "sourcesContent": ["import React, { useEffect, useState } from 'react';\nimport { useSelector } from 'react-redux';\nimport styled, { keyframes, css } from 'styled-components';\nimport { RootState } from '../store';\n\n// Exciting animations for a 7-year-old!\nconst bounce = keyframes`\n  0%, 20%, 50%, 80%, 100% { transform: translateY(0); }\n  40% { transform: translateY(-10px); }\n  60% { transform: translateY(-5px); }\n`;\n\nconst glow = keyframes`\n  0%, 100% { box-shadow: 0 0 5px #00ff00; }\n  50% { box-shadow: 0 0 20px #00ff00, 0 0 30px #00ff00; }\n`;\n\nconst rainbow = keyframes`\n  0% { color: #ff0000; }\n  16% { color: #ff8000; }\n  33% { color: #ffff00; }\n  50% { color: #00ff00; }\n  66% { color: #0080ff; }\n  83% { color: #8000ff; }\n  100% { color: #ff0000; }\n`;\n\nconst pulse = keyframes`\n  0%, 100% { transform: scale(1); }\n  50% { transform: scale(1.1); }\n`;\n\n// Styled components for the arcade HUD\nconst HUDContainer = styled.div`\n  position: fixed;\n  top: 0;\n  left: 0;\n  right: 0;\n  bottom: 0;\n  pointer-events: none;\n  z-index: 1000;\n  font-family: 'Arial Black', Arial, sans-serif;\n`;\n\nconst TopBar = styled.div`\n  position: absolute;\n  top: 20px;\n  left: 20px;\n  right: 20px;\n  display: flex;\n  justify-content: space-between;\n  align-items: flex-start;\n`;\n\nconst ScoreDisplay = styled.div<{ $isGlowing?: boolean }>`\n  background: linear-gradient(45deg, #ff6b00, #ffd700);\n  border: 3px solid #fff;\n  border-radius: 15px;\n  padding: 10px 20px;\n  color: #fff;\n  font-size: 24px;\n  font-weight: bold;\n  text-shadow: 2px 2px 4px rgba(0,0,0,0.8);\n  animation: ${props => props.$isGlowing ? glow : 'none'} 1s ease-in-out infinite;\n  \n  .score-label {\n    font-size: 14px;\n    opacity: 0.9;\n  }\n  \n  .score-value {\n    font-size: 28px;\n    animation: ${props => props.$isGlowing ? rainbow : 'none'} 2s linear infinite;\n  }\n`;\n\nconst ComboDisplay = styled.div<{ $multiplier: number }>`\n  background: linear-gradient(45deg, #ff00ff, #00ffff);\n  border: 3px solid #fff;\n  border-radius: 15px;\n  padding: 8px 16px;\n  color: #fff;\n  font-size: 18px;\n  font-weight: bold;\n  text-shadow: 2px 2px 4px rgba(0,0,0,0.8);\n  animation: ${props => props.$multiplier > 1 ? bounce : 'none'} 0.6s ease-in-out infinite;\n  \n  ${props => props.$multiplier > 3 && css`\n    animation: ${pulse} 0.3s ease-in-out infinite, ${rainbow} 1s linear infinite;\n  `}\n`;\n\nconst PowerUpBar = styled.div`\n  position: absolute;\n  top: 100px;\n  right: 20px;\n  display: flex;\n  flex-direction: column;\n  gap: 10px;\n`;\n\nconst PowerUpIcon = styled.div<{ $type: string; $timeLeft: number }>`\n  width: 60px;\n  height: 60px;\n  border-radius: 50%;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  font-size: 24px;\n  font-weight: bold;\n  border: 3px solid #fff;\n  position: relative;\n  animation: ${glow} 1s ease-in-out infinite;\n  \n  ${props => {\n    switch (props.$type) {\n      case 'invincibility':\n        return css`background: linear-gradient(45deg, #ffd700, #ffff00);`;\n      case 'mega_speed':\n        return css`background: linear-gradient(45deg, #ff0000, #ff8000);`;\n      case 'coin_magnet':\n        return css`background: linear-gradient(45deg, #00ff00, #80ff00);`;\n      case 'double_points':\n        return css`background: linear-gradient(45deg, #8000ff, #ff00ff);`;\n      default:\n        return css`background: linear-gradient(45deg, #666, #999);`;\n    }\n  }}\n  \n  &::after {\n    content: '';\n    position: absolute;\n    top: -3px;\n    left: -3px;\n    right: -3px;\n    bottom: -3px;\n    border-radius: 50%;\n    background: conic-gradient(\n      transparent 0deg,\n      transparent ${props => (1 - props.$timeLeft) * 360}deg,\n      rgba(255,255,255,0.8) ${props => (1 - props.$timeLeft) * 360}deg,\n      rgba(255,255,255,0.8) 360deg\n    );\n    z-index: -1;\n  }\n`;\n\nconst BottomBar = styled.div`\n  position: absolute;\n  bottom: 20px;\n  left: 20px;\n  right: 20px;\n  display: flex;\n  justify-content: space-between;\n  align-items: flex-end;\n`;\n\nconst CoinsDisplay = styled.div`\n  background: linear-gradient(45deg, #ffd700, #ffff80);\n  border: 3px solid #fff;\n  border-radius: 15px;\n  padding: 8px 16px;\n  color: #000;\n  font-size: 20px;\n  font-weight: bold;\n  display: flex;\n  align-items: center;\n  gap: 8px;\n  \n  .coin-icon {\n    font-size: 24px;\n    animation: ${bounce} 1s ease-in-out infinite;\n  }\n`;\n\nconst StatusEffects = styled.div`\n  display: flex;\n  gap: 10px;\n  align-items: center;\n`;\n\nconst StatusEffect = styled.div<{ $type: string }>`\n  padding: 6px 12px;\n  border-radius: 20px;\n  font-size: 14px;\n  font-weight: bold;\n  color: #fff;\n  text-shadow: 1px 1px 2px rgba(0,0,0,0.8);\n  animation: ${pulse} 1s ease-in-out infinite;\n  \n  ${props => {\n    switch (props.$type) {\n      case 'jumping':\n        return css`background: linear-gradient(45deg, #00aaff, #0080ff);`;\n      case 'drifting':\n        return css`background: linear-gradient(45deg, #ff6600, #ff8800);`;\n      case 'nitro':\n        return css`background: linear-gradient(45deg, #ff0000, #ff4400);`;\n      case 'speed_boost':\n        return css`background: linear-gradient(45deg, #ff00aa, #ff44cc);`;\n      default:\n        return css`background: linear-gradient(45deg, #666, #888);`;\n    }\n  }}\n`;\n\nconst FloatingText = styled.div<{ $x: number; $y: number; $type: string }>`\n  position: absolute;\n  left: ${props => props.$x}px;\n  top: ${props => props.$y}px;\n  font-size: 20px;\n  font-weight: bold;\n  color: #fff;\n  text-shadow: 2px 2px 4px rgba(0,0,0,0.8);\n  animation: ${bounce} 0.5s ease-out, fadeOut 2s ease-out forwards;\n  pointer-events: none;\n  z-index: 1001;\n  \n  ${props => {\n    switch (props.$type) {\n      case 'score':\n        return css`color: #ffd700; animation: ${bounce} 0.5s ease-out, ${rainbow} 2s linear infinite;`;\n      case 'combo':\n        return css`color: #ff00ff; font-size: 24px;`;\n      case 'perfect':\n        return css`color: #00ff00; font-size: 28px; animation: ${pulse} 0.3s ease-out infinite;`;\n      default:\n        return css`color: #fff;`;\n    }\n  }}\n  \n  @keyframes fadeOut {\n    0% { opacity: 1; transform: translateY(0); }\n    100% { opacity: 0; transform: translateY(-50px); }\n  }\n`;\n\ninterface ArcadeHUDProps {\n  visible?: boolean;\n}\n\nconst ArcadeHUD: React.FC<ArcadeHUDProps> = ({ visible = true }) => {\n  const { vehicleState, raceState } = useSelector((state: RootState) => state.game);\n  const [floatingTexts, setFloatingTexts] = useState<Array<{\n    id: number;\n    x: number;\n    y: number;\n    text: string;\n    type: string;\n  }>>([]);\n  const [lastScore, setLastScore] = useState(0);\n\n  // Add floating text when score increases\n  useEffect(() => {\n    if (vehicleState.totalScore && vehicleState.totalScore > lastScore) {\n      const scoreDiff = vehicleState.totalScore - lastScore;\n      if (scoreDiff > 0) {\n        const newText = {\n          id: Date.now(),\n          x: Math.random() * 200 + 100,\n          y: Math.random() * 100 + 200,\n          text: `+${scoreDiff}`,\n          type: scoreDiff > 100 ? 'perfect' : scoreDiff > 50 ? 'combo' : 'score'\n        };\n        \n        setFloatingTexts(prev => [...prev, newText]);\n        \n        // Remove after animation\n        setTimeout(() => {\n          setFloatingTexts(prev => prev.filter(t => t.id !== newText.id));\n        }, 2000);\n      }\n      setLastScore(vehicleState.totalScore);\n    }\n  }, [vehicleState.totalScore, lastScore]);\n\n  if (!visible || !raceState.isRaceStarted) return null;\n\n  const now = Date.now();\n  const activePowerUps = [\n    vehicleState.invincibilityUntil && now < vehicleState.invincibilityUntil && {\n      type: 'invincibility',\n      timeLeft: (vehicleState.invincibilityUntil - now) / 5000,\n      icon: '⭐'\n    },\n    vehicleState.speedBoostUntil && now < vehicleState.speedBoostUntil && {\n      type: 'mega_speed',\n      timeLeft: (vehicleState.speedBoostUntil - now) / 3000,\n      icon: '🚀'\n    },\n    vehicleState.coinMagnetUntil && now < vehicleState.coinMagnetUntil && {\n      type: 'coin_magnet',\n      timeLeft: (vehicleState.coinMagnetUntil - now) / 8000,\n      icon: '🧲'\n    },\n    vehicleState.doublePointsUntil && now < vehicleState.doublePointsUntil && {\n      type: 'double_points',\n      timeLeft: (vehicleState.doublePointsUntil - now) / 10000,\n      icon: '2️⃣'\n    }\n  ].filter(Boolean);\n\n  const activeEffects = [\n    vehicleState.isJumping && { type: 'jumping', text: 'AIRBORNE!' },\n    vehicleState.isDrifting && { type: 'drifting', text: 'DRIFTING!' },\n    vehicleState.isNitroActive && { type: 'nitro', text: 'NITRO!' },\n    now < (vehicleState.speedBoostUntil || 0) && { type: 'speed_boost', text: 'SPEED BOOST!' }\n  ].filter(Boolean);\n\n  return (\n    <HUDContainer>\n      <TopBar>\n        <ScoreDisplay $isGlowing={!!(vehicleState.comboMultiplier && vehicleState.comboMultiplier > 2)}>\n          <div className=\"score-label\">SCORE</div>\n          <div className=\"score-value\">{vehicleState.totalScore?.toLocaleString() || 0}</div>\n        </ScoreDisplay>\n        \n        {vehicleState.comboMultiplier && vehicleState.comboMultiplier > 1 && (\n          <ComboDisplay $multiplier={vehicleState.comboMultiplier}>\n            COMBO x{vehicleState.comboMultiplier.toFixed(1)}\n          </ComboDisplay>\n        )}\n      </TopBar>\n\n      <PowerUpBar>\n        {activePowerUps.map((powerUp: any, index) => (\n          <PowerUpIcon\n            key={powerUp.type}\n            $type={powerUp.type}\n            $timeLeft={powerUp.timeLeft}\n          >\n            {powerUp.icon}\n          </PowerUpIcon>\n        ))}\n      </PowerUpBar>\n\n      <BottomBar>\n        <CoinsDisplay>\n          <span className=\"coin-icon\">🪙</span>\n          {vehicleState.coinsCollected || 0}\n        </CoinsDisplay>\n        \n        <StatusEffects>\n          {activeEffects.map((effect: any, index) => (\n            <StatusEffect key={effect.type} $type={effect.type}>\n              {effect.text}\n            </StatusEffect>\n          ))}\n        </StatusEffects>\n      </BottomBar>\n\n      {/* Floating score texts */}\n      {floatingTexts.map(text => (\n        <FloatingText\n          key={text.id}\n          $x={text.x}\n          $y={text.y}\n          $type={text.type}\n        >\n          {text.text}\n        </FloatingText>\n      ))}\n    </HUDContainer>\n  );\n};\n\nexport default ArcadeHUD;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,SAAS,EAAEC,QAAQ,QAAQ,OAAO;AAClD,SAASC,WAAW,QAAQ,aAAa;AACzC,OAAOC,MAAM,IAAIC,SAAS,EAAEC,GAAG,QAAQ,mBAAmB;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAG3D;AACA,MAAMC,MAAM,GAAGJ,SAAS;AACxB;AACA;AACA;AACA,CAAC;AAED,MAAMK,IAAI,GAAGL,SAAS;AACtB;AACA;AACA,CAAC;AAED,MAAMM,OAAO,GAAGN,SAAS;AACzB;AACA;AACA;AACA;AACA;AACA;AACA;AACA,CAAC;AAED,MAAMO,KAAK,GAAGP,SAAS;AACvB;AACA;AACA,CAAC;;AAED;AACA,MAAMQ,YAAY,GAAGT,MAAM,CAACU,GAAG;AAC/B;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,CAAC;AAACC,EAAA,GATIF,YAAY;AAWlB,MAAMG,MAAM,GAAGZ,MAAM,CAACU,GAAG;AACzB;AACA;AACA;AACA;AACA;AACA;AACA;AACA,CAAC;AAACG,GAAA,GARID,MAAM;AAUZ,MAAME,YAAY,GAAGd,MAAM,CAACU,GAA6B;AACzD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,eAAeK,KAAK,IAAIA,KAAK,CAACC,UAAU,GAAGV,IAAI,GAAG,MAAM;AACxD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,iBAAiBS,KAAK,IAAIA,KAAK,CAACC,UAAU,GAAGT,OAAO,GAAG,MAAM;AAC7D;AACA,CAAC;AAACU,GAAA,GApBIH,YAAY;AAsBlB,MAAMI,YAAY,GAAGlB,MAAM,CAACU,GAA4B;AACxD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,eAAeK,KAAK,IAAIA,KAAK,CAACI,WAAW,GAAG,CAAC,GAAGd,MAAM,GAAG,MAAM;AAC/D;AACA,IAAIU,KAAK,IAAIA,KAAK,CAACI,WAAW,GAAG,CAAC,IAAIjB,GAAG;AACzC,iBAAiBM,KAAK,+BAA+BD,OAAO;AAC5D,GAAG;AACH,CAAC;AAACa,GAAA,GAdIF,YAAY;AAgBlB,MAAMG,UAAU,GAAGrB,MAAM,CAACU,GAAG;AAC7B;AACA;AACA;AACA;AACA;AACA;AACA,CAAC;AAACY,GAAA,GAPID,UAAU;AAShB,MAAME,WAAW,GAAGvB,MAAM,CAACU,GAAyC;AACpE;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,eAAeJ,IAAI;AACnB;AACA,IAAIS,KAAK,IAAI;EACT,QAAQA,KAAK,CAACS,KAAK;IACjB,KAAK,eAAe;MAClB,OAAOtB,GAAG,uDAAuD;IACnE,KAAK,YAAY;MACf,OAAOA,GAAG,uDAAuD;IACnE,KAAK,aAAa;MAChB,OAAOA,GAAG,uDAAuD;IACnE,KAAK,eAAe;MAClB,OAAOA,GAAG,uDAAuD;IACnE;MACE,OAAOA,GAAG,iDAAiD;EAC/D;AACF,CAAC;AACH;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,oBAAoBa,KAAK,IAAI,CAAC,CAAC,GAAGA,KAAK,CAACU,SAAS,IAAI,GAAG;AACxD,8BAA8BV,KAAK,IAAI,CAAC,CAAC,GAAGA,KAAK,CAACU,SAAS,IAAI,GAAG;AAClE;AACA;AACA;AACA;AACA,CAAC;AAACC,GAAA,GA5CIH,WAAW;AA8CjB,MAAMI,SAAS,GAAG3B,MAAM,CAACU,GAAG;AAC5B;AACA;AACA;AACA;AACA;AACA;AACA;AACA,CAAC;AAACkB,GAAA,GARID,SAAS;AAUf,MAAME,YAAY,GAAG7B,MAAM,CAACU,GAAG;AAC/B;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,iBAAiBL,MAAM;AACvB;AACA,CAAC;AAACyB,GAAA,GAhBID,YAAY;AAkBlB,MAAME,aAAa,GAAG/B,MAAM,CAACU,GAAG;AAChC;AACA;AACA;AACA,CAAC;AAACsB,GAAA,GAJID,aAAa;AAMnB,MAAME,YAAY,GAAGjC,MAAM,CAACU,GAAsB;AAClD;AACA;AACA;AACA;AACA;AACA;AACA,eAAeF,KAAK;AACpB;AACA,IAAIO,KAAK,IAAI;EACT,QAAQA,KAAK,CAACS,KAAK;IACjB,KAAK,SAAS;MACZ,OAAOtB,GAAG,uDAAuD;IACnE,KAAK,UAAU;MACb,OAAOA,GAAG,uDAAuD;IACnE,KAAK,OAAO;MACV,OAAOA,GAAG,uDAAuD;IACnE,KAAK,aAAa;MAChB,OAAOA,GAAG,uDAAuD;IACnE;MACE,OAAOA,GAAG,iDAAiD;EAC/D;AACF,CAAC;AACH,CAAC;AAACgC,GAAA,GAvBID,YAAY;AAyBlB,MAAME,YAAY,GAAGnC,MAAM,CAACU,GAA8C;AAC1E;AACA,UAAUK,KAAK,IAAIA,KAAK,CAACqB,EAAE;AAC3B,SAASrB,KAAK,IAAIA,KAAK,CAACsB,EAAE;AAC1B;AACA;AACA;AACA;AACA,eAAehC,MAAM;AACrB;AACA;AACA;AACA,IAAIU,KAAK,IAAI;EACT,QAAQA,KAAK,CAACS,KAAK;IACjB,KAAK,OAAO;MACV,OAAOtB,GAAG,8BAA8BG,MAAM,mBAAmBE,OAAO,sBAAsB;IAChG,KAAK,OAAO;MACV,OAAOL,GAAG,kCAAkC;IAC9C,KAAK,SAAS;MACZ,OAAOA,GAAG,+CAA+CM,KAAK,0BAA0B;IAC1F;MACE,OAAON,GAAG,cAAc;EAC5B;AACF,CAAC;AACH;AACA;AACA;AACA;AACA;AACA,CAAC;AAACoC,GAAA,GA7BIH,YAAY;AAmClB,MAAMI,SAAmC,GAAGA,CAAC;EAAEC,OAAO,GAAG;AAAK,CAAC,KAAK;EAAAC,EAAA;EAAA,IAAAC,qBAAA;EAClE,MAAM;IAAEC,YAAY;IAAEC;EAAU,CAAC,GAAG7C,WAAW,CAAE8C,KAAgB,IAAKA,KAAK,CAACC,IAAI,CAAC;EACjF,MAAM,CAACC,aAAa,EAAEC,gBAAgB,CAAC,GAAGlD,QAAQ,CAM9C,EAAE,CAAC;EACP,MAAM,CAACmD,SAAS,EAAEC,YAAY,CAAC,GAAGpD,QAAQ,CAAC,CAAC,CAAC;;EAE7C;EACAD,SAAS,CAAC,MAAM;IACd,IAAI8C,YAAY,CAACQ,UAAU,IAAIR,YAAY,CAACQ,UAAU,GAAGF,SAAS,EAAE;MAClE,MAAMG,SAAS,GAAGT,YAAY,CAACQ,UAAU,GAAGF,SAAS;MACrD,IAAIG,SAAS,GAAG,CAAC,EAAE;QACjB,MAAMC,OAAO,GAAG;UACdC,EAAE,EAAEC,IAAI,CAACC,GAAG,CAAC,CAAC;UACdC,CAAC,EAAEC,IAAI,CAACC,MAAM,CAAC,CAAC,GAAG,GAAG,GAAG,GAAG;UAC5BC,CAAC,EAAEF,IAAI,CAACC,MAAM,CAAC,CAAC,GAAG,GAAG,GAAG,GAAG;UAC5BE,IAAI,EAAE,IAAIT,SAAS,EAAE;UACrBU,IAAI,EAAEV,SAAS,GAAG,GAAG,GAAG,SAAS,GAAGA,SAAS,GAAG,EAAE,GAAG,OAAO,GAAG;QACjE,CAAC;QAEDJ,gBAAgB,CAACe,IAAI,IAAI,CAAC,GAAGA,IAAI,EAAEV,OAAO,CAAC,CAAC;;QAE5C;QACAW,UAAU,CAAC,MAAM;UACfhB,gBAAgB,CAACe,IAAI,IAAIA,IAAI,CAACE,MAAM,CAACC,CAAC,IAAIA,CAAC,CAACZ,EAAE,KAAKD,OAAO,CAACC,EAAE,CAAC,CAAC;QACjE,CAAC,EAAE,IAAI,CAAC;MACV;MACAJ,YAAY,CAACP,YAAY,CAACQ,UAAU,CAAC;IACvC;EACF,CAAC,EAAE,CAACR,YAAY,CAACQ,UAAU,EAAEF,SAAS,CAAC,CAAC;EAExC,IAAI,CAACT,OAAO,IAAI,CAACI,SAAS,CAACuB,aAAa,EAAE,OAAO,IAAI;EAErD,MAAMX,GAAG,GAAGD,IAAI,CAACC,GAAG,CAAC,CAAC;EACtB,MAAMY,cAAc,GAAG,CACrBzB,YAAY,CAAC0B,kBAAkB,IAAIb,GAAG,GAAGb,YAAY,CAAC0B,kBAAkB,IAAI;IAC1EP,IAAI,EAAE,eAAe;IACrBQ,QAAQ,EAAE,CAAC3B,YAAY,CAAC0B,kBAAkB,GAAGb,GAAG,IAAI,IAAI;IACxDe,IAAI,EAAE;EACR,CAAC,EACD5B,YAAY,CAAC6B,eAAe,IAAIhB,GAAG,GAAGb,YAAY,CAAC6B,eAAe,IAAI;IACpEV,IAAI,EAAE,YAAY;IAClBQ,QAAQ,EAAE,CAAC3B,YAAY,CAAC6B,eAAe,GAAGhB,GAAG,IAAI,IAAI;IACrDe,IAAI,EAAE;EACR,CAAC,EACD5B,YAAY,CAAC8B,eAAe,IAAIjB,GAAG,GAAGb,YAAY,CAAC8B,eAAe,IAAI;IACpEX,IAAI,EAAE,aAAa;IACnBQ,QAAQ,EAAE,CAAC3B,YAAY,CAAC8B,eAAe,GAAGjB,GAAG,IAAI,IAAI;IACrDe,IAAI,EAAE;EACR,CAAC,EACD5B,YAAY,CAAC+B,iBAAiB,IAAIlB,GAAG,GAAGb,YAAY,CAAC+B,iBAAiB,IAAI;IACxEZ,IAAI,EAAE,eAAe;IACrBQ,QAAQ,EAAE,CAAC3B,YAAY,CAAC+B,iBAAiB,GAAGlB,GAAG,IAAI,KAAK;IACxDe,IAAI,EAAE;EACR,CAAC,CACF,CAACN,MAAM,CAACU,OAAO,CAAC;EAEjB,MAAMC,aAAa,GAAG,CACpBjC,YAAY,CAACkC,SAAS,IAAI;IAAEf,IAAI,EAAE,SAAS;IAAED,IAAI,EAAE;EAAY,CAAC,EAChElB,YAAY,CAACmC,UAAU,IAAI;IAAEhB,IAAI,EAAE,UAAU;IAAED,IAAI,EAAE;EAAY,CAAC,EAClElB,YAAY,CAACoC,aAAa,IAAI;IAAEjB,IAAI,EAAE,OAAO;IAAED,IAAI,EAAE;EAAS,CAAC,EAC/DL,GAAG,IAAIb,YAAY,CAAC6B,eAAe,IAAI,CAAC,CAAC,IAAI;IAAEV,IAAI,EAAE,aAAa;IAAED,IAAI,EAAE;EAAe,CAAC,CAC3F,CAACI,MAAM,CAACU,OAAO,CAAC;EAEjB,oBACEvE,OAAA,CAACK,YAAY;IAAAuE,QAAA,gBACX5E,OAAA,CAACQ,MAAM;MAAAoE,QAAA,gBACL5E,OAAA,CAACU,YAAY;QAACE,UAAU,EAAE,CAAC,EAAE2B,YAAY,CAACsC,eAAe,IAAItC,YAAY,CAACsC,eAAe,GAAG,CAAC,CAAE;QAAAD,QAAA,gBAC7F5E,OAAA;UAAK8E,SAAS,EAAC,aAAa;UAAAF,QAAA,EAAC;QAAK;UAAAG,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAK,CAAC,eACxClF,OAAA;UAAK8E,SAAS,EAAC,aAAa;UAAAF,QAAA,EAAE,EAAAtC,qBAAA,GAAAC,YAAY,CAACQ,UAAU,cAAAT,qBAAA,uBAAvBA,qBAAA,CAAyB6C,cAAc,CAAC,CAAC,KAAI;QAAC;UAAAJ,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACvE,CAAC,EAEd3C,YAAY,CAACsC,eAAe,IAAItC,YAAY,CAACsC,eAAe,GAAG,CAAC,iBAC/D7E,OAAA,CAACc,YAAY;QAACC,WAAW,EAAEwB,YAAY,CAACsC,eAAgB;QAAAD,QAAA,GAAC,SAChD,EAACrC,YAAY,CAACsC,eAAe,CAACO,OAAO,CAAC,CAAC,CAAC;MAAA;QAAAL,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACnC,CACf;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACK,CAAC,eAETlF,OAAA,CAACiB,UAAU;MAAA2D,QAAA,EACRZ,cAAc,CAACqB,GAAG,CAAC,CAACC,OAAY,EAAEC,KAAK,kBACtCvF,OAAA,CAACmB,WAAW;QAEVC,KAAK,EAAEkE,OAAO,CAAC5B,IAAK;QACpBrC,SAAS,EAAEiE,OAAO,CAACpB,QAAS;QAAAU,QAAA,EAE3BU,OAAO,CAACnB;MAAI,GAJRmB,OAAO,CAAC5B,IAAI;QAAAqB,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAKN,CACd;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACQ,CAAC,eAEblF,OAAA,CAACuB,SAAS;MAAAqD,QAAA,gBACR5E,OAAA,CAACyB,YAAY;QAAAmD,QAAA,gBACX5E,OAAA;UAAM8E,SAAS,EAAC,WAAW;UAAAF,QAAA,EAAC;QAAE;UAAAG,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM,CAAC,EACpC3C,YAAY,CAACiD,cAAc,IAAI,CAAC;MAAA;QAAAT,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACrB,CAAC,eAEflF,OAAA,CAAC2B,aAAa;QAAAiD,QAAA,EACXJ,aAAa,CAACa,GAAG,CAAC,CAACI,MAAW,EAAEF,KAAK,kBACpCvF,OAAA,CAAC6B,YAAY;UAAmBT,KAAK,EAAEqE,MAAM,CAAC/B,IAAK;UAAAkB,QAAA,EAChDa,MAAM,CAAChC;QAAI,GADKgC,MAAM,CAAC/B,IAAI;UAAAqB,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAEhB,CACf;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACW,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACP,CAAC,EAGXvC,aAAa,CAAC0C,GAAG,CAAC5B,IAAI,iBACrBzD,OAAA,CAAC+B,YAAY;MAEXC,EAAE,EAAEyB,IAAI,CAACJ,CAAE;MACXpB,EAAE,EAAEwB,IAAI,CAACD,CAAE;MACXpC,KAAK,EAAEqC,IAAI,CAACC,IAAK;MAAAkB,QAAA,EAEhBnB,IAAI,CAACA;IAAI,GALLA,IAAI,CAACP,EAAE;MAAA6B,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAMA,CACf,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACU,CAAC;AAEnB,CAAC;AAAC7C,EAAA,CA3HIF,SAAmC;EAAA,QACHxC,WAAW;AAAA;AAAA+F,IAAA,GAD3CvD,SAAmC;AA6HzC,eAAeA,SAAS;AAAC,IAAA5B,EAAA,EAAAE,GAAA,EAAAI,GAAA,EAAAG,GAAA,EAAAE,GAAA,EAAAI,GAAA,EAAAE,GAAA,EAAAE,GAAA,EAAAE,GAAA,EAAAE,GAAA,EAAAI,GAAA,EAAAwD,IAAA;AAAAC,YAAA,CAAApF,EAAA;AAAAoF,YAAA,CAAAlF,GAAA;AAAAkF,YAAA,CAAA9E,GAAA;AAAA8E,YAAA,CAAA3E,GAAA;AAAA2E,YAAA,CAAAzE,GAAA;AAAAyE,YAAA,CAAArE,GAAA;AAAAqE,YAAA,CAAAnE,GAAA;AAAAmE,YAAA,CAAAjE,GAAA;AAAAiE,YAAA,CAAA/D,GAAA;AAAA+D,YAAA,CAAA7D,GAAA;AAAA6D,YAAA,CAAAzD,GAAA;AAAAyD,YAAA,CAAAD,IAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}