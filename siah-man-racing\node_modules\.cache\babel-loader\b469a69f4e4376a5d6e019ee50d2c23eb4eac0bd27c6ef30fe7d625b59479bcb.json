{"ast": null, "code": "function e(e, t, r, n) {\n  return new (r || (r = Promise))(function (o, a) {\n    function i(e) {\n      try {\n        d(n.next(e));\n      } catch (e) {\n        a(e);\n      }\n    }\n    function c(e) {\n      try {\n        d(n.throw(e));\n      } catch (e) {\n        a(e);\n      }\n    }\n    function d(e) {\n      var t;\n      e.done ? o(e.value) : (t = e.value, t instanceof r ? t : new r(function (e) {\n        e(t);\n      })).then(i, c);\n    }\n    d((n = n.apply(e, t || [])).next());\n  });\n}\n\"function\" == typeof SuppressedError && SuppressedError;\nconst t = [\"geforce 320m\", \"geforce 8600\", \"geforce 8600m gt\", \"geforce 8800 gs\", \"geforce 8800 gt\", \"geforce 9400\", \"geforce 9400m g\", \"geforce 9400m\", \"geforce 9600m gt\", \"geforce 9600m\", \"geforce fx go5200\", \"geforce gt 120\", \"geforce gt 130\", \"geforce gt 330m\", \"geforce gtx 285\", \"google swiftshader\", \"intel g41\", \"intel g45\", \"intel gma 4500mhd\", \"intel gma x3100\", \"intel hd 3000\", \"intel q45\", \"legacy\", \"mali-2\", \"mali-3\", \"mali-4\", \"quadro fx 1500\", \"quadro fx 4\", \"quadro fx 5\", \"radeon hd 2400\", \"radeon hd 2600\", \"radeon hd 4670\", \"radeon hd 4850\", \"radeon hd 4870\", \"radeon hd 5670\", \"radeon hd 5750\", \"radeon hd 6290\", \"radeon hd 6300\", \"radeon hd 6310\", \"radeon hd 6320\", \"radeon hd 6490m\", \"radeon hd 6630m\", \"radeon hd 6750m\", \"radeon hd 6770m\", \"radeon hd 6970m\", \"sgx 543\", \"sgx543\"];\nfunction r(e) {\n  return e = e.toLowerCase().replace(/.*angle ?\\((.+)\\)(?: on vulkan [0-9.]+)?$/i, \"$1\").replace(/\\s(\\d{1,2}gb|direct3d.+$)|\\(r\\)| \\([^)]+\\)$/g, \"\").replace(/(?:vulkan|opengl) \\d+\\.\\d+(?:\\.\\d+)?(?: \\((.*)\\))?/, \"$1\");\n}\nconst n = \"undefined\" == typeof window,\n  o = (() => {\n    if (n) return;\n    const {\n        userAgent: e,\n        platform: t,\n        maxTouchPoints: r\n      } = window.navigator,\n      o = /(iphone|ipod|ipad)/i.test(e),\n      a = \"iPad\" === t || \"MacIntel\" === t && r > 0 && !window.MSStream;\n    return {\n      isIpad: a,\n      isMobile: /android/i.test(e) || o || a,\n      isSafari12: /Version\\/12.+Safari/.test(e),\n      isFirefox: /Firefox/.test(e)\n    };\n  })();\nfunction a(e, t, r) {\n  if (!r) return [t];\n  const n = function (e) {\n      const t = \"\\n    precision highp float;\\n    attribute vec3 aPosition;\\n    varying float vvv;\\n    void main() {\\n      vvv = 0.31622776601683794;\\n      gl_Position = vec4(aPosition, 1.0);\\n    }\\n  \",\n        r = \"\\n    precision highp float;\\n    varying float vvv;\\n    void main() {\\n      vec4 enc = vec4(1.0, 255.0, 65025.0, 16581375.0) * vvv;\\n      enc = fract(enc);\\n      enc -= enc.yzww * vec4(1.0 / 255.0, 1.0 / 255.0, 1.0 / 255.0, 0.0);\\n      gl_FragColor = enc;\\n    }\\n  \",\n        n = e.createShader(35633),\n        o = e.createShader(35632),\n        a = e.createProgram();\n      if (!(o && n && a)) return;\n      e.shaderSource(n, t), e.shaderSource(o, r), e.compileShader(n), e.compileShader(o), e.attachShader(a, n), e.attachShader(a, o), e.linkProgram(a), e.detachShader(a, n), e.detachShader(a, o), e.deleteShader(n), e.deleteShader(o), e.useProgram(a);\n      const i = e.createBuffer();\n      e.bindBuffer(34962, i), e.bufferData(34962, new Float32Array([-1, -1, 0, 3, -1, 0, -1, 3, 0]), 35044);\n      const c = e.getAttribLocation(a, \"aPosition\");\n      e.vertexAttribPointer(c, 3, 5126, !1, 0, 0), e.enableVertexAttribArray(c), e.clearColor(1, 1, 1, 1), e.clear(16384), e.viewport(0, 0, 1, 1), e.drawArrays(4, 0, 3);\n      const d = new Uint8Array(4);\n      return e.readPixels(0, 0, 1, 1, 6408, 5121, d), e.deleteProgram(a), e.deleteBuffer(i), d.join(\"\");\n    }(e),\n    a = \"801621810\",\n    i = \"8016218135\",\n    c = \"80162181161\",\n    d = (null == o ? void 0 : o.isIpad) ? [[\"a7\", c, 12], [\"a8\", i, 15], [\"a8x\", i, 15], [\"a9\", i, 15], [\"a9x\", i, 15], [\"a10\", i, 15], [\"a10x\", i, 15], [\"a12\", a, 15], [\"a12x\", a, 15], [\"a12z\", a, 15], [\"a14\", a, 15], [\"a15\", a, 15], [\"m1\", a, 15], [\"m2\", a, 15]] : [[\"a7\", c, 12], [\"a8\", i, 12], [\"a9\", i, 15], [\"a10\", i, 15], [\"a11\", a, 15], [\"a12\", a, 15], [\"a13\", a, 15], [\"a14\", a, 15], [\"a15\", a, 15], [\"a16\", a, 15], [\"a17\", a, 15]];\n  let l;\n  \"80162181255\" === n ? l = d.filter(([,, e]) => e >= 14) : (l = d.filter(([, e]) => e === n), l.length || (l = d));\n  return l.map(([e]) => `apple ${e} gpu`);\n}\nclass i extends Error {\n  constructor(e) {\n    super(e), Object.setPrototypeOf(this, new.target.prototype);\n  }\n}\nconst c = [],\n  d = [];\nfunction l(e, t) {\n  if (e === t) return 0;\n  const r = e;\n  e.length > t.length && (e = t, t = r);\n  let n = e.length,\n    o = t.length;\n  for (; n > 0 && e.charCodeAt(~-n) === t.charCodeAt(~-o);) n--, o--;\n  let a,\n    i = 0;\n  for (; i < n && e.charCodeAt(i) === t.charCodeAt(i);) i++;\n  if (n -= i, o -= i, 0 === n) return o;\n  let l,\n    s,\n    f = 0,\n    u = 0,\n    g = 0;\n  for (; u < n;) d[u] = e.charCodeAt(i + u), c[u] = ++u;\n  for (; g < o;) for (a = t.charCodeAt(i + g), l = g++, f = g, u = 0; u < n; u++) s = a === d[u] ? l : l + 1, l = c[u], f = c[u] = l > f ? s > f ? f + 1 : s : s > l ? l + 1 : s;\n  return f;\n}\nfunction s(e) {\n  return null != e;\n}\nconst f = ({\n  mobileTiers: c = [0, 15, 30, 60],\n  desktopTiers: d = [0, 15, 30, 60],\n  override: f = {},\n  glContext: u,\n  failIfMajorPerformanceCaveat: g = !1,\n  benchmarksURL: h = \"https://unpkg.com/detect-gpu@5.0.70/dist/benchmarks\"\n} = {}) => e(void 0, void 0, void 0, function* () {\n  const p = {};\n  if (n) return {\n    tier: 0,\n    type: \"SSR\"\n  };\n  const {\n    isIpad: m = !!(null == o ? void 0 : o.isIpad),\n    isMobile: v = !!(null == o ? void 0 : o.isMobile),\n    screenSize: w = window.screen,\n    loadBenchmarks: x = t => e(void 0, void 0, void 0, function* () {\n      const e = yield fetch(`${h}/${t}`).then(e => e.json());\n      if (parseInt(e.shift().split(\".\")[0], 10) < 4) throw new i(\"Detect GPU benchmark data is out of date. Please update to version 4x\");\n      return e;\n    })\n  } = f;\n  let {\n    renderer: A\n  } = f;\n  const P = (e, t, r, n, o) => ({\n    device: o,\n    fps: n,\n    gpu: r,\n    isMobile: v,\n    tier: e,\n    type: t\n  });\n  let S,\n    b = \"\";\n  if (A) A = r(A), S = [A];else {\n    const e = u || function (e, t = !1) {\n      const r = {\n        alpha: !1,\n        antialias: !1,\n        depth: !1,\n        failIfMajorPerformanceCaveat: t,\n        powerPreference: \"high-performance\",\n        stencil: !1\n      };\n      e && delete r.powerPreference;\n      const n = window.document.createElement(\"canvas\"),\n        o = n.getContext(\"webgl\", r) || n.getContext(\"experimental-webgl\", r);\n      return null != o ? o : void 0;\n    }(null == o ? void 0 : o.isSafari12, g);\n    if (!e) return P(0, \"WEBGL_UNSUPPORTED\");\n    const t = (null == o ? void 0 : o.isFirefox) ? null : e.getExtension(\"WEBGL_debug_renderer_info\");\n    if (A = t ? e.getParameter(t.UNMASKED_RENDERER_WEBGL) : e.getParameter(e.RENDERER), !A) return P(1, \"FALLBACK\");\n    b = A, A = r(A), S = function (e, t, r) {\n      return \"apple gpu\" === t ? a(e, t, r) : [t];\n    }(e, A, v);\n  }\n  const E = (yield Promise.all(S.map(function (t) {\n    var r;\n    return e(this, void 0, void 0, function* () {\n      const e = (e => {\n        const t = v ? [\"adreno\", \"apple\", \"mali-t\", \"mali\", \"nvidia\", \"powervr\", \"samsung\"] : [\"intel\", \"apple\", \"amd\", \"radeon\", \"nvidia\", \"geforce\", \"adreno\"];\n        for (const r of t) if (e.includes(r)) return r;\n      })(t);\n      if (!e) return;\n      const n = `${v ? \"m\" : \"d\"}-${e}${m ? \"-ipad\" : \"\"}.json`,\n        o = p[n] = null !== (r = p[n]) && void 0 !== r ? r : x(n);\n      let a;\n      try {\n        a = yield o;\n      } catch (e) {\n        if (e instanceof i) throw e;\n        return;\n      }\n      const c = function (e) {\n        var t;\n        const r = (e = e.replace(/\\([^)]+\\)/, \"\")).match(/\\d+/) || e.match(/(\\W|^)([A-Za-z]{1,3})(\\W|$)/g);\n        return null !== (t = null == r ? void 0 : r.join(\"\").replace(/\\W|amd/g, \"\")) && void 0 !== t ? t : \"\";\n      }(t);\n      let d = a.filter(([, e]) => e === c);\n      d.length || (d = a.filter(([e]) => e.includes(t)));\n      const s = d.length;\n      if (0 === s) return;\n      const f = t.split(/[.,()\\[\\]/\\s]/g).sort().filter((e, t, r) => 0 === t || e !== r[t - 1]).join(\" \");\n      let u,\n        [g,,,, h] = s > 1 ? d.map(e => [e, l(f, e[2])]).sort(([, e], [, t]) => e - t)[0][0] : d[0],\n        A = Number.MAX_VALUE;\n      const {\n          devicePixelRatio: P\n        } = window,\n        S = w.width * P * w.height * P;\n      for (const e of h) {\n        const [t, r] = e,\n          n = t * r,\n          o = Math.abs(S - n);\n        o < A && (A = o, u = e);\n      }\n      if (!u) return;\n      const [,, b, E] = u;\n      return [A, b, g, E];\n    });\n  }))).filter(s).sort(([e = Number.MAX_VALUE, t], [r = Number.MAX_VALUE, n]) => e === r ? t - n : e - r);\n  if (!E.length) {\n    const e = t.find(e => A.includes(e));\n    return e ? P(0, \"BLOCKLISTED\", e) : P(1, \"FALLBACK\", `${A} (${b})`);\n  }\n  const [, y, C, L] = E[0];\n  if (-1 === y) return P(0, \"BLOCKLISTED\", C, y, L);\n  const M = v ? c : d;\n  let $ = 0;\n  for (let e = 0; e < M.length; e++) y >= M[e] && ($ = e);\n  return P($, \"BENCHMARK\", C, y, L);\n});\nexport { f as getGPUTier };", "map": {"version": 3, "names": ["t", "r", "e", "toLowerCase", "replace", "n", "window", "o", "userAgent", "platform", "maxTouchPoints", "navigator", "test", "a", "MSStream", "isIpad", "isMobile", "isSafari12", "isFirefox", "createShader", "createProgram", "shaderSource", "compileShader", "<PERSON><PERSON><PERSON><PERSON>", "linkProgram", "deta<PERSON><PERSON><PERSON><PERSON>", "deleteShader", "useProgram", "i", "createBuffer", "<PERSON><PERSON><PERSON><PERSON>", "bufferData", "Float32Array", "c", "getAttribLocation", "vertexAttribPointer", "enableVertexAttribArray", "clearColor", "clear", "viewport", "drawArrays", "d", "Uint8Array", "readPixels", "deleteProgram", "deleteBuffer", "join", "l", "filter", "length", "map", "Error", "constructor", "Object", "setPrototypeOf", "new", "target", "prototype", "charCodeAt", "s", "f", "u", "g", "getGPUTier", "mobileTiers", "desktopTiers", "override", "glContext", "failIfMajorPerformanceCaveat", "benchmarksURL", "h", "p", "tier", "type", "m", "v", "screenSize", "w", "screen", "loadBenchmarks", "x", "fetch", "then", "json", "parseInt", "shift", "split", "renderer", "A", "P", "toResult", "device", "fps", "gpu", "S", "b", "alpha", "antialias", "depth", "powerPreference", "stencil", "document", "createElement", "getContext", "getExtension", "getParameter", "UNMASKED_RENDERER_WEBGL", "RENDERER", "E", "Promise", "all", "includes", "match", "sort", "Number", "MAX_VALUE", "devicePixelRatio", "width", "height", "Math", "abs", "find", "y", "C", "L", "M", "$"], "sources": ["C:\\Users\\<USER>\\Documents\\Apps_Creations\\SiahMan_Racing4Speed_WebGame\\siah-man-racing\\node_modules\\detect-gpu\\src\\internal\\blocklistedGPUS.ts", "C:\\Users\\<USER>\\Documents\\Apps_Creations\\SiahMan_Racing4Speed_WebGame\\siah-man-racing\\node_modules\\detect-gpu\\src\\internal\\cleanRenderer.ts", "C:\\Users\\<USER>\\Documents\\Apps_Creations\\SiahMan_Racing4Speed_WebGame\\siah-man-racing\\node_modules\\detect-gpu\\node_modules\\webgl-constants\\dist\\webgl-constants.esm.js", "C:\\Users\\<USER>\\Documents\\Apps_Creations\\SiahMan_Racing4Speed_WebGame\\siah-man-racing\\node_modules\\detect-gpu\\src\\internal\\ssr.ts", "C:\\Users\\<USER>\\Documents\\Apps_Creations\\SiahMan_Racing4Speed_WebGame\\siah-man-racing\\node_modules\\detect-gpu\\src\\internal\\deviceInfo.ts", "C:\\Users\\<USER>\\Documents\\Apps_Creations\\SiahMan_Racing4Speed_WebGame\\siah-man-racing\\node_modules\\detect-gpu\\src\\internal\\deobfuscateAppleGPU.ts", "C:\\Users\\<USER>\\Documents\\Apps_Creations\\SiahMan_Racing4Speed_WebGame\\siah-man-racing\\node_modules\\detect-gpu\\src\\internal\\error.ts", "C:\\Users\\<USER>\\Documents\\Apps_Creations\\SiahMan_Racing4Speed_WebGame\\siah-man-racing\\node_modules\\detect-gpu\\src\\internal\\getLevenshteinDistance.ts", "C:\\Users\\<USER>\\Documents\\Apps_Creations\\SiahMan_Racing4Speed_WebGame\\siah-man-racing\\node_modules\\detect-gpu\\src\\internal\\util.ts", "C:\\Users\\<USER>\\Documents\\Apps_Creations\\SiahMan_Racing4Speed_WebGame\\siah-man-racing\\node_modules\\detect-gpu\\src\\index.ts", "C:\\Users\\<USER>\\Documents\\Apps_Creations\\SiahMan_Racing4Speed_WebGame\\siah-man-racing\\node_modules\\detect-gpu\\src\\internal\\getWebGLContext.ts", "C:\\Users\\<USER>\\Documents\\Apps_Creations\\SiahMan_Racing4Speed_WebGame\\siah-man-racing\\node_modules\\detect-gpu\\src\\internal\\deobfuscateRenderer.ts", "C:\\Users\\<USER>\\Documents\\Apps_Creations\\SiahMan_Racing4Speed_WebGame\\siah-man-racing\\node_modules\\detect-gpu\\src\\internal\\getGPUVersion.ts"], "sourcesContent": ["// GPU blocklist\n// SEE: https://chromium.googlesource.com/chromium/src/+/master/gpu/config/software_rendering_list.json\n// SEE: https://hg.mozilla.org/mozilla-central/raw-file/tip/services/settings/dumps/blocklists/gfx.json\nexport const BLOCKLISTED_GPUS = [\n  'geforce 320m',\n  'geforce 8600',\n  'geforce 8600m gt',\n  'geforce 8800 gs',\n  'geforce 8800 gt',\n  'geforce 9400',\n  'geforce 9400m g',\n  'geforce 9400m',\n  'geforce 9600m gt',\n  'geforce 9600m',\n  'geforce fx go5200',\n  'geforce gt 120',\n  'geforce gt 130',\n  'geforce gt 330m',\n  'geforce gtx 285',\n  'google swiftshader',\n  'intel g41',\n  'intel g45',\n  'intel gma 4500mhd',\n  'intel gma x3100',\n  'intel hd 3000',\n  'intel q45',\n  'legacy',\n  'mali-2',\n  'mali-3',\n  'mali-4',\n  'quadro fx 1500',\n  'quadro fx 4',\n  'quadro fx 5',\n  'radeon hd 2400',\n  'radeon hd 2600',\n  'radeon hd 4670',\n  'radeon hd 4850',\n  'radeon hd 4870',\n  'radeon hd 5670',\n  'radeon hd 5750',\n  'radeon hd 6290',\n  'radeon hd 6300',\n  'radeon hd 6310',\n  'radeon hd 6320',\n  'radeon hd 6490m',\n  'radeon hd 6630m',\n  'radeon hd 6750m',\n  'radeon hd 6770m',\n  'radeon hd 6970m',\n  'sgx 543',\n  'sgx543',\n];\n", "const debug = false ? console.log : undefined;\n\nexport function cleanRenderer(renderer: string) {\n  debug?.('cleanRenderer', { renderer });\n\n  renderer = renderer\n    .toLowerCase()\n    // Strip off ANGLE() - for example:\n    // 'ANGLE (NVIDIA TITAN Xp)' becomes 'NVIDIA TITAN Xp',\n    // 'Samsung Electronics Co., Ltd. ANGLE (Samsung Xclipse 920) on Vulkan 1.1.179' becomes 'Samsung Xclipse 920':\n    .replace(/.*angle ?\\((.+)\\)(?: on vulkan [0-9.]+)?$/i, '$1')\n    // Strip off [number]gb & strip off direct3d and after - for example:\n    // 'Radeon (TM) RX 470 Series Direct3D11 vs_5_0 ps_5_0' becomes\n    // 'Radeon (TM) RX 470 Series'\n    .replace(/\\s(\\d{1,2}gb|direct3d.+$)|\\(r\\)| \\([^)]+\\)$/g, '')\n    // Strip out graphics API. The one Vulkan example we've seen includes\n    // the GPU in parens after the Vulkan version so this also keeps that\n    // eg. 'vulkan 1.2.175 (nvidia nvidia geforce gtx 970 (0x000013c2))'\n    // becomes 'nvidia nvidia geforce gtx 970 (0x000013c2)'\n    // `OpenGL 4.5.0` gets removed all together\n    .replace(/(?:vulkan|opengl) \\d+\\.\\d+(?:\\.\\d+)?(?: \\((.*)\\))?/, '$1')\n\n  debug?.('cleanRenderer - renderer cleaned to', { renderer });\n\n  return renderer;\n};\n", "/**\r\n * The following defined constants and descriptions are directly ported from https://developer.mozilla.org/en-US/docs/Web/API/WebGL_API/Constants\r\n *\r\n * Any copyright is dedicated to the Public Domain. http://creativecommons.org/publicdomain/zero/1.0/\r\n *\r\n * Contributors\r\n *\r\n * See: https://developer.mozilla.org/en-US/profiles/Sheppy\r\n * See: https://developer.mozilla.org/en-US/profiles/fscholz\r\n * See: https://developer.mozilla.org/en-US/profiles/AtiX\r\n * See: https://developer.mozilla.org/en-US/profiles/Sebastianz\r\n *\r\n * These constants are defined on the WebGLRenderingContext / WebGL2RenderingContext interface\r\n */\r\n// Clearing buffers\r\n// Constants passed to WebGLRenderingContext.clear() to clear buffer masks\r\n/**\r\n * Passed to clear to clear the current depth buffer\r\n * @constant {number}\r\n */\r\nconst GL_DEPTH_BUFFER_BIT = 0x00000100;\r\n/**\r\n * Passed to clear to clear the current stencil buffer\r\n * @constant {number}\r\n */\r\nconst GL_STENCIL_BUFFER_BIT = 0x00000400;\r\n/**\r\n * Passed to clear to clear the current color buffer\r\n * @constant {number}\r\n */\r\nconst GL_COLOR_BUFFER_BIT = 0x00004000;\r\n// Rendering primitives\r\n// Constants passed to WebGLRenderingContext.drawElements() or WebGLRenderingContext.drawArrays() to specify what kind of primitive to render\r\n/**\r\n * Passed to drawElements or drawArrays to draw single points\r\n * @constant {number}\r\n */\r\nconst GL_POINTS = 0x0000;\r\n/**\r\n * Passed to drawElements or drawArrays to draw lines. Each vertex connects to the one after it\r\n * @constant {number}\r\n */\r\nconst GL_LINES = 0x0001;\r\n/**\r\n * Passed to drawElements or drawArrays to draw lines. Each set of two vertices is treated as a separate line segment\r\n * @constant {number}\r\n */\r\nconst GL_LINE_LOOP = 0x0002;\r\n/**\r\n * Passed to drawElements or drawArrays to draw a connected group of line segments from the first vertex to the last\r\n * @constant {number}\r\n */\r\nconst GL_LINE_STRIP = 0x0003;\r\n/**\r\n * Passed to drawElements or drawArrays to draw triangles. Each set of three vertices creates a separate triangle\r\n * @constant {number}\r\n */\r\nconst GL_TRIANGLES = 0x0004;\r\n/**\r\n * Passed to drawElements or drawArrays to draw a connected group of triangles\r\n * @constant {number}\r\n */\r\nconst GL_TRIANGLE_STRIP = 0x0005;\r\n/**\r\n * Passed to drawElements or drawArrays to draw a connected group of triangles. Each vertex connects to the previous and the first vertex in the fan\r\n * @constant {number}\r\n */\r\nconst GL_TRIANGLE_FAN = 0x0006;\r\n// Blending modes\r\n// Constants passed to WebGLRenderingContext.blendFunc() or WebGLRenderingContext.blendFuncSeparate() to specify the blending mode (for both, RBG and alpha, or separately)\r\n/**\r\n * Passed to blendFunc or blendFuncSeparate to turn off a component\r\n * @constant {number}\r\n */\r\nconst GL_ZERO = 0;\r\n/**\r\n * Passed to blendFunc or blendFuncSeparate to turn on a component\r\n * @constant {number}\r\n */\r\nconst GL_ONE = 1;\r\n/**\r\n * Passed to blendFunc or blendFuncSeparate to multiply a component by the source elements color\r\n * @constant {number}\r\n */\r\nconst GL_SRC_COLOR = 0x0300;\r\n/**\r\n * Passed to blendFunc or blendFuncSeparate to multiply a component by one minus the source elements color\r\n * @constant {number}\r\n */\r\nconst GL_ONE_MINUS_SRC_COLOR = 0x0301;\r\n/**\r\n * Passed to blendFunc or blendFuncSeparate to multiply a component by the source's alpha\r\n * @constant {number}\r\n */\r\nconst GL_SRC_ALPHA = 0x0302;\r\n/**\r\n * Passed to blendFunc or blendFuncSeparate to multiply a component by one minus the source's alpha\r\n * @constant {number}\r\n */\r\nconst GL_ONE_MINUS_SRC_ALPHA = 0x0303;\r\n/**\r\n * Passed to blendFunc or blendFuncSeparate to multiply a component by the destination's alpha\r\n * @constant {number}\r\n */\r\nconst GL_DST_ALPHA = 0x0304;\r\n/**\r\n * Passed to blendFunc or blendFuncSeparate to multiply a component by one minus the destination's alpha\r\n * @constant {number}\r\n */\r\nconst GL_ONE_MINUS_DST_ALPHA = 0x0305;\r\n/**\r\n * Passed to blendFunc or blendFuncSeparate to multiply a component by the destination's color\r\n * @constant {number}\r\n */\r\nconst GL_DST_COLOR = 0x0306;\r\n/**\r\n * Passed to blendFunc or blendFuncSeparate to multiply a component by one minus the destination's color\r\n * @constant {number}\r\n */\r\nconst GL_ONE_MINUS_DST_COLOR = 0x0307;\r\n/**\r\n * Passed to blendFunc or blendFuncSeparate to multiply a component by the minimum of source's alpha or one minus the destination's alpha\r\n * @constant {number}\r\n */\r\nconst GL_SRC_ALPHA_SATURATE = 0x0308;\r\n/**\r\n * Passed to blendFunc or blendFuncSeparate to specify a constant color blend function\r\n * @constant {number}\r\n */\r\nconst GL_CONSTANT_COLOR = 0x8001;\r\n/**\r\n * Passed to blendFunc or blendFuncSeparate to specify one minus a constant color blend function\r\n * @constant {number}\r\n */\r\nconst GL_ONE_MINUS_CONSTANT_COLOR = 0x8002;\r\n/**\r\n * Passed to blendFunc or blendFuncSeparate to specify a constant alpha blend function\r\n * @constant {number}\r\n */\r\nconst GL_CONSTANT_ALPHA = 0x8003;\r\n/**\r\n * Passed to blendFunc or blendFuncSeparate to specify one minus a constant alpha blend function\r\n * @constant {number}\r\n */\r\nconst GL_ONE_MINUS_CONSTANT_ALPHA = 0x8004;\r\n// Blending equations\r\n// Constants passed to WebGLRenderingContext.blendEquation() or WebGLRenderingContext.blendEquationSeparate() to control how the blending is calculated (for both, RBG and alpha, or separately)\r\n/**\r\n * Passed to blendEquation or blendEquationSeparate to set an addition blend function\r\n * @constant {number}\r\n */\r\nconst GL_FUNC_ADD = 0x8006;\r\n/**\r\n * Passed to blendEquation or blendEquationSeparate to specify a subtraction blend function (source - destination)\r\n * @constant {number}\r\n */\r\nconst GL_FUNC_SUBSTRACT = 0x800a;\r\n/**\r\n * Passed to blendEquation or blendEquationSeparate to specify a reverse subtraction blend function (destination - source)\r\n * @constant {number}\r\n */\r\nconst GL_FUNC_REVERSE_SUBTRACT = 0x800b;\r\n// Getting GL parameter information\r\n// Constants passed to WebGLRenderingContext.getParameter() to specify what information to return\r\n/**\r\n * Passed to getParameter to get the current RGB blend function\r\n * @constant {number}\r\n */\r\nconst GL_BLEND_EQUATION = 0x8009;\r\n/**\r\n * Passed to getParameter to get the current RGB blend function. Same as BLEND_EQUATION\r\n * @constant {number}\r\n */\r\nconst GL_BLEND_EQUATION_RGB = 0x8009;\r\n/**\r\n * Passed to getParameter to get the current alpha blend function. Same as BLEND_EQUATION\r\n * @constant {number}\r\n */\r\nconst GL_BLEND_EQUATION_ALPHA = 0x883d;\r\n/**\r\n * Passed to getParameter to get the current destination RGB blend function\r\n * @constant {number}\r\n */\r\nconst GL_BLEND_DST_RGB = 0x80c8;\r\n/**\r\n * Passed to getParameter to get the current source RGB blend function\r\n * @constant {number}\r\n */\r\nconst GL_BLEND_SRC_RGB = 0x80c9;\r\n/**\r\n * Passed to getParameter to get the current destination alpha blend function\r\n * @constant {number}\r\n */\r\nconst GL_BLEND_DST_ALPHA = 0x80ca;\r\n/**\r\n * Passed to getParameter to get the current source alpha blend function\r\n * @constant {number}\r\n */\r\nconst GL_BLEND_SRC_ALPHA = 0x80cb;\r\n/**\r\n * Passed to getParameter to return a the current blend color\r\n * @constant {number}\r\n */\r\nconst GL_BLEND_COLOR = 0x8005;\r\n/**\r\n * Passed to getParameter to get the array buffer binding\r\n * @constant {number}\r\n */\r\nconst GL_ARRAY_BUFFER_BINDING = 0x8894;\r\n/**\r\n * Passed to getParameter to get the current element array buffer\r\n * @constant {number}\r\n */\r\nconst GL_ELEMENT_ARRAY_BUFFER_BINDING = 0x8895;\r\n/**\r\n * Passed to getParameter to get the current lineWidth (set by the lineWidth method)\r\n * @constant {number}\r\n */\r\nconst GL_LINE_WIDTH = 0x0b21;\r\n/**\r\n * Passed to getParameter to get the current size of a point drawn with gl.POINTS\r\n * @constant {number}\r\n */\r\nconst GL_ALIASED_POINT_SIZE_RANGE = 0x846d;\r\n/**\r\n * Passed to getParameter to get the range of available widths for a line. Returns a length-2 array with the lo value at 0, and hight at 1\r\n * @constant {number}\r\n */\r\nconst GL_ALIASED_LINE_WIDTH_RANGE = 0x846e;\r\n/**\r\n * Passed to getParameter to get the current value of cullFace. Should return FRONT, BACK, or FRONT_AND_BACK\r\n * @constant {number}\r\n */\r\nconst GL_CULL_FACE_MODE = 0x0b45;\r\n/**\r\n * Passed to getParameter to determine the current value of frontFace. Should return CW or CCW\r\n * @constant {number}\r\n */\r\nconst GL_FRONT_FACE = 0x0b46;\r\n/**\r\n * Passed to getParameter to return a length-2 array of floats giving the current depth range\r\n * @constant {number}\r\n */\r\nconst GL_DEPTH_RANGE = 0x0b70;\r\n/**\r\n * Passed to getParameter to determine if the depth write mask is enabled\r\n * @constant {number}\r\n */\r\nconst GL_DEPTH_WRITEMASK = 0x0b72;\r\n/**\r\n * Passed to getParameter to determine the current depth clear value\r\n * @constant {number}\r\n */\r\nconst GL_DEPTH_CLEAR_VALUE = 0x0b73;\r\n/**\r\n * Passed to getParameter to get the current depth function. Returns NEVER, ALWAYS, LESS, EQUAL, LEQUAL, GREATER, GEQUAL, or NOTEQUAL\r\n * @constant {number}\r\n */\r\nconst GL_DEPTH_FUNC = 0x0b74;\r\n/**\r\n * Passed to getParameter to get the value the stencil will be cleared to\r\n * @constant {number}\r\n */\r\nconst GL_STENCIL_CLEAR_VALUE = 0x0b91;\r\n/**\r\n * Passed to getParameter to get the current stencil function. Returns NEVER, ALWAYS, LESS, EQUAL, LEQUAL, GREATER, GEQUAL, or NOTEQUAL\r\n * @constant {number}\r\n */\r\nconst GL_STENCIL_FUNC = 0x0b92;\r\n/**\r\n * Passed to getParameter to get the current stencil fail function. Should return KEEP, REPLACE, INCR, DECR, INVERT, INCR_WRAP, or DECR_WRAP\r\n * @constant {number}\r\n */\r\nconst GL_STENCIL_FAIL = 0x0b94;\r\n/**\r\n * Passed to getParameter to get the current stencil fail function should the depth buffer test fail. Should return KEEP, REPLACE, INCR, DECR, INVERT, INCR_WRAP, or DECR_WRAP\r\n * @constant {number}\r\n */\r\nconst GL_STENCIL_PASS_DEPTH_FAIL = 0x0b95;\r\n/**\r\n * Passed to getParameter to get the current stencil fail function should the depth buffer test pass. Should return KEEP, REPLACE, INCR, DECR, INVERT, INCR_WRAP, or DECR_WRAP\r\n * @constant {number}\r\n */\r\nconst GL_STENCIL_PASS_DEPTH_PASS = 0x0b96;\r\n/**\r\n * Passed to getParameter to get the reference value used for stencil tests\r\n * @constant {number}\r\n */\r\nconst GL_STENCIL_REF = 0x0b97;\r\n/**\r\n * @constant {number}\r\n */\r\nconst GL_STENCIL_VALUE_MASK = 0x0b93;\r\n/**\r\n * @constant {number}\r\n */\r\nconst GL_STENCIL_WRITEMASK = 0x0b98;\r\n/**\r\n * @constant {number}\r\n */\r\nconst GL_STENCIL_BACK_FUNC = 0x8800;\r\n/**\r\n * @constant {number}\r\n */\r\nconst GL_STENCIL_BACK_FAIL = 0x8801;\r\n/**\r\n * @constant {number}\r\n */\r\nconst GL_STENCIL_BACK_PASS_DEPTH_FAIL = 0x8802;\r\n/**\r\n * @constant {number}\r\n */\r\nconst GL_STENCIL_BACK_PASS_DEPTH_PASS = 0x8803;\r\n/**\r\n * @constant {number}\r\n */\r\nconst GL_STENCIL_BACK_REF = 0x8ca3;\r\n/**\r\n * @constant {number}\r\n */\r\nconst GL_STENCIL_BACK_VALUE_MASK = 0x8ca4;\r\n/**\r\n * @constant {number}\r\n */\r\nconst GL_STENCIL_BACK_WRITEMASK = 0x8ca5;\r\n/**\r\n * Returns an Int32Array with four elements for the current viewport dimensions\r\n * @constant {number}\r\n */\r\nconst GL_VIEWPORT = 0x0ba2;\r\n/**\r\n * Returns an Int32Array with four elements for the current scissor box dimensions\r\n * @constant {number}\r\n */\r\nconst GL_SCISSOR_BOX = 0x0c10;\r\n/**\r\n * @constant {number}\r\n */\r\nconst GL_COLOR_CLEAR_VALUE = 0x0c22;\r\n/**\r\n * @constant {number}\r\n */\r\nconst GL_COLOR_WRITEMASK = 0x0c23;\r\n/**\r\n * @constant {number}\r\n */\r\nconst GL_UNPACK_ALIGNMENT = 0x0cf5;\r\n/**\r\n * @constant {number}\r\n */\r\nconst GL_PACK_ALIGNMENT = 0x0d05;\r\n/**\r\n * @constant {number}\r\n */\r\nconst GL_MAX_TEXTURE_SIZE = 0x0d33;\r\n/**\r\n * @constant {number}\r\n */\r\nconst GL_MAX_VIEWPORT_DIMS = 0x0d3a;\r\n/**\r\n * @constant {number}\r\n */\r\nconst GL_SUBPIXEL_BITS = 0x0d50;\r\n/**\r\n * @constant {number}\r\n */\r\nconst GL_RED_BITS = 0x0d52;\r\n/**\r\n * @constant {number}\r\n */\r\nconst GL_GREEN_BITS = 0x0d53;\r\n/**\r\n * @constant {number}\r\n */\r\nconst GL_BLUE_BITS = 0x0d54;\r\n/**\r\n * @constant {number}\r\n */\r\nconst GL_ALPHA_BITS = 0x0d55;\r\n/**\r\n * @constant {number}\r\n */\r\nconst GL_DEPTH_BITS = 0x0d56;\r\n/**\r\n * @constant {number}\r\n */\r\nconst GL_STENCIL_BITS = 0x0d57;\r\n/**\r\n * @constant {number}\r\n */\r\nconst GL_POLYGON_OFFSET_UNITS = 0x2a00;\r\n/**\r\n * @constant {number}\r\n */\r\nconst GL_POLYGON_OFFSET_FACTOR = 0x8038;\r\n/**\r\n * @constant {number}\r\n */\r\nconst GL_TEXTURE_BINDING_2D = 0x8069;\r\n/**\r\n * @constant {number}\r\n */\r\nconst GL_SAMPLE_BUFFERS = 0x80a8;\r\n/**\r\n * @constant {number}\r\n */\r\nconst GL_SAMPLES = 0x80a9;\r\n/**\r\n * @constant {number}\r\n */\r\nconst GL_SAMPLE_COVERAGE_VALUE = 0x80aa;\r\n/**\r\n * @constant {number}\r\n */\r\nconst GL_SAMPLE_COVERAGE_INVERT = 0x80ab;\r\n/**\r\n * @constant {number}\r\n */\r\nconst GL_COMPRESSED_TEXTURE_FORMATS = 0x86a3;\r\n/**\r\n * @constant {number}\r\n */\r\nconst GL_VENDOR = 0x1f00;\r\n/**\r\n * @constant {number}\r\n */\r\nconst GL_RENDERER = 0x1f01;\r\n/**\r\n * @constant {number}\r\n */\r\nconst GL_VERSION = 0x1f02;\r\n/**\r\n * @constant {number}\r\n */\r\nconst GL_IMPLEMENTATION_COLOR_READ_TYPE = 0x8b9a;\r\n/**\r\n * @constant {number}\r\n */\r\nconst GL_IMPLEMENTATION_COLOR_READ_FORMAT = 0x8b9b;\r\n/**\r\n * @constant {number}\r\n */\r\nconst GL_BROWSER_DEFAULT_WEBGL = 0x9244;\r\n// Buffers\r\n// Constants passed to WebGLRenderingContext.bufferData(), WebGLRenderingContext.bufferSubData(), WebGLRenderingContext.bindBuffer(), or WebGLRenderingContext.getBufferParameter()\r\n/**\r\n * Passed to bufferData as a hint about whether the contents of the buffer are likely to be used often and not change often\r\n * @constant {number}\r\n */\r\nconst GL_STATIC_DRAW = 0x88e4;\r\n/**\r\n * Passed to bufferData as a hint about whether the contents of the buffer are likely to not be used often\r\n * @constant {number}\r\n */\r\nconst GL_STREAM_DRAW = 0x88e0;\r\n/**\r\n * Passed to bufferData as a hint about whether the contents of the buffer are likely to be used often and change often\r\n * @constant {number}\r\n */\r\nconst GL_DYNAMIC_DRAW = 0x88e8;\r\n/**\r\n * Passed to bindBuffer or bufferData to specify the type of buffer being used\r\n * @constant {number}\r\n */\r\nconst GL_ARRAY_BUFFER = 0x8892;\r\n/**\r\n * Passed to bindBuffer or bufferData to specify the type of buffer being used\r\n * @constant {number}\r\n */\r\nconst GL_ELEMENT_ARRAY_BUFFER = 0x8893;\r\n/**\r\n * Passed to getBufferParameter to get a buffer's size\r\n * @constant {number}\r\n */\r\nconst GL_BUFFER_SIZE = 0x8764;\r\n/**\r\n * Passed to getBufferParameter to get the hint for the buffer passed in when it was created\r\n * @constant {number}\r\n */\r\nconst GL_BUFFER_USAGE = 0x8765;\r\n// Vertex attributes\r\n// Constants passed to WebGLRenderingContext.getVertexAttrib()\r\n/**\r\n * Passed to getVertexAttrib to read back the current vertex attribute\r\n * @constant {number}\r\n */\r\nconst GL_CURRENT_VERTEX_ATTRIB = 0x8626;\r\n/**\r\n * @constant {number}\r\n */\r\nconst GL_VERTEX_ATTRIB_ARRAY_ENABLED = 0x8622;\r\n/**\r\n * @constant {number}\r\n */\r\nconst GL_VERTEX_ATTRIB_ARRAY_SIZE = 0x8623;\r\n/**\r\n * @constant {number}\r\n */\r\nconst GL_VERTEX_ATTRIB_ARRAY_STRIDE = 0x8624;\r\n/**\r\n * @constant {number}\r\n */\r\nconst GL_VERTEX_ATTRIB_ARRAY_TYPE = 0x8625;\r\n/**\r\n * @constant {number}\r\n */\r\nconst GL_VERTEX_ATTRIB_ARRAY_NORMALIZED = 0x886a;\r\n/**\r\n * @constant {number}\r\n */\r\nconst GL_VERTEX_ATTRIB_ARRAY_POINTER = 0x8645;\r\n/**\r\n * @constant {number}\r\n */\r\nconst GL_VERTEX_ATTRIB_ARRAY_BUFFER_BINDING = 0x889f;\r\n// Culling\r\n// Constants passed to WebGLRenderingContext.cullFace()\r\n/**\r\n * Passed to enable/disable to turn on/off culling. Can also be used with getParameter to find the current culling method\r\n * @constant {number}\r\n */\r\nconst GL_CULL_FACE = 0x0b44;\r\n/**\r\n * Passed to cullFace to specify that only front faces should be culled\r\n * @constant {number}\r\n */\r\nconst GL_FRONT = 0x0404;\r\n/**\r\n * Passed to cullFace to specify that only back faces should be culled\r\n * @constant {number}\r\n */\r\nconst GL_BACK = 0x0405;\r\n/**\r\n * Passed to cullFace to specify that front and back faces should be culled\r\n * @constant {number}\r\n */\r\nconst GL_FRONT_AND_BACK = 0x0408;\r\n// Enabling and disabling\r\n// Constants passed to WebGLRenderingContext.enable() or WebGLRenderingContext.disable()\r\n/**\r\n * Passed to enable/disable to turn on/off blending. Can also be used with getParameter to find the current blending method\r\n * @constant {number}\r\n */\r\nconst GL_BLEND = 0x0be2;\r\n/**\r\n * Passed to enable/disable to turn on/off the depth test. Can also be used with getParameter to query the depth test\r\n * @constant {number}\r\n */\r\nconst GL_DEPTH_TEST = 0x0b71;\r\n/**\r\n * Passed to enable/disable to turn on/off dithering. Can also be used with getParameter to find the current dithering method\r\n * @constant {number}\r\n */\r\nconst GL_DITHER = 0x0bd0;\r\n/**\r\n * Passed to enable/disable to turn on/off the polygon offset. Useful for rendering hidden-line images, decals, and or solids with highlighted edges. Can also be used with getParameter to query the scissor test\r\n * @constant {number}\r\n */\r\nconst GL_POLYGON_OFFSET_FILL = 0x8037;\r\n/**\r\n * Passed to enable/disable to turn on/off the alpha to coverage. Used in multi-sampling alpha channels\r\n * @constant {number}\r\n */\r\nconst GL_SAMPLE_ALPHA_TO_COVERAGE = 0x809e;\r\n/**\r\n * Passed to enable/disable to turn on/off the sample coverage. Used in multi-sampling\r\n * @constant {number}\r\n */\r\nconst GL_SAMPLE_COVERAGE = 0x80a0;\r\n/**\r\n * Passed to enable/disable to turn on/off the scissor test. Can also be used with getParameter to query the scissor test\r\n * @constant {number}\r\n */\r\nconst GL_SCISSOR_TEST = 0x0c11;\r\n/**\r\n * Passed to enable/disable to turn on/off the stencil test. Can also be used with getParameter to query the stencil test\r\n * @constant {number}\r\n */\r\nconst GL_STENCIL_TEST = 0x0b90;\r\n// Errors\r\n// Constants returned from WebGLRenderingContext.getError()\r\n/**\r\n * Returned from getError\r\n * @constant {number}\r\n */\r\nconst GL_NO_ERROR = 0;\r\n/**\r\n * Returned from getError\r\n * @constant {number}\r\n */\r\nconst GL_INVALID_ENUM = 0x0500;\r\n/**\r\n * Returned from getError\r\n * @constant {number}\r\n */\r\nconst GL_INVALID_VALUE = 0x0501;\r\n/**\r\n * Returned from getError\r\n * @constant {number}\r\n */\r\nconst GL_INVALID_OPERATION = 0x0502;\r\n/**\r\n * Returned from getError\r\n * @constant {number}\r\n */\r\nconst GL_OUT_OF_MEMORY = 0x0505;\r\n/**\r\n * Returned from getError\r\n * @constant {number}\r\n */\r\nconst GL_CONTEXT_LOST_WEBGL = 0x9242;\r\n// Front face directions\r\n// Constants passed to WebGLRenderingContext.frontFace()\r\n/**\r\n * Passed to frontFace to specify the front face of a polygon is drawn in the clockwise direction,\r\n * @constant {number}\r\n */\r\nconst GL_CW = 0x0900;\r\n/**\r\n * Passed to frontFace to specify the front face of a polygon is drawn in the counter clockwise direction\r\n * @constant {number}\r\n */\r\nconst GL_CCW = 0x0901;\r\n// Hints\r\n// Constants passed to WebGLRenderingContext.hint()\r\n/**\r\n * There is no preference for this behavior\r\n * @constant {number}\r\n */\r\nconst GL_DONT_CARE = 0x1100;\r\n/**\r\n * The most efficient behavior should be used\r\n * @constant {number}\r\n */\r\nconst GL_FASTEST = 0x1101;\r\n/**\r\n * The most correct or the highest quality option should be used\r\n * @constant {number}\r\n */\r\nconst GL_NICEST = 0x1102;\r\n/**\r\n * Hint for the quality of filtering when generating mipmap images with WebGLRenderingContext.generateMipmap()\r\n * @constant {number}\r\n */\r\nconst GL_GENERATE_MIPMAP_HINT = 0x8192;\r\n// Data types\r\n/**\r\n * @constant {number}\r\n */\r\nconst GL_BYTE = 0x1400;\r\n/**\r\n * @constant {number}\r\n */\r\nconst GL_UNSIGNED_BYTE = 0x1401;\r\n/**\r\n * @constant {number}\r\n */\r\nconst GL_SHORT = 0x1402;\r\n/**\r\n * @constant {number}\r\n */\r\nconst GL_UNSIGNED_SHORT = 0x1403;\r\n/**\r\n * @constant {number}\r\n */\r\nconst GL_INT = 0x1404;\r\n/**\r\n * @constant {number}\r\n */\r\nconst GL_UNSIGNED_INT = 0x1405;\r\n/**\r\n * @constant {number}\r\n */\r\nconst GL_FLOAT = 0x1406;\r\n// Pixel formats\r\n/**\r\n * @constant {number}\r\n */\r\nconst GL_DEPTH_COMPONENT = 0x1902;\r\n/**\r\n * @constant {number}\r\n */\r\nconst GL_ALPHA = 0x1906;\r\n/**\r\n * @constant {number}\r\n */\r\nconst GL_RGB = 0x1907;\r\n/**\r\n * @constant {number}\r\n */\r\nconst GL_RGBA = 0x1908;\r\n/**\r\n * @constant {number}\r\n */\r\nconst GL_LUMINANCE = 0x1909;\r\n/**\r\n * @constant {number}\r\n */\r\nconst GL_LUMINANCE_ALPHA = 0x190a;\r\n// Pixel types\r\n/**\r\n * @constant {number}\r\n */\r\nconst GL_UNSIGNED_SHORT_4_4_4_4 = 0x8033;\r\n/**\r\n * @constant {number}\r\n */\r\nconst GL_UNSIGNED_SHORT_5_5_5_1 = 0x8034;\r\n/**\r\n * @constant {number}\r\n */\r\nconst GL_UNSIGNED_SHORT_5_6_5 = 0x8363;\r\n// Shaders\r\n// Constants passed to WebGLRenderingContext.getShaderParameter()\r\n/**\r\n * Passed to createShader to define a fragment shader\r\n * @constant {number}\r\n */\r\nconst GL_FRAGMENT_SHADER = 0x8b30;\r\n/**\r\n * Passed to createShader to define a vertex shader\r\n * @constant {number}\r\n */\r\nconst GL_VERTEX_SHADER = 0x8b31;\r\n/**\r\n * Passed to getShaderParamter to get the status of the compilation. Returns false if the shader was not compiled. You can then query getShaderInfoLog to find the exact error\r\n * @constant {number}\r\n */\r\nconst GL_COMPILE_STATUS = 0x8b81;\r\n/**\r\n * Passed to getShaderParamter to determine if a shader was deleted via deleteShader. Returns true if it was, false otherwise\r\n * @constant {number}\r\n */\r\nconst GL_DELETE_STATUS = 0x8b80;\r\n/**\r\n * Passed to getProgramParameter after calling linkProgram to determine if a program was linked correctly. Returns false if there were errors. Use getProgramInfoLog to find the exact error\r\n * @constant {number}\r\n */\r\nconst GL_LINK_STATUS = 0x8b82;\r\n/**\r\n * Passed to getProgramParameter after calling validateProgram to determine if it is valid. Returns false if errors were found\r\n * @constant {number}\r\n */\r\nconst GL_VALIDATE_STATUS = 0x8b83;\r\n/**\r\n * Passed to getProgramParameter after calling attachShader to determine if the shader was attached correctly. Returns false if errors occurred\r\n * @constant {number}\r\n */\r\nconst GL_ATTACHED_SHADERS = 0x8b85;\r\n/**\r\n * Passed to getProgramParameter to get the number of attributes active in a program\r\n * @constant {number}\r\n */\r\nconst GL_ACTIVE_ATTRIBUTES = 0x8b89;\r\n/**\r\n * Passed to getProgramParamter to get the number of uniforms active in a program\r\n * @constant {number}\r\n */\r\nconst GL_ACTIVE_UNIFORMS = 0x8b86;\r\n/**\r\n * The maximum number of entries possible in the vertex attribute list\r\n * @constant {number}\r\n */\r\nconst GL_MAX_VERTEX_ATTRIBS = 0x8869;\r\n/**\r\n * @constant {number}\r\n */\r\nconst GL_MAX_VERTEX_UNIFORM_VECTORS = 0x8dfb;\r\n/**\r\n * @constant {number}\r\n */\r\nconst GL_MAX_VARYING_VECTORS = 0x8dfc;\r\n/**\r\n * @constant {number}\r\n */\r\nconst GL_MAX_COMBINED_TEXTURE_IMAGE_UNITS = 0x8b4d;\r\n/**\r\n * @constant {number}\r\n */\r\nconst GL_MAX_VERTEX_TEXTURE_IMAGE_UNITS = 0x8b4c;\r\n/**\r\n * Implementation dependent number of maximum texture units. At least 8\r\n * @constant {number}\r\n */\r\nconst GL_MAX_TEXTURE_IMAGE_UNITS = 0x8872;\r\n/**\r\n * @constant {number}\r\n */\r\nconst GL_MAX_FRAGMENT_UNIFORM_VECTORS = 0x8dfd;\r\n/**\r\n * @constant {number}\r\n */\r\nconst GL_SHADER_TYPE = 0x8b4f;\r\n/**\r\n * @constant {number}\r\n */\r\nconst GL_SHADING_LANGUAGE_VERSION = 0x8b8c;\r\n/**\r\n * @constant {number}\r\n */\r\nconst GL_CURRENT_PROGRAM = 0x8b8d;\r\n// Depth or stencil tests\r\n// Constants passed to WebGLRenderingContext.stencilFunc()\r\n/**\r\n * Passed to depthFunction or stencilFunction to specify depth or stencil tests will never pass. i.e. Nothing will be drawn\r\n * @constant {number}\r\n */\r\nconst GL_NEVER = 0x0200;\r\n/**\r\n * Passed to depthFunction or stencilFunction to specify depth or stencil tests will always pass. i.e. Pixels will be drawn in the order they are drawn\r\n * @constant {number}\r\n */\r\nconst GL_ALWAYS = 0x0207;\r\n/**\r\n * Passed to depthFunction or stencilFunction to specify depth or stencil tests will pass if the new depth value is less than the stored value\r\n * @constant {number}\r\n */\r\nconst GL_LESS = 0x0201;\r\n/**\r\n * Passed to depthFunction or stencilFunction to specify depth or stencil tests will pass if the new depth value is equals to the stored value\r\n * @constant {number}\r\n */\r\nconst GL_EQUAL = 0x0202;\r\n/**\r\n *  Passed to depthFunction or stencilFunction to specify depth or stencil tests will pass if the new depth value is less than or equal to the stored value\r\n * @constant {number}\r\n */\r\nconst GL_LEQUAL = 0x0203;\r\n/**\r\n * Passed to depthFunction or stencilFunction to specify depth or stencil tests will pass if the new depth value is greater than the stored value\r\n * @constant {number}\r\n */\r\nconst GL_GREATER = 0x0204;\r\n/**\r\n * Passed to depthFunction or stencilFunction to specify depth or stencil tests will pass if the new depth value is greater than or equal to the stored value\r\n * @constant {number}\r\n */\r\nconst GL_GEQUAL = 0x0206;\r\n/**\r\n * Passed to depthFunction or stencilFunction to specify depth or stencil tests will pass if the new depth value is not equal to the stored value\r\n * @constant {number}\r\n */\r\nconst GL_NOTEQUAL = 0x0205;\r\n// Stencil actions\r\n// Constants passed to WebGLRenderingContext.stencilOp()\r\n/**\r\n * @constant {number}\r\n */\r\nconst GL_KEEP = 0x1e00;\r\n/**\r\n * @constant {number}\r\n */\r\nconst GL_REPLACE = 0x1e01;\r\n/**\r\n * @constant {number}\r\n */\r\nconst GL_INCR = 0x1e02;\r\n/**\r\n * @constant {number}\r\n */\r\nconst GL_DECR = 0x1e03;\r\n/**\r\n * @constant {number}\r\n */\r\nconst GL_INVERT = 0x150a;\r\n/**\r\n * @constant {number}\r\n */\r\nconst GL_INCR_WRAP = 0x8507;\r\n/**\r\n * @constant {number}\r\n */\r\nconst GL_DECR_WRAP = 0x8508;\r\n// Textures\r\n// Constants passed to WebGLRenderingContext.texParameteri(), WebGLRenderingContext.texParameterf(), WebGLRenderingContext.bindTexture(), WebGLRenderingContext.texImage2D(), and others\r\n/**\r\n * @constant {number}\r\n */\r\nconst GL_NEAREST = 0x2600;\r\n/**\r\n * @constant {number}\r\n */\r\nconst GL_LINEAR = 0x2601;\r\n/**\r\n * @constant {number}\r\n */\r\nconst GL_NEAREST_MIPMAP_NEAREST = 0x2700;\r\n/**\r\n * @constant {number}\r\n */\r\nconst GL_LINEAR_MIPMAP_NEAREST = 0x2701;\r\n/**\r\n * @constant {number}\r\n */\r\nconst GL_NEAREST_MIPMAP_LINEAR = 0x2702;\r\n/**\r\n * @constant {number}\r\n */\r\nconst GL_LINEAR_MIPMAP_LINEAR = 0x2703;\r\n/**\r\n * @constant {number}\r\n */\r\nconst GL_TEXTURE_MAG_FILTER = 0x2800;\r\n/**\r\n * @constant {number}\r\n */\r\nconst GL_TEXTURE_MIN_FILTER = 0x2801;\r\n/**\r\n * @constant {number}\r\n */\r\nconst GL_TEXTURE_WRAP_S = 0x2802;\r\n/**\r\n * @constant {number}\r\n */\r\nconst GL_TEXTURE_WRAP_T = 0x2803;\r\n/**\r\n * @constant {number}\r\n */\r\nconst GL_TEXTURE_2D = 0x0de1;\r\n/**\r\n * @constant {number}\r\n */\r\nconst GL_TEXTURE = 0x1702;\r\n/**\r\n * @constant {number}\r\n */\r\nconst GL_TEXTURE_CUBE_MAP = 0x8513;\r\n/**\r\n * @constant {number}\r\n */\r\nconst GL_TEXTURE_BINDING_CUBE_MAP = 0x8514;\r\n/**\r\n * @constant {number}\r\n */\r\nconst GL_TEXTURE_CUBE_MAP_POSITIVE_X = 0x8515;\r\n/**\r\n * @constant {number}\r\n */\r\nconst GL_TEXTURE_CUBE_MAP_NEGATIVE_X = 0x8516;\r\n/**\r\n * @constant {number}\r\n */\r\nconst GL_TEXTURE_CUBE_MAP_POSITIVE_Y = 0x8517;\r\n/**\r\n * @constant {number}\r\n */\r\nconst GL_TEXTURE_CUBE_MAP_NEGATIVE_Y = 0x8518;\r\n/**\r\n * @constant {number}\r\n */\r\nconst GL_TEXTURE_CUBE_MAP_POSITIVE_Z = 0x8519;\r\n/**\r\n * @constant {number}\r\n */\r\nconst GL_TEXTURE_CUBE_MAP_NEGATIVE_Z = 0x851a;\r\n/**\r\n * @constant {number}\r\n */\r\nconst GL_MAX_CUBE_MAP_TEXTURE_SIZE = 0x851c;\r\n/**\r\n * A texture unit\r\n * @constant {number}\r\n */\r\nconst GL_TEXTURE0 = 0x84c0;\r\n/**\r\n * A texture unit\r\n * @constant {number}\r\n */\r\nconst GL_TEXTURE1 = 0x84c1;\r\n/**\r\n * A texture unit\r\n * @constant {number}\r\n */\r\nconst GL_TEXTURE2 = 0x84c2;\r\n/**\r\n * A texture unit\r\n * @constant {number}\r\n */\r\nconst GL_TEXTURE3 = 0x84c3;\r\n/**\r\n * A texture unit\r\n * @constant {number}\r\n */\r\nconst GL_TEXTURE4 = 0x84c4;\r\n/**\r\n * A texture unit\r\n * @constant {number}\r\n */\r\nconst GL_TEXTURE5 = 0x84c5;\r\n/**\r\n * A texture unit\r\n * @constant {number}\r\n */\r\nconst GL_TEXTURE6 = 0x84c6;\r\n/**\r\n * A texture unit\r\n * @constant {number}\r\n */\r\nconst GL_TEXTURE7 = 0x84c7;\r\n/**\r\n * A texture unit\r\n * @constant {number}\r\n */\r\nconst GL_TEXTURE8 = 0x84c8;\r\n/**\r\n * A texture unit\r\n * @constant {number}\r\n */\r\nconst GL_TEXTURE9 = 0x84c9;\r\n/**\r\n * A texture unit\r\n * @constant {number}\r\n */\r\nconst GL_TEXTURE10 = 0x84ca;\r\n/**\r\n * A texture unit\r\n * @constant {number}\r\n */\r\nconst GL_TEXTURE11 = 0x84cb;\r\n/**\r\n * A texture unit\r\n * @constant {number}\r\n */\r\nconst GL_TEXTURE12 = 0x84cc;\r\n/**\r\n * A texture unit\r\n * @constant {number}\r\n */\r\nconst GL_TEXTURE13 = 0x84cd;\r\n/**\r\n * A texture unit\r\n * @constant {number}\r\n */\r\nconst GL_TEXTURE14 = 0x84ce;\r\n/**\r\n * A texture unit\r\n * @constant {number}\r\n */\r\nconst GL_TEXTURE15 = 0x84cf;\r\n/**\r\n * A texture unit\r\n * @constant {number}\r\n */\r\nconst GL_TEXTURE16 = 0x84d0;\r\n/**\r\n * A texture unit\r\n * @constant {number}\r\n */\r\nconst GL_TEXTURE17 = 0x84d1;\r\n/**\r\n * A texture unit\r\n * @constant {number}\r\n */\r\nconst GL_TEXTURE18 = 0x84d2;\r\n/**\r\n * A texture unit\r\n * @constant {number}\r\n */\r\nconst GL_TEXTURE19 = 0x84d3;\r\n/**\r\n * A texture unit\r\n * @constant {number}\r\n */\r\nconst GL_TEXTURE20 = 0x84d4;\r\n/**\r\n * A texture unit\r\n * @constant {number}\r\n */\r\nconst GL_TEXTURE21 = 0x84d5;\r\n/**\r\n * A texture unit\r\n * @constant {number}\r\n */\r\nconst GL_TEXTURE22 = 0x84d6;\r\n/**\r\n * A texture unit\r\n * @constant {number}\r\n */\r\nconst GL_TEXTURE23 = 0x84d7;\r\n/**\r\n * A texture unit\r\n * @constant {number}\r\n */\r\nconst GL_TEXTURE24 = 0x84d8;\r\n/**\r\n * A texture unit\r\n * @constant {number}\r\n */\r\nconst GL_TEXTURE25 = 0x84d9;\r\n/**\r\n * A texture unit\r\n * @constant {number}\r\n */\r\nconst GL_TEXTURE26 = 0x84da;\r\n/**\r\n * A texture unit\r\n * @constant {number}\r\n */\r\nconst GL_TEXTURE27 = 0x84db;\r\n/**\r\n * A texture unit\r\n * @constant {number}\r\n */\r\nconst GL_TEXTURE28 = 0x84dc;\r\n/**\r\n * A texture unit\r\n * @constant {number}\r\n */\r\nconst GL_TEXTURE29 = 0x84dd;\r\n/**\r\n * A texture unit\r\n * @constant {number}\r\n */\r\nconst GL_TEXTURE30 = 0x84de;\r\n/**\r\n * A texture unit\r\n * @constant {number}\r\n */\r\nconst GL_TEXTURE31 = 0x84df;\r\n/**\r\n * The current active texture unit\r\n * @constant {number}\r\n */\r\nconst GL_ACTIVE_TEXTURE = 0x84e0;\r\n/**\r\n * @constant {number}\r\n */\r\nconst GL_REPEAT = 0x2901;\r\n/**\r\n * @constant {number}\r\n */\r\nconst GL_CLAMP_TO_EDGE = 0x812f;\r\n/**\r\n * @constant {number}\r\n */\r\nconst GL_MIRRORED_REPEAT = 0x8370;\r\n// Uniform types\r\n/**\r\n * @constant {number}\r\n */\r\nconst GL_FLOAT_VEC2 = 0x8b50;\r\n/**\r\n * @constant {number}\r\n */\r\nconst GL_FLOAT_VEC3 = 0x8b51;\r\n/**\r\n * @constant {number}\r\n */\r\nconst GL_FLOAT_VEC4 = 0x8b52;\r\n/**\r\n * @constant {number}\r\n */\r\nconst GL_INT_VEC2 = 0x8b53;\r\n/**\r\n * @constant {number}\r\n */\r\nconst GL_INT_VEC3 = 0x8b54;\r\n/**\r\n * @constant {number}\r\n */\r\nconst GL_INT_VEC4 = 0x8b55;\r\n/**\r\n * @constant {number}\r\n */\r\nconst GL_BOOL = 0x8b56;\r\n/**\r\n * @constant {number}\r\n */\r\nconst GL_BOOL_VEC2 = 0x8b57;\r\n/**\r\n * @constant {number}\r\n */\r\nconst GL_BOOL_VEC3 = 0x8b58;\r\n/**\r\n * @constant {number}\r\n */\r\nconst GL_BOOL_VEC4 = 0x8b59;\r\n/**\r\n * @constant {number}\r\n */\r\nconst GL_FLOAT_MAT2 = 0x8b5a;\r\n/**\r\n * @constant {number}\r\n */\r\nconst GL_FLOAT_MAT3 = 0x8b5b;\r\n/**\r\n * @constant {number}\r\n */\r\nconst GL_FLOAT_MAT4 = 0x8b5c;\r\n/**\r\n * @constant {number}\r\n */\r\nconst GL_SAMPLER_2D = 0x8b5e;\r\n/**\r\n * @constant {number}\r\n */\r\nconst GL_SAMPLER_CUBE = 0x8b60;\r\n// Shader precision-specified types\r\n/**\r\n * @constant {number}\r\n */\r\nconst GL_LOW_FLOAT = 0x8df0;\r\n/**\r\n * @constant {number}\r\n */\r\nconst GL_MEDIUM_FLOAT = 0x8df1;\r\n/**\r\n * @constant {number}\r\n */\r\nconst GL_HIGH_FLOAT = 0x8df2;\r\n/**\r\n * @constant {number}\r\n */\r\nconst GL_LOW_INT = 0x8df3;\r\n/**\r\n * @constant {number}\r\n */\r\nconst GL_MEDIUM_INT = 0x8df4;\r\n/**\r\n * @constant {number}\r\n */\r\nconst GL_HIGH_INT = 0x8df5;\r\n// Framebuffers and renderbuffers\r\n/**\r\n * @constant {number}\r\n */\r\nconst GL_FRAMEBUFFER = 0x8d40;\r\n/**\r\n * @constant {number}\r\n */\r\nconst GL_RENDERBUFFER = 0x8d41;\r\n/**\r\n * @constant {number}\r\n */\r\nconst GL_RGBA4 = 0x8056;\r\n/**\r\n * @constant {number}\r\n */\r\nconst GL_RGB5_A1 = 0x8057;\r\n/**\r\n * @constant {number}\r\n */\r\nconst GL_RGB565 = 0x8d62;\r\n/**\r\n * @constant {number}\r\n */\r\nconst GL_DEPTH_COMPONENT16 = 0x81a5;\r\n/**\r\n * @constant {number}\r\n */\r\nconst GL_STENCIL_INDEX = 0x1901;\r\n/**\r\n * @constant {number}\r\n */\r\nconst GL_STENCIL_INDEX8 = 0x8d48;\r\n/**\r\n * @constant {number}\r\n */\r\nconst GL_DEPTH_STENCIL = 0x84f9;\r\n/**\r\n * @constant {number}\r\n */\r\nconst GL_RENDERBUFFER_WIDTH = 0x8d42;\r\n/**\r\n * @constant {number}\r\n */\r\nconst GL_RENDERBUFFER_HEIGHT = 0x8d43;\r\n/**\r\n * @constant {number}\r\n */\r\nconst GL_RENDERBUFFER_INTERNAL_FORMAT = 0x8d44;\r\n/**\r\n * @constant {number}\r\n */\r\nconst GL_RENDERBUFFER_RED_SIZE = 0x8d50;\r\n/**\r\n * @constant {number}\r\n */\r\nconst GL_RENDERBUFFER_GREEN_SIZE = 0x8d51;\r\n/**\r\n * @constant {number}\r\n */\r\nconst GL_RENDERBUFFER_BLUE_SIZE = 0x8d52;\r\n/**\r\n * @constant {number}\r\n */\r\nconst GL_RENDERBUFFER_ALPHA_SIZE = 0x8d53;\r\n/**\r\n * @constant {number}\r\n */\r\nconst GL_RENDERBUFFER_DEPTH_SIZE = 0x8d54;\r\n/**\r\n * @constant {number}\r\n */\r\nconst GL_RENDERBUFFER_STENCIL_SIZE = 0x8d55;\r\n/**\r\n * @constant {number}\r\n */\r\nconst GL_FRAMEBUFFER_ATTACHMENT_OBJECT_TYPE = 0x8cd0;\r\n/**\r\n * @constant {number}\r\n */\r\nconst GL_FRAMEBUFFER_ATTACHMENT_OBJECT_NAME = 0x8cd1;\r\n/**\r\n * @constant {number}\r\n */\r\nconst GL_FRAMEBUFFER_ATTACHMENT_TEXTURE_LEVEL = 0x8cd2;\r\n/**\r\n * @constant {number}\r\n */\r\nconst GL_FRAMEBUFFER_ATTACHMENT_TEXTURE_CUBE_MAP_FACE = 0x8cd3;\r\n/**\r\n * @constant {number}\r\n */\r\nconst GL_COLOR_ATTACHMENT0 = 0x8ce0;\r\n/**\r\n * @constant {number}\r\n */\r\nconst GL_DEPTH_ATTACHMENT = 0x8d00;\r\n/**\r\n * @constant {number}\r\n */\r\nconst GL_STENCIL_ATTACHMENT = 0x8d20;\r\n/**\r\n * @constant {number}\r\n */\r\nconst GL_DEPTH_STENCIL_ATTACHMENT = 0x821a;\r\n/**\r\n * @constant {number}\r\n */\r\nconst GL_NONE = 0;\r\n/**\r\n * @constant {number}\r\n */\r\nconst GL_FRAMEBUFFER_COMPLETE = 0x8cd5;\r\n/**\r\n * @constant {number}\r\n */\r\nconst GL_FRAMEBUFFER_INCOMPLETE_ATTACHMENT = 0x8cd6;\r\n/**\r\n * @constant {number}\r\n */\r\nconst GL_FRAMEBUFFER_INCOMPLETE_MISSING_ATTACHMENT = 0x8cd7;\r\n/**\r\n * @constant {number}\r\n */\r\nconst GL_FRAMEBUFFER_INCOMPLETE_DIMENSIONS = 0x8cd9;\r\n/**\r\n * @constant {number}\r\n */\r\nconst GL_FRAMEBUFFER_UNSUPPORTED = 0x8cdd;\r\n/**\r\n * @constant {number}\r\n */\r\nconst GL_FRAMEBUFFER_BINDING = 0x8ca6;\r\n/**\r\n * @constant {number}\r\n */\r\nconst GL_RENDERBUFFER_BINDING = 0x8ca7;\r\n/**\r\n * @constant {number}\r\n */\r\nconst GL_MAX_RENDERBUFFER_SIZE = 0x84e8;\r\n/**\r\n * @constant {number}\r\n */\r\nconst GL_INVALID_FRAMEBUFFER_OPERATION = 0x0506;\r\n// Pixel storage modes\r\n// Constants passed to WebGLRenderingContext.pixelStorei()\r\n/**\r\n * @constant {number}\r\n */\r\nconst GL_UNPACK_FLIP_Y_WEBGL = 0x9240;\r\n/**\r\n * @constant {number}\r\n */\r\nconst GL_UNPACK_PREMULTIPLY_ALPHA_WEBGL = 0x9241;\r\n/**\r\n * @constant {number}\r\n */\r\nconst GL_UNPACK_COLORSPACE_CONVERSION_WEBGL = 0x9243;\r\n// Additional constants defined WebGL 2\r\n// These constants are defined on the WebGL2RenderingContext interface. All WebGL 1 constants are also available in a WebGL 2 context\r\n// Getting GL parameter information\r\n// Constants passed to WebGLRenderingContext.getParameter() to specify what information to return\r\n/**\r\n * @constant {number}\r\n */\r\nconst GL_READ_BUFFER = 0x0c02;\r\n/**\r\n * @constant {number}\r\n */\r\nconst GL_UNPACK_ROW_LENGTH = 0x0cf2;\r\n/**\r\n * @constant {number}\r\n */\r\nconst GL_UNPACK_SKIP_ROWS = 0x0cf3;\r\n/**\r\n * @constant {number}\r\n */\r\nconst GL_UNPACK_SKIP_PIXELS = 0x0cf4;\r\n/**\r\n * @constant {number}\r\n */\r\nconst GL_PACK_ROW_LENGTH = 0x0d02;\r\n/**\r\n * @constant {number}\r\n */\r\nconst GL_PACK_SKIP_ROWS = 0x0d03;\r\n/**\r\n * @constant {number}\r\n */\r\nconst GL_PACK_SKIP_PIXELS = 0x0d04;\r\n/**\r\n * @constant {number}\r\n */\r\nconst GL_TEXTURE_BINDING_3D = 0x806a;\r\n/**\r\n * @constant {number}\r\n */\r\nconst GL_UNPACK_SKIP_IMAGES = 0x806d;\r\n/**\r\n * @constant {number}\r\n */\r\nconst GL_UNPACK_IMAGE_HEIGHT = 0x806e;\r\n/**\r\n * @constant {number}\r\n */\r\nconst GL_MAX_3D_TEXTURE_SIZE = 0x8073;\r\n/**\r\n * @constant {number}\r\n */\r\nconst GL_MAX_ELEMENTS_VERTICES = 0x80e8;\r\n/**\r\n * @constant {number}\r\n */\r\nconst GL_MAX_ELEMENTS_INDICES = 0x80e9;\r\n/**\r\n * @constant {number}\r\n */\r\nconst GL_MAX_TEXTURE_LOD_BIAS = 0x84fd;\r\n/**\r\n * @constant {number}\r\n */\r\nconst GL_MAX_FRAGMENT_UNIFORM_COMPONENTS = 0x8b49;\r\n/**\r\n * @constant {number}\r\n */\r\nconst GL_MAX_VERTEX_UNIFORM_COMPONENTS = 0x8b4a;\r\n/**\r\n * @constant {number}\r\n */\r\nconst GL_MAX_ARRAY_TEXTURE_LAYERS = 0x88ff;\r\n/**\r\n * @constant {number}\r\n */\r\nconst GL_MIN_PROGRAM_TEXEL_OFFSET = 0x8904;\r\n/**\r\n * @constant {number}\r\n */\r\nconst GL_MAX_PROGRAM_TEXEL_OFFSET = 0x8905;\r\n/**\r\n * @constant {number}\r\n */\r\nconst GL_MAX_VARYING_COMPONENTS = 0x8b4b;\r\n/**\r\n * @constant {number}\r\n */\r\nconst GL_FRAGMENT_SHADER_DERIVATIVE_HINT = 0x8b8b;\r\n/**\r\n * @constant {number}\r\n */\r\nconst GL_RASTERIZER_DISCARD = 0x8c89;\r\n/**\r\n * @constant {number}\r\n */\r\nconst GL_VERTEX_ARRAY_BINDING = 0x85b5;\r\n/**\r\n * @constant {number}\r\n */\r\nconst GL_MAX_VERTEX_OUTPUT_COMPONENTS = 0x9122;\r\n/**\r\n * @constant {number}\r\n */\r\nconst GL_MAX_FRAGMENT_INPUT_COMPONENTS = 0x9125;\r\n/**\r\n * @constant {number}\r\n */\r\nconst GL_MAX_SERVER_WAIT_TIMEOUT = 0x9111;\r\n/**\r\n * @constant {number}\r\n */\r\nconst GL_MAX_ELEMENT_INDEX = 0x8d6b;\r\n// Textures\r\n// Constants passed to WebGLRenderingContext.texParameteri(), WebGLRenderingContext.texParameterf(), WebGLRenderingContext.bindTexture(), WebGLRenderingContext.texImage2D(), and others\r\n/**\r\n * @constant {number}\r\n */\r\nconst GL_RED = 0x1903;\r\n/**\r\n * @constant {number}\r\n */\r\nconst GL_RGB8 = 0x8051;\r\n/**\r\n * @constant {number}\r\n */\r\nconst GL_RGBA8 = 0x8058;\r\n/**\r\n * @constant {number}\r\n */\r\nconst GL_RGB10_A2 = 0x8059;\r\n/**\r\n * @constant {number}\r\n */\r\nconst GL_TEXTURE_3D = 0x806f;\r\n/**\r\n * @constant {number}\r\n */\r\nconst GL_TEXTURE_WRAP_R = 0x8072;\r\n/**\r\n * @constant {number}\r\n */\r\nconst GL_TEXTURE_MIN_LOD = 0x813a;\r\n/**\r\n * @constant {number}\r\n */\r\nconst GL_TEXTURE_MAX_LOD = 0x813b;\r\n/**\r\n * @constant {number}\r\n */\r\nconst GL_TEXTURE_BASE_LEVEL = 0x813c;\r\n/**\r\n * @constant {number}\r\n */\r\nconst GL_TEXTURE_MAX_LEVEL = 0x813d;\r\n/**\r\n * @constant {number}\r\n */\r\nconst GL_TEXTURE_COMPARE_MODE = 0x884c;\r\n/**\r\n * @constant {number}\r\n */\r\nconst GL_TEXTURE_COMPARE_FUNC = 0x884d;\r\n/**\r\n * @constant {number}\r\n */\r\nconst GL_SRGB = 0x8c40;\r\n/**\r\n * @constant {number}\r\n */\r\nconst GL_SRGB8 = 0x8c41;\r\n/**\r\n * @constant {number}\r\n */\r\nconst GL_SRGB8_ALPHA8 = 0x8c43;\r\n/**\r\n * @constant {number}\r\n */\r\nconst GL_COMPARE_REF_TO_TEXTURE = 0x884e;\r\n/**\r\n * @constant {number}\r\n */\r\nconst GL_RGBA32F = 0x8814;\r\n/**\r\n * @constant {number}\r\n */\r\nconst GL_RGB32F = 0x8815;\r\n/**\r\n * @constant {number}\r\n */\r\nconst GL_RGBA16F = 0x881a;\r\n/**\r\n * @constant {number}\r\n */\r\nconst GL_RGB16F = 0x881b;\r\n/**\r\n * @constant {number}\r\n */\r\nconst GL_TEXTURE_2D_ARRAY = 0x8c1a;\r\n/**\r\n * @constant {number}\r\n */\r\nconst GL_TEXTURE_BINDING_2D_ARRAY = 0x8c1d;\r\n/**\r\n * @constant {number}\r\n */\r\nconst GL_R11F_G11F_B10F = 0x8c3a;\r\n/**\r\n * @constant {number}\r\n */\r\nconst GL_RGB9_E5 = 0x8c3d;\r\n/**\r\n * @constant {number}\r\n */\r\nconst GL_RGBA32UI = 0x8d70;\r\n/**\r\n * @constant {number}\r\n */\r\nconst GL_RGB32UI = 0x8d71;\r\n/**\r\n * @constant {number}\r\n */\r\nconst GL_RGBA16UI = 0x8d76;\r\n/**\r\n * @constant {number}\r\n */\r\nconst GL_RGB16UI = 0x8d77;\r\n/**\r\n * @constant {number}\r\n */\r\nconst GL_RGBA8UI = 0x8d7c;\r\n/**\r\n * @constant {number}\r\n */\r\nconst GL_RGB8UI = 0x8d7d;\r\n/**\r\n * @constant {number}\r\n */\r\nconst GL_RGBA32I = 0x8d82;\r\n/**\r\n * @constant {number}\r\n */\r\nconst GL_RGB32I = 0x8d83;\r\n/**\r\n * @constant {number}\r\n */\r\nconst GL_RGBA16I = 0x8d88;\r\n/**\r\n * @constant {number}\r\n */\r\nconst GL_RGB16I = 0x8d89;\r\n/**\r\n * @constant {number}\r\n */\r\nconst GL_RGBA8I = 0x8d8e;\r\n/**\r\n * @constant {number}\r\n */\r\nconst GL_RGB8I = 0x8d8f;\r\n/**\r\n * @constant {number}\r\n */\r\nconst GL_RED_INTEGER = 0x8d94;\r\n/**\r\n * @constant {number}\r\n */\r\nconst GL_RGB_INTEGER = 0x8d98;\r\n/**\r\n * @constant {number}\r\n */\r\nconst GL_RGBA_INTEGER = 0x8d99;\r\n/**\r\n * @constant {number}\r\n */\r\nconst GL_R8 = 0x8229;\r\n/**\r\n * @constant {number}\r\n */\r\nconst GL_RG8 = 0x822b;\r\n/**\r\n * @constant {number}\r\n */\r\nconst GL_R16F = 0x822d;\r\n/**\r\n * @constant {number}\r\n */\r\nconst GL_R32F = 0x822e;\r\n/**\r\n * @constant {number}\r\n */\r\nconst GL_RG16F = 0x822f;\r\n/**\r\n * @constant {number}\r\n */\r\nconst GL_RG32F = 0x8230;\r\n/**\r\n * @constant {number}\r\n */\r\nconst GL_R8I = 0x8231;\r\n/**\r\n * @constant {number}\r\n */\r\nconst GL_R8UI = 0x8232;\r\n/**\r\n * @constant {number}\r\n */\r\nconst GL_R16I = 0x8233;\r\n/**\r\n * @constant {number}\r\n */\r\nconst GL_R16UI = 0x8234;\r\n/**\r\n * @constant {number}\r\n */\r\nconst GL_R32I = 0x8235;\r\n/**\r\n * @constant {number}\r\n */\r\nconst GL_R32UI = 0x8236;\r\n/**\r\n * @constant {number}\r\n */\r\nconst GL_RG8I = 0x8237;\r\n/**\r\n * @constant {number}\r\n */\r\nconst GL_RG8UI = 0x8238;\r\n/**\r\n * @constant {number}\r\n */\r\nconst GL_RG16I = 0x8239;\r\n/**\r\n * @constant {number}\r\n */\r\nconst GL_RG16UI = 0x823a;\r\n/**\r\n * @constant {number}\r\n */\r\nconst GL_RG32I = 0x823b;\r\n/**\r\n * @constant {number}\r\n */\r\nconst GL_RG32UI = 0x823c;\r\n/**\r\n * @constant {number}\r\n */\r\nconst GL_R8_SNORM = 0x8f94;\r\n/**\r\n * @constant {number}\r\n */\r\nconst GL_RG8_SNORM = 0x8f95;\r\n/**\r\n * @constant {number}\r\n */\r\nconst GL_RGB8_SNORM = 0x8f96;\r\n/**\r\n * @constant {number}\r\n */\r\nconst GL_RGBA8_SNORM = 0x8f97;\r\n/**\r\n * @constant {number}\r\n */\r\nconst GL_RGB10_A2UI = 0x906f;\r\n/**\r\n * @constant {number}\r\n */\r\nconst GL_TEXTURE_IMMUTABLE_FORMAT = 0x912f;\r\n/**\r\n * @constant {number}\r\n */\r\nconst GL_TEXTURE_IMMUTABLE_LEVELS = 0x82df;\r\n// Pixel types\r\n/**\r\n * @constant {number}\r\n */\r\nconst GL_UNSIGNED_INT_2_10_10_10_REV = 0x8368;\r\n/**\r\n * @constant {number}\r\n */\r\nconst GL_UNSIGNED_INT_10F_11F_11F_REV = 0x8c3b;\r\n/**\r\n * @constant {number}\r\n */\r\nconst GL_UNSIGNED_INT_5_9_9_9_REV = 0x8c3e;\r\n/**\r\n * @constant {number}\r\n */\r\nconst GL_FLOAT_32_UNSIGNED_INT_24_8_REV = 0x8dad;\r\n/**\r\n * @constant {number}\r\n */\r\nconst GL_UNSIGNED_INT_24_8 = 0x84fa;\r\n/**\r\n * @constant {number}\r\n */\r\nconst GL_HALF_FLOAT = 0x140b;\r\n/**\r\n * @constant {number}\r\n */\r\nconst GL_RG = 0x8227;\r\n/**\r\n * @constant {number}\r\n */\r\nconst GL_RG_INTEGER = 0x8228;\r\n/**\r\n * @constant {number}\r\n */\r\nconst GL_INT_2_10_10_10_REV = 0x8d9f;\r\n// Queries\r\n/**\r\n * @constant {number}\r\n */\r\nconst GL_CURRENT_QUERY = 0x8865;\r\n/**\r\n * @constant {number}\r\n */\r\nconst GL_QUERY_RESULT = 0x8866;\r\n/**\r\n * @constant {number}\r\n */\r\nconst GL_QUERY_RESULT_AVAILABLE = 0x8867;\r\n/**\r\n * @constant {number}\r\n */\r\nconst GL_ANY_SAMPLES_PASSED = 0x8c2f;\r\n/**\r\n * @constant {number}\r\n */\r\nconst GL_ANY_SAMPLES_PASSED_CONSERVATIVE = 0x8d6a;\r\n// Draw buffers\r\n/**\r\n * @constant {number}\r\n */\r\nconst GL_MAX_DRAW_BUFFERS = 0x8824;\r\n/**\r\n * @constant {number}\r\n */\r\nconst GL_DRAW_BUFFER0 = 0x8825;\r\n/**\r\n * @constant {number}\r\n */\r\nconst GL_DRAW_BUFFER1 = 0x8826;\r\n/**\r\n * @constant {number}\r\n */\r\nconst GL_DRAW_BUFFER2 = 0x8827;\r\n/**\r\n * @constant {number}\r\n */\r\nconst GL_DRAW_BUFFER3 = 0x8828;\r\n/**\r\n * @constant {number}\r\n */\r\nconst GL_DRAW_BUFFER4 = 0x8829;\r\n/**\r\n * @constant {number}\r\n */\r\nconst GL_DRAW_BUFFER5 = 0x882a;\r\n/**\r\n * @constant {number}\r\n */\r\nconst GL_DRAW_BUFFER6 = 0x882b;\r\n/**\r\n * @constant {number}\r\n */\r\nconst GL_DRAW_BUFFER7 = 0x882c;\r\n/**\r\n * @constant {number}\r\n */\r\nconst GL_DRAW_BUFFER8 = 0x882d;\r\n/**\r\n * @constant {number}\r\n */\r\nconst GL_DRAW_BUFFER9 = 0x882e;\r\n/**\r\n * @constant {number}\r\n */\r\nconst GL_DRAW_BUFFER10 = 0x882f;\r\n/**\r\n * @constant {number}\r\n */\r\nconst GL_DRAW_BUFFER11 = 0x8830;\r\n/**\r\n * @constant {number}\r\n */\r\nconst GL_DRAW_BUFFER12 = 0x8831;\r\n/**\r\n * @constant {number}\r\n */\r\nconst GL_DRAW_BUFFER13 = 0x8832;\r\n/**\r\n * @constant {number}\r\n */\r\nconst GL_DRAW_BUFFER14 = 0x8833;\r\n/**\r\n * @constant {number}\r\n */\r\nconst GL_DRAW_BUFFER15 = 0x8834;\r\n/**\r\n * @constant {number}\r\n */\r\nconst GL_MAX_COLOR_ATTACHMENTS = 0x8cdf;\r\n/**\r\n * @constant {number}\r\n */\r\nconst GL_COLOR_ATTACHMENT1 = 0x8ce1;\r\n/**\r\n * @constant {number}\r\n */\r\nconst GL_COLOR_ATTACHMENT2 = 0x8ce2;\r\n/**\r\n * @constant {number}\r\n */\r\nconst GL_COLOR_ATTACHMENT3 = 0x8ce3;\r\n/**\r\n * @constant {number}\r\n */\r\nconst GL_COLOR_ATTACHMENT4 = 0x8ce4;\r\n/**\r\n * @constant {number}\r\n */\r\nconst GL_COLOR_ATTACHMENT5 = 0x8ce5;\r\n/**\r\n * @constant {number}\r\n */\r\nconst GL_COLOR_ATTACHMENT6 = 0x8ce6;\r\n/**\r\n * @constant {number}\r\n */\r\nconst GL_COLOR_ATTACHMENT7 = 0x8ce7;\r\n/**\r\n * @constant {number}\r\n */\r\nconst GL_COLOR_ATTACHMENT8 = 0x8ce8;\r\n/**\r\n * @constant {number}\r\n */\r\nconst GL_COLOR_ATTACHMENT9 = 0x8ce9;\r\n/**\r\n * @constant {number}\r\n */\r\nconst GL_COLOR_ATTACHMENT10 = 0x8cea;\r\n/**\r\n * @constant {number}\r\n */\r\nconst GL_COLOR_ATTACHMENT11 = 0x8ceb;\r\n/**\r\n * @constant {number}\r\n */\r\nconst GL_COLOR_ATTACHMENT12 = 0x8cec;\r\n/**\r\n * @constant {number}\r\n */\r\nconst GL_COLOR_ATTACHMENT13 = 0x8ced;\r\n/**\r\n * @constant {number}\r\n */\r\nconst GL_COLOR_ATTACHMENT14 = 0x8cee;\r\n/**\r\n * @constant {number}\r\n */\r\nconst GL_COLOR_ATTACHMENT15 = 0x8cef;\r\n// Samplers\r\n/**\r\n * @constant {number}\r\n */\r\nconst GL_SAMPLER_3D = 0x8b5f;\r\n/**\r\n * @constant {number}\r\n */\r\nconst GL_SAMPLER_2D_SHADOW = 0x8b62;\r\n/**\r\n * @constant {number}\r\n */\r\nconst GL_SAMPLER_2D_ARRAY = 0x8dc1;\r\n/**\r\n * @constant {number}\r\n */\r\nconst GL_SAMPLER_2D_ARRAY_SHADOW = 0x8dc4;\r\n/**\r\n * @constant {number}\r\n */\r\nconst GL_SAMPLER_CUBE_SHADOW = 0x8dc5;\r\n/**\r\n * @constant {number}\r\n */\r\nconst GL_INT_SAMPLER_2D = 0x8dca;\r\n/**\r\n * @constant {number}\r\n */\r\nconst GL_INT_SAMPLER_3D = 0x8dcb;\r\n/**\r\n * @constant {number}\r\n */\r\nconst GL_INT_SAMPLER_CUBE = 0x8dcc;\r\n/**\r\n * @constant {number}\r\n */\r\nconst GL_INT_SAMPLER_2D_ARRAY = 0x8dcf;\r\n/**\r\n * @constant {number}\r\n */\r\nconst GL_UNSIGNED_INT_SAMPLER_2D = 0x8dd2;\r\n/**\r\n * @constant {number}\r\n */\r\nconst GL_UNSIGNED_INT_SAMPLER_3D = 0x8dd3;\r\n/**\r\n * @constant {number}\r\n */\r\nconst GL_UNSIGNED_INT_SAMPLER_CUBE = 0x8dd4;\r\n/**\r\n * @constant {number}\r\n */\r\nconst GL_UNSIGNED_INT_SAMPLER_2D_ARRAY = 0x8dd7;\r\n/**\r\n * @constant {number}\r\n */\r\nconst GL_MAX_SAMPLES = 0x8d57;\r\n/**\r\n * @constant {number}\r\n */\r\nconst GL_SAMPLER_BINDING = 0x8919;\r\n// Buffers\r\n/**\r\n * @constant {number}\r\n */\r\nconst GL_PIXEL_PACK_BUFFER = 0x88eb;\r\n/**\r\n * @constant {number}\r\n */\r\nconst GL_PIXEL_UNPACK_BUFFER = 0x88ec;\r\n/**\r\n * @constant {number}\r\n */\r\nconst GL_PIXEL_PACK_BUFFER_BINDING = 0x88ed;\r\n/**\r\n * @constant {number}\r\n */\r\nconst GL_PIXEL_UNPACK_BUFFER_BINDING = 0x88ef;\r\n/**\r\n * @constant {number}\r\n */\r\nconst GL_COPY_READ_BUFFER = 0x8f36;\r\n/**\r\n * @constant {number}\r\n */\r\nconst GL_COPY_WRITE_BUFFER = 0x8f37;\r\n/**\r\n * @constant {number}\r\n */\r\nconst GL_COPY_READ_BUFFER_BINDING = 0x8f36;\r\n/**\r\n * @constant {number}\r\n */\r\nconst GL_COPY_WRITE_BUFFER_BINDING = 0x8f37;\r\n// Data types\r\n/**\r\n * @constant {number}\r\n */\r\nconst GL_FLOAT_MAT2X3 = 0x8b65;\r\n/**\r\n * @constant {number}\r\n */\r\nconst GL_FLOAT_MAT2X4 = 0x8b66;\r\n/**\r\n * @constant {number}\r\n */\r\nconst GL_FLOAT_MAT3X2 = 0x8b67;\r\n/**\r\n * @constant {number}\r\n */\r\nconst GL_FLOAT_MAT3X4 = 0x8b68;\r\n/**\r\n * @constant {number}\r\n */\r\nconst GL_FLOAT_MAT4X2 = 0x8b69;\r\n/**\r\n * @constant {number}\r\n */\r\nconst GL_FLOAT_MAT4X3 = 0x8b6a;\r\n/**\r\n * @constant {number}\r\n */\r\nconst GL_UNSIGNED_INT_VEC2 = 0x8dc6;\r\n/**\r\n * @constant {number}\r\n */\r\nconst GL_UNSIGNED_INT_VEC3 = 0x8dc7;\r\n/**\r\n * @constant {number}\r\n */\r\nconst GL_UNSIGNED_INT_VEC4 = 0x8dc8;\r\n/**\r\n * @constant {number}\r\n */\r\nconst GL_UNSIGNED_NORMALIZED = 0x8c17;\r\n/**\r\n * @constant {number}\r\n */\r\nconst GL_SIGNED_NORMALIZED = 0x8f9c;\r\n// Vertex attributes\r\n/**\r\n * @constant {number}\r\n */\r\nconst GL_VERTEX_ATTRIB_ARRAY_INTEGER = 0x88fd;\r\n/**\r\n * @constant {number}\r\n */\r\nconst GL_VERTEX_ATTRIB_ARRAY_DIVISOR = 0x88fe;\r\n// Transform feedback\r\n/**\r\n * @constant {number}\r\n */\r\nconst GL_TRANSFORM_FEEDBACK_BUFFER_MODE = 0x8c7f;\r\n/**\r\n * @constant {number}\r\n */\r\nconst GL_MAX_TRANSFORM_FEEDBACK_SEPARATE_COMPONENTS = 0x8c80;\r\n/**\r\n * @constant {number}\r\n */\r\nconst GL_TRANSFORM_FEEDBACK_VARYINGS = 0x8c83;\r\n/**\r\n * @constant {number}\r\n */\r\nconst GL_TRANSFORM_FEEDBACK_BUFFER_START = 0x8c84;\r\n/**\r\n * @constant {number}\r\n */\r\nconst GL_TRANSFORM_FEEDBACK_BUFFER_SIZE = 0x8c85;\r\n/**\r\n * @constant {number}\r\n */\r\nconst GL_TRANSFORM_FEEDBACK_PRIMITIVES_WRITTEN = 0x8c88;\r\n/**\r\n * @constant {number}\r\n */\r\nconst GL_MAX_TRANSFORM_FEEDBACK_INTERLEAVED_COMPONENTS = 0x8c8a;\r\n/**\r\n * @constant {number}\r\n */\r\nconst GL_MAX_TRANSFORM_FEEDBACK_SEPARATE_ATTRIBS = 0x8c8b;\r\n/**\r\n * @constant {number}\r\n */\r\nconst GL_INTERLEAVED_ATTRIBS = 0x8c8c;\r\n/**\r\n * @constant {number}\r\n */\r\nconst GL_SEPARATE_ATTRIBS = 0x8c8d;\r\n/**\r\n * @constant {number}\r\n */\r\nconst GL_TRANSFORM_FEEDBACK_BUFFER = 0x8c8e;\r\n/**\r\n * @constant {number}\r\n */\r\nconst GL_TRANSFORM_FEEDBACK_BUFFER_BINDING = 0x8c8f;\r\n/**\r\n * @constant {number}\r\n */\r\nconst GL_TRANSFORM_FEEDBACK = 0x8e22;\r\n/**\r\n * @constant {number}\r\n */\r\nconst GL_TRANSFORM_FEEDBACK_PAUSED = 0x8e23;\r\n/**\r\n * @constant {number}\r\n */\r\nconst GL_TRANSFORM_FEEDBACK_ACTIVE = 0x8e24;\r\n/**\r\n * @constant {number}\r\n */\r\nconst GL_TRANSFORM_FEEDBACK_BINDING = 0x8e25;\r\n// Framebuffers and renderbuffers\r\n/**\r\n * @constant {number}\r\n */\r\nconst GL_FRAMEBUFFER_ATTACHMENT_COLOR_ENCODING = 0x8210;\r\n/**\r\n * @constant {number}\r\n */\r\nconst GL_FRAMEBUFFER_ATTACHMENT_COMPONENT_TYPE = 0x8211;\r\n/**\r\n * @constant {number}\r\n */\r\nconst GL_FRAMEBUFFER_ATTACHMENT_RED_SIZE = 0x8212;\r\n/**\r\n * @constant {number}\r\n */\r\nconst GL_FRAMEBUFFER_ATTACHMENT_GREEN_SIZE = 0x8213;\r\n/**\r\n * @constant {number}\r\n */\r\nconst GL_FRAMEBUFFER_ATTACHMENT_BLUE_SIZE = 0x8214;\r\n/**\r\n * @constant {number}\r\n */\r\nconst GL_FRAMEBUFFER_ATTACHMENT_ALPHA_SIZE = 0x8215;\r\n/**\r\n * @constant {number}\r\n */\r\nconst GL_FRAMEBUFFER_ATTACHMENT_DEPTH_SIZE = 0x8216;\r\n/**\r\n * @constant {number}\r\n */\r\nconst GL_FRAMEBUFFER_ATTACHMENT_STENCIL_SIZE = 0x8217;\r\n/**\r\n * @constant {number}\r\n */\r\nconst GL_FRAMEBUFFER_DEFAULT = 0x8218;\r\n/**\r\n * @constant {number}\r\n */\r\nconst GL_DEPTH24_STENCIL8 = 0x88f0;\r\n/**\r\n * @constant {number}\r\n */\r\nconst GL_DRAW_FRAMEBUFFER_BINDING = 0x8ca6;\r\n/**\r\n * @constant {number}\r\n */\r\nconst GL_READ_FRAMEBUFFER = 0x8ca8;\r\n/**\r\n * @constant {number}\r\n */\r\nconst GL_DRAW_FRAMEBUFFER = 0x8ca9;\r\n/**\r\n * @constant {number}\r\n */\r\nconst GL_READ_FRAMEBUFFER_BINDING = 0x8caa;\r\n/**\r\n * @constant {number}\r\n */\r\nconst GL_RENDERBUFFER_SAMPLES = 0x8cab;\r\n/**\r\n * @constant {number}\r\n */\r\nconst GL_FRAMEBUFFER_ATTACHMENT_TEXTURE_LAYER = 0x8cd4;\r\n/**\r\n * @constant {number}\r\n */\r\nconst GL_FRAMEBUFFER_INCOMPLETE_MULTISAMPLE = 0x8d56;\r\n// Uniforms\r\n/**\r\n * @constant {number}\r\n */\r\nconst GL_UNIFORM_BUFFER = 0x8a11;\r\n/**\r\n * @constant {number}\r\n */\r\nconst GL_UNIFORM_BUFFER_BINDING = 0x8a28;\r\n/**\r\n * @constant {number}\r\n */\r\nconst GL_UNIFORM_BUFFER_START = 0x8a29;\r\n/**\r\n * @constant {number}\r\n */\r\nconst GL_UNIFORM_BUFFER_SIZE = 0x8a2a;\r\n/**\r\n * @constant {number}\r\n */\r\nconst GL_MAX_VERTEX_UNIFORM_BLOCKS = 0x8a2b;\r\n/**\r\n * @constant {number}\r\n */\r\nconst GL_MAX_FRAGMENT_UNIFORM_BLOCKS = 0x8a2d;\r\n/**\r\n * @constant {number}\r\n */\r\nconst GL_MAX_COMBINED_UNIFORM_BLOCKS = 0x8a2e;\r\n/**\r\n * @constant {number}\r\n */\r\nconst GL_MAX_UNIFORM_BUFFER_BINDINGS = 0x8a2f;\r\n/**\r\n * @constant {number}\r\n */\r\nconst GL_MAX_UNIFORM_BLOCK_SIZE = 0x8a30;\r\n/**\r\n * @constant {number}\r\n */\r\nconst GL_MAX_COMBINED_VERTEX_UNIFORM_COMPONENTS = 0x8a31;\r\n/**\r\n * @constant {number}\r\n */\r\nconst GL_MAX_COMBINED_FRAGMENT_UNIFORM_COMPONENTS = 0x8a33;\r\n/**\r\n * @constant {number}\r\n */\r\nconst GL_UNIFORM_BUFFER_OFFSET_ALIGNMENT = 0x8a34;\r\n/**\r\n * @constant {number}\r\n */\r\nconst GL_ACTIVE_UNIFORM_BLOCKS = 0x8a36;\r\n/**\r\n * @constant {number}\r\n */\r\nconst GL_UNIFORM_TYPE = 0x8a37;\r\n/**\r\n * @constant {number}\r\n */\r\nconst GL_UNIFORM_SIZE = 0x8a38;\r\n/**\r\n * @constant {number}\r\n */\r\nconst GL_UNIFORM_BLOCK_INDEX = 0x8a3a;\r\n/**\r\n * @constant {number}\r\n */\r\nconst GL_UNIFORM_OFFSET = 0x8a3b;\r\n/**\r\n * @constant {number}\r\n */\r\nconst GL_UNIFORM_ARRAY_STRIDE = 0x8a3c;\r\n/**\r\n * @constant {number}\r\n */\r\nconst GL_UNIFORM_MATRIX_STRIDE = 0x8a3d;\r\n/**\r\n * @constant {number}\r\n */\r\nconst GL_UNIFORM_IS_ROW_MAJOR = 0x8a3e;\r\n/**\r\n * @constant {number}\r\n */\r\nconst GL_UNIFORM_BLOCK_BINDING = 0x8a3f;\r\n/**\r\n * @constant {number}\r\n */\r\nconst GL_UNIFORM_BLOCK_DATA_SIZE = 0x8a40;\r\n/**\r\n * @constant {number}\r\n */\r\nconst GL_UNIFORM_BLOCK_ACTIVE_UNIFORMS = 0x8a42;\r\n/**\r\n * @constant {number}\r\n */\r\nconst GL_UNIFORM_BLOCK_ACTIVE_UNIFORM_INDICES = 0x8a43;\r\n/**\r\n * @constant {number}\r\n */\r\nconst GL_UNIFORM_BLOCK_REFERENCED_BY_VERTEX_SHADER = 0x8a44;\r\n/**\r\n * @constant {number}\r\n */\r\nconst GL_UNIFORM_BLOCK_REFERENCED_BY_FRAGMENT_SHADER = 0x8a46;\r\n// Sync objects\r\n/**\r\n * @constant {number}\r\n */\r\nconst GL_OBJECT_TYPE = 0x9112;\r\n/**\r\n * @constant {number}\r\n */\r\nconst GL_SYNC_CONDITION = 0x9113;\r\n/**\r\n * @constant {number}\r\n */\r\nconst GL_SYNC_STATUS = 0x9114;\r\n/**\r\n * @constant {number}\r\n */\r\nconst GL_SYNC_FLAGS = 0x9115;\r\n/**\r\n * @constant {number}\r\n */\r\nconst GL_SYNC_FENCE = 0x9116;\r\n/**\r\n * @constant {number}\r\n */\r\nconst GL_SYNC_GPU_COMMANDS_COMPLETE = 0x9117;\r\n/**\r\n * @constant {number}\r\n */\r\nconst GL_UNSIGNALED = 0x9118;\r\n/**\r\n * @constant {number}\r\n */\r\nconst GL_SIGNALED = 0x9119;\r\n/**\r\n * @constant {number}\r\n */\r\nconst GL_ALREADY_SIGNALED = 0x911a;\r\n/**\r\n * @constant {number}\r\n */\r\nconst GL_TIMEOUT_EXPIRED = 0x911b;\r\n/**\r\n * @constant {number}\r\n */\r\nconst GL_CONDITION_SATISFIED = 0x911c;\r\n/**\r\n * @constant {number}\r\n */\r\nconst GL_WAIT_FAILED = 0x911d;\r\n/**\r\n * @constant {number}\r\n */\r\nconst GL_SYNC_FLUSH_COMMANDS_BIT = 0x00000001;\r\n// Miscellaneous constants\r\n/**\r\n * @constant {number}\r\n */\r\nconst GL_COLOR = 0x1800;\r\n/**\r\n * @constant {number}\r\n */\r\nconst GL_DEPTH = 0x1801;\r\n/**\r\n * @constant {number}\r\n */\r\nconst GL_STENCIL = 0x1802;\r\n/**\r\n * @constant {number}\r\n */\r\nconst GL_MIN = 0x8007;\r\n/**\r\n * @constant {number}\r\n */\r\nconst GL_MAX = 0x8008;\r\n/**\r\n * @constant {number}\r\n */\r\nconst GL_DEPTH_COMPONENT24 = 0x81a6;\r\n/**\r\n * @constant {number}\r\n */\r\nconst GL_STREAM_READ = 0x88e1;\r\n/**\r\n * @constant {number}\r\n */\r\nconst GL_STREAM_COPY = 0x88e2;\r\n/**\r\n * @constant {number}\r\n */\r\nconst GL_STATIC_READ = 0x88e5;\r\n/**\r\n * @constant {number}\r\n */\r\nconst GL_STATIC_COPY = 0x88e6;\r\n/**\r\n * @constant {number}\r\n */\r\nconst GL_DYNAMIC_READ = 0x88e9;\r\n/**\r\n * @constant {number}\r\n */\r\nconst GL_DYNAMIC_COPY = 0x88ea;\r\n/**\r\n * @constant {number}\r\n */\r\nconst GL_DEPTH_COMPONENT32F = 0x8cac;\r\n/**\r\n * @constant {number}\r\n */\r\nconst GL_DEPTH32F_STENCIL8 = 0x8cad;\r\n/**\r\n * @constant {number}\r\n */\r\nconst GL_INVALID_INDEX = 0xffffffff;\r\n/**\r\n * @constant {number}\r\n */\r\nconst GL_TIMEOUT_IGNORED = -1;\r\n/**\r\n * @constant {number}\r\n */\r\nconst GL_MAX_CLIENT_WAIT_TIMEOUT_WEBGL = 0x9247;\r\n// Constants defined in WebGL extensions\r\n// ANGLE_instanced_arrays\r\n// The ANGLE_instanced_arrays extension is part of the WebGL API and allows to draw the same object, or groups of similar objects multiple times, if they share the same vertex data, primitive count and type\r\n/**\r\n * Describes the frequency divisor used for instanced rendering\r\n * @constant {number}\r\n */\r\nconst GL_VERTEX_ATTRIB_ARRAY_DIVISOR_ANGLE = 0x88fe;\r\n// WEBGL_debug_renderer_info\r\n// The WEBGL_debug_renderer_info extension is part of the WebGL API and exposes two constants with information about the graphics driver for debugging purposes\r\n/**\r\n * Passed to getParameter to get the vendor string of the graphics driver\r\n * @constant {number}\r\n */\r\nconst GL_UNMASKED_VENDOR_WEBGL = 0x9245;\r\n/**\r\n * Passed to getParameter to get the renderer string of the graphics driver\r\n * @constant {number}\r\n */\r\nconst GL_UNMASKED_RENDERER_WEBGL = 0x9246;\r\n// EXT_texture_filter_anisotropic\r\n// The EXT_texture_filter_anisotropic extension is part of the WebGL API and exposes two constants for anisotropic filtering (AF)\r\n/**\r\n * Returns the maximum available anisotropy\r\n * @constant {number}\r\n */\r\nconst GL_MAX_TEXTURE_MAX_ANISOTROPY_EXT = 0x84ff;\r\n/**\r\n * Passed to texParameter to set the desired maximum anisotropy for a texture\r\n * @constant {number}\r\n */\r\nconst GL_TEXTURE_MAX_ANISOTROPY_EXT = 0x84fe;\r\n// WEBGL_compressed_texture_s3tc\r\n// The WEBGL_compressed_texture_s3tc extension is part of the WebGL API and exposes four S3TC compressed texture formats\r\n/**\r\n * A DXT1-compressed image in an RGB image format\r\n * @constant {number}\r\n */\r\nconst GL_COMPRESSED_RGB_S3TC_DXT1_EXT = 0x83f0;\r\n/**\r\n * A DXT1-compressed image in an RGB image format with a simple on/off alpha value\r\n * @constant {number}\r\n */\r\nconst GL_COMPRESSED_RGBA_S3TC_DXT1_EXT = 0x83f1;\r\n/**\r\n * A DXT3-compressed image in an RGBA image format. Compared to a 32-bit RGBA texture, it offers 4:1 compression\r\n * @constant {number}\r\n */\r\nconst GL_COMPRESSED_RGBA_S3TC_DXT3_EXT = 0x83f2;\r\n/**\r\n * A DXT5-compressed image in an RGBA image format. It also provides a 4:1 compression, but differs to the DXT3 compression in how the alpha compression is done\r\n * @constant {number}\r\n */\r\nconst GL_COMPRESSED_RGBA_S3TC_DXT5_EXT = 0x83f3;\r\n// WEBGL_compressed_texture_s3tc_srgb\r\n// The WEBGL_compressed_texture_s3tc_srgb extension is part of the WebGL API and exposes four S3TC compressed texture formats for the sRGB colorspace\r\n/**\r\n * A DXT1-compressed image in an sRGB image format\r\n * @constant {number}\r\n */\r\nconst GL_COMPRESSED_SRGB_S3TC_DXT1_EXT = 0x8c4c;\r\n/**\r\n * A DXT1-compressed image in an sRGB image format with a simple on/off alpha value\r\n * @constant {number}\r\n */\r\nconst GL_COMPRESSED_SRGB_ALPHA_S3TC_DXT1_EXT = 0x8c4d;\r\n/**\r\n * A DXT3-compressed image in an sRGBA image format\r\n * @constant {number}\r\n */\r\nconst GL_COMPRESSED_SRGB_ALPHA_S3TC_DXT3_EXT = 0x8c4e;\r\n/**\r\n * A DXT5-compressed image in an sRGBA image format\r\n * @constant {number}\r\n */\r\nconst GL_COMPRESSED_SRGB_ALPHA_S3TC_DXT5_EXT = 0x8c4f;\r\n// WEBGL_compressed_texture_etc\r\n// The WEBGL_compressed_texture_etc extension is part of the WebGL API and exposes 10 ETC/EAC compressed texture formats\r\n/**\r\n * One-channel (red) unsigned format compression\r\n * @constant {number}\r\n */\r\nconst GL_COMPRESSED_R11_EAC = 0x9270;\r\n/**\r\n * One-channel (red) signed format compression\r\n * @constant {number}\r\n */\r\nconst GL_COMPRESSED_SIGNED_R11_EAC = 0x9271;\r\n/**\r\n * Two-channel (red and green) unsigned format compression\r\n * @constant {number}\r\n */\r\nconst GL_COMPRESSED_RG11_EAC = 0x9272;\r\n/**\r\n * Two-channel (red and green) signed format compression\r\n * @constant {number}\r\n */\r\nconst GL_COMPRESSED_SIGNED_RG11_EAC = 0x9273;\r\n/**\r\n * Compresses RBG8 data with no alpha channel\r\n * @constant {number}\r\n */\r\nconst GL_COMPRESSED_RGB8_ETC2 = 0x9274;\r\n/**\r\n * Compresses RGBA8 data. The RGB part is encoded the same as RGB_ETC2, but the alpha part is encoded separately\r\n * @constant {number}\r\n */\r\nconst GL_COMPRESSED_RGBA8_ETC2_EAC = 0x9275;\r\n/**\r\n * Compresses sRBG8 data with no alpha channel\r\n * @constant {number}\r\n */\r\nconst GL_COMPRESSED_SRGB8_ETC2 = 0x9276;\r\n/**\r\n * Compresses sRGBA8 data. The sRGB part is encoded the same as SRGB_ETC2, but the alpha part is encoded separately\r\n * @constant {number}\r\n */\r\nconst GL_COMPRESSED_SRGB8_ALPHA8_ETC2_EAC = 0x9277;\r\n/**\r\n * Similar to RGB8_ETC, but with ability to punch through the alpha channel, which means to make it completely opaque or transparent\r\n * @constant {number}\r\n */\r\nconst GL_COMPRESSED_RGB8_PUNCHTHROUGH_ALPHA1_ETC2 = 0x9278;\r\n/**\r\n * Similar to SRGB8_ETC, but with ability to punch through the alpha channel, which means to make it completely opaque or transparent\r\n * @constant {number}\r\n */\r\nconst GL_COMPRESSED_SRGB8_PUNCHTHROUGH_ALPHA1_ETC2 = 0x9279;\r\n// WEBGL_compressed_texture_pvrtc\r\n// The WEBGL_compressed_texture_pvrtc extension is part of the WebGL API and exposes four PVRTC compressed texture formats\r\n/**\r\n * RGB compression in 4-bit mode. One block for each 4×4 pixels\r\n * @constant {number}\r\n */\r\nconst GL_COMPRESSED_RGB_PVRTC_4BPPV1_IMG = 0x8c00;\r\n/**\r\n * RGBA compression in 4-bit mode. One block for each 4×4 pixels\r\n * @constant {number}\r\n */\r\nconst GL_COMPRESSED_RGBA_PVRTC_4BPPV1_IMG = 0x8c02;\r\n/**\r\n * RGB compression in 2-bit mode. One block for each 8×4 pixels\r\n * @constant {number}\r\n */\r\nconst GL_COMPRESSED_RGB_PVRTC_2BPPV1_IMG = 0x8c01;\r\n/**\r\n * RGBA compression in 2-bit mode. One block for each 8×4 pixels\r\n * @constant {number}\r\n */\r\nconst GL_COMPRESSED_RGBA_PVRTC_2BPPV1_IMG = 0x8c03;\r\n// WEBGL_compressed_texture_etc1\r\n// The WEBGL_compressed_texture_etc1 extension is part of the WebGL API and exposes the ETC1 compressed texture format\r\n/**\r\n * Compresses 24-bit RGB data with no alpha channel\r\n * @constant {number}\r\n */\r\nconst GL_COMPRESSED_RGB_ETC1_WEBGL = 0x8d64;\r\n// WEBGL_compressed_texture_atc\r\n// The WEBGL_compressed_texture_atc extension is part of the WebGL API and exposes 3 ATC compressed texture formats. ATC is a proprietary compression algorithm for compressing textures on handheld devices\r\n/**\r\n * Compresses RGB textures with no alpha channel\r\n * @constant {number}\r\n */\r\nconst GL_COMPRESSED_RGB_ATC_WEBGL = 0x8c92;\r\n/**\r\n * Compresses RGBA textures using explicit alpha encoding (useful when alpha transitions are sharp)\r\n * @constant {number}\r\n */\r\nconst GL_COMPRESSED_RGBA_ATC_EXPLICIT_ALPHA_WEBGL = 0x8c92;\r\n/**\r\n * Compresses RGBA textures using interpolated alpha encoding (useful when alpha transitions are gradient)\r\n * @constant {number}\r\n */\r\nconst GL_COMPRESSED_RGBA_ATC_INTERPOLATED_ALPHA_WEBGL = 0x87ee;\r\n// WEBGL_compressed_texture_astc\r\n// The WEBGL_compressed_texture_astc extension is part of the WebGL API and exposes Adaptive Scalable Texture Compression (ASTC) compressed texture formats to WebGL\r\n// https://www.khronos.org/registry/webgl/extensions/WEBGL_compressed_texture_astc/\r\n// https://developer.nvidia.com/astc-texture-compression-for-game-assets\r\n/**\r\n * Compresses RGBA textures using ASTC compression in a blocksize of 4x4\r\n * @constant {number}\r\n */\r\nconst GL_COMPRESSED_RGBA_ASTC_4X4_KHR = 0x93b0;\r\n/**\r\n * Compresses RGBA textures using ASTC compression in a blocksize of 5x4\r\n * @constant {number}\r\n */\r\nconst GL_COMPRESSED_RGBA_ASTC_5X4_KHR = 0x93b1;\r\n/**\r\n * Compresses RGBA textures using ASTC compression in a blocksize of 5x5\r\n * @constant {number}\r\n */\r\nconst GL_COMPRESSED_RGBA_ASTC_5X5_KHR = 0x93b2;\r\n/**\r\n * Compresses RGBA textures using ASTC compression in a blocksize of 6x5\r\n * @constant {number}\r\n */\r\nconst GL_COMPRESSED_RGBA_ASTC_6X5_KHR = 0x93b3;\r\n/**\r\n * Compresses RGBA textures using ASTC compression in a blocksize of 6x6\r\n * @constant {number}\r\n */\r\nconst GL_COMPRESSED_RGBA_ASTC_6X6_KHR = 0x93b4;\r\n/**\r\n * Compresses RGBA textures using ASTC compression in a blocksize of 8x5\r\n * @constant {number}\r\n */\r\nconst GL_COMPRESSED_RGBA_ASTC_8X5_KHR = 0x93b5;\r\n/**\r\n * Compresses RGBA textures using ASTC compression in a blocksize of 8x6\r\n * @constant {number}\r\n */\r\nconst GL_COMPRESSED_RGBA_ASTC_8X6_KHR = 0x93b6;\r\n/**\r\n * Compresses RGBA textures using ASTC compression in a blocksize of 8x8\r\n * @constant {number}\r\n */\r\nconst GL_COMPRESSED_RGBA_ASTC_8X8_KHR = 0x93b7;\r\n/**\r\n * Compresses RGBA textures using ASTC compression in a blocksize of 10x5\r\n * @constant {number}\r\n */\r\nconst GL_COMPRESSED_RGBA_ASTC_10X5_KHR = 0x93b8;\r\n/**\r\n * Compresses RGBA textures using ASTC compression in a blocksize of 10x6\r\n * @constant {number}\r\n */\r\nconst GL_COMPRESSED_RGBA_ASTC_10X6_KHR = 0x93b9;\r\n/**\r\n * Compresses RGBA textures using ASTC compression in a blocksize of 10x8\r\n * @constant {number}\r\n */\r\nconst GL_COMPRESSED_RGBA_ASTC_10X8_KHR = 0x93ba;\r\n/**\r\n * Compresses RGBA textures using ASTC compression in a blocksize of 10x10\r\n * @constant {number}\r\n */\r\nconst GL_COMPRESSED_RGBA_ASTC_10X10_KHR = 0x93bb;\r\n/**\r\n * Compresses RGBA textures using ASTC compression in a blocksize of 12x10\r\n * @constant {number}\r\n */\r\nconst GL_COMPRESSED_RGBA_ASTC_12X10_KHR = 0x93bc;\r\n/**\r\n * Compresses RGBA textures using ASTC compression in a blocksize of 12x12\r\n * @constant {number}\r\n */\r\nconst GL_COMPRESSED_RGBA_ASTC_12X12_KHR = 0x93bd;\r\n/**\r\n * Compresses SRGB8 textures using ASTC compression in a blocksize of 4x4\r\n * @constant {number}\r\n */\r\nconst GL_COMPRESSED_SRGB8_ALPHA8_ASTC_4X4_KHR = 0x93d0;\r\n/**\r\n * Compresses SRGB8 textures using ASTC compression in a blocksize of 5x4\r\n * @constant {number}\r\n */\r\nconst GL_COMPRESSED_SRGB8_ALPHA8_ASTC_5X4_KHR = 0x93d1;\r\n/**\r\n * Compresses SRGB8 textures using ASTC compression in a blocksize of 5x5\r\n * @constant {number}\r\n */\r\nconst GL_COMPRESSED_SRGB8_ALPHA8_ASTC_5X5_KHR = 0x93d2;\r\n/**\r\n * Compresses SRGB8 textures using ASTC compression in a blocksize of 6x5\r\n * @constant {number}\r\n */\r\nconst GL_COMPRESSED_SRGB8_ALPHA8_ASTC_6X5_KHR = 0x93d3;\r\n/**\r\n * Compresses SRGB8 textures using ASTC compression in a blocksize of 6x6\r\n * @constant {number}\r\n */\r\nconst GL_COMPRESSED_SRGB8_ALPHA8_ASTC_6X6_KHR = 0x93d4;\r\n/**\r\n * Compresses SRGB8 textures using ASTC compression in a blocksize of 8x5\r\n * @constant {number}\r\n */\r\nconst GL_COMPRESSED_SRGB8_ALPHA8_ASTC_8X5_KHR = 0x93d5;\r\n/**\r\n * Compresses SRGB8 textures using ASTC compression in a blocksize of 8x6\r\n * @constant {number}\r\n */\r\nconst GL_COMPRESSED_SRGB8_ALPHA8_ASTC_8X6_KHR = 0x93d6;\r\n/**\r\n * Compresses SRGB8 textures using ASTC compression in a blocksize of 8x8\r\n * @constant {number}\r\n */\r\nconst GL_COMPRESSED_SRGB8_ALPHA8_ASTC_8X8_KHR = 0x93d7;\r\n/**\r\n * Compresses SRGB8 textures using ASTC compression in a blocksize of 10x5\r\n * @constant {number}\r\n */\r\nconst GL_COMPRESSED_SRGB8_ALPHA8_ASTC_10X5_KHR = 0x93d8;\r\n/**\r\n * Compresses SRGB8 textures using ASTC compression in a blocksize of 10x6\r\n * @constant {number}\r\n */\r\nconst GL_COMPRESSED_SRGB8_ALPHA8_ASTC_10X6_KHR = 0x93d9;\r\n/**\r\n * Compresses SRGB8 textures using ASTC compression in a blocksize of 10x8\r\n * @constant {number}\r\n */\r\nconst GL_COMPRESSED_SRGB8_ALPHA8_ASTC_10X8_KHR = 0x93da;\r\n/**\r\n * Compresses SRGB8 textures using ASTC compression in a blocksize of 10x10\r\n * @constant {number}\r\n */\r\nconst GL_COMPRESSED_SRGB8_ALPHA8_ASTC_10X10_KHR = 0x93db;\r\n/**\r\n * Compresses SRGB8 textures using ASTC compression in a blocksize of 12x10\r\n * @constant {number}\r\n */\r\nconst GL_COMPRESSED_SRGB8_ALPHA8_ASTC_12X10_KHR = 0x93dc;\r\n/**\r\n * Compresses SRGB8 textures using ASTC compression in a blocksize of 12x12\r\n * @constant {number}\r\n */\r\nconst GL_COMPRESSED_SRGB8_ALPHA8_ASTC_12X12_KHR = 0x93dd;\r\n// WEBGL_depth_texture\r\n// The WEBGL_depth_texture extension is part of the WebGL API and defines 2D depth and depth-stencil textures\r\n/**\r\n * Unsigned integer type for 24-bit depth texture data\r\n * @constant {number}\r\n */\r\nconst GL_UNSIGNED_INT_24_8_WEBGL = 0x84fa;\r\n// OES_texture_half_float\r\n// The OES_texture_half_float extension is part of the WebGL API and adds texture formats with 16- (aka half float) and 32-bit floating-point components\r\n/**\r\n * Half floating-point type (16-bit)\r\n * @constant {number}\r\n */\r\nconst GL_HALF_FLOAT_OES = 0x8d61;\r\n// WEBGL_color_buffer_float\r\n// The WEBGL_color_buffer_float extension is part of the WebGL API and adds the ability to render to 32-bit floating-point color buffers\r\n/**\r\n * RGBA 32-bit floating-point color-renderable format\r\n * @constant {number}\r\n */\r\nconst GL_RGBA32F_EXT = 0x8814;\r\n/**\r\n * RGB 32-bit floating-point color-renderable format\r\n * @constant {number}\r\n */\r\nconst GL_RGB32F_EXT = 0x8815;\r\n/**\r\n * @constant {number}\r\n */\r\nconst GL_FRAMEBUFFER_ATTACHMENT_COMPONENT_TYPE_EXT = 0x8211;\r\n/**\r\n * @constant {number}\r\n */\r\nconst GL_UNSIGNED_NORMALIZED_EXT = 0x8c17;\r\n// EXT_blend_minmax\r\n// The EXT_blend_minmax extension is part of the WebGL API and extends blending capabilities by adding two new blend equations: the minimum or maximum color components of the source and destination colors\r\n/**\r\n * Produces the minimum color components of the source and destination colors\r\n * @constant {number}\r\n */\r\nconst GL_MIN_EXT = 0x8007;\r\n/**\r\n * Produces the maximum color components of the source and destination colors\r\n * @constant {number}\r\n */\r\nconst GL_MAX_EXT = 0x8008;\r\n// EXT_sRGB\r\n// The EXT_sRGB extension is part of the WebGL API and adds sRGB support to textures and framebuffer objects\r\n/**\r\n * Unsized sRGB format that leaves the precision up to the driver\r\n * @constant {number}\r\n */\r\nconst GL_SRGB_EXT = 0x8c40;\r\n/**\r\n * Unsized sRGB format with unsized alpha component\r\n * @constant {number}\r\n */\r\nconst GL_SRGB_ALPHA_EXT = 0x8c42;\r\n/**\r\n * Sized (8-bit) sRGB and alpha formats\r\n * @constant {number}\r\n */\r\nconst GL_SRGB8_ALPHA8_EXT = 0x8c43;\r\n/**\r\n * Returns the framebuffer color encoding\r\n * @constant {number}\r\n */\r\nconst GL_FRAMEBUFFER_ATTACHMENT_COLOR_ENCODING_EXT = 0x8210;\r\n// OES_standard_derivatives\r\n// The OES_standard_derivatives extension is part of the WebGL API and adds the GLSL derivative functions dFdx, dFdy, and fwidth\r\n/**\r\n * Indicates the accuracy of the derivative calculation for the GLSL built-in functions: dFdx, dFdy, and fwidth\r\n * @constant {number}\r\n */\r\nconst GL_FRAGMENT_SHADER_DERIVATIVE_HINT_OES = 0x8b8b;\r\n// WEBGL_draw_buffers\r\n// The WEBGL_draw_buffers extension is part of the WebGL API and enables a fragment shader to write to several textures, which is useful for deferred shading, for example\r\n/**\r\n * Framebuffer color attachment point\r\n * @constant {number}\r\n */\r\nconst GL_COLOR_ATTACHMENT0_WEBGL = 0x8ce0;\r\n/**\r\n * Framebuffer color attachment point\r\n * @constant {number}\r\n */\r\nconst GL_COLOR_ATTACHMENT1_WEBGL = 0x8ce1;\r\n/**\r\n * Framebuffer color attachment point\r\n * @constant {number}\r\n */\r\nconst GL_COLOR_ATTACHMENT2_WEBGL = 0x8ce2;\r\n/**\r\n * Framebuffer color attachment point\r\n * @constant {number}\r\n */\r\nconst GL_COLOR_ATTACHMENT3_WEBGL = 0x8ce3;\r\n/**\r\n * Framebuffer color attachment point\r\n * @constant {number}\r\n */\r\nconst GL_COLOR_ATTACHMENT4_WEBGL = 0x8ce4;\r\n/**\r\n * Framebuffer color attachment point\r\n * @constant {number}\r\n */\r\nconst GL_COLOR_ATTACHMENT5_WEBGL = 0x8ce5;\r\n/**\r\n * Framebuffer color attachment point\r\n * @constant {number}\r\n */\r\nconst GL_COLOR_ATTACHMENT6_WEBGL = 0x8ce6;\r\n/**\r\n * Framebuffer color attachment point\r\n * @constant {number}\r\n */\r\nconst GL_COLOR_ATTACHMENT7_WEBGL = 0x8ce7;\r\n/**\r\n * Framebuffer color attachment point\r\n * @constant {number}\r\n */\r\nconst GL_COLOR_ATTACHMENT8_WEBGL = 0x8ce8;\r\n/**\r\n * Framebuffer color attachment point\r\n * @constant {number}\r\n */\r\nconst GL_COLOR_ATTACHMENT9_WEBGL = 0x8ce9;\r\n/**\r\n * Framebuffer color attachment point\r\n * @constant {number}\r\n */\r\nconst GL_COLOR_ATTACHMENT10_WEBGL = 0x8cea;\r\n/**\r\n * Framebuffer color attachment point\r\n * @constant {number}\r\n */\r\nconst GL_COLOR_ATTACHMENT11_WEBGL = 0x8ceb;\r\n/**\r\n * Framebuffer color attachment point\r\n * @constant {number}\r\n */\r\nconst GL_COLOR_ATTACHMENT12_WEBGL = 0x8cec;\r\n/**\r\n * Framebuffer color attachment point\r\n * @constant {number}\r\n */\r\nconst GL_COLOR_ATTACHMENT13_WEBGL = 0x8ced;\r\n/**\r\n * Framebuffer color attachment point\r\n * @constant {number}\r\n */\r\nconst GL_COLOR_ATTACHMENT14_WEBGL = 0x8cee;\r\n/**\r\n * Framebuffer color attachment point\r\n * @constant {number}\r\n */\r\nconst GL_COLOR_ATTACHMENT15_WEBGL = 0x8cef;\r\n/**\r\n * Draw buffer\r\n * @constant {number}\r\n */\r\nconst GL_DRAW_BUFFER0_WEBGL = 0x8825;\r\n/**\r\n * Draw buffer\r\n * @constant {number}\r\n */\r\nconst GL_DRAW_BUFFER1_WEBGL = 0x8826;\r\n/**\r\n * Draw buffer\r\n * @constant {number}\r\n */\r\nconst GL_DRAW_BUFFER2_WEBGL = 0x8827;\r\n/**\r\n * Draw buffer\r\n * @constant {number}\r\n */\r\nconst GL_DRAW_BUFFER3_WEBGL = 0x8828;\r\n/**\r\n * Draw buffer\r\n * @constant {number}\r\n */\r\nconst GL_DRAW_BUFFER4_WEBGL = 0x8829;\r\n/**\r\n * Draw buffer\r\n * @constant {number}\r\n */\r\nconst GL_DRAW_BUFFER5_WEBGL = 0x882a;\r\n/**\r\n * Draw buffer\r\n * @constant {number}\r\n */\r\nconst GL_DRAW_BUFFER6_WEBGL = 0x882b;\r\n/**\r\n * Draw buffer\r\n * @constant {number}\r\n */\r\nconst GL_DRAW_BUFFER7_WEBGL = 0x882c;\r\n/**\r\n * Draw buffer\r\n * @constant {number}\r\n */\r\nconst GL_DRAW_BUFFER8_WEBGL = 0x882d;\r\n/**\r\n * Draw buffer\r\n * @constant {number}\r\n */\r\nconst GL_DRAW_BUFFER9_WEBGL = 0x882e;\r\n/**\r\n * Draw buffer\r\n * @constant {number}\r\n */\r\nconst GL_DRAW_BUFFER10_WEBGL = 0x882f;\r\n/**\r\n * Draw buffer\r\n * @constant {number}\r\n */\r\nconst GL_DRAW_BUFFER11_WEBGL = 0x8830;\r\n/**\r\n * Draw buffer\r\n * @constant {number}\r\n */\r\nconst GL_DRAW_BUFFER12_WEBGL = 0x8831;\r\n/**\r\n * Draw buffer\r\n * @constant {number}\r\n */\r\nconst GL_DRAW_BUFFER13_WEBGL = 0x8832;\r\n/**\r\n * Draw buffer\r\n * @constant {number}\r\n */\r\nconst GL_DRAW_BUFFER14_WEBGL = 0x8833;\r\n/**\r\n * Draw buffer\r\n * @constant {number}\r\n */\r\nconst GL_DRAW_BUFFER15_WEBGL = 0x8834;\r\n/**\r\n * Maximum number of framebuffer color attachment points\r\n * @constant {number}\r\n */\r\nconst GL_MAX_COLOR_ATTACHMENTS_WEBGL = 0x8cdf;\r\n/**\r\n * Maximum number of draw buffers\r\n * @constant {number}\r\n */\r\nconst GL_MAX_DRAW_BUFFERS_WEBGL = 0x8824;\r\n// OES_vertex_array_object\r\n// The OES_vertex_array_object extension is part of the WebGL API and provides vertex array objects (VAOs) which encapsulate vertex array states. These objects keep pointers to vertex data and provide names for different sets of vertex data\r\n/**\r\n * The bound vertex array object (VAO)\r\n * @constant {number}\r\n */\r\nconst GL_VERTEX_ARRAY_BINDING_OES = 0x85b5;\r\n// EXT_disjoint_timer_query\r\n// The EXT_disjoint_timer_query extension is part of the WebGL API and provides a way to measure the duration of a set of GL commands, without stalling the rendering pipeline\r\n/**\r\n * The number of bits used to hold the query result for the given target\r\n * @constant {number}\r\n */\r\nconst GL_QUERY_COUNTER_BITS_EXT = 0x8864;\r\n/**\r\n * The currently active query\r\n * @constant {number}\r\n */\r\nconst GL_CURRENT_QUERY_EXT = 0x8865;\r\n/**\r\n * The query result\r\n * @constant {number}\r\n */\r\nconst GL_QUERY_RESULT_EXT = 0x8866;\r\n/**\r\n * A Boolean indicating whether or not a query result is available\r\n * @constant {number}\r\n */\r\nconst GL_QUERY_RESULT_AVAILABLE_EXT = 0x8867;\r\n/**\r\n * Elapsed time (in nanoseconds)\r\n * @constant {number}\r\n */\r\nconst GL_TIME_ELAPSED_EXT = 0x88bf;\r\n/**\r\n * The current time\r\n * @constant {number}\r\n */\r\nconst GL_TIMESTAMP_EXT = 0x8e28;\r\n/**\r\n * A Boolean indicating whether or not the GPU performed any disjoint operation\r\n * @constant {number}\r\n */\r\nconst GL_GPU_DISJOINT_EXT = 0x8fbb;\r\n// Constants defined in WebGL draft extensions\r\n// KHR_parallel_shader_compile\r\n// The KHR_parallel_shader_compile extension is part of the WebGL draft API and provides multithreaded asynchronous shader compilation\r\n/**\r\n * Query to determine if the compilation process is complete\r\n * @constant {number}\r\n */\r\nconst GL_COMPLETION_STATUS_KHR = 0x91b1;\n\nexport { GL_ACTIVE_ATTRIBUTES, GL_ACTIVE_TEXTURE, GL_ACTIVE_UNIFORMS, GL_ACTIVE_UNIFORM_BLOCKS, GL_ALIASED_LINE_WIDTH_RANGE, GL_ALIASED_POINT_SIZE_RANGE, GL_ALPHA, GL_ALPHA_BITS, GL_ALREADY_SIGNALED, GL_ALWAYS, GL_ANY_SAMPLES_PASSED, GL_ANY_SAMPLES_PASSED_CONSERVATIVE, GL_ARRAY_BUFFER, GL_ARRAY_BUFFER_BINDING, GL_ATTACHED_SHADERS, GL_BACK, GL_BLEND, GL_BLEND_COLOR, GL_BLEND_DST_ALPHA, GL_BLEND_DST_RGB, GL_BLEND_EQUATION, GL_BLEND_EQUATION_ALPHA, GL_BLEND_EQUATION_RGB, GL_BLEND_SRC_ALPHA, GL_BLEND_SRC_RGB, GL_BLUE_BITS, GL_BOOL, GL_BOOL_VEC2, GL_BOOL_VEC3, GL_BOOL_VEC4, GL_BROWSER_DEFAULT_WEBGL, GL_BUFFER_SIZE, GL_BUFFER_USAGE, GL_BYTE, GL_CCW, GL_CLAMP_TO_EDGE, GL_COLOR, GL_COLOR_ATTACHMENT0, GL_COLOR_ATTACHMENT0_WEBGL, GL_COLOR_ATTACHMENT1, GL_COLOR_ATTACHMENT10, GL_COLOR_ATTACHMENT10_WEBGL, GL_COLOR_ATTACHMENT11, GL_COLOR_ATTACHMENT11_WEBGL, GL_COLOR_ATTACHMENT12, GL_COLOR_ATTACHMENT12_WEBGL, GL_COLOR_ATTACHMENT13, GL_COLOR_ATTACHMENT13_WEBGL, GL_COLOR_ATTACHMENT14, GL_COLOR_ATTACHMENT14_WEBGL, GL_COLOR_ATTACHMENT15, GL_COLOR_ATTACHMENT15_WEBGL, GL_COLOR_ATTACHMENT1_WEBGL, GL_COLOR_ATTACHMENT2, GL_COLOR_ATTACHMENT2_WEBGL, GL_COLOR_ATTACHMENT3, GL_COLOR_ATTACHMENT3_WEBGL, GL_COLOR_ATTACHMENT4, GL_COLOR_ATTACHMENT4_WEBGL, GL_COLOR_ATTACHMENT5, GL_COLOR_ATTACHMENT5_WEBGL, GL_COLOR_ATTACHMENT6, GL_COLOR_ATTACHMENT6_WEBGL, GL_COLOR_ATTACHMENT7, GL_COLOR_ATTACHMENT7_WEBGL, GL_COLOR_ATTACHMENT8, GL_COLOR_ATTACHMENT8_WEBGL, GL_COLOR_ATTACHMENT9, GL_COLOR_ATTACHMENT9_WEBGL, GL_COLOR_BUFFER_BIT, GL_COLOR_CLEAR_VALUE, GL_COLOR_WRITEMASK, GL_COMPARE_REF_TO_TEXTURE, GL_COMPILE_STATUS, GL_COMPLETION_STATUS_KHR, GL_COMPRESSED_R11_EAC, GL_COMPRESSED_RG11_EAC, GL_COMPRESSED_RGB8_ETC2, GL_COMPRESSED_RGB8_PUNCHTHROUGH_ALPHA1_ETC2, GL_COMPRESSED_RGBA8_ETC2_EAC, GL_COMPRESSED_RGBA_ASTC_10X10_KHR, GL_COMPRESSED_RGBA_ASTC_10X5_KHR, GL_COMPRESSED_RGBA_ASTC_10X6_KHR, GL_COMPRESSED_RGBA_ASTC_10X8_KHR, GL_COMPRESSED_RGBA_ASTC_12X10_KHR, GL_COMPRESSED_RGBA_ASTC_12X12_KHR, GL_COMPRESSED_RGBA_ASTC_4X4_KHR, GL_COMPRESSED_RGBA_ASTC_5X4_KHR, GL_COMPRESSED_RGBA_ASTC_5X5_KHR, GL_COMPRESSED_RGBA_ASTC_6X5_KHR, GL_COMPRESSED_RGBA_ASTC_6X6_KHR, GL_COMPRESSED_RGBA_ASTC_8X5_KHR, GL_COMPRESSED_RGBA_ASTC_8X6_KHR, GL_COMPRESSED_RGBA_ASTC_8X8_KHR, GL_COMPRESSED_RGBA_ATC_EXPLICIT_ALPHA_WEBGL, GL_COMPRESSED_RGBA_ATC_INTERPOLATED_ALPHA_WEBGL, GL_COMPRESSED_RGBA_PVRTC_2BPPV1_IMG, GL_COMPRESSED_RGBA_PVRTC_4BPPV1_IMG, GL_COMPRESSED_RGBA_S3TC_DXT1_EXT, GL_COMPRESSED_RGBA_S3TC_DXT3_EXT, GL_COMPRESSED_RGBA_S3TC_DXT5_EXT, GL_COMPRESSED_RGB_ATC_WEBGL, GL_COMPRESSED_RGB_ETC1_WEBGL, GL_COMPRESSED_RGB_PVRTC_2BPPV1_IMG, GL_COMPRESSED_RGB_PVRTC_4BPPV1_IMG, GL_COMPRESSED_RGB_S3TC_DXT1_EXT, GL_COMPRESSED_SIGNED_R11_EAC, GL_COMPRESSED_SIGNED_RG11_EAC, GL_COMPRESSED_SRGB8_ALPHA8_ASTC_10X10_KHR, GL_COMPRESSED_SRGB8_ALPHA8_ASTC_10X5_KHR, GL_COMPRESSED_SRGB8_ALPHA8_ASTC_10X6_KHR, GL_COMPRESSED_SRGB8_ALPHA8_ASTC_10X8_KHR, GL_COMPRESSED_SRGB8_ALPHA8_ASTC_12X10_KHR, GL_COMPRESSED_SRGB8_ALPHA8_ASTC_12X12_KHR, GL_COMPRESSED_SRGB8_ALPHA8_ASTC_4X4_KHR, GL_COMPRESSED_SRGB8_ALPHA8_ASTC_5X4_KHR, GL_COMPRESSED_SRGB8_ALPHA8_ASTC_5X5_KHR, GL_COMPRESSED_SRGB8_ALPHA8_ASTC_6X5_KHR, GL_COMPRESSED_SRGB8_ALPHA8_ASTC_6X6_KHR, GL_COMPRESSED_SRGB8_ALPHA8_ASTC_8X5_KHR, GL_COMPRESSED_SRGB8_ALPHA8_ASTC_8X6_KHR, GL_COMPRESSED_SRGB8_ALPHA8_ASTC_8X8_KHR, GL_COMPRESSED_SRGB8_ALPHA8_ETC2_EAC, GL_COMPRESSED_SRGB8_ETC2, GL_COMPRESSED_SRGB8_PUNCHTHROUGH_ALPHA1_ETC2, GL_COMPRESSED_SRGB_ALPHA_S3TC_DXT1_EXT, GL_COMPRESSED_SRGB_ALPHA_S3TC_DXT3_EXT, GL_COMPRESSED_SRGB_ALPHA_S3TC_DXT5_EXT, GL_COMPRESSED_SRGB_S3TC_DXT1_EXT, GL_COMPRESSED_TEXTURE_FORMATS, GL_CONDITION_SATISFIED, GL_CONSTANT_ALPHA, GL_CONSTANT_COLOR, GL_CONTEXT_LOST_WEBGL, GL_COPY_READ_BUFFER, GL_COPY_READ_BUFFER_BINDING, GL_COPY_WRITE_BUFFER, GL_COPY_WRITE_BUFFER_BINDING, GL_CULL_FACE, GL_CULL_FACE_MODE, GL_CURRENT_PROGRAM, GL_CURRENT_QUERY, GL_CURRENT_QUERY_EXT, GL_CURRENT_VERTEX_ATTRIB, GL_CW, GL_DECR, GL_DECR_WRAP, GL_DELETE_STATUS, GL_DEPTH, GL_DEPTH24_STENCIL8, GL_DEPTH32F_STENCIL8, GL_DEPTH_ATTACHMENT, GL_DEPTH_BITS, GL_DEPTH_BUFFER_BIT, GL_DEPTH_CLEAR_VALUE, GL_DEPTH_COMPONENT, GL_DEPTH_COMPONENT16, GL_DEPTH_COMPONENT24, GL_DEPTH_COMPONENT32F, GL_DEPTH_FUNC, GL_DEPTH_RANGE, GL_DEPTH_STENCIL, GL_DEPTH_STENCIL_ATTACHMENT, GL_DEPTH_TEST, GL_DEPTH_WRITEMASK, GL_DITHER, GL_DONT_CARE, GL_DRAW_BUFFER0, GL_DRAW_BUFFER0_WEBGL, GL_DRAW_BUFFER1, GL_DRAW_BUFFER10, GL_DRAW_BUFFER10_WEBGL, GL_DRAW_BUFFER11, GL_DRAW_BUFFER11_WEBGL, GL_DRAW_BUFFER12, GL_DRAW_BUFFER12_WEBGL, GL_DRAW_BUFFER13, GL_DRAW_BUFFER13_WEBGL, GL_DRAW_BUFFER14, GL_DRAW_BUFFER14_WEBGL, GL_DRAW_BUFFER15, GL_DRAW_BUFFER15_WEBGL, GL_DRAW_BUFFER1_WEBGL, GL_DRAW_BUFFER2, GL_DRAW_BUFFER2_WEBGL, GL_DRAW_BUFFER3, GL_DRAW_BUFFER3_WEBGL, GL_DRAW_BUFFER4, GL_DRAW_BUFFER4_WEBGL, GL_DRAW_BUFFER5, GL_DRAW_BUFFER5_WEBGL, GL_DRAW_BUFFER6, GL_DRAW_BUFFER6_WEBGL, GL_DRAW_BUFFER7, GL_DRAW_BUFFER7_WEBGL, GL_DRAW_BUFFER8, GL_DRAW_BUFFER8_WEBGL, GL_DRAW_BUFFER9, GL_DRAW_BUFFER9_WEBGL, GL_DRAW_FRAMEBUFFER, GL_DRAW_FRAMEBUFFER_BINDING, GL_DST_ALPHA, GL_DST_COLOR, GL_DYNAMIC_COPY, GL_DYNAMIC_DRAW, GL_DYNAMIC_READ, GL_ELEMENT_ARRAY_BUFFER, GL_ELEMENT_ARRAY_BUFFER_BINDING, GL_EQUAL, GL_FASTEST, GL_FLOAT, GL_FLOAT_32_UNSIGNED_INT_24_8_REV, GL_FLOAT_MAT2, GL_FLOAT_MAT2X3, GL_FLOAT_MAT2X4, GL_FLOAT_MAT3, GL_FLOAT_MAT3X2, GL_FLOAT_MAT3X4, GL_FLOAT_MAT4, GL_FLOAT_MAT4X2, GL_FLOAT_MAT4X3, GL_FLOAT_VEC2, GL_FLOAT_VEC3, GL_FLOAT_VEC4, GL_FRAGMENT_SHADER, GL_FRAGMENT_SHADER_DERIVATIVE_HINT, GL_FRAGMENT_SHADER_DERIVATIVE_HINT_OES, GL_FRAMEBUFFER, GL_FRAMEBUFFER_ATTACHMENT_ALPHA_SIZE, GL_FRAMEBUFFER_ATTACHMENT_BLUE_SIZE, GL_FRAMEBUFFER_ATTACHMENT_COLOR_ENCODING, GL_FRAMEBUFFER_ATTACHMENT_COLOR_ENCODING_EXT, GL_FRAMEBUFFER_ATTACHMENT_COMPONENT_TYPE, GL_FRAMEBUFFER_ATTACHMENT_COMPONENT_TYPE_EXT, GL_FRAMEBUFFER_ATTACHMENT_DEPTH_SIZE, GL_FRAMEBUFFER_ATTACHMENT_GREEN_SIZE, GL_FRAMEBUFFER_ATTACHMENT_OBJECT_NAME, GL_FRAMEBUFFER_ATTACHMENT_OBJECT_TYPE, GL_FRAMEBUFFER_ATTACHMENT_RED_SIZE, GL_FRAMEBUFFER_ATTACHMENT_STENCIL_SIZE, GL_FRAMEBUFFER_ATTACHMENT_TEXTURE_CUBE_MAP_FACE, GL_FRAMEBUFFER_ATTACHMENT_TEXTURE_LAYER, GL_FRAMEBUFFER_ATTACHMENT_TEXTURE_LEVEL, GL_FRAMEBUFFER_BINDING, GL_FRAMEBUFFER_COMPLETE, GL_FRAMEBUFFER_DEFAULT, GL_FRAMEBUFFER_INCOMPLETE_ATTACHMENT, GL_FRAMEBUFFER_INCOMPLETE_DIMENSIONS, GL_FRAMEBUFFER_INCOMPLETE_MISSING_ATTACHMENT, GL_FRAMEBUFFER_INCOMPLETE_MULTISAMPLE, GL_FRAMEBUFFER_UNSUPPORTED, GL_FRONT, GL_FRONT_AND_BACK, GL_FRONT_FACE, GL_FUNC_ADD, GL_FUNC_REVERSE_SUBTRACT, GL_FUNC_SUBSTRACT, GL_GENERATE_MIPMAP_HINT, GL_GEQUAL, GL_GPU_DISJOINT_EXT, GL_GREATER, GL_GREEN_BITS, GL_HALF_FLOAT, GL_HALF_FLOAT_OES, GL_HIGH_FLOAT, GL_HIGH_INT, GL_IMPLEMENTATION_COLOR_READ_FORMAT, GL_IMPLEMENTATION_COLOR_READ_TYPE, GL_INCR, GL_INCR_WRAP, GL_INT, GL_INTERLEAVED_ATTRIBS, GL_INT_2_10_10_10_REV, GL_INT_SAMPLER_2D, GL_INT_SAMPLER_2D_ARRAY, GL_INT_SAMPLER_3D, GL_INT_SAMPLER_CUBE, GL_INT_VEC2, GL_INT_VEC3, GL_INT_VEC4, GL_INVALID_ENUM, GL_INVALID_FRAMEBUFFER_OPERATION, GL_INVALID_INDEX, GL_INVALID_OPERATION, GL_INVALID_VALUE, GL_INVERT, GL_KEEP, GL_LEQUAL, GL_LESS, GL_LINEAR, GL_LINEAR_MIPMAP_LINEAR, GL_LINEAR_MIPMAP_NEAREST, GL_LINES, GL_LINE_LOOP, GL_LINE_STRIP, GL_LINE_WIDTH, GL_LINK_STATUS, GL_LOW_FLOAT, GL_LOW_INT, GL_LUMINANCE, GL_LUMINANCE_ALPHA, GL_MAX, GL_MAX_3D_TEXTURE_SIZE, GL_MAX_ARRAY_TEXTURE_LAYERS, GL_MAX_CLIENT_WAIT_TIMEOUT_WEBGL, GL_MAX_COLOR_ATTACHMENTS, GL_MAX_COLOR_ATTACHMENTS_WEBGL, GL_MAX_COMBINED_FRAGMENT_UNIFORM_COMPONENTS, GL_MAX_COMBINED_TEXTURE_IMAGE_UNITS, GL_MAX_COMBINED_UNIFORM_BLOCKS, GL_MAX_COMBINED_VERTEX_UNIFORM_COMPONENTS, GL_MAX_CUBE_MAP_TEXTURE_SIZE, GL_MAX_DRAW_BUFFERS, GL_MAX_DRAW_BUFFERS_WEBGL, GL_MAX_ELEMENTS_INDICES, GL_MAX_ELEMENTS_VERTICES, GL_MAX_ELEMENT_INDEX, GL_MAX_EXT, GL_MAX_FRAGMENT_INPUT_COMPONENTS, GL_MAX_FRAGMENT_UNIFORM_BLOCKS, GL_MAX_FRAGMENT_UNIFORM_COMPONENTS, GL_MAX_FRAGMENT_UNIFORM_VECTORS, GL_MAX_PROGRAM_TEXEL_OFFSET, GL_MAX_RENDERBUFFER_SIZE, GL_MAX_SAMPLES, GL_MAX_SERVER_WAIT_TIMEOUT, GL_MAX_TEXTURE_IMAGE_UNITS, GL_MAX_TEXTURE_LOD_BIAS, GL_MAX_TEXTURE_MAX_ANISOTROPY_EXT, GL_MAX_TEXTURE_SIZE, GL_MAX_TRANSFORM_FEEDBACK_INTERLEAVED_COMPONENTS, GL_MAX_TRANSFORM_FEEDBACK_SEPARATE_ATTRIBS, GL_MAX_TRANSFORM_FEEDBACK_SEPARATE_COMPONENTS, GL_MAX_UNIFORM_BLOCK_SIZE, GL_MAX_UNIFORM_BUFFER_BINDINGS, GL_MAX_VARYING_COMPONENTS, GL_MAX_VARYING_VECTORS, GL_MAX_VERTEX_ATTRIBS, GL_MAX_VERTEX_OUTPUT_COMPONENTS, GL_MAX_VERTEX_TEXTURE_IMAGE_UNITS, GL_MAX_VERTEX_UNIFORM_BLOCKS, GL_MAX_VERTEX_UNIFORM_COMPONENTS, GL_MAX_VERTEX_UNIFORM_VECTORS, GL_MAX_VIEWPORT_DIMS, GL_MEDIUM_FLOAT, GL_MEDIUM_INT, GL_MIN, GL_MIN_EXT, GL_MIN_PROGRAM_TEXEL_OFFSET, GL_MIRRORED_REPEAT, GL_NEAREST, GL_NEAREST_MIPMAP_LINEAR, GL_NEAREST_MIPMAP_NEAREST, GL_NEVER, GL_NICEST, GL_NONE, GL_NOTEQUAL, GL_NO_ERROR, GL_OBJECT_TYPE, GL_ONE, GL_ONE_MINUS_CONSTANT_ALPHA, GL_ONE_MINUS_CONSTANT_COLOR, GL_ONE_MINUS_DST_ALPHA, GL_ONE_MINUS_DST_COLOR, GL_ONE_MINUS_SRC_ALPHA, GL_ONE_MINUS_SRC_COLOR, GL_OUT_OF_MEMORY, GL_PACK_ALIGNMENT, GL_PACK_ROW_LENGTH, GL_PACK_SKIP_PIXELS, GL_PACK_SKIP_ROWS, GL_PIXEL_PACK_BUFFER, GL_PIXEL_PACK_BUFFER_BINDING, GL_PIXEL_UNPACK_BUFFER, GL_PIXEL_UNPACK_BUFFER_BINDING, GL_POINTS, GL_POLYGON_OFFSET_FACTOR, GL_POLYGON_OFFSET_FILL, GL_POLYGON_OFFSET_UNITS, GL_QUERY_COUNTER_BITS_EXT, GL_QUERY_RESULT, GL_QUERY_RESULT_AVAILABLE, GL_QUERY_RESULT_AVAILABLE_EXT, GL_QUERY_RESULT_EXT, GL_R11F_G11F_B10F, GL_R16F, GL_R16I, GL_R16UI, GL_R32F, GL_R32I, GL_R32UI, GL_R8, GL_R8I, GL_R8UI, GL_R8_SNORM, GL_RASTERIZER_DISCARD, GL_READ_BUFFER, GL_READ_FRAMEBUFFER, GL_READ_FRAMEBUFFER_BINDING, GL_RED, GL_RED_BITS, GL_RED_INTEGER, GL_RENDERBUFFER, GL_RENDERBUFFER_ALPHA_SIZE, GL_RENDERBUFFER_BINDING, GL_RENDERBUFFER_BLUE_SIZE, GL_RENDERBUFFER_DEPTH_SIZE, GL_RENDERBUFFER_GREEN_SIZE, GL_RENDERBUFFER_HEIGHT, GL_RENDERBUFFER_INTERNAL_FORMAT, GL_RENDERBUFFER_RED_SIZE, GL_RENDERBUFFER_SAMPLES, GL_RENDERBUFFER_STENCIL_SIZE, GL_RENDERBUFFER_WIDTH, GL_RENDERER, GL_REPEAT, GL_REPLACE, GL_RG, GL_RG16F, GL_RG16I, GL_RG16UI, GL_RG32F, GL_RG32I, GL_RG32UI, GL_RG8, GL_RG8I, GL_RG8UI, GL_RG8_SNORM, GL_RGB, GL_RGB10_A2, GL_RGB10_A2UI, GL_RGB16F, GL_RGB16I, GL_RGB16UI, GL_RGB32F, GL_RGB32F_EXT, GL_RGB32I, GL_RGB32UI, GL_RGB565, GL_RGB5_A1, GL_RGB8, GL_RGB8I, GL_RGB8UI, GL_RGB8_SNORM, GL_RGB9_E5, GL_RGBA, GL_RGBA16F, GL_RGBA16I, GL_RGBA16UI, GL_RGBA32F, GL_RGBA32F_EXT, GL_RGBA32I, GL_RGBA32UI, GL_RGBA4, GL_RGBA8, GL_RGBA8I, GL_RGBA8UI, GL_RGBA8_SNORM, GL_RGBA_INTEGER, GL_RGB_INTEGER, GL_RG_INTEGER, GL_SAMPLER_2D, GL_SAMPLER_2D_ARRAY, GL_SAMPLER_2D_ARRAY_SHADOW, GL_SAMPLER_2D_SHADOW, GL_SAMPLER_3D, GL_SAMPLER_BINDING, GL_SAMPLER_CUBE, GL_SAMPLER_CUBE_SHADOW, GL_SAMPLES, GL_SAMPLE_ALPHA_TO_COVERAGE, GL_SAMPLE_BUFFERS, GL_SAMPLE_COVERAGE, GL_SAMPLE_COVERAGE_INVERT, GL_SAMPLE_COVERAGE_VALUE, GL_SCISSOR_BOX, GL_SCISSOR_TEST, GL_SEPARATE_ATTRIBS, GL_SHADER_TYPE, GL_SHADING_LANGUAGE_VERSION, GL_SHORT, GL_SIGNALED, GL_SIGNED_NORMALIZED, GL_SRC_ALPHA, GL_SRC_ALPHA_SATURATE, GL_SRC_COLOR, GL_SRGB, GL_SRGB8, GL_SRGB8_ALPHA8, GL_SRGB8_ALPHA8_EXT, GL_SRGB_ALPHA_EXT, GL_SRGB_EXT, GL_STATIC_COPY, GL_STATIC_DRAW, GL_STATIC_READ, GL_STENCIL, GL_STENCIL_ATTACHMENT, GL_STENCIL_BACK_FAIL, GL_STENCIL_BACK_FUNC, GL_STENCIL_BACK_PASS_DEPTH_FAIL, GL_STENCIL_BACK_PASS_DEPTH_PASS, GL_STENCIL_BACK_REF, GL_STENCIL_BACK_VALUE_MASK, GL_STENCIL_BACK_WRITEMASK, GL_STENCIL_BITS, GL_STENCIL_BUFFER_BIT, GL_STENCIL_CLEAR_VALUE, GL_STENCIL_FAIL, GL_STENCIL_FUNC, GL_STENCIL_INDEX, GL_STENCIL_INDEX8, GL_STENCIL_PASS_DEPTH_FAIL, GL_STENCIL_PASS_DEPTH_PASS, GL_STENCIL_REF, GL_STENCIL_TEST, GL_STENCIL_VALUE_MASK, GL_STENCIL_WRITEMASK, GL_STREAM_COPY, GL_STREAM_DRAW, GL_STREAM_READ, GL_SUBPIXEL_BITS, GL_SYNC_CONDITION, GL_SYNC_FENCE, GL_SYNC_FLAGS, GL_SYNC_FLUSH_COMMANDS_BIT, GL_SYNC_GPU_COMMANDS_COMPLETE, GL_SYNC_STATUS, GL_TEXTURE, GL_TEXTURE0, GL_TEXTURE1, GL_TEXTURE10, GL_TEXTURE11, GL_TEXTURE12, GL_TEXTURE13, GL_TEXTURE14, GL_TEXTURE15, GL_TEXTURE16, GL_TEXTURE17, GL_TEXTURE18, GL_TEXTURE19, GL_TEXTURE2, GL_TEXTURE20, GL_TEXTURE21, GL_TEXTURE22, GL_TEXTURE23, GL_TEXTURE24, GL_TEXTURE25, GL_TEXTURE26, GL_TEXTURE27, GL_TEXTURE28, GL_TEXTURE29, GL_TEXTURE3, GL_TEXTURE30, GL_TEXTURE31, GL_TEXTURE4, GL_TEXTURE5, GL_TEXTURE6, GL_TEXTURE7, GL_TEXTURE8, GL_TEXTURE9, GL_TEXTURE_2D, GL_TEXTURE_2D_ARRAY, GL_TEXTURE_3D, GL_TEXTURE_BASE_LEVEL, GL_TEXTURE_BINDING_2D, GL_TEXTURE_BINDING_2D_ARRAY, GL_TEXTURE_BINDING_3D, GL_TEXTURE_BINDING_CUBE_MAP, GL_TEXTURE_COMPARE_FUNC, GL_TEXTURE_COMPARE_MODE, GL_TEXTURE_CUBE_MAP, GL_TEXTURE_CUBE_MAP_NEGATIVE_X, GL_TEXTURE_CUBE_MAP_NEGATIVE_Y, GL_TEXTURE_CUBE_MAP_NEGATIVE_Z, GL_TEXTURE_CUBE_MAP_POSITIVE_X, GL_TEXTURE_CUBE_MAP_POSITIVE_Y, GL_TEXTURE_CUBE_MAP_POSITIVE_Z, GL_TEXTURE_IMMUTABLE_FORMAT, GL_TEXTURE_IMMUTABLE_LEVELS, GL_TEXTURE_MAG_FILTER, GL_TEXTURE_MAX_ANISOTROPY_EXT, GL_TEXTURE_MAX_LEVEL, GL_TEXTURE_MAX_LOD, GL_TEXTURE_MIN_FILTER, GL_TEXTURE_MIN_LOD, GL_TEXTURE_WRAP_R, GL_TEXTURE_WRAP_S, GL_TEXTURE_WRAP_T, GL_TIMEOUT_EXPIRED, GL_TIMEOUT_IGNORED, GL_TIMESTAMP_EXT, GL_TIME_ELAPSED_EXT, GL_TRANSFORM_FEEDBACK, GL_TRANSFORM_FEEDBACK_ACTIVE, GL_TRANSFORM_FEEDBACK_BINDING, GL_TRANSFORM_FEEDBACK_BUFFER, GL_TRANSFORM_FEEDBACK_BUFFER_BINDING, GL_TRANSFORM_FEEDBACK_BUFFER_MODE, GL_TRANSFORM_FEEDBACK_BUFFER_SIZE, GL_TRANSFORM_FEEDBACK_BUFFER_START, GL_TRANSFORM_FEEDBACK_PAUSED, GL_TRANSFORM_FEEDBACK_PRIMITIVES_WRITTEN, GL_TRANSFORM_FEEDBACK_VARYINGS, GL_TRIANGLES, GL_TRIANGLE_FAN, GL_TRIANGLE_STRIP, GL_UNIFORM_ARRAY_STRIDE, GL_UNIFORM_BLOCK_ACTIVE_UNIFORMS, GL_UNIFORM_BLOCK_ACTIVE_UNIFORM_INDICES, GL_UNIFORM_BLOCK_BINDING, GL_UNIFORM_BLOCK_DATA_SIZE, GL_UNIFORM_BLOCK_INDEX, GL_UNIFORM_BLOCK_REFERENCED_BY_FRAGMENT_SHADER, GL_UNIFORM_BLOCK_REFERENCED_BY_VERTEX_SHADER, GL_UNIFORM_BUFFER, GL_UNIFORM_BUFFER_BINDING, GL_UNIFORM_BUFFER_OFFSET_ALIGNMENT, GL_UNIFORM_BUFFER_SIZE, GL_UNIFORM_BUFFER_START, GL_UNIFORM_IS_ROW_MAJOR, GL_UNIFORM_MATRIX_STRIDE, GL_UNIFORM_OFFSET, GL_UNIFORM_SIZE, GL_UNIFORM_TYPE, GL_UNMASKED_RENDERER_WEBGL, GL_UNMASKED_VENDOR_WEBGL, GL_UNPACK_ALIGNMENT, GL_UNPACK_COLORSPACE_CONVERSION_WEBGL, GL_UNPACK_FLIP_Y_WEBGL, GL_UNPACK_IMAGE_HEIGHT, GL_UNPACK_PREMULTIPLY_ALPHA_WEBGL, GL_UNPACK_ROW_LENGTH, GL_UNPACK_SKIP_IMAGES, GL_UNPACK_SKIP_PIXELS, GL_UNPACK_SKIP_ROWS, GL_UNSIGNALED, GL_UNSIGNED_BYTE, GL_UNSIGNED_INT, GL_UNSIGNED_INT_10F_11F_11F_REV, GL_UNSIGNED_INT_24_8, GL_UNSIGNED_INT_24_8_WEBGL, GL_UNSIGNED_INT_2_10_10_10_REV, GL_UNSIGNED_INT_5_9_9_9_REV, GL_UNSIGNED_INT_SAMPLER_2D, GL_UNSIGNED_INT_SAMPLER_2D_ARRAY, GL_UNSIGNED_INT_SAMPLER_3D, GL_UNSIGNED_INT_SAMPLER_CUBE, GL_UNSIGNED_INT_VEC2, GL_UNSIGNED_INT_VEC3, GL_UNSIGNED_INT_VEC4, GL_UNSIGNED_NORMALIZED, GL_UNSIGNED_NORMALIZED_EXT, GL_UNSIGNED_SHORT, GL_UNSIGNED_SHORT_4_4_4_4, GL_UNSIGNED_SHORT_5_5_5_1, GL_UNSIGNED_SHORT_5_6_5, GL_VALIDATE_STATUS, GL_VENDOR, GL_VERSION, GL_VERTEX_ARRAY_BINDING, GL_VERTEX_ARRAY_BINDING_OES, GL_VERTEX_ATTRIB_ARRAY_BUFFER_BINDING, GL_VERTEX_ATTRIB_ARRAY_DIVISOR, GL_VERTEX_ATTRIB_ARRAY_DIVISOR_ANGLE, GL_VERTEX_ATTRIB_ARRAY_ENABLED, GL_VERTEX_ATTRIB_ARRAY_INTEGER, GL_VERTEX_ATTRIB_ARRAY_NORMALIZED, GL_VERTEX_ATTRIB_ARRAY_POINTER, GL_VERTEX_ATTRIB_ARRAY_SIZE, GL_VERTEX_ATTRIB_ARRAY_STRIDE, GL_VERTEX_ATTRIB_ARRAY_TYPE, GL_VERTEX_SHADER, GL_VIEWPORT, GL_WAIT_FAILED, GL_ZERO };\n", "export const isSSR = typeof window === 'undefined';\n", "import { isSSR } from './ssr';\n\nexport const deviceInfo = (() => {\n  if (isSSR) {\n    return;\n  }\n\n  const { userAgent, platform, maxTouchPoints } = window.navigator;\n\n  const isIOS = /(iphone|ipod|ipad)/i.test(userAgent);\n\n  // Workaround for ipadOS, force detection as tablet\n  // SEE: https://github.com/lancedikson/bowser/issues/329\n  // SEE: https://stackoverflow.com/questions/58019463/how-to-detect-device-name-in-safari-on-ios-13-while-it-doesnt-show-the-correct\n  const isIpad =\n    platform === 'iPad' ||\n    // @ts-expect-error window.MSStream is non standard\n    (platform === 'MacIntel' && maxTouchPoints > 0 && !window.MSStream);\n\n  const isAndroid = /android/i.test(userAgent);\n\n  return {\n    isIpad,\n    isMobile: isAndroid || isIOS || isIpad,\n    isSafari12: /Version\\/12.+Safari/.test(userAgent),\n    isFirefox: /Firefox/.test(userAgent)\n  };\n})();\n", "// Vendor\nimport {\n  G<PERSON>_ARRAY_BUFFER,\n  GL_COLOR_BUFFER_BIT,\n  GL_FLOAT,\n  GL_FRAGMENT_SHADER,\n  GL_RGBA,\n  GL_STATIC_DRAW,\n  GL_TRIANGLES,\n  GL_UNSIGNED_BYTE,\n  GL_VERTEX_SHADER,\n} from 'webgl-constants';\n\n// Internal\nimport { deviceInfo } from './deviceInfo';\n\nconst debug = false ? console.warn : undefined;\n\nexport function deobfuscateAppleGPU(\n  gl: WebGLRenderingContext,\n  renderer: string,\n  isMobileTier: boolean\n) {\n  if (!isMobileTier) {\n    debug?.('Safari 14+ obfuscates its GPU type and version, using fallback');\n    return [renderer];\n  }\n  const pixelId = calculateMagicPixelId(gl);\n  const codeA = '801621810' as const;\n  const codeB = '8016218135' as const;\n  const codeC = '80162181161' as const;\n  const codeFB = '80162181255';\n\n  // All chipsets that support at least iOS 12:\n  const possibleChipsets: [\n    string,\n    typeof codeA | typeof codeB | typeof codeC,\n    number,\n  ][] = deviceInfo?.isIpad\n    ? [\n        // ['a4', 5], // ipad 1st gen\n        // ['a5', 9], // ipad 2 / ipad mini 1st gen\n        // ['a5x', 9], // ipad 3rd gen\n        // ['a6x', 10], // ipad 4th gen\n        ['a7', codeC, 12], // ipad air / ipad mini 2 / ipad mini 3\n        ['a8', codeB, 15], // pad mini 4\n        ['a8x', codeB, 15], // ipad air 2\n        ['a9', codeB, 15], // ipad 5th gen\n        ['a9x', codeB, 15], // pro 9.7 2016 / pro 12.9 2015\n        ['a10', codeB, 15], // ipad 7th gen / ipad 6th gen\n        ['a10x', codeB, 15], // pro 10.5 2017 / pro 12.9 2nd gen, 2017\n        ['a12', codeA, 15], // ipad 8th gen / ipad air 3rd gen / ipad mini 5th gen\n        ['a12x', codeA, 15], // ipad pro 11 3st gen / ipad pro 12.9 3rd gen\n        ['a12z', codeA, 15], // ipad pro 11 4nd gen / ipad pro 12.9 4th gen\n        ['a14', codeA, 15], // ipad air 4th gen\n        ['a15', codeA, 15], // ipad mini 6th gen / ipad 10th gen\n        ['m1', codeA, 15], // ipad pro 11 5nd gen / ipad pro 12.9 5th gen / ipad air 5th gen\n        ['m2', codeA, 15], // ipad pro 11 6nd gen / ipad pro 12.9 6th gen\n      ]\n    : [\n        // ['a4', 7], // 4 / ipod touch 4th gen\n        // ['a5', 9], // 4S / ipod touch 5th gen\n        // ['a6', 10], // 5 / 5C\n        ['a7', codeC, 12], // 5S\n        ['a8', codeB, 12], // 6 / 6 plus / ipod touch 6th gen\n        ['a9', codeB, 15], // 6s / 6s plus/ se 1st gen\n        ['a10', codeB, 15], // 7 / 7 plus / iPod Touch 7th gen\n        ['a11', codeA, 15], // 8 / 8 plus / X\n        ['a12', codeA, 15], // XS / XS Max / XR\n        ['a13', codeA, 15], // 11 / 11 pro / 11 pro max / se 2nd gen\n        ['a14', codeA, 15], // 12 / 12 mini / 12 pro / 12 pro max\n        ['a15', codeA, 15], // 13 / 13 mini / 13 pro / 13 pro max / se 3rd gen / 14 / 14 plus\n        ['a16', codeA, 15], // 14 pro / 14 pro max / 15 / 15 plus\n        ['a17', codeA, 15], // 15 pro / 15 pro max\n      ];\n  let chipsets: typeof possibleChipsets;\n\n  // In iOS 14.x Apple started normalizing the outcome of this hack,\n  // we use this fact to limit the list to devices that support ios 14+\n  if (pixelId === codeFB) {\n    chipsets = possibleChipsets.filter(([, , iosVersion]) => iosVersion >= 14);\n  } else {\n    chipsets = possibleChipsets.filter(([, id]) => id === pixelId);\n    // If nothing was found to match the pixel id, include all chipsets:\n    if (!chipsets.length) {\n      chipsets = possibleChipsets;\n    }\n  }\n  const renderers = chipsets.map(([gpu]) => `apple ${gpu} gpu`);\n  debug?.(\n    `iOS 12.2+ obfuscates its GPU type and version, using closest matches: ${JSON.stringify(\n      renderers\n    )}`\n  );\n  return renderers;\n}\n\n// Apple GPU (iOS 12.2+, Safari 14+)\n// SEE: https://github.com/pmndrs/detect-gpu/issues/7\n// CREDIT: https://medium.com/@Samsy/detecting-apple-a10-iphone-7-to-a11-iphone-8-and-b019b8f0eb87\n// CREDIT: https://github.com/Samsy/appleGPUDetection/blob/master/index.js\nfunction calculateMagicPixelId(gl: WebGLRenderingContext) {\n  const vertexShaderSource = /* glsl */ `\n    precision highp float;\n    attribute vec3 aPosition;\n    varying float vvv;\n    void main() {\n      vvv = 0.31622776601683794;\n      gl_Position = vec4(aPosition, 1.0);\n    }\n  `;\n\n  const fragmentShaderSource = /* glsl */ `\n    precision highp float;\n    varying float vvv;\n    void main() {\n      vec4 enc = vec4(1.0, 255.0, 65025.0, 16581375.0) * vvv;\n      enc = fract(enc);\n      enc -= enc.yzww * vec4(1.0 / 255.0, 1.0 / 255.0, 1.0 / 255.0, 0.0);\n      gl_FragColor = enc;\n    }\n  `;\n\n  const vertexShader = gl.createShader(GL_VERTEX_SHADER);\n  const fragmentShader = gl.createShader(GL_FRAGMENT_SHADER);\n  const program = gl.createProgram();\n  if (!(fragmentShader && vertexShader && program)) return;\n  gl.shaderSource(vertexShader, vertexShaderSource);\n  gl.shaderSource(fragmentShader, fragmentShaderSource);\n  gl.compileShader(vertexShader);\n  gl.compileShader(fragmentShader);\n  gl.attachShader(program, vertexShader);\n  gl.attachShader(program, fragmentShader);\n\n  gl.linkProgram(program);\n\n  gl.detachShader(program, vertexShader);\n  gl.detachShader(program, fragmentShader);\n  gl.deleteShader(vertexShader);\n  gl.deleteShader(fragmentShader);\n\n  gl.useProgram(program);\n\n  const vertexArray = gl.createBuffer();\n  gl.bindBuffer(GL_ARRAY_BUFFER, vertexArray);\n  gl.bufferData(\n    GL_ARRAY_BUFFER,\n    new Float32Array([-1, -1, 0, 3, -1, 0, -1, 3, 0]),\n    GL_STATIC_DRAW\n  );\n\n  const aPosition = gl.getAttribLocation(program, 'aPosition');\n  gl.vertexAttribPointer(aPosition, 3, GL_FLOAT, false, 0, 0);\n  gl.enableVertexAttribArray(aPosition);\n\n  gl.clearColor(1, 1, 1, 1);\n  gl.clear(GL_COLOR_BUFFER_BIT);\n  gl.viewport(0, 0, 1, 1);\n  gl.drawArrays(GL_TRIANGLES, 0, 3);\n\n  const pixels = new Uint8Array(4);\n  gl.readPixels(0, 0, 1, 1, GL_RGBA, GL_UNSIGNED_BYTE, pixels);\n\n  gl.deleteProgram(program);\n  gl.deleteBuffer(vertexArray);\n  return pixels.join('');\n}\n", "export class OutdatedBenchmarksError extends Error {\n  constructor(message?: string) {\n    super(message); // 'Error' breaks prototype chain here\n    Object.setPrototypeOf(this, new.target.prototype); // restore prototype chain\n  }\n}\n", "// Caches\nconst array: number[] = [];\nconst charCodeCache: number[] = [];\n\n// Compute the difference (distance) between two strings\n// SEE: https://en.wikipedia.org/wiki/Levenshtein_distance\n// CREDIT: https://github.com/sindresorhus/leven (version 3.1.0)\nexport function getLevenshteinDistance(left: string, right: string): number {\n  if (left === right) {\n    return 0;\n  }\n\n  const swap = left;\n\n  // Swapping the strings if `a` is longer than `b` so we know which one is the\n  // shortest & which one is the longest\n  if (left.length > right.length) {\n    left = right;\n    right = swap;\n  }\n\n  let leftLength = left.length;\n  let rightLength = right.length;\n\n  // Performing suffix trimming:\n  // We can linearly drop suffix common to both strings since they\n  // don't increase distance at all\n  // Note: `~-` is the bitwise way to perform a `- 1` operation\n  while (leftLength > 0 &&\n    left.charCodeAt(~-leftLength) === right.charCodeAt(~-rightLength)) {\n    leftLength--;\n    rightLength--;\n  }\n\n  // Performing prefix trimming\n  // We can linearly drop prefix common to both strings since they\n  // don't increase distance at all\n  let start = 0;\n\n  while (start < leftLength &&\n    left.charCodeAt(start) === right.charCodeAt(start)) {\n    start++;\n  }\n\n  leftLength -= start;\n  rightLength -= start;\n\n  if (leftLength === 0) {\n    return rightLength;\n  }\n\n  let bCharCode;\n  let result = 0;\n  let temp;\n  let temp2;\n  let i = 0;\n  let j = 0;\n\n  while (i < leftLength) {\n    charCodeCache[i] = left.charCodeAt(start + i);\n    array[i] = ++i;\n  }\n\n  while (j < rightLength) {\n    bCharCode = right.charCodeAt(start + j);\n    temp = j++;\n    result = j;\n\n    for (i = 0; i < leftLength; i++) {\n      temp2 = bCharCode === charCodeCache[i] ? temp : temp + 1;\n      temp = array[i];\n      // eslint-disable-next-line no-multi-assign\n      result = array[i] =\n        temp > result\n          ? temp2 > result\n            ? result + 1\n            : temp2\n          : temp2 > temp\n            ? temp + 1\n            : temp2;\n    }\n  }\n\n  return result;\n}\n\nexport function tokenizeForLevenshteinDistance(str: string): string {\n  return str\n    .split(/[.,()\\[\\]/\\s]/g)\n    .sort()\n    // Remove duplicates\n    .filter((item, pos, arr) => pos === 0 || item !== arr[pos - 1])\n    .join(' ');\n}\n", "export function isDefined<T>(val: T | undefined | null | void): val is T {\n  return val !== undefined && val !== null;\n}\n", "// Data\nimport { version } from '../package.json';\n\n// Internal\nimport { BLOCKLISTED_GPUS } from './internal/blocklistedGPUS';\nimport { cleanRenderer } from './internal/cleanRenderer';\nimport { deobfuscateRenderer } from './internal/deobfuscateRenderer';\nimport { deviceInfo } from './internal/deviceInfo';\nimport { OutdatedBenchmarksError } from './internal/error';\nimport { getGPUVersion } from './internal/getGPUVersion';\nimport {\n  getLevenshteinDistance,\n  tokenizeForLevenshteinDistance,\n} from './internal/getLevenshteinDistance';\nimport { getWebGLContext } from './internal/getWebGLContext';\nimport { isSSR } from './internal/ssr';\nimport { isDefined } from './internal/util';\n\n// Types\nexport interface GetGPUTier {\n  /**\n   * URL of directory where benchmark data is hosted.\n   *\n   * @default https://unpkg.com/detect-gpu@{version}/dist/benchmarks\n   */\n  benchmarksURL?: string;\n  /**\n   * Optionally pass in a WebGL context to avoid creating a temporary one\n   * internally.\n   */\n  glContext?: WebGLRenderingContext | WebGL2RenderingContext;\n  /**\n   * Whether to fail if the system performance is low or if no hardware GPU is\n   * available.\n   *\n   * @default false\n   */\n  failIfMajorPerformanceCaveat?: boolean;\n  /**\n   * Framerate per tier for mobile devices.\n   *\n   * @defaultValue [0, 15, 30, 60]\n   */\n  mobileTiers?: number[];\n  /**\n   * Framerate per tier for desktop devices.\n   *\n   * @defaultValue [0, 15, 30, 60]\n   */\n  desktopTiers?: number[];\n  /**\n   * Optionally override specific parameters. Used mainly for testing.\n   */\n  override?: {\n    renderer?: string;\n    /**\n     * Override whether device is an iPad.\n     */\n    isIpad?: boolean;\n    /**\n     * Override whether device is a mobile device.\n     */\n    isMobile?: boolean;\n    /**\n     * Override device screen size.\n     */\n    screenSize?: { width: number; height: number };\n    /**\n     * Override how benchmark data is loaded\n     */\n    loadBenchmarks?: (file: string) => Promise<ModelEntry[]>;\n  };\n}\n\nexport type TierType =\n  | 'SSR'\n  | 'WEBGL_UNSUPPORTED'\n  | 'BLOCKLISTED'\n  | 'FALLBACK'\n  | 'BENCHMARK';\n\nexport type TierResult = {\n  tier: number;\n  type: TierType;\n  isMobile?: boolean;\n  fps?: number;\n  gpu?: string;\n  device?: string;\n};\n\nexport type ModelEntryScreen = [number, number, number, string | undefined];\n\nexport type ModelEntry = [string, string, string, 0 | 1, ModelEntryScreen[]];\n\nconst debug = false ? console.log : undefined;\n\nexport const getGPUTier = async ({\n  mobileTiers = [0, 15, 30, 60],\n  desktopTiers = [0, 15, 30, 60],\n  override = {},\n  glContext,\n  failIfMajorPerformanceCaveat = false,\n  benchmarksURL = `https://unpkg.com/detect-gpu@${version}/dist/benchmarks`,\n}: GetGPUTier = {}): Promise<TierResult> => {\n  const queryCache: { [k: string]: Promise<ModelEntry[]> } = {};\n  if (isSSR) {\n    return {\n      tier: 0,\n      type: 'SSR',\n    };\n  }\n\n  const {\n    isIpad = !!deviceInfo?.isIpad,\n    isMobile = !!deviceInfo?.isMobile,\n    screenSize = window.screen,\n    loadBenchmarks = async (file: string) => {\n      const data: ModelEntry[] = await fetch(`${benchmarksURL}/${file}`).then(\n        (response) => response.json()\n      );\n\n      // Remove version tag and check version is supported\n      const version = parseInt(\n        (data.shift() as unknown as string).split('.')[0],\n        10\n      );\n      if (version < 4) {\n        throw new OutdatedBenchmarksError(\n          'Detect GPU benchmark data is out of date. Please update to version 4x'\n        );\n      }\n      return data;\n    },\n  } = override;\n  let { renderer } = override;\n  const getGpuType = (renderer: string) => {\n    const types = isMobile\n      ? ([\n          'adreno',\n          'apple',\n          'mali-t',\n          'mali',\n          'nvidia',\n          'powervr',\n          'samsung',\n        ] as const)\n      : ([\n          'intel',\n          'apple',\n          'amd',\n          'radeon',\n          'nvidia',\n          'geforce',\n          'adreno',\n        ] as const);\n    for (const type of types) {\n      if (renderer.includes(type)) {\n        return type;\n      }\n    }\n  };\n\n  async function queryBenchmarks(renderer: string) {\n    const type = getGpuType(renderer);\n    if (!type) {\n      return;\n    }\n\n    debug?.('queryBenchmarks - found type:', { type });\n\n    const benchmarkFile = `${isMobile ? 'm' : 'd'}-${type}${\n      isIpad ? '-ipad' : ''\n    }.json`;\n\n    const benchmark = (queryCache[benchmarkFile] =\n      queryCache[benchmarkFile] ?? loadBenchmarks(benchmarkFile));\n    let benchmarks: ModelEntry[];\n    try {\n      benchmarks = await benchmark;\n    } catch (error) {\n      if (error instanceof OutdatedBenchmarksError) {\n        throw error;\n      }\n      debug?.(\"queryBenchmarks - couldn't load benchmark:\", { error });\n      return;\n    }\n\n    const version = getGPUVersion(renderer);\n\n    let matched = benchmarks.filter(\n      ([, modelVersion]) => modelVersion === version\n    );\n\n    debug?.(\n      `found ${matched.length} matching entries using version '${version}':`,\n\n      matched.map(([model]) => model)\n    );\n\n    // If nothing matched, try comparing model names:\n    if (!matched.length) {\n      matched = benchmarks.filter(([model]) => model.includes(renderer));\n\n      debug?.(\n        `found ${matched.length} matching entries comparing model names`,\n        {\n          matched,\n        }\n      );\n    }\n\n    const matchCount = matched.length;\n\n    if (matchCount === 0) {\n      return;\n    }\n\n    const tokenizedRenderer = tokenizeForLevenshteinDistance(renderer);\n    // eslint-disable-next-line prefer-const\n    let [gpu, , , , fpsesByPixelCount] =\n      matchCount > 1\n        ? matched\n            .map(\n              (match) =>\n                [\n                  match,\n                  getLevenshteinDistance(tokenizedRenderer, match[2]),\n                ] as const\n            )\n            .sort(([, a], [, b]) => a - b)[0][0]\n        : matched[0];\n\n    debug?.(\n      `${renderer} matched closest to ${gpu} with the following screen sizes`,\n      JSON.stringify(fpsesByPixelCount)\n    );\n\n    let minDistance = Number.MAX_VALUE;\n    let closest: ModelEntryScreen | undefined;\n    const { devicePixelRatio } = window;\n    const pixelCount =\n      screenSize.width *\n      devicePixelRatio *\n      screenSize.height *\n      devicePixelRatio;\n\n    for (const match of fpsesByPixelCount) {\n      const [width, height] = match;\n      const entryPixelCount = width * height;\n      const distance = Math.abs(pixelCount - entryPixelCount);\n\n      if (distance < minDistance) {\n        minDistance = distance;\n        closest = match;\n      }\n    }\n\n    if (!closest) {\n      return;\n    }\n\n    // eslint-disable-next-line @typescript-eslint/no-non-null-assertion\n    const [, , fps, device] = closest!;\n\n    return [minDistance, fps, gpu, device] as const;\n  }\n\n  const toResult = (\n    tier: number,\n    type: TierType,\n    gpu?: string,\n    fps?: number,\n    device?: string\n  ) => ({\n    device,\n    fps,\n    gpu,\n    isMobile,\n    tier,\n    type,\n  });\n\n  let renderers: string[];\n  let rawRenderer = '';\n\n  if (!renderer) {\n    const gl =\n      glContext ||\n      getWebGLContext(deviceInfo?.isSafari12, failIfMajorPerformanceCaveat);\n\n    if (!gl) {\n      return toResult(0, 'WEBGL_UNSUPPORTED');\n    }\n\n    const debugRendererInfo = deviceInfo?.isFirefox\n      ? null\n      : gl.getExtension('WEBGL_debug_renderer_info');\n\n    renderer = debugRendererInfo\n      ? gl.getParameter(debugRendererInfo.UNMASKED_RENDERER_WEBGL)\n      : gl.getParameter(gl.RENDERER);\n\n    if (!renderer) {\n      return toResult(1, 'FALLBACK');\n    }\n\n    rawRenderer = renderer;\n    renderer = cleanRenderer(renderer);\n    renderers = deobfuscateRenderer(gl, renderer, isMobile);\n  } else {\n    renderer = cleanRenderer(renderer);\n    renderers = [renderer];\n  }\n\n  const results = (await Promise.all(renderers.map(queryBenchmarks)))\n    .filter(isDefined)\n    .sort(([aDis = Number.MAX_VALUE, aFps], [bDis = Number.MAX_VALUE, bFps]) =>\n      aDis === bDis ? aFps - bFps : aDis - bDis\n    );\n  if (!results.length) {\n    const blocklistedModel: string | undefined = BLOCKLISTED_GPUS.find(\n      // eslint-disable-next-line @typescript-eslint/no-non-null-assertion\n      (blocklistedModel) => renderer!.includes(blocklistedModel)\n    );\n    return blocklistedModel\n      ? toResult(0, 'BLOCKLISTED', blocklistedModel)\n      : toResult(1, 'FALLBACK', `${renderer} (${rawRenderer})`);\n  }\n\n  const [, fps, model, device] = results[0];\n\n  if (fps === -1) {\n    return toResult(0, 'BLOCKLISTED', model, fps, device);\n  }\n\n  const tiers = isMobile ? mobileTiers : desktopTiers;\n  let tier = 0;\n\n  for (let i = 0; i < tiers.length; i++) {\n    if (fps >= tiers[i]) {\n      tier = i;\n    }\n  }\n\n  return toResult(tier, 'BENCHMARK', model, fps, device);\n};\n", "export function getWebGLContext(isSafari12?: boolean,\n  failIfMajorPerformanceCaveat = false) {\n  const attributes: {\n    alpha: boolean;\n    antialias: boolean;\n    depth: boolean;\n    failIfMajorPerformanceCaveat: boolean;\n    powerPreference?: string;\n    stencil: boolean;\n  } = {\n    alpha: false,\n    antialias: false,\n    depth: false,\n    failIfMajorPerformanceCaveat,\n    powerPreference: 'high-performance',\n    stencil: false,\n  };\n\n  // Workaround for Safari 12, which otherwise crashes with powerPreference set\n  // to high-performance: https://github.com/pmndrs/detect-gpu/issues/5\n  if (isSafari12) {\n    delete attributes.powerPreference;\n  }\n\n  const canvas = window.document.createElement('canvas');\n\n  const gl = (canvas.getContext('webgl', attributes) ||\n    canvas.getContext(\n      'experimental-webgl',\n      attributes\n    )) as WebGLRenderingContext | null;\n\n  return gl ?? undefined;\n}\n", "// Internal\nimport { deobfuscateAppleGPU } from './deobfuscateAppleGPU';\n\nexport function deobfuscateRenderer(\n  gl: WebGLRenderingContext | WebGL2RenderingContext,\n  renderer: string,\n  isMobileTier: boolean\n) {\n  return renderer === 'apple gpu'\n    ? deobfuscateAppleGPU(gl, renderer, isMobileTier)\n    : [renderer];\n}\n", "export function getGPUVersion(model: string) {\n  model = model.replace(/\\([^)]+\\)/, '');\n\n  const matches =\n    // First set of digits\n    model.match(/\\d+/) ||\n    // If the renderer did not contain any numbers, match letters\n    model.match(/(\\W|^)([A-Za-z]{1,3})(\\W|$)/g);\n\n  // Remove any non-word characters and also remove 'amd' which could be matched\n  // in the clause above\n  return matches?.join('').replace(/\\W|amd/g, '') ?? '';\n}\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;AAGO,MAAMA,CAAA,GAAmB,CAC9B,gBACA,gBACA,oBACA,mBACA,mBACA,gBACA,mBACA,iBACA,oBACA,iBACA,qBACA,kBACA,kBACA,mBACA,mBACA,sBACA,aACA,aACA,qBACA,mBACA,iBACA,aACA,UACA,UACA,UACA,UACA,kBACA,eACA,eACA,kBACA,kBACA,kBACA,kBACA,kBACA,kBACA,kBACA,kBACA,kBACA,kBACA,kBACA,mBACA,mBACA,mBACA,mBACA,mBACA,WACA;AChDI,SAAUC,EAAcC,CAAA;EAsB5B,OAnBAA,CAAA,GAAWA,CAAA,CACRC,WAAA,GAIAC,OAAA,CAAQ,8CAA8C,MAItDA,OAAA,CAAQ,gDAAgD,IAMxDA,OAAA,CAAQ,sDAAsD;AAAA;ACUnE,MC9BaC,CAAA,GAA0B,sBAAXC,MAAA;ECEfC,CAAA,GAAa;IACxB,IAAIF,CAAA,EACF;IAGF;QAAMG,SAAA,EAAEN,CAAA;QAASO,QAAA,EAAET,CAAA;QAAQU,cAAA,EAAET;MAAA,IAAmBK,MAAA,CAAOK,SAAA;MAEjDJ,CAAA,GAAQ,sBAAsBK,IAAA,CAAKV,CAAA;MAKnCW,CAAA,GACS,WAAbb,CAAA,IAEc,eAAbA,CAAA,IAA2BC,CAAA,GAAiB,MAAMK,MAAA,CAAOQ,QAAA;IAI5D,OAAO;MACLC,MAAA,EAAAF,CAAA;MACAG,QAAA,EAJgB,WAAWJ,IAAA,CAAKV,CAAA,KAITK,CAAA,IAASM,CAAA;MAChCI,UAAA,EAAY,sBAAsBL,IAAA,CAAKV,CAAA;MACvCgB,SAAA,EAAW,UAAUN,IAAA,CAAKV,CAAA;IAAA;EAAA,GAvBJ;AAAA,SCgBVW,EACdX,CAAA,EACAF,CAAA,EACAC,CAAA;EAEA,KAAKA,CAAA,EAEH,OAAO,CAACD,CAAA;EAEV,MAAMK,CAAA,GA0ER,UAA+BH,CAAA;MAC7B,MAAMF,CAAA,GAAgC;QAUhCC,CAAA,GAAkC;QAWlCI,CAAA,GAAeH,CAAA,CAAGiB,YAAA,CHwlBD;QGvlBjBZ,CAAA,GAAiBL,CAAA,CAAGiB,YAAA,CHklBD;QGjlBnBN,CAAA,GAAUX,CAAA,CAAGkB,aAAA;MACnB,MAAMb,CAAA,IAAkBF,CAAA,IAAgBQ,CAAA,GAAU;MAClDX,CAAA,CAAGmB,YAAA,CAAahB,CAAA,EAAcL,CAAA,GAC9BE,CAAA,CAAGmB,YAAA,CAAad,CAAA,EAAgBN,CAAA,GAChCC,CAAA,CAAGoB,aAAA,CAAcjB,CAAA,GACjBH,CAAA,CAAGoB,aAAA,CAAcf,CAAA,GACjBL,CAAA,CAAGqB,YAAA,CAAaV,CAAA,EAASR,CAAA,GACzBH,CAAA,CAAGqB,YAAA,CAAaV,CAAA,EAASN,CAAA,GAEzBL,CAAA,CAAGsB,WAAA,CAAYX,CAAA,GAEfX,CAAA,CAAGuB,YAAA,CAAaZ,CAAA,EAASR,CAAA,GACzBH,CAAA,CAAGuB,YAAA,CAAaZ,CAAA,EAASN,CAAA,GACzBL,CAAA,CAAGwB,YAAA,CAAarB,CAAA,GAChBH,CAAA,CAAGwB,YAAA,CAAanB,CAAA,GAEhBL,CAAA,CAAGyB,UAAA,CAAWd,CAAA;MAEd,MAAMe,CAAA,GAAc1B,CAAA,CAAG2B,YAAA;MACvB3B,CAAA,CAAG4B,UAAA,CHgUmB,OGhUSF,CAAA,GAC/B1B,CAAA,CAAG6B,UAAA,CH+TmB,OG7TpB,IAAIC,YAAA,CAAa,EAAE,IAAI,GAAG,GAAG,IAAI,GAAG,IAAI,GAAG,GAAG,KH8S3B;MG1SrB,MAAMC,CAAA,GAAY/B,CAAA,CAAGgC,iBAAA,CAAkBrB,CAAA,EAAS;MAChDX,CAAA,CAAGiC,mBAAA,CAAoBF,CAAA,EAAW,GHygBnB,OGzgBgC,GAAO,GAAG,IACzD/B,CAAA,CAAGkC,uBAAA,CAAwBH,CAAA,GAE3B/B,CAAA,CAAGmC,UAAA,CAAW,GAAG,GAAG,GAAG,IACvBnC,CAAA,CAAGoC,KAAA,CH9HuB,QG+H1BpC,CAAA,CAAGqC,QAAA,CAAS,GAAG,GAAG,GAAG,IACrBrC,CAAA,CAAGsC,UAAA,CHrGgB,GGqGS,GAAG;MAE/B,MAAMC,CAAA,GAAS,IAAIC,UAAA,CAAW;MAK9B,OAJAxC,CAAA,CAAGyC,UAAA,CAAW,GAAG,GAAG,GAAG,GHihBT,MArCS,MG5e8BF,CAAA,GAErDvC,CAAA,CAAG0C,aAAA,CAAc/B,CAAA,GACjBX,CAAA,CAAG2C,YAAA,CAAajB,CAAA,GACTa,CAAA,CAAOK,IAAA,CAAK;IAAA,CA1IH,CAAsB5C,CAAA;IAChCW,CAAA,GAAQ;IACRe,CAAA,GAAQ;IACRK,CAAA,GAAQ;IAIRQ,CAAA,IAIA,QAAAlC,CAAA,QAAU,IAAVA,CAAA,CAAYQ,MAAA,IACd,CAKE,CAAC,MAAMkB,CAAA,EAAO,KACd,CAAC,MAAML,CAAA,EAAO,KACd,CAAC,OAAOA,CAAA,EAAO,KACf,CAAC,MAAMA,CAAA,EAAO,KACd,CAAC,OAAOA,CAAA,EAAO,KACf,CAAC,OAAOA,CAAA,EAAO,KACf,CAAC,QAAQA,CAAA,EAAO,KAChB,CAAC,OAAOf,CAAA,EAAO,KACf,CAAC,QAAQA,CAAA,EAAO,KAChB,CAAC,QAAQA,CAAA,EAAO,KAChB,CAAC,OAAOA,CAAA,EAAO,KACf,CAAC,OAAOA,CAAA,EAAO,KACf,CAAC,MAAMA,CAAA,EAAO,KACd,CAAC,MAAMA,CAAA,EAAO,OAEhB,CAIE,CAAC,MAAMoB,CAAA,EAAO,KACd,CAAC,MAAML,CAAA,EAAO,KACd,CAAC,MAAMA,CAAA,EAAO,KACd,CAAC,OAAOA,CAAA,EAAO,KACf,CAAC,OAAOf,CAAA,EAAO,KACf,CAAC,OAAOA,CAAA,EAAO,KACf,CAAC,OAAOA,CAAA,EAAO,KACf,CAAC,OAAOA,CAAA,EAAO,KACf,CAAC,OAAOA,CAAA,EAAO,KACf,CAAC,OAAOA,CAAA,EAAO,KACf,CAAC,OAAOA,CAAA,EAAO;EAErB,IAAIkC,CAAA;EA5CW,kBAgDX1C,CAAA,GACF0C,CAAA,GAAWN,CAAA,CAAiBO,MAAA,CAAO,KAAM9C,CAAA,MAAgBA,CAAA,IAAc,OAEvE6C,CAAA,GAAWN,CAAA,CAAiBO,MAAA,CAAO,IAAI9C,CAAA,MAAQA,CAAA,KAAOG,CAAA,GAEjD0C,CAAA,CAASE,MAAA,KACZF,CAAA,GAAWN,CAAA;EASf,OANkBM,CAAA,CAASG,GAAA,CAAI,EAAEhD,CAAA,MAAS,SAASA,CAAA;AAAA;ACxF/C,MAAO0B,CAAA,SAAgCuB,KAAA;EAC3CC,YAAYlD,CAAA;IACV,MAAMA,CAAA,GACNmD,MAAA,CAAOC,cAAA,CAAe,MAAAC,GAAA,CAAAC,MAAA,CAAiBC,SAAA;EAAA;AAAA;ACF3C,MAAMxB,CAAA,GAAkB;EAClBQ,CAAA,GAA0B;AAKhB,SAAAM,EAAuB7C,CAAA,EAAcF,CAAA;EACnD,IAAIE,CAAA,KAASF,CAAA,EACX,OAAO;EAGT,MAAMC,CAAA,GAAOC,CAAA;EAITA,CAAA,CAAK+C,MAAA,GAASjD,CAAA,CAAMiD,MAAA,KACtB/C,CAAA,GAAOF,CAAA,EACPA,CAAA,GAAQC,CAAA;EAGV,IAAII,CAAA,GAAaH,CAAA,CAAK+C,MAAA;IAClB1C,CAAA,GAAcP,CAAA,CAAMiD,MAAA;EAMxB,OAAO5C,CAAA,GAAa,KAClBH,CAAA,CAAKwD,UAAA,GAAarD,CAAA,MAAgBL,CAAA,CAAM0D,UAAA,GAAanD,CAAA,IACrDF,CAAA,IACAE,CAAA;EAMF,IAcIM,CAAA;IAdAe,CAAA,GAAQ;EAEZ,OAAOA,CAAA,GAAQvB,CAAA,IACbH,CAAA,CAAKwD,UAAA,CAAW9B,CAAA,MAAW5B,CAAA,CAAM0D,UAAA,CAAW9B,CAAA,IAC5CA,CAAA;EAMF,IAHAvB,CAAA,IAAcuB,CAAA,EACdrB,CAAA,IAAeqB,CAAA,EAEI,MAAfvB,CAAA,EACF,OAAOE,CAAA;EAIT,IACIwC,CAAA;IACAY,CAAA;IAFAC,CAAA,GAAS;IAGTC,CAAA,GAAI;IACJC,CAAA,GAAI;EAER,OAAOD,CAAA,GAAIxD,CAAA,GACToC,CAAA,CAAcoB,CAAA,IAAK3D,CAAA,CAAKwD,UAAA,CAAW9B,CAAA,GAAQiC,CAAA,GAC3C5B,CAAA,CAAM4B,CAAA,MAAOA,CAAA;EAGf,OAAOC,CAAA,GAAIvD,CAAA,GAKT,KAJAM,CAAA,GAAYb,CAAA,CAAM0D,UAAA,CAAW9B,CAAA,GAAQkC,CAAA,GACrCf,CAAA,GAAOe,CAAA,IACPF,CAAA,GAASE,CAAA,EAEJD,CAAA,GAAI,GAAGA,CAAA,GAAIxD,CAAA,EAAYwD,CAAA,IAC1BF,CAAA,GAAQ9C,CAAA,KAAc4B,CAAA,CAAcoB,CAAA,IAAKd,CAAA,GAAOA,CAAA,GAAO,GACvDA,CAAA,GAAOd,CAAA,CAAM4B,CAAA,GAEbD,CAAA,GAAS3B,CAAA,CAAM4B,CAAA,IACbd,CAAA,GAAOa,CAAA,GACHD,CAAA,GAAQC,CAAA,GACNA,CAAA,GAAS,IACTD,CAAA,GACFA,CAAA,GAAQZ,CAAA,GACNA,CAAA,GAAO,IACPY,CAAA;EAIZ,OAAOC,CAAA;AAAA;ACnFH,SAAUD,EAAazD,CAAA;EAC3B,OAAO,QAAAA,CAAA;AAAA;AAAA,MC+FI0D,CAAA,GAAaG,CAAA;EACxBC,WAAA,EAAA/B,CAAA,GAAc,CAAC,GAAG,IAAI,IAAI;EAC1BgC,YAAA,EAAAxB,CAAA,GAAe,CAAC,GAAG,IAAI,IAAI;EAC3ByB,QAAA,EAAAN,CAAA,GAAW;EACXO,SAAA,EAAAN,CAAA;EACAO,4BAAA,EAAAN,CAAA,IAA+B;EAC/BO,aAAA,EAAAC,CAAA,GAAgB;AAAA,IACF,OAA2BpE,CAAA;EACzC,MAAMqE,CAAA,GAAqD;EAC3D,IAAIlE,CAAA,EACF,OAAO;IACLmE,IAAA,EAAM;IACNC,IAAA,EAAM;EAAA;EAIV;IAAM1D,MAAA,EACJ2D,CAAA,MAAW,QAAAnE,CAAA,YAAAA,CAAA,CAAYQ,MAAA;IAAMC,QAAA,EAC7B2D,CAAA,MAAa,QAAApE,CAAA,YAAAA,CAAA,CAAYS,QAAA;IAAQ4D,UAAA,EACjCC,CAAA,GAAavE,MAAA,CAAOwE,MAAA;IAAMC,cAAA,EAC1BC,CAAA,GAAwBhF,CAAA,IAAgBE,CAAA;MACtC,MAAMA,CAAA,SAA2B+E,KAAA,CAAM,GAAGX,CAAA,IAAiBtE,CAAA,IAAQkF,IAAA,CAChEhF,CAAA,IAAaA,CAAA,CAASiF,IAAA;MAQzB,IAJgBC,QAAA,CACblF,CAAA,CAAKmF,KAAA,GAA8BC,KAAA,CAAM,KAAK,IAC/C,MAEY,GACZ,MAAM,IAAI1D,CAAA,CACR;MAGJ,OAAO1B,CAAA;IAAA;EAAA,IAEP0D,CAAA;EACJ;IAAI2B,QAAA,EAAEC;EAAA,IAAa5B,CAAA;EAqInB,MAAM6B,CAAA,GAAWC,CACfxF,CAAA,EACAF,CAAA,EACAC,CAAA,EACAI,CAAA,EACAE,CAAA,MACI;IACJoF,MAAA,EAAApF,CAAA;IACAqF,GAAA,EAAAvF,CAAA;IACAwF,GAAA,EAAA5F,CAAA;IACAe,QAAA,EAAA2D,CAAA;IACAH,IAAA,EAAAtE,CAAA;IACAuE,IAAA,EAAAzE;EAAA;EAGF,IAAI8F,CAAA;IACAC,CAAA,GAAc;EAElB,IAAKP,CAAA,EAyBHA,CAAA,GAAWvF,CAAA,CAAcuF,CAAA,GACzBM,CAAA,GAAY,CAACN,CAAA,OA1BA;IACb,MAAMtF,CAAA,GACJ2D,CAAA,cC/R0B3D,CAAA,EAC9BF,CAAA,IAA+B;MAC/B,MAAMC,CAAA,GAOF;QACF+F,KAAA,GAAO;QACPC,SAAA,GAAW;QACXC,KAAA,GAAO;QACP9B,4BAAA,EAAApE,CAAA;QACAmG,eAAA,EAAiB;QACjBC,OAAA,GAAS;MAAA;MAKPlG,CAAA,WACKD,CAAA,CAAWkG,eAAA;MAGpB,MAAM9F,CAAA,GAASC,MAAA,CAAO+F,QAAA,CAASC,aAAA,CAAc;QAEvC/F,CAAA,GAAMF,CAAA,CAAOkG,UAAA,CAAW,SAAStG,CAAA,KACrCI,CAAA,CAAOkG,UAAA,CACL,sBACAtG,CAAA;MAGJ,OAAO,QAAAM,CAAA,GAAAA,CAAA,QAAM;IAAA,CDgQT,CAAgB,QAAAA,CAAA,QAAU,IAAVA,CAAA,CAAYU,UAAA,EAAY6C,CAAA;IAE1C,KAAK5D,CAAA,EACH,OAAOuF,CAAA,CAAS,GAAG;IAGrB,MAAMzF,CAAA,IAAoB,QAAAO,CAAA,QAAU,IAAVA,CAAA,CAAYW,SAAA,IAClC,OACAhB,CAAA,CAAGsG,YAAA,CAAa;IAMpB,IAJAhB,CAAA,GAAWxF,CAAA,GACPE,CAAA,CAAGuG,YAAA,CAAazG,CAAA,CAAkB0G,uBAAA,IAClCxG,CAAA,CAAGuG,YAAA,CAAavG,CAAA,CAAGyG,QAAA,IAElBnB,CAAA,EACH,OAAOC,CAAA,CAAS,GAAG;IAGrBM,CAAA,GAAcP,CAAA,EACdA,CAAA,GAAWvF,CAAA,CAAcuF,CAAA,GACzBM,CAAA,aEhTF5F,CAAA,EACAF,CAAA,EACAC,CAAA;MAEA,OAAoB,gBAAbD,CAAA,GACHa,CAAA,CAAoBX,CAAA,EAAIF,CAAA,EAAUC,CAAA,IAClC,CAACD,CAAA;IAAA,CF0SS,CAAoBE,CAAA,EAAIsF,CAAA,EAAUb,CAAA;EAAA;EAMhD,MAAMiC,CAAA,UAAiBC,OAAA,CAAQC,GAAA,CAAIhB,CAAA,CAAU5C,GAAA,CAxJ7C,UAA+BlD,CAAA;IAAA,IAAAC,CAAA;IAAA,OAAAC,CAAA;MAC7B,MAAMA,CAAA,GA5BW,CAACA,CAAA;QAClB,MAAMF,CAAA,GAAQ2E,CAAA,GACT,CACC,UACA,SACA,UACA,QACA,UACA,WACA,aAED,CACC,SACA,SACA,OACA,UACA,UACA,WACA;QAEN,KAAK,MAAM1E,CAAA,IAAQD,CAAA,EACjB,IAAIE,CAAA,CAAS6G,QAAA,CAAS9G,CAAA,GACpB,OAAOA,CAAA;MAAA,GAMaD,CAAA;MACxB,KAAKE,CAAA,EACH;MAKF,MAAMG,CAAA,GAAgB,GAAGsE,CAAA,GAAW,MAAM,OAAOzE,CAAA,GAC/CwE,CAAA,GAAS,UAAU;QAGfnE,CAAA,GAAagE,CAAA,CAAWlE,CAAA,IACH,UAAzBJ,CAAA,GAAAsE,CAAA,CAAWlE,CAAA,WAAc,MAAAJ,CAAA,GAAAA,CAAA,GAAI+E,CAAA,CAAe3E,CAAA;MAC9C,IAAIQ,CAAA;MACJ;QACEA,CAAA,SAAmBN,CAAA;MAAA,CACnB,QAAOL,CAAA;QACP,IAAIA,CAAA,YAAiB0B,CAAA,EACnB,MAAM1B,CAAA;QAGR;MAAA;MAGF,MAAM+B,CAAA,GG3LJ,UAAwB/B,CAAA;QAAA,IAAAF,CAAA;QAG5B,MAAMC,CAAA,IAFNC,CAAA,GAAQA,CAAA,CAAME,OAAA,CAAQ,aAAa,KAI3B4G,KAAA,CAAM,UAEZ9G,CAAA,CAAM8G,KAAA,CAAM;QAId,OAA+C,UAAAhH,CAAA,GAAxC,QAAAC,CAAA,YAAAA,CAAA,CAAS6C,IAAA,CAAK,IAAI1C,OAAA,CAAQ,WAAW,aAAG,MAAAJ,CAAA,GAAAA,CAAA,GAAI;MAAA,CHgLjC,CAAcA,CAAA;MAE9B,IAAIyC,CAAA,GAAU5B,CAAA,CAAWmC,MAAA,CACvB,IAAI9C,CAAA,MAAkBA,CAAA,KAAiB+B,CAAA;MAUpCQ,CAAA,CAAQQ,MAAA,KACXR,CAAA,GAAU5B,CAAA,CAAWmC,MAAA,CAAO,EAAE9C,CAAA,MAAWA,CAAA,CAAM6G,QAAA,CAAS/G,CAAA;MAU1D,MAAM2D,CAAA,GAAalB,CAAA,CAAQQ,MAAA;MAE3B,IAAmB,MAAfU,CAAA,EACF;MAGF,MAAMC,CAAA,GAAmD5D,CAAA,CFjIxDsF,KAAA,CAAM,kBACN2B,IAAA,GAEAjE,MAAA,CAAO,CAAC9C,CAAA,EAAMF,CAAA,EAAKC,CAAA,KAAgB,MAARD,CAAA,IAAaE,CAAA,KAASD,CAAA,CAAID,CAAA,GAAM,IAC3D8C,IAAA,CAAK;ME+HN,IAmBIe,CAAA;QAAA,CAnBCC,CAAA,KAAWQ,CAAA,IACdX,CAAA,GAAa,IACTlB,CAAA,CACGS,GAAA,CACEhD,CAAA,IACC,CACEA,CAAA,EACA6C,CAAA,CAAuBa,CAAA,EAAmB1D,CAAA,CAAM,MAGrD+G,IAAA,CAAK,IAAI/G,CAAA,MAAOF,CAAA,MAAOE,CAAA,GAAIF,CAAA,EAAG,GAAG,KACpCyC,CAAA,CAAQ;QAOV+C,CAAA,GAAc0B,MAAA,CAAOC,SAAA;MAEzB;UAAMC,gBAAA,EAAE3B;QAAA,IAAqBnF,MAAA;QACvBwF,CAAA,GACJjB,CAAA,CAAWwC,KAAA,GACX5B,CAAA,GACAZ,CAAA,CAAWyC,MAAA,GACX7B,CAAA;MAEF,KAAK,MAAMvF,CAAA,IAASoE,CAAA,EAAmB;QACrC,OAAOtE,CAAA,EAAOC,CAAA,IAAUC,CAAA;UAClBG,CAAA,GAAkBL,CAAA,GAAQC,CAAA;UAC1BM,CAAA,GAAWgH,IAAA,CAAKC,GAAA,CAAI1B,CAAA,GAAazF,CAAA;QAEnCE,CAAA,GAAWiF,CAAA,KACbA,CAAA,GAAcjF,CAAA,EACdsD,CAAA,GAAU3D,CAAA;MAAA;MAId,KAAK2D,CAAA,EACH;MAIF,UAAWkC,CAAA,EAAKa,CAAA,IAAU/C,CAAA;MAE1B,OAAO,CAAC2B,CAAA,EAAaO,CAAA,EAAKjC,CAAA,EAAK8C,CAAA;IAAA;EAAA,KAmD9B5D,MAAA,CAAOW,CAAA,EACPsD,IAAA,CAAK,EAAE/G,CAAA,GAAOgH,MAAA,CAAOC,SAAA,EAAWnH,CAAA,IAAQC,CAAA,GAAOiH,MAAA,CAAOC,SAAA,EAAW9G,CAAA,MAChEH,CAAA,KAASD,CAAA,GAAOD,CAAA,GAAOK,CAAA,GAAOH,CAAA,GAAOD,CAAA;EAEzC,KAAK2G,CAAA,CAAQ3D,MAAA,EAAQ;IACnB,MAAM/C,CAAA,GAAuCF,CAAA,CAAiByH,IAAA,CAE3DvH,CAAA,IAAqBsF,CAAA,CAAUuB,QAAA,CAAS7G,CAAA;IAE3C,OAAOA,CAAA,GACHuF,CAAA,CAAS,GAAG,eAAevF,CAAA,IAC3BuF,CAAA,CAAS,GAAG,YAAY,GAAGD,CAAA,KAAaO,CAAA;EAAA;EAG9C,SAAS2B,CAAA,EAAKC,CAAA,EAAOC,CAAA,IAAUhB,CAAA,CAAQ;EAEvC,KAAa,MAATc,CAAA,EACF,OAAOjC,CAAA,CAAS,GAAG,eAAekC,CAAA,EAAOD,CAAA,EAAKE,CAAA;EAGhD,MAAMC,CAAA,GAAQlD,CAAA,GAAW1C,CAAA,GAAcQ,CAAA;EACvC,IAAIqF,CAAA,GAAO;EAEX,KAAK,IAAI5H,CAAA,GAAI,GAAGA,CAAA,GAAI2H,CAAA,CAAM5E,MAAA,EAAQ/C,CAAA,IAC5BwH,CAAA,IAAOG,CAAA,CAAM3H,CAAA,MACf4H,CAAA,GAAO5H,CAAA;EAIX,OAAOuF,CAAA,CAASqC,CAAA,EAAM,aAAaH,CAAA,EAAOD,CAAA,EAAKE,CAAA;AAAA;AAAA,SAAAhE,CAAA,IAAAG,UAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}