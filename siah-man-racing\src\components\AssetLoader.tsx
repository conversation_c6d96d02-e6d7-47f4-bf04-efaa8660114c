import React, { useState, useEffect } from 'react';
import { AssetLoader } from '../game/AssetLoader';
import styled from 'styled-components';

// Styled components
const LoadingScreen = styled.div`
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100vh;
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  background: ${props => props.theme.backgroundGradient || 'linear-gradient(145deg, #1a1a2e, #16213e, #0f3460)'};
  color: white;
  z-index: 1000;
  transition: opacity 0.5s ease-out;
`;

const LoadingLogo = styled.img`
  width: 180px;
  height: 180px;
  margin-bottom: 30px;
  animation: pulse 2s infinite;
  
  @keyframes pulse {
    0% { transform: scale(1); }
    50% { transform: scale(1.05); }
    100% { transform: scale(1); }
  }
`;

const LoadingTitle = styled.h1`
  font-size: 3.5rem;
  margin-bottom: 10px;
  font-weight: 800;
  background: linear-gradient(45deg, #ff0000, #ff8000, #ffff00, #00ff00, #0080ff, #8000ff);
  background-size: 400% 400%;
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  animation: rainbow 3s ease infinite, bounce 2s ease-in-out infinite;
  text-shadow: 0 0 20px rgba(255, 255, 255, 0.5);
  font-family: 'Arial Black', Arial, sans-serif;

  @keyframes rainbow {
    0%, 100% { background-position: 0% 50%; }
    50% { background-position: 100% 50%; }
  }

  @keyframes bounce {
    0%, 20%, 50%, 80%, 100% { transform: translateY(0); }
    40% { transform: translateY(-10px); }
    60% { transform: translateY(-5px); }
  }
`;

const LoadingSubtitle = styled.h2`
  font-size: 1.2rem;
  margin-bottom: 30px;
  font-weight: 400;
  opacity: 0.8;
`;

const ProgressBarContainer = styled.div`
  width: 80%;
  max-width: 500px;
  background: rgba(255, 255, 255, 0.1);
  border-radius: 10px;
  padding: 3px;
  margin-bottom: 10px;
`;

const ProgressBarFill = styled.div<{ $progress: number }>`
  height: 20px;
  border-radius: 8px;
  width: ${props => `${props.$progress}%`};
  background: linear-gradient(90deg, #00aaff, #0088ff);
  box-shadow: 0 0 10px rgba(0, 170, 255, 0.7);
  transition: width 0.3s ease;
`;

const ProgressText = styled.p`
  font-size: 1rem;
  margin-bottom: 40px;
`;

const LoadingStatus = styled.p`
  font-style: italic;
  opacity: 0.7;
`;

interface AssetLoaderComponentProps {
  onLoadingComplete: () => void;
  audioInitialized?: boolean;
}

export const AssetLoaderComponent: React.FC<AssetLoaderComponentProps> = ({ onLoadingComplete, audioInitialized = false }) => {
  const [progress, setProgress] = useState(0);
  const [loadingStatus, setLoadingStatus] = useState('Initializing game...');
  const [error, setError] = useState<string | null>(null);
  const [isComplete, setIsComplete] = useState(false);
  
  useEffect(() => {
    const assetLoader = AssetLoader.getInstance();
    
    const updateStatus = (progress: number) => {
      setProgress(progress * 100);

      // Update loading status text based on progress and audio status
      if (!audioInitialized) {
        setLoadingStatus('Initializing audio systems...');
      } else if (progress < 0.2) {
        setLoadingStatus('Loading game assets...');
      } else if (progress < 0.4) {
        setLoadingStatus('Loading vehicles...');
      } else if (progress < 0.6) {
        setLoadingStatus('Loading race tracks...');
      } else if (progress < 0.8) {
        setLoadingStatus('Loading character voices...');
      } else {
        setLoadingStatus('Finalizing setup...');
      }
    };
    
    const handleComplete = () => {
      setProgress(100);
      setLoadingStatus('Ready to race!');
      setIsComplete(true);
      
      // Add a small delay before completing to show the 100% state
      setTimeout(() => {
        onLoadingComplete();
      }, 1000);
    };
    
    const handleError = (errorMsg: string) => {
      setError(errorMsg);
      setLoadingStatus('Error loading assets. Please refresh the page.');
    };
    
    assetLoader.initializeAssets(updateStatus, handleComplete, handleError);
    
    // Cleanup effect (not really needed for this component but good practice)
    return () => {
      // Any cleanup if needed
    };
  }, [onLoadingComplete]);
  
  return (
    <LoadingScreen style={{ opacity: isComplete ? 0.95 : 1 }}>
      <LoadingLogo src="/assets/images/logo.png" alt="Super Siah Man Racing" />
      <LoadingTitle>Super Siah Man Racing</LoadingTitle>
      <LoadingSubtitle>Racing For Speed</LoadingSubtitle>
      
      <ProgressBarContainer>
        <ProgressBarFill $progress={progress} />
      </ProgressBarContainer>
      
      <ProgressText>{progress.toFixed(0)}% Complete</ProgressText>
      
      {error ? (
        <LoadingStatus style={{ color: '#ff3366' }}>{error}</LoadingStatus>
      ) : (
        <LoadingStatus>{loadingStatus}</LoadingStatus>
      )}
    </LoadingScreen>
  );
};

export default AssetLoaderComponent;
