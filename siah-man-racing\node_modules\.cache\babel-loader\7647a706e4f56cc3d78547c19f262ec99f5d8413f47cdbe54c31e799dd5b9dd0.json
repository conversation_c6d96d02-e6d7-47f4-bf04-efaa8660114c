{"ast": null, "code": "import { <PERSON><PERSON>, <PERSON><PERSON>oader, <PERSON>ufferGeometry, Float32BufferAttribute } from \"three\";\nclass PDBLoader extends Loader {\n  constructor(manager) {\n    super(manager);\n  }\n  load(url, onLoad, onProgress, onError) {\n    const scope = this;\n    const loader = new FileLoader(scope.manager);\n    loader.setPath(scope.path);\n    loader.setRequestHeader(scope.requestHeader);\n    loader.setWithCredentials(scope.withCredentials);\n    loader.load(url, function (text) {\n      try {\n        onLoad(scope.parse(text));\n      } catch (e) {\n        if (onError) {\n          onError(e);\n        } else {\n          console.error(e);\n        }\n        scope.manager.itemError(url);\n      }\n    }, onProgress, onError);\n  }\n  // Based on CanvasMol PDB parser\n  parse(text) {\n    function trim(text2) {\n      return text2.replace(/^\\s\\s*/, \"\").replace(/\\s\\s*$/, \"\");\n    }\n    function capitalize(text2) {\n      return text2.charAt(0).toUpperCase() + text2.substr(1).toLowerCase();\n    }\n    function hash(s, e) {\n      return \"s\" + Math.min(s, e) + \"e\" + Math.max(s, e);\n    }\n    function parseBond(start, length, satom, i) {\n      const eatom = parseInt(lines[i].substr(start, length));\n      if (eatom) {\n        const h = hash(satom, eatom);\n        if (_bhash[h] === void 0) {\n          _bonds.push([satom - 1, eatom - 1, 1]);\n          _bhash[h] = _bonds.length - 1;\n        }\n      }\n    }\n    function buildGeometry() {\n      const build = {\n        geometryAtoms: new BufferGeometry(),\n        geometryBonds: new BufferGeometry(),\n        json: {\n          atoms\n        }\n      };\n      const geometryAtoms = build.geometryAtoms;\n      const geometryBonds = build.geometryBonds;\n      const verticesAtoms = [];\n      const colorsAtoms = [];\n      const verticesBonds = [];\n      for (let i = 0, l = atoms.length; i < l; i++) {\n        const atom = atoms[i];\n        const x = atom[0];\n        const y = atom[1];\n        const z = atom[2];\n        verticesAtoms.push(x, y, z);\n        const r = atom[3][0] / 255;\n        const g = atom[3][1] / 255;\n        const b = atom[3][2] / 255;\n        colorsAtoms.push(r, g, b);\n      }\n      for (let i = 0, l = _bonds.length; i < l; i++) {\n        const bond = _bonds[i];\n        const start = bond[0];\n        const end = bond[1];\n        const startAtom = _atomMap[start];\n        const endAtom = _atomMap[end];\n        let x = startAtom[0];\n        let y = startAtom[1];\n        let z = startAtom[2];\n        verticesBonds.push(x, y, z);\n        x = endAtom[0];\n        y = endAtom[1];\n        z = endAtom[2];\n        verticesBonds.push(x, y, z);\n      }\n      geometryAtoms.setAttribute(\"position\", new Float32BufferAttribute(verticesAtoms, 3));\n      geometryAtoms.setAttribute(\"color\", new Float32BufferAttribute(colorsAtoms, 3));\n      geometryBonds.setAttribute(\"position\", new Float32BufferAttribute(verticesBonds, 3));\n      return build;\n    }\n    const CPK = {\n      h: [255, 255, 255],\n      he: [217, 255, 255],\n      li: [204, 128, 255],\n      be: [194, 255, 0],\n      b: [255, 181, 181],\n      c: [144, 144, 144],\n      n: [48, 80, 248],\n      o: [255, 13, 13],\n      f: [144, 224, 80],\n      ne: [179, 227, 245],\n      na: [171, 92, 242],\n      mg: [138, 255, 0],\n      al: [191, 166, 166],\n      si: [240, 200, 160],\n      p: [255, 128, 0],\n      s: [255, 255, 48],\n      cl: [31, 240, 31],\n      ar: [128, 209, 227],\n      k: [143, 64, 212],\n      ca: [61, 255, 0],\n      sc: [230, 230, 230],\n      ti: [191, 194, 199],\n      v: [166, 166, 171],\n      cr: [138, 153, 199],\n      mn: [156, 122, 199],\n      fe: [224, 102, 51],\n      co: [240, 144, 160],\n      ni: [80, 208, 80],\n      cu: [200, 128, 51],\n      zn: [125, 128, 176],\n      ga: [194, 143, 143],\n      ge: [102, 143, 143],\n      as: [189, 128, 227],\n      se: [255, 161, 0],\n      br: [166, 41, 41],\n      kr: [92, 184, 209],\n      rb: [112, 46, 176],\n      sr: [0, 255, 0],\n      y: [148, 255, 255],\n      zr: [148, 224, 224],\n      nb: [115, 194, 201],\n      mo: [84, 181, 181],\n      tc: [59, 158, 158],\n      ru: [36, 143, 143],\n      rh: [10, 125, 140],\n      pd: [0, 105, 133],\n      ag: [192, 192, 192],\n      cd: [255, 217, 143],\n      in: [166, 117, 115],\n      sn: [102, 128, 128],\n      sb: [158, 99, 181],\n      te: [212, 122, 0],\n      i: [148, 0, 148],\n      xe: [66, 158, 176],\n      cs: [87, 23, 143],\n      ba: [0, 201, 0],\n      la: [112, 212, 255],\n      ce: [255, 255, 199],\n      pr: [217, 255, 199],\n      nd: [199, 255, 199],\n      pm: [163, 255, 199],\n      sm: [143, 255, 199],\n      eu: [97, 255, 199],\n      gd: [69, 255, 199],\n      tb: [48, 255, 199],\n      dy: [31, 255, 199],\n      ho: [0, 255, 156],\n      er: [0, 230, 117],\n      tm: [0, 212, 82],\n      yb: [0, 191, 56],\n      lu: [0, 171, 36],\n      hf: [77, 194, 255],\n      ta: [77, 166, 255],\n      w: [33, 148, 214],\n      re: [38, 125, 171],\n      os: [38, 102, 150],\n      ir: [23, 84, 135],\n      pt: [208, 208, 224],\n      au: [255, 209, 35],\n      hg: [184, 184, 208],\n      tl: [166, 84, 77],\n      pb: [87, 89, 97],\n      bi: [158, 79, 181],\n      po: [171, 92, 0],\n      at: [117, 79, 69],\n      rn: [66, 130, 150],\n      fr: [66, 0, 102],\n      ra: [0, 125, 0],\n      ac: [112, 171, 250],\n      th: [0, 186, 255],\n      pa: [0, 161, 255],\n      u: [0, 143, 255],\n      np: [0, 128, 255],\n      pu: [0, 107, 255],\n      am: [84, 92, 242],\n      cm: [120, 92, 227],\n      bk: [138, 79, 227],\n      cf: [161, 54, 212],\n      es: [179, 31, 212],\n      fm: [179, 31, 186],\n      md: [179, 13, 166],\n      no: [189, 13, 135],\n      lr: [199, 0, 102],\n      rf: [204, 0, 89],\n      db: [209, 0, 79],\n      sg: [217, 0, 69],\n      bh: [224, 0, 56],\n      hs: [230, 0, 46],\n      mt: [235, 0, 38],\n      ds: [235, 0, 38],\n      rg: [235, 0, 38],\n      cn: [235, 0, 38],\n      uut: [235, 0, 38],\n      uuq: [235, 0, 38],\n      uup: [235, 0, 38],\n      uuh: [235, 0, 38],\n      uus: [235, 0, 38],\n      uuo: [235, 0, 38]\n    };\n    const atoms = [];\n    const _bonds = [];\n    const _bhash = {};\n    const _atomMap = {};\n    const lines = text.split(\"\\n\");\n    for (let i = 0, l = lines.length; i < l; i++) {\n      if (lines[i].substr(0, 4) === \"ATOM\" || lines[i].substr(0, 6) === \"HETATM\") {\n        const x = parseFloat(lines[i].substr(30, 7));\n        const y = parseFloat(lines[i].substr(38, 7));\n        const z = parseFloat(lines[i].substr(46, 7));\n        const index = parseInt(lines[i].substr(6, 5)) - 1;\n        let e = trim(lines[i].substr(76, 2)).toLowerCase();\n        if (e === \"\") {\n          e = trim(lines[i].substr(12, 2)).toLowerCase();\n        }\n        const atomData = [x, y, z, CPK[e], capitalize(e)];\n        atoms.push(atomData);\n        _atomMap[index] = atomData;\n      } else if (lines[i].substr(0, 6) === \"CONECT\") {\n        const satom = parseInt(lines[i].substr(6, 5));\n        parseBond(11, 5, satom, i);\n        parseBond(16, 5, satom, i);\n        parseBond(21, 5, satom, i);\n        parseBond(26, 5, satom, i);\n      }\n    }\n    return buildGeometry();\n  }\n}\nexport { PDBLoader };", "map": {"version": 3, "names": ["<PERSON><PERSON><PERSON><PERSON>", "Loader", "constructor", "manager", "load", "url", "onLoad", "onProgress", "onError", "scope", "loader", "<PERSON><PERSON><PERSON><PERSON>", "set<PERSON>ath", "path", "setRequestHeader", "requestHeader", "setWithCredentials", "withCredentials", "text", "parse", "e", "console", "error", "itemError", "trim", "text2", "replace", "capitalize", "char<PERSON>t", "toUpperCase", "substr", "toLowerCase", "hash", "s", "Math", "min", "max", "parseBond", "start", "length", "satom", "i", "eatom", "parseInt", "lines", "h", "_bhash", "_bonds", "push", "buildGeometry", "build", "geometryAtoms", "BufferGeometry", "geometryBonds", "json", "atoms", "verticesAtoms", "colorsAtoms", "verticesBonds", "l", "atom", "x", "y", "z", "r", "g", "b", "bond", "end", "startAtom", "_atomMap", "endAtom", "setAttribute", "Float32BufferAttribute", "CPK", "he", "li", "be", "c", "n", "o", "f", "ne", "na", "mg", "al", "si", "p", "cl", "ar", "k", "ca", "sc", "ti", "v", "cr", "mn", "fe", "co", "ni", "cu", "zn", "ga", "ge", "as", "se", "br", "kr", "rb", "sr", "zr", "nb", "mo", "tc", "ru", "rh", "pd", "ag", "cd", "in", "sn", "sb", "te", "xe", "cs", "ba", "la", "ce", "pr", "nd", "pm", "sm", "eu", "gd", "tb", "dy", "ho", "er", "tm", "yb", "lu", "hf", "ta", "w", "re", "os", "ir", "pt", "au", "hg", "tl", "pb", "bi", "po", "at", "rn", "fr", "ra", "ac", "th", "pa", "u", "np", "pu", "am", "cm", "bk", "cf", "es", "fm", "md", "no", "lr", "rf", "db", "sg", "bh", "hs", "mt", "ds", "rg", "cn", "uut", "uuq", "uup", "uuh", "uus", "uuo", "split", "parseFloat", "index", "atomData"], "sources": ["C:\\Users\\<USER>\\Documents\\Apps_Creations\\SiahMan_Racing4Speed_WebGame\\siah-man-racing\\node_modules\\src\\loaders\\PDBLoader.js"], "sourcesContent": ["import { <PERSON>ufferGeo<PERSON>, FileLoader, Float32<PERSON>uffer<PERSON><PERSON><PERSON><PERSON><PERSON>, Loader } from 'three'\n\nclass PDBLoader extends Loader {\n  constructor(manager) {\n    super(manager)\n  }\n\n  load(url, onLoad, onProgress, onError) {\n    const scope = this\n\n    const loader = new FileLoader(scope.manager)\n    loader.setPath(scope.path)\n    loader.setRequestHeader(scope.requestHeader)\n    loader.setWithCredentials(scope.withCredentials)\n    loader.load(\n      url,\n      function (text) {\n        try {\n          onLoad(scope.parse(text))\n        } catch (e) {\n          if (onError) {\n            onError(e)\n          } else {\n            console.error(e)\n          }\n\n          scope.manager.itemError(url)\n        }\n      },\n      onProgress,\n      onError,\n    )\n  }\n\n  // Based on CanvasMol PDB parser\n\n  parse(text) {\n    function trim(text) {\n      return text.replace(/^\\s\\s*/, '').replace(/\\s\\s*$/, '')\n    }\n\n    function capitalize(text) {\n      return text.charAt(0).toUpperCase() + text.substr(1).toLowerCase()\n    }\n\n    function hash(s, e) {\n      return 's' + Math.min(s, e) + 'e' + Math.max(s, e)\n    }\n\n    function parseBond(start, length, satom, i) {\n      const eatom = parseInt(lines[i].substr(start, length))\n\n      if (eatom) {\n        const h = hash(satom, eatom)\n\n        if (_bhash[h] === undefined) {\n          _bonds.push([satom - 1, eatom - 1, 1])\n          _bhash[h] = _bonds.length - 1\n        } else {\n          // doesn't really work as almost all PDBs\n          // have just normal bonds appearing multiple\n          // times instead of being double/triple bonds\n          // bonds[bhash[h]][2] += 1;\n        }\n      }\n    }\n\n    function buildGeometry() {\n      const build = {\n        geometryAtoms: new BufferGeometry(),\n        geometryBonds: new BufferGeometry(),\n        json: {\n          atoms: atoms,\n        },\n      }\n\n      const geometryAtoms = build.geometryAtoms\n      const geometryBonds = build.geometryBonds\n\n      const verticesAtoms = []\n      const colorsAtoms = []\n      const verticesBonds = []\n\n      // atoms\n\n      for (let i = 0, l = atoms.length; i < l; i++) {\n        const atom = atoms[i]\n\n        const x = atom[0]\n        const y = atom[1]\n        const z = atom[2]\n\n        verticesAtoms.push(x, y, z)\n\n        const r = atom[3][0] / 255\n        const g = atom[3][1] / 255\n        const b = atom[3][2] / 255\n\n        colorsAtoms.push(r, g, b)\n      }\n\n      // bonds\n\n      for (let i = 0, l = _bonds.length; i < l; i++) {\n        const bond = _bonds[i]\n\n        const start = bond[0]\n        const end = bond[1]\n\n        const startAtom = _atomMap[start]\n        const endAtom = _atomMap[end]\n\n        let x = startAtom[0]\n        let y = startAtom[1]\n        let z = startAtom[2]\n\n        verticesBonds.push(x, y, z)\n\n        x = endAtom[0]\n        y = endAtom[1]\n        z = endAtom[2]\n\n        verticesBonds.push(x, y, z)\n      }\n\n      // build geometry\n\n      geometryAtoms.setAttribute('position', new Float32BufferAttribute(verticesAtoms, 3))\n      geometryAtoms.setAttribute('color', new Float32BufferAttribute(colorsAtoms, 3))\n\n      geometryBonds.setAttribute('position', new Float32BufferAttribute(verticesBonds, 3))\n\n      return build\n    }\n\n    const CPK = {\n      h: [255, 255, 255],\n      he: [217, 255, 255],\n      li: [204, 128, 255],\n      be: [194, 255, 0],\n      b: [255, 181, 181],\n      c: [144, 144, 144],\n      n: [48, 80, 248],\n      o: [255, 13, 13],\n      f: [144, 224, 80],\n      ne: [179, 227, 245],\n      na: [171, 92, 242],\n      mg: [138, 255, 0],\n      al: [191, 166, 166],\n      si: [240, 200, 160],\n      p: [255, 128, 0],\n      s: [255, 255, 48],\n      cl: [31, 240, 31],\n      ar: [128, 209, 227],\n      k: [143, 64, 212],\n      ca: [61, 255, 0],\n      sc: [230, 230, 230],\n      ti: [191, 194, 199],\n      v: [166, 166, 171],\n      cr: [138, 153, 199],\n      mn: [156, 122, 199],\n      fe: [224, 102, 51],\n      co: [240, 144, 160],\n      ni: [80, 208, 80],\n      cu: [200, 128, 51],\n      zn: [125, 128, 176],\n      ga: [194, 143, 143],\n      ge: [102, 143, 143],\n      as: [189, 128, 227],\n      se: [255, 161, 0],\n      br: [166, 41, 41],\n      kr: [92, 184, 209],\n      rb: [112, 46, 176],\n      sr: [0, 255, 0],\n      y: [148, 255, 255],\n      zr: [148, 224, 224],\n      nb: [115, 194, 201],\n      mo: [84, 181, 181],\n      tc: [59, 158, 158],\n      ru: [36, 143, 143],\n      rh: [10, 125, 140],\n      pd: [0, 105, 133],\n      ag: [192, 192, 192],\n      cd: [255, 217, 143],\n      in: [166, 117, 115],\n      sn: [102, 128, 128],\n      sb: [158, 99, 181],\n      te: [212, 122, 0],\n      i: [148, 0, 148],\n      xe: [66, 158, 176],\n      cs: [87, 23, 143],\n      ba: [0, 201, 0],\n      la: [112, 212, 255],\n      ce: [255, 255, 199],\n      pr: [217, 255, 199],\n      nd: [199, 255, 199],\n      pm: [163, 255, 199],\n      sm: [143, 255, 199],\n      eu: [97, 255, 199],\n      gd: [69, 255, 199],\n      tb: [48, 255, 199],\n      dy: [31, 255, 199],\n      ho: [0, 255, 156],\n      er: [0, 230, 117],\n      tm: [0, 212, 82],\n      yb: [0, 191, 56],\n      lu: [0, 171, 36],\n      hf: [77, 194, 255],\n      ta: [77, 166, 255],\n      w: [33, 148, 214],\n      re: [38, 125, 171],\n      os: [38, 102, 150],\n      ir: [23, 84, 135],\n      pt: [208, 208, 224],\n      au: [255, 209, 35],\n      hg: [184, 184, 208],\n      tl: [166, 84, 77],\n      pb: [87, 89, 97],\n      bi: [158, 79, 181],\n      po: [171, 92, 0],\n      at: [117, 79, 69],\n      rn: [66, 130, 150],\n      fr: [66, 0, 102],\n      ra: [0, 125, 0],\n      ac: [112, 171, 250],\n      th: [0, 186, 255],\n      pa: [0, 161, 255],\n      u: [0, 143, 255],\n      np: [0, 128, 255],\n      pu: [0, 107, 255],\n      am: [84, 92, 242],\n      cm: [120, 92, 227],\n      bk: [138, 79, 227],\n      cf: [161, 54, 212],\n      es: [179, 31, 212],\n      fm: [179, 31, 186],\n      md: [179, 13, 166],\n      no: [189, 13, 135],\n      lr: [199, 0, 102],\n      rf: [204, 0, 89],\n      db: [209, 0, 79],\n      sg: [217, 0, 69],\n      bh: [224, 0, 56],\n      hs: [230, 0, 46],\n      mt: [235, 0, 38],\n      ds: [235, 0, 38],\n      rg: [235, 0, 38],\n      cn: [235, 0, 38],\n      uut: [235, 0, 38],\n      uuq: [235, 0, 38],\n      uup: [235, 0, 38],\n      uuh: [235, 0, 38],\n      uus: [235, 0, 38],\n      uuo: [235, 0, 38],\n    }\n\n    const atoms = []\n\n    const _bonds = []\n    const _bhash = {}\n    const _atomMap = {}\n\n    // parse\n\n    const lines = text.split('\\n')\n\n    for (let i = 0, l = lines.length; i < l; i++) {\n      if (lines[i].substr(0, 4) === 'ATOM' || lines[i].substr(0, 6) === 'HETATM') {\n        const x = parseFloat(lines[i].substr(30, 7))\n        const y = parseFloat(lines[i].substr(38, 7))\n        const z = parseFloat(lines[i].substr(46, 7))\n        const index = parseInt(lines[i].substr(6, 5)) - 1\n\n        let e = trim(lines[i].substr(76, 2)).toLowerCase()\n\n        if (e === '') {\n          e = trim(lines[i].substr(12, 2)).toLowerCase()\n        }\n\n        const atomData = [x, y, z, CPK[e], capitalize(e)]\n\n        atoms.push(atomData)\n        _atomMap[index] = atomData\n      } else if (lines[i].substr(0, 6) === 'CONECT') {\n        const satom = parseInt(lines[i].substr(6, 5))\n\n        parseBond(11, 5, satom, i)\n        parseBond(16, 5, satom, i)\n        parseBond(21, 5, satom, i)\n        parseBond(26, 5, satom, i)\n      }\n    }\n\n    // build and return geometry\n\n    return buildGeometry()\n  }\n}\n\nexport { PDBLoader }\n"], "mappings": ";AAEA,MAAMA,SAAA,SAAkBC,MAAA,CAAO;EAC7BC,YAAYC,OAAA,EAAS;IACnB,MAAMA,OAAO;EACd;EAEDC,KAAKC,GAAA,EAAKC,MAAA,EAAQC,UAAA,EAAYC,OAAA,EAAS;IACrC,MAAMC,KAAA,GAAQ;IAEd,MAAMC,MAAA,GAAS,IAAIC,UAAA,CAAWF,KAAA,CAAMN,OAAO;IAC3CO,MAAA,CAAOE,OAAA,CAAQH,KAAA,CAAMI,IAAI;IACzBH,MAAA,CAAOI,gBAAA,CAAiBL,KAAA,CAAMM,aAAa;IAC3CL,MAAA,CAAOM,kBAAA,CAAmBP,KAAA,CAAMQ,eAAe;IAC/CP,MAAA,CAAON,IAAA,CACLC,GAAA,EACA,UAAUa,IAAA,EAAM;MACd,IAAI;QACFZ,MAAA,CAAOG,KAAA,CAAMU,KAAA,CAAMD,IAAI,CAAC;MACzB,SAAQE,CAAA,EAAP;QACA,IAAIZ,OAAA,EAAS;UACXA,OAAA,CAAQY,CAAC;QACrB,OAAiB;UACLC,OAAA,CAAQC,KAAA,CAAMF,CAAC;QAChB;QAEDX,KAAA,CAAMN,OAAA,CAAQoB,SAAA,CAAUlB,GAAG;MAC5B;IACF,GACDE,UAAA,EACAC,OACD;EACF;EAAA;EAIDW,MAAMD,IAAA,EAAM;IACV,SAASM,KAAKC,KAAA,EAAM;MAClB,OAAOA,KAAA,CAAKC,OAAA,CAAQ,UAAU,EAAE,EAAEA,OAAA,CAAQ,UAAU,EAAE;IACvD;IAED,SAASC,WAAWF,KAAA,EAAM;MACxB,OAAOA,KAAA,CAAKG,MAAA,CAAO,CAAC,EAAEC,WAAA,KAAgBJ,KAAA,CAAKK,MAAA,CAAO,CAAC,EAAEC,WAAA,CAAa;IACnE;IAED,SAASC,KAAKC,CAAA,EAAGb,CAAA,EAAG;MAClB,OAAO,MAAMc,IAAA,CAAKC,GAAA,CAAIF,CAAA,EAAGb,CAAC,IAAI,MAAMc,IAAA,CAAKE,GAAA,CAAIH,CAAA,EAAGb,CAAC;IAClD;IAED,SAASiB,UAAUC,KAAA,EAAOC,MAAA,EAAQC,KAAA,EAAOC,CAAA,EAAG;MAC1C,MAAMC,KAAA,GAAQC,QAAA,CAASC,KAAA,CAAMH,CAAC,EAAEX,MAAA,CAAOQ,KAAA,EAAOC,MAAM,CAAC;MAErD,IAAIG,KAAA,EAAO;QACT,MAAMG,CAAA,GAAIb,IAAA,CAAKQ,KAAA,EAAOE,KAAK;QAE3B,IAAII,MAAA,CAAOD,CAAC,MAAM,QAAW;UAC3BE,MAAA,CAAOC,IAAA,CAAK,CAACR,KAAA,GAAQ,GAAGE,KAAA,GAAQ,GAAG,CAAC,CAAC;UACrCI,MAAA,CAAOD,CAAC,IAAIE,MAAA,CAAOR,MAAA,GAAS;QAM7B;MACF;IACF;IAED,SAASU,cAAA,EAAgB;MACvB,MAAMC,KAAA,GAAQ;QACZC,aAAA,EAAe,IAAIC,cAAA,CAAgB;QACnCC,aAAA,EAAe,IAAID,cAAA,CAAgB;QACnCE,IAAA,EAAM;UACJC;QACD;MACF;MAED,MAAMJ,aAAA,GAAgBD,KAAA,CAAMC,aAAA;MAC5B,MAAME,aAAA,GAAgBH,KAAA,CAAMG,aAAA;MAE5B,MAAMG,aAAA,GAAgB,EAAE;MACxB,MAAMC,WAAA,GAAc,EAAE;MACtB,MAAMC,aAAA,GAAgB,EAAE;MAIxB,SAASjB,CAAA,GAAI,GAAGkB,CAAA,GAAIJ,KAAA,CAAMhB,MAAA,EAAQE,CAAA,GAAIkB,CAAA,EAAGlB,CAAA,IAAK;QAC5C,MAAMmB,IAAA,GAAOL,KAAA,CAAMd,CAAC;QAEpB,MAAMoB,CAAA,GAAID,IAAA,CAAK,CAAC;QAChB,MAAME,CAAA,GAAIF,IAAA,CAAK,CAAC;QAChB,MAAMG,CAAA,GAAIH,IAAA,CAAK,CAAC;QAEhBJ,aAAA,CAAcR,IAAA,CAAKa,CAAA,EAAGC,CAAA,EAAGC,CAAC;QAE1B,MAAMC,CAAA,GAAIJ,IAAA,CAAK,CAAC,EAAE,CAAC,IAAI;QACvB,MAAMK,CAAA,GAAIL,IAAA,CAAK,CAAC,EAAE,CAAC,IAAI;QACvB,MAAMM,CAAA,GAAIN,IAAA,CAAK,CAAC,EAAE,CAAC,IAAI;QAEvBH,WAAA,CAAYT,IAAA,CAAKgB,CAAA,EAAGC,CAAA,EAAGC,CAAC;MACzB;MAID,SAASzB,CAAA,GAAI,GAAGkB,CAAA,GAAIZ,MAAA,CAAOR,MAAA,EAAQE,CAAA,GAAIkB,CAAA,EAAGlB,CAAA,IAAK;QAC7C,MAAM0B,IAAA,GAAOpB,MAAA,CAAON,CAAC;QAErB,MAAMH,KAAA,GAAQ6B,IAAA,CAAK,CAAC;QACpB,MAAMC,GAAA,GAAMD,IAAA,CAAK,CAAC;QAElB,MAAME,SAAA,GAAYC,QAAA,CAAShC,KAAK;QAChC,MAAMiC,OAAA,GAAUD,QAAA,CAASF,GAAG;QAE5B,IAAIP,CAAA,GAAIQ,SAAA,CAAU,CAAC;QACnB,IAAIP,CAAA,GAAIO,SAAA,CAAU,CAAC;QACnB,IAAIN,CAAA,GAAIM,SAAA,CAAU,CAAC;QAEnBX,aAAA,CAAcV,IAAA,CAAKa,CAAA,EAAGC,CAAA,EAAGC,CAAC;QAE1BF,CAAA,GAAIU,OAAA,CAAQ,CAAC;QACbT,CAAA,GAAIS,OAAA,CAAQ,CAAC;QACbR,CAAA,GAAIQ,OAAA,CAAQ,CAAC;QAEbb,aAAA,CAAcV,IAAA,CAAKa,CAAA,EAAGC,CAAA,EAAGC,CAAC;MAC3B;MAIDZ,aAAA,CAAcqB,YAAA,CAAa,YAAY,IAAIC,sBAAA,CAAuBjB,aAAA,EAAe,CAAC,CAAC;MACnFL,aAAA,CAAcqB,YAAA,CAAa,SAAS,IAAIC,sBAAA,CAAuBhB,WAAA,EAAa,CAAC,CAAC;MAE9EJ,aAAA,CAAcmB,YAAA,CAAa,YAAY,IAAIC,sBAAA,CAAuBf,aAAA,EAAe,CAAC,CAAC;MAEnF,OAAOR,KAAA;IACR;IAED,MAAMwB,GAAA,GAAM;MACV7B,CAAA,EAAG,CAAC,KAAK,KAAK,GAAG;MACjB8B,EAAA,EAAI,CAAC,KAAK,KAAK,GAAG;MAClBC,EAAA,EAAI,CAAC,KAAK,KAAK,GAAG;MAClBC,EAAA,EAAI,CAAC,KAAK,KAAK,CAAC;MAChBX,CAAA,EAAG,CAAC,KAAK,KAAK,GAAG;MACjBY,CAAA,EAAG,CAAC,KAAK,KAAK,GAAG;MACjBC,CAAA,EAAG,CAAC,IAAI,IAAI,GAAG;MACfC,CAAA,EAAG,CAAC,KAAK,IAAI,EAAE;MACfC,CAAA,EAAG,CAAC,KAAK,KAAK,EAAE;MAChBC,EAAA,EAAI,CAAC,KAAK,KAAK,GAAG;MAClBC,EAAA,EAAI,CAAC,KAAK,IAAI,GAAG;MACjBC,EAAA,EAAI,CAAC,KAAK,KAAK,CAAC;MAChBC,EAAA,EAAI,CAAC,KAAK,KAAK,GAAG;MAClBC,EAAA,EAAI,CAAC,KAAK,KAAK,GAAG;MAClBC,CAAA,EAAG,CAAC,KAAK,KAAK,CAAC;MACftD,CAAA,EAAG,CAAC,KAAK,KAAK,EAAE;MAChBuD,EAAA,EAAI,CAAC,IAAI,KAAK,EAAE;MAChBC,EAAA,EAAI,CAAC,KAAK,KAAK,GAAG;MAClBC,CAAA,EAAG,CAAC,KAAK,IAAI,GAAG;MAChBC,EAAA,EAAI,CAAC,IAAI,KAAK,CAAC;MACfC,EAAA,EAAI,CAAC,KAAK,KAAK,GAAG;MAClBC,EAAA,EAAI,CAAC,KAAK,KAAK,GAAG;MAClBC,CAAA,EAAG,CAAC,KAAK,KAAK,GAAG;MACjBC,EAAA,EAAI,CAAC,KAAK,KAAK,GAAG;MAClBC,EAAA,EAAI,CAAC,KAAK,KAAK,GAAG;MAClBC,EAAA,EAAI,CAAC,KAAK,KAAK,EAAE;MACjBC,EAAA,EAAI,CAAC,KAAK,KAAK,GAAG;MAClBC,EAAA,EAAI,CAAC,IAAI,KAAK,EAAE;MAChBC,EAAA,EAAI,CAAC,KAAK,KAAK,EAAE;MACjBC,EAAA,EAAI,CAAC,KAAK,KAAK,GAAG;MAClBC,EAAA,EAAI,CAAC,KAAK,KAAK,GAAG;MAClBC,EAAA,EAAI,CAAC,KAAK,KAAK,GAAG;MAClBC,EAAA,EAAI,CAAC,KAAK,KAAK,GAAG;MAClBC,EAAA,EAAI,CAAC,KAAK,KAAK,CAAC;MAChBC,EAAA,EAAI,CAAC,KAAK,IAAI,EAAE;MAChBC,EAAA,EAAI,CAAC,IAAI,KAAK,GAAG;MACjBC,EAAA,EAAI,CAAC,KAAK,IAAI,GAAG;MACjBC,EAAA,EAAI,CAAC,GAAG,KAAK,CAAC;MACd/C,CAAA,EAAG,CAAC,KAAK,KAAK,GAAG;MACjBgD,EAAA,EAAI,CAAC,KAAK,KAAK,GAAG;MAClBC,EAAA,EAAI,CAAC,KAAK,KAAK,GAAG;MAClBC,EAAA,EAAI,CAAC,IAAI,KAAK,GAAG;MACjBC,EAAA,EAAI,CAAC,IAAI,KAAK,GAAG;MACjBC,EAAA,EAAI,CAAC,IAAI,KAAK,GAAG;MACjBC,EAAA,EAAI,CAAC,IAAI,KAAK,GAAG;MACjBC,EAAA,EAAI,CAAC,GAAG,KAAK,GAAG;MAChBC,EAAA,EAAI,CAAC,KAAK,KAAK,GAAG;MAClBC,EAAA,EAAI,CAAC,KAAK,KAAK,GAAG;MAClBC,EAAA,EAAI,CAAC,KAAK,KAAK,GAAG;MAClBC,EAAA,EAAI,CAAC,KAAK,KAAK,GAAG;MAClBC,EAAA,EAAI,CAAC,KAAK,IAAI,GAAG;MACjBC,EAAA,EAAI,CAAC,KAAK,KAAK,CAAC;MAChBjF,CAAA,EAAG,CAAC,KAAK,GAAG,GAAG;MACfkF,EAAA,EAAI,CAAC,IAAI,KAAK,GAAG;MACjBC,EAAA,EAAI,CAAC,IAAI,IAAI,GAAG;MAChBC,EAAA,EAAI,CAAC,GAAG,KAAK,CAAC;MACdC,EAAA,EAAI,CAAC,KAAK,KAAK,GAAG;MAClBC,EAAA,EAAI,CAAC,KAAK,KAAK,GAAG;MAClBC,EAAA,EAAI,CAAC,KAAK,KAAK,GAAG;MAClBC,EAAA,EAAI,CAAC,KAAK,KAAK,GAAG;MAClBC,EAAA,EAAI,CAAC,KAAK,KAAK,GAAG;MAClBC,EAAA,EAAI,CAAC,KAAK,KAAK,GAAG;MAClBC,EAAA,EAAI,CAAC,IAAI,KAAK,GAAG;MACjBC,EAAA,EAAI,CAAC,IAAI,KAAK,GAAG;MACjBC,EAAA,EAAI,CAAC,IAAI,KAAK,GAAG;MACjBC,EAAA,EAAI,CAAC,IAAI,KAAK,GAAG;MACjBC,EAAA,EAAI,CAAC,GAAG,KAAK,GAAG;MAChBC,EAAA,EAAI,CAAC,GAAG,KAAK,GAAG;MAChBC,EAAA,EAAI,CAAC,GAAG,KAAK,EAAE;MACfC,EAAA,EAAI,CAAC,GAAG,KAAK,EAAE;MACfC,EAAA,EAAI,CAAC,GAAG,KAAK,EAAE;MACfC,EAAA,EAAI,CAAC,IAAI,KAAK,GAAG;MACjBC,EAAA,EAAI,CAAC,IAAI,KAAK,GAAG;MACjBC,CAAA,EAAG,CAAC,IAAI,KAAK,GAAG;MAChBC,EAAA,EAAI,CAAC,IAAI,KAAK,GAAG;MACjBC,EAAA,EAAI,CAAC,IAAI,KAAK,GAAG;MACjBC,EAAA,EAAI,CAAC,IAAI,IAAI,GAAG;MAChBC,EAAA,EAAI,CAAC,KAAK,KAAK,GAAG;MAClBC,EAAA,EAAI,CAAC,KAAK,KAAK,EAAE;MACjBC,EAAA,EAAI,CAAC,KAAK,KAAK,GAAG;MAClBC,EAAA,EAAI,CAAC,KAAK,IAAI,EAAE;MAChBC,EAAA,EAAI,CAAC,IAAI,IAAI,EAAE;MACfC,EAAA,EAAI,CAAC,KAAK,IAAI,GAAG;MACjBC,EAAA,EAAI,CAAC,KAAK,IAAI,CAAC;MACfC,EAAA,EAAI,CAAC,KAAK,IAAI,EAAE;MAChBC,EAAA,EAAI,CAAC,IAAI,KAAK,GAAG;MACjBC,EAAA,EAAI,CAAC,IAAI,GAAG,GAAG;MACfC,EAAA,EAAI,CAAC,GAAG,KAAK,CAAC;MACdC,EAAA,EAAI,CAAC,KAAK,KAAK,GAAG;MAClBC,EAAA,EAAI,CAAC,GAAG,KAAK,GAAG;MAChBC,EAAA,EAAI,CAAC,GAAG,KAAK,GAAG;MAChBC,CAAA,EAAG,CAAC,GAAG,KAAK,GAAG;MACfC,EAAA,EAAI,CAAC,GAAG,KAAK,GAAG;MAChBC,EAAA,EAAI,CAAC,GAAG,KAAK,GAAG;MAChBC,EAAA,EAAI,CAAC,IAAI,IAAI,GAAG;MAChBC,EAAA,EAAI,CAAC,KAAK,IAAI,GAAG;MACjBC,EAAA,EAAI,CAAC,KAAK,IAAI,GAAG;MACjBC,EAAA,EAAI,CAAC,KAAK,IAAI,GAAG;MACjBC,EAAA,EAAI,CAAC,KAAK,IAAI,GAAG;MACjBC,EAAA,EAAI,CAAC,KAAK,IAAI,GAAG;MACjBC,EAAA,EAAI,CAAC,KAAK,IAAI,GAAG;MACjBC,EAAA,EAAI,CAAC,KAAK,IAAI,GAAG;MACjBC,EAAA,EAAI,CAAC,KAAK,GAAG,GAAG;MAChBC,EAAA,EAAI,CAAC,KAAK,GAAG,EAAE;MACfC,EAAA,EAAI,CAAC,KAAK,GAAG,EAAE;MACfC,EAAA,EAAI,CAAC,KAAK,GAAG,EAAE;MACfC,EAAA,EAAI,CAAC,KAAK,GAAG,EAAE;MACfC,EAAA,EAAI,CAAC,KAAK,GAAG,EAAE;MACfC,EAAA,EAAI,CAAC,KAAK,GAAG,EAAE;MACfC,EAAA,EAAI,CAAC,KAAK,GAAG,EAAE;MACfC,EAAA,EAAI,CAAC,KAAK,GAAG,EAAE;MACfC,EAAA,EAAI,CAAC,KAAK,GAAG,EAAE;MACfC,GAAA,EAAK,CAAC,KAAK,GAAG,EAAE;MAChBC,GAAA,EAAK,CAAC,KAAK,GAAG,EAAE;MAChBC,GAAA,EAAK,CAAC,KAAK,GAAG,EAAE;MAChBC,GAAA,EAAK,CAAC,KAAK,GAAG,EAAE;MAChBC,GAAA,EAAK,CAAC,KAAK,GAAG,EAAE;MAChBC,GAAA,EAAK,CAAC,KAAK,GAAG,EAAE;IACjB;IAED,MAAMpI,KAAA,GAAQ,EAAE;IAEhB,MAAMR,MAAA,GAAS,EAAE;IACjB,MAAMD,MAAA,GAAS,CAAE;IACjB,MAAMwB,QAAA,GAAW,CAAE;IAInB,MAAM1B,KAAA,GAAQ1B,IAAA,CAAK0K,KAAA,CAAM,IAAI;IAE7B,SAASnJ,CAAA,GAAI,GAAGkB,CAAA,GAAIf,KAAA,CAAML,MAAA,EAAQE,CAAA,GAAIkB,CAAA,EAAGlB,CAAA,IAAK;MAC5C,IAAIG,KAAA,CAAMH,CAAC,EAAEX,MAAA,CAAO,GAAG,CAAC,MAAM,UAAUc,KAAA,CAAMH,CAAC,EAAEX,MAAA,CAAO,GAAG,CAAC,MAAM,UAAU;QAC1E,MAAM+B,CAAA,GAAIgI,UAAA,CAAWjJ,KAAA,CAAMH,CAAC,EAAEX,MAAA,CAAO,IAAI,CAAC,CAAC;QAC3C,MAAMgC,CAAA,GAAI+H,UAAA,CAAWjJ,KAAA,CAAMH,CAAC,EAAEX,MAAA,CAAO,IAAI,CAAC,CAAC;QAC3C,MAAMiC,CAAA,GAAI8H,UAAA,CAAWjJ,KAAA,CAAMH,CAAC,EAAEX,MAAA,CAAO,IAAI,CAAC,CAAC;QAC3C,MAAMgK,KAAA,GAAQnJ,QAAA,CAASC,KAAA,CAAMH,CAAC,EAAEX,MAAA,CAAO,GAAG,CAAC,CAAC,IAAI;QAEhD,IAAIV,CAAA,GAAII,IAAA,CAAKoB,KAAA,CAAMH,CAAC,EAAEX,MAAA,CAAO,IAAI,CAAC,CAAC,EAAEC,WAAA,CAAa;QAElD,IAAIX,CAAA,KAAM,IAAI;UACZA,CAAA,GAAII,IAAA,CAAKoB,KAAA,CAAMH,CAAC,EAAEX,MAAA,CAAO,IAAI,CAAC,CAAC,EAAEC,WAAA,CAAa;QAC/C;QAED,MAAMgK,QAAA,GAAW,CAAClI,CAAA,EAAGC,CAAA,EAAGC,CAAA,EAAGW,GAAA,CAAItD,CAAC,GAAGO,UAAA,CAAWP,CAAC,CAAC;QAEhDmC,KAAA,CAAMP,IAAA,CAAK+I,QAAQ;QACnBzH,QAAA,CAASwH,KAAK,IAAIC,QAAA;MAC1B,WAAiBnJ,KAAA,CAAMH,CAAC,EAAEX,MAAA,CAAO,GAAG,CAAC,MAAM,UAAU;QAC7C,MAAMU,KAAA,GAAQG,QAAA,CAASC,KAAA,CAAMH,CAAC,EAAEX,MAAA,CAAO,GAAG,CAAC,CAAC;QAE5CO,SAAA,CAAU,IAAI,GAAGG,KAAA,EAAOC,CAAC;QACzBJ,SAAA,CAAU,IAAI,GAAGG,KAAA,EAAOC,CAAC;QACzBJ,SAAA,CAAU,IAAI,GAAGG,KAAA,EAAOC,CAAC;QACzBJ,SAAA,CAAU,IAAI,GAAGG,KAAA,EAAOC,CAAC;MAC1B;IACF;IAID,OAAOQ,aAAA,CAAe;EACvB;AACH", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}