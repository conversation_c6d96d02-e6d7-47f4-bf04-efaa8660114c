{"ast": null, "code": "var __defProp = Object.defineProperty;\nvar __defNormalProp = (obj, key, value) => key in obj ? __defProp(obj, key, {\n  enumerable: true,\n  configurable: true,\n  writable: true,\n  value\n}) : obj[key] = value;\nvar __publicField = (obj, key, value) => {\n  __defNormalProp(obj, typeof key !== \"symbol\" ? key + \"\" : key, value);\n  return value;\n};\nimport { Pass } from \"./Pass.js\";\nimport { Color } from \"three\";\nclass ClearPass extends Pass {\n  constructor(clearColor, clearAlpha) {\n    super();\n    __publicField(this, \"clearColor\");\n    __publicField(this, \"clearAlpha\");\n    __publicField(this, \"_oldClearColor\");\n    this.needsSwap = false;\n    this.clearColor = clearColor !== void 0 ? clearColor : 0;\n    this.clearAlpha = clearAlpha !== void 0 ? clearAlpha : 0;\n    this._oldClearColor = new Color();\n  }\n  render(renderer, writeBuffer, readBuffer) {\n    let oldClearAlpha;\n    if (this.clearColor) {\n      renderer.getClearColor(this._oldClearColor);\n      oldClearAlpha = renderer.getClearAlpha();\n      renderer.setClearColor(this.clearColor, this.clearAlpha);\n    }\n    renderer.setRenderTarget(this.renderToScreen ? null : readBuffer);\n    renderer.clear();\n    if (this.clearColor) {\n      renderer.setClearColor(this._oldClearColor, oldClearAlpha);\n    }\n  }\n}\nexport { ClearPass };", "map": {"version": 3, "names": ["ClearPass", "Pass", "constructor", "clearColor", "clearAlpha", "__publicField", "needsSwap", "_oldClearColor", "Color", "render", "renderer", "writeBuffer", "readBuffer", "oldClearAlpha", "getClearColor", "getClearAlpha", "setClearColor", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "renderToScreen", "clear"], "sources": ["C:\\Users\\<USER>\\Documents\\Apps_Creations\\SiahMan_Racing4Speed_WebGame\\siah-man-racing\\node_modules\\src\\postprocessing\\ClearPass.ts"], "sourcesContent": ["import { Pass } from './Pass'\nimport { Color, WebGLRenderer, WebGLRenderTarget } from 'three'\n\nclass ClearPass extends Pass {\n  public clearColor: Color | string | number\n  public clearAlpha: number\n\n  private _oldClearColor: Color\n\n  constructor(clearColor?: Color | string | number, clearAlpha?: number) {\n    super()\n    this.needsSwap = false\n    this.clearColor = clearColor !== undefined ? clearColor : 0x000000\n    this.clearAlpha = clearAlpha !== undefined ? clearAlpha : 0\n    this._oldClearColor = new Color()\n  }\n\n  public render(\n    renderer: WebGLRenderer,\n    writeBuffer: WebGLRenderTarget,\n    readBuffer: WebGLRenderTarget,\n    /*, deltaTime, maskActive */\n  ): void {\n    let oldClearAlpha\n\n    if (this.clearColor) {\n      renderer.getClearColor(this._oldClearColor)\n      oldClearAlpha = renderer.getClearAlpha()\n      renderer.setClearColor(this.clearColor, this.clearAlpha)\n    }\n\n    renderer.setRenderTarget(this.renderToScreen ? null : readBuffer)\n    renderer.clear()\n\n    if (this.clearColor) {\n      renderer.setClearColor(this._oldClearColor, oldClearAlpha)\n    }\n  }\n}\n\nexport { ClearPass }\n"], "mappings": ";;;;;;;;;;;;;AAGA,MAAMA,SAAA,SAAkBC,IAAA,CAAK;EAM3BC,YAAYC,UAAA,EAAsCC,UAAA,EAAqB;IAC/D;IANDC,aAAA;IACAA,aAAA;IAECA,aAAA;IAIN,KAAKC,SAAA,GAAY;IACZ,KAAAH,UAAA,GAAaA,UAAA,KAAe,SAAYA,UAAA,GAAa;IACrD,KAAAC,UAAA,GAAaA,UAAA,KAAe,SAAYA,UAAA,GAAa;IACrD,KAAAG,cAAA,GAAiB,IAAIC,KAAA;EAC5B;EAEOC,OACLC,QAAA,EACAC,WAAA,EACAC,UAAA,EAEM;IACF,IAAAC,aAAA;IAEJ,IAAI,KAAKV,UAAA,EAAY;MACVO,QAAA,CAAAI,aAAA,CAAc,KAAKP,cAAc;MAC1CM,aAAA,GAAgBH,QAAA,CAASK,aAAA;MACzBL,QAAA,CAASM,aAAA,CAAc,KAAKb,UAAA,EAAY,KAAKC,UAAU;IACzD;IAEAM,QAAA,CAASO,eAAA,CAAgB,KAAKC,cAAA,GAAiB,OAAON,UAAU;IAChEF,QAAA,CAASS,KAAA,CAAM;IAEf,IAAI,KAAKhB,UAAA,EAAY;MACVO,QAAA,CAAAM,aAAA,CAAc,KAAKT,cAAA,EAAgBM,aAAa;IAC3D;EACF;AACF", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}