{"ast": null, "code": "import { B<PERSON><PERSON><PERSON>eo<PERSON>, Matrix4, Vector3, Vector4, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>e } from \"three\";\nclass TeapotGeometry extends BufferGeometry {\n  constructor(size, segments, bottom, lid, body, fitLid, blinn) {\n    const teapotPatches = [/*rim*/\n    0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 3, 16, 17, 18, 7, 19, 20, 21, 11, 22, 23, 24, 15, 25, 26, 27, 18, 28, 29, 30, 21, 31, 32, 33, 24, 34, 35, 36, 27, 37, 38, 39, 30, 40, 41, 0, 33, 42, 43, 4, 36, 44, 45, 8, 39, 46, 47, 12, /*body*/\n    12, 13, 14, 15, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 15, 25, 26, 27, 51, 60, 61, 62, 55, 63, 64, 65, 59, 66, 67, 68, 27, 37, 38, 39, 62, 69, 70, 71, 65, 72, 73, 74, 68, 75, 76, 77, 39, 46, 47, 12, 71, 78, 79, 48, 74, 80, 81, 52, 77, 82, 83, 56, 56, 57, 58, 59, 84, 85, 86, 87, 88, 89, 90, 91, 92, 93, 94, 95, 59, 66, 67, 68, 87, 96, 97, 98, 91, 99, 100, 101, 95, 102, 103, 104, 68, 75, 76, 77, 98, 105, 106, 107, 101, 108, 109, 110, 104, 111, 112, 113, 77, 82, 83, 56, 107, 114, 115, 84, 110, 116, 117, 88, 113, 118, 119, 92, /*handle*/\n    120, 121, 122, 123, 124, 125, 126, 127, 128, 129, 130, 131, 132, 133, 134, 135, 123, 136, 137, 120, 127, 138, 139, 124, 131, 140, 141, 128, 135, 142, 143, 132, 132, 133, 134, 135, 144, 145, 146, 147, 148, 149, 150, 151, 68, 152, 153, 154, 135, 142, 143, 132, 147, 155, 156, 144, 151, 157, 158, 148, 154, 159, 160, 68, /*spout*/\n    161, 162, 163, 164, 165, 166, 167, 168, 169, 170, 171, 172, 173, 174, 175, 176, 164, 177, 178, 161, 168, 179, 180, 165, 172, 181, 182, 169, 176, 183, 184, 173, 173, 174, 175, 176, 185, 186, 187, 188, 189, 190, 191, 192, 193, 194, 195, 196, 176, 183, 184, 173, 188, 197, 198, 185, 192, 199, 200, 189, 196, 201, 202, 193, /*lid*/\n    203, 203, 203, 203, 204, 205, 206, 207, 208, 208, 208, 208, 209, 210, 211, 212, 203, 203, 203, 203, 207, 213, 214, 215, 208, 208, 208, 208, 212, 216, 217, 218, 203, 203, 203, 203, 215, 219, 220, 221, 208, 208, 208, 208, 218, 222, 223, 224, 203, 203, 203, 203, 221, 225, 226, 204, 208, 208, 208, 208, 224, 227, 228, 209, 209, 210, 211, 212, 229, 230, 231, 232, 233, 234, 235, 236, 237, 238, 239, 240, 212, 216, 217, 218, 232, 241, 242, 243, 236, 244, 245, 246, 240, 247, 248, 249, 218, 222, 223, 224, 243, 250, 251, 252, 246, 253, 254, 255, 249, 256, 257, 258, 224, 227, 228, 209, 252, 259, 260, 229, 255, 261, 262, 233, 258, 263, 264, 237, /*bottom*/\n    265, 265, 265, 265, 266, 267, 268, 269, 270, 271, 272, 273, 92, 119, 118, 113, 265, 265, 265, 265, 269, 274, 275, 276, 273, 277, 278, 279, 113, 112, 111, 104, 265, 265, 265, 265, 276, 280, 281, 282, 279, 283, 284, 285, 104, 103, 102, 95, 265, 265, 265, 265, 282, 286, 287, 266, 285, 288, 289, 270, 95, 94, 93, 92];\n    const teapotVertices = [1.4, 0, 2.4, 1.4, -0.784, 2.4, 0.784, -1.4, 2.4, 0, -1.4, 2.4, 1.3375, 0, 2.53125, 1.3375, -0.749, 2.53125, 0.749, -1.3375, 2.53125, 0, -1.3375, 2.53125, 1.4375, 0, 2.53125, 1.4375, -0.805, 2.53125, 0.805, -1.4375, 2.53125, 0, -1.4375, 2.53125, 1.5, 0, 2.4, 1.5, -0.84, 2.4, 0.84, -1.5, 2.4, 0, -1.5, 2.4, -0.784, -1.4, 2.4, -1.4, -0.784, 2.4, -1.4, 0, 2.4, -0.749, -1.3375, 2.53125, -1.3375, -0.749, 2.53125, -1.3375, 0, 2.53125, -0.805, -1.4375, 2.53125, -1.4375, -0.805, 2.53125, -1.4375, 0, 2.53125, -0.84, -1.5, 2.4, -1.5, -0.84, 2.4, -1.5, 0, 2.4, -1.4, 0.784, 2.4, -0.784, 1.4, 2.4, 0, 1.4, 2.4, -1.3375, 0.749, 2.53125, -0.749, 1.3375, 2.53125, 0, 1.3375, 2.53125, -1.4375, 0.805, 2.53125, -0.805, 1.4375, 2.53125, 0, 1.4375, 2.53125, -1.5, 0.84, 2.4, -0.84, 1.5, 2.4, 0, 1.5, 2.4, 0.784, 1.4, 2.4, 1.4, 0.784, 2.4, 0.749, 1.3375, 2.53125, 1.3375, 0.749, 2.53125, 0.805, 1.4375, 2.53125, 1.4375, 0.805, 2.53125, 0.84, 1.5, 2.4, 1.5, 0.84, 2.4, 1.75, 0, 1.875, 1.75, -0.98, 1.875, 0.98, -1.75, 1.875, 0, -1.75, 1.875, 2, 0, 1.35, 2, -1.12, 1.35, 1.12, -2, 1.35, 0, -2, 1.35, 2, 0, 0.9, 2, -1.12, 0.9, 1.12, -2, 0.9, 0, -2, 0.9, -0.98, -1.75, 1.875, -1.75, -0.98, 1.875, -1.75, 0, 1.875, -1.12, -2, 1.35, -2, -1.12, 1.35, -2, 0, 1.35, -1.12, -2, 0.9, -2, -1.12, 0.9, -2, 0, 0.9, -1.75, 0.98, 1.875, -0.98, 1.75, 1.875, 0, 1.75, 1.875, -2, 1.12, 1.35, -1.12, 2, 1.35, 0, 2, 1.35, -2, 1.12, 0.9, -1.12, 2, 0.9, 0, 2, 0.9, 0.98, 1.75, 1.875, 1.75, 0.98, 1.875, 1.12, 2, 1.35, 2, 1.12, 1.35, 1.12, 2, 0.9, 2, 1.12, 0.9, 2, 0, 0.45, 2, -1.12, 0.45, 1.12, -2, 0.45, 0, -2, 0.45, 1.5, 0, 0.225, 1.5, -0.84, 0.225, 0.84, -1.5, 0.225, 0, -1.5, 0.225, 1.5, 0, 0.15, 1.5, -0.84, 0.15, 0.84, -1.5, 0.15, 0, -1.5, 0.15, -1.12, -2, 0.45, -2, -1.12, 0.45, -2, 0, 0.45, -0.84, -1.5, 0.225, -1.5, -0.84, 0.225, -1.5, 0, 0.225, -0.84, -1.5, 0.15, -1.5, -0.84, 0.15, -1.5, 0, 0.15, -2, 1.12, 0.45, -1.12, 2, 0.45, 0, 2, 0.45, -1.5, 0.84, 0.225, -0.84, 1.5, 0.225, 0, 1.5, 0.225, -1.5, 0.84, 0.15, -0.84, 1.5, 0.15, 0, 1.5, 0.15, 1.12, 2, 0.45, 2, 1.12, 0.45, 0.84, 1.5, 0.225, 1.5, 0.84, 0.225, 0.84, 1.5, 0.15, 1.5, 0.84, 0.15, -1.6, 0, 2.025, -1.6, -0.3, 2.025, -1.5, -0.3, 2.25, -1.5, 0, 2.25, -2.3, 0, 2.025, -2.3, -0.3, 2.025, -2.5, -0.3, 2.25, -2.5, 0, 2.25, -2.7, 0, 2.025, -2.7, -0.3, 2.025, -3, -0.3, 2.25, -3, 0, 2.25, -2.7, 0, 1.8, -2.7, -0.3, 1.8, -3, -0.3, 1.8, -3, 0, 1.8, -1.5, 0.3, 2.25, -1.6, 0.3, 2.025, -2.5, 0.3, 2.25, -2.3, 0.3, 2.025, -3, 0.3, 2.25, -2.7, 0.3, 2.025, -3, 0.3, 1.8, -2.7, 0.3, 1.8, -2.7, 0, 1.575, -2.7, -0.3, 1.575, -3, -0.3, 1.35, -3, 0, 1.35, -2.5, 0, 1.125, -2.5, -0.3, 1.125, -2.65, -0.3, 0.9375, -2.65, 0, 0.9375, -2, -0.3, 0.9, -1.9, -0.3, 0.6, -1.9, 0, 0.6, -3, 0.3, 1.35, -2.7, 0.3, 1.575, -2.65, 0.3, 0.9375, -2.5, 0.3, 1.125, -1.9, 0.3, 0.6, -2, 0.3, 0.9, 1.7, 0, 1.425, 1.7, -0.66, 1.425, 1.7, -0.66, 0.6, 1.7, 0, 0.6, 2.6, 0, 1.425, 2.6, -0.66, 1.425, 3.1, -0.66, 0.825, 3.1, 0, 0.825, 2.3, 0, 2.1, 2.3, -0.25, 2.1, 2.4, -0.25, 2.025, 2.4, 0, 2.025, 2.7, 0, 2.4, 2.7, -0.25, 2.4, 3.3, -0.25, 2.4, 3.3, 0, 2.4, 1.7, 0.66, 0.6, 1.7, 0.66, 1.425, 3.1, 0.66, 0.825, 2.6, 0.66, 1.425, 2.4, 0.25, 2.025, 2.3, 0.25, 2.1, 3.3, 0.25, 2.4, 2.7, 0.25, 2.4, 2.8, 0, 2.475, 2.8, -0.25, 2.475, 3.525, -0.25, 2.49375, 3.525, 0, 2.49375, 2.9, 0, 2.475, 2.9, -0.15, 2.475, 3.45, -0.15, 2.5125, 3.45, 0, 2.5125, 2.8, 0, 2.4, 2.8, -0.15, 2.4, 3.2, -0.15, 2.4, 3.2, 0, 2.4, 3.525, 0.25, 2.49375, 2.8, 0.25, 2.475, 3.45, 0.15, 2.5125, 2.9, 0.15, 2.475, 3.2, 0.15, 2.4, 2.8, 0.15, 2.4, 0, 0, 3.15, 0.8, 0, 3.15, 0.8, -0.45, 3.15, 0.45, -0.8, 3.15, 0, -0.8, 3.15, 0, 0, 2.85, 0.2, 0, 2.7, 0.2, -0.112, 2.7, 0.112, -0.2, 2.7, 0, -0.2, 2.7, -0.45, -0.8, 3.15, -0.8, -0.45, 3.15, -0.8, 0, 3.15, -0.112, -0.2, 2.7, -0.2, -0.112, 2.7, -0.2, 0, 2.7, -0.8, 0.45, 3.15, -0.45, 0.8, 3.15, 0, 0.8, 3.15, -0.2, 0.112, 2.7, -0.112, 0.2, 2.7, 0, 0.2, 2.7, 0.45, 0.8, 3.15, 0.8, 0.45, 3.15, 0.112, 0.2, 2.7, 0.2, 0.112, 2.7, 0.4, 0, 2.55, 0.4, -0.224, 2.55, 0.224, -0.4, 2.55, 0, -0.4, 2.55, 1.3, 0, 2.55, 1.3, -0.728, 2.55, 0.728, -1.3, 2.55, 0, -1.3, 2.55, 1.3, 0, 2.4, 1.3, -0.728, 2.4, 0.728, -1.3, 2.4, 0, -1.3, 2.4, -0.224, -0.4, 2.55, -0.4, -0.224, 2.55, -0.4, 0, 2.55, -0.728, -1.3, 2.55, -1.3, -0.728, 2.55, -1.3, 0, 2.55, -0.728, -1.3, 2.4, -1.3, -0.728, 2.4, -1.3, 0, 2.4, -0.4, 0.224, 2.55, -0.224, 0.4, 2.55, 0, 0.4, 2.55, -1.3, 0.728, 2.55, -0.728, 1.3, 2.55, 0, 1.3, 2.55, -1.3, 0.728, 2.4, -0.728, 1.3, 2.4, 0, 1.3, 2.4, 0.224, 0.4, 2.55, 0.4, 0.224, 2.55, 0.728, 1.3, 2.55, 1.3, 0.728, 2.55, 0.728, 1.3, 2.4, 1.3, 0.728, 2.4, 0, 0, 0, 1.425, 0, 0, 1.425, 0.798, 0, 0.798, 1.425, 0, 0, 1.425, 0, 1.5, 0, 0.075, 1.5, 0.84, 0.075, 0.84, 1.5, 0.075, 0, 1.5, 0.075, -0.798, 1.425, 0, -1.425, 0.798, 0, -1.425, 0, 0, -0.84, 1.5, 0.075, -1.5, 0.84, 0.075, -1.5, 0, 0.075, -1.425, -0.798, 0, -0.798, -1.425, 0, 0, -1.425, 0, -1.5, -0.84, 0.075, -0.84, -1.5, 0.075, 0, -1.5, 0.075, 0.798, -1.425, 0, 1.425, -0.798, 0, 0.84, -1.5, 0.075, 1.5, -0.84, 0.075];\n    super();\n    size = size || 50;\n    segments = segments !== void 0 ? Math.max(2, Math.floor(segments) || 10) : 10;\n    bottom = bottom === void 0 ? true : bottom;\n    lid = lid === void 0 ? true : lid;\n    body = body === void 0 ? true : body;\n    fitLid = fitLid === void 0 ? true : fitLid;\n    const blinnScale = 1.3;\n    blinn = blinn === void 0 ? true : blinn;\n    const maxHeight = 3.15 * (blinn ? 1 : blinnScale);\n    const maxHeight2 = maxHeight / 2;\n    const trueSize = size / maxHeight2;\n    let numTriangles = bottom ? (8 * segments - 4) * segments : 0;\n    numTriangles += lid ? (16 * segments - 4) * segments : 0;\n    numTriangles += body ? 40 * segments * segments : 0;\n    const indices = new Uint32Array(numTriangles * 3);\n    let numVertices = bottom ? 4 : 0;\n    numVertices += lid ? 8 : 0;\n    numVertices += body ? 20 : 0;\n    numVertices *= (segments + 1) * (segments + 1);\n    const vertices = new Float32Array(numVertices * 3);\n    const normals = new Float32Array(numVertices * 3);\n    const uvs = new Float32Array(numVertices * 2);\n    const ms = new Matrix4();\n    ms.set(-1, 3, -3, 1, 3, -6, 3, 0, -3, 3, 0, 0, 1, 0, 0, 0);\n    const g = [];\n    let i, r, c;\n    const sp = [];\n    const tp = [];\n    const dsp = [];\n    const dtp = [];\n    const mgm = [];\n    const vert = [];\n    const sdir = [];\n    const tdir = [];\n    const norm = new Vector3();\n    let tcoord;\n    let sstep, tstep;\n    let vertPerRow;\n    let s, t, sval, tval, p;\n    let dsval = 0;\n    let dtval = 0;\n    const normOut = new Vector3();\n    let v1, v2, v3, v4;\n    const gmx = new Matrix4();\n    const tmtx = new Matrix4();\n    const vsp = new Vector4();\n    const vtp = new Vector4();\n    const vdsp = new Vector4();\n    const vdtp = new Vector4();\n    const vsdir = new Vector3();\n    const vtdir = new Vector3();\n    const mst = ms.clone();\n    mst.transpose();\n    const notDegenerate = (vtx1, vtx2, vtx3) => !(vertices[vtx1 * 3] === vertices[vtx2 * 3] && vertices[vtx1 * 3 + 1] === vertices[vtx2 * 3 + 1] && vertices[vtx1 * 3 + 2] === vertices[vtx2 * 3 + 2] || vertices[vtx1 * 3] === vertices[vtx3 * 3] && vertices[vtx1 * 3 + 1] === vertices[vtx3 * 3 + 1] && vertices[vtx1 * 3 + 2] === vertices[vtx3 * 3 + 2] || vertices[vtx2 * 3] === vertices[vtx3 * 3] && vertices[vtx2 * 3 + 1] === vertices[vtx3 * 3 + 1] && vertices[vtx2 * 3 + 2] === vertices[vtx3 * 3 + 2]);\n    for (i = 0; i < 3; i++) {\n      mgm[i] = new Matrix4();\n    }\n    const minPatches = body ? 0 : 20;\n    const maxPatches = bottom ? 32 : 28;\n    vertPerRow = segments + 1;\n    let surfCount = 0;\n    let vertCount = 0;\n    let normCount = 0;\n    let uvCount = 0;\n    let indexCount = 0;\n    for (let surf = minPatches; surf < maxPatches; surf++) {\n      if (lid || surf < 20 || surf >= 28) {\n        for (i = 0; i < 3; i++) {\n          for (r = 0; r < 4; r++) {\n            for (c = 0; c < 4; c++) {\n              g[c * 4 + r] = teapotVertices[teapotPatches[surf * 16 + r * 4 + c] * 3 + i];\n              if (fitLid && surf >= 20 && surf < 28 && i !== 2) {\n                g[c * 4 + r] *= 1.077;\n              }\n              if (!blinn && i === 2) {\n                g[c * 4 + r] *= blinnScale;\n              }\n            }\n          }\n          gmx.set(g[0], g[1], g[2], g[3], g[4], g[5], g[6], g[7], g[8], g[9], g[10], g[11], g[12], g[13], g[14], g[15]);\n          tmtx.multiplyMatrices(gmx, ms);\n          mgm[i].multiplyMatrices(mst, tmtx);\n        }\n        for (sstep = 0; sstep <= segments; sstep++) {\n          s = sstep / segments;\n          for (tstep = 0; tstep <= segments; tstep++) {\n            t = tstep / segments;\n            for (p = 4, sval = tval = 1; p--;) {\n              sp[p] = sval;\n              tp[p] = tval;\n              sval *= s;\n              tval *= t;\n              if (p === 3) {\n                dsp[p] = dtp[p] = 0;\n                dsval = dtval = 1;\n              } else {\n                dsp[p] = dsval * (3 - p);\n                dtp[p] = dtval * (3 - p);\n                dsval *= s;\n                dtval *= t;\n              }\n            }\n            vsp.fromArray(sp);\n            vtp.fromArray(tp);\n            vdsp.fromArray(dsp);\n            vdtp.fromArray(dtp);\n            for (i = 0; i < 3; i++) {\n              tcoord = vsp.clone();\n              tcoord.applyMatrix4(mgm[i]);\n              vert[i] = tcoord.dot(vtp);\n              tcoord = vdsp.clone();\n              tcoord.applyMatrix4(mgm[i]);\n              sdir[i] = tcoord.dot(vtp);\n              tcoord = vsp.clone();\n              tcoord.applyMatrix4(mgm[i]);\n              tdir[i] = tcoord.dot(vdtp);\n            }\n            vsdir.fromArray(sdir);\n            vtdir.fromArray(tdir);\n            norm.crossVectors(vtdir, vsdir);\n            norm.normalize();\n            if (vert[0] === 0 && vert[1] === 0) {\n              normOut.set(0, vert[2] > maxHeight2 ? 1 : -1, 0);\n            } else {\n              normOut.set(norm.x, norm.z, -norm.y);\n            }\n            vertices[vertCount++] = trueSize * vert[0];\n            vertices[vertCount++] = trueSize * (vert[2] - maxHeight2);\n            vertices[vertCount++] = -trueSize * vert[1];\n            normals[normCount++] = normOut.x;\n            normals[normCount++] = normOut.y;\n            normals[normCount++] = normOut.z;\n            uvs[uvCount++] = 1 - t;\n            uvs[uvCount++] = 1 - s;\n          }\n        }\n        for (sstep = 0; sstep < segments; sstep++) {\n          for (tstep = 0; tstep < segments; tstep++) {\n            v1 = surfCount * vertPerRow * vertPerRow + sstep * vertPerRow + tstep;\n            v2 = v1 + 1;\n            v3 = v2 + vertPerRow;\n            v4 = v1 + vertPerRow;\n            if (notDegenerate(v1, v2, v3)) {\n              indices[indexCount++] = v1;\n              indices[indexCount++] = v2;\n              indices[indexCount++] = v3;\n            }\n            if (notDegenerate(v1, v3, v4)) {\n              indices[indexCount++] = v1;\n              indices[indexCount++] = v3;\n              indices[indexCount++] = v4;\n            }\n          }\n        }\n        surfCount++;\n      }\n    }\n    this.setIndex(new BufferAttribute(indices, 1));\n    this.setAttribute(\"position\", new BufferAttribute(vertices, 3));\n    this.setAttribute(\"normal\", new BufferAttribute(normals, 3));\n    this.setAttribute(\"uv\", new BufferAttribute(uvs, 2));\n    this.computeBoundingSphere();\n  }\n}\nexport { TeapotGeometry };", "map": {"version": 3, "names": ["TeapotGeometry", "BufferGeometry", "constructor", "size", "segments", "bottom", "lid", "body", "fitLid", "blinn", "teapotPatches", "teapotVertices", "Math", "max", "floor", "blinnScale", "maxHeight", "maxHeight2", "trueSize", "numTriangles", "indices", "Uint32Array", "numVertices", "vertices", "Float32Array", "normals", "uvs", "ms", "Matrix4", "set", "g", "i", "r", "c", "sp", "tp", "dsp", "dtp", "mgm", "vert", "sdir", "tdir", "norm", "Vector3", "tcoord", "sstep", "tstep", "vertPerRow", "s", "t", "sval", "tval", "p", "dsval", "dtval", "normOut", "v1", "v2", "v3", "v4", "gmx", "tmtx", "vsp", "Vector4", "vtp", "vdsp", "vdtp", "vsdir", "vtdir", "mst", "clone", "transpose", "notDegenerate", "vtx1", "vtx2", "vtx3", "minPatches", "max<PERSON>atch<PERSON>", "surfCount", "vertCount", "normCount", "uvCount", "indexCount", "surf", "multiplyMatrices", "fromArray", "applyMatrix4", "dot", "crossVectors", "normalize", "x", "z", "y", "setIndex", "BufferAttribute", "setAttribute", "computeBoundingSphere"], "sources": ["C:\\Users\\<USER>\\Documents\\Apps_Creations\\SiahMan_Racing4Speed_WebGame\\siah-man-racing\\node_modules\\src\\geometries\\TeapotGeometry.js"], "sourcesContent": ["import { <PERSON><PERSON>er<PERSON><PERSON><PERSON><PERSON><PERSON>, BufferGeometry, Matrix4, Vector3, Vector4 } from 'three'\n\n/**\n * Tessellates the famous Utah teapot database by <PERSON> into triangles.\n *\n * Parameters: size = 50, segments = 10, bottom = true, lid = true, body = true,\n *   fitLid = false, blinn = true\n *\n * size is a relative scale: I've scaled the teapot to fit vertically between -1 and 1.\n * Think of it as a \"radius\".\n * segments - number of line segments to subdivide each patch edge;\n *   1 is possible but gives degenerates, so two is the real minimum.\n * bottom - boolean, if true (default) then the bottom patches are added. Some consider\n *   adding the bottom heresy, so set this to \"false\" to adhere to the One True Way.\n * lid - to remove the lid and look inside, set to true.\n * body - to remove the body and leave the lid, set this and \"bottom\" to false.\n * fitLid - the lid is a tad small in the original. This stretches it a bit so you can't\n *   see the teapot's insides through the gap.\n * blinn - <PERSON> scaled the original data vertically by dividing by about 1.3 to look\n *   nicer. If you want to see the original teapot, similar to the real-world model, set\n *   this to false. True by default.\n *   See http://en.wikipedia.org/wiki/File:Original_Utah_Teapot.jpg for the original\n *   real-world teapot (from http://en.wikipedia.org/wiki/Utah_teapot).\n *\n * Note that the bottom (the last four patches) is not flat - blame <PERSON>, not me.\n *\n * The teapot should normally be rendered as a double sided object, since for some\n * patches both sides can be seen, e.g., the gap around the lid and inside the spout.\n *\n * Segments 'n' determines the number of triangles output.\n *   Total triangles = 32*2*n*n - 8*n    [degenerates at the top and bottom cusps are deleted]\n *\n *   size_factor   # triangles\n *       1          56\n *       2         240\n *       3         552\n *       4         992\n *\n *      10        6320\n *      20       25440\n *      30       57360\n *\n * Code converted from my ancient SPD software, http://tog.acm.org/resources/SPD/\n * Created for the Udacity course \"Interactive Rendering\", http://bit.ly/ericity\n * Lesson: https://www.udacity.com/course/viewer#!/c-cs291/l-68866048/m-106482448\n * YouTube video on teapot history: https://www.youtube.com/watch?v=DxMfblPzFNc\n *\n * See https://en.wikipedia.org/wiki/Utah_teapot for the history of the teapot\n *\n */\n\nclass TeapotGeometry extends BufferGeometry {\n  constructor(size, segments, bottom, lid, body, fitLid, blinn) {\n    // 32 * 4 * 4 Bezier spline patches\n    const teapotPatches = [\n      /*rim*/\n      0,\n      1,\n      2,\n      3,\n      4,\n      5,\n      6,\n      7,\n      8,\n      9,\n      10,\n      11,\n      12,\n      13,\n      14,\n      15,\n      3,\n      16,\n      17,\n      18,\n      7,\n      19,\n      20,\n      21,\n      11,\n      22,\n      23,\n      24,\n      15,\n      25,\n      26,\n      27,\n      18,\n      28,\n      29,\n      30,\n      21,\n      31,\n      32,\n      33,\n      24,\n      34,\n      35,\n      36,\n      27,\n      37,\n      38,\n      39,\n      30,\n      40,\n      41,\n      0,\n      33,\n      42,\n      43,\n      4,\n      36,\n      44,\n      45,\n      8,\n      39,\n      46,\n      47,\n      12,\n      /*body*/\n      12,\n      13,\n      14,\n      15,\n      48,\n      49,\n      50,\n      51,\n      52,\n      53,\n      54,\n      55,\n      56,\n      57,\n      58,\n      59,\n      15,\n      25,\n      26,\n      27,\n      51,\n      60,\n      61,\n      62,\n      55,\n      63,\n      64,\n      65,\n      59,\n      66,\n      67,\n      68,\n      27,\n      37,\n      38,\n      39,\n      62,\n      69,\n      70,\n      71,\n      65,\n      72,\n      73,\n      74,\n      68,\n      75,\n      76,\n      77,\n      39,\n      46,\n      47,\n      12,\n      71,\n      78,\n      79,\n      48,\n      74,\n      80,\n      81,\n      52,\n      77,\n      82,\n      83,\n      56,\n      56,\n      57,\n      58,\n      59,\n      84,\n      85,\n      86,\n      87,\n      88,\n      89,\n      90,\n      91,\n      92,\n      93,\n      94,\n      95,\n      59,\n      66,\n      67,\n      68,\n      87,\n      96,\n      97,\n      98,\n      91,\n      99,\n      100,\n      101,\n      95,\n      102,\n      103,\n      104,\n      68,\n      75,\n      76,\n      77,\n      98,\n      105,\n      106,\n      107,\n      101,\n      108,\n      109,\n      110,\n      104,\n      111,\n      112,\n      113,\n      77,\n      82,\n      83,\n      56,\n      107,\n      114,\n      115,\n      84,\n      110,\n      116,\n      117,\n      88,\n      113,\n      118,\n      119,\n      92,\n      /*handle*/\n      120,\n      121,\n      122,\n      123,\n      124,\n      125,\n      126,\n      127,\n      128,\n      129,\n      130,\n      131,\n      132,\n      133,\n      134,\n      135,\n      123,\n      136,\n      137,\n      120,\n      127,\n      138,\n      139,\n      124,\n      131,\n      140,\n      141,\n      128,\n      135,\n      142,\n      143,\n      132,\n      132,\n      133,\n      134,\n      135,\n      144,\n      145,\n      146,\n      147,\n      148,\n      149,\n      150,\n      151,\n      68,\n      152,\n      153,\n      154,\n      135,\n      142,\n      143,\n      132,\n      147,\n      155,\n      156,\n      144,\n      151,\n      157,\n      158,\n      148,\n      154,\n      159,\n      160,\n      68,\n      /*spout*/\n      161,\n      162,\n      163,\n      164,\n      165,\n      166,\n      167,\n      168,\n      169,\n      170,\n      171,\n      172,\n      173,\n      174,\n      175,\n      176,\n      164,\n      177,\n      178,\n      161,\n      168,\n      179,\n      180,\n      165,\n      172,\n      181,\n      182,\n      169,\n      176,\n      183,\n      184,\n      173,\n      173,\n      174,\n      175,\n      176,\n      185,\n      186,\n      187,\n      188,\n      189,\n      190,\n      191,\n      192,\n      193,\n      194,\n      195,\n      196,\n      176,\n      183,\n      184,\n      173,\n      188,\n      197,\n      198,\n      185,\n      192,\n      199,\n      200,\n      189,\n      196,\n      201,\n      202,\n      193,\n      /*lid*/\n      203,\n      203,\n      203,\n      203,\n      204,\n      205,\n      206,\n      207,\n      208,\n      208,\n      208,\n      208,\n      209,\n      210,\n      211,\n      212,\n      203,\n      203,\n      203,\n      203,\n      207,\n      213,\n      214,\n      215,\n      208,\n      208,\n      208,\n      208,\n      212,\n      216,\n      217,\n      218,\n      203,\n      203,\n      203,\n      203,\n      215,\n      219,\n      220,\n      221,\n      208,\n      208,\n      208,\n      208,\n      218,\n      222,\n      223,\n      224,\n      203,\n      203,\n      203,\n      203,\n      221,\n      225,\n      226,\n      204,\n      208,\n      208,\n      208,\n      208,\n      224,\n      227,\n      228,\n      209,\n      209,\n      210,\n      211,\n      212,\n      229,\n      230,\n      231,\n      232,\n      233,\n      234,\n      235,\n      236,\n      237,\n      238,\n      239,\n      240,\n      212,\n      216,\n      217,\n      218,\n      232,\n      241,\n      242,\n      243,\n      236,\n      244,\n      245,\n      246,\n      240,\n      247,\n      248,\n      249,\n      218,\n      222,\n      223,\n      224,\n      243,\n      250,\n      251,\n      252,\n      246,\n      253,\n      254,\n      255,\n      249,\n      256,\n      257,\n      258,\n      224,\n      227,\n      228,\n      209,\n      252,\n      259,\n      260,\n      229,\n      255,\n      261,\n      262,\n      233,\n      258,\n      263,\n      264,\n      237,\n      /*bottom*/\n      265,\n      265,\n      265,\n      265,\n      266,\n      267,\n      268,\n      269,\n      270,\n      271,\n      272,\n      273,\n      92,\n      119,\n      118,\n      113,\n      265,\n      265,\n      265,\n      265,\n      269,\n      274,\n      275,\n      276,\n      273,\n      277,\n      278,\n      279,\n      113,\n      112,\n      111,\n      104,\n      265,\n      265,\n      265,\n      265,\n      276,\n      280,\n      281,\n      282,\n      279,\n      283,\n      284,\n      285,\n      104,\n      103,\n      102,\n      95,\n      265,\n      265,\n      265,\n      265,\n      282,\n      286,\n      287,\n      266,\n      285,\n      288,\n      289,\n      270,\n      95,\n      94,\n      93,\n      92,\n    ]\n\n    const teapotVertices = [\n      1.4,\n      0,\n      2.4,\n      1.4,\n      -0.784,\n      2.4,\n      0.784,\n      -1.4,\n      2.4,\n      0,\n      -1.4,\n      2.4,\n      1.3375,\n      0,\n      2.53125,\n      1.3375,\n      -0.749,\n      2.53125,\n      0.749,\n      -1.3375,\n      2.53125,\n      0,\n      -1.3375,\n      2.53125,\n      1.4375,\n      0,\n      2.53125,\n      1.4375,\n      -0.805,\n      2.53125,\n      0.805,\n      -1.4375,\n      2.53125,\n      0,\n      -1.4375,\n      2.53125,\n      1.5,\n      0,\n      2.4,\n      1.5,\n      -0.84,\n      2.4,\n      0.84,\n      -1.5,\n      2.4,\n      0,\n      -1.5,\n      2.4,\n      -0.784,\n      -1.4,\n      2.4,\n      -1.4,\n      -0.784,\n      2.4,\n      -1.4,\n      0,\n      2.4,\n      -0.749,\n      -1.3375,\n      2.53125,\n      -1.3375,\n      -0.749,\n      2.53125,\n      -1.3375,\n      0,\n      2.53125,\n      -0.805,\n      -1.4375,\n      2.53125,\n      -1.4375,\n      -0.805,\n      2.53125,\n      -1.4375,\n      0,\n      2.53125,\n      -0.84,\n      -1.5,\n      2.4,\n      -1.5,\n      -0.84,\n      2.4,\n      -1.5,\n      0,\n      2.4,\n      -1.4,\n      0.784,\n      2.4,\n      -0.784,\n      1.4,\n      2.4,\n      0,\n      1.4,\n      2.4,\n      -1.3375,\n      0.749,\n      2.53125,\n      -0.749,\n      1.3375,\n      2.53125,\n      0,\n      1.3375,\n      2.53125,\n      -1.4375,\n      0.805,\n      2.53125,\n      -0.805,\n      1.4375,\n      2.53125,\n      0,\n      1.4375,\n      2.53125,\n      -1.5,\n      0.84,\n      2.4,\n      -0.84,\n      1.5,\n      2.4,\n      0,\n      1.5,\n      2.4,\n      0.784,\n      1.4,\n      2.4,\n      1.4,\n      0.784,\n      2.4,\n      0.749,\n      1.3375,\n      2.53125,\n      1.3375,\n      0.749,\n      2.53125,\n      0.805,\n      1.4375,\n      2.53125,\n      1.4375,\n      0.805,\n      2.53125,\n      0.84,\n      1.5,\n      2.4,\n      1.5,\n      0.84,\n      2.4,\n      1.75,\n      0,\n      1.875,\n      1.75,\n      -0.98,\n      1.875,\n      0.98,\n      -1.75,\n      1.875,\n      0,\n      -1.75,\n      1.875,\n      2,\n      0,\n      1.35,\n      2,\n      -1.12,\n      1.35,\n      1.12,\n      -2,\n      1.35,\n      0,\n      -2,\n      1.35,\n      2,\n      0,\n      0.9,\n      2,\n      -1.12,\n      0.9,\n      1.12,\n      -2,\n      0.9,\n      0,\n      -2,\n      0.9,\n      -0.98,\n      -1.75,\n      1.875,\n      -1.75,\n      -0.98,\n      1.875,\n      -1.75,\n      0,\n      1.875,\n      -1.12,\n      -2,\n      1.35,\n      -2,\n      -1.12,\n      1.35,\n      -2,\n      0,\n      1.35,\n      -1.12,\n      -2,\n      0.9,\n      -2,\n      -1.12,\n      0.9,\n      -2,\n      0,\n      0.9,\n      -1.75,\n      0.98,\n      1.875,\n      -0.98,\n      1.75,\n      1.875,\n      0,\n      1.75,\n      1.875,\n      -2,\n      1.12,\n      1.35,\n      -1.12,\n      2,\n      1.35,\n      0,\n      2,\n      1.35,\n      -2,\n      1.12,\n      0.9,\n      -1.12,\n      2,\n      0.9,\n      0,\n      2,\n      0.9,\n      0.98,\n      1.75,\n      1.875,\n      1.75,\n      0.98,\n      1.875,\n      1.12,\n      2,\n      1.35,\n      2,\n      1.12,\n      1.35,\n      1.12,\n      2,\n      0.9,\n      2,\n      1.12,\n      0.9,\n      2,\n      0,\n      0.45,\n      2,\n      -1.12,\n      0.45,\n      1.12,\n      -2,\n      0.45,\n      0,\n      -2,\n      0.45,\n      1.5,\n      0,\n      0.225,\n      1.5,\n      -0.84,\n      0.225,\n      0.84,\n      -1.5,\n      0.225,\n      0,\n      -1.5,\n      0.225,\n      1.5,\n      0,\n      0.15,\n      1.5,\n      -0.84,\n      0.15,\n      0.84,\n      -1.5,\n      0.15,\n      0,\n      -1.5,\n      0.15,\n      -1.12,\n      -2,\n      0.45,\n      -2,\n      -1.12,\n      0.45,\n      -2,\n      0,\n      0.45,\n      -0.84,\n      -1.5,\n      0.225,\n      -1.5,\n      -0.84,\n      0.225,\n      -1.5,\n      0,\n      0.225,\n      -0.84,\n      -1.5,\n      0.15,\n      -1.5,\n      -0.84,\n      0.15,\n      -1.5,\n      0,\n      0.15,\n      -2,\n      1.12,\n      0.45,\n      -1.12,\n      2,\n      0.45,\n      0,\n      2,\n      0.45,\n      -1.5,\n      0.84,\n      0.225,\n      -0.84,\n      1.5,\n      0.225,\n      0,\n      1.5,\n      0.225,\n      -1.5,\n      0.84,\n      0.15,\n      -0.84,\n      1.5,\n      0.15,\n      0,\n      1.5,\n      0.15,\n      1.12,\n      2,\n      0.45,\n      2,\n      1.12,\n      0.45,\n      0.84,\n      1.5,\n      0.225,\n      1.5,\n      0.84,\n      0.225,\n      0.84,\n      1.5,\n      0.15,\n      1.5,\n      0.84,\n      0.15,\n      -1.6,\n      0,\n      2.025,\n      -1.6,\n      -0.3,\n      2.025,\n      -1.5,\n      -0.3,\n      2.25,\n      -1.5,\n      0,\n      2.25,\n      -2.3,\n      0,\n      2.025,\n      -2.3,\n      -0.3,\n      2.025,\n      -2.5,\n      -0.3,\n      2.25,\n      -2.5,\n      0,\n      2.25,\n      -2.7,\n      0,\n      2.025,\n      -2.7,\n      -0.3,\n      2.025,\n      -3,\n      -0.3,\n      2.25,\n      -3,\n      0,\n      2.25,\n      -2.7,\n      0,\n      1.8,\n      -2.7,\n      -0.3,\n      1.8,\n      -3,\n      -0.3,\n      1.8,\n      -3,\n      0,\n      1.8,\n      -1.5,\n      0.3,\n      2.25,\n      -1.6,\n      0.3,\n      2.025,\n      -2.5,\n      0.3,\n      2.25,\n      -2.3,\n      0.3,\n      2.025,\n      -3,\n      0.3,\n      2.25,\n      -2.7,\n      0.3,\n      2.025,\n      -3,\n      0.3,\n      1.8,\n      -2.7,\n      0.3,\n      1.8,\n      -2.7,\n      0,\n      1.575,\n      -2.7,\n      -0.3,\n      1.575,\n      -3,\n      -0.3,\n      1.35,\n      -3,\n      0,\n      1.35,\n      -2.5,\n      0,\n      1.125,\n      -2.5,\n      -0.3,\n      1.125,\n      -2.65,\n      -0.3,\n      0.9375,\n      -2.65,\n      0,\n      0.9375,\n      -2,\n      -0.3,\n      0.9,\n      -1.9,\n      -0.3,\n      0.6,\n      -1.9,\n      0,\n      0.6,\n      -3,\n      0.3,\n      1.35,\n      -2.7,\n      0.3,\n      1.575,\n      -2.65,\n      0.3,\n      0.9375,\n      -2.5,\n      0.3,\n      1.125,\n      -1.9,\n      0.3,\n      0.6,\n      -2,\n      0.3,\n      0.9,\n      1.7,\n      0,\n      1.425,\n      1.7,\n      -0.66,\n      1.425,\n      1.7,\n      -0.66,\n      0.6,\n      1.7,\n      0,\n      0.6,\n      2.6,\n      0,\n      1.425,\n      2.6,\n      -0.66,\n      1.425,\n      3.1,\n      -0.66,\n      0.825,\n      3.1,\n      0,\n      0.825,\n      2.3,\n      0,\n      2.1,\n      2.3,\n      -0.25,\n      2.1,\n      2.4,\n      -0.25,\n      2.025,\n      2.4,\n      0,\n      2.025,\n      2.7,\n      0,\n      2.4,\n      2.7,\n      -0.25,\n      2.4,\n      3.3,\n      -0.25,\n      2.4,\n      3.3,\n      0,\n      2.4,\n      1.7,\n      0.66,\n      0.6,\n      1.7,\n      0.66,\n      1.425,\n      3.1,\n      0.66,\n      0.825,\n      2.6,\n      0.66,\n      1.425,\n      2.4,\n      0.25,\n      2.025,\n      2.3,\n      0.25,\n      2.1,\n      3.3,\n      0.25,\n      2.4,\n      2.7,\n      0.25,\n      2.4,\n      2.8,\n      0,\n      2.475,\n      2.8,\n      -0.25,\n      2.475,\n      3.525,\n      -0.25,\n      2.49375,\n      3.525,\n      0,\n      2.49375,\n      2.9,\n      0,\n      2.475,\n      2.9,\n      -0.15,\n      2.475,\n      3.45,\n      -0.15,\n      2.5125,\n      3.45,\n      0,\n      2.5125,\n      2.8,\n      0,\n      2.4,\n      2.8,\n      -0.15,\n      2.4,\n      3.2,\n      -0.15,\n      2.4,\n      3.2,\n      0,\n      2.4,\n      3.525,\n      0.25,\n      2.49375,\n      2.8,\n      0.25,\n      2.475,\n      3.45,\n      0.15,\n      2.5125,\n      2.9,\n      0.15,\n      2.475,\n      3.2,\n      0.15,\n      2.4,\n      2.8,\n      0.15,\n      2.4,\n      0,\n      0,\n      3.15,\n      0.8,\n      0,\n      3.15,\n      0.8,\n      -0.45,\n      3.15,\n      0.45,\n      -0.8,\n      3.15,\n      0,\n      -0.8,\n      3.15,\n      0,\n      0,\n      2.85,\n      0.2,\n      0,\n      2.7,\n      0.2,\n      -0.112,\n      2.7,\n      0.112,\n      -0.2,\n      2.7,\n      0,\n      -0.2,\n      2.7,\n      -0.45,\n      -0.8,\n      3.15,\n      -0.8,\n      -0.45,\n      3.15,\n      -0.8,\n      0,\n      3.15,\n      -0.112,\n      -0.2,\n      2.7,\n      -0.2,\n      -0.112,\n      2.7,\n      -0.2,\n      0,\n      2.7,\n      -0.8,\n      0.45,\n      3.15,\n      -0.45,\n      0.8,\n      3.15,\n      0,\n      0.8,\n      3.15,\n      -0.2,\n      0.112,\n      2.7,\n      -0.112,\n      0.2,\n      2.7,\n      0,\n      0.2,\n      2.7,\n      0.45,\n      0.8,\n      3.15,\n      0.8,\n      0.45,\n      3.15,\n      0.112,\n      0.2,\n      2.7,\n      0.2,\n      0.112,\n      2.7,\n      0.4,\n      0,\n      2.55,\n      0.4,\n      -0.224,\n      2.55,\n      0.224,\n      -0.4,\n      2.55,\n      0,\n      -0.4,\n      2.55,\n      1.3,\n      0,\n      2.55,\n      1.3,\n      -0.728,\n      2.55,\n      0.728,\n      -1.3,\n      2.55,\n      0,\n      -1.3,\n      2.55,\n      1.3,\n      0,\n      2.4,\n      1.3,\n      -0.728,\n      2.4,\n      0.728,\n      -1.3,\n      2.4,\n      0,\n      -1.3,\n      2.4,\n      -0.224,\n      -0.4,\n      2.55,\n      -0.4,\n      -0.224,\n      2.55,\n      -0.4,\n      0,\n      2.55,\n      -0.728,\n      -1.3,\n      2.55,\n      -1.3,\n      -0.728,\n      2.55,\n      -1.3,\n      0,\n      2.55,\n      -0.728,\n      -1.3,\n      2.4,\n      -1.3,\n      -0.728,\n      2.4,\n      -1.3,\n      0,\n      2.4,\n      -0.4,\n      0.224,\n      2.55,\n      -0.224,\n      0.4,\n      2.55,\n      0,\n      0.4,\n      2.55,\n      -1.3,\n      0.728,\n      2.55,\n      -0.728,\n      1.3,\n      2.55,\n      0,\n      1.3,\n      2.55,\n      -1.3,\n      0.728,\n      2.4,\n      -0.728,\n      1.3,\n      2.4,\n      0,\n      1.3,\n      2.4,\n      0.224,\n      0.4,\n      2.55,\n      0.4,\n      0.224,\n      2.55,\n      0.728,\n      1.3,\n      2.55,\n      1.3,\n      0.728,\n      2.55,\n      0.728,\n      1.3,\n      2.4,\n      1.3,\n      0.728,\n      2.4,\n      0,\n      0,\n      0,\n      1.425,\n      0,\n      0,\n      1.425,\n      0.798,\n      0,\n      0.798,\n      1.425,\n      0,\n      0,\n      1.425,\n      0,\n      1.5,\n      0,\n      0.075,\n      1.5,\n      0.84,\n      0.075,\n      0.84,\n      1.5,\n      0.075,\n      0,\n      1.5,\n      0.075,\n      -0.798,\n      1.425,\n      0,\n      -1.425,\n      0.798,\n      0,\n      -1.425,\n      0,\n      0,\n      -0.84,\n      1.5,\n      0.075,\n      -1.5,\n      0.84,\n      0.075,\n      -1.5,\n      0,\n      0.075,\n      -1.425,\n      -0.798,\n      0,\n      -0.798,\n      -1.425,\n      0,\n      0,\n      -1.425,\n      0,\n      -1.5,\n      -0.84,\n      0.075,\n      -0.84,\n      -1.5,\n      0.075,\n      0,\n      -1.5,\n      0.075,\n      0.798,\n      -1.425,\n      0,\n      1.425,\n      -0.798,\n      0,\n      0.84,\n      -1.5,\n      0.075,\n      1.5,\n      -0.84,\n      0.075,\n    ]\n\n    super()\n\n    size = size || 50\n\n    // number of segments per patch\n    segments = segments !== undefined ? Math.max(2, Math.floor(segments) || 10) : 10\n\n    // which parts should be visible\n    bottom = bottom === undefined ? true : bottom\n    lid = lid === undefined ? true : lid\n    body = body === undefined ? true : body\n\n    // Should the lid be snug? It's not traditional, but we make it snug by default\n    fitLid = fitLid === undefined ? true : fitLid\n\n    // Jim Blinn scaled the teapot down in size by about 1.3 for\n    // some rendering tests. He liked the new proportions that he kept\n    // the data in this form. The model was distributed with these new\n    // proportions and became the norm. Trivia: comparing images of the\n    // real teapot and the computer model, the ratio for the bowl of the\n    // real teapot is more like 1.25, but since 1.3 is the traditional\n    // value given, we use it here.\n    const blinnScale = 1.3\n    blinn = blinn === undefined ? true : blinn\n\n    // scale the size to be the real scaling factor\n    const maxHeight = 3.15 * (blinn ? 1 : blinnScale)\n\n    const maxHeight2 = maxHeight / 2\n    const trueSize = size / maxHeight2\n\n    // Number of elements depends on what is needed. Subtract degenerate\n    // triangles at tip of bottom and lid out in advance.\n    let numTriangles = bottom ? (8 * segments - 4) * segments : 0\n    numTriangles += lid ? (16 * segments - 4) * segments : 0\n    numTriangles += body ? 40 * segments * segments : 0\n\n    const indices = new Uint32Array(numTriangles * 3)\n\n    let numVertices = bottom ? 4 : 0\n    numVertices += lid ? 8 : 0\n    numVertices += body ? 20 : 0\n    numVertices *= (segments + 1) * (segments + 1)\n\n    const vertices = new Float32Array(numVertices * 3)\n    const normals = new Float32Array(numVertices * 3)\n    const uvs = new Float32Array(numVertices * 2)\n\n    // Bezier form\n    const ms = new Matrix4()\n    ms.set(-1.0, 3.0, -3.0, 1.0, 3.0, -6.0, 3.0, 0.0, -3.0, 3.0, 0.0, 0.0, 1.0, 0.0, 0.0, 0.0)\n\n    const g = []\n    let i, r, c\n\n    const sp = []\n    const tp = []\n    const dsp = []\n    const dtp = []\n\n    // M * G * M matrix, sort of see\n    // http://www.cs.helsinki.fi/group/goa/mallinnus/curves/surfaces.html\n    const mgm = []\n\n    const vert = []\n    const sdir = []\n    const tdir = []\n\n    const norm = new Vector3()\n\n    let tcoord\n\n    let sstep, tstep\n    let vertPerRow\n\n    let s, t, sval, tval, p\n    let dsval = 0\n    let dtval = 0\n\n    const normOut = new Vector3()\n    let v1, v2, v3, v4\n\n    const gmx = new Matrix4()\n    const tmtx = new Matrix4()\n\n    const vsp = new Vector4()\n    const vtp = new Vector4()\n    const vdsp = new Vector4()\n    const vdtp = new Vector4()\n\n    const vsdir = new Vector3()\n    const vtdir = new Vector3()\n\n    const mst = ms.clone()\n    mst.transpose()\n\n    // internal function: test if triangle has any matching vertices;\n    // if so, don't save triangle, since it won't display anything.\n    const notDegenerate = (\n      vtx1,\n      vtx2,\n      vtx3, // if any vertex matches, return false\n    ) =>\n      !(\n        (vertices[vtx1 * 3] === vertices[vtx2 * 3] &&\n          vertices[vtx1 * 3 + 1] === vertices[vtx2 * 3 + 1] &&\n          vertices[vtx1 * 3 + 2] === vertices[vtx2 * 3 + 2]) ||\n        (vertices[vtx1 * 3] === vertices[vtx3 * 3] &&\n          vertices[vtx1 * 3 + 1] === vertices[vtx3 * 3 + 1] &&\n          vertices[vtx1 * 3 + 2] === vertices[vtx3 * 3 + 2]) ||\n        (vertices[vtx2 * 3] === vertices[vtx3 * 3] &&\n          vertices[vtx2 * 3 + 1] === vertices[vtx3 * 3 + 1] &&\n          vertices[vtx2 * 3 + 2] === vertices[vtx3 * 3 + 2])\n      )\n\n    for (i = 0; i < 3; i++) {\n      mgm[i] = new Matrix4()\n    }\n\n    const minPatches = body ? 0 : 20\n    const maxPatches = bottom ? 32 : 28\n\n    vertPerRow = segments + 1\n\n    let surfCount = 0\n\n    let vertCount = 0\n    let normCount = 0\n    let uvCount = 0\n\n    let indexCount = 0\n\n    for (let surf = minPatches; surf < maxPatches; surf++) {\n      // lid is in the middle of the data, patches 20-27,\n      // so ignore it for this part of the loop if the lid is not desired\n      if (lid || surf < 20 || surf >= 28) {\n        // get M * G * M matrix for x,y,z\n        for (i = 0; i < 3; i++) {\n          // get control patches\n          for (r = 0; r < 4; r++) {\n            for (c = 0; c < 4; c++) {\n              // transposed\n              g[c * 4 + r] = teapotVertices[teapotPatches[surf * 16 + r * 4 + c] * 3 + i]\n\n              // is the lid to be made larger, and is this a point on the lid\n              // that is X or Y?\n              if (fitLid && surf >= 20 && surf < 28 && i !== 2) {\n                // increase XY size by 7.7%, found empirically. I don't\n                // increase Z so that the teapot will continue to fit in the\n                // space -1 to 1 for Y (Y is up for the final model).\n                g[c * 4 + r] *= 1.077\n              }\n\n              // Blinn \"fixed\" the teapot by dividing Z by blinnScale, and that's the\n              // data we now use. The original teapot is taller. Fix it:\n              if (!blinn && i === 2) {\n                g[c * 4 + r] *= blinnScale\n              }\n            }\n          }\n\n          gmx.set(g[0], g[1], g[2], g[3], g[4], g[5], g[6], g[7], g[8], g[9], g[10], g[11], g[12], g[13], g[14], g[15])\n\n          tmtx.multiplyMatrices(gmx, ms)\n          mgm[i].multiplyMatrices(mst, tmtx)\n        }\n\n        // step along, get points, and output\n        for (sstep = 0; sstep <= segments; sstep++) {\n          s = sstep / segments\n\n          for (tstep = 0; tstep <= segments; tstep++) {\n            t = tstep / segments\n\n            // point from basis\n            // get power vectors and their derivatives\n            for (p = 4, sval = tval = 1.0; p--; ) {\n              sp[p] = sval\n              tp[p] = tval\n              sval *= s\n              tval *= t\n\n              if (p === 3) {\n                dsp[p] = dtp[p] = 0.0\n                dsval = dtval = 1.0\n              } else {\n                dsp[p] = dsval * (3 - p)\n                dtp[p] = dtval * (3 - p)\n                dsval *= s\n                dtval *= t\n              }\n            }\n\n            vsp.fromArray(sp)\n            vtp.fromArray(tp)\n            vdsp.fromArray(dsp)\n            vdtp.fromArray(dtp)\n\n            // do for x,y,z\n            for (i = 0; i < 3; i++) {\n              // multiply power vectors times matrix to get value\n              tcoord = vsp.clone()\n              tcoord.applyMatrix4(mgm[i])\n              vert[i] = tcoord.dot(vtp)\n\n              // get s and t tangent vectors\n              tcoord = vdsp.clone()\n              tcoord.applyMatrix4(mgm[i])\n              sdir[i] = tcoord.dot(vtp)\n\n              tcoord = vsp.clone()\n              tcoord.applyMatrix4(mgm[i])\n              tdir[i] = tcoord.dot(vdtp)\n            }\n\n            // find normal\n            vsdir.fromArray(sdir)\n            vtdir.fromArray(tdir)\n            norm.crossVectors(vtdir, vsdir)\n            norm.normalize()\n\n            // if X and Z length is 0, at the cusp, so point the normal up or down, depending on patch number\n            if (vert[0] === 0 && vert[1] === 0) {\n              // if above the middle of the teapot, normal points up, else down\n              normOut.set(0, vert[2] > maxHeight2 ? 1 : -1, 0)\n            } else {\n              // standard output: rotate on X axis\n              normOut.set(norm.x, norm.z, -norm.y)\n            }\n\n            // store it all\n            vertices[vertCount++] = trueSize * vert[0]\n            vertices[vertCount++] = trueSize * (vert[2] - maxHeight2)\n            vertices[vertCount++] = -trueSize * vert[1]\n\n            normals[normCount++] = normOut.x\n            normals[normCount++] = normOut.y\n            normals[normCount++] = normOut.z\n\n            uvs[uvCount++] = 1 - t\n            uvs[uvCount++] = 1 - s\n          }\n        }\n\n        // save the faces\n        for (sstep = 0; sstep < segments; sstep++) {\n          for (tstep = 0; tstep < segments; tstep++) {\n            v1 = surfCount * vertPerRow * vertPerRow + sstep * vertPerRow + tstep\n            v2 = v1 + 1\n            v3 = v2 + vertPerRow\n            v4 = v1 + vertPerRow\n\n            // Normals and UVs cannot be shared. Without clone(), you can see the consequences\n            // of sharing if you call geometry.applyMatrix4( matrix ).\n            if (notDegenerate(v1, v2, v3)) {\n              indices[indexCount++] = v1\n              indices[indexCount++] = v2\n              indices[indexCount++] = v3\n            }\n\n            if (notDegenerate(v1, v3, v4)) {\n              indices[indexCount++] = v1\n              indices[indexCount++] = v3\n              indices[indexCount++] = v4\n            }\n          }\n        }\n\n        // increment only if a surface was used\n        surfCount++\n      }\n    }\n\n    this.setIndex(new BufferAttribute(indices, 1))\n    this.setAttribute('position', new BufferAttribute(vertices, 3))\n    this.setAttribute('normal', new BufferAttribute(normals, 3))\n    this.setAttribute('uv', new BufferAttribute(uvs, 2))\n\n    this.computeBoundingSphere()\n  }\n}\n\nexport { TeapotGeometry }\n"], "mappings": ";AAmDA,MAAMA,cAAA,SAAuBC,cAAA,CAAe;EAC1CC,YAAYC,IAAA,EAAMC,QAAA,EAAUC,MAAA,EAAQC,GAAA,EAAKC,IAAA,EAAMC,MAAA,EAAQC,KAAA,EAAO;IAE5D,MAAMC,aAAA,GAAgB;IAEpB,GACA,GACA,GACA,GACA,GACA,GACA,GACA,GACA,GACA,GACA,IACA,IACA,IACA,IACA,IACA,IACA,GACA,IACA,IACA,IACA,GACA,IACA,IACA,IACA,IACA,IACA,IACA,IACA,IACA,IACA,IACA,IACA,IACA,IACA,IACA,IACA,IACA,IACA,IACA,IACA,IACA,IACA,IACA,IACA,IACA,IACA,IACA,IACA,IACA,IACA,IACA,GACA,IACA,IACA,IACA,GACA,IACA,IACA,IACA,GACA,IACA,IACA,IACA;IAEA,IACA,IACA,IACA,IACA,IACA,IACA,IACA,IACA,IACA,IACA,IACA,IACA,IACA,IACA,IACA,IACA,IACA,IACA,IACA,IACA,IACA,IACA,IACA,IACA,IACA,IACA,IACA,IACA,IACA,IACA,IACA,IACA,IACA,IACA,IACA,IACA,IACA,IACA,IACA,IACA,IACA,IACA,IACA,IACA,IACA,IACA,IACA,IACA,IACA,IACA,IACA,IACA,IACA,IACA,IACA,IACA,IACA,IACA,IACA,IACA,IACA,IACA,IACA,IACA,IACA,IACA,IACA,IACA,IACA,IACA,IACA,IACA,IACA,IACA,IACA,IACA,IACA,IACA,IACA,IACA,IACA,IACA,IACA,IACA,IACA,IACA,IACA,IACA,IACA,IACA,KACA,KACA,IACA,KACA,KACA,KACA,IACA,IACA,IACA,IACA,IACA,KACA,KACA,KACA,KACA,KACA,KACA,KACA,KACA,KACA,KACA,KACA,IACA,IACA,IACA,IACA,KACA,KACA,KACA,IACA,KACA,KACA,KACA,IACA,KACA,KACA,KACA;IAEA,KACA,KACA,KACA,KACA,KACA,KACA,KACA,KACA,KACA,KACA,KACA,KACA,KACA,KACA,KACA,KACA,KACA,KACA,KACA,KACA,KACA,KACA,KACA,KACA,KACA,KACA,KACA,KACA,KACA,KACA,KACA,KACA,KACA,KACA,KACA,KACA,KACA,KACA,KACA,KACA,KACA,KACA,KACA,KACA,IACA,KACA,KACA,KACA,KACA,KACA,KACA,KACA,KACA,KACA,KACA,KACA,KACA,KACA,KACA,KACA,KACA,KACA,KACA;IAEA,KACA,KACA,KACA,KACA,KACA,KACA,KACA,KACA,KACA,KACA,KACA,KACA,KACA,KACA,KACA,KACA,KACA,KACA,KACA,KACA,KACA,KACA,KACA,KACA,KACA,KACA,KACA,KACA,KACA,KACA,KACA,KACA,KACA,KACA,KACA,KACA,KACA,KACA,KACA,KACA,KACA,KACA,KACA,KACA,KACA,KACA,KACA,KACA,KACA,KACA,KACA,KACA,KACA,KACA,KACA,KACA,KACA,KACA,KACA,KACA,KACA,KACA,KACA;IAEA,KACA,KACA,KACA,KACA,KACA,KACA,KACA,KACA,KACA,KACA,KACA,KACA,KACA,KACA,KACA,KACA,KACA,KACA,KACA,KACA,KACA,KACA,KACA,KACA,KACA,KACA,KACA,KACA,KACA,KACA,KACA,KACA,KACA,KACA,KACA,KACA,KACA,KACA,KACA,KACA,KACA,KACA,KACA,KACA,KACA,KACA,KACA,KACA,KACA,KACA,KACA,KACA,KACA,KACA,KACA,KACA,KACA,KACA,KACA,KACA,KACA,KACA,KACA,KACA,KACA,KACA,KACA,KACA,KACA,KACA,KACA,KACA,KACA,KACA,KACA,KACA,KACA,KACA,KACA,KACA,KACA,KACA,KACA,KACA,KACA,KACA,KACA,KACA,KACA,KACA,KACA,KACA,KACA,KACA,KACA,KACA,KACA,KACA,KACA,KACA,KACA,KACA,KACA,KACA,KACA,KACA,KACA,KACA,KACA,KACA,KACA,KACA,KACA,KACA,KACA,KACA,KACA,KACA,KACA,KACA,KACA,KACA,KACA,KACA,KACA,KACA,KACA;IAEA,KACA,KACA,KACA,KACA,KACA,KACA,KACA,KACA,KACA,KACA,KACA,KACA,IACA,KACA,KACA,KACA,KACA,KACA,KACA,KACA,KACA,KACA,KACA,KACA,KACA,KACA,KACA,KACA,KACA,KACA,KACA,KACA,KACA,KACA,KACA,KACA,KACA,KACA,KACA,KACA,KACA,KACA,KACA,KACA,KACA,KACA,KACA,IACA,KACA,KACA,KACA,KACA,KACA,KACA,KACA,KACA,KACA,KACA,KACA,KACA,IACA,IACA,IACA,GACD;IAED,MAAMC,cAAA,GAAiB,CACrB,KACA,GACA,KACA,KACA,QACA,KACA,OACA,MACA,KACA,GACA,MACA,KACA,QACA,GACA,SACA,QACA,QACA,SACA,OACA,SACA,SACA,GACA,SACA,SACA,QACA,GACA,SACA,QACA,QACA,SACA,OACA,SACA,SACA,GACA,SACA,SACA,KACA,GACA,KACA,KACA,OACA,KACA,MACA,MACA,KACA,GACA,MACA,KACA,QACA,MACA,KACA,MACA,QACA,KACA,MACA,GACA,KACA,QACA,SACA,SACA,SACA,QACA,SACA,SACA,GACA,SACA,QACA,SACA,SACA,SACA,QACA,SACA,SACA,GACA,SACA,OACA,MACA,KACA,MACA,OACA,KACA,MACA,GACA,KACA,MACA,OACA,KACA,QACA,KACA,KACA,GACA,KACA,KACA,SACA,OACA,SACA,QACA,QACA,SACA,GACA,QACA,SACA,SACA,OACA,SACA,QACA,QACA,SACA,GACA,QACA,SACA,MACA,MACA,KACA,OACA,KACA,KACA,GACA,KACA,KACA,OACA,KACA,KACA,KACA,OACA,KACA,OACA,QACA,SACA,QACA,OACA,SACA,OACA,QACA,SACA,QACA,OACA,SACA,MACA,KACA,KACA,KACA,MACA,KACA,MACA,GACA,OACA,MACA,OACA,OACA,MACA,OACA,OACA,GACA,OACA,OACA,GACA,GACA,MACA,GACA,OACA,MACA,MACA,IACA,MACA,GACA,IACA,MACA,GACA,GACA,KACA,GACA,OACA,KACA,MACA,IACA,KACA,GACA,IACA,KACA,OACA,OACA,OACA,OACA,OACA,OACA,OACA,GACA,OACA,OACA,IACA,MACA,IACA,OACA,MACA,IACA,GACA,MACA,OACA,IACA,KACA,IACA,OACA,KACA,IACA,GACA,KACA,OACA,MACA,OACA,OACA,MACA,OACA,GACA,MACA,OACA,IACA,MACA,MACA,OACA,GACA,MACA,GACA,GACA,MACA,IACA,MACA,KACA,OACA,GACA,KACA,GACA,GACA,KACA,MACA,MACA,OACA,MACA,MACA,OACA,MACA,GACA,MACA,GACA,MACA,MACA,MACA,GACA,KACA,GACA,MACA,KACA,GACA,GACA,MACA,GACA,OACA,MACA,MACA,IACA,MACA,GACA,IACA,MACA,KACA,GACA,OACA,KACA,OACA,OACA,MACA,MACA,OACA,GACA,MACA,OACA,KACA,GACA,MACA,KACA,OACA,MACA,MACA,MACA,MACA,GACA,MACA,MACA,OACA,IACA,MACA,IACA,OACA,MACA,IACA,GACA,MACA,OACA,MACA,OACA,MACA,OACA,OACA,MACA,GACA,OACA,OACA,MACA,MACA,MACA,OACA,MACA,MACA,GACA,MACA,IACA,MACA,MACA,OACA,GACA,MACA,GACA,GACA,MACA,MACA,MACA,OACA,OACA,KACA,OACA,GACA,KACA,OACA,MACA,MACA,MACA,OACA,KACA,MACA,GACA,KACA,MACA,MACA,GACA,MACA,GACA,MACA,MACA,MACA,KACA,OACA,KACA,MACA,OACA,MACA,KACA,MACA,KACA,MACA,MACA,MACA,GACA,OACA,MACA,MACA,OACA,MACA,MACA,MACA,MACA,GACA,MACA,MACA,GACA,OACA,MACA,MACA,OACA,MACA,MACA,MACA,MACA,GACA,MACA,MACA,GACA,OACA,MACA,MACA,OACA,IACA,MACA,MACA,IACA,GACA,MACA,MACA,GACA,KACA,MACA,MACA,KACA,IACA,MACA,KACA,IACA,GACA,KACA,MACA,KACA,MACA,MACA,KACA,OACA,MACA,KACA,MACA,MACA,KACA,OACA,IACA,KACA,MACA,MACA,KACA,OACA,IACA,KACA,KACA,MACA,KACA,KACA,MACA,GACA,OACA,MACA,MACA,OACA,IACA,MACA,MACA,IACA,GACA,MACA,MACA,GACA,OACA,MACA,MACA,OACA,OACA,MACA,QACA,OACA,GACA,QACA,IACA,MACA,KACA,MACA,MACA,KACA,MACA,GACA,KACA,IACA,KACA,MACA,MACA,KACA,OACA,OACA,KACA,QACA,MACA,KACA,OACA,MACA,KACA,KACA,IACA,KACA,KACA,KACA,GACA,OACA,KACA,OACA,OACA,KACA,OACA,KACA,KACA,GACA,KACA,KACA,GACA,OACA,KACA,OACA,OACA,KACA,OACA,OACA,KACA,GACA,OACA,KACA,GACA,KACA,KACA,OACA,KACA,KACA,OACA,OACA,KACA,GACA,OACA,KACA,GACA,KACA,KACA,OACA,KACA,KACA,OACA,KACA,KACA,GACA,KACA,KACA,MACA,KACA,KACA,MACA,OACA,KACA,MACA,OACA,KACA,MACA,OACA,KACA,MACA,OACA,KACA,MACA,KACA,KACA,MACA,KACA,KACA,MACA,KACA,KACA,GACA,OACA,KACA,OACA,OACA,OACA,OACA,SACA,OACA,GACA,SACA,KACA,GACA,OACA,KACA,OACA,OACA,MACA,OACA,QACA,MACA,GACA,QACA,KACA,GACA,KACA,KACA,OACA,KACA,KACA,OACA,KACA,KACA,GACA,KACA,OACA,MACA,SACA,KACA,MACA,OACA,MACA,MACA,QACA,KACA,MACA,OACA,KACA,MACA,KACA,KACA,MACA,KACA,GACA,GACA,MACA,KACA,GACA,MACA,KACA,OACA,MACA,MACA,MACA,MACA,GACA,MACA,MACA,GACA,GACA,MACA,KACA,GACA,KACA,KACA,QACA,KACA,OACA,MACA,KACA,GACA,MACA,KACA,OACA,MACA,MACA,MACA,OACA,MACA,MACA,GACA,MACA,QACA,MACA,KACA,MACA,QACA,KACA,MACA,GACA,KACA,MACA,MACA,MACA,OACA,KACA,MACA,GACA,KACA,MACA,MACA,OACA,KACA,QACA,KACA,KACA,GACA,KACA,KACA,MACA,KACA,MACA,KACA,MACA,MACA,OACA,KACA,KACA,KACA,OACA,KACA,KACA,GACA,MACA,KACA,QACA,MACA,OACA,MACA,MACA,GACA,MACA,MACA,KACA,GACA,MACA,KACA,QACA,MACA,OACA,MACA,MACA,GACA,MACA,MACA,KACA,GACA,KACA,KACA,QACA,KACA,OACA,MACA,KACA,GACA,MACA,KACA,QACA,MACA,MACA,MACA,QACA,MACA,MACA,GACA,MACA,QACA,MACA,MACA,MACA,QACA,MACA,MACA,GACA,MACA,QACA,MACA,KACA,MACA,QACA,KACA,MACA,GACA,KACA,MACA,OACA,MACA,QACA,KACA,MACA,GACA,KACA,MACA,MACA,OACA,MACA,QACA,KACA,MACA,GACA,KACA,MACA,MACA,OACA,KACA,QACA,KACA,KACA,GACA,KACA,KACA,OACA,KACA,MACA,KACA,OACA,MACA,OACA,KACA,MACA,KACA,OACA,MACA,OACA,KACA,KACA,KACA,OACA,KACA,GACA,GACA,GACA,OACA,GACA,GACA,OACA,OACA,GACA,OACA,OACA,GACA,GACA,OACA,GACA,KACA,GACA,OACA,KACA,MACA,OACA,MACA,KACA,OACA,GACA,KACA,OACA,QACA,OACA,GACA,QACA,OACA,GACA,QACA,GACA,GACA,OACA,KACA,OACA,MACA,MACA,OACA,MACA,GACA,OACA,QACA,QACA,GACA,QACA,QACA,GACA,GACA,QACA,GACA,MACA,OACA,OACA,OACA,MACA,OACA,GACA,MACA,OACA,OACA,QACA,GACA,OACA,QACA,GACA,MACA,MACA,OACA,KACA,OACA,MACD;IAED,MAAO;IAEPR,IAAA,GAAOA,IAAA,IAAQ;IAGfC,QAAA,GAAWA,QAAA,KAAa,SAAYQ,IAAA,CAAKC,GAAA,CAAI,GAAGD,IAAA,CAAKE,KAAA,CAAMV,QAAQ,KAAK,EAAE,IAAI;IAG9EC,MAAA,GAASA,MAAA,KAAW,SAAY,OAAOA,MAAA;IACvCC,GAAA,GAAMA,GAAA,KAAQ,SAAY,OAAOA,GAAA;IACjCC,IAAA,GAAOA,IAAA,KAAS,SAAY,OAAOA,IAAA;IAGnCC,MAAA,GAASA,MAAA,KAAW,SAAY,OAAOA,MAAA;IASvC,MAAMO,UAAA,GAAa;IACnBN,KAAA,GAAQA,KAAA,KAAU,SAAY,OAAOA,KAAA;IAGrC,MAAMO,SAAA,GAAY,QAAQP,KAAA,GAAQ,IAAIM,UAAA;IAEtC,MAAME,UAAA,GAAaD,SAAA,GAAY;IAC/B,MAAME,QAAA,GAAWf,IAAA,GAAOc,UAAA;IAIxB,IAAIE,YAAA,GAAed,MAAA,IAAU,IAAID,QAAA,GAAW,KAAKA,QAAA,GAAW;IAC5De,YAAA,IAAgBb,GAAA,IAAO,KAAKF,QAAA,GAAW,KAAKA,QAAA,GAAW;IACvDe,YAAA,IAAgBZ,IAAA,GAAO,KAAKH,QAAA,GAAWA,QAAA,GAAW;IAElD,MAAMgB,OAAA,GAAU,IAAIC,WAAA,CAAYF,YAAA,GAAe,CAAC;IAEhD,IAAIG,WAAA,GAAcjB,MAAA,GAAS,IAAI;IAC/BiB,WAAA,IAAehB,GAAA,GAAM,IAAI;IACzBgB,WAAA,IAAef,IAAA,GAAO,KAAK;IAC3Be,WAAA,KAAgBlB,QAAA,GAAW,MAAMA,QAAA,GAAW;IAE5C,MAAMmB,QAAA,GAAW,IAAIC,YAAA,CAAaF,WAAA,GAAc,CAAC;IACjD,MAAMG,OAAA,GAAU,IAAID,YAAA,CAAaF,WAAA,GAAc,CAAC;IAChD,MAAMI,GAAA,GAAM,IAAIF,YAAA,CAAaF,WAAA,GAAc,CAAC;IAG5C,MAAMK,EAAA,GAAK,IAAIC,OAAA,CAAS;IACxBD,EAAA,CAAGE,GAAA,CAAI,IAAM,GAAK,IAAM,GAAK,GAAK,IAAM,GAAK,GAAK,IAAM,GAAK,GAAK,GAAK,GAAK,GAAK,GAAK,CAAG;IAEzF,MAAMC,CAAA,GAAI,EAAE;IACZ,IAAIC,CAAA,EAAGC,CAAA,EAAGC,CAAA;IAEV,MAAMC,EAAA,GAAK,EAAE;IACb,MAAMC,EAAA,GAAK,EAAE;IACb,MAAMC,GAAA,GAAM,EAAE;IACd,MAAMC,GAAA,GAAM,EAAE;IAId,MAAMC,GAAA,GAAM,EAAE;IAEd,MAAMC,IAAA,GAAO,EAAE;IACf,MAAMC,IAAA,GAAO,EAAE;IACf,MAAMC,IAAA,GAAO,EAAE;IAEf,MAAMC,IAAA,GAAO,IAAIC,OAAA,CAAS;IAE1B,IAAIC,MAAA;IAEJ,IAAIC,KAAA,EAAOC,KAAA;IACX,IAAIC,UAAA;IAEJ,IAAIC,CAAA,EAAGC,CAAA,EAAGC,IAAA,EAAMC,IAAA,EAAMC,CAAA;IACtB,IAAIC,KAAA,GAAQ;IACZ,IAAIC,KAAA,GAAQ;IAEZ,MAAMC,OAAA,GAAU,IAAIZ,OAAA,CAAS;IAC7B,IAAIa,EAAA,EAAIC,EAAA,EAAIC,EAAA,EAAIC,EAAA;IAEhB,MAAMC,GAAA,GAAM,IAAIhC,OAAA,CAAS;IACzB,MAAMiC,IAAA,GAAO,IAAIjC,OAAA,CAAS;IAE1B,MAAMkC,GAAA,GAAM,IAAIC,OAAA,CAAS;IACzB,MAAMC,GAAA,GAAM,IAAID,OAAA,CAAS;IACzB,MAAME,IAAA,GAAO,IAAIF,OAAA,CAAS;IAC1B,MAAMG,IAAA,GAAO,IAAIH,OAAA,CAAS;IAE1B,MAAMI,KAAA,GAAQ,IAAIxB,OAAA,CAAS;IAC3B,MAAMyB,KAAA,GAAQ,IAAIzB,OAAA,CAAS;IAE3B,MAAM0B,GAAA,GAAM1C,EAAA,CAAG2C,KAAA,CAAO;IACtBD,GAAA,CAAIE,SAAA,CAAW;IAIf,MAAMC,aAAA,GAAgBA,CACpBC,IAAA,EACAC,IAAA,EACAC,IAAA,KAEA,EACGpD,QAAA,CAASkD,IAAA,GAAO,CAAC,MAAMlD,QAAA,CAASmD,IAAA,GAAO,CAAC,KACvCnD,QAAA,CAASkD,IAAA,GAAO,IAAI,CAAC,MAAMlD,QAAA,CAASmD,IAAA,GAAO,IAAI,CAAC,KAChDnD,QAAA,CAASkD,IAAA,GAAO,IAAI,CAAC,MAAMlD,QAAA,CAASmD,IAAA,GAAO,IAAI,CAAC,KACjDnD,QAAA,CAASkD,IAAA,GAAO,CAAC,MAAMlD,QAAA,CAASoD,IAAA,GAAO,CAAC,KACvCpD,QAAA,CAASkD,IAAA,GAAO,IAAI,CAAC,MAAMlD,QAAA,CAASoD,IAAA,GAAO,IAAI,CAAC,KAChDpD,QAAA,CAASkD,IAAA,GAAO,IAAI,CAAC,MAAMlD,QAAA,CAASoD,IAAA,GAAO,IAAI,CAAC,KACjDpD,QAAA,CAASmD,IAAA,GAAO,CAAC,MAAMnD,QAAA,CAASoD,IAAA,GAAO,CAAC,KACvCpD,QAAA,CAASmD,IAAA,GAAO,IAAI,CAAC,MAAMnD,QAAA,CAASoD,IAAA,GAAO,IAAI,CAAC,KAChDpD,QAAA,CAASmD,IAAA,GAAO,IAAI,CAAC,MAAMnD,QAAA,CAASoD,IAAA,GAAO,IAAI,CAAC;IAGtD,KAAK5C,CAAA,GAAI,GAAGA,CAAA,GAAI,GAAGA,CAAA,IAAK;MACtBO,GAAA,CAAIP,CAAC,IAAI,IAAIH,OAAA,CAAS;IACvB;IAED,MAAMgD,UAAA,GAAarE,IAAA,GAAO,IAAI;IAC9B,MAAMsE,UAAA,GAAaxE,MAAA,GAAS,KAAK;IAEjC0C,UAAA,GAAa3C,QAAA,GAAW;IAExB,IAAI0E,SAAA,GAAY;IAEhB,IAAIC,SAAA,GAAY;IAChB,IAAIC,SAAA,GAAY;IAChB,IAAIC,OAAA,GAAU;IAEd,IAAIC,UAAA,GAAa;IAEjB,SAASC,IAAA,GAAOP,UAAA,EAAYO,IAAA,GAAON,UAAA,EAAYM,IAAA,IAAQ;MAGrD,IAAI7E,GAAA,IAAO6E,IAAA,GAAO,MAAMA,IAAA,IAAQ,IAAI;QAElC,KAAKpD,CAAA,GAAI,GAAGA,CAAA,GAAI,GAAGA,CAAA,IAAK;UAEtB,KAAKC,CAAA,GAAI,GAAGA,CAAA,GAAI,GAAGA,CAAA,IAAK;YACtB,KAAKC,CAAA,GAAI,GAAGA,CAAA,GAAI,GAAGA,CAAA,IAAK;cAEtBH,CAAA,CAAEG,CAAA,GAAI,IAAID,CAAC,IAAIrB,cAAA,CAAeD,aAAA,CAAcyE,IAAA,GAAO,KAAKnD,CAAA,GAAI,IAAIC,CAAC,IAAI,IAAIF,CAAC;cAI1E,IAAIvB,MAAA,IAAU2E,IAAA,IAAQ,MAAMA,IAAA,GAAO,MAAMpD,CAAA,KAAM,GAAG;gBAIhDD,CAAA,CAAEG,CAAA,GAAI,IAAID,CAAC,KAAK;cACjB;cAID,IAAI,CAACvB,KAAA,IAASsB,CAAA,KAAM,GAAG;gBACrBD,CAAA,CAAEG,CAAA,GAAI,IAAID,CAAC,KAAKjB,UAAA;cACjB;YACF;UACF;UAED6C,GAAA,CAAI/B,GAAA,CAAIC,CAAA,CAAE,CAAC,GAAGA,CAAA,CAAE,CAAC,GAAGA,CAAA,CAAE,CAAC,GAAGA,CAAA,CAAE,CAAC,GAAGA,CAAA,CAAE,CAAC,GAAGA,CAAA,CAAE,CAAC,GAAGA,CAAA,CAAE,CAAC,GAAGA,CAAA,CAAE,CAAC,GAAGA,CAAA,CAAE,CAAC,GAAGA,CAAA,CAAE,CAAC,GAAGA,CAAA,CAAE,EAAE,GAAGA,CAAA,CAAE,EAAE,GAAGA,CAAA,CAAE,EAAE,GAAGA,CAAA,CAAE,EAAE,GAAGA,CAAA,CAAE,EAAE,GAAGA,CAAA,CAAE,EAAE,CAAC;UAE5G+B,IAAA,CAAKuB,gBAAA,CAAiBxB,GAAA,EAAKjC,EAAE;UAC7BW,GAAA,CAAIP,CAAC,EAAEqD,gBAAA,CAAiBf,GAAA,EAAKR,IAAI;QAClC;QAGD,KAAKhB,KAAA,GAAQ,GAAGA,KAAA,IAASzC,QAAA,EAAUyC,KAAA,IAAS;UAC1CG,CAAA,GAAIH,KAAA,GAAQzC,QAAA;UAEZ,KAAK0C,KAAA,GAAQ,GAAGA,KAAA,IAAS1C,QAAA,EAAU0C,KAAA,IAAS;YAC1CG,CAAA,GAAIH,KAAA,GAAQ1C,QAAA;YAIZ,KAAKgD,CAAA,GAAI,GAAGF,IAAA,GAAOC,IAAA,GAAO,GAAKC,CAAA,KAAO;cACpClB,EAAA,CAAGkB,CAAC,IAAIF,IAAA;cACRf,EAAA,CAAGiB,CAAC,IAAID,IAAA;cACRD,IAAA,IAAQF,CAAA;cACRG,IAAA,IAAQF,CAAA;cAER,IAAIG,CAAA,KAAM,GAAG;gBACXhB,GAAA,CAAIgB,CAAC,IAAIf,GAAA,CAAIe,CAAC,IAAI;gBAClBC,KAAA,GAAQC,KAAA,GAAQ;cAChC,OAAqB;gBACLlB,GAAA,CAAIgB,CAAC,IAAIC,KAAA,IAAS,IAAID,CAAA;gBACtBf,GAAA,CAAIe,CAAC,IAAIE,KAAA,IAAS,IAAIF,CAAA;gBACtBC,KAAA,IAASL,CAAA;gBACTM,KAAA,IAASL,CAAA;cACV;YACF;YAEDa,GAAA,CAAIuB,SAAA,CAAUnD,EAAE;YAChB8B,GAAA,CAAIqB,SAAA,CAAUlD,EAAE;YAChB8B,IAAA,CAAKoB,SAAA,CAAUjD,GAAG;YAClB8B,IAAA,CAAKmB,SAAA,CAAUhD,GAAG;YAGlB,KAAKN,CAAA,GAAI,GAAGA,CAAA,GAAI,GAAGA,CAAA,IAAK;cAEtBa,MAAA,GAASkB,GAAA,CAAIQ,KAAA,CAAO;cACpB1B,MAAA,CAAO0C,YAAA,CAAahD,GAAA,CAAIP,CAAC,CAAC;cAC1BQ,IAAA,CAAKR,CAAC,IAAIa,MAAA,CAAO2C,GAAA,CAAIvB,GAAG;cAGxBpB,MAAA,GAASqB,IAAA,CAAKK,KAAA,CAAO;cACrB1B,MAAA,CAAO0C,YAAA,CAAahD,GAAA,CAAIP,CAAC,CAAC;cAC1BS,IAAA,CAAKT,CAAC,IAAIa,MAAA,CAAO2C,GAAA,CAAIvB,GAAG;cAExBpB,MAAA,GAASkB,GAAA,CAAIQ,KAAA,CAAO;cACpB1B,MAAA,CAAO0C,YAAA,CAAahD,GAAA,CAAIP,CAAC,CAAC;cAC1BU,IAAA,CAAKV,CAAC,IAAIa,MAAA,CAAO2C,GAAA,CAAIrB,IAAI;YAC1B;YAGDC,KAAA,CAAMkB,SAAA,CAAU7C,IAAI;YACpB4B,KAAA,CAAMiB,SAAA,CAAU5C,IAAI;YACpBC,IAAA,CAAK8C,YAAA,CAAapB,KAAA,EAAOD,KAAK;YAC9BzB,IAAA,CAAK+C,SAAA,CAAW;YAGhB,IAAIlD,IAAA,CAAK,CAAC,MAAM,KAAKA,IAAA,CAAK,CAAC,MAAM,GAAG;cAElCgB,OAAA,CAAQ1B,GAAA,CAAI,GAAGU,IAAA,CAAK,CAAC,IAAItB,UAAA,GAAa,IAAI,IAAI,CAAC;YAC7D,OAAmB;cAELsC,OAAA,CAAQ1B,GAAA,CAAIa,IAAA,CAAKgD,CAAA,EAAGhD,IAAA,CAAKiD,CAAA,EAAG,CAACjD,IAAA,CAAKkD,CAAC;YACpC;YAGDrE,QAAA,CAASwD,SAAA,EAAW,IAAI7D,QAAA,GAAWqB,IAAA,CAAK,CAAC;YACzChB,QAAA,CAASwD,SAAA,EAAW,IAAI7D,QAAA,IAAYqB,IAAA,CAAK,CAAC,IAAItB,UAAA;YAC9CM,QAAA,CAASwD,SAAA,EAAW,IAAI,CAAC7D,QAAA,GAAWqB,IAAA,CAAK,CAAC;YAE1Cd,OAAA,CAAQuD,SAAA,EAAW,IAAIzB,OAAA,CAAQmC,CAAA;YAC/BjE,OAAA,CAAQuD,SAAA,EAAW,IAAIzB,OAAA,CAAQqC,CAAA;YAC/BnE,OAAA,CAAQuD,SAAA,EAAW,IAAIzB,OAAA,CAAQoC,CAAA;YAE/BjE,GAAA,CAAIuD,OAAA,EAAS,IAAI,IAAIhC,CAAA;YACrBvB,GAAA,CAAIuD,OAAA,EAAS,IAAI,IAAIjC,CAAA;UACtB;QACF;QAGD,KAAKH,KAAA,GAAQ,GAAGA,KAAA,GAAQzC,QAAA,EAAUyC,KAAA,IAAS;UACzC,KAAKC,KAAA,GAAQ,GAAGA,KAAA,GAAQ1C,QAAA,EAAU0C,KAAA,IAAS;YACzCU,EAAA,GAAKsB,SAAA,GAAY/B,UAAA,GAAaA,UAAA,GAAaF,KAAA,GAAQE,UAAA,GAAaD,KAAA;YAChEW,EAAA,GAAKD,EAAA,GAAK;YACVE,EAAA,GAAKD,EAAA,GAAKV,UAAA;YACVY,EAAA,GAAKH,EAAA,GAAKT,UAAA;YAIV,IAAIyB,aAAA,CAAchB,EAAA,EAAIC,EAAA,EAAIC,EAAE,GAAG;cAC7BtC,OAAA,CAAQ8D,UAAA,EAAY,IAAI1B,EAAA;cACxBpC,OAAA,CAAQ8D,UAAA,EAAY,IAAIzB,EAAA;cACxBrC,OAAA,CAAQ8D,UAAA,EAAY,IAAIxB,EAAA;YACzB;YAED,IAAIc,aAAA,CAAchB,EAAA,EAAIE,EAAA,EAAIC,EAAE,GAAG;cAC7BvC,OAAA,CAAQ8D,UAAA,EAAY,IAAI1B,EAAA;cACxBpC,OAAA,CAAQ8D,UAAA,EAAY,IAAIxB,EAAA;cACxBtC,OAAA,CAAQ8D,UAAA,EAAY,IAAIvB,EAAA;YACzB;UACF;QACF;QAGDmB,SAAA;MACD;IACF;IAED,KAAKe,QAAA,CAAS,IAAIC,eAAA,CAAgB1E,OAAA,EAAS,CAAC,CAAC;IAC7C,KAAK2E,YAAA,CAAa,YAAY,IAAID,eAAA,CAAgBvE,QAAA,EAAU,CAAC,CAAC;IAC9D,KAAKwE,YAAA,CAAa,UAAU,IAAID,eAAA,CAAgBrE,OAAA,EAAS,CAAC,CAAC;IAC3D,KAAKsE,YAAA,CAAa,MAAM,IAAID,eAAA,CAAgBpE,GAAA,EAAK,CAAC,CAAC;IAEnD,KAAKsE,qBAAA,CAAuB;EAC7B;AACH", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}