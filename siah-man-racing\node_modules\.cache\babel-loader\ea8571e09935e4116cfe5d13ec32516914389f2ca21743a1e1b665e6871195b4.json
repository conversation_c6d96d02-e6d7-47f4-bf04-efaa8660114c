{"ast": null, "code": "import _extends from '@babel/runtime/helpers/esm/extends';\nimport * as THREE from 'three';\nimport * as React from 'react';\nconst Mask = /* @__PURE__ */React.forwardRef(({\n  id = 1,\n  colorWrite = false,\n  depthWrite = false,\n  ...props\n}, fref) => {\n  const ref = React.useRef(null);\n  const spread = React.useMemo(() => ({\n    colorWrite,\n    depthWrite,\n    stencilWrite: true,\n    stencilRef: id,\n    stencilFunc: THREE.AlwaysStencilFunc,\n    stencilFail: THREE.ReplaceStencilOp,\n    stencilZFail: THREE.ReplaceStencilOp,\n    stencilZPass: THREE.ReplaceStencilOp\n  }), [id, colorWrite, depthWrite]);\n  React.useLayoutEffect(() => {\n    Object.assign(ref.current.material, spread);\n  });\n  React.useImperativeHandle(fref, () => ref.current, []);\n  return /*#__PURE__*/React.createElement(\"mesh\", _extends({\n    ref: ref,\n    renderOrder: -id\n  }, props));\n});\nfunction useMask(id, inverse = false) {\n  return {\n    stencilWrite: true,\n    stencilRef: id,\n    stencilFunc: inverse ? THREE.NotEqualStencilFunc : THREE.EqualStencilFunc,\n    stencilFail: THREE.KeepStencilOp,\n    stencilZFail: THREE.KeepStencilOp,\n    stencilZPass: THREE.KeepStencilOp\n  };\n}\nexport { Mask, useMask };", "map": {"version": 3, "names": ["_extends", "THREE", "React", "Mask", "forwardRef", "id", "colorWrite", "depthWrite", "props", "fref", "ref", "useRef", "spread", "useMemo", "stencilWrite", "stencilRef", "stencil<PERSON>unc", "AlwaysStencilFunc", "stencilFail", "ReplaceStencilOp", "stencilZFail", "stencilZPass", "useLayoutEffect", "Object", "assign", "current", "material", "useImperativeHandle", "createElement", "renderOrder", "useMask", "inverse", "NotEqualStencilFunc", "EqualStencilFunc", "KeepStencilOp"], "sources": ["C:/Users/<USER>/Documents/Apps_Creations/SiahMan_Racing4Speed_WebGame/siah-man-racing/node_modules/@react-three/drei/core/Mask.js"], "sourcesContent": ["import _extends from '@babel/runtime/helpers/esm/extends';\nimport * as THREE from 'three';\nimport * as React from 'react';\n\nconst Mask = /* @__PURE__ */React.forwardRef(({\n  id = 1,\n  colorWrite = false,\n  depthWrite = false,\n  ...props\n}, fref) => {\n  const ref = React.useRef(null);\n  const spread = React.useMemo(() => ({\n    colorWrite,\n    depthWrite,\n    stencilWrite: true,\n    stencilRef: id,\n    stencilFunc: THREE.AlwaysStencilFunc,\n    stencilFail: THREE.ReplaceStencilOp,\n    stencilZFail: THREE.ReplaceStencilOp,\n    stencilZPass: THREE.ReplaceStencilOp\n  }), [id, colorWrite, depthWrite]);\n  React.useLayoutEffect(() => {\n    Object.assign(ref.current.material, spread);\n  });\n  React.useImperativeHandle(fref, () => ref.current, []);\n  return /*#__PURE__*/React.createElement(\"mesh\", _extends({\n    ref: ref,\n    renderOrder: -id\n  }, props));\n});\nfunction useMask(id, inverse = false) {\n  return {\n    stencilWrite: true,\n    stencilRef: id,\n    stencilFunc: inverse ? THREE.NotEqualStencilFunc : THREE.EqualStencilFunc,\n    stencilFail: THREE.KeepStencilOp,\n    stencilZFail: THREE.KeepStencilOp,\n    stencilZPass: THREE.KeepStencilOp\n  };\n}\n\nexport { Mask, useMask };\n"], "mappings": "AAAA,OAAOA,QAAQ,MAAM,oCAAoC;AACzD,OAAO,KAAKC,KAAK,MAAM,OAAO;AAC9B,OAAO,KAAKC,KAAK,MAAM,OAAO;AAE9B,MAAMC,IAAI,GAAG,eAAeD,KAAK,CAACE,UAAU,CAAC,CAAC;EAC5CC,EAAE,GAAG,CAAC;EACNC,UAAU,GAAG,KAAK;EAClBC,UAAU,GAAG,KAAK;EAClB,GAAGC;AACL,CAAC,EAAEC,IAAI,KAAK;EACV,MAAMC,GAAG,GAAGR,KAAK,CAACS,MAAM,CAAC,IAAI,CAAC;EAC9B,MAAMC,MAAM,GAAGV,KAAK,CAACW,OAAO,CAAC,OAAO;IAClCP,UAAU;IACVC,UAAU;IACVO,YAAY,EAAE,IAAI;IAClBC,UAAU,EAAEV,EAAE;IACdW,WAAW,EAAEf,KAAK,CAACgB,iBAAiB;IACpCC,WAAW,EAAEjB,KAAK,CAACkB,gBAAgB;IACnCC,YAAY,EAAEnB,KAAK,CAACkB,gBAAgB;IACpCE,YAAY,EAAEpB,KAAK,CAACkB;EACtB,CAAC,CAAC,EAAE,CAACd,EAAE,EAAEC,UAAU,EAAEC,UAAU,CAAC,CAAC;EACjCL,KAAK,CAACoB,eAAe,CAAC,MAAM;IAC1BC,MAAM,CAACC,MAAM,CAACd,GAAG,CAACe,OAAO,CAACC,QAAQ,EAAEd,MAAM,CAAC;EAC7C,CAAC,CAAC;EACFV,KAAK,CAACyB,mBAAmB,CAAClB,IAAI,EAAE,MAAMC,GAAG,CAACe,OAAO,EAAE,EAAE,CAAC;EACtD,OAAO,aAAavB,KAAK,CAAC0B,aAAa,CAAC,MAAM,EAAE5B,QAAQ,CAAC;IACvDU,GAAG,EAAEA,GAAG;IACRmB,WAAW,EAAE,CAACxB;EAChB,CAAC,EAAEG,KAAK,CAAC,CAAC;AACZ,CAAC,CAAC;AACF,SAASsB,OAAOA,CAACzB,EAAE,EAAE0B,OAAO,GAAG,KAAK,EAAE;EACpC,OAAO;IACLjB,YAAY,EAAE,IAAI;IAClBC,UAAU,EAAEV,EAAE;IACdW,WAAW,EAAEe,OAAO,GAAG9B,KAAK,CAAC+B,mBAAmB,GAAG/B,KAAK,CAACgC,gBAAgB;IACzEf,WAAW,EAAEjB,KAAK,CAACiC,aAAa;IAChCd,YAAY,EAAEnB,KAAK,CAACiC,aAAa;IACjCb,YAAY,EAAEpB,KAAK,CAACiC;EACtB,CAAC;AACH;AAEA,SAAS/B,IAAI,EAAE2B,OAAO", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}