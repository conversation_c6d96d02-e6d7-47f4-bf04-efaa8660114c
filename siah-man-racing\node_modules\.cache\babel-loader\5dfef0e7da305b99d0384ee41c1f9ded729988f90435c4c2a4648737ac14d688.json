{"ast": null, "code": "import { Loader, HalfFloatType, CubeTexture, LinearFilter, FloatType, FileLoader, DataTexture } from \"three\";\nimport { RGBELoader } from \"./RGBELoader.js\";\nclass HDRCubeTextureLoader extends Loader {\n  constructor(manager) {\n    super(manager);\n    this.hdrLoader = new RGBELoader();\n    this.type = HalfFloatType;\n  }\n  load(urls, onLoad, onProgress, onError) {\n    if (typeof urls === \"string\") {\n      urls = [urls];\n    } else if (!Array.isArray(urls)) {\n      console.warn(\"THREE.HDRCubeTextureLoader signature has changed. Use .setDataType() instead.\");\n      this.setDataType(urls);\n      urls = onLoad;\n      onLoad = onProgress;\n      onProgress = onError;\n      onError = arguments[4];\n    }\n    const texture = new CubeTexture();\n    texture.type = this.type;\n    switch (texture.type) {\n      case FloatType:\n      case HalfFloatType:\n        if (\"colorSpace\" in texture) texture.colorSpace = \"srgb-linear\";else texture.encoding = 3e3;\n        texture.minFilter = LinearFilter;\n        texture.magFilter = LinearFilter;\n        texture.generateMipmaps = false;\n        break;\n    }\n    const scope = this;\n    let loaded = 0;\n    function loadHDRData(i, onLoad2, onProgress2, onError2) {\n      new FileLoader(scope.manager).setPath(scope.path).setResponseType(\"arraybuffer\").setWithCredentials(scope.withCredentials).load(urls[i], function (buffer) {\n        loaded++;\n        const texData = scope.hdrLoader.parse(buffer);\n        if (!texData) return;\n        if (texData.data !== void 0) {\n          const dataTexture = new DataTexture(texData.data, texData.width, texData.height);\n          dataTexture.type = texture.type;\n          if (\"colorSpace\" in dataTexture) dataTexture.colorSpace = texture.SRGBColorSpace;else dataTexture.encoding = texture.encoding;\n          dataTexture.format = texture.format;\n          dataTexture.minFilter = texture.minFilter;\n          dataTexture.magFilter = texture.magFilter;\n          dataTexture.generateMipmaps = texture.generateMipmaps;\n          texture.images[i] = dataTexture;\n        }\n        if (loaded === 6) {\n          texture.needsUpdate = true;\n          if (onLoad2) onLoad2(texture);\n        }\n      }, onProgress2, onError2);\n    }\n    for (let i = 0; i < urls.length; i++) {\n      loadHDRData(i, onLoad, onProgress, onError);\n    }\n    return texture;\n  }\n  setDataType(value) {\n    this.type = value;\n    this.hdrLoader.setDataType(value);\n    return this;\n  }\n}\nexport { HDRCubeTextureLoader };", "map": {"version": 3, "names": ["HDRCubeTextureLoader", "Loader", "constructor", "manager", "hdrL<PERSON>der", "RGBELoader", "type", "HalfFloatType", "load", "urls", "onLoad", "onProgress", "onError", "Array", "isArray", "console", "warn", "setDataType", "arguments", "texture", "CubeTexture", "FloatType", "colorSpace", "encoding", "minFilter", "LinearFilter", "magFilter", "generateMipmaps", "scope", "loaded", "loadHDRData", "i", "onLoad2", "onProgress2", "onError2", "<PERSON><PERSON><PERSON><PERSON>", "set<PERSON>ath", "path", "setResponseType", "setWithCredentials", "withCredentials", "buffer", "texData", "parse", "data", "dataTexture", "DataTexture", "width", "height", "SRGBColorSpace", "format", "images", "needsUpdate", "length", "value"], "sources": ["C:\\Users\\<USER>\\Documents\\Apps_Creations\\SiahMan_Racing4Speed_WebGame\\siah-man-racing\\node_modules\\src\\loaders\\HDRCubeTextureLoader.js"], "sourcesContent": ["import { CubeTexture, DataTexture, FileLoader, FloatType, HalfFloatType, LinearFilter, Loader } from 'three'\nimport { RGBELoader } from '../loaders/RGBELoader.js'\n\nclass HDRCubeTextureLoader extends Loader {\n  constructor(manager) {\n    super(manager)\n\n    this.hdrLoader = new RGBELoader()\n    this.type = HalfFloatType\n  }\n\n  load(urls, onLoad, onProgress, onError) {\n    if (typeof urls === 'string') {\n      urls = [urls]\n    } else if (!Array.isArray(urls)) {\n      console.warn('THREE.HDRCubeTextureLoader signature has changed. Use .setDataType() instead.')\n\n      this.setDataType(urls)\n\n      urls = onLoad\n      onLoad = onProgress\n      onProgress = onError\n      onError = arguments[4]\n    }\n\n    const texture = new CubeTexture()\n\n    texture.type = this.type\n\n    switch (texture.type) {\n      case FloatType:\n      case HalfFloatType:\n        if ('colorSpace' in texture) texture.colorSpace = 'srgb-linear'\n        else texture.encoding = 3000 // LinearEncoding\n        texture.minFilter = LinearFilter\n        texture.magFilter = LinearFilter\n        texture.generateMipmaps = false\n        break\n    }\n\n    const scope = this\n\n    let loaded = 0\n\n    function loadHDRData(i, onLoad, onProgress, onError) {\n      new FileLoader(scope.manager)\n        .setPath(scope.path)\n        .setResponseType('arraybuffer')\n        .setWithCredentials(scope.withCredentials)\n        .load(\n          urls[i],\n          function (buffer) {\n            loaded++\n\n            const texData = scope.hdrLoader.parse(buffer)\n\n            if (!texData) return\n\n            if (texData.data !== undefined) {\n              const dataTexture = new DataTexture(texData.data, texData.width, texData.height)\n\n              dataTexture.type = texture.type\n              if ('colorSpace' in dataTexture) dataTexture.colorSpace = texture.SRGBColorSpace\n              else dataTexture.encoding = texture.encoding\n              dataTexture.format = texture.format\n              dataTexture.minFilter = texture.minFilter\n              dataTexture.magFilter = texture.magFilter\n              dataTexture.generateMipmaps = texture.generateMipmaps\n\n              texture.images[i] = dataTexture\n            }\n\n            if (loaded === 6) {\n              texture.needsUpdate = true\n              if (onLoad) onLoad(texture)\n            }\n          },\n          onProgress,\n          onError,\n        )\n    }\n\n    for (let i = 0; i < urls.length; i++) {\n      loadHDRData(i, onLoad, onProgress, onError)\n    }\n\n    return texture\n  }\n\n  setDataType(value) {\n    this.type = value\n    this.hdrLoader.setDataType(value)\n\n    return this\n  }\n}\n\nexport { HDRCubeTextureLoader }\n"], "mappings": ";;AAGA,MAAMA,oBAAA,SAA6BC,MAAA,CAAO;EACxCC,YAAYC,OAAA,EAAS;IACnB,MAAMA,OAAO;IAEb,KAAKC,SAAA,GAAY,IAAIC,UAAA,CAAY;IACjC,KAAKC,IAAA,GAAOC,aAAA;EACb;EAEDC,KAAKC,IAAA,EAAMC,MAAA,EAAQC,UAAA,EAAYC,OAAA,EAAS;IACtC,IAAI,OAAOH,IAAA,KAAS,UAAU;MAC5BA,IAAA,GAAO,CAACA,IAAI;IACb,WAAU,CAACI,KAAA,CAAMC,OAAA,CAAQL,IAAI,GAAG;MAC/BM,OAAA,CAAQC,IAAA,CAAK,+EAA+E;MAE5F,KAAKC,WAAA,CAAYR,IAAI;MAErBA,IAAA,GAAOC,MAAA;MACPA,MAAA,GAASC,UAAA;MACTA,UAAA,GAAaC,OAAA;MACbA,OAAA,GAAUM,SAAA,CAAU,CAAC;IACtB;IAED,MAAMC,OAAA,GAAU,IAAIC,WAAA,CAAa;IAEjCD,OAAA,CAAQb,IAAA,GAAO,KAAKA,IAAA;IAEpB,QAAQa,OAAA,CAAQb,IAAA;MACd,KAAKe,SAAA;MACL,KAAKd,aAAA;QACH,IAAI,gBAAgBY,OAAA,EAASA,OAAA,CAAQG,UAAA,GAAa,mBAC7CH,OAAA,CAAQI,QAAA,GAAW;QACxBJ,OAAA,CAAQK,SAAA,GAAYC,YAAA;QACpBN,OAAA,CAAQO,SAAA,GAAYD,YAAA;QACpBN,OAAA,CAAQQ,eAAA,GAAkB;QAC1B;IACH;IAED,MAAMC,KAAA,GAAQ;IAEd,IAAIC,MAAA,GAAS;IAEb,SAASC,YAAYC,CAAA,EAAGC,OAAA,EAAQC,WAAA,EAAYC,QAAA,EAAS;MACnD,IAAIC,UAAA,CAAWP,KAAA,CAAMzB,OAAO,EACzBiC,OAAA,CAAQR,KAAA,CAAMS,IAAI,EAClBC,eAAA,CAAgB,aAAa,EAC7BC,kBAAA,CAAmBX,KAAA,CAAMY,eAAe,EACxChC,IAAA,CACCC,IAAA,CAAKsB,CAAC,GACN,UAAUU,MAAA,EAAQ;QAChBZ,MAAA;QAEA,MAAMa,OAAA,GAAUd,KAAA,CAAMxB,SAAA,CAAUuC,KAAA,CAAMF,MAAM;QAE5C,IAAI,CAACC,OAAA,EAAS;QAEd,IAAIA,OAAA,CAAQE,IAAA,KAAS,QAAW;UAC9B,MAAMC,WAAA,GAAc,IAAIC,WAAA,CAAYJ,OAAA,CAAQE,IAAA,EAAMF,OAAA,CAAQK,KAAA,EAAOL,OAAA,CAAQM,MAAM;UAE/EH,WAAA,CAAYvC,IAAA,GAAOa,OAAA,CAAQb,IAAA;UAC3B,IAAI,gBAAgBuC,WAAA,EAAaA,WAAA,CAAYvB,UAAA,GAAaH,OAAA,CAAQ8B,cAAA,MAC7DJ,WAAA,CAAYtB,QAAA,GAAWJ,OAAA,CAAQI,QAAA;UACpCsB,WAAA,CAAYK,MAAA,GAAS/B,OAAA,CAAQ+B,MAAA;UAC7BL,WAAA,CAAYrB,SAAA,GAAYL,OAAA,CAAQK,SAAA;UAChCqB,WAAA,CAAYnB,SAAA,GAAYP,OAAA,CAAQO,SAAA;UAChCmB,WAAA,CAAYlB,eAAA,GAAkBR,OAAA,CAAQQ,eAAA;UAEtCR,OAAA,CAAQgC,MAAA,CAAOpB,CAAC,IAAIc,WAAA;QACrB;QAED,IAAIhB,MAAA,KAAW,GAAG;UAChBV,OAAA,CAAQiC,WAAA,GAAc;UACtB,IAAIpB,OAAA,EAAQA,OAAA,CAAOb,OAAO;QAC3B;MACF,GACDc,WAAA,EACAC,QACD;IACJ;IAED,SAASH,CAAA,GAAI,GAAGA,CAAA,GAAItB,IAAA,CAAK4C,MAAA,EAAQtB,CAAA,IAAK;MACpCD,WAAA,CAAYC,CAAA,EAAGrB,MAAA,EAAQC,UAAA,EAAYC,OAAO;IAC3C;IAED,OAAOO,OAAA;EACR;EAEDF,YAAYqC,KAAA,EAAO;IACjB,KAAKhD,IAAA,GAAOgD,KAAA;IACZ,KAAKlD,SAAA,CAAUa,WAAA,CAAYqC,KAAK;IAEhC,OAAO;EACR;AACH", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}