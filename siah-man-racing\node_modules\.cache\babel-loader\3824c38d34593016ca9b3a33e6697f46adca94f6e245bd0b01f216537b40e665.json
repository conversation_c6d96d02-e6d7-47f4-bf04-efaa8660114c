{"ast": null, "code": "import _extends from '@babel/runtime/helpers/esm/extends';\nimport * as THREE from 'three';\nimport * as React from 'react';\nfunction createScreenQuadGeometry() {\n  const geometry = new THREE.BufferGeometry();\n  const vertices = new Float32Array([-1, -1, 3, -1, -1, 3]);\n  geometry.boundingSphere = new THREE.Sphere();\n  geometry.boundingSphere.set(new THREE.Vector3(), Infinity);\n  geometry.setAttribute('position', new THREE.BufferAttribute(vertices, 2));\n  return geometry;\n}\nconst ScreenQuad = /* @__PURE__ */React.forwardRef(function ScreenQuad({\n  children,\n  ...restProps\n}, ref) {\n  const geometry = React.useMemo(createScreenQuadGeometry, []);\n  return /*#__PURE__*/React.createElement(\"mesh\", _extends({\n    ref: ref,\n    geometry: geometry,\n    frustumCulled: false\n  }, restProps), children);\n});\nexport { ScreenQuad };", "map": {"version": 3, "names": ["_extends", "THREE", "React", "createScreenQuadGeometry", "geometry", "BufferGeometry", "vertices", "Float32Array", "boundingSphere", "Sphere", "set", "Vector3", "Infinity", "setAttribute", "BufferAttribute", "ScreenQuad", "forwardRef", "children", "restProps", "ref", "useMemo", "createElement", "frustumCulled"], "sources": ["C:/Users/<USER>/Documents/Apps_Creations/SiahMan_Racing4Speed_WebGame/siah-man-racing/node_modules/@react-three/drei/core/ScreenQuad.js"], "sourcesContent": ["import _extends from '@babel/runtime/helpers/esm/extends';\nimport * as THREE from 'three';\nimport * as React from 'react';\n\nfunction createScreenQuadGeometry() {\n  const geometry = new THREE.BufferGeometry();\n  const vertices = new Float32Array([-1, -1, 3, -1, -1, 3]);\n  geometry.boundingSphere = new THREE.Sphere();\n  geometry.boundingSphere.set(new THREE.Vector3(), Infinity);\n  geometry.setAttribute('position', new THREE.BufferAttribute(vertices, 2));\n  return geometry;\n}\nconst ScreenQuad = /* @__PURE__ */React.forwardRef(function ScreenQuad({\n  children,\n  ...restProps\n}, ref) {\n  const geometry = React.useMemo(createScreenQuadGeometry, []);\n  return /*#__PURE__*/React.createElement(\"mesh\", _extends({\n    ref: ref,\n    geometry: geometry,\n    frustumCulled: false\n  }, restProps), children);\n});\n\nexport { ScreenQuad };\n"], "mappings": "AAAA,OAAOA,QAAQ,MAAM,oCAAoC;AACzD,OAAO,KAAKC,KAAK,MAAM,OAAO;AAC9B,OAAO,KAAKC,KAAK,MAAM,OAAO;AAE9B,SAASC,wBAAwBA,CAAA,EAAG;EAClC,MAAMC,QAAQ,GAAG,IAAIH,KAAK,CAACI,cAAc,CAAC,CAAC;EAC3C,MAAMC,QAAQ,GAAG,IAAIC,YAAY,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;EACzDH,QAAQ,CAACI,cAAc,GAAG,IAAIP,KAAK,CAACQ,MAAM,CAAC,CAAC;EAC5CL,QAAQ,CAACI,cAAc,CAACE,GAAG,CAAC,IAAIT,KAAK,CAACU,OAAO,CAAC,CAAC,EAAEC,QAAQ,CAAC;EAC1DR,QAAQ,CAACS,YAAY,CAAC,UAAU,EAAE,IAAIZ,KAAK,CAACa,eAAe,CAACR,QAAQ,EAAE,CAAC,CAAC,CAAC;EACzE,OAAOF,QAAQ;AACjB;AACA,MAAMW,UAAU,GAAG,eAAeb,KAAK,CAACc,UAAU,CAAC,SAASD,UAAUA,CAAC;EACrEE,QAAQ;EACR,GAAGC;AACL,CAAC,EAAEC,GAAG,EAAE;EACN,MAAMf,QAAQ,GAAGF,KAAK,CAACkB,OAAO,CAACjB,wBAAwB,EAAE,EAAE,CAAC;EAC5D,OAAO,aAAaD,KAAK,CAACmB,aAAa,CAAC,MAAM,EAAErB,QAAQ,CAAC;IACvDmB,GAAG,EAAEA,GAAG;IACRf,QAAQ,EAAEA,QAAQ;IAClBkB,aAAa,EAAE;EACjB,CAAC,EAAEJ,SAAS,CAAC,EAAED,QAAQ,CAAC;AAC1B,CAAC,CAAC;AAEF,SAASF,UAAU", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}