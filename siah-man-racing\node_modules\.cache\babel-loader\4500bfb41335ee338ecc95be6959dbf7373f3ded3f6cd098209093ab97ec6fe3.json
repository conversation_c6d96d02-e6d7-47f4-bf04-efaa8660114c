{"ast": null, "code": "import _extends from '@babel/runtime/helpers/esm/extends';\nimport * as React from 'react';\nimport * as THREE from 'three';\nimport { extend } from '@react-three/fiber';\nimport { WireframeMaterial, WireframeMaterialShaders, useWireframeUniforms, setWireframeOverride } from '../materials/WireframeMaterial.js';\nfunction isWithGeometry(object) {\n  return !!(object != null && object.geometry);\n}\nfunction isGeometry(object) {\n  return !!(object != null && object.isBufferGeometry);\n}\nfunction isRefObject(object) {\n  return !!(object != null && object.current);\n}\nfunction isRef(object) {\n  return (object == null ? void 0 : object.current) !== undefined;\n}\nfunction isWireframeGeometry(geometry) {\n  return geometry.type === 'WireframeGeometry';\n}\nfunction getUniforms() {\n  const u = {};\n  for (const key in WireframeMaterialShaders.uniforms) {\n    u[key] = {\n      value: WireframeMaterialShaders.uniforms[key]\n    };\n  }\n  return u;\n}\nfunction getBarycentricCoordinates(geometry, removeEdge) {\n  const position = geometry.getAttribute('position');\n  const count = position.count;\n  const barycentric = [];\n  for (let i = 0; i < count; i++) {\n    const even = i % 2 === 0;\n    const Q = removeEdge ? 1 : 0;\n    if (even) {\n      barycentric.push(0, 0, 1, 0, 1, 0, 1, 0, Q);\n    } else {\n      barycentric.push(0, 1, 0, 0, 0, 1, 1, 0, Q);\n    }\n  }\n  return new THREE.BufferAttribute(Float32Array.from(barycentric), 3);\n}\nfunction getInputGeometry(inputGeometry) {\n  const geo = isRefObject(inputGeometry) ? inputGeometry.current : inputGeometry;\n  if (!isGeometry(geo)) {\n    // Disallow WireframeGeometry\n    if (isWireframeGeometry(geo)) {\n      throw new Error('Wireframe: WireframeGeometry is not supported.');\n    }\n    const parent = geo.parent;\n    if (isWithGeometry(parent)) {\n      // Disallow WireframeGeometry\n      if (isWireframeGeometry(parent.geometry)) {\n        throw new Error('Wireframe: WireframeGeometry is not supported.');\n      }\n      return parent.geometry;\n    }\n  } else {\n    return geo;\n  }\n}\nfunction setBarycentricCoordinates(geometry, simplify) {\n  if (geometry.index) {\n    console.warn('Wireframe: Requires non-indexed geometry, converting to non-indexed geometry.');\n    const nonIndexedGeo = geometry.toNonIndexed();\n    geometry.copy(nonIndexedGeo);\n    geometry.setIndex(null);\n  }\n  const newBarycentric = getBarycentricCoordinates(geometry, simplify);\n  geometry.setAttribute('barycentric', newBarycentric);\n}\nfunction WireframeWithCustomGeo({\n  geometry: customGeometry,\n  simplify = false,\n  ...props\n}) {\n  extend({\n    MeshWireframeMaterial: WireframeMaterial\n  });\n  const [geometry, setGeometry] = React.useState(null);\n  React.useLayoutEffect(() => {\n    const geom = getInputGeometry(customGeometry);\n    if (!geom) {\n      throw new Error('Wireframe: geometry prop must be a BufferGeometry or a ref to a BufferGeometry.');\n    }\n    setBarycentricCoordinates(geom, simplify);\n    if (isRef(customGeometry)) {\n      setGeometry(geom);\n    }\n  }, [simplify, customGeometry]);\n  const drawnGeo = isRef(customGeometry) ? geometry : customGeometry;\n  return /*#__PURE__*/React.createElement(React.Fragment, null, drawnGeo && /*#__PURE__*/React.createElement(\"mesh\", {\n    geometry: drawnGeo\n  }, /*#__PURE__*/React.createElement(\"meshWireframeMaterial\", _extends({\n    attach: \"material\",\n    transparent: true,\n    side: THREE.DoubleSide,\n    polygonOffset: true //\n    ,\n\n    polygonOffsetFactor: -4\n  }, props, {\n    extensions: {\n      derivatives: true,\n      fragDepth: false,\n      drawBuffers: false,\n      shaderTextureLOD: false\n    }\n  }))));\n}\nfunction WireframeWithoutCustomGeo({\n  simplify = false,\n  ...props\n}) {\n  const objectRef = React.useRef(null);\n  const uniforms = React.useMemo(() => getUniforms(), [WireframeMaterialShaders.uniforms]);\n  useWireframeUniforms(uniforms, props);\n  React.useLayoutEffect(() => {\n    const geom = getInputGeometry(objectRef);\n    if (!geom) {\n      throw new Error('Wireframe: Must be a child of a Mesh, Line or Points object or specify a geometry prop.');\n    }\n    const og = geom.clone();\n    setBarycentricCoordinates(geom, simplify);\n    return () => {\n      geom.copy(og);\n      og.dispose();\n    };\n  }, [simplify]);\n  React.useLayoutEffect(() => {\n    const parentMesh = objectRef.current.parent;\n    const og = parentMesh.material.clone();\n    setWireframeOverride(parentMesh.material, uniforms);\n    return () => {\n      parentMesh.material.dispose();\n      parentMesh.material = og;\n    };\n  }, []);\n  return /*#__PURE__*/React.createElement(\"object3D\", {\n    ref: objectRef\n  });\n}\nfunction Wireframe({\n  geometry: customGeometry,\n  ...props\n}) {\n  if (customGeometry) {\n    return /*#__PURE__*/React.createElement(WireframeWithCustomGeo, _extends({\n      geometry: customGeometry\n    }, props));\n  }\n  return /*#__PURE__*/React.createElement(WireframeWithoutCustomGeo, props);\n}\nexport { Wireframe };", "map": {"version": 3, "names": ["_extends", "React", "THREE", "extend", "WireframeMaterial", "WireframeMaterialShaders", "useWireframeUniforms", "setWireframeOverride", "isWithGeometry", "object", "geometry", "isGeometry", "isBufferGeometry", "isRefObject", "current", "isRef", "undefined", "isWireframeGeometry", "type", "getUniforms", "u", "key", "uniforms", "value", "getBarycentricCoordinates", "removeEdge", "position", "getAttribute", "count", "barycentric", "i", "even", "Q", "push", "BufferAttribute", "Float32Array", "from", "getInputGeometry", "inputGeometry", "geo", "Error", "parent", "setBarycentricCoordinates", "simplify", "index", "console", "warn", "nonIndexedGeo", "toNonIndexed", "copy", "setIndex", "newBarycentric", "setAttribute", "WireframeWithCustomGeo", "customGeometry", "props", "MeshWireframeMaterial", "setGeometry", "useState", "useLayoutEffect", "geom", "drawnGeo", "createElement", "Fragment", "attach", "transparent", "side", "DoubleSide", "polygonOffset", "polygonOffsetFactor", "extensions", "derivatives", "fragDepth", "drawBuffers", "shaderTextureLOD", "WireframeWithoutCustomGeo", "objectRef", "useRef", "useMemo", "og", "clone", "dispose", "parent<PERSON><PERSON>", "material", "ref", "Wireframe"], "sources": ["C:/Users/<USER>/Documents/Apps_Creations/SiahMan_Racing4Speed_WebGame/siah-man-racing/node_modules/@react-three/drei/core/Wireframe.js"], "sourcesContent": ["import _extends from '@babel/runtime/helpers/esm/extends';\nimport * as React from 'react';\nimport * as THREE from 'three';\nimport { extend } from '@react-three/fiber';\nimport { WireframeMaterial, WireframeMaterialShaders, useWireframeUniforms, setWireframeOverride } from '../materials/WireframeMaterial.js';\n\nfunction isWithGeometry(object) {\n  return !!(object != null && object.geometry);\n}\nfunction isGeometry(object) {\n  return !!(object != null && object.isBufferGeometry);\n}\nfunction isRefObject(object) {\n  return !!(object != null && object.current);\n}\nfunction isRef(object) {\n  return (object == null ? void 0 : object.current) !== undefined;\n}\nfunction isWireframeGeometry(geometry) {\n  return geometry.type === 'WireframeGeometry';\n}\nfunction getUniforms() {\n  const u = {};\n  for (const key in WireframeMaterialShaders.uniforms) {\n    u[key] = {\n      value: WireframeMaterialShaders.uniforms[key]\n    };\n  }\n  return u;\n}\nfunction getBarycentricCoordinates(geometry, removeEdge) {\n  const position = geometry.getAttribute('position');\n  const count = position.count;\n  const barycentric = [];\n  for (let i = 0; i < count; i++) {\n    const even = i % 2 === 0;\n    const Q = removeEdge ? 1 : 0;\n    if (even) {\n      barycentric.push(0, 0, 1, 0, 1, 0, 1, 0, Q);\n    } else {\n      barycentric.push(0, 1, 0, 0, 0, 1, 1, 0, Q);\n    }\n  }\n  return new THREE.BufferAttribute(Float32Array.from(barycentric), 3);\n}\nfunction getInputGeometry(inputGeometry) {\n  const geo = isRefObject(inputGeometry) ? inputGeometry.current : inputGeometry;\n  if (!isGeometry(geo)) {\n    // Disallow WireframeGeometry\n    if (isWireframeGeometry(geo)) {\n      throw new Error('Wireframe: WireframeGeometry is not supported.');\n    }\n    const parent = geo.parent;\n    if (isWithGeometry(parent)) {\n      // Disallow WireframeGeometry\n      if (isWireframeGeometry(parent.geometry)) {\n        throw new Error('Wireframe: WireframeGeometry is not supported.');\n      }\n      return parent.geometry;\n    }\n  } else {\n    return geo;\n  }\n}\nfunction setBarycentricCoordinates(geometry, simplify) {\n  if (geometry.index) {\n    console.warn('Wireframe: Requires non-indexed geometry, converting to non-indexed geometry.');\n    const nonIndexedGeo = geometry.toNonIndexed();\n    geometry.copy(nonIndexedGeo);\n    geometry.setIndex(null);\n  }\n  const newBarycentric = getBarycentricCoordinates(geometry, simplify);\n  geometry.setAttribute('barycentric', newBarycentric);\n}\nfunction WireframeWithCustomGeo({\n  geometry: customGeometry,\n  simplify = false,\n  ...props\n}) {\n  extend({\n    MeshWireframeMaterial: WireframeMaterial\n  });\n  const [geometry, setGeometry] = React.useState(null);\n  React.useLayoutEffect(() => {\n    const geom = getInputGeometry(customGeometry);\n    if (!geom) {\n      throw new Error('Wireframe: geometry prop must be a BufferGeometry or a ref to a BufferGeometry.');\n    }\n    setBarycentricCoordinates(geom, simplify);\n    if (isRef(customGeometry)) {\n      setGeometry(geom);\n    }\n  }, [simplify, customGeometry]);\n  const drawnGeo = isRef(customGeometry) ? geometry : customGeometry;\n  return /*#__PURE__*/React.createElement(React.Fragment, null, drawnGeo && /*#__PURE__*/React.createElement(\"mesh\", {\n    geometry: drawnGeo\n  }, /*#__PURE__*/React.createElement(\"meshWireframeMaterial\", _extends({\n    attach: \"material\",\n    transparent: true,\n    side: THREE.DoubleSide,\n    polygonOffset: true //\n    ,\n    polygonOffsetFactor: -4\n  }, props, {\n    extensions: {\n      derivatives: true,\n      fragDepth: false,\n      drawBuffers: false,\n      shaderTextureLOD: false\n    }\n  }))));\n}\nfunction WireframeWithoutCustomGeo({\n  simplify = false,\n  ...props\n}) {\n  const objectRef = React.useRef(null);\n  const uniforms = React.useMemo(() => getUniforms(), [WireframeMaterialShaders.uniforms]);\n  useWireframeUniforms(uniforms, props);\n  React.useLayoutEffect(() => {\n    const geom = getInputGeometry(objectRef);\n    if (!geom) {\n      throw new Error('Wireframe: Must be a child of a Mesh, Line or Points object or specify a geometry prop.');\n    }\n    const og = geom.clone();\n    setBarycentricCoordinates(geom, simplify);\n    return () => {\n      geom.copy(og);\n      og.dispose();\n    };\n  }, [simplify]);\n  React.useLayoutEffect(() => {\n    const parentMesh = objectRef.current.parent;\n    const og = parentMesh.material.clone();\n    setWireframeOverride(parentMesh.material, uniforms);\n    return () => {\n      parentMesh.material.dispose();\n      parentMesh.material = og;\n    };\n  }, []);\n  return /*#__PURE__*/React.createElement(\"object3D\", {\n    ref: objectRef\n  });\n}\nfunction Wireframe({\n  geometry: customGeometry,\n  ...props\n}) {\n  if (customGeometry) {\n    return /*#__PURE__*/React.createElement(WireframeWithCustomGeo, _extends({\n      geometry: customGeometry\n    }, props));\n  }\n  return /*#__PURE__*/React.createElement(WireframeWithoutCustomGeo, props);\n}\n\nexport { Wireframe };\n"], "mappings": "AAAA,OAAOA,QAAQ,MAAM,oCAAoC;AACzD,OAAO,KAAKC,KAAK,MAAM,OAAO;AAC9B,OAAO,KAAKC,KAAK,MAAM,OAAO;AAC9B,SAASC,MAAM,QAAQ,oBAAoB;AAC3C,SAASC,iBAAiB,EAAEC,wBAAwB,EAAEC,oBAAoB,EAAEC,oBAAoB,QAAQ,mCAAmC;AAE3I,SAASC,cAAcA,CAACC,MAAM,EAAE;EAC9B,OAAO,CAAC,EAAEA,MAAM,IAAI,IAAI,IAAIA,MAAM,CAACC,QAAQ,CAAC;AAC9C;AACA,SAASC,UAAUA,CAACF,MAAM,EAAE;EAC1B,OAAO,CAAC,EAAEA,MAAM,IAAI,IAAI,IAAIA,MAAM,CAACG,gBAAgB,CAAC;AACtD;AACA,SAASC,WAAWA,CAACJ,MAAM,EAAE;EAC3B,OAAO,CAAC,EAAEA,MAAM,IAAI,IAAI,IAAIA,MAAM,CAACK,OAAO,CAAC;AAC7C;AACA,SAASC,KAAKA,CAACN,MAAM,EAAE;EACrB,OAAO,CAACA,MAAM,IAAI,IAAI,GAAG,KAAK,CAAC,GAAGA,MAAM,CAACK,OAAO,MAAME,SAAS;AACjE;AACA,SAASC,mBAAmBA,CAACP,QAAQ,EAAE;EACrC,OAAOA,QAAQ,CAACQ,IAAI,KAAK,mBAAmB;AAC9C;AACA,SAASC,WAAWA,CAAA,EAAG;EACrB,MAAMC,CAAC,GAAG,CAAC,CAAC;EACZ,KAAK,MAAMC,GAAG,IAAIhB,wBAAwB,CAACiB,QAAQ,EAAE;IACnDF,CAAC,CAACC,GAAG,CAAC,GAAG;MACPE,KAAK,EAAElB,wBAAwB,CAACiB,QAAQ,CAACD,GAAG;IAC9C,CAAC;EACH;EACA,OAAOD,CAAC;AACV;AACA,SAASI,yBAAyBA,CAACd,QAAQ,EAAEe,UAAU,EAAE;EACvD,MAAMC,QAAQ,GAAGhB,QAAQ,CAACiB,YAAY,CAAC,UAAU,CAAC;EAClD,MAAMC,KAAK,GAAGF,QAAQ,CAACE,KAAK;EAC5B,MAAMC,WAAW,GAAG,EAAE;EACtB,KAAK,IAAIC,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGF,KAAK,EAAEE,CAAC,EAAE,EAAE;IAC9B,MAAMC,IAAI,GAAGD,CAAC,GAAG,CAAC,KAAK,CAAC;IACxB,MAAME,CAAC,GAAGP,UAAU,GAAG,CAAC,GAAG,CAAC;IAC5B,IAAIM,IAAI,EAAE;MACRF,WAAW,CAACI,IAAI,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAED,CAAC,CAAC;IAC7C,CAAC,MAAM;MACLH,WAAW,CAACI,IAAI,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAED,CAAC,CAAC;IAC7C;EACF;EACA,OAAO,IAAI9B,KAAK,CAACgC,eAAe,CAACC,YAAY,CAACC,IAAI,CAACP,WAAW,CAAC,EAAE,CAAC,CAAC;AACrE;AACA,SAASQ,gBAAgBA,CAACC,aAAa,EAAE;EACvC,MAAMC,GAAG,GAAG1B,WAAW,CAACyB,aAAa,CAAC,GAAGA,aAAa,CAACxB,OAAO,GAAGwB,aAAa;EAC9E,IAAI,CAAC3B,UAAU,CAAC4B,GAAG,CAAC,EAAE;IACpB;IACA,IAAItB,mBAAmB,CAACsB,GAAG,CAAC,EAAE;MAC5B,MAAM,IAAIC,KAAK,CAAC,gDAAgD,CAAC;IACnE;IACA,MAAMC,MAAM,GAAGF,GAAG,CAACE,MAAM;IACzB,IAAIjC,cAAc,CAACiC,MAAM,CAAC,EAAE;MAC1B;MACA,IAAIxB,mBAAmB,CAACwB,MAAM,CAAC/B,QAAQ,CAAC,EAAE;QACxC,MAAM,IAAI8B,KAAK,CAAC,gDAAgD,CAAC;MACnE;MACA,OAAOC,MAAM,CAAC/B,QAAQ;IACxB;EACF,CAAC,MAAM;IACL,OAAO6B,GAAG;EACZ;AACF;AACA,SAASG,yBAAyBA,CAAChC,QAAQ,EAAEiC,QAAQ,EAAE;EACrD,IAAIjC,QAAQ,CAACkC,KAAK,EAAE;IAClBC,OAAO,CAACC,IAAI,CAAC,+EAA+E,CAAC;IAC7F,MAAMC,aAAa,GAAGrC,QAAQ,CAACsC,YAAY,CAAC,CAAC;IAC7CtC,QAAQ,CAACuC,IAAI,CAACF,aAAa,CAAC;IAC5BrC,QAAQ,CAACwC,QAAQ,CAAC,IAAI,CAAC;EACzB;EACA,MAAMC,cAAc,GAAG3B,yBAAyB,CAACd,QAAQ,EAAEiC,QAAQ,CAAC;EACpEjC,QAAQ,CAAC0C,YAAY,CAAC,aAAa,EAAED,cAAc,CAAC;AACtD;AACA,SAASE,sBAAsBA,CAAC;EAC9B3C,QAAQ,EAAE4C,cAAc;EACxBX,QAAQ,GAAG,KAAK;EAChB,GAAGY;AACL,CAAC,EAAE;EACDpD,MAAM,CAAC;IACLqD,qBAAqB,EAAEpD;EACzB,CAAC,CAAC;EACF,MAAM,CAACM,QAAQ,EAAE+C,WAAW,CAAC,GAAGxD,KAAK,CAACyD,QAAQ,CAAC,IAAI,CAAC;EACpDzD,KAAK,CAAC0D,eAAe,CAAC,MAAM;IAC1B,MAAMC,IAAI,GAAGvB,gBAAgB,CAACiB,cAAc,CAAC;IAC7C,IAAI,CAACM,IAAI,EAAE;MACT,MAAM,IAAIpB,KAAK,CAAC,iFAAiF,CAAC;IACpG;IACAE,yBAAyB,CAACkB,IAAI,EAAEjB,QAAQ,CAAC;IACzC,IAAI5B,KAAK,CAACuC,cAAc,CAAC,EAAE;MACzBG,WAAW,CAACG,IAAI,CAAC;IACnB;EACF,CAAC,EAAE,CAACjB,QAAQ,EAAEW,cAAc,CAAC,CAAC;EAC9B,MAAMO,QAAQ,GAAG9C,KAAK,CAACuC,cAAc,CAAC,GAAG5C,QAAQ,GAAG4C,cAAc;EAClE,OAAO,aAAarD,KAAK,CAAC6D,aAAa,CAAC7D,KAAK,CAAC8D,QAAQ,EAAE,IAAI,EAAEF,QAAQ,IAAI,aAAa5D,KAAK,CAAC6D,aAAa,CAAC,MAAM,EAAE;IACjHpD,QAAQ,EAAEmD;EACZ,CAAC,EAAE,aAAa5D,KAAK,CAAC6D,aAAa,CAAC,uBAAuB,EAAE9D,QAAQ,CAAC;IACpEgE,MAAM,EAAE,UAAU;IAClBC,WAAW,EAAE,IAAI;IACjBC,IAAI,EAAEhE,KAAK,CAACiE,UAAU;IACtBC,aAAa,EAAE,IAAI,CAAC;IAAA;;IAEpBC,mBAAmB,EAAE,CAAC;EACxB,CAAC,EAAEd,KAAK,EAAE;IACRe,UAAU,EAAE;MACVC,WAAW,EAAE,IAAI;MACjBC,SAAS,EAAE,KAAK;MAChBC,WAAW,EAAE,KAAK;MAClBC,gBAAgB,EAAE;IACpB;EACF,CAAC,CAAC,CAAC,CAAC,CAAC;AACP;AACA,SAASC,yBAAyBA,CAAC;EACjChC,QAAQ,GAAG,KAAK;EAChB,GAAGY;AACL,CAAC,EAAE;EACD,MAAMqB,SAAS,GAAG3E,KAAK,CAAC4E,MAAM,CAAC,IAAI,CAAC;EACpC,MAAMvD,QAAQ,GAAGrB,KAAK,CAAC6E,OAAO,CAAC,MAAM3D,WAAW,CAAC,CAAC,EAAE,CAACd,wBAAwB,CAACiB,QAAQ,CAAC,CAAC;EACxFhB,oBAAoB,CAACgB,QAAQ,EAAEiC,KAAK,CAAC;EACrCtD,KAAK,CAAC0D,eAAe,CAAC,MAAM;IAC1B,MAAMC,IAAI,GAAGvB,gBAAgB,CAACuC,SAAS,CAAC;IACxC,IAAI,CAAChB,IAAI,EAAE;MACT,MAAM,IAAIpB,KAAK,CAAC,yFAAyF,CAAC;IAC5G;IACA,MAAMuC,EAAE,GAAGnB,IAAI,CAACoB,KAAK,CAAC,CAAC;IACvBtC,yBAAyB,CAACkB,IAAI,EAAEjB,QAAQ,CAAC;IACzC,OAAO,MAAM;MACXiB,IAAI,CAACX,IAAI,CAAC8B,EAAE,CAAC;MACbA,EAAE,CAACE,OAAO,CAAC,CAAC;IACd,CAAC;EACH,CAAC,EAAE,CAACtC,QAAQ,CAAC,CAAC;EACd1C,KAAK,CAAC0D,eAAe,CAAC,MAAM;IAC1B,MAAMuB,UAAU,GAAGN,SAAS,CAAC9D,OAAO,CAAC2B,MAAM;IAC3C,MAAMsC,EAAE,GAAGG,UAAU,CAACC,QAAQ,CAACH,KAAK,CAAC,CAAC;IACtCzE,oBAAoB,CAAC2E,UAAU,CAACC,QAAQ,EAAE7D,QAAQ,CAAC;IACnD,OAAO,MAAM;MACX4D,UAAU,CAACC,QAAQ,CAACF,OAAO,CAAC,CAAC;MAC7BC,UAAU,CAACC,QAAQ,GAAGJ,EAAE;IAC1B,CAAC;EACH,CAAC,EAAE,EAAE,CAAC;EACN,OAAO,aAAa9E,KAAK,CAAC6D,aAAa,CAAC,UAAU,EAAE;IAClDsB,GAAG,EAAER;EACP,CAAC,CAAC;AACJ;AACA,SAASS,SAASA,CAAC;EACjB3E,QAAQ,EAAE4C,cAAc;EACxB,GAAGC;AACL,CAAC,EAAE;EACD,IAAID,cAAc,EAAE;IAClB,OAAO,aAAarD,KAAK,CAAC6D,aAAa,CAACT,sBAAsB,EAAErD,QAAQ,CAAC;MACvEU,QAAQ,EAAE4C;IACZ,CAAC,EAAEC,KAAK,CAAC,CAAC;EACZ;EACA,OAAO,aAAatD,KAAK,CAAC6D,aAAa,CAACa,yBAAyB,EAAEpB,KAAK,CAAC;AAC3E;AAEA,SAAS8B,SAAS", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}