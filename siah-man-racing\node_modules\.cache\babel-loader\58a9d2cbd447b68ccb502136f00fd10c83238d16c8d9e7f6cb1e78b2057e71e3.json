{"ast": null, "code": "import { Curve, Vector3 } from \"three\";\nclass GrannyKnot extends Curve {\n  getPoint(t, optionalTarget = new Vector3()) {\n    const point = optionalTarget;\n    t = 2 * Math.PI * t;\n    const x = -0.22 * Math.cos(t) - 1.28 * Math.sin(t) - 0.44 * Math.cos(3 * t) - 0.78 * Math.sin(3 * t);\n    const y = -0.1 * Math.cos(2 * t) - 0.27 * Math.sin(2 * t) + 0.38 * Math.cos(4 * t) + 0.46 * Math.sin(4 * t);\n    const z = 0.7 * Math.cos(3 * t) - 0.4 * Math.sin(3 * t);\n    return point.set(x, y, z).multiplyScalar(20);\n  }\n}\nclass HeartCurve extends Curve {\n  constructor(scale = 5) {\n    super();\n    this.scale = scale;\n  }\n  getPoint(t, optionalTarget = new Vector3()) {\n    const point = optionalTarget;\n    t *= 2 * Math.PI;\n    const x = 16 * Math.pow(Math.sin(t), 3);\n    const y = 13 * Math.cos(t) - 5 * Math.cos(2 * t) - 2 * Math.cos(3 * t) - Math.cos(4 * t);\n    const z = 0;\n    return point.set(x, y, z).multiplyScalar(this.scale);\n  }\n}\nclass VivianiCurve extends Curve {\n  constructor(scale = 70) {\n    super();\n    this.scale = scale;\n  }\n  getPoint(t, optionalTarget = new Vector3()) {\n    const point = optionalTarget;\n    t = t * 4 * Math.PI;\n    const a = this.scale / 2;\n    const x = a * (1 + Math.cos(t));\n    const y = a * Math.sin(t);\n    const z = 2 * a * Math.sin(t / 2);\n    return point.set(x, y, z);\n  }\n}\nclass KnotCurve extends Curve {\n  getPoint(t, optionalTarget = new Vector3()) {\n    const point = optionalTarget;\n    t *= 2 * Math.PI;\n    const R = 10;\n    const s = 50;\n    const x = s * Math.sin(t);\n    const y = Math.cos(t) * (R + s * Math.cos(t));\n    const z = Math.sin(t) * (R + s * Math.cos(t));\n    return point.set(x, y, z);\n  }\n}\nclass HelixCurve extends Curve {\n  getPoint(t, optionalTarget = new Vector3()) {\n    const point = optionalTarget;\n    const a = 30;\n    const b = 150;\n    const t2 = 2 * Math.PI * t * b / 30;\n    const x = Math.cos(t2) * a;\n    const y = Math.sin(t2) * a;\n    const z = b * t;\n    return point.set(x, y, z);\n  }\n}\nclass TrefoilKnot extends Curve {\n  constructor(scale = 10) {\n    super();\n    this.scale = scale;\n  }\n  getPoint(t, optionalTarget = new Vector3()) {\n    const point = optionalTarget;\n    t *= Math.PI * 2;\n    const x = (2 + Math.cos(3 * t)) * Math.cos(2 * t);\n    const y = (2 + Math.cos(3 * t)) * Math.sin(2 * t);\n    const z = Math.sin(3 * t);\n    return point.set(x, y, z).multiplyScalar(this.scale);\n  }\n}\nclass TorusKnot extends Curve {\n  constructor(scale = 10) {\n    super();\n    this.scale = scale;\n  }\n  getPoint(t, optionalTarget = new Vector3()) {\n    const point = optionalTarget;\n    const p = 3;\n    const q = 4;\n    t *= Math.PI * 2;\n    const x = (2 + Math.cos(q * t)) * Math.cos(p * t);\n    const y = (2 + Math.cos(q * t)) * Math.sin(p * t);\n    const z = Math.sin(q * t);\n    return point.set(x, y, z).multiplyScalar(this.scale);\n  }\n}\nclass CinquefoilKnot extends Curve {\n  constructor(scale = 10) {\n    super();\n    this.scale = scale;\n  }\n  getPoint(t, optionalTarget = new Vector3()) {\n    const point = optionalTarget;\n    const p = 2;\n    const q = 5;\n    t *= Math.PI * 2;\n    const x = (2 + Math.cos(q * t)) * Math.cos(p * t);\n    const y = (2 + Math.cos(q * t)) * Math.sin(p * t);\n    const z = Math.sin(q * t);\n    return point.set(x, y, z).multiplyScalar(this.scale);\n  }\n}\nclass TrefoilPolynomialKnot extends Curve {\n  constructor(scale = 10) {\n    super();\n    this.scale = scale;\n  }\n  getPoint(t, optionalTarget = new Vector3()) {\n    const point = optionalTarget;\n    t = t * 4 - 2;\n    const x = Math.pow(t, 3) - 3 * t;\n    const y = Math.pow(t, 4) - 4 * t * t;\n    const z = 1 / 5 * Math.pow(t, 5) - 2 * t;\n    return point.set(x, y, z).multiplyScalar(this.scale);\n  }\n}\nfunction scaleTo(x, y, t) {\n  const r = y - x;\n  return t * r + x;\n}\nclass FigureEightPolynomialKnot extends Curve {\n  constructor(scale = 1) {\n    super();\n    this.scale = scale;\n  }\n  getPoint(t, optionalTarget = new Vector3()) {\n    const point = optionalTarget;\n    t = scaleTo(-4, 4, t);\n    const x = 2 / 5 * t * (t * t - 7) * (t * t - 10);\n    const y = Math.pow(t, 4) - 13 * t * t;\n    const z = 1 / 10 * t * (t * t - 4) * (t * t - 9) * (t * t - 12);\n    return point.set(x, y, z).multiplyScalar(this.scale);\n  }\n}\nclass DecoratedTorusKnot4a extends Curve {\n  constructor(scale = 40) {\n    super();\n    this.scale = scale;\n  }\n  getPoint(t, optionalTarget = new Vector3()) {\n    const point = optionalTarget;\n    t *= Math.PI * 2;\n    const x = Math.cos(2 * t) * (1 + 0.6 * (Math.cos(5 * t) + 0.75 * Math.cos(10 * t)));\n    const y = Math.sin(2 * t) * (1 + 0.6 * (Math.cos(5 * t) + 0.75 * Math.cos(10 * t)));\n    const z = 0.35 * Math.sin(5 * t);\n    return point.set(x, y, z).multiplyScalar(this.scale);\n  }\n}\nclass DecoratedTorusKnot4b extends Curve {\n  constructor(scale = 40) {\n    super();\n    this.scale = scale;\n  }\n  getPoint(t, optionalTarget = new Vector3()) {\n    const point = optionalTarget;\n    const fi = t * Math.PI * 2;\n    const x = Math.cos(2 * fi) * (1 + 0.45 * Math.cos(3 * fi) + 0.4 * Math.cos(9 * fi));\n    const y = Math.sin(2 * fi) * (1 + 0.45 * Math.cos(3 * fi) + 0.4 * Math.cos(9 * fi));\n    const z = 0.2 * Math.sin(9 * fi);\n    return point.set(x, y, z).multiplyScalar(this.scale);\n  }\n}\nclass DecoratedTorusKnot5a extends Curve {\n  constructor(scale = 40) {\n    super();\n    this.scale = scale;\n  }\n  getPoint(t, optionalTarget = new Vector3()) {\n    const point = optionalTarget;\n    const fi = t * Math.PI * 2;\n    const x = Math.cos(3 * fi) * (1 + 0.3 * Math.cos(5 * fi) + 0.5 * Math.cos(10 * fi));\n    const y = Math.sin(3 * fi) * (1 + 0.3 * Math.cos(5 * fi) + 0.5 * Math.cos(10 * fi));\n    const z = 0.2 * Math.sin(20 * fi);\n    return point.set(x, y, z).multiplyScalar(this.scale);\n  }\n}\nclass DecoratedTorusKnot5c extends Curve {\n  constructor(scale = 40) {\n    super();\n    this.scale = scale;\n  }\n  getPoint(t, optionalTarget = new Vector3()) {\n    const point = optionalTarget;\n    const fi = t * Math.PI * 2;\n    const x = Math.cos(4 * fi) * (1 + 0.5 * (Math.cos(5 * fi) + 0.4 * Math.cos(20 * fi)));\n    const y = Math.sin(4 * fi) * (1 + 0.5 * (Math.cos(5 * fi) + 0.4 * Math.cos(20 * fi)));\n    const z = 0.35 * Math.sin(15 * fi);\n    return point.set(x, y, z).multiplyScalar(this.scale);\n  }\n}\nexport { CinquefoilKnot, DecoratedTorusKnot4a, DecoratedTorusKnot4b, DecoratedTorusKnot5a, DecoratedTorusKnot5c, FigureEightPolynomialKnot, GrannyKnot, HeartCurve, HelixCurve, KnotCurve, TorusKnot, TrefoilKnot, TrefoilPolynomialKnot, VivianiCurve };", "map": {"version": 3, "names": ["<PERSON><PERSON><PERSON>", "Curve", "getPoint", "t", "optionalTarget", "Vector3", "point", "Math", "PI", "x", "cos", "sin", "y", "z", "set", "multiplyScalar", "HeartCurve", "constructor", "scale", "pow", "VivianiCurve", "a", "KnotCurve", "R", "s", "HelixCurve", "b", "t2", "TrefoilKnot", "<PERSON><PERSON><PERSON><PERSON>", "p", "q", "CinquefoilKnot", "TrefoilPolynomialKnot", "scaleTo", "r", "FigureEightPolynomialKnot", "DecoratedTorusKnot4a", "DecoratedTorusKnot4b", "fi", "DecoratedTorusKnot5a", "DecoratedTorusKnot5c"], "sources": ["C:\\Users\\<USER>\\Documents\\Apps_Creations\\SiahMan_Racing4Speed_WebGame\\siah-man-racing\\node_modules\\src\\curves\\CurveExtras.js"], "sourcesContent": ["import { Curve, Vector3 } from 'three'\n\n/**\n * A bunch of parametric curves\n *\n * Formulas collected from various sources\n * http://mathworld.wolfram.com/HeartCurve.html\n * http://en.wikipedia.org/wiki/Viviani%27s_curve\n * http://www.mi.sanu.ac.rs/vismath/taylorapril2011/Taylor.pdf\n * https://prideout.net/blog/old/blog/index.html@p=44.html\n */\n\n// GrannyKnot\n\nclass GrannyKnot extends Curve {\n  getPoint(t, optionalTarget = new Vector3()) {\n    const point = optionalTarget\n\n    t = 2 * Math.PI * t\n\n    const x = -0.22 * Math.cos(t) - 1.28 * Math.sin(t) - 0.44 * Math.cos(3 * t) - 0.78 * Math.sin(3 * t)\n    const y = -0.1 * Math.cos(2 * t) - 0.27 * Math.sin(2 * t) + 0.38 * Math.cos(4 * t) + 0.46 * Math.sin(4 * t)\n    const z = 0.7 * Math.cos(3 * t) - 0.4 * Math.sin(3 * t)\n\n    return point.set(x, y, z).multiplyScalar(20)\n  }\n}\n\n// HeartCurve\n\nclass HeartCurve extends Curve {\n  constructor(scale = 5) {\n    super()\n\n    this.scale = scale\n  }\n\n  getPoint(t, optionalTarget = new Vector3()) {\n    const point = optionalTarget\n\n    t *= 2 * Math.PI\n\n    const x = 16 * Math.pow(Math.sin(t), 3)\n    const y = 13 * Math.cos(t) - 5 * Math.cos(2 * t) - 2 * Math.cos(3 * t) - Math.cos(4 * t)\n    const z = 0\n\n    return point.set(x, y, z).multiplyScalar(this.scale)\n  }\n}\n\n// Viviani's Curve\n\nclass VivianiCurve extends Curve {\n  constructor(scale = 70) {\n    super()\n\n    this.scale = scale\n  }\n\n  getPoint(t, optionalTarget = new Vector3()) {\n    const point = optionalTarget\n\n    t = t * 4 * Math.PI // normalized to 0..1\n    const a = this.scale / 2\n\n    const x = a * (1 + Math.cos(t))\n    const y = a * Math.sin(t)\n    const z = 2 * a * Math.sin(t / 2)\n\n    return point.set(x, y, z)\n  }\n}\n\n// KnotCurve\n\nclass KnotCurve extends Curve {\n  getPoint(t, optionalTarget = new Vector3()) {\n    const point = optionalTarget\n\n    t *= 2 * Math.PI\n\n    const R = 10\n    const s = 50\n\n    const x = s * Math.sin(t)\n    const y = Math.cos(t) * (R + s * Math.cos(t))\n    const z = Math.sin(t) * (R + s * Math.cos(t))\n\n    return point.set(x, y, z)\n  }\n}\n\n// HelixCurve\n\nclass HelixCurve extends Curve {\n  getPoint(t, optionalTarget = new Vector3()) {\n    const point = optionalTarget\n\n    const a = 30 // radius\n    const b = 150 // height\n\n    const t2 = (2 * Math.PI * t * b) / 30\n\n    const x = Math.cos(t2) * a\n    const y = Math.sin(t2) * a\n    const z = b * t\n\n    return point.set(x, y, z)\n  }\n}\n\n// TrefoilKnot\n\nclass TrefoilKnot extends Curve {\n  constructor(scale = 10) {\n    super()\n\n    this.scale = scale\n  }\n\n  getPoint(t, optionalTarget = new Vector3()) {\n    const point = optionalTarget\n\n    t *= Math.PI * 2\n\n    const x = (2 + Math.cos(3 * t)) * Math.cos(2 * t)\n    const y = (2 + Math.cos(3 * t)) * Math.sin(2 * t)\n    const z = Math.sin(3 * t)\n\n    return point.set(x, y, z).multiplyScalar(this.scale)\n  }\n}\n\n// TorusKnot\n\nclass TorusKnot extends Curve {\n  constructor(scale = 10) {\n    super()\n\n    this.scale = scale\n  }\n\n  getPoint(t, optionalTarget = new Vector3()) {\n    const point = optionalTarget\n\n    const p = 3\n    const q = 4\n\n    t *= Math.PI * 2\n\n    const x = (2 + Math.cos(q * t)) * Math.cos(p * t)\n    const y = (2 + Math.cos(q * t)) * Math.sin(p * t)\n    const z = Math.sin(q * t)\n\n    return point.set(x, y, z).multiplyScalar(this.scale)\n  }\n}\n\n// CinquefoilKnot\n\nclass CinquefoilKnot extends Curve {\n  constructor(scale = 10) {\n    super()\n\n    this.scale = scale\n  }\n\n  getPoint(t, optionalTarget = new Vector3()) {\n    const point = optionalTarget\n\n    const p = 2\n    const q = 5\n\n    t *= Math.PI * 2\n\n    const x = (2 + Math.cos(q * t)) * Math.cos(p * t)\n    const y = (2 + Math.cos(q * t)) * Math.sin(p * t)\n    const z = Math.sin(q * t)\n\n    return point.set(x, y, z).multiplyScalar(this.scale)\n  }\n}\n\n// TrefoilPolynomialKnot\n\nclass TrefoilPolynomialKnot extends Curve {\n  constructor(scale = 10) {\n    super()\n\n    this.scale = scale\n  }\n\n  getPoint(t, optionalTarget = new Vector3()) {\n    const point = optionalTarget\n\n    t = t * 4 - 2\n\n    const x = Math.pow(t, 3) - 3 * t\n    const y = Math.pow(t, 4) - 4 * t * t\n    const z = (1 / 5) * Math.pow(t, 5) - 2 * t\n\n    return point.set(x, y, z).multiplyScalar(this.scale)\n  }\n}\n\nfunction scaleTo(x, y, t) {\n  const r = y - x\n  return t * r + x\n}\n\n// FigureEightPolynomialKnot\n\nclass FigureEightPolynomialKnot extends Curve {\n  constructor(scale = 1) {\n    super()\n\n    this.scale = scale\n  }\n\n  getPoint(t, optionalTarget = new Vector3()) {\n    const point = optionalTarget\n\n    t = scaleTo(-4, 4, t)\n\n    const x = (2 / 5) * t * (t * t - 7) * (t * t - 10)\n    const y = Math.pow(t, 4) - 13 * t * t\n    const z = (1 / 10) * t * (t * t - 4) * (t * t - 9) * (t * t - 12)\n\n    return point.set(x, y, z).multiplyScalar(this.scale)\n  }\n}\n\n// DecoratedTorusKnot4a\n\nclass DecoratedTorusKnot4a extends Curve {\n  constructor(scale = 40) {\n    super()\n\n    this.scale = scale\n  }\n\n  getPoint(t, optionalTarget = new Vector3()) {\n    const point = optionalTarget\n\n    t *= Math.PI * 2\n\n    const x = Math.cos(2 * t) * (1 + 0.6 * (Math.cos(5 * t) + 0.75 * Math.cos(10 * t)))\n    const y = Math.sin(2 * t) * (1 + 0.6 * (Math.cos(5 * t) + 0.75 * Math.cos(10 * t)))\n    const z = 0.35 * Math.sin(5 * t)\n\n    return point.set(x, y, z).multiplyScalar(this.scale)\n  }\n}\n\n// DecoratedTorusKnot4b\n\nclass DecoratedTorusKnot4b extends Curve {\n  constructor(scale = 40) {\n    super()\n\n    this.scale = scale\n  }\n\n  getPoint(t, optionalTarget = new Vector3()) {\n    const point = optionalTarget\n\n    const fi = t * Math.PI * 2\n\n    const x = Math.cos(2 * fi) * (1 + 0.45 * Math.cos(3 * fi) + 0.4 * Math.cos(9 * fi))\n    const y = Math.sin(2 * fi) * (1 + 0.45 * Math.cos(3 * fi) + 0.4 * Math.cos(9 * fi))\n    const z = 0.2 * Math.sin(9 * fi)\n\n    return point.set(x, y, z).multiplyScalar(this.scale)\n  }\n}\n\n// DecoratedTorusKnot5a\n\nclass DecoratedTorusKnot5a extends Curve {\n  constructor(scale = 40) {\n    super()\n\n    this.scale = scale\n  }\n\n  getPoint(t, optionalTarget = new Vector3()) {\n    const point = optionalTarget\n\n    const fi = t * Math.PI * 2\n\n    const x = Math.cos(3 * fi) * (1 + 0.3 * Math.cos(5 * fi) + 0.5 * Math.cos(10 * fi))\n    const y = Math.sin(3 * fi) * (1 + 0.3 * Math.cos(5 * fi) + 0.5 * Math.cos(10 * fi))\n    const z = 0.2 * Math.sin(20 * fi)\n\n    return point.set(x, y, z).multiplyScalar(this.scale)\n  }\n}\n\n// DecoratedTorusKnot5c\n\nclass DecoratedTorusKnot5c extends Curve {\n  constructor(scale = 40) {\n    super()\n\n    this.scale = scale\n  }\n\n  getPoint(t, optionalTarget = new Vector3()) {\n    const point = optionalTarget\n\n    const fi = t * Math.PI * 2\n\n    const x = Math.cos(4 * fi) * (1 + 0.5 * (Math.cos(5 * fi) + 0.4 * Math.cos(20 * fi)))\n    const y = Math.sin(4 * fi) * (1 + 0.5 * (Math.cos(5 * fi) + 0.4 * Math.cos(20 * fi)))\n    const z = 0.35 * Math.sin(15 * fi)\n\n    return point.set(x, y, z).multiplyScalar(this.scale)\n  }\n}\n\nexport {\n  GrannyKnot,\n  HeartCurve,\n  VivianiCurve,\n  KnotCurve,\n  HelixCurve,\n  TrefoilKnot,\n  TorusKnot,\n  CinquefoilKnot,\n  TrefoilPolynomialKnot,\n  FigureEightPolynomialKnot,\n  DecoratedTorusKnot4a,\n  DecoratedTorusKnot4b,\n  DecoratedTorusKnot5a,\n  DecoratedTorusKnot5c,\n}\n"], "mappings": ";AAcA,MAAMA,UAAA,SAAmBC,KAAA,CAAM;EAC7BC,SAASC,CAAA,EAAGC,cAAA,GAAiB,IAAIC,OAAA,CAAO,GAAI;IAC1C,MAAMC,KAAA,GAAQF,cAAA;IAEdD,CAAA,GAAI,IAAII,IAAA,CAAKC,EAAA,GAAKL,CAAA;IAElB,MAAMM,CAAA,GAAI,QAAQF,IAAA,CAAKG,GAAA,CAAIP,CAAC,IAAI,OAAOI,IAAA,CAAKI,GAAA,CAAIR,CAAC,IAAI,OAAOI,IAAA,CAAKG,GAAA,CAAI,IAAIP,CAAC,IAAI,OAAOI,IAAA,CAAKI,GAAA,CAAI,IAAIR,CAAC;IACnG,MAAMS,CAAA,GAAI,OAAOL,IAAA,CAAKG,GAAA,CAAI,IAAIP,CAAC,IAAI,OAAOI,IAAA,CAAKI,GAAA,CAAI,IAAIR,CAAC,IAAI,OAAOI,IAAA,CAAKG,GAAA,CAAI,IAAIP,CAAC,IAAI,OAAOI,IAAA,CAAKI,GAAA,CAAI,IAAIR,CAAC;IAC1G,MAAMU,CAAA,GAAI,MAAMN,IAAA,CAAKG,GAAA,CAAI,IAAIP,CAAC,IAAI,MAAMI,IAAA,CAAKI,GAAA,CAAI,IAAIR,CAAC;IAEtD,OAAOG,KAAA,CAAMQ,GAAA,CAAIL,CAAA,EAAGG,CAAA,EAAGC,CAAC,EAAEE,cAAA,CAAe,EAAE;EAC5C;AACH;AAIA,MAAMC,UAAA,SAAmBf,KAAA,CAAM;EAC7BgB,YAAYC,KAAA,GAAQ,GAAG;IACrB,MAAO;IAEP,KAAKA,KAAA,GAAQA,KAAA;EACd;EAEDhB,SAASC,CAAA,EAAGC,cAAA,GAAiB,IAAIC,OAAA,CAAO,GAAI;IAC1C,MAAMC,KAAA,GAAQF,cAAA;IAEdD,CAAA,IAAK,IAAII,IAAA,CAAKC,EAAA;IAEd,MAAMC,CAAA,GAAI,KAAKF,IAAA,CAAKY,GAAA,CAAIZ,IAAA,CAAKI,GAAA,CAAIR,CAAC,GAAG,CAAC;IACtC,MAAMS,CAAA,GAAI,KAAKL,IAAA,CAAKG,GAAA,CAAIP,CAAC,IAAI,IAAII,IAAA,CAAKG,GAAA,CAAI,IAAIP,CAAC,IAAI,IAAII,IAAA,CAAKG,GAAA,CAAI,IAAIP,CAAC,IAAII,IAAA,CAAKG,GAAA,CAAI,IAAIP,CAAC;IACvF,MAAMU,CAAA,GAAI;IAEV,OAAOP,KAAA,CAAMQ,GAAA,CAAIL,CAAA,EAAGG,CAAA,EAAGC,CAAC,EAAEE,cAAA,CAAe,KAAKG,KAAK;EACpD;AACH;AAIA,MAAME,YAAA,SAAqBnB,KAAA,CAAM;EAC/BgB,YAAYC,KAAA,GAAQ,IAAI;IACtB,MAAO;IAEP,KAAKA,KAAA,GAAQA,KAAA;EACd;EAEDhB,SAASC,CAAA,EAAGC,cAAA,GAAiB,IAAIC,OAAA,CAAO,GAAI;IAC1C,MAAMC,KAAA,GAAQF,cAAA;IAEdD,CAAA,GAAIA,CAAA,GAAI,IAAII,IAAA,CAAKC,EAAA;IACjB,MAAMa,CAAA,GAAI,KAAKH,KAAA,GAAQ;IAEvB,MAAMT,CAAA,GAAIY,CAAA,IAAK,IAAId,IAAA,CAAKG,GAAA,CAAIP,CAAC;IAC7B,MAAMS,CAAA,GAAIS,CAAA,GAAId,IAAA,CAAKI,GAAA,CAAIR,CAAC;IACxB,MAAMU,CAAA,GAAI,IAAIQ,CAAA,GAAId,IAAA,CAAKI,GAAA,CAAIR,CAAA,GAAI,CAAC;IAEhC,OAAOG,KAAA,CAAMQ,GAAA,CAAIL,CAAA,EAAGG,CAAA,EAAGC,CAAC;EACzB;AACH;AAIA,MAAMS,SAAA,SAAkBrB,KAAA,CAAM;EAC5BC,SAASC,CAAA,EAAGC,cAAA,GAAiB,IAAIC,OAAA,CAAO,GAAI;IAC1C,MAAMC,KAAA,GAAQF,cAAA;IAEdD,CAAA,IAAK,IAAII,IAAA,CAAKC,EAAA;IAEd,MAAMe,CAAA,GAAI;IACV,MAAMC,CAAA,GAAI;IAEV,MAAMf,CAAA,GAAIe,CAAA,GAAIjB,IAAA,CAAKI,GAAA,CAAIR,CAAC;IACxB,MAAMS,CAAA,GAAIL,IAAA,CAAKG,GAAA,CAAIP,CAAC,KAAKoB,CAAA,GAAIC,CAAA,GAAIjB,IAAA,CAAKG,GAAA,CAAIP,CAAC;IAC3C,MAAMU,CAAA,GAAIN,IAAA,CAAKI,GAAA,CAAIR,CAAC,KAAKoB,CAAA,GAAIC,CAAA,GAAIjB,IAAA,CAAKG,GAAA,CAAIP,CAAC;IAE3C,OAAOG,KAAA,CAAMQ,GAAA,CAAIL,CAAA,EAAGG,CAAA,EAAGC,CAAC;EACzB;AACH;AAIA,MAAMY,UAAA,SAAmBxB,KAAA,CAAM;EAC7BC,SAASC,CAAA,EAAGC,cAAA,GAAiB,IAAIC,OAAA,CAAO,GAAI;IAC1C,MAAMC,KAAA,GAAQF,cAAA;IAEd,MAAMiB,CAAA,GAAI;IACV,MAAMK,CAAA,GAAI;IAEV,MAAMC,EAAA,GAAM,IAAIpB,IAAA,CAAKC,EAAA,GAAKL,CAAA,GAAIuB,CAAA,GAAK;IAEnC,MAAMjB,CAAA,GAAIF,IAAA,CAAKG,GAAA,CAAIiB,EAAE,IAAIN,CAAA;IACzB,MAAMT,CAAA,GAAIL,IAAA,CAAKI,GAAA,CAAIgB,EAAE,IAAIN,CAAA;IACzB,MAAMR,CAAA,GAAIa,CAAA,GAAIvB,CAAA;IAEd,OAAOG,KAAA,CAAMQ,GAAA,CAAIL,CAAA,EAAGG,CAAA,EAAGC,CAAC;EACzB;AACH;AAIA,MAAMe,WAAA,SAAoB3B,KAAA,CAAM;EAC9BgB,YAAYC,KAAA,GAAQ,IAAI;IACtB,MAAO;IAEP,KAAKA,KAAA,GAAQA,KAAA;EACd;EAEDhB,SAASC,CAAA,EAAGC,cAAA,GAAiB,IAAIC,OAAA,CAAO,GAAI;IAC1C,MAAMC,KAAA,GAAQF,cAAA;IAEdD,CAAA,IAAKI,IAAA,CAAKC,EAAA,GAAK;IAEf,MAAMC,CAAA,IAAK,IAAIF,IAAA,CAAKG,GAAA,CAAI,IAAIP,CAAC,KAAKI,IAAA,CAAKG,GAAA,CAAI,IAAIP,CAAC;IAChD,MAAMS,CAAA,IAAK,IAAIL,IAAA,CAAKG,GAAA,CAAI,IAAIP,CAAC,KAAKI,IAAA,CAAKI,GAAA,CAAI,IAAIR,CAAC;IAChD,MAAMU,CAAA,GAAIN,IAAA,CAAKI,GAAA,CAAI,IAAIR,CAAC;IAExB,OAAOG,KAAA,CAAMQ,GAAA,CAAIL,CAAA,EAAGG,CAAA,EAAGC,CAAC,EAAEE,cAAA,CAAe,KAAKG,KAAK;EACpD;AACH;AAIA,MAAMW,SAAA,SAAkB5B,KAAA,CAAM;EAC5BgB,YAAYC,KAAA,GAAQ,IAAI;IACtB,MAAO;IAEP,KAAKA,KAAA,GAAQA,KAAA;EACd;EAEDhB,SAASC,CAAA,EAAGC,cAAA,GAAiB,IAAIC,OAAA,CAAO,GAAI;IAC1C,MAAMC,KAAA,GAAQF,cAAA;IAEd,MAAM0B,CAAA,GAAI;IACV,MAAMC,CAAA,GAAI;IAEV5B,CAAA,IAAKI,IAAA,CAAKC,EAAA,GAAK;IAEf,MAAMC,CAAA,IAAK,IAAIF,IAAA,CAAKG,GAAA,CAAIqB,CAAA,GAAI5B,CAAC,KAAKI,IAAA,CAAKG,GAAA,CAAIoB,CAAA,GAAI3B,CAAC;IAChD,MAAMS,CAAA,IAAK,IAAIL,IAAA,CAAKG,GAAA,CAAIqB,CAAA,GAAI5B,CAAC,KAAKI,IAAA,CAAKI,GAAA,CAAImB,CAAA,GAAI3B,CAAC;IAChD,MAAMU,CAAA,GAAIN,IAAA,CAAKI,GAAA,CAAIoB,CAAA,GAAI5B,CAAC;IAExB,OAAOG,KAAA,CAAMQ,GAAA,CAAIL,CAAA,EAAGG,CAAA,EAAGC,CAAC,EAAEE,cAAA,CAAe,KAAKG,KAAK;EACpD;AACH;AAIA,MAAMc,cAAA,SAAuB/B,KAAA,CAAM;EACjCgB,YAAYC,KAAA,GAAQ,IAAI;IACtB,MAAO;IAEP,KAAKA,KAAA,GAAQA,KAAA;EACd;EAEDhB,SAASC,CAAA,EAAGC,cAAA,GAAiB,IAAIC,OAAA,CAAO,GAAI;IAC1C,MAAMC,KAAA,GAAQF,cAAA;IAEd,MAAM0B,CAAA,GAAI;IACV,MAAMC,CAAA,GAAI;IAEV5B,CAAA,IAAKI,IAAA,CAAKC,EAAA,GAAK;IAEf,MAAMC,CAAA,IAAK,IAAIF,IAAA,CAAKG,GAAA,CAAIqB,CAAA,GAAI5B,CAAC,KAAKI,IAAA,CAAKG,GAAA,CAAIoB,CAAA,GAAI3B,CAAC;IAChD,MAAMS,CAAA,IAAK,IAAIL,IAAA,CAAKG,GAAA,CAAIqB,CAAA,GAAI5B,CAAC,KAAKI,IAAA,CAAKI,GAAA,CAAImB,CAAA,GAAI3B,CAAC;IAChD,MAAMU,CAAA,GAAIN,IAAA,CAAKI,GAAA,CAAIoB,CAAA,GAAI5B,CAAC;IAExB,OAAOG,KAAA,CAAMQ,GAAA,CAAIL,CAAA,EAAGG,CAAA,EAAGC,CAAC,EAAEE,cAAA,CAAe,KAAKG,KAAK;EACpD;AACH;AAIA,MAAMe,qBAAA,SAA8BhC,KAAA,CAAM;EACxCgB,YAAYC,KAAA,GAAQ,IAAI;IACtB,MAAO;IAEP,KAAKA,KAAA,GAAQA,KAAA;EACd;EAEDhB,SAASC,CAAA,EAAGC,cAAA,GAAiB,IAAIC,OAAA,CAAO,GAAI;IAC1C,MAAMC,KAAA,GAAQF,cAAA;IAEdD,CAAA,GAAIA,CAAA,GAAI,IAAI;IAEZ,MAAMM,CAAA,GAAIF,IAAA,CAAKY,GAAA,CAAIhB,CAAA,EAAG,CAAC,IAAI,IAAIA,CAAA;IAC/B,MAAMS,CAAA,GAAIL,IAAA,CAAKY,GAAA,CAAIhB,CAAA,EAAG,CAAC,IAAI,IAAIA,CAAA,GAAIA,CAAA;IACnC,MAAMU,CAAA,GAAK,IAAI,IAAKN,IAAA,CAAKY,GAAA,CAAIhB,CAAA,EAAG,CAAC,IAAI,IAAIA,CAAA;IAEzC,OAAOG,KAAA,CAAMQ,GAAA,CAAIL,CAAA,EAAGG,CAAA,EAAGC,CAAC,EAAEE,cAAA,CAAe,KAAKG,KAAK;EACpD;AACH;AAEA,SAASgB,QAAQzB,CAAA,EAAGG,CAAA,EAAGT,CAAA,EAAG;EACxB,MAAMgC,CAAA,GAAIvB,CAAA,GAAIH,CAAA;EACd,OAAON,CAAA,GAAIgC,CAAA,GAAI1B,CAAA;AACjB;AAIA,MAAM2B,yBAAA,SAAkCnC,KAAA,CAAM;EAC5CgB,YAAYC,KAAA,GAAQ,GAAG;IACrB,MAAO;IAEP,KAAKA,KAAA,GAAQA,KAAA;EACd;EAEDhB,SAASC,CAAA,EAAGC,cAAA,GAAiB,IAAIC,OAAA,CAAO,GAAI;IAC1C,MAAMC,KAAA,GAAQF,cAAA;IAEdD,CAAA,GAAI+B,OAAA,CAAQ,IAAI,GAAG/B,CAAC;IAEpB,MAAMM,CAAA,GAAK,IAAI,IAAKN,CAAA,IAAKA,CAAA,GAAIA,CAAA,GAAI,MAAMA,CAAA,GAAIA,CAAA,GAAI;IAC/C,MAAMS,CAAA,GAAIL,IAAA,CAAKY,GAAA,CAAIhB,CAAA,EAAG,CAAC,IAAI,KAAKA,CAAA,GAAIA,CAAA;IACpC,MAAMU,CAAA,GAAK,IAAI,KAAMV,CAAA,IAAKA,CAAA,GAAIA,CAAA,GAAI,MAAMA,CAAA,GAAIA,CAAA,GAAI,MAAMA,CAAA,GAAIA,CAAA,GAAI;IAE9D,OAAOG,KAAA,CAAMQ,GAAA,CAAIL,CAAA,EAAGG,CAAA,EAAGC,CAAC,EAAEE,cAAA,CAAe,KAAKG,KAAK;EACpD;AACH;AAIA,MAAMmB,oBAAA,SAA6BpC,KAAA,CAAM;EACvCgB,YAAYC,KAAA,GAAQ,IAAI;IACtB,MAAO;IAEP,KAAKA,KAAA,GAAQA,KAAA;EACd;EAEDhB,SAASC,CAAA,EAAGC,cAAA,GAAiB,IAAIC,OAAA,CAAO,GAAI;IAC1C,MAAMC,KAAA,GAAQF,cAAA;IAEdD,CAAA,IAAKI,IAAA,CAAKC,EAAA,GAAK;IAEf,MAAMC,CAAA,GAAIF,IAAA,CAAKG,GAAA,CAAI,IAAIP,CAAC,KAAK,IAAI,OAAOI,IAAA,CAAKG,GAAA,CAAI,IAAIP,CAAC,IAAI,OAAOI,IAAA,CAAKG,GAAA,CAAI,KAAKP,CAAC;IAChF,MAAMS,CAAA,GAAIL,IAAA,CAAKI,GAAA,CAAI,IAAIR,CAAC,KAAK,IAAI,OAAOI,IAAA,CAAKG,GAAA,CAAI,IAAIP,CAAC,IAAI,OAAOI,IAAA,CAAKG,GAAA,CAAI,KAAKP,CAAC;IAChF,MAAMU,CAAA,GAAI,OAAON,IAAA,CAAKI,GAAA,CAAI,IAAIR,CAAC;IAE/B,OAAOG,KAAA,CAAMQ,GAAA,CAAIL,CAAA,EAAGG,CAAA,EAAGC,CAAC,EAAEE,cAAA,CAAe,KAAKG,KAAK;EACpD;AACH;AAIA,MAAMoB,oBAAA,SAA6BrC,KAAA,CAAM;EACvCgB,YAAYC,KAAA,GAAQ,IAAI;IACtB,MAAO;IAEP,KAAKA,KAAA,GAAQA,KAAA;EACd;EAEDhB,SAASC,CAAA,EAAGC,cAAA,GAAiB,IAAIC,OAAA,CAAO,GAAI;IAC1C,MAAMC,KAAA,GAAQF,cAAA;IAEd,MAAMmC,EAAA,GAAKpC,CAAA,GAAII,IAAA,CAAKC,EAAA,GAAK;IAEzB,MAAMC,CAAA,GAAIF,IAAA,CAAKG,GAAA,CAAI,IAAI6B,EAAE,KAAK,IAAI,OAAOhC,IAAA,CAAKG,GAAA,CAAI,IAAI6B,EAAE,IAAI,MAAMhC,IAAA,CAAKG,GAAA,CAAI,IAAI6B,EAAE;IACjF,MAAM3B,CAAA,GAAIL,IAAA,CAAKI,GAAA,CAAI,IAAI4B,EAAE,KAAK,IAAI,OAAOhC,IAAA,CAAKG,GAAA,CAAI,IAAI6B,EAAE,IAAI,MAAMhC,IAAA,CAAKG,GAAA,CAAI,IAAI6B,EAAE;IACjF,MAAM1B,CAAA,GAAI,MAAMN,IAAA,CAAKI,GAAA,CAAI,IAAI4B,EAAE;IAE/B,OAAOjC,KAAA,CAAMQ,GAAA,CAAIL,CAAA,EAAGG,CAAA,EAAGC,CAAC,EAAEE,cAAA,CAAe,KAAKG,KAAK;EACpD;AACH;AAIA,MAAMsB,oBAAA,SAA6BvC,KAAA,CAAM;EACvCgB,YAAYC,KAAA,GAAQ,IAAI;IACtB,MAAO;IAEP,KAAKA,KAAA,GAAQA,KAAA;EACd;EAEDhB,SAASC,CAAA,EAAGC,cAAA,GAAiB,IAAIC,OAAA,CAAO,GAAI;IAC1C,MAAMC,KAAA,GAAQF,cAAA;IAEd,MAAMmC,EAAA,GAAKpC,CAAA,GAAII,IAAA,CAAKC,EAAA,GAAK;IAEzB,MAAMC,CAAA,GAAIF,IAAA,CAAKG,GAAA,CAAI,IAAI6B,EAAE,KAAK,IAAI,MAAMhC,IAAA,CAAKG,GAAA,CAAI,IAAI6B,EAAE,IAAI,MAAMhC,IAAA,CAAKG,GAAA,CAAI,KAAK6B,EAAE;IACjF,MAAM3B,CAAA,GAAIL,IAAA,CAAKI,GAAA,CAAI,IAAI4B,EAAE,KAAK,IAAI,MAAMhC,IAAA,CAAKG,GAAA,CAAI,IAAI6B,EAAE,IAAI,MAAMhC,IAAA,CAAKG,GAAA,CAAI,KAAK6B,EAAE;IACjF,MAAM1B,CAAA,GAAI,MAAMN,IAAA,CAAKI,GAAA,CAAI,KAAK4B,EAAE;IAEhC,OAAOjC,KAAA,CAAMQ,GAAA,CAAIL,CAAA,EAAGG,CAAA,EAAGC,CAAC,EAAEE,cAAA,CAAe,KAAKG,KAAK;EACpD;AACH;AAIA,MAAMuB,oBAAA,SAA6BxC,KAAA,CAAM;EACvCgB,YAAYC,KAAA,GAAQ,IAAI;IACtB,MAAO;IAEP,KAAKA,KAAA,GAAQA,KAAA;EACd;EAEDhB,SAASC,CAAA,EAAGC,cAAA,GAAiB,IAAIC,OAAA,CAAO,GAAI;IAC1C,MAAMC,KAAA,GAAQF,cAAA;IAEd,MAAMmC,EAAA,GAAKpC,CAAA,GAAII,IAAA,CAAKC,EAAA,GAAK;IAEzB,MAAMC,CAAA,GAAIF,IAAA,CAAKG,GAAA,CAAI,IAAI6B,EAAE,KAAK,IAAI,OAAOhC,IAAA,CAAKG,GAAA,CAAI,IAAI6B,EAAE,IAAI,MAAMhC,IAAA,CAAKG,GAAA,CAAI,KAAK6B,EAAE;IAClF,MAAM3B,CAAA,GAAIL,IAAA,CAAKI,GAAA,CAAI,IAAI4B,EAAE,KAAK,IAAI,OAAOhC,IAAA,CAAKG,GAAA,CAAI,IAAI6B,EAAE,IAAI,MAAMhC,IAAA,CAAKG,GAAA,CAAI,KAAK6B,EAAE;IAClF,MAAM1B,CAAA,GAAI,OAAON,IAAA,CAAKI,GAAA,CAAI,KAAK4B,EAAE;IAEjC,OAAOjC,KAAA,CAAMQ,GAAA,CAAIL,CAAA,EAAGG,CAAA,EAAGC,CAAC,EAAEE,cAAA,CAAe,KAAKG,KAAK;EACpD;AACH", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}