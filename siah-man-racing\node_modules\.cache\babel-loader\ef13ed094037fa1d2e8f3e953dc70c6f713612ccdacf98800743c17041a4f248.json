{"ast": null, "code": "import _extends from '@babel/runtime/helpers/esm/extends';\nimport * as THREE from 'three';\nimport * as React from 'react';\nimport { extend, useThree, useFrame } from '@react-three/fiber';\nimport { shaderMaterial } from './shaderMaterial.js';\nimport { DiscardMaterial } from '../materials/DiscardMaterial.js';\nimport { version } from '../helpers/constants.js';\nfunction isLight(object) {\n  return object.isLight;\n}\nfunction isGeometry(object) {\n  return !!object.geometry;\n}\nconst accumulativeContext = /* @__PURE__ */React.createContext(null);\nconst SoftShadowMaterial = /* @__PURE__ */shaderMaterial({\n  color: /* @__PURE__ */new THREE.Color(),\n  blend: 2.0,\n  alphaTest: 0.75,\n  opacity: 0,\n  map: null\n}, `varying vec2 vUv;\n   void main() {\n     gl_Position = projectionMatrix * viewMatrix * modelMatrix * vec4(position, 1.);\n     vUv = uv;\n   }`, `varying vec2 vUv;\n   uniform sampler2D map;\n   uniform vec3 color;\n   uniform float opacity;\n   uniform float alphaTest;\n   uniform float blend;\n   void main() {\n     vec4 sampledDiffuseColor = texture2D(map, vUv);\n     gl_FragColor = vec4(color * sampledDiffuseColor.r * blend, max(0.0, (1.0 - (sampledDiffuseColor.r + sampledDiffuseColor.g + sampledDiffuseColor.b) / alphaTest)) * opacity);\n     #include <tonemapping_fragment>\n     #include <${version >= 154 ? 'colorspace_fragment' : 'encodings_fragment'}>\n   }`);\nconst AccumulativeShadows = /* @__PURE__ */React.forwardRef(({\n  children,\n  temporal,\n  frames = 40,\n  limit = Infinity,\n  blend = 20,\n  scale = 10,\n  opacity = 1,\n  alphaTest = 0.75,\n  color = 'black',\n  colorBlend = 2,\n  resolution = 1024,\n  toneMapped = true,\n  ...props\n}, forwardRef) => {\n  extend({\n    SoftShadowMaterial\n  });\n  const gl = useThree(state => state.gl);\n  const scene = useThree(state => state.scene);\n  const camera = useThree(state => state.camera);\n  const invalidate = useThree(state => state.invalidate);\n  const gPlane = React.useRef(null);\n  const gLights = React.useRef(null);\n  const [plm] = React.useState(() => new ProgressiveLightMap(gl, scene, resolution));\n  React.useLayoutEffect(() => {\n    plm.configure(gPlane.current);\n  }, []);\n  const api = React.useMemo(() => ({\n    lights: new Map(),\n    temporal: !!temporal,\n    frames: Math.max(2, frames),\n    blend: Math.max(2, frames === Infinity ? blend : frames),\n    count: 0,\n    getMesh: () => gPlane.current,\n    reset: () => {\n      // Clear buffers, reset opacities, set frame count to 0\n      plm.clear();\n      const material = gPlane.current.material;\n      material.opacity = 0;\n      material.alphaTest = 0;\n      api.count = 0;\n    },\n    update: (frames = 1) => {\n      // Adapt the opacity-blend ratio to the number of frames\n      const material = gPlane.current.material;\n      if (!api.temporal) {\n        material.opacity = opacity;\n        material.alphaTest = alphaTest;\n      } else {\n        material.opacity = Math.min(opacity, material.opacity + opacity / api.blend);\n        material.alphaTest = Math.min(alphaTest, material.alphaTest + alphaTest / api.blend);\n      }\n\n      // Switch accumulative lights on\n      gLights.current.visible = true;\n      // Collect scene lights and meshes\n      plm.prepare();\n\n      // Update the lightmap and the accumulative lights\n      for (let i = 0; i < frames; i++) {\n        api.lights.forEach(light => light.update());\n        plm.update(camera, api.blend);\n      }\n      // Switch lights off\n      gLights.current.visible = false;\n      // Restore lights and meshes\n      plm.finish();\n    }\n  }), [plm, camera, scene, temporal, frames, blend, opacity, alphaTest]);\n  React.useLayoutEffect(() => {\n    // Reset internals, buffers, ...\n    api.reset();\n    // Update lightmap\n    if (!api.temporal && api.frames !== Infinity) api.update(api.blend);\n  });\n\n  // Expose api, allow children to set itself as the main light source\n  React.useImperativeHandle(forwardRef, () => api, [api]);\n  useFrame(() => {\n    if ((api.temporal || api.frames === Infinity) && api.count < api.frames && api.count < limit) {\n      invalidate();\n      api.update();\n      api.count++;\n    }\n  });\n  return /*#__PURE__*/React.createElement(\"group\", props, /*#__PURE__*/React.createElement(\"group\", {\n    traverse: () => null,\n    ref: gLights\n  }, /*#__PURE__*/React.createElement(accumulativeContext.Provider, {\n    value: api\n  }, children)), /*#__PURE__*/React.createElement(\"mesh\", {\n    receiveShadow: true,\n    ref: gPlane,\n    scale: scale,\n    rotation: [-Math.PI / 2, 0, 0]\n  }, /*#__PURE__*/React.createElement(\"planeGeometry\", null), /*#__PURE__*/React.createElement(\"softShadowMaterial\", {\n    transparent: true,\n    depthWrite: false,\n    toneMapped: toneMapped,\n    color: color,\n    blend: colorBlend,\n    map: plm.progressiveLightMap2.texture\n  })));\n});\nconst RandomizedLight = /* @__PURE__ */React.forwardRef(({\n  castShadow = true,\n  bias = 0.001,\n  mapSize = 512,\n  size = 5,\n  near = 0.5,\n  far = 500,\n  frames = 1,\n  position = [0, 0, 0],\n  radius = 1,\n  amount = 8,\n  intensity = version >= 155 ? Math.PI : 1,\n  ambient = 0.5,\n  ...props\n}, forwardRef) => {\n  const gLights = React.useRef(null);\n  const length = new THREE.Vector3(...position).length();\n  const parent = React.useContext(accumulativeContext);\n  const update = React.useCallback(() => {\n    let light;\n    if (gLights.current) {\n      for (let l = 0; l < gLights.current.children.length; l++) {\n        light = gLights.current.children[l];\n        if (Math.random() > ambient) {\n          light.position.set(position[0] + THREE.MathUtils.randFloatSpread(radius), position[1] + THREE.MathUtils.randFloatSpread(radius), position[2] + THREE.MathUtils.randFloatSpread(radius));\n        } else {\n          let lambda = Math.acos(2 * Math.random() - 1) - Math.PI / 2.0;\n          let phi = 2 * Math.PI * Math.random();\n          light.position.set(Math.cos(lambda) * Math.cos(phi) * length, Math.abs(Math.cos(lambda) * Math.sin(phi) * length), Math.sin(lambda) * length);\n        }\n      }\n    }\n  }, [radius, ambient, length, ...position]);\n  const api = React.useMemo(() => ({\n    update\n  }), [update]);\n  React.useImperativeHandle(forwardRef, () => api, [api]);\n  React.useLayoutEffect(() => {\n    var _parent$lights;\n    const group = gLights.current;\n    if (parent) (_parent$lights = parent.lights) == null || _parent$lights.set(group.uuid, api);\n    return () => {\n      var _parent$lights2;\n      return void (parent == null || (_parent$lights2 = parent.lights) == null ? void 0 : _parent$lights2.delete(group.uuid));\n    };\n  }, [parent, api]);\n  return /*#__PURE__*/React.createElement(\"group\", _extends({\n    ref: gLights\n  }, props), Array.from({\n    length: amount\n  }, (_, index) => /*#__PURE__*/React.createElement(\"directionalLight\", {\n    key: index,\n    castShadow: castShadow,\n    \"shadow-bias\": bias,\n    \"shadow-mapSize\": [mapSize, mapSize],\n    intensity: intensity / amount\n  }, /*#__PURE__*/React.createElement(\"orthographicCamera\", {\n    attach: \"shadow-camera\",\n    args: [-size, size, size, -size, near, far]\n  }))));\n});\n\n// Based on \"Progressive Light Map Accumulator\", by [zalo](https://github.com/zalo/)\nclass ProgressiveLightMap {\n  constructor(renderer, scene, res = 1024) {\n    this.renderer = renderer;\n    this.res = res;\n    this.scene = scene;\n    this.buffer1Active = false;\n    this.lights = [];\n    this.meshes = [];\n    this.object = null;\n    this.clearColor = new THREE.Color();\n    this.clearAlpha = 0;\n\n    // Create the Progressive LightMap Texture\n    const textureParams = {\n      type: THREE.HalfFloatType,\n      magFilter: THREE.NearestFilter,\n      minFilter: THREE.NearestFilter\n    };\n    this.progressiveLightMap1 = new THREE.WebGLRenderTarget(this.res, this.res, textureParams);\n    this.progressiveLightMap2 = new THREE.WebGLRenderTarget(this.res, this.res, textureParams);\n\n    // Inject some spicy new logic into a standard phong material\n    this.discardMat = new DiscardMaterial();\n    this.targetMat = new THREE.MeshLambertMaterial({\n      fog: false\n    });\n    this.previousShadowMap = {\n      value: this.progressiveLightMap1.texture\n    };\n    this.averagingWindow = {\n      value: 100\n    };\n    this.targetMat.onBeforeCompile = shader => {\n      // Vertex Shader: Set Vertex Positions to the Unwrapped UV Positions\n      shader.vertexShader = 'varying vec2 vUv;\\n' + shader.vertexShader.slice(0, -1) + 'vUv = uv; gl_Position = vec4((uv - 0.5) * 2.0, 1.0, 1.0); }';\n\n      // Fragment Shader: Set Pixels to average in the Previous frame's Shadows\n      const bodyStart = shader.fragmentShader.indexOf('void main() {');\n      shader.fragmentShader = 'varying vec2 vUv;\\n' + shader.fragmentShader.slice(0, bodyStart) + 'uniform sampler2D previousShadowMap;\\n\tuniform float averagingWindow;\\n' + shader.fragmentShader.slice(bodyStart - 1, -1) + `\\nvec3 texelOld = texture2D(previousShadowMap, vUv).rgb;\n        gl_FragColor.rgb = mix(texelOld, gl_FragColor.rgb, 1.0/ averagingWindow);\n      }`;\n\n      // Set the Previous Frame's Texture Buffer and Averaging Window\n      shader.uniforms.previousShadowMap = this.previousShadowMap;\n      shader.uniforms.averagingWindow = this.averagingWindow;\n    };\n  }\n  clear() {\n    this.renderer.getClearColor(this.clearColor);\n    this.clearAlpha = this.renderer.getClearAlpha();\n    this.renderer.setClearColor('black', 1);\n    this.renderer.setRenderTarget(this.progressiveLightMap1);\n    this.renderer.clear();\n    this.renderer.setRenderTarget(this.progressiveLightMap2);\n    this.renderer.clear();\n    this.renderer.setRenderTarget(null);\n    this.renderer.setClearColor(this.clearColor, this.clearAlpha);\n    this.lights = [];\n    this.meshes = [];\n    this.scene.traverse(object => {\n      if (isGeometry(object)) {\n        this.meshes.push({\n          object,\n          material: object.material\n        });\n      } else if (isLight(object)) {\n        this.lights.push({\n          object,\n          intensity: object.intensity\n        });\n      }\n    });\n  }\n  prepare() {\n    this.lights.forEach(light => light.object.intensity = 0);\n    this.meshes.forEach(mesh => mesh.object.material = this.discardMat);\n  }\n  finish() {\n    this.lights.forEach(light => light.object.intensity = light.intensity);\n    this.meshes.forEach(mesh => mesh.object.material = mesh.material);\n  }\n  configure(object) {\n    this.object = object;\n  }\n  update(camera, blendWindow = 100) {\n    if (!this.object) return;\n    // Set each object's material to the UV Unwrapped Surface Mapping Version\n    this.averagingWindow.value = blendWindow;\n    this.object.material = this.targetMat;\n    // Ping-pong two surface buffers for reading/writing\n    const activeMap = this.buffer1Active ? this.progressiveLightMap1 : this.progressiveLightMap2;\n    const inactiveMap = this.buffer1Active ? this.progressiveLightMap2 : this.progressiveLightMap1;\n    // Render the object's surface maps\n    const oldBg = this.scene.background;\n    this.scene.background = null;\n    this.renderer.setRenderTarget(activeMap);\n    this.previousShadowMap.value = inactiveMap.texture;\n    this.buffer1Active = !this.buffer1Active;\n    this.renderer.render(this.scene, camera);\n    this.renderer.setRenderTarget(null);\n    this.scene.background = oldBg;\n  }\n}\nexport { AccumulativeShadows, RandomizedLight, accumulativeContext };", "map": {"version": 3, "names": ["_extends", "THREE", "React", "extend", "useThree", "useFrame", "shaderMaterial", "DiscardMaterial", "version", "isLight", "object", "isGeometry", "geometry", "accumulativeContext", "createContext", "SoftShadowMaterial", "color", "Color", "blend", "alphaTest", "opacity", "map", "AccumulativeShadows", "forwardRef", "children", "temporal", "frames", "limit", "Infinity", "scale", "colorBlend", "resolution", "toneMapped", "props", "gl", "state", "scene", "camera", "invalidate", "gPlane", "useRef", "gLights", "plm", "useState", "ProgressiveLightMap", "useLayoutEffect", "configure", "current", "api", "useMemo", "lights", "Map", "Math", "max", "count", "<PERSON><PERSON><PERSON>", "reset", "clear", "material", "update", "min", "visible", "prepare", "i", "for<PERSON>ach", "light", "finish", "useImperativeHandle", "createElement", "traverse", "ref", "Provider", "value", "receiveShadow", "rotation", "PI", "transparent", "depthWrite", "progressiveLightMap2", "texture", "RandomizedLight", "<PERSON><PERSON><PERSON><PERSON>", "bias", "mapSize", "size", "near", "far", "position", "radius", "amount", "intensity", "ambient", "length", "Vector3", "parent", "useContext", "useCallback", "l", "random", "set", "MathUtils", "randFloatSpread", "lambda", "acos", "phi", "cos", "abs", "sin", "_parent$lights", "group", "uuid", "_parent$lights2", "delete", "Array", "from", "_", "index", "key", "attach", "args", "constructor", "renderer", "res", "buffer1Active", "meshes", "clearColor", "clearAlpha", "textureParams", "type", "HalfFloatType", "magFilter", "NearestFilter", "minFilter", "progressiveLightMap1", "WebGLRenderTarget", "discardMat", "targetMat", "MeshLambertMaterial", "fog", "previousShadowMap", "averagingWindow", "onBeforeCompile", "shader", "vertexShader", "slice", "bodyStart", "fragmentShader", "indexOf", "uniforms", "getClearColor", "getClearAlpha", "setClearColor", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "push", "mesh", "blendWindow", "activeMap", "inactiveMap", "oldBg", "background", "render"], "sources": ["C:/Users/<USER>/Documents/Apps_Creations/SiahMan_Racing4Speed_WebGame/siah-man-racing/node_modules/@react-three/drei/core/AccumulativeShadows.js"], "sourcesContent": ["import _extends from '@babel/runtime/helpers/esm/extends';\nimport * as THREE from 'three';\nimport * as React from 'react';\nimport { extend, useThree, useFrame } from '@react-three/fiber';\nimport { shaderMaterial } from './shaderMaterial.js';\nimport { DiscardMaterial } from '../materials/DiscardMaterial.js';\nimport { version } from '../helpers/constants.js';\n\nfunction isLight(object) {\n  return object.isLight;\n}\nfunction isGeometry(object) {\n  return !!object.geometry;\n}\nconst accumulativeContext = /* @__PURE__ */React.createContext(null);\nconst SoftShadowMaterial = /* @__PURE__ */shaderMaterial({\n  color: /* @__PURE__ */new THREE.Color(),\n  blend: 2.0,\n  alphaTest: 0.75,\n  opacity: 0,\n  map: null\n}, `varying vec2 vUv;\n   void main() {\n     gl_Position = projectionMatrix * viewMatrix * modelMatrix * vec4(position, 1.);\n     vUv = uv;\n   }`, `varying vec2 vUv;\n   uniform sampler2D map;\n   uniform vec3 color;\n   uniform float opacity;\n   uniform float alphaTest;\n   uniform float blend;\n   void main() {\n     vec4 sampledDiffuseColor = texture2D(map, vUv);\n     gl_FragColor = vec4(color * sampledDiffuseColor.r * blend, max(0.0, (1.0 - (sampledDiffuseColor.r + sampledDiffuseColor.g + sampledDiffuseColor.b) / alphaTest)) * opacity);\n     #include <tonemapping_fragment>\n     #include <${version >= 154 ? 'colorspace_fragment' : 'encodings_fragment'}>\n   }`);\nconst AccumulativeShadows = /* @__PURE__ */React.forwardRef(({\n  children,\n  temporal,\n  frames = 40,\n  limit = Infinity,\n  blend = 20,\n  scale = 10,\n  opacity = 1,\n  alphaTest = 0.75,\n  color = 'black',\n  colorBlend = 2,\n  resolution = 1024,\n  toneMapped = true,\n  ...props\n}, forwardRef) => {\n  extend({\n    SoftShadowMaterial\n  });\n  const gl = useThree(state => state.gl);\n  const scene = useThree(state => state.scene);\n  const camera = useThree(state => state.camera);\n  const invalidate = useThree(state => state.invalidate);\n  const gPlane = React.useRef(null);\n  const gLights = React.useRef(null);\n  const [plm] = React.useState(() => new ProgressiveLightMap(gl, scene, resolution));\n  React.useLayoutEffect(() => {\n    plm.configure(gPlane.current);\n  }, []);\n  const api = React.useMemo(() => ({\n    lights: new Map(),\n    temporal: !!temporal,\n    frames: Math.max(2, frames),\n    blend: Math.max(2, frames === Infinity ? blend : frames),\n    count: 0,\n    getMesh: () => gPlane.current,\n    reset: () => {\n      // Clear buffers, reset opacities, set frame count to 0\n      plm.clear();\n      const material = gPlane.current.material;\n      material.opacity = 0;\n      material.alphaTest = 0;\n      api.count = 0;\n    },\n    update: (frames = 1) => {\n      // Adapt the opacity-blend ratio to the number of frames\n      const material = gPlane.current.material;\n      if (!api.temporal) {\n        material.opacity = opacity;\n        material.alphaTest = alphaTest;\n      } else {\n        material.opacity = Math.min(opacity, material.opacity + opacity / api.blend);\n        material.alphaTest = Math.min(alphaTest, material.alphaTest + alphaTest / api.blend);\n      }\n\n      // Switch accumulative lights on\n      gLights.current.visible = true;\n      // Collect scene lights and meshes\n      plm.prepare();\n\n      // Update the lightmap and the accumulative lights\n      for (let i = 0; i < frames; i++) {\n        api.lights.forEach(light => light.update());\n        plm.update(camera, api.blend);\n      }\n      // Switch lights off\n      gLights.current.visible = false;\n      // Restore lights and meshes\n      plm.finish();\n    }\n  }), [plm, camera, scene, temporal, frames, blend, opacity, alphaTest]);\n  React.useLayoutEffect(() => {\n    // Reset internals, buffers, ...\n    api.reset();\n    // Update lightmap\n    if (!api.temporal && api.frames !== Infinity) api.update(api.blend);\n  });\n\n  // Expose api, allow children to set itself as the main light source\n  React.useImperativeHandle(forwardRef, () => api, [api]);\n  useFrame(() => {\n    if ((api.temporal || api.frames === Infinity) && api.count < api.frames && api.count < limit) {\n      invalidate();\n      api.update();\n      api.count++;\n    }\n  });\n  return /*#__PURE__*/React.createElement(\"group\", props, /*#__PURE__*/React.createElement(\"group\", {\n    traverse: () => null,\n    ref: gLights\n  }, /*#__PURE__*/React.createElement(accumulativeContext.Provider, {\n    value: api\n  }, children)), /*#__PURE__*/React.createElement(\"mesh\", {\n    receiveShadow: true,\n    ref: gPlane,\n    scale: scale,\n    rotation: [-Math.PI / 2, 0, 0]\n  }, /*#__PURE__*/React.createElement(\"planeGeometry\", null), /*#__PURE__*/React.createElement(\"softShadowMaterial\", {\n    transparent: true,\n    depthWrite: false,\n    toneMapped: toneMapped,\n    color: color,\n    blend: colorBlend,\n    map: plm.progressiveLightMap2.texture\n  })));\n});\nconst RandomizedLight = /* @__PURE__ */React.forwardRef(({\n  castShadow = true,\n  bias = 0.001,\n  mapSize = 512,\n  size = 5,\n  near = 0.5,\n  far = 500,\n  frames = 1,\n  position = [0, 0, 0],\n  radius = 1,\n  amount = 8,\n  intensity = version >= 155 ? Math.PI : 1,\n  ambient = 0.5,\n  ...props\n}, forwardRef) => {\n  const gLights = React.useRef(null);\n  const length = new THREE.Vector3(...position).length();\n  const parent = React.useContext(accumulativeContext);\n  const update = React.useCallback(() => {\n    let light;\n    if (gLights.current) {\n      for (let l = 0; l < gLights.current.children.length; l++) {\n        light = gLights.current.children[l];\n        if (Math.random() > ambient) {\n          light.position.set(position[0] + THREE.MathUtils.randFloatSpread(radius), position[1] + THREE.MathUtils.randFloatSpread(radius), position[2] + THREE.MathUtils.randFloatSpread(radius));\n        } else {\n          let lambda = Math.acos(2 * Math.random() - 1) - Math.PI / 2.0;\n          let phi = 2 * Math.PI * Math.random();\n          light.position.set(Math.cos(lambda) * Math.cos(phi) * length, Math.abs(Math.cos(lambda) * Math.sin(phi) * length), Math.sin(lambda) * length);\n        }\n      }\n    }\n  }, [radius, ambient, length, ...position]);\n  const api = React.useMemo(() => ({\n    update\n  }), [update]);\n  React.useImperativeHandle(forwardRef, () => api, [api]);\n  React.useLayoutEffect(() => {\n    var _parent$lights;\n    const group = gLights.current;\n    if (parent) (_parent$lights = parent.lights) == null || _parent$lights.set(group.uuid, api);\n    return () => {\n      var _parent$lights2;\n      return void (parent == null || (_parent$lights2 = parent.lights) == null ? void 0 : _parent$lights2.delete(group.uuid));\n    };\n  }, [parent, api]);\n  return /*#__PURE__*/React.createElement(\"group\", _extends({\n    ref: gLights\n  }, props), Array.from({\n    length: amount\n  }, (_, index) => /*#__PURE__*/React.createElement(\"directionalLight\", {\n    key: index,\n    castShadow: castShadow,\n    \"shadow-bias\": bias,\n    \"shadow-mapSize\": [mapSize, mapSize],\n    intensity: intensity / amount\n  }, /*#__PURE__*/React.createElement(\"orthographicCamera\", {\n    attach: \"shadow-camera\",\n    args: [-size, size, size, -size, near, far]\n  }))));\n});\n\n// Based on \"Progressive Light Map Accumulator\", by [zalo](https://github.com/zalo/)\nclass ProgressiveLightMap {\n  constructor(renderer, scene, res = 1024) {\n    this.renderer = renderer;\n    this.res = res;\n    this.scene = scene;\n    this.buffer1Active = false;\n    this.lights = [];\n    this.meshes = [];\n    this.object = null;\n    this.clearColor = new THREE.Color();\n    this.clearAlpha = 0;\n\n    // Create the Progressive LightMap Texture\n    const textureParams = {\n      type: THREE.HalfFloatType,\n      magFilter: THREE.NearestFilter,\n      minFilter: THREE.NearestFilter\n    };\n    this.progressiveLightMap1 = new THREE.WebGLRenderTarget(this.res, this.res, textureParams);\n    this.progressiveLightMap2 = new THREE.WebGLRenderTarget(this.res, this.res, textureParams);\n\n    // Inject some spicy new logic into a standard phong material\n    this.discardMat = new DiscardMaterial();\n    this.targetMat = new THREE.MeshLambertMaterial({\n      fog: false\n    });\n    this.previousShadowMap = {\n      value: this.progressiveLightMap1.texture\n    };\n    this.averagingWindow = {\n      value: 100\n    };\n    this.targetMat.onBeforeCompile = shader => {\n      // Vertex Shader: Set Vertex Positions to the Unwrapped UV Positions\n      shader.vertexShader = 'varying vec2 vUv;\\n' + shader.vertexShader.slice(0, -1) + 'vUv = uv; gl_Position = vec4((uv - 0.5) * 2.0, 1.0, 1.0); }';\n\n      // Fragment Shader: Set Pixels to average in the Previous frame's Shadows\n      const bodyStart = shader.fragmentShader.indexOf('void main() {');\n      shader.fragmentShader = 'varying vec2 vUv;\\n' + shader.fragmentShader.slice(0, bodyStart) + 'uniform sampler2D previousShadowMap;\\n\tuniform float averagingWindow;\\n' + shader.fragmentShader.slice(bodyStart - 1, -1) + `\\nvec3 texelOld = texture2D(previousShadowMap, vUv).rgb;\n        gl_FragColor.rgb = mix(texelOld, gl_FragColor.rgb, 1.0/ averagingWindow);\n      }`;\n\n      // Set the Previous Frame's Texture Buffer and Averaging Window\n      shader.uniforms.previousShadowMap = this.previousShadowMap;\n      shader.uniforms.averagingWindow = this.averagingWindow;\n    };\n  }\n  clear() {\n    this.renderer.getClearColor(this.clearColor);\n    this.clearAlpha = this.renderer.getClearAlpha();\n    this.renderer.setClearColor('black', 1);\n    this.renderer.setRenderTarget(this.progressiveLightMap1);\n    this.renderer.clear();\n    this.renderer.setRenderTarget(this.progressiveLightMap2);\n    this.renderer.clear();\n    this.renderer.setRenderTarget(null);\n    this.renderer.setClearColor(this.clearColor, this.clearAlpha);\n    this.lights = [];\n    this.meshes = [];\n    this.scene.traverse(object => {\n      if (isGeometry(object)) {\n        this.meshes.push({\n          object,\n          material: object.material\n        });\n      } else if (isLight(object)) {\n        this.lights.push({\n          object,\n          intensity: object.intensity\n        });\n      }\n    });\n  }\n  prepare() {\n    this.lights.forEach(light => light.object.intensity = 0);\n    this.meshes.forEach(mesh => mesh.object.material = this.discardMat);\n  }\n  finish() {\n    this.lights.forEach(light => light.object.intensity = light.intensity);\n    this.meshes.forEach(mesh => mesh.object.material = mesh.material);\n  }\n  configure(object) {\n    this.object = object;\n  }\n  update(camera, blendWindow = 100) {\n    if (!this.object) return;\n    // Set each object's material to the UV Unwrapped Surface Mapping Version\n    this.averagingWindow.value = blendWindow;\n    this.object.material = this.targetMat;\n    // Ping-pong two surface buffers for reading/writing\n    const activeMap = this.buffer1Active ? this.progressiveLightMap1 : this.progressiveLightMap2;\n    const inactiveMap = this.buffer1Active ? this.progressiveLightMap2 : this.progressiveLightMap1;\n    // Render the object's surface maps\n    const oldBg = this.scene.background;\n    this.scene.background = null;\n    this.renderer.setRenderTarget(activeMap);\n    this.previousShadowMap.value = inactiveMap.texture;\n    this.buffer1Active = !this.buffer1Active;\n    this.renderer.render(this.scene, camera);\n    this.renderer.setRenderTarget(null);\n    this.scene.background = oldBg;\n  }\n}\n\nexport { AccumulativeShadows, RandomizedLight, accumulativeContext };\n"], "mappings": "AAAA,OAAOA,QAAQ,MAAM,oCAAoC;AACzD,OAAO,KAAKC,KAAK,MAAM,OAAO;AAC9B,OAAO,KAAKC,KAAK,MAAM,OAAO;AAC9B,SAASC,MAAM,EAAEC,QAAQ,EAAEC,QAAQ,QAAQ,oBAAoB;AAC/D,SAASC,cAAc,QAAQ,qBAAqB;AACpD,SAASC,eAAe,QAAQ,iCAAiC;AACjE,SAASC,OAAO,QAAQ,yBAAyB;AAEjD,SAASC,OAAOA,CAACC,MAAM,EAAE;EACvB,OAAOA,MAAM,CAACD,OAAO;AACvB;AACA,SAASE,UAAUA,CAACD,MAAM,EAAE;EAC1B,OAAO,CAAC,CAACA,MAAM,CAACE,QAAQ;AAC1B;AACA,MAAMC,mBAAmB,GAAG,eAAeX,KAAK,CAACY,aAAa,CAAC,IAAI,CAAC;AACpE,MAAMC,kBAAkB,GAAG,eAAeT,cAAc,CAAC;EACvDU,KAAK,EAAE,eAAe,IAAIf,KAAK,CAACgB,KAAK,CAAC,CAAC;EACvCC,KAAK,EAAE,GAAG;EACVC,SAAS,EAAE,IAAI;EACfC,OAAO,EAAE,CAAC;EACVC,GAAG,EAAE;AACP,CAAC,EAAE;AACH;AACA;AACA;AACA,KAAK,EAAE;AACP;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,iBAAiBb,OAAO,IAAI,GAAG,GAAG,qBAAqB,GAAG,oBAAoB;AAC9E,KAAK,CAAC;AACN,MAAMc,mBAAmB,GAAG,eAAepB,KAAK,CAACqB,UAAU,CAAC,CAAC;EAC3DC,QAAQ;EACRC,QAAQ;EACRC,MAAM,GAAG,EAAE;EACXC,KAAK,GAAGC,QAAQ;EAChBV,KAAK,GAAG,EAAE;EACVW,KAAK,GAAG,EAAE;EACVT,OAAO,GAAG,CAAC;EACXD,SAAS,GAAG,IAAI;EAChBH,KAAK,GAAG,OAAO;EACfc,UAAU,GAAG,CAAC;EACdC,UAAU,GAAG,IAAI;EACjBC,UAAU,GAAG,IAAI;EACjB,GAAGC;AACL,CAAC,EAAEV,UAAU,KAAK;EAChBpB,MAAM,CAAC;IACLY;EACF,CAAC,CAAC;EACF,MAAMmB,EAAE,GAAG9B,QAAQ,CAAC+B,KAAK,IAAIA,KAAK,CAACD,EAAE,CAAC;EACtC,MAAME,KAAK,GAAGhC,QAAQ,CAAC+B,KAAK,IAAIA,KAAK,CAACC,KAAK,CAAC;EAC5C,MAAMC,MAAM,GAAGjC,QAAQ,CAAC+B,KAAK,IAAIA,KAAK,CAACE,MAAM,CAAC;EAC9C,MAAMC,UAAU,GAAGlC,QAAQ,CAAC+B,KAAK,IAAIA,KAAK,CAACG,UAAU,CAAC;EACtD,MAAMC,MAAM,GAAGrC,KAAK,CAACsC,MAAM,CAAC,IAAI,CAAC;EACjC,MAAMC,OAAO,GAAGvC,KAAK,CAACsC,MAAM,CAAC,IAAI,CAAC;EAClC,MAAM,CAACE,GAAG,CAAC,GAAGxC,KAAK,CAACyC,QAAQ,CAAC,MAAM,IAAIC,mBAAmB,CAACV,EAAE,EAAEE,KAAK,EAAEL,UAAU,CAAC,CAAC;EAClF7B,KAAK,CAAC2C,eAAe,CAAC,MAAM;IAC1BH,GAAG,CAACI,SAAS,CAACP,MAAM,CAACQ,OAAO,CAAC;EAC/B,CAAC,EAAE,EAAE,CAAC;EACN,MAAMC,GAAG,GAAG9C,KAAK,CAAC+C,OAAO,CAAC,OAAO;IAC/BC,MAAM,EAAE,IAAIC,GAAG,CAAC,CAAC;IACjB1B,QAAQ,EAAE,CAAC,CAACA,QAAQ;IACpBC,MAAM,EAAE0B,IAAI,CAACC,GAAG,CAAC,CAAC,EAAE3B,MAAM,CAAC;IAC3BR,KAAK,EAAEkC,IAAI,CAACC,GAAG,CAAC,CAAC,EAAE3B,MAAM,KAAKE,QAAQ,GAAGV,KAAK,GAAGQ,MAAM,CAAC;IACxD4B,KAAK,EAAE,CAAC;IACRC,OAAO,EAAEA,CAAA,KAAMhB,MAAM,CAACQ,OAAO;IAC7BS,KAAK,EAAEA,CAAA,KAAM;MACX;MACAd,GAAG,CAACe,KAAK,CAAC,CAAC;MACX,MAAMC,QAAQ,GAAGnB,MAAM,CAACQ,OAAO,CAACW,QAAQ;MACxCA,QAAQ,CAACtC,OAAO,GAAG,CAAC;MACpBsC,QAAQ,CAACvC,SAAS,GAAG,CAAC;MACtB6B,GAAG,CAACM,KAAK,GAAG,CAAC;IACf,CAAC;IACDK,MAAM,EAAEA,CAACjC,MAAM,GAAG,CAAC,KAAK;MACtB;MACA,MAAMgC,QAAQ,GAAGnB,MAAM,CAACQ,OAAO,CAACW,QAAQ;MACxC,IAAI,CAACV,GAAG,CAACvB,QAAQ,EAAE;QACjBiC,QAAQ,CAACtC,OAAO,GAAGA,OAAO;QAC1BsC,QAAQ,CAACvC,SAAS,GAAGA,SAAS;MAChC,CAAC,MAAM;QACLuC,QAAQ,CAACtC,OAAO,GAAGgC,IAAI,CAACQ,GAAG,CAACxC,OAAO,EAAEsC,QAAQ,CAACtC,OAAO,GAAGA,OAAO,GAAG4B,GAAG,CAAC9B,KAAK,CAAC;QAC5EwC,QAAQ,CAACvC,SAAS,GAAGiC,IAAI,CAACQ,GAAG,CAACzC,SAAS,EAAEuC,QAAQ,CAACvC,SAAS,GAAGA,SAAS,GAAG6B,GAAG,CAAC9B,KAAK,CAAC;MACtF;;MAEA;MACAuB,OAAO,CAACM,OAAO,CAACc,OAAO,GAAG,IAAI;MAC9B;MACAnB,GAAG,CAACoB,OAAO,CAAC,CAAC;;MAEb;MACA,KAAK,IAAIC,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGrC,MAAM,EAAEqC,CAAC,EAAE,EAAE;QAC/Bf,GAAG,CAACE,MAAM,CAACc,OAAO,CAACC,KAAK,IAAIA,KAAK,CAACN,MAAM,CAAC,CAAC,CAAC;QAC3CjB,GAAG,CAACiB,MAAM,CAACtB,MAAM,EAAEW,GAAG,CAAC9B,KAAK,CAAC;MAC/B;MACA;MACAuB,OAAO,CAACM,OAAO,CAACc,OAAO,GAAG,KAAK;MAC/B;MACAnB,GAAG,CAACwB,MAAM,CAAC,CAAC;IACd;EACF,CAAC,CAAC,EAAE,CAACxB,GAAG,EAAEL,MAAM,EAAED,KAAK,EAAEX,QAAQ,EAAEC,MAAM,EAAER,KAAK,EAAEE,OAAO,EAAED,SAAS,CAAC,CAAC;EACtEjB,KAAK,CAAC2C,eAAe,CAAC,MAAM;IAC1B;IACAG,GAAG,CAACQ,KAAK,CAAC,CAAC;IACX;IACA,IAAI,CAACR,GAAG,CAACvB,QAAQ,IAAIuB,GAAG,CAACtB,MAAM,KAAKE,QAAQ,EAAEoB,GAAG,CAACW,MAAM,CAACX,GAAG,CAAC9B,KAAK,CAAC;EACrE,CAAC,CAAC;;EAEF;EACAhB,KAAK,CAACiE,mBAAmB,CAAC5C,UAAU,EAAE,MAAMyB,GAAG,EAAE,CAACA,GAAG,CAAC,CAAC;EACvD3C,QAAQ,CAAC,MAAM;IACb,IAAI,CAAC2C,GAAG,CAACvB,QAAQ,IAAIuB,GAAG,CAACtB,MAAM,KAAKE,QAAQ,KAAKoB,GAAG,CAACM,KAAK,GAAGN,GAAG,CAACtB,MAAM,IAAIsB,GAAG,CAACM,KAAK,GAAG3B,KAAK,EAAE;MAC5FW,UAAU,CAAC,CAAC;MACZU,GAAG,CAACW,MAAM,CAAC,CAAC;MACZX,GAAG,CAACM,KAAK,EAAE;IACb;EACF,CAAC,CAAC;EACF,OAAO,aAAapD,KAAK,CAACkE,aAAa,CAAC,OAAO,EAAEnC,KAAK,EAAE,aAAa/B,KAAK,CAACkE,aAAa,CAAC,OAAO,EAAE;IAChGC,QAAQ,EAAEA,CAAA,KAAM,IAAI;IACpBC,GAAG,EAAE7B;EACP,CAAC,EAAE,aAAavC,KAAK,CAACkE,aAAa,CAACvD,mBAAmB,CAAC0D,QAAQ,EAAE;IAChEC,KAAK,EAAExB;EACT,CAAC,EAAExB,QAAQ,CAAC,CAAC,EAAE,aAAatB,KAAK,CAACkE,aAAa,CAAC,MAAM,EAAE;IACtDK,aAAa,EAAE,IAAI;IACnBH,GAAG,EAAE/B,MAAM;IACXV,KAAK,EAAEA,KAAK;IACZ6C,QAAQ,EAAE,CAAC,CAACtB,IAAI,CAACuB,EAAE,GAAG,CAAC,EAAE,CAAC,EAAE,CAAC;EAC/B,CAAC,EAAE,aAAazE,KAAK,CAACkE,aAAa,CAAC,eAAe,EAAE,IAAI,CAAC,EAAE,aAAalE,KAAK,CAACkE,aAAa,CAAC,oBAAoB,EAAE;IACjHQ,WAAW,EAAE,IAAI;IACjBC,UAAU,EAAE,KAAK;IACjB7C,UAAU,EAAEA,UAAU;IACtBhB,KAAK,EAAEA,KAAK;IACZE,KAAK,EAAEY,UAAU;IACjBT,GAAG,EAAEqB,GAAG,CAACoC,oBAAoB,CAACC;EAChC,CAAC,CAAC,CAAC,CAAC;AACN,CAAC,CAAC;AACF,MAAMC,eAAe,GAAG,eAAe9E,KAAK,CAACqB,UAAU,CAAC,CAAC;EACvD0D,UAAU,GAAG,IAAI;EACjBC,IAAI,GAAG,KAAK;EACZC,OAAO,GAAG,GAAG;EACbC,IAAI,GAAG,CAAC;EACRC,IAAI,GAAG,GAAG;EACVC,GAAG,GAAG,GAAG;EACT5D,MAAM,GAAG,CAAC;EACV6D,QAAQ,GAAG,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC;EACpBC,MAAM,GAAG,CAAC;EACVC,MAAM,GAAG,CAAC;EACVC,SAAS,GAAGlF,OAAO,IAAI,GAAG,GAAG4C,IAAI,CAACuB,EAAE,GAAG,CAAC;EACxCgB,OAAO,GAAG,GAAG;EACb,GAAG1D;AACL,CAAC,EAAEV,UAAU,KAAK;EAChB,MAAMkB,OAAO,GAAGvC,KAAK,CAACsC,MAAM,CAAC,IAAI,CAAC;EAClC,MAAMoD,MAAM,GAAG,IAAI3F,KAAK,CAAC4F,OAAO,CAAC,GAAGN,QAAQ,CAAC,CAACK,MAAM,CAAC,CAAC;EACtD,MAAME,MAAM,GAAG5F,KAAK,CAAC6F,UAAU,CAAClF,mBAAmB,CAAC;EACpD,MAAM8C,MAAM,GAAGzD,KAAK,CAAC8F,WAAW,CAAC,MAAM;IACrC,IAAI/B,KAAK;IACT,IAAIxB,OAAO,CAACM,OAAO,EAAE;MACnB,KAAK,IAAIkD,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGxD,OAAO,CAACM,OAAO,CAACvB,QAAQ,CAACoE,MAAM,EAAEK,CAAC,EAAE,EAAE;QACxDhC,KAAK,GAAGxB,OAAO,CAACM,OAAO,CAACvB,QAAQ,CAACyE,CAAC,CAAC;QACnC,IAAI7C,IAAI,CAAC8C,MAAM,CAAC,CAAC,GAAGP,OAAO,EAAE;UAC3B1B,KAAK,CAACsB,QAAQ,CAACY,GAAG,CAACZ,QAAQ,CAAC,CAAC,CAAC,GAAGtF,KAAK,CAACmG,SAAS,CAACC,eAAe,CAACb,MAAM,CAAC,EAAED,QAAQ,CAAC,CAAC,CAAC,GAAGtF,KAAK,CAACmG,SAAS,CAACC,eAAe,CAACb,MAAM,CAAC,EAAED,QAAQ,CAAC,CAAC,CAAC,GAAGtF,KAAK,CAACmG,SAAS,CAACC,eAAe,CAACb,MAAM,CAAC,CAAC;QACzL,CAAC,MAAM;UACL,IAAIc,MAAM,GAAGlD,IAAI,CAACmD,IAAI,CAAC,CAAC,GAAGnD,IAAI,CAAC8C,MAAM,CAAC,CAAC,GAAG,CAAC,CAAC,GAAG9C,IAAI,CAACuB,EAAE,GAAG,GAAG;UAC7D,IAAI6B,GAAG,GAAG,CAAC,GAAGpD,IAAI,CAACuB,EAAE,GAAGvB,IAAI,CAAC8C,MAAM,CAAC,CAAC;UACrCjC,KAAK,CAACsB,QAAQ,CAACY,GAAG,CAAC/C,IAAI,CAACqD,GAAG,CAACH,MAAM,CAAC,GAAGlD,IAAI,CAACqD,GAAG,CAACD,GAAG,CAAC,GAAGZ,MAAM,EAAExC,IAAI,CAACsD,GAAG,CAACtD,IAAI,CAACqD,GAAG,CAACH,MAAM,CAAC,GAAGlD,IAAI,CAACuD,GAAG,CAACH,GAAG,CAAC,GAAGZ,MAAM,CAAC,EAAExC,IAAI,CAACuD,GAAG,CAACL,MAAM,CAAC,GAAGV,MAAM,CAAC;QAC/I;MACF;IACF;EACF,CAAC,EAAE,CAACJ,MAAM,EAAEG,OAAO,EAAEC,MAAM,EAAE,GAAGL,QAAQ,CAAC,CAAC;EAC1C,MAAMvC,GAAG,GAAG9C,KAAK,CAAC+C,OAAO,CAAC,OAAO;IAC/BU;EACF,CAAC,CAAC,EAAE,CAACA,MAAM,CAAC,CAAC;EACbzD,KAAK,CAACiE,mBAAmB,CAAC5C,UAAU,EAAE,MAAMyB,GAAG,EAAE,CAACA,GAAG,CAAC,CAAC;EACvD9C,KAAK,CAAC2C,eAAe,CAAC,MAAM;IAC1B,IAAI+D,cAAc;IAClB,MAAMC,KAAK,GAAGpE,OAAO,CAACM,OAAO;IAC7B,IAAI+C,MAAM,EAAE,CAACc,cAAc,GAAGd,MAAM,CAAC5C,MAAM,KAAK,IAAI,IAAI0D,cAAc,CAACT,GAAG,CAACU,KAAK,CAACC,IAAI,EAAE9D,GAAG,CAAC;IAC3F,OAAO,MAAM;MACX,IAAI+D,eAAe;MACnB,OAAO,MAAMjB,MAAM,IAAI,IAAI,IAAI,CAACiB,eAAe,GAAGjB,MAAM,CAAC5C,MAAM,KAAK,IAAI,GAAG,KAAK,CAAC,GAAG6D,eAAe,CAACC,MAAM,CAACH,KAAK,CAACC,IAAI,CAAC,CAAC;IACzH,CAAC;EACH,CAAC,EAAE,CAAChB,MAAM,EAAE9C,GAAG,CAAC,CAAC;EACjB,OAAO,aAAa9C,KAAK,CAACkE,aAAa,CAAC,OAAO,EAAEpE,QAAQ,CAAC;IACxDsE,GAAG,EAAE7B;EACP,CAAC,EAAER,KAAK,CAAC,EAAEgF,KAAK,CAACC,IAAI,CAAC;IACpBtB,MAAM,EAAEH;EACV,CAAC,EAAE,CAAC0B,CAAC,EAAEC,KAAK,KAAK,aAAalH,KAAK,CAACkE,aAAa,CAAC,kBAAkB,EAAE;IACpEiD,GAAG,EAAED,KAAK;IACVnC,UAAU,EAAEA,UAAU;IACtB,aAAa,EAAEC,IAAI;IACnB,gBAAgB,EAAE,CAACC,OAAO,EAAEA,OAAO,CAAC;IACpCO,SAAS,EAAEA,SAAS,GAAGD;EACzB,CAAC,EAAE,aAAavF,KAAK,CAACkE,aAAa,CAAC,oBAAoB,EAAE;IACxDkD,MAAM,EAAE,eAAe;IACvBC,IAAI,EAAE,CAAC,CAACnC,IAAI,EAAEA,IAAI,EAAEA,IAAI,EAAE,CAACA,IAAI,EAAEC,IAAI,EAAEC,GAAG;EAC5C,CAAC,CAAC,CAAC,CAAC,CAAC;AACP,CAAC,CAAC;;AAEF;AACA,MAAM1C,mBAAmB,CAAC;EACxB4E,WAAWA,CAACC,QAAQ,EAAErF,KAAK,EAAEsF,GAAG,GAAG,IAAI,EAAE;IACvC,IAAI,CAACD,QAAQ,GAAGA,QAAQ;IACxB,IAAI,CAACC,GAAG,GAAGA,GAAG;IACd,IAAI,CAACtF,KAAK,GAAGA,KAAK;IAClB,IAAI,CAACuF,aAAa,GAAG,KAAK;IAC1B,IAAI,CAACzE,MAAM,GAAG,EAAE;IAChB,IAAI,CAAC0E,MAAM,GAAG,EAAE;IAChB,IAAI,CAAClH,MAAM,GAAG,IAAI;IAClB,IAAI,CAACmH,UAAU,GAAG,IAAI5H,KAAK,CAACgB,KAAK,CAAC,CAAC;IACnC,IAAI,CAAC6G,UAAU,GAAG,CAAC;;IAEnB;IACA,MAAMC,aAAa,GAAG;MACpBC,IAAI,EAAE/H,KAAK,CAACgI,aAAa;MACzBC,SAAS,EAAEjI,KAAK,CAACkI,aAAa;MAC9BC,SAAS,EAAEnI,KAAK,CAACkI;IACnB,CAAC;IACD,IAAI,CAACE,oBAAoB,GAAG,IAAIpI,KAAK,CAACqI,iBAAiB,CAAC,IAAI,CAACZ,GAAG,EAAE,IAAI,CAACA,GAAG,EAAEK,aAAa,CAAC;IAC1F,IAAI,CAACjD,oBAAoB,GAAG,IAAI7E,KAAK,CAACqI,iBAAiB,CAAC,IAAI,CAACZ,GAAG,EAAE,IAAI,CAACA,GAAG,EAAEK,aAAa,CAAC;;IAE1F;IACA,IAAI,CAACQ,UAAU,GAAG,IAAIhI,eAAe,CAAC,CAAC;IACvC,IAAI,CAACiI,SAAS,GAAG,IAAIvI,KAAK,CAACwI,mBAAmB,CAAC;MAC7CC,GAAG,EAAE;IACP,CAAC,CAAC;IACF,IAAI,CAACC,iBAAiB,GAAG;MACvBnE,KAAK,EAAE,IAAI,CAAC6D,oBAAoB,CAACtD;IACnC,CAAC;IACD,IAAI,CAAC6D,eAAe,GAAG;MACrBpE,KAAK,EAAE;IACT,CAAC;IACD,IAAI,CAACgE,SAAS,CAACK,eAAe,GAAGC,MAAM,IAAI;MACzC;MACAA,MAAM,CAACC,YAAY,GAAG,qBAAqB,GAAGD,MAAM,CAACC,YAAY,CAACC,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,GAAG,6DAA6D;;MAE9I;MACA,MAAMC,SAAS,GAAGH,MAAM,CAACI,cAAc,CAACC,OAAO,CAAC,eAAe,CAAC;MAChEL,MAAM,CAACI,cAAc,GAAG,qBAAqB,GAAGJ,MAAM,CAACI,cAAc,CAACF,KAAK,CAAC,CAAC,EAAEC,SAAS,CAAC,GAAG,yEAAyE,GAAGH,MAAM,CAACI,cAAc,CAACF,KAAK,CAACC,SAAS,GAAG,CAAC,EAAE,CAAC,CAAC,CAAC,GAAG;AAC/N;AACA,QAAQ;;MAEF;MACAH,MAAM,CAACM,QAAQ,CAACT,iBAAiB,GAAG,IAAI,CAACA,iBAAiB;MAC1DG,MAAM,CAACM,QAAQ,CAACR,eAAe,GAAG,IAAI,CAACA,eAAe;IACxD,CAAC;EACH;EACAnF,KAAKA,CAAA,EAAG;IACN,IAAI,CAACgE,QAAQ,CAAC4B,aAAa,CAAC,IAAI,CAACxB,UAAU,CAAC;IAC5C,IAAI,CAACC,UAAU,GAAG,IAAI,CAACL,QAAQ,CAAC6B,aAAa,CAAC,CAAC;IAC/C,IAAI,CAAC7B,QAAQ,CAAC8B,aAAa,CAAC,OAAO,EAAE,CAAC,CAAC;IACvC,IAAI,CAAC9B,QAAQ,CAAC+B,eAAe,CAAC,IAAI,CAACnB,oBAAoB,CAAC;IACxD,IAAI,CAACZ,QAAQ,CAAChE,KAAK,CAAC,CAAC;IACrB,IAAI,CAACgE,QAAQ,CAAC+B,eAAe,CAAC,IAAI,CAAC1E,oBAAoB,CAAC;IACxD,IAAI,CAAC2C,QAAQ,CAAChE,KAAK,CAAC,CAAC;IACrB,IAAI,CAACgE,QAAQ,CAAC+B,eAAe,CAAC,IAAI,CAAC;IACnC,IAAI,CAAC/B,QAAQ,CAAC8B,aAAa,CAAC,IAAI,CAAC1B,UAAU,EAAE,IAAI,CAACC,UAAU,CAAC;IAC7D,IAAI,CAAC5E,MAAM,GAAG,EAAE;IAChB,IAAI,CAAC0E,MAAM,GAAG,EAAE;IAChB,IAAI,CAACxF,KAAK,CAACiC,QAAQ,CAAC3D,MAAM,IAAI;MAC5B,IAAIC,UAAU,CAACD,MAAM,CAAC,EAAE;QACtB,IAAI,CAACkH,MAAM,CAAC6B,IAAI,CAAC;UACf/I,MAAM;UACNgD,QAAQ,EAAEhD,MAAM,CAACgD;QACnB,CAAC,CAAC;MACJ,CAAC,MAAM,IAAIjD,OAAO,CAACC,MAAM,CAAC,EAAE;QAC1B,IAAI,CAACwC,MAAM,CAACuG,IAAI,CAAC;UACf/I,MAAM;UACNgF,SAAS,EAAEhF,MAAM,CAACgF;QACpB,CAAC,CAAC;MACJ;IACF,CAAC,CAAC;EACJ;EACA5B,OAAOA,CAAA,EAAG;IACR,IAAI,CAACZ,MAAM,CAACc,OAAO,CAACC,KAAK,IAAIA,KAAK,CAACvD,MAAM,CAACgF,SAAS,GAAG,CAAC,CAAC;IACxD,IAAI,CAACkC,MAAM,CAAC5D,OAAO,CAAC0F,IAAI,IAAIA,IAAI,CAAChJ,MAAM,CAACgD,QAAQ,GAAG,IAAI,CAAC6E,UAAU,CAAC;EACrE;EACArE,MAAMA,CAAA,EAAG;IACP,IAAI,CAAChB,MAAM,CAACc,OAAO,CAACC,KAAK,IAAIA,KAAK,CAACvD,MAAM,CAACgF,SAAS,GAAGzB,KAAK,CAACyB,SAAS,CAAC;IACtE,IAAI,CAACkC,MAAM,CAAC5D,OAAO,CAAC0F,IAAI,IAAIA,IAAI,CAAChJ,MAAM,CAACgD,QAAQ,GAAGgG,IAAI,CAAChG,QAAQ,CAAC;EACnE;EACAZ,SAASA,CAACpC,MAAM,EAAE;IAChB,IAAI,CAACA,MAAM,GAAGA,MAAM;EACtB;EACAiD,MAAMA,CAACtB,MAAM,EAAEsH,WAAW,GAAG,GAAG,EAAE;IAChC,IAAI,CAAC,IAAI,CAACjJ,MAAM,EAAE;IAClB;IACA,IAAI,CAACkI,eAAe,CAACpE,KAAK,GAAGmF,WAAW;IACxC,IAAI,CAACjJ,MAAM,CAACgD,QAAQ,GAAG,IAAI,CAAC8E,SAAS;IACrC;IACA,MAAMoB,SAAS,GAAG,IAAI,CAACjC,aAAa,GAAG,IAAI,CAACU,oBAAoB,GAAG,IAAI,CAACvD,oBAAoB;IAC5F,MAAM+E,WAAW,GAAG,IAAI,CAAClC,aAAa,GAAG,IAAI,CAAC7C,oBAAoB,GAAG,IAAI,CAACuD,oBAAoB;IAC9F;IACA,MAAMyB,KAAK,GAAG,IAAI,CAAC1H,KAAK,CAAC2H,UAAU;IACnC,IAAI,CAAC3H,KAAK,CAAC2H,UAAU,GAAG,IAAI;IAC5B,IAAI,CAACtC,QAAQ,CAAC+B,eAAe,CAACI,SAAS,CAAC;IACxC,IAAI,CAACjB,iBAAiB,CAACnE,KAAK,GAAGqF,WAAW,CAAC9E,OAAO;IAClD,IAAI,CAAC4C,aAAa,GAAG,CAAC,IAAI,CAACA,aAAa;IACxC,IAAI,CAACF,QAAQ,CAACuC,MAAM,CAAC,IAAI,CAAC5H,KAAK,EAAEC,MAAM,CAAC;IACxC,IAAI,CAACoF,QAAQ,CAAC+B,eAAe,CAAC,IAAI,CAAC;IACnC,IAAI,CAACpH,KAAK,CAAC2H,UAAU,GAAGD,KAAK;EAC/B;AACF;AAEA,SAASxI,mBAAmB,EAAE0D,eAAe,EAAEnE,mBAAmB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}