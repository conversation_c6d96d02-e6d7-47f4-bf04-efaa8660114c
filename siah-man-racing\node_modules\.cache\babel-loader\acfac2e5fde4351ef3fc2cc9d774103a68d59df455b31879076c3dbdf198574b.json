{"ast": null, "code": "class Panel {\n  constructor(name, fg, bg) {\n    this.name = name;\n    this.fg = fg;\n    this.bg = bg;\n    this.gradient = null;\n    this.PR = Math.round(window.devicePixelRatio || 1);\n    this.WIDTH = 90 * this.PR;\n    this.HEIGHT = 48 * this.PR;\n    this.TEXT_X = 3 * this.PR;\n    this.TEXT_Y = 2 * this.PR;\n    this.GRAPH_X = 3 * this.PR;\n    this.GRAPH_Y = 15 * this.PR;\n    this.GRAPH_WIDTH = 84 * this.PR;\n    this.GRAPH_HEIGHT = 30 * this.PR;\n    this.canvas = document.createElement(\"canvas\");\n    this.canvas.width = this.WIDTH;\n    this.canvas.height = this.HEIGHT;\n    this.canvas.style.width = \"90px\";\n    this.canvas.style.height = \"48px\";\n    this.canvas.style.position = \"absolute\";\n    this.canvas.style.cssText = \"width:90px;height:48px\";\n    this.context = this.canvas.getContext(\"2d\");\n    this.initializeCanvas();\n  }\n  createGradient() {\n    if (!this.context) throw new Error(\"No context\");\n    const gradient = this.context.createLinearGradient(0, this.GRAPH_Y, 0, this.GRAPH_Y + this.GRAPH_HEIGHT);\n    let startColor;\n    const endColor = this.fg;\n    switch (this.fg.toLowerCase()) {\n      case \"#0ff\":\n        startColor = \"#006666\";\n        break;\n      case \"#0f0\":\n        startColor = \"#006600\";\n        break;\n      case \"#ff0\":\n        startColor = \"#666600\";\n        break;\n      case \"#e1e1e1\":\n        startColor = \"#666666\";\n        break;\n      default:\n        startColor = this.bg;\n        break;\n    }\n    gradient.addColorStop(0, startColor);\n    gradient.addColorStop(1, endColor);\n    return gradient;\n  }\n  initializeCanvas() {\n    if (!this.context) return;\n    this.context.font = \"bold \" + 9 * this.PR + \"px Helvetica,Arial,sans-serif\";\n    this.context.textBaseline = \"top\";\n    this.gradient = this.createGradient();\n    this.context.fillStyle = this.bg;\n    this.context.fillRect(0, 0, this.WIDTH, this.HEIGHT);\n    this.context.fillStyle = this.fg;\n    this.context.fillText(this.name, this.TEXT_X, this.TEXT_Y);\n    this.context.fillStyle = this.fg;\n    this.context.fillRect(this.GRAPH_X, this.GRAPH_Y, this.GRAPH_WIDTH, this.GRAPH_HEIGHT);\n    this.context.fillStyle = this.bg;\n    this.context.globalAlpha = 0.9;\n    this.context.fillRect(this.GRAPH_X, this.GRAPH_Y, this.GRAPH_WIDTH, this.GRAPH_HEIGHT);\n  }\n  update(value, valueGraph, maxValue, maxGraph, decimals = 0) {\n    if (!this.context || !this.gradient) return;\n    const min = Math.min(Infinity, value);\n    const max = Math.max(maxValue, value);\n    maxGraph = Math.max(maxGraph, valueGraph);\n    this.context.globalAlpha = 1;\n    this.context.fillStyle = this.bg;\n    this.context.fillRect(0, 0, this.WIDTH, this.GRAPH_Y);\n    this.context.fillStyle = this.fg;\n    this.context.fillText(`${value.toFixed(decimals)} ${this.name} (${min.toFixed(decimals)}-${parseFloat(max.toFixed(decimals))})`, this.TEXT_X, this.TEXT_Y);\n    this.context.drawImage(this.canvas, this.GRAPH_X + this.PR, this.GRAPH_Y, this.GRAPH_WIDTH - this.PR, this.GRAPH_HEIGHT, this.GRAPH_X, this.GRAPH_Y, this.GRAPH_WIDTH - this.PR, this.GRAPH_HEIGHT);\n    const columnHeight = this.GRAPH_HEIGHT - (1 - valueGraph / maxGraph) * this.GRAPH_HEIGHT;\n    if (columnHeight > 0) {\n      this.context.globalAlpha = 1;\n      this.context.fillStyle = this.gradient;\n      this.context.fillRect(this.GRAPH_X + this.GRAPH_WIDTH - this.PR, this.GRAPH_Y + this.GRAPH_HEIGHT - columnHeight, this.PR, columnHeight);\n    }\n  }\n}\nexport { Panel };", "map": {"version": 3, "names": ["Panel", "constructor", "name", "fg", "bg", "gradient", "PR", "Math", "round", "window", "devicePixelRatio", "WIDTH", "HEIGHT", "TEXT_X", "TEXT_Y", "GRAPH_X", "GRAPH_Y", "GRAPH_WIDTH", "GRAPH_HEIGHT", "canvas", "document", "createElement", "width", "height", "style", "position", "cssText", "context", "getContext", "initializeCanvas", "createGradient", "Error", "createLinearGradient", "startColor", "endColor", "toLowerCase", "addColorStop", "font", "textBaseline", "fillStyle", "fillRect", "fillText", "globalAlpha", "update", "value", "valueGraph", "maxValue", "maxGraph", "decimals", "min", "Infinity", "max", "toFixed", "parseFloat", "drawImage", "columnHeight"], "sources": ["C:\\Users\\<USER>\\Documents\\Apps_Creations\\SiahMan_Racing4Speed_WebGame\\siah-man-racing\\node_modules\\stats-gl\\lib\\panel.ts"], "sourcesContent": ["class Panel {\n    canvas: HTMLCanvasElement;\n    context: CanvasRenderingContext2D | null;\n    name: string;\n    fg: string;\n    bg: string;\n    gradient: CanvasGradient | null;\n    PR: number;\n    WIDTH: number;\n    HEIGHT: number;\n    TEXT_X: number;\n    TEXT_Y: number;\n    GRAPH_X: number;\n    GRAPH_Y: number;\n    GRAPH_WIDTH: number;\n    GRAPH_HEIGHT: number;\n\n    constructor(name: string, fg: string, bg: string) {\n        this.name = name;\n        this.fg = fg;\n        this.bg = bg;\n        this.gradient = null;\n        this.PR = Math.round(window.devicePixelRatio || 1);\n\n        this.WIDTH = 90 * this.PR;\n        this.HEIGHT = 48 * this.PR;\n        this.TEXT_X = 3 * this.PR;\n        this.TEXT_Y = 2 * this.PR;\n        this.GRAPH_X = 3 * this.PR;\n        this.GRAPH_Y = 15 * this.PR;\n        this.GRAPH_WIDTH = 84 * this.PR;\n        this.GRAPH_HEIGHT = 30 * this.PR;\n\n        this.canvas = document.createElement('canvas');\n        this.canvas.width = this.WIDTH;\n        this.canvas.height = this.HEIGHT;\n        this.canvas.style.width = '90px';\n        this.canvas.style.height = '48px';\n        this.canvas.style.position = 'absolute';\n        this.canvas.style.cssText = 'width:90px;height:48px';\n\n        this.context = this.canvas.getContext('2d');\n        this.initializeCanvas();\n    }\n\n    private createGradient(): CanvasGradient {\n        if (!this.context) throw new Error('No context');\n\n        const gradient = this.context.createLinearGradient(\n            0,\n            this.GRAPH_Y,\n            0,\n            this.GRAPH_Y + this.GRAPH_HEIGHT\n        );\n\n        let startColor: string;\n        const endColor: string = this.fg;\n\n        switch (this.fg.toLowerCase()) {\n            case '#0ff': // Cyan\n                startColor = '#006666'; // Dark Cyan\n                break;\n            case '#0f0': // Green\n                startColor = '#006600'; // Dark Green\n                break;\n            case '#ff0': // Yellow\n                startColor = '#666600'; // Dark Yellow\n                break;\n            case '#e1e1e1': // Light Gray\n                startColor = '#666666'; // Medium Gray\n                break;\n            default:\n                startColor = this.bg;\n                break;\n        }\n\n        gradient.addColorStop(0, startColor);\n        gradient.addColorStop(1, endColor);\n\n        return gradient;\n    }\n\n    private initializeCanvas() {\n        if (!this.context) return;\n\n        this.context.font = 'bold ' + (9 * this.PR) + 'px Helvetica,Arial,sans-serif';\n        this.context.textBaseline = 'top';\n\n        // Create gradient\n        this.gradient = this.createGradient();\n\n        // Fill background\n        this.context.fillStyle = this.bg;\n        this.context.fillRect(0, 0, this.WIDTH, this.HEIGHT);\n\n        // Draw text\n        this.context.fillStyle = this.fg;\n        this.context.fillText(this.name, this.TEXT_X, this.TEXT_Y);\n\n        // Draw initial graph area\n        this.context.fillStyle = this.fg;\n        this.context.fillRect(this.GRAPH_X, this.GRAPH_Y, this.GRAPH_WIDTH, this.GRAPH_HEIGHT);\n\n        // Apply semi-transparent background\n        this.context.fillStyle = this.bg;\n        this.context.globalAlpha = 0.9;\n        this.context.fillRect(this.GRAPH_X, this.GRAPH_Y, this.GRAPH_WIDTH, this.GRAPH_HEIGHT);\n    }\n\n    update(\n        value: number,\n        valueGraph: number,\n        maxValue: number,\n        maxGraph: number,\n        decimals = 0\n    ) {\n        if (!this.context || !this.gradient) return;\n\n        const min = Math.min(Infinity, value);\n        const max = Math.max(maxValue, value);\n        maxGraph = Math.max(maxGraph, valueGraph);\n\n        // Clear and redraw background\n        this.context.globalAlpha = 1;\n        this.context.fillStyle = this.bg;\n        this.context.fillRect(0, 0, this.WIDTH, this.GRAPH_Y);\n\n        // Draw text\n        this.context.fillStyle = this.fg;\n        this.context.fillText(\n            `${value.toFixed(decimals)} ${this.name} (${min.toFixed(decimals)}-${parseFloat(\n                max.toFixed(decimals)\n            )})`,\n            this.TEXT_X,\n            this.TEXT_Y\n        );\n\n        // Shift the graph left\n        this.context.drawImage(\n            this.canvas,\n            this.GRAPH_X + this.PR,\n            this.GRAPH_Y,\n            this.GRAPH_WIDTH - this.PR,\n            this.GRAPH_HEIGHT,\n            this.GRAPH_X,\n            this.GRAPH_Y,\n            this.GRAPH_WIDTH - this.PR,\n            this.GRAPH_HEIGHT\n        );\n\n        // Draw new column with gradient\n        const columnHeight = this.GRAPH_HEIGHT - (1 - valueGraph / maxGraph) * this.GRAPH_HEIGHT;\n\n        if (columnHeight > 0) {\n            this.context.globalAlpha = 1;\n            this.context.fillStyle = this.gradient;\n            this.context.fillRect(\n                this.GRAPH_X + this.GRAPH_WIDTH - this.PR,\n                this.GRAPH_Y + this.GRAPH_HEIGHT - columnHeight,\n                this.PR,\n                columnHeight\n            );\n        }\n    }\n}\n\nexport { Panel };\n"], "mappings": "AAAA,MAAMA,KAAA,CAAM;EAiBRC,YAAYC,IAAA,EAAcC,EAAA,EAAYC,EAAA,EAAY;IAC9C,KAAKF,IAAA,GAAOA,IAAA;IACZ,KAAKC,EAAA,GAAKA,EAAA;IACV,KAAKC,EAAA,GAAKA,EAAA;IACV,KAAKC,QAAA,GAAW;IAChB,KAAKC,EAAA,GAAKC,IAAA,CAAKC,KAAA,CAAMC,MAAA,CAAOC,gBAAA,IAAoB,CAAC;IAE5C,KAAAC,KAAA,GAAQ,KAAK,KAAKL,EAAA;IAClB,KAAAM,MAAA,GAAS,KAAK,KAAKN,EAAA;IACnB,KAAAO,MAAA,GAAS,IAAI,KAAKP,EAAA;IAClB,KAAAQ,MAAA,GAAS,IAAI,KAAKR,EAAA;IAClB,KAAAS,OAAA,GAAU,IAAI,KAAKT,EAAA;IACnB,KAAAU,OAAA,GAAU,KAAK,KAAKV,EAAA;IACpB,KAAAW,WAAA,GAAc,KAAK,KAAKX,EAAA;IACxB,KAAAY,YAAA,GAAe,KAAK,KAAKZ,EAAA;IAEzB,KAAAa,MAAA,GAASC,QAAA,CAASC,aAAA,CAAc,QAAQ;IACxC,KAAAF,MAAA,CAAOG,KAAA,GAAQ,KAAKX,KAAA;IACpB,KAAAQ,MAAA,CAAOI,MAAA,GAAS,KAAKX,MAAA;IACrB,KAAAO,MAAA,CAAOK,KAAA,CAAMF,KAAA,GAAQ;IACrB,KAAAH,MAAA,CAAOK,KAAA,CAAMD,MAAA,GAAS;IACtB,KAAAJ,MAAA,CAAOK,KAAA,CAAMC,QAAA,GAAW;IACxB,KAAAN,MAAA,CAAOK,KAAA,CAAME,OAAA,GAAU;IAE5B,KAAKC,OAAA,GAAU,KAAKR,MAAA,CAAOS,UAAA,CAAW,IAAI;IAC1C,KAAKC,gBAAA,CAAiB;EAC1B;EAEQC,eAAA,EAAiC;IACrC,IAAI,CAAC,KAAKH,OAAA,EAAe,UAAII,KAAA,CAAM,YAAY;IAEzC,MAAA1B,QAAA,GAAW,KAAKsB,OAAA,CAAQK,oBAAA,CAC1B,GACA,KAAKhB,OAAA,EACL,GACA,KAAKA,OAAA,GAAU,KAAKE,YAAA;IAGpB,IAAAe,UAAA;IACJ,MAAMC,QAAA,GAAmB,KAAK/B,EAAA;IAEtB,aAAKA,EAAA,CAAGgC,WAAA,CAAe;MAC3B,KAAK;QACYF,UAAA;QACb;MACJ,KAAK;QACYA,UAAA;QACb;MACJ,KAAK;QACYA,UAAA;QACb;MACJ,KAAK;QACYA,UAAA;QACb;MACJ;QACIA,UAAA,GAAa,KAAK7B,EAAA;QAClB;IACR;IAESC,QAAA,CAAA+B,YAAA,CAAa,GAAGH,UAAU;IAC1B5B,QAAA,CAAA+B,YAAA,CAAa,GAAGF,QAAQ;IAE1B,OAAA7B,QAAA;EACX;EAEQwB,iBAAA,EAAmB;IACvB,IAAI,CAAC,KAAKF,OAAA,EAAS;IAEnB,KAAKA,OAAA,CAAQU,IAAA,GAAO,UAAW,IAAI,KAAK/B,EAAA,GAAM;IAC9C,KAAKqB,OAAA,CAAQW,YAAA,GAAe;IAGvB,KAAAjC,QAAA,GAAW,KAAKyB,cAAA;IAGhB,KAAAH,OAAA,CAAQY,SAAA,GAAY,KAAKnC,EAAA;IAC9B,KAAKuB,OAAA,CAAQa,QAAA,CAAS,GAAG,GAAG,KAAK7B,KAAA,EAAO,KAAKC,MAAM;IAG9C,KAAAe,OAAA,CAAQY,SAAA,GAAY,KAAKpC,EAAA;IAC9B,KAAKwB,OAAA,CAAQc,QAAA,CAAS,KAAKvC,IAAA,EAAM,KAAKW,MAAA,EAAQ,KAAKC,MAAM;IAGpD,KAAAa,OAAA,CAAQY,SAAA,GAAY,KAAKpC,EAAA;IACzB,KAAAwB,OAAA,CAAQa,QAAA,CAAS,KAAKzB,OAAA,EAAS,KAAKC,OAAA,EAAS,KAAKC,WAAA,EAAa,KAAKC,YAAY;IAGhF,KAAAS,OAAA,CAAQY,SAAA,GAAY,KAAKnC,EAAA;IAC9B,KAAKuB,OAAA,CAAQe,WAAA,GAAc;IACtB,KAAAf,OAAA,CAAQa,QAAA,CAAS,KAAKzB,OAAA,EAAS,KAAKC,OAAA,EAAS,KAAKC,WAAA,EAAa,KAAKC,YAAY;EACzF;EAEAyB,OACIC,KAAA,EACAC,UAAA,EACAC,QAAA,EACAC,QAAA,EACAC,QAAA,GAAW,GACb;IACE,IAAI,CAAC,KAAKrB,OAAA,IAAW,CAAC,KAAKtB,QAAA,EAAU;IAErC,MAAM4C,GAAA,GAAM1C,IAAA,CAAK0C,GAAA,CAAIC,QAAA,EAAUN,KAAK;IACpC,MAAMO,GAAA,GAAM5C,IAAA,CAAK4C,GAAA,CAAIL,QAAA,EAAUF,KAAK;IACzBG,QAAA,GAAAxC,IAAA,CAAK4C,GAAA,CAAIJ,QAAA,EAAUF,UAAU;IAGxC,KAAKlB,OAAA,CAAQe,WAAA,GAAc;IACtB,KAAAf,OAAA,CAAQY,SAAA,GAAY,KAAKnC,EAAA;IAC9B,KAAKuB,OAAA,CAAQa,QAAA,CAAS,GAAG,GAAG,KAAK7B,KAAA,EAAO,KAAKK,OAAO;IAG/C,KAAAW,OAAA,CAAQY,SAAA,GAAY,KAAKpC,EAAA;IAC9B,KAAKwB,OAAA,CAAQc,QAAA,CACT,GAAGG,KAAA,CAAMQ,OAAA,CAAQJ,QAAQ,CAAC,IAAI,KAAK9C,IAAI,KAAK+C,GAAA,CAAIG,OAAA,CAAQJ,QAAQ,CAAC,IAAIK,UAAA,CACjEF,GAAA,CAAIC,OAAA,CAAQJ,QAAQ,CACvB,MACD,KAAKnC,MAAA,EACL,KAAKC,MAAA;IAIT,KAAKa,OAAA,CAAQ2B,SAAA,CACT,KAAKnC,MAAA,EACL,KAAKJ,OAAA,GAAU,KAAKT,EAAA,EACpB,KAAKU,OAAA,EACL,KAAKC,WAAA,GAAc,KAAKX,EAAA,EACxB,KAAKY,YAAA,EACL,KAAKH,OAAA,EACL,KAAKC,OAAA,EACL,KAAKC,WAAA,GAAc,KAAKX,EAAA,EACxB,KAAKY,YAAA;IAIT,MAAMqC,YAAA,GAAe,KAAKrC,YAAA,IAAgB,IAAI2B,UAAA,GAAaE,QAAA,IAAY,KAAK7B,YAAA;IAE5E,IAAIqC,YAAA,GAAe,GAAG;MAClB,KAAK5B,OAAA,CAAQe,WAAA,GAAc;MACtB,KAAAf,OAAA,CAAQY,SAAA,GAAY,KAAKlC,QAAA;MAC9B,KAAKsB,OAAA,CAAQa,QAAA,CACT,KAAKzB,OAAA,GAAU,KAAKE,WAAA,GAAc,KAAKX,EAAA,EACvC,KAAKU,OAAA,GAAU,KAAKE,YAAA,GAAeqC,YAAA,EACnC,KAAKjD,EAAA,EACLiD,YAAA;IAER;EACJ;AACJ", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}