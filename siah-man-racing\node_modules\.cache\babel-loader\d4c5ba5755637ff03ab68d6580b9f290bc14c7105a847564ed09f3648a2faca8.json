{"ast": null, "code": "import { Object3D, MeshBasicMaterial, MathUtils, Mesh } from \"three\";\nimport { LightningStrike } from \"../geometries/LightningStrike.js\";\nclass LightningStorm extends Object3D {\n  constructor(stormParams = {}) {\n    super();\n    this.isLightningStorm = true;\n    this.stormParams = stormParams;\n    stormParams.size = stormParams.size !== void 0 ? stormParams.size : 1e3;\n    stormParams.minHeight = stormParams.minHeight !== void 0 ? stormParams.minHeight : 80;\n    stormParams.maxHeight = stormParams.maxHeight !== void 0 ? stormParams.maxHeight : 100;\n    stormParams.maxSlope = stormParams.maxSlope !== void 0 ? stormParams.maxSlope : 1.1;\n    stormParams.maxLightnings = stormParams.maxLightnings !== void 0 ? stormParams.maxLightnings : 3;\n    stormParams.lightningMinPeriod = stormParams.lightningMinPeriod !== void 0 ? stormParams.lightningMinPeriod : 3;\n    stormParams.lightningMaxPeriod = stormParams.lightningMaxPeriod !== void 0 ? stormParams.lightningMaxPeriod : 7;\n    stormParams.lightningMinDuration = stormParams.lightningMinDuration !== void 0 ? stormParams.lightningMinDuration : 1;\n    stormParams.lightningMaxDuration = stormParams.lightningMaxDuration !== void 0 ? stormParams.lightningMaxDuration : 2.5;\n    this.lightningParameters = LightningStrike.copyParameters(stormParams.lightningParameters, stormParams.lightningParameters);\n    this.lightningParameters.isEternal = false;\n    this.lightningMaterial = stormParams.lightningMaterial !== void 0 ? stormParams.lightningMaterial : new MeshBasicMaterial({\n      color: 11599871\n    });\n    if (stormParams.onRayPosition !== void 0) {\n      this.onRayPosition = stormParams.onRayPosition;\n    } else {\n      this.onRayPosition = function (source, dest) {\n        dest.set((Math.random() - 0.5) * stormParams.size, 0, (Math.random() - 0.5) * stormParams.size);\n        const height = MathUtils.lerp(stormParams.minHeight, stormParams.maxHeight, Math.random());\n        source.set(stormParams.maxSlope * (2 * Math.random() - 1), 1, stormParams.maxSlope * (2 * Math.random() - 1)).multiplyScalar(height).add(dest);\n      };\n    }\n    this.onLightningDown = stormParams.onLightningDown;\n    this.inited = false;\n    this.nextLightningTime = 0;\n    this.lightningsMeshes = [];\n    this.deadLightningsMeshes = [];\n    for (let i = 0; i < this.stormParams.maxLightnings; i++) {\n      const lightning = new LightningStrike(LightningStrike.copyParameters({}, this.lightningParameters));\n      const mesh = new Mesh(lightning, this.lightningMaterial);\n      this.deadLightningsMeshes.push(mesh);\n    }\n  }\n  update(time) {\n    if (!this.inited) {\n      this.nextLightningTime = this.getNextLightningTime(time) * Math.random();\n      this.inited = true;\n    }\n    if (time >= this.nextLightningTime) {\n      const lightningMesh = this.deadLightningsMeshes.pop();\n      if (lightningMesh) {\n        const lightningParams1 = LightningStrike.copyParameters(lightningMesh.geometry.rayParameters, this.lightningParameters);\n        lightningParams1.birthTime = time;\n        lightningParams1.deathTime = time + MathUtils.lerp(this.stormParams.lightningMinDuration, this.stormParams.lightningMaxDuration, Math.random());\n        this.onRayPosition(lightningParams1.sourceOffset, lightningParams1.destOffset);\n        lightningParams1.noiseSeed = Math.random();\n        this.add(lightningMesh);\n        this.lightningsMeshes.push(lightningMesh);\n      }\n      this.nextLightningTime = this.getNextLightningTime(time);\n    }\n    let i = 0,\n      il = this.lightningsMeshes.length;\n    while (i < il) {\n      const mesh = this.lightningsMeshes[i];\n      const lightning = mesh.geometry;\n      const prevState = lightning.state;\n      lightning.update(time);\n      if (prevState === LightningStrike.RAY_PROPAGATING && lightning.state > prevState) {\n        if (this.onLightningDown) {\n          this.onLightningDown(lightning);\n        }\n      }\n      if (lightning.state === LightningStrike.RAY_EXTINGUISHED) {\n        this.lightningsMeshes.splice(this.lightningsMeshes.indexOf(mesh), 1);\n        this.deadLightningsMeshes.push(mesh);\n        this.remove(mesh);\n        il--;\n      } else {\n        i++;\n      }\n    }\n  }\n  getNextLightningTime(currentTime) {\n    return currentTime + MathUtils.lerp(this.stormParams.lightningMinPeriod, this.stormParams.lightningMaxPeriod, Math.random()) / (this.stormParams.maxLightnings + 1);\n  }\n  copy(source, recursive) {\n    super.copy(source, recursive);\n    this.stormParams.size = source.stormParams.size;\n    this.stormParams.minHeight = source.stormParams.minHeight;\n    this.stormParams.maxHeight = source.stormParams.maxHeight;\n    this.stormParams.maxSlope = source.stormParams.maxSlope;\n    this.stormParams.maxLightnings = source.stormParams.maxLightnings;\n    this.stormParams.lightningMinPeriod = source.stormParams.lightningMinPeriod;\n    this.stormParams.lightningMaxPeriod = source.stormParams.lightningMaxPeriod;\n    this.stormParams.lightningMinDuration = source.stormParams.lightningMinDuration;\n    this.stormParams.lightningMaxDuration = source.stormParams.lightningMaxDuration;\n    this.lightningParameters = LightningStrike.copyParameters({}, source.lightningParameters);\n    this.lightningMaterial = source.stormParams.lightningMaterial;\n    this.onLightningDown = source.onLightningDown;\n    return this;\n  }\n  clone() {\n    return new this.constructor(this.stormParams).copy(this);\n  }\n}\nexport { LightningStorm };", "map": {"version": 3, "names": ["LightningStorm", "Object3D", "constructor", "stormParams", "isLightningStorm", "size", "minHeight", "maxHeight", "maxSlope", "maxLightnings", "lightningMinPeriod", "lightningMaxPeriod", "lightningMinDuration", "lightningMaxDuration", "lightningParameters", "LightningStrike", "copyParameters", "isEternal", "lightningMaterial", "MeshBasicMaterial", "color", "onRayPosition", "source", "dest", "set", "Math", "random", "height", "MathUtils", "lerp", "multiplyScalar", "add", "onLightningDown", "inited", "nextLightningTime", "lightnings<PERSON><PERSON><PERSON>", "deadLightningsMeshes", "i", "lightning", "mesh", "<PERSON><PERSON>", "push", "update", "time", "getNextLightningTime", "<PERSON><PERSON><PERSON>", "pop", "lightningParams1", "geometry", "rayParameters", "birthTime", "deathTime", "sourceOffset", "destOffset", "noiseSeed", "il", "length", "prevState", "state", "RAY_PROPAGATING", "RAY_EXTINGUISHED", "splice", "indexOf", "remove", "currentTime", "copy", "recursive", "clone"], "sources": ["C:\\Users\\<USER>\\Documents\\Apps_Creations\\SiahMan_Racing4Speed_WebGame\\siah-man-racing\\node_modules\\src\\objects\\LightningStorm.js"], "sourcesContent": ["import { MathUtils, Mesh, MeshBasicMaterial, Object3D } from 'three'\nimport { LightningStrike } from '../geometries/LightningStrike'\n\n/**\n * @fileoverview Lightning strike object generator\n *\n *\n * Usage\n *\n * const myStorm = new LightningStorm( paramsObject );\n * myStorm.position.set( ... );\n * scene.add( myStorm );\n * ...\n * myStorm.update( currentTime );\n *\n * The \"currentTime\" can only go forwards or be stopped.\n *\n *\n * LightningStorm parameters:\n *\n * @param {double} size Size of the storm. If no 'onRayPosition' parameter is defined, it means the side of the rectangle the storm covers.\n *\n * @param {double} minHeight Minimum height a ray can start at. If no 'onRayPosition' parameter is defined, it means the height above plane y = 0.\n *\n * @param {double} maxHeight Maximum height a ray can start at. If no 'onRayPosition' parameter is defined, it means the height above plane y = 0.\n *\n * @param {double} maxSlope The maximum inclination slope of a ray. If no 'onRayPosition' parameter is defined, it means the slope relative to plane y = 0.\n *\n * @param {integer} maxLightnings Greater than 0. The maximum number of simultaneous rays.\n *\n * @param {double} lightningMinPeriod minimum time between two consecutive rays.\n *\n * @param {double} lightningMaxPeriod maximum time between two consecutive rays.\n *\n * @param {double} lightningMinDuration The minimum time a ray can last.\n *\n * @param {double} lightningMaxDuration The maximum time a ray can last.\n *\n * @param {Object} lightningParameters The parameters for created rays. See LightningStrike (geometry)\n *\n * @param {Material} lightningMaterial The THREE.Material used for the created rays.\n *\n * @param {function} onRayPosition Optional callback with two Vector3 parameters (source, dest). You can set here the start and end points for each created ray, using the standard size, minHeight, etc parameters and other values in your algorithm.\n *\n * @param {function} onLightningDown This optional callback is called with one parameter (lightningStrike) when a ray ends propagating, so it has hit the ground.\n *\n *\n */\n\nclass LightningStorm extends Object3D {\n  constructor(stormParams = {}) {\n    super()\n\n    this.isLightningStorm = true\n\n    // Parameters\n\n    this.stormParams = stormParams\n\n    stormParams.size = stormParams.size !== undefined ? stormParams.size : 1000.0\n    stormParams.minHeight = stormParams.minHeight !== undefined ? stormParams.minHeight : 80.0\n    stormParams.maxHeight = stormParams.maxHeight !== undefined ? stormParams.maxHeight : 100.0\n    stormParams.maxSlope = stormParams.maxSlope !== undefined ? stormParams.maxSlope : 1.1\n\n    stormParams.maxLightnings = stormParams.maxLightnings !== undefined ? stormParams.maxLightnings : 3\n\n    stormParams.lightningMinPeriod = stormParams.lightningMinPeriod !== undefined ? stormParams.lightningMinPeriod : 3.0\n    stormParams.lightningMaxPeriod = stormParams.lightningMaxPeriod !== undefined ? stormParams.lightningMaxPeriod : 7.0\n\n    stormParams.lightningMinDuration =\n      stormParams.lightningMinDuration !== undefined ? stormParams.lightningMinDuration : 1.0\n    stormParams.lightningMaxDuration =\n      stormParams.lightningMaxDuration !== undefined ? stormParams.lightningMaxDuration : 2.5\n\n    this.lightningParameters = LightningStrike.copyParameters(\n      stormParams.lightningParameters,\n      stormParams.lightningParameters,\n    )\n\n    this.lightningParameters.isEternal = false\n\n    this.lightningMaterial =\n      stormParams.lightningMaterial !== undefined\n        ? stormParams.lightningMaterial\n        : new MeshBasicMaterial({ color: 0xb0ffff })\n\n    if (stormParams.onRayPosition !== undefined) {\n      this.onRayPosition = stormParams.onRayPosition\n    } else {\n      this.onRayPosition = function (source, dest) {\n        dest.set((Math.random() - 0.5) * stormParams.size, 0, (Math.random() - 0.5) * stormParams.size)\n\n        const height = MathUtils.lerp(stormParams.minHeight, stormParams.maxHeight, Math.random())\n\n        source\n          .set(stormParams.maxSlope * (2 * Math.random() - 1), 1, stormParams.maxSlope * (2 * Math.random() - 1))\n          .multiplyScalar(height)\n          .add(dest)\n      }\n    }\n\n    this.onLightningDown = stormParams.onLightningDown\n\n    // Internal state\n\n    this.inited = false\n    this.nextLightningTime = 0\n    this.lightningsMeshes = []\n    this.deadLightningsMeshes = []\n\n    for (let i = 0; i < this.stormParams.maxLightnings; i++) {\n      const lightning = new LightningStrike(LightningStrike.copyParameters({}, this.lightningParameters))\n      const mesh = new Mesh(lightning, this.lightningMaterial)\n      this.deadLightningsMeshes.push(mesh)\n    }\n  }\n\n  update(time) {\n    if (!this.inited) {\n      this.nextLightningTime = this.getNextLightningTime(time) * Math.random()\n      this.inited = true\n    }\n\n    if (time >= this.nextLightningTime) {\n      // Lightning creation\n\n      const lightningMesh = this.deadLightningsMeshes.pop()\n\n      if (lightningMesh) {\n        const lightningParams1 = LightningStrike.copyParameters(\n          lightningMesh.geometry.rayParameters,\n          this.lightningParameters,\n        )\n\n        lightningParams1.birthTime = time\n        lightningParams1.deathTime =\n          time +\n          MathUtils.lerp(this.stormParams.lightningMinDuration, this.stormParams.lightningMaxDuration, Math.random())\n\n        this.onRayPosition(lightningParams1.sourceOffset, lightningParams1.destOffset)\n\n        lightningParams1.noiseSeed = Math.random()\n\n        this.add(lightningMesh)\n\n        this.lightningsMeshes.push(lightningMesh)\n      }\n\n      // Schedule next lightning\n      this.nextLightningTime = this.getNextLightningTime(time)\n    }\n\n    let i = 0,\n      il = this.lightningsMeshes.length\n\n    while (i < il) {\n      const mesh = this.lightningsMeshes[i]\n\n      const lightning = mesh.geometry\n\n      const prevState = lightning.state\n\n      lightning.update(time)\n\n      if (prevState === LightningStrike.RAY_PROPAGATING && lightning.state > prevState) {\n        if (this.onLightningDown) {\n          this.onLightningDown(lightning)\n        }\n      }\n\n      if (lightning.state === LightningStrike.RAY_EXTINGUISHED) {\n        // Lightning is to be destroyed\n\n        this.lightningsMeshes.splice(this.lightningsMeshes.indexOf(mesh), 1)\n\n        this.deadLightningsMeshes.push(mesh)\n\n        this.remove(mesh)\n\n        il--\n      } else {\n        i++\n      }\n    }\n  }\n\n  getNextLightningTime(currentTime) {\n    return (\n      currentTime +\n      MathUtils.lerp(this.stormParams.lightningMinPeriod, this.stormParams.lightningMaxPeriod, Math.random()) /\n        (this.stormParams.maxLightnings + 1)\n    )\n  }\n\n  copy(source, recursive) {\n    super.copy(source, recursive)\n\n    this.stormParams.size = source.stormParams.size\n    this.stormParams.minHeight = source.stormParams.minHeight\n    this.stormParams.maxHeight = source.stormParams.maxHeight\n    this.stormParams.maxSlope = source.stormParams.maxSlope\n\n    this.stormParams.maxLightnings = source.stormParams.maxLightnings\n\n    this.stormParams.lightningMinPeriod = source.stormParams.lightningMinPeriod\n    this.stormParams.lightningMaxPeriod = source.stormParams.lightningMaxPeriod\n\n    this.stormParams.lightningMinDuration = source.stormParams.lightningMinDuration\n    this.stormParams.lightningMaxDuration = source.stormParams.lightningMaxDuration\n\n    this.lightningParameters = LightningStrike.copyParameters({}, source.lightningParameters)\n\n    this.lightningMaterial = source.stormParams.lightningMaterial\n\n    this.onLightningDown = source.onLightningDown\n\n    return this\n  }\n\n  clone() {\n    return new this.constructor(this.stormParams).copy(this)\n  }\n}\n\nexport { LightningStorm }\n"], "mappings": ";;AAiDA,MAAMA,cAAA,SAAuBC,QAAA,CAAS;EACpCC,YAAYC,WAAA,GAAc,IAAI;IAC5B,MAAO;IAEP,KAAKC,gBAAA,GAAmB;IAIxB,KAAKD,WAAA,GAAcA,WAAA;IAEnBA,WAAA,CAAYE,IAAA,GAAOF,WAAA,CAAYE,IAAA,KAAS,SAAYF,WAAA,CAAYE,IAAA,GAAO;IACvEF,WAAA,CAAYG,SAAA,GAAYH,WAAA,CAAYG,SAAA,KAAc,SAAYH,WAAA,CAAYG,SAAA,GAAY;IACtFH,WAAA,CAAYI,SAAA,GAAYJ,WAAA,CAAYI,SAAA,KAAc,SAAYJ,WAAA,CAAYI,SAAA,GAAY;IACtFJ,WAAA,CAAYK,QAAA,GAAWL,WAAA,CAAYK,QAAA,KAAa,SAAYL,WAAA,CAAYK,QAAA,GAAW;IAEnFL,WAAA,CAAYM,aAAA,GAAgBN,WAAA,CAAYM,aAAA,KAAkB,SAAYN,WAAA,CAAYM,aAAA,GAAgB;IAElGN,WAAA,CAAYO,kBAAA,GAAqBP,WAAA,CAAYO,kBAAA,KAAuB,SAAYP,WAAA,CAAYO,kBAAA,GAAqB;IACjHP,WAAA,CAAYQ,kBAAA,GAAqBR,WAAA,CAAYQ,kBAAA,KAAuB,SAAYR,WAAA,CAAYQ,kBAAA,GAAqB;IAEjHR,WAAA,CAAYS,oBAAA,GACVT,WAAA,CAAYS,oBAAA,KAAyB,SAAYT,WAAA,CAAYS,oBAAA,GAAuB;IACtFT,WAAA,CAAYU,oBAAA,GACVV,WAAA,CAAYU,oBAAA,KAAyB,SAAYV,WAAA,CAAYU,oBAAA,GAAuB;IAEtF,KAAKC,mBAAA,GAAsBC,eAAA,CAAgBC,cAAA,CACzCb,WAAA,CAAYW,mBAAA,EACZX,WAAA,CAAYW,mBACb;IAED,KAAKA,mBAAA,CAAoBG,SAAA,GAAY;IAErC,KAAKC,iBAAA,GACHf,WAAA,CAAYe,iBAAA,KAAsB,SAC9Bf,WAAA,CAAYe,iBAAA,GACZ,IAAIC,iBAAA,CAAkB;MAAEC,KAAA,EAAO;IAAA,CAAU;IAE/C,IAAIjB,WAAA,CAAYkB,aAAA,KAAkB,QAAW;MAC3C,KAAKA,aAAA,GAAgBlB,WAAA,CAAYkB,aAAA;IACvC,OAAW;MACL,KAAKA,aAAA,GAAgB,UAAUC,MAAA,EAAQC,IAAA,EAAM;QAC3CA,IAAA,CAAKC,GAAA,EAAKC,IAAA,CAAKC,MAAA,CAAQ,IAAG,OAAOvB,WAAA,CAAYE,IAAA,EAAM,IAAIoB,IAAA,CAAKC,MAAA,CAAQ,IAAG,OAAOvB,WAAA,CAAYE,IAAI;QAE9F,MAAMsB,MAAA,GAASC,SAAA,CAAUC,IAAA,CAAK1B,WAAA,CAAYG,SAAA,EAAWH,WAAA,CAAYI,SAAA,EAAWkB,IAAA,CAAKC,MAAA,EAAQ;QAEzFJ,MAAA,CACGE,GAAA,CAAIrB,WAAA,CAAYK,QAAA,IAAY,IAAIiB,IAAA,CAAKC,MAAA,CAAM,IAAK,IAAI,GAAGvB,WAAA,CAAYK,QAAA,IAAY,IAAIiB,IAAA,CAAKC,MAAA,CAAM,IAAK,EAAE,EACrGI,cAAA,CAAeH,MAAM,EACrBI,GAAA,CAAIR,IAAI;MACZ;IACF;IAED,KAAKS,eAAA,GAAkB7B,WAAA,CAAY6B,eAAA;IAInC,KAAKC,MAAA,GAAS;IACd,KAAKC,iBAAA,GAAoB;IACzB,KAAKC,gBAAA,GAAmB,EAAE;IAC1B,KAAKC,oBAAA,GAAuB,EAAE;IAE9B,SAASC,CAAA,GAAI,GAAGA,CAAA,GAAI,KAAKlC,WAAA,CAAYM,aAAA,EAAe4B,CAAA,IAAK;MACvD,MAAMC,SAAA,GAAY,IAAIvB,eAAA,CAAgBA,eAAA,CAAgBC,cAAA,CAAe,IAAI,KAAKF,mBAAmB,CAAC;MAClG,MAAMyB,IAAA,GAAO,IAAIC,IAAA,CAAKF,SAAA,EAAW,KAAKpB,iBAAiB;MACvD,KAAKkB,oBAAA,CAAqBK,IAAA,CAAKF,IAAI;IACpC;EACF;EAEDG,OAAOC,IAAA,EAAM;IACX,IAAI,CAAC,KAAKV,MAAA,EAAQ;MAChB,KAAKC,iBAAA,GAAoB,KAAKU,oBAAA,CAAqBD,IAAI,IAAIlB,IAAA,CAAKC,MAAA,CAAQ;MACxE,KAAKO,MAAA,GAAS;IACf;IAED,IAAIU,IAAA,IAAQ,KAAKT,iBAAA,EAAmB;MAGlC,MAAMW,aAAA,GAAgB,KAAKT,oBAAA,CAAqBU,GAAA,CAAK;MAErD,IAAID,aAAA,EAAe;QACjB,MAAME,gBAAA,GAAmBhC,eAAA,CAAgBC,cAAA,CACvC6B,aAAA,CAAcG,QAAA,CAASC,aAAA,EACvB,KAAKnC,mBACN;QAEDiC,gBAAA,CAAiBG,SAAA,GAAYP,IAAA;QAC7BI,gBAAA,CAAiBI,SAAA,GACfR,IAAA,GACAf,SAAA,CAAUC,IAAA,CAAK,KAAK1B,WAAA,CAAYS,oBAAA,EAAsB,KAAKT,WAAA,CAAYU,oBAAA,EAAsBY,IAAA,CAAKC,MAAA,EAAQ;QAE5G,KAAKL,aAAA,CAAc0B,gBAAA,CAAiBK,YAAA,EAAcL,gBAAA,CAAiBM,UAAU;QAE7EN,gBAAA,CAAiBO,SAAA,GAAY7B,IAAA,CAAKC,MAAA,CAAQ;QAE1C,KAAKK,GAAA,CAAIc,aAAa;QAEtB,KAAKV,gBAAA,CAAiBM,IAAA,CAAKI,aAAa;MACzC;MAGD,KAAKX,iBAAA,GAAoB,KAAKU,oBAAA,CAAqBD,IAAI;IACxD;IAED,IAAIN,CAAA,GAAI;MACNkB,EAAA,GAAK,KAAKpB,gBAAA,CAAiBqB,MAAA;IAE7B,OAAOnB,CAAA,GAAIkB,EAAA,EAAI;MACb,MAAMhB,IAAA,GAAO,KAAKJ,gBAAA,CAAiBE,CAAC;MAEpC,MAAMC,SAAA,GAAYC,IAAA,CAAKS,QAAA;MAEvB,MAAMS,SAAA,GAAYnB,SAAA,CAAUoB,KAAA;MAE5BpB,SAAA,CAAUI,MAAA,CAAOC,IAAI;MAErB,IAAIc,SAAA,KAAc1C,eAAA,CAAgB4C,eAAA,IAAmBrB,SAAA,CAAUoB,KAAA,GAAQD,SAAA,EAAW;QAChF,IAAI,KAAKzB,eAAA,EAAiB;UACxB,KAAKA,eAAA,CAAgBM,SAAS;QAC/B;MACF;MAED,IAAIA,SAAA,CAAUoB,KAAA,KAAU3C,eAAA,CAAgB6C,gBAAA,EAAkB;QAGxD,KAAKzB,gBAAA,CAAiB0B,MAAA,CAAO,KAAK1B,gBAAA,CAAiB2B,OAAA,CAAQvB,IAAI,GAAG,CAAC;QAEnE,KAAKH,oBAAA,CAAqBK,IAAA,CAAKF,IAAI;QAEnC,KAAKwB,MAAA,CAAOxB,IAAI;QAEhBgB,EAAA;MACR,OAAa;QACLlB,CAAA;MACD;IACF;EACF;EAEDO,qBAAqBoB,WAAA,EAAa;IAChC,OACEA,WAAA,GACApC,SAAA,CAAUC,IAAA,CAAK,KAAK1B,WAAA,CAAYO,kBAAA,EAAoB,KAAKP,WAAA,CAAYQ,kBAAA,EAAoBc,IAAA,CAAKC,MAAA,EAAQ,KACnG,KAAKvB,WAAA,CAAYM,aAAA,GAAgB;EAEvC;EAEDwD,KAAK3C,MAAA,EAAQ4C,SAAA,EAAW;IACtB,MAAMD,IAAA,CAAK3C,MAAA,EAAQ4C,SAAS;IAE5B,KAAK/D,WAAA,CAAYE,IAAA,GAAOiB,MAAA,CAAOnB,WAAA,CAAYE,IAAA;IAC3C,KAAKF,WAAA,CAAYG,SAAA,GAAYgB,MAAA,CAAOnB,WAAA,CAAYG,SAAA;IAChD,KAAKH,WAAA,CAAYI,SAAA,GAAYe,MAAA,CAAOnB,WAAA,CAAYI,SAAA;IAChD,KAAKJ,WAAA,CAAYK,QAAA,GAAWc,MAAA,CAAOnB,WAAA,CAAYK,QAAA;IAE/C,KAAKL,WAAA,CAAYM,aAAA,GAAgBa,MAAA,CAAOnB,WAAA,CAAYM,aAAA;IAEpD,KAAKN,WAAA,CAAYO,kBAAA,GAAqBY,MAAA,CAAOnB,WAAA,CAAYO,kBAAA;IACzD,KAAKP,WAAA,CAAYQ,kBAAA,GAAqBW,MAAA,CAAOnB,WAAA,CAAYQ,kBAAA;IAEzD,KAAKR,WAAA,CAAYS,oBAAA,GAAuBU,MAAA,CAAOnB,WAAA,CAAYS,oBAAA;IAC3D,KAAKT,WAAA,CAAYU,oBAAA,GAAuBS,MAAA,CAAOnB,WAAA,CAAYU,oBAAA;IAE3D,KAAKC,mBAAA,GAAsBC,eAAA,CAAgBC,cAAA,CAAe,CAAE,GAAEM,MAAA,CAAOR,mBAAmB;IAExF,KAAKI,iBAAA,GAAoBI,MAAA,CAAOnB,WAAA,CAAYe,iBAAA;IAE5C,KAAKc,eAAA,GAAkBV,MAAA,CAAOU,eAAA;IAE9B,OAAO;EACR;EAEDmC,MAAA,EAAQ;IACN,OAAO,IAAI,KAAKjE,WAAA,CAAY,KAAKC,WAAW,EAAE8D,IAAA,CAAK,IAAI;EACxD;AACH", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}