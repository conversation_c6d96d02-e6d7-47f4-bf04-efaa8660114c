{"ast": null, "code": "import * as React from 'react';\nimport * as THREE from 'three';\nimport { useThree } from '@react-three/fiber';\nimport { Line } from '../../core/Line.js';\nimport { Html } from '../Html.js';\nimport { context } from './context.js';\nconst clickDir = /* @__PURE__ */new THREE.Vector3();\nconst intersectionDir = /* @__PURE__ */new THREE.Vector3();\nconst toDegrees = radians => radians * 180 / Math.PI;\nconst toRadians = degrees => degrees * Math.PI / 180;\nconst calculateAngle = (clickPoint, intersectionPoint, origin, e1, e2) => {\n  clickDir.copy(clickPoint).sub(origin);\n  intersectionDir.copy(intersectionPoint).sub(origin);\n  const dote1e1 = e1.dot(e1);\n  const dote2e2 = e2.dot(e2);\n  const uClick = clickDir.dot(e1) / dote1e1;\n  const vClick = clickDir.dot(e2) / dote2e2;\n  const uIntersection = intersectionDir.dot(e1) / dote1e1;\n  const vIntersection = intersectionDir.dot(e2) / dote2e2;\n  const angleClick = Math.atan2(vClick, uClick);\n  const angleIntersection = Math.atan2(vIntersection, uIntersection);\n  return angleIntersection - angleClick;\n};\nconst fmod = (num, denom) => {\n  let k = Math.floor(num / denom);\n  k = k < 0 ? k + 1 : k;\n  return num - k * denom;\n};\nconst minimizeAngle = angle => {\n  let result = fmod(angle, 2 * Math.PI);\n  if (Math.abs(result) < 1e-6) {\n    return 0.0;\n  }\n  if (result < 0.0) {\n    result += 2 * Math.PI;\n  }\n  return result;\n};\nconst rotMatrix = /* @__PURE__ */new THREE.Matrix4();\nconst posNew = /* @__PURE__ */new THREE.Vector3();\nconst ray = /* @__PURE__ */new THREE.Ray();\nconst intersection = /* @__PURE__ */new THREE.Vector3();\nconst AxisRotator = ({\n  dir1,\n  dir2,\n  axis\n}) => {\n  const {\n    rotationLimits,\n    annotations,\n    annotationsClass,\n    depthTest,\n    scale,\n    lineWidth,\n    fixed,\n    axisColors,\n    hoveredColor,\n    opacity,\n    onDragStart,\n    onDrag,\n    onDragEnd,\n    userData\n  } = React.useContext(context);\n  const camControls = useThree(state => state.controls);\n  const divRef = React.useRef(null);\n  const objRef = React.useRef(null);\n  const angle0 = React.useRef(0);\n  const angle = React.useRef(0);\n  const clickInfo = React.useRef(null);\n  const [isHovered, setIsHovered] = React.useState(false);\n  const onPointerDown = React.useCallback(e => {\n    if (annotations) {\n      divRef.current.innerText = `${toDegrees(angle.current).toFixed(0)}º`;\n      divRef.current.style.display = 'block';\n    }\n    e.stopPropagation();\n    const clickPoint = e.point.clone();\n    const origin = new THREE.Vector3().setFromMatrixPosition(objRef.current.matrixWorld);\n    const e1 = new THREE.Vector3().setFromMatrixColumn(objRef.current.matrixWorld, 0).normalize();\n    const e2 = new THREE.Vector3().setFromMatrixColumn(objRef.current.matrixWorld, 1).normalize();\n    const normal = new THREE.Vector3().setFromMatrixColumn(objRef.current.matrixWorld, 2).normalize();\n    const plane = new THREE.Plane().setFromNormalAndCoplanarPoint(normal, origin);\n    clickInfo.current = {\n      clickPoint,\n      origin,\n      e1,\n      e2,\n      normal,\n      plane\n    };\n    onDragStart({\n      component: 'Rotator',\n      axis,\n      origin,\n      directions: [e1, e2, normal]\n    });\n    camControls && (camControls.enabled = false);\n    // @ts-ignore\n    e.target.setPointerCapture(e.pointerId);\n  }, [annotations, camControls, onDragStart, axis]);\n  const onPointerMove = React.useCallback(e => {\n    e.stopPropagation();\n    if (!isHovered) setIsHovered(true);\n    if (clickInfo.current) {\n      const {\n        clickPoint,\n        origin,\n        e1,\n        e2,\n        normal,\n        plane\n      } = clickInfo.current;\n      const [min, max] = (rotationLimits == null ? void 0 : rotationLimits[axis]) || [undefined, undefined];\n      ray.copy(e.ray);\n      ray.intersectPlane(plane, intersection);\n      ray.direction.negate();\n      ray.intersectPlane(plane, intersection);\n      let deltaAngle = calculateAngle(clickPoint, intersection, origin, e1, e2);\n      let degrees = toDegrees(deltaAngle);\n\n      // @ts-ignore\n      if (e.shiftKey) {\n        degrees = Math.round(degrees / 10) * 10;\n        deltaAngle = toRadians(degrees);\n      }\n      if (min !== undefined && max !== undefined && max - min < 2 * Math.PI) {\n        deltaAngle = minimizeAngle(deltaAngle);\n        deltaAngle = deltaAngle > Math.PI ? deltaAngle - 2 * Math.PI : deltaAngle;\n        deltaAngle = THREE.MathUtils.clamp(deltaAngle, min - angle0.current, max - angle0.current);\n        angle.current = angle0.current + deltaAngle;\n      } else {\n        angle.current = minimizeAngle(angle0.current + deltaAngle);\n        angle.current = angle.current > Math.PI ? angle.current - 2 * Math.PI : angle.current;\n      }\n      if (annotations) {\n        degrees = toDegrees(angle.current);\n        divRef.current.innerText = `${degrees.toFixed(0)}º`;\n      }\n      rotMatrix.makeRotationAxis(normal, deltaAngle);\n      posNew.copy(origin).applyMatrix4(rotMatrix).sub(origin).negate();\n      rotMatrix.setPosition(posNew);\n      onDrag(rotMatrix);\n    }\n  }, [annotations, onDrag, isHovered, rotationLimits, axis]);\n  const onPointerUp = React.useCallback(e => {\n    if (annotations) {\n      divRef.current.style.display = 'none';\n    }\n    e.stopPropagation();\n    angle0.current = angle.current;\n    clickInfo.current = null;\n    onDragEnd();\n    camControls && (camControls.enabled = true);\n    // @ts-ignore\n    e.target.releasePointerCapture(e.pointerId);\n  }, [annotations, camControls, onDragEnd]);\n  const onPointerOut = React.useCallback(e => {\n    e.stopPropagation();\n    setIsHovered(false);\n  }, []);\n  const matrixL = React.useMemo(() => {\n    const dir1N = dir1.clone().normalize();\n    const dir2N = dir2.clone().normalize();\n    return new THREE.Matrix4().makeBasis(dir1N, dir2N, dir1N.clone().cross(dir2N));\n  }, [dir1, dir2]);\n  const r = fixed ? 0.65 : scale * 0.65;\n  const arc = React.useMemo(() => {\n    const segments = 32;\n    const points = [];\n    for (let j = 0; j <= segments; j++) {\n      const angle = j * (Math.PI / 2) / segments;\n      points.push(new THREE.Vector3(Math.cos(angle) * r, Math.sin(angle) * r, 0));\n    }\n    return points;\n  }, [r]);\n  return /*#__PURE__*/React.createElement(\"group\", {\n    ref: objRef,\n    onPointerDown: onPointerDown,\n    onPointerMove: onPointerMove,\n    onPointerUp: onPointerUp,\n    onPointerOut: onPointerOut,\n    matrix: matrixL,\n    matrixAutoUpdate: false\n  }, annotations && /*#__PURE__*/React.createElement(Html, {\n    position: [r, r, 0]\n  }, /*#__PURE__*/React.createElement(\"div\", {\n    style: {\n      display: 'none',\n      background: '#151520',\n      color: 'white',\n      padding: '6px 8px',\n      borderRadius: 7,\n      whiteSpace: 'nowrap'\n    },\n    className: annotationsClass,\n    ref: divRef\n  })), /*#__PURE__*/React.createElement(Line, {\n    points: arc,\n    lineWidth: lineWidth * 4,\n    visible: false,\n    userData: userData\n  }), /*#__PURE__*/React.createElement(Line, {\n    transparent: true,\n    raycast: () => null,\n    depthTest: depthTest,\n    points: arc,\n    lineWidth: lineWidth,\n    side: THREE.DoubleSide,\n    color: isHovered ? hoveredColor : axisColors[axis],\n    opacity: opacity,\n    polygonOffset: true,\n    polygonOffsetFactor: -10,\n    fog: false\n  }));\n};\nexport { AxisRotator };", "map": {"version": 3, "names": ["React", "THREE", "useThree", "Line", "Html", "context", "clickDir", "Vector3", "intersectionDir", "toDegrees", "radians", "Math", "PI", "toRadians", "degrees", "calculateAngle", "clickPoint", "intersectionPoint", "origin", "e1", "e2", "copy", "sub", "dote1e1", "dot", "dote2e2", "uClick", "vClick", "uIntersection", "vIntersection", "angleClick", "atan2", "angleIntersection", "fmod", "num", "denom", "k", "floor", "minimizeAngle", "angle", "result", "abs", "rotMatrix", "Matrix4", "posNew", "ray", "<PERSON>", "intersection", "AxisRotator", "dir1", "dir2", "axis", "rotationLimits", "annotations", "annotationsClass", "depthTest", "scale", "lineWidth", "fixed", "axisColors", "hoveredColor", "opacity", "onDragStart", "onDrag", "onDragEnd", "userData", "useContext", "camControls", "state", "controls", "divRef", "useRef", "objRef", "angle0", "clickInfo", "isHovered", "setIsHovered", "useState", "onPointerDown", "useCallback", "e", "current", "innerText", "toFixed", "style", "display", "stopPropagation", "point", "clone", "setFromMatrixPosition", "matrixWorld", "setFromMatrixColumn", "normalize", "normal", "plane", "Plane", "setFromNormalAndCoplanarPoint", "component", "directions", "enabled", "target", "setPointerCapture", "pointerId", "onPointerMove", "min", "max", "undefined", "intersectPlane", "direction", "negate", "deltaAngle", "shift<PERSON>ey", "round", "MathUtils", "clamp", "makeRotationAxis", "applyMatrix4", "setPosition", "onPointerUp", "releasePointerCapture", "onPointerOut", "matrixL", "useMemo", "dir1N", "dir2N", "makeBasis", "cross", "r", "arc", "segments", "points", "j", "push", "cos", "sin", "createElement", "ref", "matrix", "matrixAutoUpdate", "position", "background", "color", "padding", "borderRadius", "whiteSpace", "className", "visible", "transparent", "raycast", "side", "DoubleSide", "polygonOffset", "polygonOffsetFactor", "fog"], "sources": ["C:/Users/<USER>/Documents/Apps_Creations/SiahMan_Racing4Speed_WebGame/siah-man-racing/node_modules/@react-three/drei/web/pivotControls/AxisRotator.js"], "sourcesContent": ["import * as React from 'react';\nimport * as THREE from 'three';\nimport { useThree } from '@react-three/fiber';\nimport { Line } from '../../core/Line.js';\nimport { Html } from '../Html.js';\nimport { context } from './context.js';\n\nconst clickDir = /* @__PURE__ */new THREE.Vector3();\nconst intersectionDir = /* @__PURE__ */new THREE.Vector3();\nconst toDegrees = radians => radians * 180 / Math.PI;\nconst toRadians = degrees => degrees * Math.PI / 180;\nconst calculateAngle = (clickPoint, intersectionPoint, origin, e1, e2) => {\n  clickDir.copy(clickPoint).sub(origin);\n  intersectionDir.copy(intersectionPoint).sub(origin);\n  const dote1e1 = e1.dot(e1);\n  const dote2e2 = e2.dot(e2);\n  const uClick = clickDir.dot(e1) / dote1e1;\n  const vClick = clickDir.dot(e2) / dote2e2;\n  const uIntersection = intersectionDir.dot(e1) / dote1e1;\n  const vIntersection = intersectionDir.dot(e2) / dote2e2;\n  const angleClick = Math.atan2(vClick, uClick);\n  const angleIntersection = Math.atan2(vIntersection, uIntersection);\n  return angleIntersection - angleClick;\n};\nconst fmod = (num, denom) => {\n  let k = Math.floor(num / denom);\n  k = k < 0 ? k + 1 : k;\n  return num - k * denom;\n};\nconst minimizeAngle = angle => {\n  let result = fmod(angle, 2 * Math.PI);\n  if (Math.abs(result) < 1e-6) {\n    return 0.0;\n  }\n  if (result < 0.0) {\n    result += 2 * Math.PI;\n  }\n  return result;\n};\nconst rotMatrix = /* @__PURE__ */new THREE.Matrix4();\nconst posNew = /* @__PURE__ */new THREE.Vector3();\nconst ray = /* @__PURE__ */new THREE.Ray();\nconst intersection = /* @__PURE__ */new THREE.Vector3();\nconst AxisRotator = ({\n  dir1,\n  dir2,\n  axis\n}) => {\n  const {\n    rotationLimits,\n    annotations,\n    annotationsClass,\n    depthTest,\n    scale,\n    lineWidth,\n    fixed,\n    axisColors,\n    hoveredColor,\n    opacity,\n    onDragStart,\n    onDrag,\n    onDragEnd,\n    userData\n  } = React.useContext(context);\n  const camControls = useThree(state => state.controls);\n  const divRef = React.useRef(null);\n  const objRef = React.useRef(null);\n  const angle0 = React.useRef(0);\n  const angle = React.useRef(0);\n  const clickInfo = React.useRef(null);\n  const [isHovered, setIsHovered] = React.useState(false);\n  const onPointerDown = React.useCallback(e => {\n    if (annotations) {\n      divRef.current.innerText = `${toDegrees(angle.current).toFixed(0)}º`;\n      divRef.current.style.display = 'block';\n    }\n    e.stopPropagation();\n    const clickPoint = e.point.clone();\n    const origin = new THREE.Vector3().setFromMatrixPosition(objRef.current.matrixWorld);\n    const e1 = new THREE.Vector3().setFromMatrixColumn(objRef.current.matrixWorld, 0).normalize();\n    const e2 = new THREE.Vector3().setFromMatrixColumn(objRef.current.matrixWorld, 1).normalize();\n    const normal = new THREE.Vector3().setFromMatrixColumn(objRef.current.matrixWorld, 2).normalize();\n    const plane = new THREE.Plane().setFromNormalAndCoplanarPoint(normal, origin);\n    clickInfo.current = {\n      clickPoint,\n      origin,\n      e1,\n      e2,\n      normal,\n      plane\n    };\n    onDragStart({\n      component: 'Rotator',\n      axis,\n      origin,\n      directions: [e1, e2, normal]\n    });\n    camControls && (camControls.enabled = false);\n    // @ts-ignore\n    e.target.setPointerCapture(e.pointerId);\n  }, [annotations, camControls, onDragStart, axis]);\n  const onPointerMove = React.useCallback(e => {\n    e.stopPropagation();\n    if (!isHovered) setIsHovered(true);\n    if (clickInfo.current) {\n      const {\n        clickPoint,\n        origin,\n        e1,\n        e2,\n        normal,\n        plane\n      } = clickInfo.current;\n      const [min, max] = (rotationLimits == null ? void 0 : rotationLimits[axis]) || [undefined, undefined];\n      ray.copy(e.ray);\n      ray.intersectPlane(plane, intersection);\n      ray.direction.negate();\n      ray.intersectPlane(plane, intersection);\n      let deltaAngle = calculateAngle(clickPoint, intersection, origin, e1, e2);\n      let degrees = toDegrees(deltaAngle);\n\n      // @ts-ignore\n      if (e.shiftKey) {\n        degrees = Math.round(degrees / 10) * 10;\n        deltaAngle = toRadians(degrees);\n      }\n      if (min !== undefined && max !== undefined && max - min < 2 * Math.PI) {\n        deltaAngle = minimizeAngle(deltaAngle);\n        deltaAngle = deltaAngle > Math.PI ? deltaAngle - 2 * Math.PI : deltaAngle;\n        deltaAngle = THREE.MathUtils.clamp(deltaAngle, min - angle0.current, max - angle0.current);\n        angle.current = angle0.current + deltaAngle;\n      } else {\n        angle.current = minimizeAngle(angle0.current + deltaAngle);\n        angle.current = angle.current > Math.PI ? angle.current - 2 * Math.PI : angle.current;\n      }\n      if (annotations) {\n        degrees = toDegrees(angle.current);\n        divRef.current.innerText = `${degrees.toFixed(0)}º`;\n      }\n      rotMatrix.makeRotationAxis(normal, deltaAngle);\n      posNew.copy(origin).applyMatrix4(rotMatrix).sub(origin).negate();\n      rotMatrix.setPosition(posNew);\n      onDrag(rotMatrix);\n    }\n  }, [annotations, onDrag, isHovered, rotationLimits, axis]);\n  const onPointerUp = React.useCallback(e => {\n    if (annotations) {\n      divRef.current.style.display = 'none';\n    }\n    e.stopPropagation();\n    angle0.current = angle.current;\n    clickInfo.current = null;\n    onDragEnd();\n    camControls && (camControls.enabled = true);\n    // @ts-ignore\n    e.target.releasePointerCapture(e.pointerId);\n  }, [annotations, camControls, onDragEnd]);\n  const onPointerOut = React.useCallback(e => {\n    e.stopPropagation();\n    setIsHovered(false);\n  }, []);\n  const matrixL = React.useMemo(() => {\n    const dir1N = dir1.clone().normalize();\n    const dir2N = dir2.clone().normalize();\n    return new THREE.Matrix4().makeBasis(dir1N, dir2N, dir1N.clone().cross(dir2N));\n  }, [dir1, dir2]);\n  const r = fixed ? 0.65 : scale * 0.65;\n  const arc = React.useMemo(() => {\n    const segments = 32;\n    const points = [];\n    for (let j = 0; j <= segments; j++) {\n      const angle = j * (Math.PI / 2) / segments;\n      points.push(new THREE.Vector3(Math.cos(angle) * r, Math.sin(angle) * r, 0));\n    }\n    return points;\n  }, [r]);\n  return /*#__PURE__*/React.createElement(\"group\", {\n    ref: objRef,\n    onPointerDown: onPointerDown,\n    onPointerMove: onPointerMove,\n    onPointerUp: onPointerUp,\n    onPointerOut: onPointerOut,\n    matrix: matrixL,\n    matrixAutoUpdate: false\n  }, annotations && /*#__PURE__*/React.createElement(Html, {\n    position: [r, r, 0]\n  }, /*#__PURE__*/React.createElement(\"div\", {\n    style: {\n      display: 'none',\n      background: '#151520',\n      color: 'white',\n      padding: '6px 8px',\n      borderRadius: 7,\n      whiteSpace: 'nowrap'\n    },\n    className: annotationsClass,\n    ref: divRef\n  })), /*#__PURE__*/React.createElement(Line, {\n    points: arc,\n    lineWidth: lineWidth * 4,\n    visible: false,\n    userData: userData\n  }), /*#__PURE__*/React.createElement(Line, {\n    transparent: true,\n    raycast: () => null,\n    depthTest: depthTest,\n    points: arc,\n    lineWidth: lineWidth,\n    side: THREE.DoubleSide,\n    color: isHovered ? hoveredColor : axisColors[axis],\n    opacity: opacity,\n    polygonOffset: true,\n    polygonOffsetFactor: -10,\n    fog: false\n  }));\n};\n\nexport { AxisRotator };\n"], "mappings": "AAAA,OAAO,KAAKA,KAAK,MAAM,OAAO;AAC9B,OAAO,KAAKC,KAAK,MAAM,OAAO;AAC9B,SAASC,QAAQ,QAAQ,oBAAoB;AAC7C,SAASC,IAAI,QAAQ,oBAAoB;AACzC,SAASC,IAAI,QAAQ,YAAY;AACjC,SAASC,OAAO,QAAQ,cAAc;AAEtC,MAAMC,QAAQ,GAAG,eAAe,IAAIL,KAAK,CAACM,OAAO,CAAC,CAAC;AACnD,MAAMC,eAAe,GAAG,eAAe,IAAIP,KAAK,CAACM,OAAO,CAAC,CAAC;AAC1D,MAAME,SAAS,GAAGC,OAAO,IAAIA,OAAO,GAAG,GAAG,GAAGC,IAAI,CAACC,EAAE;AACpD,MAAMC,SAAS,GAAGC,OAAO,IAAIA,OAAO,GAAGH,IAAI,CAACC,EAAE,GAAG,GAAG;AACpD,MAAMG,cAAc,GAAGA,CAACC,UAAU,EAAEC,iBAAiB,EAAEC,MAAM,EAAEC,EAAE,EAAEC,EAAE,KAAK;EACxEd,QAAQ,CAACe,IAAI,CAACL,UAAU,CAAC,CAACM,GAAG,CAACJ,MAAM,CAAC;EACrCV,eAAe,CAACa,IAAI,CAACJ,iBAAiB,CAAC,CAACK,GAAG,CAACJ,MAAM,CAAC;EACnD,MAAMK,OAAO,GAAGJ,EAAE,CAACK,GAAG,CAACL,EAAE,CAAC;EAC1B,MAAMM,OAAO,GAAGL,EAAE,CAACI,GAAG,CAACJ,EAAE,CAAC;EAC1B,MAAMM,MAAM,GAAGpB,QAAQ,CAACkB,GAAG,CAACL,EAAE,CAAC,GAAGI,OAAO;EACzC,MAAMI,MAAM,GAAGrB,QAAQ,CAACkB,GAAG,CAACJ,EAAE,CAAC,GAAGK,OAAO;EACzC,MAAMG,aAAa,GAAGpB,eAAe,CAACgB,GAAG,CAACL,EAAE,CAAC,GAAGI,OAAO;EACvD,MAAMM,aAAa,GAAGrB,eAAe,CAACgB,GAAG,CAACJ,EAAE,CAAC,GAAGK,OAAO;EACvD,MAAMK,UAAU,GAAGnB,IAAI,CAACoB,KAAK,CAACJ,MAAM,EAAED,MAAM,CAAC;EAC7C,MAAMM,iBAAiB,GAAGrB,IAAI,CAACoB,KAAK,CAACF,aAAa,EAAED,aAAa,CAAC;EAClE,OAAOI,iBAAiB,GAAGF,UAAU;AACvC,CAAC;AACD,MAAMG,IAAI,GAAGA,CAACC,GAAG,EAAEC,KAAK,KAAK;EAC3B,IAAIC,CAAC,GAAGzB,IAAI,CAAC0B,KAAK,CAACH,GAAG,GAAGC,KAAK,CAAC;EAC/BC,CAAC,GAAGA,CAAC,GAAG,CAAC,GAAGA,CAAC,GAAG,CAAC,GAAGA,CAAC;EACrB,OAAOF,GAAG,GAAGE,CAAC,GAAGD,KAAK;AACxB,CAAC;AACD,MAAMG,aAAa,GAAGC,KAAK,IAAI;EAC7B,IAAIC,MAAM,GAAGP,IAAI,CAACM,KAAK,EAAE,CAAC,GAAG5B,IAAI,CAACC,EAAE,CAAC;EACrC,IAAID,IAAI,CAAC8B,GAAG,CAACD,MAAM,CAAC,GAAG,IAAI,EAAE;IAC3B,OAAO,GAAG;EACZ;EACA,IAAIA,MAAM,GAAG,GAAG,EAAE;IAChBA,MAAM,IAAI,CAAC,GAAG7B,IAAI,CAACC,EAAE;EACvB;EACA,OAAO4B,MAAM;AACf,CAAC;AACD,MAAME,SAAS,GAAG,eAAe,IAAIzC,KAAK,CAAC0C,OAAO,CAAC,CAAC;AACpD,MAAMC,MAAM,GAAG,eAAe,IAAI3C,KAAK,CAACM,OAAO,CAAC,CAAC;AACjD,MAAMsC,GAAG,GAAG,eAAe,IAAI5C,KAAK,CAAC6C,GAAG,CAAC,CAAC;AAC1C,MAAMC,YAAY,GAAG,eAAe,IAAI9C,KAAK,CAACM,OAAO,CAAC,CAAC;AACvD,MAAMyC,WAAW,GAAGA,CAAC;EACnBC,IAAI;EACJC,IAAI;EACJC;AACF,CAAC,KAAK;EACJ,MAAM;IACJC,cAAc;IACdC,WAAW;IACXC,gBAAgB;IAChBC,SAAS;IACTC,KAAK;IACLC,SAAS;IACTC,KAAK;IACLC,UAAU;IACVC,YAAY;IACZC,OAAO;IACPC,WAAW;IACXC,MAAM;IACNC,SAAS;IACTC;EACF,CAAC,GAAGjE,KAAK,CAACkE,UAAU,CAAC7D,OAAO,CAAC;EAC7B,MAAM8D,WAAW,GAAGjE,QAAQ,CAACkE,KAAK,IAAIA,KAAK,CAACC,QAAQ,CAAC;EACrD,MAAMC,MAAM,GAAGtE,KAAK,CAACuE,MAAM,CAAC,IAAI,CAAC;EACjC,MAAMC,MAAM,GAAGxE,KAAK,CAACuE,MAAM,CAAC,IAAI,CAAC;EACjC,MAAME,MAAM,GAAGzE,KAAK,CAACuE,MAAM,CAAC,CAAC,CAAC;EAC9B,MAAMhC,KAAK,GAAGvC,KAAK,CAACuE,MAAM,CAAC,CAAC,CAAC;EAC7B,MAAMG,SAAS,GAAG1E,KAAK,CAACuE,MAAM,CAAC,IAAI,CAAC;EACpC,MAAM,CAACI,SAAS,EAAEC,YAAY,CAAC,GAAG5E,KAAK,CAAC6E,QAAQ,CAAC,KAAK,CAAC;EACvD,MAAMC,aAAa,GAAG9E,KAAK,CAAC+E,WAAW,CAACC,CAAC,IAAI;IAC3C,IAAI3B,WAAW,EAAE;MACfiB,MAAM,CAACW,OAAO,CAACC,SAAS,GAAG,GAAGzE,SAAS,CAAC8B,KAAK,CAAC0C,OAAO,CAAC,CAACE,OAAO,CAAC,CAAC,CAAC,GAAG;MACpEb,MAAM,CAACW,OAAO,CAACG,KAAK,CAACC,OAAO,GAAG,OAAO;IACxC;IACAL,CAAC,CAACM,eAAe,CAAC,CAAC;IACnB,MAAMtE,UAAU,GAAGgE,CAAC,CAACO,KAAK,CAACC,KAAK,CAAC,CAAC;IAClC,MAAMtE,MAAM,GAAG,IAAIjB,KAAK,CAACM,OAAO,CAAC,CAAC,CAACkF,qBAAqB,CAACjB,MAAM,CAACS,OAAO,CAACS,WAAW,CAAC;IACpF,MAAMvE,EAAE,GAAG,IAAIlB,KAAK,CAACM,OAAO,CAAC,CAAC,CAACoF,mBAAmB,CAACnB,MAAM,CAACS,OAAO,CAACS,WAAW,EAAE,CAAC,CAAC,CAACE,SAAS,CAAC,CAAC;IAC7F,MAAMxE,EAAE,GAAG,IAAInB,KAAK,CAACM,OAAO,CAAC,CAAC,CAACoF,mBAAmB,CAACnB,MAAM,CAACS,OAAO,CAACS,WAAW,EAAE,CAAC,CAAC,CAACE,SAAS,CAAC,CAAC;IAC7F,MAAMC,MAAM,GAAG,IAAI5F,KAAK,CAACM,OAAO,CAAC,CAAC,CAACoF,mBAAmB,CAACnB,MAAM,CAACS,OAAO,CAACS,WAAW,EAAE,CAAC,CAAC,CAACE,SAAS,CAAC,CAAC;IACjG,MAAME,KAAK,GAAG,IAAI7F,KAAK,CAAC8F,KAAK,CAAC,CAAC,CAACC,6BAA6B,CAACH,MAAM,EAAE3E,MAAM,CAAC;IAC7EwD,SAAS,CAACO,OAAO,GAAG;MAClBjE,UAAU;MACVE,MAAM;MACNC,EAAE;MACFC,EAAE;MACFyE,MAAM;MACNC;IACF,CAAC;IACDhC,WAAW,CAAC;MACVmC,SAAS,EAAE,SAAS;MACpB9C,IAAI;MACJjC,MAAM;MACNgF,UAAU,EAAE,CAAC/E,EAAE,EAAEC,EAAE,EAAEyE,MAAM;IAC7B,CAAC,CAAC;IACF1B,WAAW,KAAKA,WAAW,CAACgC,OAAO,GAAG,KAAK,CAAC;IAC5C;IACAnB,CAAC,CAACoB,MAAM,CAACC,iBAAiB,CAACrB,CAAC,CAACsB,SAAS,CAAC;EACzC,CAAC,EAAE,CAACjD,WAAW,EAAEc,WAAW,EAAEL,WAAW,EAAEX,IAAI,CAAC,CAAC;EACjD,MAAMoD,aAAa,GAAGvG,KAAK,CAAC+E,WAAW,CAACC,CAAC,IAAI;IAC3CA,CAAC,CAACM,eAAe,CAAC,CAAC;IACnB,IAAI,CAACX,SAAS,EAAEC,YAAY,CAAC,IAAI,CAAC;IAClC,IAAIF,SAAS,CAACO,OAAO,EAAE;MACrB,MAAM;QACJjE,UAAU;QACVE,MAAM;QACNC,EAAE;QACFC,EAAE;QACFyE,MAAM;QACNC;MACF,CAAC,GAAGpB,SAAS,CAACO,OAAO;MACrB,MAAM,CAACuB,GAAG,EAAEC,GAAG,CAAC,GAAG,CAACrD,cAAc,IAAI,IAAI,GAAG,KAAK,CAAC,GAAGA,cAAc,CAACD,IAAI,CAAC,KAAK,CAACuD,SAAS,EAAEA,SAAS,CAAC;MACrG7D,GAAG,CAACxB,IAAI,CAAC2D,CAAC,CAACnC,GAAG,CAAC;MACfA,GAAG,CAAC8D,cAAc,CAACb,KAAK,EAAE/C,YAAY,CAAC;MACvCF,GAAG,CAAC+D,SAAS,CAACC,MAAM,CAAC,CAAC;MACtBhE,GAAG,CAAC8D,cAAc,CAACb,KAAK,EAAE/C,YAAY,CAAC;MACvC,IAAI+D,UAAU,GAAG/F,cAAc,CAACC,UAAU,EAAE+B,YAAY,EAAE7B,MAAM,EAAEC,EAAE,EAAEC,EAAE,CAAC;MACzE,IAAIN,OAAO,GAAGL,SAAS,CAACqG,UAAU,CAAC;;MAEnC;MACA,IAAI9B,CAAC,CAAC+B,QAAQ,EAAE;QACdjG,OAAO,GAAGH,IAAI,CAACqG,KAAK,CAAClG,OAAO,GAAG,EAAE,CAAC,GAAG,EAAE;QACvCgG,UAAU,GAAGjG,SAAS,CAACC,OAAO,CAAC;MACjC;MACA,IAAI0F,GAAG,KAAKE,SAAS,IAAID,GAAG,KAAKC,SAAS,IAAID,GAAG,GAAGD,GAAG,GAAG,CAAC,GAAG7F,IAAI,CAACC,EAAE,EAAE;QACrEkG,UAAU,GAAGxE,aAAa,CAACwE,UAAU,CAAC;QACtCA,UAAU,GAAGA,UAAU,GAAGnG,IAAI,CAACC,EAAE,GAAGkG,UAAU,GAAG,CAAC,GAAGnG,IAAI,CAACC,EAAE,GAAGkG,UAAU;QACzEA,UAAU,GAAG7G,KAAK,CAACgH,SAAS,CAACC,KAAK,CAACJ,UAAU,EAAEN,GAAG,GAAG/B,MAAM,CAACQ,OAAO,EAAEwB,GAAG,GAAGhC,MAAM,CAACQ,OAAO,CAAC;QAC1F1C,KAAK,CAAC0C,OAAO,GAAGR,MAAM,CAACQ,OAAO,GAAG6B,UAAU;MAC7C,CAAC,MAAM;QACLvE,KAAK,CAAC0C,OAAO,GAAG3C,aAAa,CAACmC,MAAM,CAACQ,OAAO,GAAG6B,UAAU,CAAC;QAC1DvE,KAAK,CAAC0C,OAAO,GAAG1C,KAAK,CAAC0C,OAAO,GAAGtE,IAAI,CAACC,EAAE,GAAG2B,KAAK,CAAC0C,OAAO,GAAG,CAAC,GAAGtE,IAAI,CAACC,EAAE,GAAG2B,KAAK,CAAC0C,OAAO;MACvF;MACA,IAAI5B,WAAW,EAAE;QACfvC,OAAO,GAAGL,SAAS,CAAC8B,KAAK,CAAC0C,OAAO,CAAC;QAClCX,MAAM,CAACW,OAAO,CAACC,SAAS,GAAG,GAAGpE,OAAO,CAACqE,OAAO,CAAC,CAAC,CAAC,GAAG;MACrD;MACAzC,SAAS,CAACyE,gBAAgB,CAACtB,MAAM,EAAEiB,UAAU,CAAC;MAC9ClE,MAAM,CAACvB,IAAI,CAACH,MAAM,CAAC,CAACkG,YAAY,CAAC1E,SAAS,CAAC,CAACpB,GAAG,CAACJ,MAAM,CAAC,CAAC2F,MAAM,CAAC,CAAC;MAChEnE,SAAS,CAAC2E,WAAW,CAACzE,MAAM,CAAC;MAC7BmB,MAAM,CAACrB,SAAS,CAAC;IACnB;EACF,CAAC,EAAE,CAACW,WAAW,EAAEU,MAAM,EAAEY,SAAS,EAAEvB,cAAc,EAAED,IAAI,CAAC,CAAC;EAC1D,MAAMmE,WAAW,GAAGtH,KAAK,CAAC+E,WAAW,CAACC,CAAC,IAAI;IACzC,IAAI3B,WAAW,EAAE;MACfiB,MAAM,CAACW,OAAO,CAACG,KAAK,CAACC,OAAO,GAAG,MAAM;IACvC;IACAL,CAAC,CAACM,eAAe,CAAC,CAAC;IACnBb,MAAM,CAACQ,OAAO,GAAG1C,KAAK,CAAC0C,OAAO;IAC9BP,SAAS,CAACO,OAAO,GAAG,IAAI;IACxBjB,SAAS,CAAC,CAAC;IACXG,WAAW,KAAKA,WAAW,CAACgC,OAAO,GAAG,IAAI,CAAC;IAC3C;IACAnB,CAAC,CAACoB,MAAM,CAACmB,qBAAqB,CAACvC,CAAC,CAACsB,SAAS,CAAC;EAC7C,CAAC,EAAE,CAACjD,WAAW,EAAEc,WAAW,EAAEH,SAAS,CAAC,CAAC;EACzC,MAAMwD,YAAY,GAAGxH,KAAK,CAAC+E,WAAW,CAACC,CAAC,IAAI;IAC1CA,CAAC,CAACM,eAAe,CAAC,CAAC;IACnBV,YAAY,CAAC,KAAK,CAAC;EACrB,CAAC,EAAE,EAAE,CAAC;EACN,MAAM6C,OAAO,GAAGzH,KAAK,CAAC0H,OAAO,CAAC,MAAM;IAClC,MAAMC,KAAK,GAAG1E,IAAI,CAACuC,KAAK,CAAC,CAAC,CAACI,SAAS,CAAC,CAAC;IACtC,MAAMgC,KAAK,GAAG1E,IAAI,CAACsC,KAAK,CAAC,CAAC,CAACI,SAAS,CAAC,CAAC;IACtC,OAAO,IAAI3F,KAAK,CAAC0C,OAAO,CAAC,CAAC,CAACkF,SAAS,CAACF,KAAK,EAAEC,KAAK,EAAED,KAAK,CAACnC,KAAK,CAAC,CAAC,CAACsC,KAAK,CAACF,KAAK,CAAC,CAAC;EAChF,CAAC,EAAE,CAAC3E,IAAI,EAAEC,IAAI,CAAC,CAAC;EAChB,MAAM6E,CAAC,GAAGrE,KAAK,GAAG,IAAI,GAAGF,KAAK,GAAG,IAAI;EACrC,MAAMwE,GAAG,GAAGhI,KAAK,CAAC0H,OAAO,CAAC,MAAM;IAC9B,MAAMO,QAAQ,GAAG,EAAE;IACnB,MAAMC,MAAM,GAAG,EAAE;IACjB,KAAK,IAAIC,CAAC,GAAG,CAAC,EAAEA,CAAC,IAAIF,QAAQ,EAAEE,CAAC,EAAE,EAAE;MAClC,MAAM5F,KAAK,GAAG4F,CAAC,IAAIxH,IAAI,CAACC,EAAE,GAAG,CAAC,CAAC,GAAGqH,QAAQ;MAC1CC,MAAM,CAACE,IAAI,CAAC,IAAInI,KAAK,CAACM,OAAO,CAACI,IAAI,CAAC0H,GAAG,CAAC9F,KAAK,CAAC,GAAGwF,CAAC,EAAEpH,IAAI,CAAC2H,GAAG,CAAC/F,KAAK,CAAC,GAAGwF,CAAC,EAAE,CAAC,CAAC,CAAC;IAC7E;IACA,OAAOG,MAAM;EACf,CAAC,EAAE,CAACH,CAAC,CAAC,CAAC;EACP,OAAO,aAAa/H,KAAK,CAACuI,aAAa,CAAC,OAAO,EAAE;IAC/CC,GAAG,EAAEhE,MAAM;IACXM,aAAa,EAAEA,aAAa;IAC5ByB,aAAa,EAAEA,aAAa;IAC5Be,WAAW,EAAEA,WAAW;IACxBE,YAAY,EAAEA,YAAY;IAC1BiB,MAAM,EAAEhB,OAAO;IACfiB,gBAAgB,EAAE;EACpB,CAAC,EAAErF,WAAW,IAAI,aAAarD,KAAK,CAACuI,aAAa,CAACnI,IAAI,EAAE;IACvDuI,QAAQ,EAAE,CAACZ,CAAC,EAAEA,CAAC,EAAE,CAAC;EACpB,CAAC,EAAE,aAAa/H,KAAK,CAACuI,aAAa,CAAC,KAAK,EAAE;IACzCnD,KAAK,EAAE;MACLC,OAAO,EAAE,MAAM;MACfuD,UAAU,EAAE,SAAS;MACrBC,KAAK,EAAE,OAAO;MACdC,OAAO,EAAE,SAAS;MAClBC,YAAY,EAAE,CAAC;MACfC,UAAU,EAAE;IACd,CAAC;IACDC,SAAS,EAAE3F,gBAAgB;IAC3BkF,GAAG,EAAElE;EACP,CAAC,CAAC,CAAC,EAAE,aAAatE,KAAK,CAACuI,aAAa,CAACpI,IAAI,EAAE;IAC1C+H,MAAM,EAAEF,GAAG;IACXvE,SAAS,EAAEA,SAAS,GAAG,CAAC;IACxByF,OAAO,EAAE,KAAK;IACdjF,QAAQ,EAAEA;EACZ,CAAC,CAAC,EAAE,aAAajE,KAAK,CAACuI,aAAa,CAACpI,IAAI,EAAE;IACzCgJ,WAAW,EAAE,IAAI;IACjBC,OAAO,EAAEA,CAAA,KAAM,IAAI;IACnB7F,SAAS,EAAEA,SAAS;IACpB2E,MAAM,EAAEF,GAAG;IACXvE,SAAS,EAAEA,SAAS;IACpB4F,IAAI,EAAEpJ,KAAK,CAACqJ,UAAU;IACtBT,KAAK,EAAElE,SAAS,GAAGf,YAAY,GAAGD,UAAU,CAACR,IAAI,CAAC;IAClDU,OAAO,EAAEA,OAAO;IAChB0F,aAAa,EAAE,IAAI;IACnBC,mBAAmB,EAAE,CAAC,EAAE;IACxBC,GAAG,EAAE;EACP,CAAC,CAAC,CAAC;AACL,CAAC;AAED,SAASzG,WAAW", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}