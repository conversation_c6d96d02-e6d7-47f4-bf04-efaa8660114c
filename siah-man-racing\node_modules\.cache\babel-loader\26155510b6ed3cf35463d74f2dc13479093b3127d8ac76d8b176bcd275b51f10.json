{"ast": null, "code": "import { useEffect } from 'react';\nimport { useThree } from '@react-three/fiber';\nfunction BakeShadows() {\n  const gl = useThree(state => state.gl);\n  useEffect(() => {\n    gl.shadowMap.autoUpdate = false;\n    gl.shadowMap.needsUpdate = true;\n    return () => {\n      gl.shadowMap.autoUpdate = gl.shadowMap.needsUpdate = true;\n    };\n  }, [gl.shadowMap]);\n  return null;\n}\nexport { BakeShadows };", "map": {"version": 3, "names": ["useEffect", "useThree", "BakeShadows", "gl", "state", "shadowMap", "autoUpdate", "needsUpdate"], "sources": ["C:/Users/<USER>/Documents/Apps_Creations/SiahMan_Racing4Speed_WebGame/siah-man-racing/node_modules/@react-three/drei/core/BakeShadows.js"], "sourcesContent": ["import { useEffect } from 'react';\nimport { useThree } from '@react-three/fiber';\n\nfunction BakeShadows() {\n  const gl = useThree(state => state.gl);\n  useEffect(() => {\n    gl.shadowMap.autoUpdate = false;\n    gl.shadowMap.needsUpdate = true;\n    return () => {\n      gl.shadowMap.autoUpdate = gl.shadowMap.needsUpdate = true;\n    };\n  }, [gl.shadowMap]);\n  return null;\n}\n\nexport { BakeShadows };\n"], "mappings": "AAAA,SAASA,SAAS,QAAQ,OAAO;AACjC,SAASC,QAAQ,QAAQ,oBAAoB;AAE7C,SAASC,WAAWA,CAAA,EAAG;EACrB,MAAMC,EAAE,GAAGF,QAAQ,CAACG,KAAK,IAAIA,KAAK,CAACD,EAAE,CAAC;EACtCH,SAAS,CAAC,MAAM;IACdG,EAAE,CAACE,SAAS,CAACC,UAAU,GAAG,KAAK;IAC/BH,EAAE,CAACE,SAAS,CAACE,WAAW,GAAG,IAAI;IAC/B,OAAO,MAAM;MACXJ,EAAE,CAACE,SAAS,CAACC,UAAU,GAAGH,EAAE,CAACE,SAAS,CAACE,WAAW,GAAG,IAAI;IAC3D,CAAC;EACH,CAAC,EAAE,CAACJ,EAAE,CAACE,SAAS,CAAC,CAAC;EAClB,OAAO,IAAI;AACb;AAEA,SAASH,WAAW", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}