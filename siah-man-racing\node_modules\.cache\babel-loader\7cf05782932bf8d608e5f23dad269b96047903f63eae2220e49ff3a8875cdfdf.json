{"ast": null, "code": "import { Ray, <PERSON>4, <PERSON>sh, Vector3, <PERSON>phere, BatchedMesh, REVISION } from 'three';\nimport { convertRaycastIntersect } from './GeometryRayIntersectUtilities.js';\nimport { MeshBVH } from '../core/MeshBVH.js';\nconst IS_REVISION_166 = parseInt(REVISION) >= 166;\nconst ray = /* @__PURE__ */new Ray();\nconst direction = /* @__PURE__ */new Vector3();\nconst tmpInverseMatrix = /* @__PURE__ */new Matrix4();\nconst origMeshRaycastFunc = Mesh.prototype.raycast;\nconst origBatchedRaycastFunc = BatchedMesh.prototype.raycast;\nconst _worldScale = /* @__PURE__ */new Vector3();\nconst _mesh = /* @__PURE__ */new Mesh();\nconst _batchIntersects = [];\nexport function acceleratedRaycast(raycaster, intersects) {\n  if (this.isBatchedMesh) {\n    acceleratedBatchedMeshRaycast.call(this, raycaster, intersects);\n  } else {\n    acceleratedMeshRaycast.call(this, raycaster, intersects);\n  }\n}\nfunction acceleratedBatchedMeshRaycast(raycaster, intersects) {\n  if (this.boundsTrees) {\n    // TODO: remove use of geometry info, instance info when r170 is minimum version\n    const boundsTrees = this.boundsTrees;\n    const drawInfo = this._drawInfo || this._instanceInfo;\n    const drawRanges = this._drawRanges || this._geometryInfo;\n    const matrixWorld = this.matrixWorld;\n    _mesh.material = this.material;\n    _mesh.geometry = this.geometry;\n    const oldBoundsTree = _mesh.geometry.boundsTree;\n    const oldDrawRange = _mesh.geometry.drawRange;\n    if (_mesh.geometry.boundingSphere === null) {\n      _mesh.geometry.boundingSphere = new Sphere();\n    }\n\n    // TODO: provide new method to get instances count instead of 'drawInfo.length'\n    for (let i = 0, l = drawInfo.length; i < l; i++) {\n      if (!this.getVisibleAt(i)) {\n        continue;\n      }\n\n      // TODO: use getGeometryIndex\n      const geometryId = drawInfo[i].geometryIndex;\n      _mesh.geometry.boundsTree = boundsTrees[geometryId];\n      this.getMatrixAt(i, _mesh.matrixWorld).premultiply(matrixWorld);\n      if (!_mesh.geometry.boundsTree) {\n        this.getBoundingBoxAt(geometryId, _mesh.geometry.boundingBox);\n        this.getBoundingSphereAt(geometryId, _mesh.geometry.boundingSphere);\n        const drawRange = drawRanges[geometryId];\n        _mesh.geometry.setDrawRange(drawRange.start, drawRange.count);\n      }\n      _mesh.raycast(raycaster, _batchIntersects);\n      for (let j = 0, l = _batchIntersects.length; j < l; j++) {\n        const intersect = _batchIntersects[j];\n        intersect.object = this;\n        intersect.batchId = i;\n        intersects.push(intersect);\n      }\n      _batchIntersects.length = 0;\n    }\n    _mesh.geometry.boundsTree = oldBoundsTree;\n    _mesh.geometry.drawRange = oldDrawRange;\n    _mesh.material = null;\n    _mesh.geometry = null;\n  } else {\n    origBatchedRaycastFunc.call(this, raycaster, intersects);\n  }\n}\nfunction acceleratedMeshRaycast(raycaster, intersects) {\n  if (this.geometry.boundsTree) {\n    if (this.material === undefined) return;\n    tmpInverseMatrix.copy(this.matrixWorld).invert();\n    ray.copy(raycaster.ray).applyMatrix4(tmpInverseMatrix);\n    _worldScale.setFromMatrixScale(this.matrixWorld);\n    direction.copy(ray.direction).multiply(_worldScale);\n    const scaleFactor = direction.length();\n    const near = raycaster.near / scaleFactor;\n    const far = raycaster.far / scaleFactor;\n    const bvh = this.geometry.boundsTree;\n    if (raycaster.firstHitOnly === true) {\n      const hit = convertRaycastIntersect(bvh.raycastFirst(ray, this.material, near, far), this, raycaster);\n      if (hit) {\n        intersects.push(hit);\n      }\n    } else {\n      const hits = bvh.raycast(ray, this.material, near, far);\n      for (let i = 0, l = hits.length; i < l; i++) {\n        const hit = convertRaycastIntersect(hits[i], this, raycaster);\n        if (hit) {\n          intersects.push(hit);\n        }\n      }\n    }\n  } else {\n    origMeshRaycastFunc.call(this, raycaster, intersects);\n  }\n}\nexport function computeBoundsTree(options = {}) {\n  this.boundsTree = new MeshBVH(this, options);\n  return this.boundsTree;\n}\nexport function disposeBoundsTree() {\n  this.boundsTree = null;\n}\nexport function computeBatchedBoundsTree(index = -1, options = {}) {\n  if (!IS_REVISION_166) {\n    throw new Error('BatchedMesh: Three r166+ is required to compute bounds trees.');\n  }\n  if (options.indirect) {\n    console.warn('\"Indirect\" is set to false because it is not supported for BatchedMesh.');\n  }\n  options = {\n    ...options,\n    indirect: false,\n    range: null\n  };\n  const drawRanges = this._drawRanges || this._geometryInfo;\n  const geometryCount = this._geometryCount;\n  if (!this.boundsTrees) {\n    this.boundsTrees = new Array(geometryCount).fill(null);\n  }\n  const boundsTrees = this.boundsTrees;\n  while (boundsTrees.length < geometryCount) {\n    boundsTrees.push(null);\n  }\n  if (index < 0) {\n    for (let i = 0; i < geometryCount; i++) {\n      options.range = drawRanges[i];\n      boundsTrees[i] = new MeshBVH(this.geometry, options);\n    }\n    return boundsTrees;\n  } else {\n    if (index < drawRanges.length) {\n      options.range = drawRanges[index];\n      boundsTrees[index] = new MeshBVH(this.geometry, options);\n    }\n    return boundsTrees[index] || null;\n  }\n}\nexport function disposeBatchedBoundsTree(index = -1) {\n  if (index < 0) {\n    this.boundsTrees.fill(null);\n  } else {\n    if (index < this.boundsTree.length) {\n      this.boundsTrees[index] = null;\n    }\n  }\n}", "map": {"version": 3, "names": ["<PERSON>", "Matrix4", "<PERSON><PERSON>", "Vector3", "Sphere", "<PERSON><PERSON><PERSON><PERSON>", "REVISION", "convertRaycastIntersect", "MeshBVH", "IS_REVISION_166", "parseInt", "ray", "direction", "tmpInverseMatrix", "origMeshRaycastFunc", "prototype", "raycast", "origBatchedRaycastFunc", "_worldScale", "_mesh", "_batchIntersects", "acceleratedRaycast", "raycaster", "intersects", "isBatchedMesh", "acceleratedBatchedMeshRaycast", "call", "acceleratedMeshRaycast", "boundsTrees", "drawInfo", "_drawInfo", "_instanceInfo", "drawRanges", "_drawRanges", "_geometryInfo", "matrixWorld", "material", "geometry", "oldBoundsTree", "boundsTree", "oldDrawRange", "drawRange", "boundingSphere", "i", "l", "length", "getVisibleAt", "geometryId", "geometryIndex", "getMatrixAt", "premultiply", "getBoundingBoxAt", "boundingBox", "getBoundingSphereAt", "setDrawRange", "start", "count", "j", "intersect", "object", "batchId", "push", "undefined", "copy", "invert", "applyMatrix4", "setFromMatrixScale", "multiply", "scaleFactor", "near", "far", "bvh", "firstHitOnly", "hit", "raycastFirst", "hits", "computeBoundsTree", "options", "disposeBoundsTree", "computeBatchedBoundsTree", "index", "Error", "indirect", "console", "warn", "range", "geometryCount", "_geometryCount", "Array", "fill", "disposeBatchedBoundsTree"], "sources": ["C:/Users/<USER>/Documents/Apps_Creations/SiahMan_Racing4Speed_WebGame/siah-man-racing/node_modules/three-mesh-bvh/src/utils/ExtensionUtilities.js"], "sourcesContent": ["import { Ray, <PERSON>4, <PERSON>sh, Vector3, <PERSON>phere, BatchedMesh, REVISION } from 'three';\nimport { convertRaycastIntersect } from './GeometryRayIntersectUtilities.js';\nimport { MeshBVH } from '../core/MeshBVH.js';\n\nconst IS_REVISION_166 = parseInt( REVISION ) >= 166;\nconst ray = /* @__PURE__ */ new Ray();\nconst direction = /* @__PURE__ */ new Vector3();\nconst tmpInverseMatrix = /* @__PURE__ */ new Matrix4();\nconst origMeshRaycastFunc = Mesh.prototype.raycast;\nconst origBatchedRaycastFunc = BatchedMesh.prototype.raycast;\nconst _worldScale = /* @__PURE__ */ new Vector3();\nconst _mesh = /* @__PURE__ */ new Mesh();\nconst _batchIntersects = [];\n\nexport function acceleratedRaycast( raycaster, intersects ) {\n\n\tif ( this.isBatchedMesh ) {\n\n\t\tacceleratedBatchedMeshRaycast.call( this, raycaster, intersects );\n\n\t} else {\n\n\t\tacceleratedMeshRaycast.call( this, raycaster, intersects );\n\n\t}\n\n}\n\nfunction acceleratedBatchedMeshRaycast( raycaster, intersects ) {\n\n\tif ( this.boundsTrees ) {\n\n\t\t// TODO: remove use of geometry info, instance info when r170 is minimum version\n\t\tconst boundsTrees = this.boundsTrees;\n\t\tconst drawInfo = this._drawInfo || this._instanceInfo;\n\t\tconst drawRanges = this._drawRanges || this._geometryInfo;\n\t\tconst matrixWorld = this.matrixWorld;\n\n\t\t_mesh.material = this.material;\n\t\t_mesh.geometry = this.geometry;\n\n\t\tconst oldBoundsTree = _mesh.geometry.boundsTree;\n\t\tconst oldDrawRange = _mesh.geometry.drawRange;\n\n\t\tif ( _mesh.geometry.boundingSphere === null ) {\n\n\t\t\t_mesh.geometry.boundingSphere = new Sphere();\n\n\t\t}\n\n\t\t// TODO: provide new method to get instances count instead of 'drawInfo.length'\n\t\tfor ( let i = 0, l = drawInfo.length; i < l; i ++ ) {\n\n\t\t\tif ( ! this.getVisibleAt( i ) ) {\n\n\t\t\t\tcontinue;\n\n\t\t\t}\n\n\t\t\t// TODO: use getGeometryIndex\n\t\t\tconst geometryId = drawInfo[ i ].geometryIndex;\n\n\t\t\t_mesh.geometry.boundsTree = boundsTrees[ geometryId ];\n\n\t\t\tthis.getMatrixAt( i, _mesh.matrixWorld ).premultiply( matrixWorld );\n\n\t\t\tif ( ! _mesh.geometry.boundsTree ) {\n\n\t\t\t\tthis.getBoundingBoxAt( geometryId, _mesh.geometry.boundingBox );\n\t\t\t\tthis.getBoundingSphereAt( geometryId, _mesh.geometry.boundingSphere );\n\n\t\t\t\tconst drawRange = drawRanges[ geometryId ];\n\t\t\t\t_mesh.geometry.setDrawRange( drawRange.start, drawRange.count );\n\n\t\t\t}\n\n\t\t\t_mesh.raycast( raycaster, _batchIntersects );\n\n\t\t\tfor ( let j = 0, l = _batchIntersects.length; j < l; j ++ ) {\n\n\t\t\t\tconst intersect = _batchIntersects[ j ];\n\t\t\t\tintersect.object = this;\n\t\t\t\tintersect.batchId = i;\n\t\t\t\tintersects.push( intersect );\n\n\t\t\t}\n\n\t\t\t_batchIntersects.length = 0;\n\n\t\t}\n\n\t\t_mesh.geometry.boundsTree = oldBoundsTree;\n\t\t_mesh.geometry.drawRange = oldDrawRange;\n\t\t_mesh.material = null;\n\t\t_mesh.geometry = null;\n\n\t} else {\n\n\t\torigBatchedRaycastFunc.call( this, raycaster, intersects );\n\n\t}\n\n}\n\nfunction acceleratedMeshRaycast( raycaster, intersects ) {\n\n\tif ( this.geometry.boundsTree ) {\n\n\t\tif ( this.material === undefined ) return;\n\n\t\ttmpInverseMatrix.copy( this.matrixWorld ).invert();\n\t\tray.copy( raycaster.ray ).applyMatrix4( tmpInverseMatrix );\n\n\t\t_worldScale.setFromMatrixScale( this.matrixWorld );\n\t\tdirection.copy( ray.direction ).multiply( _worldScale );\n\n\t\tconst scaleFactor = direction.length();\n\t\tconst near = raycaster.near / scaleFactor;\n\t\tconst far = raycaster.far / scaleFactor;\n\n\t\tconst bvh = this.geometry.boundsTree;\n\t\tif ( raycaster.firstHitOnly === true ) {\n\n\t\t\tconst hit = convertRaycastIntersect( bvh.raycastFirst( ray, this.material, near, far ), this, raycaster );\n\t\t\tif ( hit ) {\n\n\t\t\t\tintersects.push( hit );\n\n\t\t\t}\n\n\t\t} else {\n\n\t\t\tconst hits = bvh.raycast( ray, this.material, near, far );\n\t\t\tfor ( let i = 0, l = hits.length; i < l; i ++ ) {\n\n\t\t\t\tconst hit = convertRaycastIntersect( hits[ i ], this, raycaster );\n\t\t\t\tif ( hit ) {\n\n\t\t\t\t\tintersects.push( hit );\n\n\t\t\t\t}\n\n\t\t\t}\n\n\t\t}\n\n\t} else {\n\n\t\torigMeshRaycastFunc.call( this, raycaster, intersects );\n\n\t}\n\n}\n\nexport function computeBoundsTree( options = {} ) {\n\n\tthis.boundsTree = new MeshBVH( this, options );\n\treturn this.boundsTree;\n\n}\n\nexport function disposeBoundsTree() {\n\n\tthis.boundsTree = null;\n\n}\n\nexport function computeBatchedBoundsTree( index = - 1, options = {} ) {\n\n\tif ( ! IS_REVISION_166 ) {\n\n\t\tthrow new Error( 'BatchedMesh: Three r166+ is required to compute bounds trees.' );\n\n\t}\n\n\tif ( options.indirect ) {\n\n\t\tconsole.warn( '\"Indirect\" is set to false because it is not supported for BatchedMesh.' );\n\n\t}\n\n\toptions = {\n\t\t...options,\n\t\tindirect: false,\n\t\trange: null\n\t};\n\n\tconst drawRanges = this._drawRanges || this._geometryInfo;\n\tconst geometryCount = this._geometryCount;\n\tif ( ! this.boundsTrees ) {\n\n\t\tthis.boundsTrees = new Array( geometryCount ).fill( null );\n\n\t}\n\n\tconst boundsTrees = this.boundsTrees;\n\twhile ( boundsTrees.length < geometryCount ) {\n\n\t\tboundsTrees.push( null );\n\n\t}\n\n\tif ( index < 0 ) {\n\n\t\tfor ( let i = 0; i < geometryCount; i ++ ) {\n\n\t\t\toptions.range = drawRanges[ i ];\n\t\t\tboundsTrees[ i ] = new MeshBVH( this.geometry, options );\n\n\t\t}\n\n\t\treturn boundsTrees;\n\n\t} else {\n\n\t\tif ( index < drawRanges.length ) {\n\n\t\t\toptions.range = drawRanges[ index ];\n\t\t\tboundsTrees[ index ] = new MeshBVH( this.geometry, options );\n\n\t\t}\n\n\t\treturn boundsTrees[ index ] || null;\n\n\t}\n\n}\n\nexport function disposeBatchedBoundsTree( index = - 1 ) {\n\n\tif ( index < 0 ) {\n\n\t\tthis.boundsTrees.fill( null );\n\n\t} else {\n\n\t\tif ( index < this.boundsTree.length ) {\n\n\t\t\tthis.boundsTrees[ index ] = null;\n\n\t\t}\n\n\t}\n\n}\n"], "mappings": "AAAA,SAASA,GAAG,EAAEC,OAAO,EAAEC,IAAI,EAAEC,OAAO,EAAEC,MAAM,EAAEC,WAAW,EAAEC,QAAQ,QAAQ,OAAO;AAClF,SAASC,uBAAuB,QAAQ,oCAAoC;AAC5E,SAASC,OAAO,QAAQ,oBAAoB;AAE5C,MAAMC,eAAe,GAAGC,QAAQ,CAAEJ,QAAS,CAAC,IAAI,GAAG;AACnD,MAAMK,GAAG,GAAG,eAAgB,IAAIX,GAAG,CAAC,CAAC;AACrC,MAAMY,SAAS,GAAG,eAAgB,IAAIT,OAAO,CAAC,CAAC;AAC/C,MAAMU,gBAAgB,GAAG,eAAgB,IAAIZ,OAAO,CAAC,CAAC;AACtD,MAAMa,mBAAmB,GAAGZ,IAAI,CAACa,SAAS,CAACC,OAAO;AAClD,MAAMC,sBAAsB,GAAGZ,WAAW,CAACU,SAAS,CAACC,OAAO;AAC5D,MAAME,WAAW,GAAG,eAAgB,IAAIf,OAAO,CAAC,CAAC;AACjD,MAAMgB,KAAK,GAAG,eAAgB,IAAIjB,IAAI,CAAC,CAAC;AACxC,MAAMkB,gBAAgB,GAAG,EAAE;AAE3B,OAAO,SAASC,kBAAkBA,CAAEC,SAAS,EAAEC,UAAU,EAAG;EAE3D,IAAK,IAAI,CAACC,aAAa,EAAG;IAEzBC,6BAA6B,CAACC,IAAI,CAAE,IAAI,EAAEJ,SAAS,EAAEC,UAAW,CAAC;EAElE,CAAC,MAAM;IAENI,sBAAsB,CAACD,IAAI,CAAE,IAAI,EAAEJ,SAAS,EAAEC,UAAW,CAAC;EAE3D;AAED;AAEA,SAASE,6BAA6BA,CAAEH,SAAS,EAAEC,UAAU,EAAG;EAE/D,IAAK,IAAI,CAACK,WAAW,EAAG;IAEvB;IACA,MAAMA,WAAW,GAAG,IAAI,CAACA,WAAW;IACpC,MAAMC,QAAQ,GAAG,IAAI,CAACC,SAAS,IAAI,IAAI,CAACC,aAAa;IACrD,MAAMC,UAAU,GAAG,IAAI,CAACC,WAAW,IAAI,IAAI,CAACC,aAAa;IACzD,MAAMC,WAAW,GAAG,IAAI,CAACA,WAAW;IAEpChB,KAAK,CAACiB,QAAQ,GAAG,IAAI,CAACA,QAAQ;IAC9BjB,KAAK,CAACkB,QAAQ,GAAG,IAAI,CAACA,QAAQ;IAE9B,MAAMC,aAAa,GAAGnB,KAAK,CAACkB,QAAQ,CAACE,UAAU;IAC/C,MAAMC,YAAY,GAAGrB,KAAK,CAACkB,QAAQ,CAACI,SAAS;IAE7C,IAAKtB,KAAK,CAACkB,QAAQ,CAACK,cAAc,KAAK,IAAI,EAAG;MAE7CvB,KAAK,CAACkB,QAAQ,CAACK,cAAc,GAAG,IAAItC,MAAM,CAAC,CAAC;IAE7C;;IAEA;IACA,KAAM,IAAIuC,CAAC,GAAG,CAAC,EAAEC,CAAC,GAAGf,QAAQ,CAACgB,MAAM,EAAEF,CAAC,GAAGC,CAAC,EAAED,CAAC,EAAG,EAAG;MAEnD,IAAK,CAAE,IAAI,CAACG,YAAY,CAAEH,CAAE,CAAC,EAAG;QAE/B;MAED;;MAEA;MACA,MAAMI,UAAU,GAAGlB,QAAQ,CAAEc,CAAC,CAAE,CAACK,aAAa;MAE9C7B,KAAK,CAACkB,QAAQ,CAACE,UAAU,GAAGX,WAAW,CAAEmB,UAAU,CAAE;MAErD,IAAI,CAACE,WAAW,CAAEN,CAAC,EAAExB,KAAK,CAACgB,WAAY,CAAC,CAACe,WAAW,CAAEf,WAAY,CAAC;MAEnE,IAAK,CAAEhB,KAAK,CAACkB,QAAQ,CAACE,UAAU,EAAG;QAElC,IAAI,CAACY,gBAAgB,CAAEJ,UAAU,EAAE5B,KAAK,CAACkB,QAAQ,CAACe,WAAY,CAAC;QAC/D,IAAI,CAACC,mBAAmB,CAAEN,UAAU,EAAE5B,KAAK,CAACkB,QAAQ,CAACK,cAAe,CAAC;QAErE,MAAMD,SAAS,GAAGT,UAAU,CAAEe,UAAU,CAAE;QAC1C5B,KAAK,CAACkB,QAAQ,CAACiB,YAAY,CAAEb,SAAS,CAACc,KAAK,EAAEd,SAAS,CAACe,KAAM,CAAC;MAEhE;MAEArC,KAAK,CAACH,OAAO,CAAEM,SAAS,EAAEF,gBAAiB,CAAC;MAE5C,KAAM,IAAIqC,CAAC,GAAG,CAAC,EAAEb,CAAC,GAAGxB,gBAAgB,CAACyB,MAAM,EAAEY,CAAC,GAAGb,CAAC,EAAEa,CAAC,EAAG,EAAG;QAE3D,MAAMC,SAAS,GAAGtC,gBAAgB,CAAEqC,CAAC,CAAE;QACvCC,SAAS,CAACC,MAAM,GAAG,IAAI;QACvBD,SAAS,CAACE,OAAO,GAAGjB,CAAC;QACrBpB,UAAU,CAACsC,IAAI,CAAEH,SAAU,CAAC;MAE7B;MAEAtC,gBAAgB,CAACyB,MAAM,GAAG,CAAC;IAE5B;IAEA1B,KAAK,CAACkB,QAAQ,CAACE,UAAU,GAAGD,aAAa;IACzCnB,KAAK,CAACkB,QAAQ,CAACI,SAAS,GAAGD,YAAY;IACvCrB,KAAK,CAACiB,QAAQ,GAAG,IAAI;IACrBjB,KAAK,CAACkB,QAAQ,GAAG,IAAI;EAEtB,CAAC,MAAM;IAENpB,sBAAsB,CAACS,IAAI,CAAE,IAAI,EAAEJ,SAAS,EAAEC,UAAW,CAAC;EAE3D;AAED;AAEA,SAASI,sBAAsBA,CAAEL,SAAS,EAAEC,UAAU,EAAG;EAExD,IAAK,IAAI,CAACc,QAAQ,CAACE,UAAU,EAAG;IAE/B,IAAK,IAAI,CAACH,QAAQ,KAAK0B,SAAS,EAAG;IAEnCjD,gBAAgB,CAACkD,IAAI,CAAE,IAAI,CAAC5B,WAAY,CAAC,CAAC6B,MAAM,CAAC,CAAC;IAClDrD,GAAG,CAACoD,IAAI,CAAEzC,SAAS,CAACX,GAAI,CAAC,CAACsD,YAAY,CAAEpD,gBAAiB,CAAC;IAE1DK,WAAW,CAACgD,kBAAkB,CAAE,IAAI,CAAC/B,WAAY,CAAC;IAClDvB,SAAS,CAACmD,IAAI,CAAEpD,GAAG,CAACC,SAAU,CAAC,CAACuD,QAAQ,CAAEjD,WAAY,CAAC;IAEvD,MAAMkD,WAAW,GAAGxD,SAAS,CAACiC,MAAM,CAAC,CAAC;IACtC,MAAMwB,IAAI,GAAG/C,SAAS,CAAC+C,IAAI,GAAGD,WAAW;IACzC,MAAME,GAAG,GAAGhD,SAAS,CAACgD,GAAG,GAAGF,WAAW;IAEvC,MAAMG,GAAG,GAAG,IAAI,CAAClC,QAAQ,CAACE,UAAU;IACpC,IAAKjB,SAAS,CAACkD,YAAY,KAAK,IAAI,EAAG;MAEtC,MAAMC,GAAG,GAAGlE,uBAAuB,CAAEgE,GAAG,CAACG,YAAY,CAAE/D,GAAG,EAAE,IAAI,CAACyB,QAAQ,EAAEiC,IAAI,EAAEC,GAAI,CAAC,EAAE,IAAI,EAAEhD,SAAU,CAAC;MACzG,IAAKmD,GAAG,EAAG;QAEVlD,UAAU,CAACsC,IAAI,CAAEY,GAAI,CAAC;MAEvB;IAED,CAAC,MAAM;MAEN,MAAME,IAAI,GAAGJ,GAAG,CAACvD,OAAO,CAAEL,GAAG,EAAE,IAAI,CAACyB,QAAQ,EAAEiC,IAAI,EAAEC,GAAI,CAAC;MACzD,KAAM,IAAI3B,CAAC,GAAG,CAAC,EAAEC,CAAC,GAAG+B,IAAI,CAAC9B,MAAM,EAAEF,CAAC,GAAGC,CAAC,EAAED,CAAC,EAAG,EAAG;QAE/C,MAAM8B,GAAG,GAAGlE,uBAAuB,CAAEoE,IAAI,CAAEhC,CAAC,CAAE,EAAE,IAAI,EAAErB,SAAU,CAAC;QACjE,IAAKmD,GAAG,EAAG;UAEVlD,UAAU,CAACsC,IAAI,CAAEY,GAAI,CAAC;QAEvB;MAED;IAED;EAED,CAAC,MAAM;IAEN3D,mBAAmB,CAACY,IAAI,CAAE,IAAI,EAAEJ,SAAS,EAAEC,UAAW,CAAC;EAExD;AAED;AAEA,OAAO,SAASqD,iBAAiBA,CAAEC,OAAO,GAAG,CAAC,CAAC,EAAG;EAEjD,IAAI,CAACtC,UAAU,GAAG,IAAI/B,OAAO,CAAE,IAAI,EAAEqE,OAAQ,CAAC;EAC9C,OAAO,IAAI,CAACtC,UAAU;AAEvB;AAEA,OAAO,SAASuC,iBAAiBA,CAAA,EAAG;EAEnC,IAAI,CAACvC,UAAU,GAAG,IAAI;AAEvB;AAEA,OAAO,SAASwC,wBAAwBA,CAAEC,KAAK,GAAG,CAAE,CAAC,EAAEH,OAAO,GAAG,CAAC,CAAC,EAAG;EAErE,IAAK,CAAEpE,eAAe,EAAG;IAExB,MAAM,IAAIwE,KAAK,CAAE,+DAAgE,CAAC;EAEnF;EAEA,IAAKJ,OAAO,CAACK,QAAQ,EAAG;IAEvBC,OAAO,CAACC,IAAI,CAAE,yEAA0E,CAAC;EAE1F;EAEAP,OAAO,GAAG;IACT,GAAGA,OAAO;IACVK,QAAQ,EAAE,KAAK;IACfG,KAAK,EAAE;EACR,CAAC;EAED,MAAMrD,UAAU,GAAG,IAAI,CAACC,WAAW,IAAI,IAAI,CAACC,aAAa;EACzD,MAAMoD,aAAa,GAAG,IAAI,CAACC,cAAc;EACzC,IAAK,CAAE,IAAI,CAAC3D,WAAW,EAAG;IAEzB,IAAI,CAACA,WAAW,GAAG,IAAI4D,KAAK,CAAEF,aAAc,CAAC,CAACG,IAAI,CAAE,IAAK,CAAC;EAE3D;EAEA,MAAM7D,WAAW,GAAG,IAAI,CAACA,WAAW;EACpC,OAAQA,WAAW,CAACiB,MAAM,GAAGyC,aAAa,EAAG;IAE5C1D,WAAW,CAACiC,IAAI,CAAE,IAAK,CAAC;EAEzB;EAEA,IAAKmB,KAAK,GAAG,CAAC,EAAG;IAEhB,KAAM,IAAIrC,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAG2C,aAAa,EAAE3C,CAAC,EAAG,EAAG;MAE1CkC,OAAO,CAACQ,KAAK,GAAGrD,UAAU,CAAEW,CAAC,CAAE;MAC/Bf,WAAW,CAAEe,CAAC,CAAE,GAAG,IAAInC,OAAO,CAAE,IAAI,CAAC6B,QAAQ,EAAEwC,OAAQ,CAAC;IAEzD;IAEA,OAAOjD,WAAW;EAEnB,CAAC,MAAM;IAEN,IAAKoD,KAAK,GAAGhD,UAAU,CAACa,MAAM,EAAG;MAEhCgC,OAAO,CAACQ,KAAK,GAAGrD,UAAU,CAAEgD,KAAK,CAAE;MACnCpD,WAAW,CAAEoD,KAAK,CAAE,GAAG,IAAIxE,OAAO,CAAE,IAAI,CAAC6B,QAAQ,EAAEwC,OAAQ,CAAC;IAE7D;IAEA,OAAOjD,WAAW,CAAEoD,KAAK,CAAE,IAAI,IAAI;EAEpC;AAED;AAEA,OAAO,SAASU,wBAAwBA,CAAEV,KAAK,GAAG,CAAE,CAAC,EAAG;EAEvD,IAAKA,KAAK,GAAG,CAAC,EAAG;IAEhB,IAAI,CAACpD,WAAW,CAAC6D,IAAI,CAAE,IAAK,CAAC;EAE9B,CAAC,MAAM;IAEN,IAAKT,KAAK,GAAG,IAAI,CAACzC,UAAU,CAACM,MAAM,EAAG;MAErC,IAAI,CAACjB,WAAW,CAAEoD,KAAK,CAAE,GAAG,IAAI;IAEjC;EAED;AAED", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}