{"ast": null, "code": "import _extends from '@babel/runtime/helpers/esm/extends';\nimport * as THREE from 'three';\nimport * as React from 'react';\nimport { useThree, createPortal, useFrame } from '@react-three/fiber';\nimport { useFBO } from './Fbo.js';\nconst RenderTexture = /* @__PURE__ */React.forwardRef(({\n  children,\n  compute,\n  width,\n  height,\n  samples = 8,\n  renderPriority = 0,\n  eventPriority = 0,\n  frames = Infinity,\n  stencilBuffer = false,\n  depthBuffer = true,\n  generateMipmaps = false,\n  ...props\n}, forwardRef) => {\n  const {\n    size,\n    viewport\n  } = useThree();\n  const fbo = useFBO((width || size.width) * viewport.dpr, (height || size.height) * viewport.dpr, {\n    samples,\n    stencilBuffer,\n    depthBuffer,\n    generateMipmaps\n  });\n  const [vScene] = React.useState(() => new THREE.Scene());\n  const uvCompute = React.useCallback((event, state, previous) => {\n    var _fbo$texture, _previous$previousRoo;\n    // Since this is only a texture it does not have an easy way to obtain the parent, which we\n    // need to transform event coordinates to local coordinates. We use r3f internals to find the\n    // next Object3D.\n    let parent = (_fbo$texture = fbo.texture) == null || (_fbo$texture = _fbo$texture.__r3f.parent) == null ? void 0 : _fbo$texture.object;\n    while (parent && !(parent instanceof THREE.Object3D)) {\n      var _parent$__r3f$parent;\n      parent = (_parent$__r3f$parent = parent.__r3f.parent) == null ? void 0 : _parent$__r3f$parent.object;\n    }\n    if (!parent) return false;\n    // First we call the previous state-onion-layers compute, this is what makes it possible to nest portals\n    if (!previous.raycaster.camera) previous.events.compute(event, previous, (_previous$previousRoo = previous.previousRoot) == null ? void 0 : _previous$previousRoo.getState());\n    // We run a quick check against the parent, if it isn't hit there's no need to raycast at all\n    const [intersection] = previous.raycaster.intersectObject(parent);\n    if (!intersection) return false;\n    // We take that hits uv coords, set up this layers raycaster, et voilà, we have raycasting on arbitrary surfaces\n    const uv = intersection.uv;\n    if (!uv) return false;\n    state.raycaster.setFromCamera(state.pointer.set(uv.x * 2 - 1, uv.y * 2 - 1), state.camera);\n  }, []);\n  React.useImperativeHandle(forwardRef, () => fbo.texture, [fbo]);\n  return /*#__PURE__*/React.createElement(React.Fragment, null, createPortal(/*#__PURE__*/React.createElement(Container, {\n    renderPriority: renderPriority,\n    frames: frames,\n    fbo: fbo\n  }, children, /*#__PURE__*/React.createElement(\"group\", {\n    onPointerOver: () => null\n  })), vScene, {\n    events: {\n      compute: compute || uvCompute,\n      priority: eventPriority\n    }\n  }), /*#__PURE__*/React.createElement(\"primitive\", _extends({\n    object: fbo.texture\n  }, props)));\n});\n\n// The container component has to be separate, it can not be inlined because \"useFrame(state\" when run inside createPortal will return\n// the portals own state which includes user-land overrides (custom cameras etc), but if it is executed in <RenderTexture>'s render function\n// it would return the default state.\nfunction Container({\n  frames,\n  renderPriority,\n  children,\n  fbo\n}) {\n  let count = 0;\n  let oldAutoClear;\n  let oldXrEnabled;\n  let oldRenderTarget;\n  let oldIsPresenting;\n  useFrame(state => {\n    if (frames === Infinity || count < frames) {\n      oldAutoClear = state.gl.autoClear;\n      oldXrEnabled = state.gl.xr.enabled;\n      oldRenderTarget = state.gl.getRenderTarget();\n      oldIsPresenting = state.gl.xr.isPresenting;\n      state.gl.autoClear = true;\n      state.gl.xr.enabled = false;\n      state.gl.xr.isPresenting = false;\n      state.gl.setRenderTarget(fbo);\n      state.gl.render(state.scene, state.camera);\n      state.gl.setRenderTarget(oldRenderTarget);\n      state.gl.autoClear = oldAutoClear;\n      state.gl.xr.enabled = oldXrEnabled;\n      state.gl.xr.isPresenting = oldIsPresenting;\n      count++;\n    }\n  }, renderPriority);\n  return /*#__PURE__*/React.createElement(React.Fragment, null, children);\n}\nexport { RenderTexture };", "map": {"version": 3, "names": ["_extends", "THREE", "React", "useThree", "createPortal", "useFrame", "useFBO", "RenderTexture", "forwardRef", "children", "compute", "width", "height", "samples", "renderPriority", "eventPriority", "frames", "Infinity", "stencil<PERSON>uffer", "depthBuffer", "generateMipmaps", "props", "size", "viewport", "fbo", "dpr", "vScene", "useState", "Scene", "uvCompute", "useCallback", "event", "state", "previous", "_fbo$texture", "_previous$previousRoo", "parent", "texture", "__r3f", "object", "Object3D", "_parent$__r3f$parent", "raycaster", "camera", "events", "previousRoot", "getState", "intersection", "intersectObject", "uv", "setFromCamera", "pointer", "set", "x", "y", "useImperativeHandle", "createElement", "Fragment", "Container", "onPointerOver", "priority", "count", "oldAutoClear", "oldXrEnabled", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "oldIsPresenting", "gl", "autoClear", "xr", "enabled", "get<PERSON><PERSON><PERSON><PERSON><PERSON>", "isPresenting", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "render", "scene"], "sources": ["C:/Users/<USER>/Documents/Apps_Creations/SiahMan_Racing4Speed_WebGame/siah-man-racing/node_modules/@react-three/drei/core/RenderTexture.js"], "sourcesContent": ["import _extends from '@babel/runtime/helpers/esm/extends';\nimport * as THREE from 'three';\nimport * as React from 'react';\nimport { useThree, createPortal, useFrame } from '@react-three/fiber';\nimport { useFBO } from './Fbo.js';\n\nconst RenderTexture = /* @__PURE__ */React.forwardRef(({\n  children,\n  compute,\n  width,\n  height,\n  samples = 8,\n  renderPriority = 0,\n  eventPriority = 0,\n  frames = Infinity,\n  stencilBuffer = false,\n  depthBuffer = true,\n  generateMipmaps = false,\n  ...props\n}, forwardRef) => {\n  const {\n    size,\n    viewport\n  } = useThree();\n  const fbo = useFBO((width || size.width) * viewport.dpr, (height || size.height) * viewport.dpr, {\n    samples,\n    stencilBuffer,\n    depthBuffer,\n    generateMipmaps\n  });\n  const [vScene] = React.useState(() => new THREE.Scene());\n  const uvCompute = React.useCallback((event, state, previous) => {\n    var _fbo$texture, _previous$previousRoo;\n    // Since this is only a texture it does not have an easy way to obtain the parent, which we\n    // need to transform event coordinates to local coordinates. We use r3f internals to find the\n    // next Object3D.\n    let parent = (_fbo$texture = fbo.texture) == null || (_fbo$texture = _fbo$texture.__r3f.parent) == null ? void 0 : _fbo$texture.object;\n    while (parent && !(parent instanceof THREE.Object3D)) {\n      var _parent$__r3f$parent;\n      parent = (_parent$__r3f$parent = parent.__r3f.parent) == null ? void 0 : _parent$__r3f$parent.object;\n    }\n    if (!parent) return false;\n    // First we call the previous state-onion-layers compute, this is what makes it possible to nest portals\n    if (!previous.raycaster.camera) previous.events.compute(event, previous, (_previous$previousRoo = previous.previousRoot) == null ? void 0 : _previous$previousRoo.getState());\n    // We run a quick check against the parent, if it isn't hit there's no need to raycast at all\n    const [intersection] = previous.raycaster.intersectObject(parent);\n    if (!intersection) return false;\n    // We take that hits uv coords, set up this layers raycaster, et voilà, we have raycasting on arbitrary surfaces\n    const uv = intersection.uv;\n    if (!uv) return false;\n    state.raycaster.setFromCamera(state.pointer.set(uv.x * 2 - 1, uv.y * 2 - 1), state.camera);\n  }, []);\n  React.useImperativeHandle(forwardRef, () => fbo.texture, [fbo]);\n  return /*#__PURE__*/React.createElement(React.Fragment, null, createPortal(/*#__PURE__*/React.createElement(Container, {\n    renderPriority: renderPriority,\n    frames: frames,\n    fbo: fbo\n  }, children, /*#__PURE__*/React.createElement(\"group\", {\n    onPointerOver: () => null\n  })), vScene, {\n    events: {\n      compute: compute || uvCompute,\n      priority: eventPriority\n    }\n  }), /*#__PURE__*/React.createElement(\"primitive\", _extends({\n    object: fbo.texture\n  }, props)));\n});\n\n// The container component has to be separate, it can not be inlined because \"useFrame(state\" when run inside createPortal will return\n// the portals own state which includes user-land overrides (custom cameras etc), but if it is executed in <RenderTexture>'s render function\n// it would return the default state.\nfunction Container({\n  frames,\n  renderPriority,\n  children,\n  fbo\n}) {\n  let count = 0;\n  let oldAutoClear;\n  let oldXrEnabled;\n  let oldRenderTarget;\n  let oldIsPresenting;\n  useFrame(state => {\n    if (frames === Infinity || count < frames) {\n      oldAutoClear = state.gl.autoClear;\n      oldXrEnabled = state.gl.xr.enabled;\n      oldRenderTarget = state.gl.getRenderTarget();\n      oldIsPresenting = state.gl.xr.isPresenting;\n      state.gl.autoClear = true;\n      state.gl.xr.enabled = false;\n      state.gl.xr.isPresenting = false;\n      state.gl.setRenderTarget(fbo);\n      state.gl.render(state.scene, state.camera);\n      state.gl.setRenderTarget(oldRenderTarget);\n      state.gl.autoClear = oldAutoClear;\n      state.gl.xr.enabled = oldXrEnabled;\n      state.gl.xr.isPresenting = oldIsPresenting;\n      count++;\n    }\n  }, renderPriority);\n  return /*#__PURE__*/React.createElement(React.Fragment, null, children);\n}\n\nexport { RenderTexture };\n"], "mappings": "AAAA,OAAOA,QAAQ,MAAM,oCAAoC;AACzD,OAAO,KAAKC,KAAK,MAAM,OAAO;AAC9B,OAAO,KAAKC,KAAK,MAAM,OAAO;AAC9B,SAASC,QAAQ,EAAEC,YAAY,EAAEC,QAAQ,QAAQ,oBAAoB;AACrE,SAASC,MAAM,QAAQ,UAAU;AAEjC,MAAMC,aAAa,GAAG,eAAeL,KAAK,CAACM,UAAU,CAAC,CAAC;EACrDC,QAAQ;EACRC,OAAO;EACPC,KAAK;EACLC,MAAM;EACNC,OAAO,GAAG,CAAC;EACXC,cAAc,GAAG,CAAC;EAClBC,aAAa,GAAG,CAAC;EACjBC,MAAM,GAAGC,QAAQ;EACjBC,aAAa,GAAG,KAAK;EACrBC,WAAW,GAAG,IAAI;EAClBC,eAAe,GAAG,KAAK;EACvB,GAAGC;AACL,CAAC,EAAEb,UAAU,KAAK;EAChB,MAAM;IACJc,IAAI;IACJC;EACF,CAAC,GAAGpB,QAAQ,CAAC,CAAC;EACd,MAAMqB,GAAG,GAAGlB,MAAM,CAAC,CAACK,KAAK,IAAIW,IAAI,CAACX,KAAK,IAAIY,QAAQ,CAACE,GAAG,EAAE,CAACb,MAAM,IAAIU,IAAI,CAACV,MAAM,IAAIW,QAAQ,CAACE,GAAG,EAAE;IAC/FZ,OAAO;IACPK,aAAa;IACbC,WAAW;IACXC;EACF,CAAC,CAAC;EACF,MAAM,CAACM,MAAM,CAAC,GAAGxB,KAAK,CAACyB,QAAQ,CAAC,MAAM,IAAI1B,KAAK,CAAC2B,KAAK,CAAC,CAAC,CAAC;EACxD,MAAMC,SAAS,GAAG3B,KAAK,CAAC4B,WAAW,CAAC,CAACC,KAAK,EAAEC,KAAK,EAAEC,QAAQ,KAAK;IAC9D,IAAIC,YAAY,EAAEC,qBAAqB;IACvC;IACA;IACA;IACA,IAAIC,MAAM,GAAG,CAACF,YAAY,GAAGV,GAAG,CAACa,OAAO,KAAK,IAAI,IAAI,CAACH,YAAY,GAAGA,YAAY,CAACI,KAAK,CAACF,MAAM,KAAK,IAAI,GAAG,KAAK,CAAC,GAAGF,YAAY,CAACK,MAAM;IACtI,OAAOH,MAAM,IAAI,EAAEA,MAAM,YAAYnC,KAAK,CAACuC,QAAQ,CAAC,EAAE;MACpD,IAAIC,oBAAoB;MACxBL,MAAM,GAAG,CAACK,oBAAoB,GAAGL,MAAM,CAACE,KAAK,CAACF,MAAM,KAAK,IAAI,GAAG,KAAK,CAAC,GAAGK,oBAAoB,CAACF,MAAM;IACtG;IACA,IAAI,CAACH,MAAM,EAAE,OAAO,KAAK;IACzB;IACA,IAAI,CAACH,QAAQ,CAACS,SAAS,CAACC,MAAM,EAAEV,QAAQ,CAACW,MAAM,CAAClC,OAAO,CAACqB,KAAK,EAAEE,QAAQ,EAAE,CAACE,qBAAqB,GAAGF,QAAQ,CAACY,YAAY,KAAK,IAAI,GAAG,KAAK,CAAC,GAAGV,qBAAqB,CAACW,QAAQ,CAAC,CAAC,CAAC;IAC7K;IACA,MAAM,CAACC,YAAY,CAAC,GAAGd,QAAQ,CAACS,SAAS,CAACM,eAAe,CAACZ,MAAM,CAAC;IACjE,IAAI,CAACW,YAAY,EAAE,OAAO,KAAK;IAC/B;IACA,MAAME,EAAE,GAAGF,YAAY,CAACE,EAAE;IAC1B,IAAI,CAACA,EAAE,EAAE,OAAO,KAAK;IACrBjB,KAAK,CAACU,SAAS,CAACQ,aAAa,CAAClB,KAAK,CAACmB,OAAO,CAACC,GAAG,CAACH,EAAE,CAACI,CAAC,GAAG,CAAC,GAAG,CAAC,EAAEJ,EAAE,CAACK,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC,EAAEtB,KAAK,CAACW,MAAM,CAAC;EAC5F,CAAC,EAAE,EAAE,CAAC;EACNzC,KAAK,CAACqD,mBAAmB,CAAC/C,UAAU,EAAE,MAAMgB,GAAG,CAACa,OAAO,EAAE,CAACb,GAAG,CAAC,CAAC;EAC/D,OAAO,aAAatB,KAAK,CAACsD,aAAa,CAACtD,KAAK,CAACuD,QAAQ,EAAE,IAAI,EAAErD,YAAY,CAAC,aAAaF,KAAK,CAACsD,aAAa,CAACE,SAAS,EAAE;IACrH5C,cAAc,EAAEA,cAAc;IAC9BE,MAAM,EAAEA,MAAM;IACdQ,GAAG,EAAEA;EACP,CAAC,EAAEf,QAAQ,EAAE,aAAaP,KAAK,CAACsD,aAAa,CAAC,OAAO,EAAE;IACrDG,aAAa,EAAEA,CAAA,KAAM;EACvB,CAAC,CAAC,CAAC,EAAEjC,MAAM,EAAE;IACXkB,MAAM,EAAE;MACNlC,OAAO,EAAEA,OAAO,IAAImB,SAAS;MAC7B+B,QAAQ,EAAE7C;IACZ;EACF,CAAC,CAAC,EAAE,aAAab,KAAK,CAACsD,aAAa,CAAC,WAAW,EAAExD,QAAQ,CAAC;IACzDuC,MAAM,EAAEf,GAAG,CAACa;EACd,CAAC,EAAEhB,KAAK,CAAC,CAAC,CAAC;AACb,CAAC,CAAC;;AAEF;AACA;AACA;AACA,SAASqC,SAASA,CAAC;EACjB1C,MAAM;EACNF,cAAc;EACdL,QAAQ;EACRe;AACF,CAAC,EAAE;EACD,IAAIqC,KAAK,GAAG,CAAC;EACb,IAAIC,YAAY;EAChB,IAAIC,YAAY;EAChB,IAAIC,eAAe;EACnB,IAAIC,eAAe;EACnB5D,QAAQ,CAAC2B,KAAK,IAAI;IAChB,IAAIhB,MAAM,KAAKC,QAAQ,IAAI4C,KAAK,GAAG7C,MAAM,EAAE;MACzC8C,YAAY,GAAG9B,KAAK,CAACkC,EAAE,CAACC,SAAS;MACjCJ,YAAY,GAAG/B,KAAK,CAACkC,EAAE,CAACE,EAAE,CAACC,OAAO;MAClCL,eAAe,GAAGhC,KAAK,CAACkC,EAAE,CAACI,eAAe,CAAC,CAAC;MAC5CL,eAAe,GAAGjC,KAAK,CAACkC,EAAE,CAACE,EAAE,CAACG,YAAY;MAC1CvC,KAAK,CAACkC,EAAE,CAACC,SAAS,GAAG,IAAI;MACzBnC,KAAK,CAACkC,EAAE,CAACE,EAAE,CAACC,OAAO,GAAG,KAAK;MAC3BrC,KAAK,CAACkC,EAAE,CAACE,EAAE,CAACG,YAAY,GAAG,KAAK;MAChCvC,KAAK,CAACkC,EAAE,CAACM,eAAe,CAAChD,GAAG,CAAC;MAC7BQ,KAAK,CAACkC,EAAE,CAACO,MAAM,CAACzC,KAAK,CAAC0C,KAAK,EAAE1C,KAAK,CAACW,MAAM,CAAC;MAC1CX,KAAK,CAACkC,EAAE,CAACM,eAAe,CAACR,eAAe,CAAC;MACzChC,KAAK,CAACkC,EAAE,CAACC,SAAS,GAAGL,YAAY;MACjC9B,KAAK,CAACkC,EAAE,CAACE,EAAE,CAACC,OAAO,GAAGN,YAAY;MAClC/B,KAAK,CAACkC,EAAE,CAACE,EAAE,CAACG,YAAY,GAAGN,eAAe;MAC1CJ,KAAK,EAAE;IACT;EACF,CAAC,EAAE/C,cAAc,CAAC;EAClB,OAAO,aAAaZ,KAAK,CAACsD,aAAa,CAACtD,KAAK,CAACuD,QAAQ,EAAE,IAAI,EAAEhD,QAAQ,CAAC;AACzE;AAEA,SAASF,aAAa", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}