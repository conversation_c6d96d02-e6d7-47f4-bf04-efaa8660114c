{"ast": null, "code": "import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON>ector3, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON> } from \"three\";\nconst _normalData = [[-0.525731, 0, 0.850651], [-0.442863, 0.238856, 0.864188], [-0.295242, 0, 0.955423], [-0.309017, 0.5, 0.809017], [-0.16246, 0.262866, 0.951056], [0, 0, 1], [0, 0.850651, 0.525731], [-0.147621, 0.716567, 0.681718], [0.147621, 0.716567, 0.681718], [0, 0.525731, 0.850651], [0.309017, 0.5, 0.809017], [0.525731, 0, 0.850651], [0.295242, 0, 0.955423], [0.442863, 0.238856, 0.864188], [0.16246, 0.262866, 0.951056], [-0.681718, 0.147621, 0.716567], [-0.809017, 0.309017, 0.5], [-0.587785, 0.425325, 0.688191], [-0.850651, 0.525731, 0], [-0.864188, 0.442863, 0.238856], [-0.716567, 0.681718, 0.147621], [-0.688191, 0.587785, 0.425325], [-0.5, 0.809017, 0.309017], [-0.238856, 0.864188, 0.442863], [-0.425325, 0.688191, 0.587785], [-0.716567, 0.681718, -0.147621], [-0.5, 0.809017, -0.309017], [-0.525731, 0.850651, 0], [0, 0.850651, -0.525731], [-0.238856, 0.864188, -0.442863], [0, 0.955423, -0.295242], [-0.262866, 0.951056, -0.16246], [0, 1, 0], [0, 0.955423, 0.295242], [-0.262866, 0.951056, 0.16246], [0.238856, 0.864188, 0.442863], [0.262866, 0.951056, 0.16246], [0.5, 0.809017, 0.309017], [0.238856, 0.864188, -0.442863], [0.262866, 0.951056, -0.16246], [0.5, 0.809017, -0.309017], [0.850651, 0.525731, 0], [0.716567, 0.681718, 0.147621], [0.716567, 0.681718, -0.147621], [0.525731, 0.850651, 0], [0.425325, 0.688191, 0.587785], [0.864188, 0.442863, 0.238856], [0.688191, 0.587785, 0.425325], [0.809017, 0.309017, 0.5], [0.681718, 0.147621, 0.716567], [0.587785, 0.425325, 0.688191], [0.955423, 0.295242, 0], [1, 0, 0], [0.951056, 0.16246, 0.262866], [0.850651, -0.525731, 0], [0.955423, -0.295242, 0], [0.864188, -0.442863, 0.238856], [0.951056, -0.16246, 0.262866], [0.809017, -0.309017, 0.5], [0.681718, -0.147621, 0.716567], [0.850651, 0, 0.525731], [0.864188, 0.442863, -0.238856], [0.809017, 0.309017, -0.5], [0.951056, 0.16246, -0.262866], [0.525731, 0, -0.850651], [0.681718, 0.147621, -0.716567], [0.681718, -0.147621, -0.716567], [0.850651, 0, -0.525731], [0.809017, -0.309017, -0.5], [0.864188, -0.442863, -0.238856], [0.951056, -0.16246, -0.262866], [0.147621, 0.716567, -0.681718], [0.309017, 0.5, -0.809017], [0.425325, 0.688191, -0.587785], [0.442863, 0.238856, -0.864188], [0.587785, 0.425325, -0.688191], [0.688191, 0.587785, -0.425325], [-0.147621, 0.716567, -0.681718], [-0.309017, 0.5, -0.809017], [0, 0.525731, -0.850651], [-0.525731, 0, -0.850651], [-0.442863, 0.238856, -0.864188], [-0.295242, 0, -0.955423], [-0.16246, 0.262866, -0.951056], [0, 0, -1], [0.295242, 0, -0.955423], [0.16246, 0.262866, -0.951056], [-0.442863, -0.238856, -0.864188], [-0.309017, -0.5, -0.809017], [-0.16246, -0.262866, -0.951056], [0, -0.850651, -0.525731], [-0.147621, -0.716567, -0.681718], [0.147621, -0.716567, -0.681718], [0, -0.525731, -0.850651], [0.309017, -0.5, -0.809017], [0.442863, -0.238856, -0.864188], [0.16246, -0.262866, -0.951056], [0.238856, -0.864188, -0.442863], [0.5, -0.809017, -0.309017], [0.425325, -0.688191, -0.587785], [0.716567, -0.681718, -0.147621], [0.688191, -0.587785, -0.425325], [0.587785, -0.425325, -0.688191], [0, -0.955423, -0.295242], [0, -1, 0], [0.262866, -0.951056, -0.16246], [0, -0.850651, 0.525731], [0, -0.955423, 0.295242], [0.238856, -0.864188, 0.442863], [0.262866, -0.951056, 0.16246], [0.5, -0.809017, 0.309017], [0.716567, -0.681718, 0.147621], [0.525731, -0.850651, 0], [-0.238856, -0.864188, -0.442863], [-0.5, -0.809017, -0.309017], [-0.262866, -0.951056, -0.16246], [-0.850651, -0.525731, 0], [-0.716567, -0.681718, -0.147621], [-0.716567, -0.681718, 0.147621], [-0.525731, -0.850651, 0], [-0.5, -0.809017, 0.309017], [-0.238856, -0.864188, 0.442863], [-0.262866, -0.951056, 0.16246], [-0.864188, -0.442863, 0.238856], [-0.809017, -0.309017, 0.5], [-0.688191, -0.587785, 0.425325], [-0.681718, -0.147621, 0.716567], [-0.442863, -0.238856, 0.864188], [-0.587785, -0.425325, 0.688191], [-0.309017, -0.5, 0.809017], [-0.147621, -0.716567, 0.681718], [-0.425325, -0.688191, 0.587785], [-0.16246, -0.262866, 0.951056], [0.442863, -0.238856, 0.864188], [0.16246, -0.262866, 0.951056], [0.309017, -0.5, 0.809017], [0.147621, -0.716567, 0.681718], [0, -0.525731, 0.850651], [0.425325, -0.688191, 0.587785], [0.587785, -0.425325, 0.688191], [0.688191, -0.587785, 0.425325], [-0.955423, 0.295242, 0], [-0.951056, 0.16246, 0.262866], [-1, 0, 0], [-0.850651, 0, 0.525731], [-0.955423, -0.295242, 0], [-0.951056, -0.16246, 0.262866], [-0.864188, 0.442863, -0.238856], [-0.951056, 0.16246, -0.262866], [-0.809017, 0.309017, -0.5], [-0.864188, -0.442863, -0.238856], [-0.951056, -0.16246, -0.262866], [-0.809017, -0.309017, -0.5], [-0.681718, 0.147621, -0.716567], [-0.681718, -0.147621, -0.716567], [-0.850651, 0, -0.525731], [-0.688191, 0.587785, -0.425325], [-0.587785, 0.425325, -0.688191], [-0.425325, 0.688191, -0.587785], [-0.425325, -0.688191, -0.587785], [-0.587785, -0.425325, -0.688191], [-0.688191, -0.587785, -0.425325]];\nclass MD2Loader extends Loader {\n  constructor(manager) {\n    super(manager);\n  }\n  load(url, onLoad, onProgress, onError) {\n    const scope = this;\n    const loader = new FileLoader(scope.manager);\n    loader.setPath(scope.path);\n    loader.setResponseType(\"arraybuffer\");\n    loader.setRequestHeader(scope.requestHeader);\n    loader.setWithCredentials(scope.withCredentials);\n    loader.load(url, function (buffer) {\n      try {\n        onLoad(scope.parse(buffer));\n      } catch (e) {\n        if (onError) {\n          onError(e);\n        } else {\n          console.error(e);\n        }\n        scope.manager.itemError(url);\n      }\n    }, onProgress, onError);\n  }\n  parse(buffer) {\n    const data = new DataView(buffer);\n    const header = {};\n    const headerNames = [\"ident\", \"version\", \"skinwidth\", \"skinheight\", \"framesize\", \"num_skins\", \"num_vertices\", \"num_st\", \"num_tris\", \"num_glcmds\", \"num_frames\", \"offset_skins\", \"offset_st\", \"offset_tris\", \"offset_frames\", \"offset_glcmds\", \"offset_end\"];\n    for (let i = 0; i < headerNames.length; i++) {\n      header[headerNames[i]] = data.getInt32(i * 4, true);\n    }\n    if (header.ident !== 844121161 || header.version !== 8) {\n      console.error(\"Not a valid MD2 file\");\n      return;\n    }\n    if (header.offset_end !== data.byteLength) {\n      console.error(\"Corrupted MD2 file\");\n      return;\n    }\n    const geometry = new BufferGeometry();\n    const uvsTemp = [];\n    let offset = header.offset_st;\n    for (let i = 0, l = header.num_st; i < l; i++) {\n      const u = data.getInt16(offset + 0, true);\n      const v = data.getInt16(offset + 2, true);\n      uvsTemp.push(u / header.skinwidth, 1 - v / header.skinheight);\n      offset += 4;\n    }\n    offset = header.offset_tris;\n    const vertexIndices = [];\n    const uvIndices = [];\n    for (let i = 0, l = header.num_tris; i < l; i++) {\n      vertexIndices.push(data.getUint16(offset + 0, true), data.getUint16(offset + 2, true), data.getUint16(offset + 4, true));\n      uvIndices.push(data.getUint16(offset + 6, true), data.getUint16(offset + 8, true), data.getUint16(offset + 10, true));\n      offset += 12;\n    }\n    const translation = new Vector3();\n    const scale = new Vector3();\n    const string = [];\n    const frames = [];\n    offset = header.offset_frames;\n    for (let i = 0, l = header.num_frames; i < l; i++) {\n      scale.set(data.getFloat32(offset + 0, true), data.getFloat32(offset + 4, true), data.getFloat32(offset + 8, true));\n      translation.set(data.getFloat32(offset + 12, true), data.getFloat32(offset + 16, true), data.getFloat32(offset + 20, true));\n      offset += 24;\n      for (let j = 0; j < 16; j++) {\n        const character = data.getUint8(offset + j, true);\n        if (character === 0) break;\n        string[j] = character;\n      }\n      const frame = {\n        name: String.fromCharCode.apply(null, string),\n        vertices: [],\n        normals: []\n      };\n      offset += 16;\n      for (let j = 0; j < header.num_vertices; j++) {\n        let x = data.getUint8(offset++, true);\n        let y = data.getUint8(offset++, true);\n        let z = data.getUint8(offset++, true);\n        const n = _normalData[data.getUint8(offset++, true)];\n        x = x * scale.x + translation.x;\n        y = y * scale.y + translation.y;\n        z = z * scale.z + translation.z;\n        frame.vertices.push(x, z, y);\n        frame.normals.push(n[0], n[2], n[1]);\n      }\n      frames.push(frame);\n    }\n    const positions = [];\n    const normals = [];\n    const uvs = [];\n    const verticesTemp = frames[0].vertices;\n    const normalsTemp = frames[0].normals;\n    for (let i = 0, l = vertexIndices.length; i < l; i++) {\n      const vertexIndex = vertexIndices[i];\n      let stride = vertexIndex * 3;\n      const x = verticesTemp[stride];\n      const y = verticesTemp[stride + 1];\n      const z = verticesTemp[stride + 2];\n      positions.push(x, y, z);\n      const nx = normalsTemp[stride];\n      const ny = normalsTemp[stride + 1];\n      const nz = normalsTemp[stride + 2];\n      normals.push(nx, ny, nz);\n      const uvIndex = uvIndices[i];\n      stride = uvIndex * 2;\n      const u = uvsTemp[stride];\n      const v = uvsTemp[stride + 1];\n      uvs.push(u, v);\n    }\n    geometry.setAttribute(\"position\", new Float32BufferAttribute(positions, 3));\n    geometry.setAttribute(\"normal\", new Float32BufferAttribute(normals, 3));\n    geometry.setAttribute(\"uv\", new Float32BufferAttribute(uvs, 2));\n    const morphPositions = [];\n    const morphNormals = [];\n    for (let i = 0, l = frames.length; i < l; i++) {\n      const frame = frames[i];\n      const attributeName = frame.name;\n      if (frame.vertices.length > 0) {\n        const positions2 = [];\n        for (let j = 0, jl = vertexIndices.length; j < jl; j++) {\n          const vertexIndex = vertexIndices[j];\n          const stride = vertexIndex * 3;\n          const x = frame.vertices[stride];\n          const y = frame.vertices[stride + 1];\n          const z = frame.vertices[stride + 2];\n          positions2.push(x, y, z);\n        }\n        const positionAttribute = new Float32BufferAttribute(positions2, 3);\n        positionAttribute.name = attributeName;\n        morphPositions.push(positionAttribute);\n      }\n      if (frame.normals.length > 0) {\n        const normals2 = [];\n        for (let j = 0, jl = vertexIndices.length; j < jl; j++) {\n          const vertexIndex = vertexIndices[j];\n          const stride = vertexIndex * 3;\n          const nx = frame.normals[stride];\n          const ny = frame.normals[stride + 1];\n          const nz = frame.normals[stride + 2];\n          normals2.push(nx, ny, nz);\n        }\n        const normalAttribute = new Float32BufferAttribute(normals2, 3);\n        normalAttribute.name = attributeName;\n        morphNormals.push(normalAttribute);\n      }\n    }\n    geometry.morphAttributes.position = morphPositions;\n    geometry.morphAttributes.normal = morphNormals;\n    geometry.morphTargetsRelative = false;\n    geometry.animations = AnimationClip.CreateClipsFromMorphTargetSequences(frames, 10);\n    return geometry;\n  }\n}\nexport { MD2Loader };", "map": {"version": 3, "names": ["_normalData", "MD2Loader", "Loader", "constructor", "manager", "load", "url", "onLoad", "onProgress", "onError", "scope", "loader", "<PERSON><PERSON><PERSON><PERSON>", "set<PERSON>ath", "path", "setResponseType", "setRequestHeader", "requestHeader", "setWithCredentials", "withCredentials", "buffer", "parse", "e", "console", "error", "itemError", "data", "DataView", "header", "headerNames", "i", "length", "getInt32", "ident", "version", "offset_end", "byteLength", "geometry", "BufferGeometry", "uvsTemp", "offset", "offset_st", "l", "num_st", "u", "getInt16", "v", "push", "skinwidth", "skinheight", "offset_tris", "vertexIndices", "uvIndices", "num_tris", "getUint16", "translation", "Vector3", "scale", "string", "frames", "offset_frames", "num_frames", "set", "getFloat32", "j", "character", "getUint8", "frame", "name", "String", "fromCharCode", "apply", "vertices", "normals", "num_vertices", "x", "y", "z", "n", "positions", "uvs", "verticesTemp", "normalsTemp", "vertexIndex", "stride", "nx", "ny", "nz", "uvIndex", "setAttribute", "Float32BufferAttribute", "morphPositions", "morphNormals", "attributeName", "positions2", "jl", "positionAttribute", "normals2", "normalAttribute", "morphAttributes", "position", "normal", "morphTargetsRelative", "animations", "AnimationClip", "CreateClipsFromMorphTargetSequences"], "sources": ["C:\\Users\\<USER>\\Documents\\Apps_Creations\\SiahMan_Racing4Speed_WebGame\\siah-man-racing\\node_modules\\src\\loaders\\MD2Loader.js"], "sourcesContent": ["import { <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON>ector3 } from 'three'\n\nconst _normalData = [\n  [-0.525731, 0.0, 0.850651],\n  [-0.442863, 0.238856, 0.864188],\n  [-0.295242, 0.0, 0.955423],\n  [-0.309017, 0.5, 0.809017],\n  [-0.16246, 0.262866, 0.951056],\n  [0.0, 0.0, 1.0],\n  [0.0, 0.850651, 0.525731],\n  [-0.147621, 0.716567, 0.681718],\n  [0.147621, 0.716567, 0.681718],\n  [0.0, 0.525731, 0.850651],\n  [0.309017, 0.5, 0.809017],\n  [0.525731, 0.0, 0.850651],\n  [0.295242, 0.0, 0.955423],\n  [0.442863, 0.238856, 0.864188],\n  [0.16246, 0.262866, 0.951056],\n  [-0.681718, 0.147621, 0.716567],\n  [-0.809017, 0.309017, 0.5],\n  [-0.587785, 0.425325, 0.688191],\n  [-0.850651, 0.525731, 0.0],\n  [-0.864188, 0.442863, 0.238856],\n  [-0.716567, 0.681718, 0.147621],\n  [-0.688191, 0.587785, 0.425325],\n  [-0.5, 0.809017, 0.309017],\n  [-0.238856, 0.864188, 0.442863],\n  [-0.425325, 0.688191, 0.587785],\n  [-0.716567, 0.681718, -0.147621],\n  [-0.5, 0.809017, -0.309017],\n  [-0.525731, 0.850651, 0.0],\n  [0.0, 0.850651, -0.525731],\n  [-0.238856, 0.864188, -0.442863],\n  [0.0, 0.955423, -0.295242],\n  [-0.262866, 0.951056, -0.16246],\n  [0.0, 1.0, 0.0],\n  [0.0, 0.955423, 0.295242],\n  [-0.262866, 0.951056, 0.16246],\n  [0.238856, 0.864188, 0.442863],\n  [0.262866, 0.951056, 0.16246],\n  [0.5, 0.809017, 0.309017],\n  [0.238856, 0.864188, -0.442863],\n  [0.262866, 0.951056, -0.16246],\n  [0.5, 0.809017, -0.309017],\n  [0.850651, 0.525731, 0.0],\n  [0.716567, 0.681718, 0.147621],\n  [0.716567, 0.681718, -0.147621],\n  [0.525731, 0.850651, 0.0],\n  [0.425325, 0.688191, 0.587785],\n  [0.864188, 0.442863, 0.238856],\n  [0.688191, 0.587785, 0.425325],\n  [0.809017, 0.309017, 0.5],\n  [0.681718, 0.147621, 0.716567],\n  [0.587785, 0.425325, 0.688191],\n  [0.955423, 0.295242, 0.0],\n  [1.0, 0.0, 0.0],\n  [0.951056, 0.16246, 0.262866],\n  [0.850651, -0.525731, 0.0],\n  [0.955423, -0.295242, 0.0],\n  [0.864188, -0.442863, 0.238856],\n  [0.951056, -0.16246, 0.262866],\n  [0.809017, -0.309017, 0.5],\n  [0.681718, -0.147621, 0.716567],\n  [0.850651, 0.0, 0.525731],\n  [0.864188, 0.442863, -0.238856],\n  [0.809017, 0.309017, -0.5],\n  [0.951056, 0.16246, -0.262866],\n  [0.525731, 0.0, -0.850651],\n  [0.681718, 0.147621, -0.716567],\n  [0.681718, -0.147621, -0.716567],\n  [0.850651, 0.0, -0.525731],\n  [0.809017, -0.309017, -0.5],\n  [0.864188, -0.442863, -0.238856],\n  [0.951056, -0.16246, -0.262866],\n  [0.147621, 0.716567, -0.681718],\n  [0.309017, 0.5, -0.809017],\n  [0.425325, 0.688191, -0.587785],\n  [0.442863, 0.238856, -0.864188],\n  [0.587785, 0.425325, -0.688191],\n  [0.688191, 0.587785, -0.425325],\n  [-0.147621, 0.716567, -0.681718],\n  [-0.309017, 0.5, -0.809017],\n  [0.0, 0.525731, -0.850651],\n  [-0.525731, 0.0, -0.850651],\n  [-0.442863, 0.238856, -0.864188],\n  [-0.295242, 0.0, -0.955423],\n  [-0.16246, 0.262866, -0.951056],\n  [0.0, 0.0, -1.0],\n  [0.295242, 0.0, -0.955423],\n  [0.16246, 0.262866, -0.951056],\n  [-0.442863, -0.238856, -0.864188],\n  [-0.309017, -0.5, -0.809017],\n  [-0.16246, -0.262866, -0.951056],\n  [0.0, -0.850651, -0.525731],\n  [-0.147621, -0.716567, -0.681718],\n  [0.147621, -0.716567, -0.681718],\n  [0.0, -0.525731, -0.850651],\n  [0.309017, -0.5, -0.809017],\n  [0.442863, -0.238856, -0.864188],\n  [0.16246, -0.262866, -0.951056],\n  [0.238856, -0.864188, -0.442863],\n  [0.5, -0.809017, -0.309017],\n  [0.425325, -0.688191, -0.587785],\n  [0.716567, -0.681718, -0.147621],\n  [0.688191, -0.587785, -0.425325],\n  [0.587785, -0.425325, -0.688191],\n  [0.0, -0.955423, -0.295242],\n  [0.0, -1.0, 0.0],\n  [0.262866, -0.951056, -0.16246],\n  [0.0, -0.850651, 0.525731],\n  [0.0, -0.955423, 0.295242],\n  [0.238856, -0.864188, 0.442863],\n  [0.262866, -0.951056, 0.16246],\n  [0.5, -0.809017, 0.309017],\n  [0.716567, -0.681718, 0.147621],\n  [0.525731, -0.850651, 0.0],\n  [-0.238856, -0.864188, -0.442863],\n  [-0.5, -0.809017, -0.309017],\n  [-0.262866, -0.951056, -0.16246],\n  [-0.850651, -0.525731, 0.0],\n  [-0.716567, -0.681718, -0.147621],\n  [-0.716567, -0.681718, 0.147621],\n  [-0.525731, -0.850651, 0.0],\n  [-0.5, -0.809017, 0.309017],\n  [-0.238856, -0.864188, 0.442863],\n  [-0.262866, -0.951056, 0.16246],\n  [-0.864188, -0.442863, 0.238856],\n  [-0.809017, -0.309017, 0.5],\n  [-0.688191, -0.587785, 0.425325],\n  [-0.681718, -0.147621, 0.716567],\n  [-0.442863, -0.238856, 0.864188],\n  [-0.587785, -0.425325, 0.688191],\n  [-0.309017, -0.5, 0.809017],\n  [-0.147621, -0.716567, 0.681718],\n  [-0.425325, -0.688191, 0.587785],\n  [-0.16246, -0.262866, 0.951056],\n  [0.442863, -0.238856, 0.864188],\n  [0.16246, -0.262866, 0.951056],\n  [0.309017, -0.5, 0.809017],\n  [0.147621, -0.716567, 0.681718],\n  [0.0, -0.525731, 0.850651],\n  [0.425325, -0.688191, 0.587785],\n  [0.587785, -0.425325, 0.688191],\n  [0.688191, -0.587785, 0.425325],\n  [-0.955423, 0.295242, 0.0],\n  [-0.951056, 0.16246, 0.262866],\n  [-1.0, 0.0, 0.0],\n  [-0.850651, 0.0, 0.525731],\n  [-0.955423, -0.295242, 0.0],\n  [-0.951056, -0.16246, 0.262866],\n  [-0.864188, 0.442863, -0.238856],\n  [-0.951056, 0.16246, -0.262866],\n  [-0.809017, 0.309017, -0.5],\n  [-0.864188, -0.442863, -0.238856],\n  [-0.951056, -0.16246, -0.262866],\n  [-0.809017, -0.309017, -0.5],\n  [-0.681718, 0.147621, -0.716567],\n  [-0.681718, -0.147621, -0.716567],\n  [-0.850651, 0.0, -0.525731],\n  [-0.688191, 0.587785, -0.425325],\n  [-0.587785, 0.425325, -0.688191],\n  [-0.425325, 0.688191, -0.587785],\n  [-0.425325, -0.688191, -0.587785],\n  [-0.587785, -0.425325, -0.688191],\n  [-0.688191, -0.587785, -0.425325],\n]\n\nclass MD2Loader extends Loader {\n  constructor(manager) {\n    super(manager)\n  }\n\n  load(url, onLoad, onProgress, onError) {\n    const scope = this\n\n    const loader = new FileLoader(scope.manager)\n    loader.setPath(scope.path)\n    loader.setResponseType('arraybuffer')\n    loader.setRequestHeader(scope.requestHeader)\n    loader.setWithCredentials(scope.withCredentials)\n    loader.load(\n      url,\n      function (buffer) {\n        try {\n          onLoad(scope.parse(buffer))\n        } catch (e) {\n          if (onError) {\n            onError(e)\n          } else {\n            console.error(e)\n          }\n\n          scope.manager.itemError(url)\n        }\n      },\n      onProgress,\n      onError,\n    )\n  }\n\n  parse(buffer) {\n    const data = new DataView(buffer)\n\n    // http://tfc.duke.free.fr/coding/md2-specs-en.html\n\n    const header = {}\n    const headerNames = [\n      'ident',\n      'version',\n      'skinwidth',\n      'skinheight',\n      'framesize',\n      'num_skins',\n      'num_vertices',\n      'num_st',\n      'num_tris',\n      'num_glcmds',\n      'num_frames',\n      'offset_skins',\n      'offset_st',\n      'offset_tris',\n      'offset_frames',\n      'offset_glcmds',\n      'offset_end',\n    ]\n\n    for (let i = 0; i < headerNames.length; i++) {\n      header[headerNames[i]] = data.getInt32(i * 4, true)\n    }\n\n    if (header.ident !== 844121161 || header.version !== 8) {\n      console.error('Not a valid MD2 file')\n      return\n    }\n\n    if (header.offset_end !== data.byteLength) {\n      console.error('Corrupted MD2 file')\n      return\n    }\n\n    //\n\n    const geometry = new BufferGeometry()\n\n    // uvs\n\n    const uvsTemp = []\n    let offset = header.offset_st\n\n    for (let i = 0, l = header.num_st; i < l; i++) {\n      const u = data.getInt16(offset + 0, true)\n      const v = data.getInt16(offset + 2, true)\n\n      uvsTemp.push(u / header.skinwidth, 1 - v / header.skinheight)\n\n      offset += 4\n    }\n\n    // triangles\n\n    offset = header.offset_tris\n\n    const vertexIndices = []\n    const uvIndices = []\n\n    for (let i = 0, l = header.num_tris; i < l; i++) {\n      vertexIndices.push(\n        data.getUint16(offset + 0, true),\n        data.getUint16(offset + 2, true),\n        data.getUint16(offset + 4, true),\n      )\n\n      uvIndices.push(\n        data.getUint16(offset + 6, true),\n        data.getUint16(offset + 8, true),\n        data.getUint16(offset + 10, true),\n      )\n\n      offset += 12\n    }\n\n    // frames\n\n    const translation = new Vector3()\n    const scale = new Vector3()\n    const string = []\n\n    const frames = []\n\n    offset = header.offset_frames\n\n    for (let i = 0, l = header.num_frames; i < l; i++) {\n      scale.set(data.getFloat32(offset + 0, true), data.getFloat32(offset + 4, true), data.getFloat32(offset + 8, true))\n\n      translation.set(\n        data.getFloat32(offset + 12, true),\n        data.getFloat32(offset + 16, true),\n        data.getFloat32(offset + 20, true),\n      )\n\n      offset += 24\n\n      for (let j = 0; j < 16; j++) {\n        const character = data.getUint8(offset + j, true)\n        if (character === 0) break\n\n        string[j] = character\n      }\n\n      const frame = {\n        name: String.fromCharCode.apply(null, string),\n        vertices: [],\n        normals: [],\n      }\n\n      offset += 16\n\n      for (let j = 0; j < header.num_vertices; j++) {\n        let x = data.getUint8(offset++, true)\n        let y = data.getUint8(offset++, true)\n        let z = data.getUint8(offset++, true)\n        const n = _normalData[data.getUint8(offset++, true)]\n\n        x = x * scale.x + translation.x\n        y = y * scale.y + translation.y\n        z = z * scale.z + translation.z\n\n        frame.vertices.push(x, z, y) // convert to Y-up\n        frame.normals.push(n[0], n[2], n[1]) // convert to Y-up\n      }\n\n      frames.push(frame)\n    }\n\n    // static\n\n    const positions = []\n    const normals = []\n    const uvs = []\n\n    const verticesTemp = frames[0].vertices\n    const normalsTemp = frames[0].normals\n\n    for (let i = 0, l = vertexIndices.length; i < l; i++) {\n      const vertexIndex = vertexIndices[i]\n      let stride = vertexIndex * 3\n\n      //\n\n      const x = verticesTemp[stride]\n      const y = verticesTemp[stride + 1]\n      const z = verticesTemp[stride + 2]\n\n      positions.push(x, y, z)\n\n      //\n\n      const nx = normalsTemp[stride]\n      const ny = normalsTemp[stride + 1]\n      const nz = normalsTemp[stride + 2]\n\n      normals.push(nx, ny, nz)\n\n      //\n\n      const uvIndex = uvIndices[i]\n      stride = uvIndex * 2\n\n      const u = uvsTemp[stride]\n      const v = uvsTemp[stride + 1]\n\n      uvs.push(u, v)\n    }\n\n    geometry.setAttribute('position', new Float32BufferAttribute(positions, 3))\n    geometry.setAttribute('normal', new Float32BufferAttribute(normals, 3))\n    geometry.setAttribute('uv', new Float32BufferAttribute(uvs, 2))\n\n    // animation\n\n    const morphPositions = []\n    const morphNormals = []\n\n    for (let i = 0, l = frames.length; i < l; i++) {\n      const frame = frames[i]\n      const attributeName = frame.name\n\n      if (frame.vertices.length > 0) {\n        const positions = []\n\n        for (let j = 0, jl = vertexIndices.length; j < jl; j++) {\n          const vertexIndex = vertexIndices[j]\n          const stride = vertexIndex * 3\n\n          const x = frame.vertices[stride]\n          const y = frame.vertices[stride + 1]\n          const z = frame.vertices[stride + 2]\n\n          positions.push(x, y, z)\n        }\n\n        const positionAttribute = new Float32BufferAttribute(positions, 3)\n        positionAttribute.name = attributeName\n\n        morphPositions.push(positionAttribute)\n      }\n\n      if (frame.normals.length > 0) {\n        const normals = []\n\n        for (let j = 0, jl = vertexIndices.length; j < jl; j++) {\n          const vertexIndex = vertexIndices[j]\n          const stride = vertexIndex * 3\n\n          const nx = frame.normals[stride]\n          const ny = frame.normals[stride + 1]\n          const nz = frame.normals[stride + 2]\n\n          normals.push(nx, ny, nz)\n        }\n\n        const normalAttribute = new Float32BufferAttribute(normals, 3)\n        normalAttribute.name = attributeName\n\n        morphNormals.push(normalAttribute)\n      }\n    }\n\n    geometry.morphAttributes.position = morphPositions\n    geometry.morphAttributes.normal = morphNormals\n    geometry.morphTargetsRelative = false\n\n    geometry.animations = AnimationClip.CreateClipsFromMorphTargetSequences(frames, 10)\n\n    return geometry\n  }\n}\n\nexport { MD2Loader }\n"], "mappings": ";AAEA,MAAMA,WAAA,GAAc,CAClB,CAAC,WAAW,GAAK,QAAQ,GACzB,CAAC,WAAW,UAAU,QAAQ,GAC9B,CAAC,WAAW,GAAK,QAAQ,GACzB,CAAC,WAAW,KAAK,QAAQ,GACzB,CAAC,UAAU,UAAU,QAAQ,GAC7B,CAAC,GAAK,GAAK,CAAG,GACd,CAAC,GAAK,UAAU,QAAQ,GACxB,CAAC,WAAW,UAAU,QAAQ,GAC9B,CAAC,UAAU,UAAU,QAAQ,GAC7B,CAAC,GAAK,UAAU,QAAQ,GACxB,CAAC,UAAU,KAAK,QAAQ,GACxB,CAAC,UAAU,GAAK,QAAQ,GACxB,CAAC,UAAU,GAAK,QAAQ,GACxB,CAAC,UAAU,UAAU,QAAQ,GAC7B,CAAC,SAAS,UAAU,QAAQ,GAC5B,CAAC,WAAW,UAAU,QAAQ,GAC9B,CAAC,WAAW,UAAU,GAAG,GACzB,CAAC,WAAW,UAAU,QAAQ,GAC9B,CAAC,WAAW,UAAU,CAAG,GACzB,CAAC,WAAW,UAAU,QAAQ,GAC9B,CAAC,WAAW,UAAU,QAAQ,GAC9B,CAAC,WAAW,UAAU,QAAQ,GAC9B,CAAC,MAAM,UAAU,QAAQ,GACzB,CAAC,WAAW,UAAU,QAAQ,GAC9B,CAAC,WAAW,UAAU,QAAQ,GAC9B,CAAC,WAAW,UAAU,SAAS,GAC/B,CAAC,MAAM,UAAU,SAAS,GAC1B,CAAC,WAAW,UAAU,CAAG,GACzB,CAAC,GAAK,UAAU,SAAS,GACzB,CAAC,WAAW,UAAU,SAAS,GAC/B,CAAC,GAAK,UAAU,SAAS,GACzB,CAAC,WAAW,UAAU,QAAQ,GAC9B,CAAC,GAAK,GAAK,CAAG,GACd,CAAC,GAAK,UAAU,QAAQ,GACxB,CAAC,WAAW,UAAU,OAAO,GAC7B,CAAC,UAAU,UAAU,QAAQ,GAC7B,CAAC,UAAU,UAAU,OAAO,GAC5B,CAAC,KAAK,UAAU,QAAQ,GACxB,CAAC,UAAU,UAAU,SAAS,GAC9B,CAAC,UAAU,UAAU,QAAQ,GAC7B,CAAC,KAAK,UAAU,SAAS,GACzB,CAAC,UAAU,UAAU,CAAG,GACxB,CAAC,UAAU,UAAU,QAAQ,GAC7B,CAAC,UAAU,UAAU,SAAS,GAC9B,CAAC,UAAU,UAAU,CAAG,GACxB,CAAC,UAAU,UAAU,QAAQ,GAC7B,CAAC,UAAU,UAAU,QAAQ,GAC7B,CAAC,UAAU,UAAU,QAAQ,GAC7B,CAAC,UAAU,UAAU,GAAG,GACxB,CAAC,UAAU,UAAU,QAAQ,GAC7B,CAAC,UAAU,UAAU,QAAQ,GAC7B,CAAC,UAAU,UAAU,CAAG,GACxB,CAAC,GAAK,GAAK,CAAG,GACd,CAAC,UAAU,SAAS,QAAQ,GAC5B,CAAC,UAAU,WAAW,CAAG,GACzB,CAAC,UAAU,WAAW,CAAG,GACzB,CAAC,UAAU,WAAW,QAAQ,GAC9B,CAAC,UAAU,UAAU,QAAQ,GAC7B,CAAC,UAAU,WAAW,GAAG,GACzB,CAAC,UAAU,WAAW,QAAQ,GAC9B,CAAC,UAAU,GAAK,QAAQ,GACxB,CAAC,UAAU,UAAU,SAAS,GAC9B,CAAC,UAAU,UAAU,IAAI,GACzB,CAAC,UAAU,SAAS,SAAS,GAC7B,CAAC,UAAU,GAAK,SAAS,GACzB,CAAC,UAAU,UAAU,SAAS,GAC9B,CAAC,UAAU,WAAW,SAAS,GAC/B,CAAC,UAAU,GAAK,SAAS,GACzB,CAAC,UAAU,WAAW,IAAI,GAC1B,CAAC,UAAU,WAAW,SAAS,GAC/B,CAAC,UAAU,UAAU,SAAS,GAC9B,CAAC,UAAU,UAAU,SAAS,GAC9B,CAAC,UAAU,KAAK,SAAS,GACzB,CAAC,UAAU,UAAU,SAAS,GAC9B,CAAC,UAAU,UAAU,SAAS,GAC9B,CAAC,UAAU,UAAU,SAAS,GAC9B,CAAC,UAAU,UAAU,SAAS,GAC9B,CAAC,WAAW,UAAU,SAAS,GAC/B,CAAC,WAAW,KAAK,SAAS,GAC1B,CAAC,GAAK,UAAU,SAAS,GACzB,CAAC,WAAW,GAAK,SAAS,GAC1B,CAAC,WAAW,UAAU,SAAS,GAC/B,CAAC,WAAW,GAAK,SAAS,GAC1B,CAAC,UAAU,UAAU,SAAS,GAC9B,CAAC,GAAK,GAAK,EAAI,GACf,CAAC,UAAU,GAAK,SAAS,GACzB,CAAC,SAAS,UAAU,SAAS,GAC7B,CAAC,WAAW,WAAW,SAAS,GAChC,CAAC,WAAW,MAAM,SAAS,GAC3B,CAAC,UAAU,WAAW,SAAS,GAC/B,CAAC,GAAK,WAAW,SAAS,GAC1B,CAAC,WAAW,WAAW,SAAS,GAChC,CAAC,UAAU,WAAW,SAAS,GAC/B,CAAC,GAAK,WAAW,SAAS,GAC1B,CAAC,UAAU,MAAM,SAAS,GAC1B,CAAC,UAAU,WAAW,SAAS,GAC/B,CAAC,SAAS,WAAW,SAAS,GAC9B,CAAC,UAAU,WAAW,SAAS,GAC/B,CAAC,KAAK,WAAW,SAAS,GAC1B,CAAC,UAAU,WAAW,SAAS,GAC/B,CAAC,UAAU,WAAW,SAAS,GAC/B,CAAC,UAAU,WAAW,SAAS,GAC/B,CAAC,UAAU,WAAW,SAAS,GAC/B,CAAC,GAAK,WAAW,SAAS,GAC1B,CAAC,GAAK,IAAM,CAAG,GACf,CAAC,UAAU,WAAW,QAAQ,GAC9B,CAAC,GAAK,WAAW,QAAQ,GACzB,CAAC,GAAK,WAAW,QAAQ,GACzB,CAAC,UAAU,WAAW,QAAQ,GAC9B,CAAC,UAAU,WAAW,OAAO,GAC7B,CAAC,KAAK,WAAW,QAAQ,GACzB,CAAC,UAAU,WAAW,QAAQ,GAC9B,CAAC,UAAU,WAAW,CAAG,GACzB,CAAC,WAAW,WAAW,SAAS,GAChC,CAAC,MAAM,WAAW,SAAS,GAC3B,CAAC,WAAW,WAAW,QAAQ,GAC/B,CAAC,WAAW,WAAW,CAAG,GAC1B,CAAC,WAAW,WAAW,SAAS,GAChC,CAAC,WAAW,WAAW,QAAQ,GAC/B,CAAC,WAAW,WAAW,CAAG,GAC1B,CAAC,MAAM,WAAW,QAAQ,GAC1B,CAAC,WAAW,WAAW,QAAQ,GAC/B,CAAC,WAAW,WAAW,OAAO,GAC9B,CAAC,WAAW,WAAW,QAAQ,GAC/B,CAAC,WAAW,WAAW,GAAG,GAC1B,CAAC,WAAW,WAAW,QAAQ,GAC/B,CAAC,WAAW,WAAW,QAAQ,GAC/B,CAAC,WAAW,WAAW,QAAQ,GAC/B,CAAC,WAAW,WAAW,QAAQ,GAC/B,CAAC,WAAW,MAAM,QAAQ,GAC1B,CAAC,WAAW,WAAW,QAAQ,GAC/B,CAAC,WAAW,WAAW,QAAQ,GAC/B,CAAC,UAAU,WAAW,QAAQ,GAC9B,CAAC,UAAU,WAAW,QAAQ,GAC9B,CAAC,SAAS,WAAW,QAAQ,GAC7B,CAAC,UAAU,MAAM,QAAQ,GACzB,CAAC,UAAU,WAAW,QAAQ,GAC9B,CAAC,GAAK,WAAW,QAAQ,GACzB,CAAC,UAAU,WAAW,QAAQ,GAC9B,CAAC,UAAU,WAAW,QAAQ,GAC9B,CAAC,UAAU,WAAW,QAAQ,GAC9B,CAAC,WAAW,UAAU,CAAG,GACzB,CAAC,WAAW,SAAS,QAAQ,GAC7B,CAAC,IAAM,GAAK,CAAG,GACf,CAAC,WAAW,GAAK,QAAQ,GACzB,CAAC,WAAW,WAAW,CAAG,GAC1B,CAAC,WAAW,UAAU,QAAQ,GAC9B,CAAC,WAAW,UAAU,SAAS,GAC/B,CAAC,WAAW,SAAS,SAAS,GAC9B,CAAC,WAAW,UAAU,IAAI,GAC1B,CAAC,WAAW,WAAW,SAAS,GAChC,CAAC,WAAW,UAAU,SAAS,GAC/B,CAAC,WAAW,WAAW,IAAI,GAC3B,CAAC,WAAW,UAAU,SAAS,GAC/B,CAAC,WAAW,WAAW,SAAS,GAChC,CAAC,WAAW,GAAK,SAAS,GAC1B,CAAC,WAAW,UAAU,SAAS,GAC/B,CAAC,WAAW,UAAU,SAAS,GAC/B,CAAC,WAAW,UAAU,SAAS,GAC/B,CAAC,WAAW,WAAW,SAAS,GAChC,CAAC,WAAW,WAAW,SAAS,GAChC,CAAC,WAAW,WAAW,SAAS,EAClC;AAEA,MAAMC,SAAA,SAAkBC,MAAA,CAAO;EAC7BC,YAAYC,OAAA,EAAS;IACnB,MAAMA,OAAO;EACd;EAEDC,KAAKC,GAAA,EAAKC,MAAA,EAAQC,UAAA,EAAYC,OAAA,EAAS;IACrC,MAAMC,KAAA,GAAQ;IAEd,MAAMC,MAAA,GAAS,IAAIC,UAAA,CAAWF,KAAA,CAAMN,OAAO;IAC3CO,MAAA,CAAOE,OAAA,CAAQH,KAAA,CAAMI,IAAI;IACzBH,MAAA,CAAOI,eAAA,CAAgB,aAAa;IACpCJ,MAAA,CAAOK,gBAAA,CAAiBN,KAAA,CAAMO,aAAa;IAC3CN,MAAA,CAAOO,kBAAA,CAAmBR,KAAA,CAAMS,eAAe;IAC/CR,MAAA,CAAON,IAAA,CACLC,GAAA,EACA,UAAUc,MAAA,EAAQ;MAChB,IAAI;QACFb,MAAA,CAAOG,KAAA,CAAMW,KAAA,CAAMD,MAAM,CAAC;MAC3B,SAAQE,CAAA,EAAP;QACA,IAAIb,OAAA,EAAS;UACXA,OAAA,CAAQa,CAAC;QACrB,OAAiB;UACLC,OAAA,CAAQC,KAAA,CAAMF,CAAC;QAChB;QAEDZ,KAAA,CAAMN,OAAA,CAAQqB,SAAA,CAAUnB,GAAG;MAC5B;IACF,GACDE,UAAA,EACAC,OACD;EACF;EAEDY,MAAMD,MAAA,EAAQ;IACZ,MAAMM,IAAA,GAAO,IAAIC,QAAA,CAASP,MAAM;IAIhC,MAAMQ,MAAA,GAAS,CAAE;IACjB,MAAMC,WAAA,GAAc,CAClB,SACA,WACA,aACA,cACA,aACA,aACA,gBACA,UACA,YACA,cACA,cACA,gBACA,aACA,eACA,iBACA,iBACA,aACD;IAED,SAASC,CAAA,GAAI,GAAGA,CAAA,GAAID,WAAA,CAAYE,MAAA,EAAQD,CAAA,IAAK;MAC3CF,MAAA,CAAOC,WAAA,CAAYC,CAAC,CAAC,IAAIJ,IAAA,CAAKM,QAAA,CAASF,CAAA,GAAI,GAAG,IAAI;IACnD;IAED,IAAIF,MAAA,CAAOK,KAAA,KAAU,aAAaL,MAAA,CAAOM,OAAA,KAAY,GAAG;MACtDX,OAAA,CAAQC,KAAA,CAAM,sBAAsB;MACpC;IACD;IAED,IAAII,MAAA,CAAOO,UAAA,KAAeT,IAAA,CAAKU,UAAA,EAAY;MACzCb,OAAA,CAAQC,KAAA,CAAM,oBAAoB;MAClC;IACD;IAID,MAAMa,QAAA,GAAW,IAAIC,cAAA,CAAgB;IAIrC,MAAMC,OAAA,GAAU,EAAE;IAClB,IAAIC,MAAA,GAASZ,MAAA,CAAOa,SAAA;IAEpB,SAASX,CAAA,GAAI,GAAGY,CAAA,GAAId,MAAA,CAAOe,MAAA,EAAQb,CAAA,GAAIY,CAAA,EAAGZ,CAAA,IAAK;MAC7C,MAAMc,CAAA,GAAIlB,IAAA,CAAKmB,QAAA,CAASL,MAAA,GAAS,GAAG,IAAI;MACxC,MAAMM,CAAA,GAAIpB,IAAA,CAAKmB,QAAA,CAASL,MAAA,GAAS,GAAG,IAAI;MAExCD,OAAA,CAAQQ,IAAA,CAAKH,CAAA,GAAIhB,MAAA,CAAOoB,SAAA,EAAW,IAAIF,CAAA,GAAIlB,MAAA,CAAOqB,UAAU;MAE5DT,MAAA,IAAU;IACX;IAIDA,MAAA,GAASZ,MAAA,CAAOsB,WAAA;IAEhB,MAAMC,aAAA,GAAgB,EAAE;IACxB,MAAMC,SAAA,GAAY,EAAE;IAEpB,SAAStB,CAAA,GAAI,GAAGY,CAAA,GAAId,MAAA,CAAOyB,QAAA,EAAUvB,CAAA,GAAIY,CAAA,EAAGZ,CAAA,IAAK;MAC/CqB,aAAA,CAAcJ,IAAA,CACZrB,IAAA,CAAK4B,SAAA,CAAUd,MAAA,GAAS,GAAG,IAAI,GAC/Bd,IAAA,CAAK4B,SAAA,CAAUd,MAAA,GAAS,GAAG,IAAI,GAC/Bd,IAAA,CAAK4B,SAAA,CAAUd,MAAA,GAAS,GAAG,IAAI,CAChC;MAEDY,SAAA,CAAUL,IAAA,CACRrB,IAAA,CAAK4B,SAAA,CAAUd,MAAA,GAAS,GAAG,IAAI,GAC/Bd,IAAA,CAAK4B,SAAA,CAAUd,MAAA,GAAS,GAAG,IAAI,GAC/Bd,IAAA,CAAK4B,SAAA,CAAUd,MAAA,GAAS,IAAI,IAAI,CACjC;MAEDA,MAAA,IAAU;IACX;IAID,MAAMe,WAAA,GAAc,IAAIC,OAAA,CAAS;IACjC,MAAMC,KAAA,GAAQ,IAAID,OAAA,CAAS;IAC3B,MAAME,MAAA,GAAS,EAAE;IAEjB,MAAMC,MAAA,GAAS,EAAE;IAEjBnB,MAAA,GAASZ,MAAA,CAAOgC,aAAA;IAEhB,SAAS9B,CAAA,GAAI,GAAGY,CAAA,GAAId,MAAA,CAAOiC,UAAA,EAAY/B,CAAA,GAAIY,CAAA,EAAGZ,CAAA,IAAK;MACjD2B,KAAA,CAAMK,GAAA,CAAIpC,IAAA,CAAKqC,UAAA,CAAWvB,MAAA,GAAS,GAAG,IAAI,GAAGd,IAAA,CAAKqC,UAAA,CAAWvB,MAAA,GAAS,GAAG,IAAI,GAAGd,IAAA,CAAKqC,UAAA,CAAWvB,MAAA,GAAS,GAAG,IAAI,CAAC;MAEjHe,WAAA,CAAYO,GAAA,CACVpC,IAAA,CAAKqC,UAAA,CAAWvB,MAAA,GAAS,IAAI,IAAI,GACjCd,IAAA,CAAKqC,UAAA,CAAWvB,MAAA,GAAS,IAAI,IAAI,GACjCd,IAAA,CAAKqC,UAAA,CAAWvB,MAAA,GAAS,IAAI,IAAI,CAClC;MAEDA,MAAA,IAAU;MAEV,SAASwB,CAAA,GAAI,GAAGA,CAAA,GAAI,IAAIA,CAAA,IAAK;QAC3B,MAAMC,SAAA,GAAYvC,IAAA,CAAKwC,QAAA,CAAS1B,MAAA,GAASwB,CAAA,EAAG,IAAI;QAChD,IAAIC,SAAA,KAAc,GAAG;QAErBP,MAAA,CAAOM,CAAC,IAAIC,SAAA;MACb;MAED,MAAME,KAAA,GAAQ;QACZC,IAAA,EAAMC,MAAA,CAAOC,YAAA,CAAaC,KAAA,CAAM,MAAMb,MAAM;QAC5Cc,QAAA,EAAU,EAAE;QACZC,OAAA,EAAS;MACV;MAEDjC,MAAA,IAAU;MAEV,SAASwB,CAAA,GAAI,GAAGA,CAAA,GAAIpC,MAAA,CAAO8C,YAAA,EAAcV,CAAA,IAAK;QAC5C,IAAIW,CAAA,GAAIjD,IAAA,CAAKwC,QAAA,CAAS1B,MAAA,IAAU,IAAI;QACpC,IAAIoC,CAAA,GAAIlD,IAAA,CAAKwC,QAAA,CAAS1B,MAAA,IAAU,IAAI;QACpC,IAAIqC,CAAA,GAAInD,IAAA,CAAKwC,QAAA,CAAS1B,MAAA,IAAU,IAAI;QACpC,MAAMsC,CAAA,GAAI9E,WAAA,CAAY0B,IAAA,CAAKwC,QAAA,CAAS1B,MAAA,IAAU,IAAI,CAAC;QAEnDmC,CAAA,GAAIA,CAAA,GAAIlB,KAAA,CAAMkB,CAAA,GAAIpB,WAAA,CAAYoB,CAAA;QAC9BC,CAAA,GAAIA,CAAA,GAAInB,KAAA,CAAMmB,CAAA,GAAIrB,WAAA,CAAYqB,CAAA;QAC9BC,CAAA,GAAIA,CAAA,GAAIpB,KAAA,CAAMoB,CAAA,GAAItB,WAAA,CAAYsB,CAAA;QAE9BV,KAAA,CAAMK,QAAA,CAASzB,IAAA,CAAK4B,CAAA,EAAGE,CAAA,EAAGD,CAAC;QAC3BT,KAAA,CAAMM,OAAA,CAAQ1B,IAAA,CAAK+B,CAAA,CAAE,CAAC,GAAGA,CAAA,CAAE,CAAC,GAAGA,CAAA,CAAE,CAAC,CAAC;MACpC;MAEDnB,MAAA,CAAOZ,IAAA,CAAKoB,KAAK;IAClB;IAID,MAAMY,SAAA,GAAY,EAAE;IACpB,MAAMN,OAAA,GAAU,EAAE;IAClB,MAAMO,GAAA,GAAM,EAAE;IAEd,MAAMC,YAAA,GAAetB,MAAA,CAAO,CAAC,EAAEa,QAAA;IAC/B,MAAMU,WAAA,GAAcvB,MAAA,CAAO,CAAC,EAAEc,OAAA;IAE9B,SAAS3C,CAAA,GAAI,GAAGY,CAAA,GAAIS,aAAA,CAAcpB,MAAA,EAAQD,CAAA,GAAIY,CAAA,EAAGZ,CAAA,IAAK;MACpD,MAAMqD,WAAA,GAAchC,aAAA,CAAcrB,CAAC;MACnC,IAAIsD,MAAA,GAASD,WAAA,GAAc;MAI3B,MAAMR,CAAA,GAAIM,YAAA,CAAaG,MAAM;MAC7B,MAAMR,CAAA,GAAIK,YAAA,CAAaG,MAAA,GAAS,CAAC;MACjC,MAAMP,CAAA,GAAII,YAAA,CAAaG,MAAA,GAAS,CAAC;MAEjCL,SAAA,CAAUhC,IAAA,CAAK4B,CAAA,EAAGC,CAAA,EAAGC,CAAC;MAItB,MAAMQ,EAAA,GAAKH,WAAA,CAAYE,MAAM;MAC7B,MAAME,EAAA,GAAKJ,WAAA,CAAYE,MAAA,GAAS,CAAC;MACjC,MAAMG,EAAA,GAAKL,WAAA,CAAYE,MAAA,GAAS,CAAC;MAEjCX,OAAA,CAAQ1B,IAAA,CAAKsC,EAAA,EAAIC,EAAA,EAAIC,EAAE;MAIvB,MAAMC,OAAA,GAAUpC,SAAA,CAAUtB,CAAC;MAC3BsD,MAAA,GAASI,OAAA,GAAU;MAEnB,MAAM5C,CAAA,GAAIL,OAAA,CAAQ6C,MAAM;MACxB,MAAMtC,CAAA,GAAIP,OAAA,CAAQ6C,MAAA,GAAS,CAAC;MAE5BJ,GAAA,CAAIjC,IAAA,CAAKH,CAAA,EAAGE,CAAC;IACd;IAEDT,QAAA,CAASoD,YAAA,CAAa,YAAY,IAAIC,sBAAA,CAAuBX,SAAA,EAAW,CAAC,CAAC;IAC1E1C,QAAA,CAASoD,YAAA,CAAa,UAAU,IAAIC,sBAAA,CAAuBjB,OAAA,EAAS,CAAC,CAAC;IACtEpC,QAAA,CAASoD,YAAA,CAAa,MAAM,IAAIC,sBAAA,CAAuBV,GAAA,EAAK,CAAC,CAAC;IAI9D,MAAMW,cAAA,GAAiB,EAAE;IACzB,MAAMC,YAAA,GAAe,EAAE;IAEvB,SAAS9D,CAAA,GAAI,GAAGY,CAAA,GAAIiB,MAAA,CAAO5B,MAAA,EAAQD,CAAA,GAAIY,CAAA,EAAGZ,CAAA,IAAK;MAC7C,MAAMqC,KAAA,GAAQR,MAAA,CAAO7B,CAAC;MACtB,MAAM+D,aAAA,GAAgB1B,KAAA,CAAMC,IAAA;MAE5B,IAAID,KAAA,CAAMK,QAAA,CAASzC,MAAA,GAAS,GAAG;QAC7B,MAAM+D,UAAA,GAAY,EAAE;QAEpB,SAAS9B,CAAA,GAAI,GAAG+B,EAAA,GAAK5C,aAAA,CAAcpB,MAAA,EAAQiC,CAAA,GAAI+B,EAAA,EAAI/B,CAAA,IAAK;UACtD,MAAMmB,WAAA,GAAchC,aAAA,CAAca,CAAC;UACnC,MAAMoB,MAAA,GAASD,WAAA,GAAc;UAE7B,MAAMR,CAAA,GAAIR,KAAA,CAAMK,QAAA,CAASY,MAAM;UAC/B,MAAMR,CAAA,GAAIT,KAAA,CAAMK,QAAA,CAASY,MAAA,GAAS,CAAC;UACnC,MAAMP,CAAA,GAAIV,KAAA,CAAMK,QAAA,CAASY,MAAA,GAAS,CAAC;UAEnCU,UAAA,CAAU/C,IAAA,CAAK4B,CAAA,EAAGC,CAAA,EAAGC,CAAC;QACvB;QAED,MAAMmB,iBAAA,GAAoB,IAAIN,sBAAA,CAAuBI,UAAA,EAAW,CAAC;QACjEE,iBAAA,CAAkB5B,IAAA,GAAOyB,aAAA;QAEzBF,cAAA,CAAe5C,IAAA,CAAKiD,iBAAiB;MACtC;MAED,IAAI7B,KAAA,CAAMM,OAAA,CAAQ1C,MAAA,GAAS,GAAG;QAC5B,MAAMkE,QAAA,GAAU,EAAE;QAElB,SAASjC,CAAA,GAAI,GAAG+B,EAAA,GAAK5C,aAAA,CAAcpB,MAAA,EAAQiC,CAAA,GAAI+B,EAAA,EAAI/B,CAAA,IAAK;UACtD,MAAMmB,WAAA,GAAchC,aAAA,CAAca,CAAC;UACnC,MAAMoB,MAAA,GAASD,WAAA,GAAc;UAE7B,MAAME,EAAA,GAAKlB,KAAA,CAAMM,OAAA,CAAQW,MAAM;UAC/B,MAAME,EAAA,GAAKnB,KAAA,CAAMM,OAAA,CAAQW,MAAA,GAAS,CAAC;UACnC,MAAMG,EAAA,GAAKpB,KAAA,CAAMM,OAAA,CAAQW,MAAA,GAAS,CAAC;UAEnCa,QAAA,CAAQlD,IAAA,CAAKsC,EAAA,EAAIC,EAAA,EAAIC,EAAE;QACxB;QAED,MAAMW,eAAA,GAAkB,IAAIR,sBAAA,CAAuBO,QAAA,EAAS,CAAC;QAC7DC,eAAA,CAAgB9B,IAAA,GAAOyB,aAAA;QAEvBD,YAAA,CAAa7C,IAAA,CAAKmD,eAAe;MAClC;IACF;IAED7D,QAAA,CAAS8D,eAAA,CAAgBC,QAAA,GAAWT,cAAA;IACpCtD,QAAA,CAAS8D,eAAA,CAAgBE,MAAA,GAAST,YAAA;IAClCvD,QAAA,CAASiE,oBAAA,GAAuB;IAEhCjE,QAAA,CAASkE,UAAA,GAAaC,aAAA,CAAcC,mCAAA,CAAoC9C,MAAA,EAAQ,EAAE;IAElF,OAAOtB,QAAA;EACR;AACH", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}