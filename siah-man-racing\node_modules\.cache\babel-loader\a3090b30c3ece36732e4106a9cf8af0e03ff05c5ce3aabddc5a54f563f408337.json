{"ast": null, "code": "var __defProp = Object.defineProperty;\nvar __defNormalProp = (obj, key, value) => key in obj ? __defProp(obj, key, {\n  enumerable: true,\n  configurable: true,\n  writable: true,\n  value\n}) : obj[key] = value;\nvar __publicField = (obj, key, value) => {\n  __defNormalProp(obj, typeof key !== \"symbol\" ? key + \"\" : key, value);\n  return value;\n};\nimport { Vector2, ShaderMaterial } from \"three\";\nimport { Pass, FullScreenQuad } from \"./Pass.js\";\nclass WaterPass extends Pass {\n  constructor() {\n    super();\n    __publicField(this, \"material\");\n    __publicField(this, \"fsQuad\");\n    __publicField(this, \"factor\");\n    __publicField(this, \"time\");\n    __publicField(this, \"uniforms\");\n    this.uniforms = {\n      tex: {\n        value: null\n      },\n      time: {\n        value: 0\n      },\n      factor: {\n        value: 0\n      },\n      resolution: {\n        value: new Vector2(64, 64)\n      }\n    };\n    this.material = new ShaderMaterial({\n      uniforms: this.uniforms,\n      vertexShader: `\n      varying vec2 vUv;\n      void main(){  \n        vUv = uv; \n        vec4 modelViewPosition = modelViewMatrix * vec4(position, 1.0);\n        gl_Position = projectionMatrix * modelViewPosition;\n      }`,\n      fragmentShader: `\n      uniform float time;\n      uniform float factor;\n      uniform vec2 resolution;\n      uniform sampler2D tex;\n      varying vec2 vUv;\n      void main() {  \n        vec2 uv1 = vUv;\n        vec2 uv = gl_FragCoord.xy/resolution.xy;\n        float frequency = 6.0 * factor;\n        float amplitude = 0.015 * factor;\n        float x = uv1.y * frequency + time * .7; \n        float y = uv1.x * frequency + time * .3;\n        uv1.x += cos(x+y) * amplitude * cos(y);\n        uv1.y += sin(x-y) * amplitude * cos(y);\n        vec4 rgba = texture2D(tex, uv1);\n        gl_FragColor = rgba;\n      }`\n    });\n    this.fsQuad = new FullScreenQuad(this.material);\n    this.factor = 0;\n    this.time = 0;\n  }\n  render(renderer, writeBuffer, readBuffer) {\n    this.uniforms[\"tex\"].value = readBuffer.texture;\n    this.uniforms[\"time\"].value = this.time;\n    this.uniforms[\"factor\"].value = this.factor;\n    if (this.renderToScreen) {\n      renderer.setRenderTarget(null);\n      this.fsQuad.render(renderer);\n    } else {\n      renderer.setRenderTarget(writeBuffer);\n      if (this.clear) renderer.clear();\n      this.fsQuad.render(renderer);\n    }\n  }\n}\nexport { WaterPass };", "map": {"version": 3, "names": ["WaterPass", "Pass", "constructor", "__publicField", "uniforms", "tex", "value", "time", "factor", "resolution", "Vector2", "material", "ShaderMaterial", "vertexShader", "fragmentShader", "fsQuad", "FullScreenQuad", "render", "renderer", "writeBuffer", "readBuffer", "texture", "renderToScreen", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "clear"], "sources": ["C:\\Users\\<USER>\\Documents\\Apps_Creations\\SiahMan_Racing4Speed_WebGame\\siah-man-racing\\node_modules\\src\\postprocessing\\WaterPass.ts"], "sourcesContent": ["import { Web<PERSON><PERSON>ender<PERSON>, WebGLRenderTarget, ShaderMaterial, Vector2, IUniform, Texture } from 'three'\nimport { Pass, FullScreenQuad } from '../postprocessing/Pass'\n\n/**\n * Simple underwater shader\n * \n \n parameters:\n tex: texture\n time: this should increase with time passing\n factor: to what degree will the shader distort the screen \n\n explaination:\n the shader is quite simple\n it chooses a center and start from there make pixels around it to \"swell\" then \"shrink\" then \"swell\"...\n this is of course nothing really similar to underwater scene\n but you can combine several this shaders together to create the effect you need...\n And yes, this shader could be used for something other than underwater effect, for example, magnifier effect :)\n\n * <AUTHOR> Wang\n */\n\nclass WaterPass extends Pass {\n  public material: ShaderMaterial\n  public fsQuad: FullScreenQuad\n  public factor: number\n  public time: number\n  public uniforms: {\n    tex: IUniform<Texture>\n    time: IUniform<number>\n    factor: IUniform<number>\n    resolution: IUniform<Vector2>\n  }\n\n  constructor() {\n    super()\n    this.uniforms = {\n      tex: { value: null! },\n      time: { value: 0.0 },\n      factor: { value: 0.0 },\n      resolution: { value: new Vector2(64, 64) },\n    }\n    this.material = new ShaderMaterial({\n      uniforms: this.uniforms,\n      vertexShader: `\n      varying vec2 vUv;\n      void main(){  \n        vUv = uv; \n        vec4 modelViewPosition = modelViewMatrix * vec4(position, 1.0);\n        gl_Position = projectionMatrix * modelViewPosition;\n      }`,\n      fragmentShader: `\n      uniform float time;\n      uniform float factor;\n      uniform vec2 resolution;\n      uniform sampler2D tex;\n      varying vec2 vUv;\n      void main() {  \n        vec2 uv1 = vUv;\n        vec2 uv = gl_FragCoord.xy/resolution.xy;\n        float frequency = 6.0 * factor;\n        float amplitude = 0.015 * factor;\n        float x = uv1.y * frequency + time * .7; \n        float y = uv1.x * frequency + time * .3;\n        uv1.x += cos(x+y) * amplitude * cos(y);\n        uv1.y += sin(x-y) * amplitude * cos(y);\n        vec4 rgba = texture2D(tex, uv1);\n        gl_FragColor = rgba;\n      }`,\n    })\n    this.fsQuad = new FullScreenQuad(this.material)\n    this.factor = 0\n    this.time = 0\n  }\n\n  public render(renderer: WebGLRenderer, writeBuffer: WebGLRenderTarget, readBuffer: WebGLRenderTarget): void {\n    this.uniforms['tex'].value = readBuffer.texture\n    this.uniforms['time'].value = this.time\n    this.uniforms['factor'].value = this.factor\n    if (this.renderToScreen) {\n      renderer.setRenderTarget(null)\n      this.fsQuad.render(renderer)\n    } else {\n      renderer.setRenderTarget(writeBuffer)\n      if (this.clear) renderer.clear()\n      this.fsQuad.render(renderer)\n    }\n  }\n}\n\nexport { WaterPass }\n"], "mappings": ";;;;;;;;;;;;;AAsBA,MAAMA,SAAA,SAAkBC,IAAA,CAAK;EAY3BC,YAAA,EAAc;IACN;IAZDC,aAAA;IACAA,aAAA;IACAA,aAAA;IACAA,aAAA;IACAA,aAAA;IASL,KAAKC,QAAA,GAAW;MACdC,GAAA,EAAK;QAAEC,KAAA,EAAO;MAAM;MACpBC,IAAA,EAAM;QAAED,KAAA,EAAO;MAAI;MACnBE,MAAA,EAAQ;QAAEF,KAAA,EAAO;MAAI;MACrBG,UAAA,EAAY;QAAEH,KAAA,EAAO,IAAII,OAAA,CAAQ,IAAI,EAAE;MAAE;IAAA;IAEtC,KAAAC,QAAA,GAAW,IAAIC,cAAA,CAAe;MACjCR,QAAA,EAAU,KAAKA,QAAA;MACfS,YAAA,EAAc;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;MAOdC,cAAA,EAAgB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;IAAA,CAkBjB;IACD,KAAKC,MAAA,GAAS,IAAIC,cAAA,CAAe,KAAKL,QAAQ;IAC9C,KAAKH,MAAA,GAAS;IACd,KAAKD,IAAA,GAAO;EACd;EAEOU,OAAOC,QAAA,EAAyBC,WAAA,EAAgCC,UAAA,EAAqC;IAC1G,KAAKhB,QAAA,CAAS,KAAK,EAAEE,KAAA,GAAQc,UAAA,CAAWC,OAAA;IACxC,KAAKjB,QAAA,CAAS,MAAM,EAAEE,KAAA,GAAQ,KAAKC,IAAA;IACnC,KAAKH,QAAA,CAAS,QAAQ,EAAEE,KAAA,GAAQ,KAAKE,MAAA;IACrC,IAAI,KAAKc,cAAA,EAAgB;MACvBJ,QAAA,CAASK,eAAA,CAAgB,IAAI;MACxB,KAAAR,MAAA,CAAOE,MAAA,CAAOC,QAAQ;IAAA,OACtB;MACLA,QAAA,CAASK,eAAA,CAAgBJ,WAAW;MACpC,IAAI,KAAKK,KAAA,EAAON,QAAA,CAASM,KAAA,CAAM;MAC1B,KAAAT,MAAA,CAAOE,MAAA,CAAOC,QAAQ;IAC7B;EACF;AACF", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}