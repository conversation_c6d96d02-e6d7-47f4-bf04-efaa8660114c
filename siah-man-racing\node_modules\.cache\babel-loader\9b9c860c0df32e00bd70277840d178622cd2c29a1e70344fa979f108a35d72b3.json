{"ast": null, "code": "import { <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, Vector3, <PERSON>uatern<PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>, Color } from \"three\";\nclass RollerCoasterGeometry extends BufferGeometry {\n  constructor(curve, divisions) {\n    super();\n    const vertices = [];\n    const normals = [];\n    const colors = [];\n    const color1 = [1, 1, 1];\n    const color2 = [1, 1, 0];\n    const up = new Vector3(0, 1, 0);\n    const forward = new Vector3();\n    const right = new Vector3();\n    const quaternion = new Quaternion();\n    const prevQuaternion = new Quaternion();\n    prevQuaternion.setFromAxisAngle(up, Math.PI / 2);\n    const point = new Vector3();\n    const prevPoint = new Vector3();\n    prevPoint.copy(curve.getPointAt(0));\n    const step = [new Vector3(-0.225, 0, 0), new Vector3(0, -0.05, 0), new Vector3(0, -0.175, 0), new Vector3(0, -0.05, 0), new Vector3(0.225, 0, 0), new Vector3(0, -0.175, 0)];\n    const PI2 = Math.PI * 2;\n    let sides = 5;\n    const tube1 = [];\n    for (let i = 0; i < sides; i++) {\n      const angle = i / sides * PI2;\n      tube1.push(new Vector3(Math.sin(angle) * 0.06, Math.cos(angle) * 0.06, 0));\n    }\n    sides = 6;\n    const tube2 = [];\n    for (let i = 0; i < sides; i++) {\n      const angle = i / sides * PI2;\n      tube2.push(new Vector3(Math.sin(angle) * 0.025, Math.cos(angle) * 0.025, 0));\n    }\n    const vector = new Vector3();\n    const normal = new Vector3();\n    function drawShape(shape, color) {\n      normal.set(0, 0, -1).applyQuaternion(quaternion);\n      for (let j = 0; j < shape.length; j++) {\n        vector.copy(shape[j]);\n        vector.applyQuaternion(quaternion);\n        vector.add(point);\n        vertices.push(vector.x, vector.y, vector.z);\n        normals.push(normal.x, normal.y, normal.z);\n        colors.push(color[0], color[1], color[2]);\n      }\n      normal.set(0, 0, 1).applyQuaternion(quaternion);\n      for (let j = shape.length - 1; j >= 0; j--) {\n        vector.copy(shape[j]);\n        vector.applyQuaternion(quaternion);\n        vector.add(point);\n        vertices.push(vector.x, vector.y, vector.z);\n        normals.push(normal.x, normal.y, normal.z);\n        colors.push(color[0], color[1], color[2]);\n      }\n    }\n    const vector1 = new Vector3();\n    const vector2 = new Vector3();\n    const vector3 = new Vector3();\n    const vector4 = new Vector3();\n    const normal1 = new Vector3();\n    const normal2 = new Vector3();\n    const normal3 = new Vector3();\n    const normal4 = new Vector3();\n    function extrudeShape(shape, offset2, color) {\n      for (let j = 0, jl = shape.length; j < jl; j++) {\n        const point1 = shape[j];\n        const point2 = shape[(j + 1) % jl];\n        vector1.copy(point1).add(offset2);\n        vector1.applyQuaternion(quaternion);\n        vector1.add(point);\n        vector2.copy(point2).add(offset2);\n        vector2.applyQuaternion(quaternion);\n        vector2.add(point);\n        vector3.copy(point2).add(offset2);\n        vector3.applyQuaternion(prevQuaternion);\n        vector3.add(prevPoint);\n        vector4.copy(point1).add(offset2);\n        vector4.applyQuaternion(prevQuaternion);\n        vector4.add(prevPoint);\n        vertices.push(vector1.x, vector1.y, vector1.z);\n        vertices.push(vector2.x, vector2.y, vector2.z);\n        vertices.push(vector4.x, vector4.y, vector4.z);\n        vertices.push(vector2.x, vector2.y, vector2.z);\n        vertices.push(vector3.x, vector3.y, vector3.z);\n        vertices.push(vector4.x, vector4.y, vector4.z);\n        normal1.copy(point1);\n        normal1.applyQuaternion(quaternion);\n        normal1.normalize();\n        normal2.copy(point2);\n        normal2.applyQuaternion(quaternion);\n        normal2.normalize();\n        normal3.copy(point2);\n        normal3.applyQuaternion(prevQuaternion);\n        normal3.normalize();\n        normal4.copy(point1);\n        normal4.applyQuaternion(prevQuaternion);\n        normal4.normalize();\n        normals.push(normal1.x, normal1.y, normal1.z);\n        normals.push(normal2.x, normal2.y, normal2.z);\n        normals.push(normal4.x, normal4.y, normal4.z);\n        normals.push(normal2.x, normal2.y, normal2.z);\n        normals.push(normal3.x, normal3.y, normal3.z);\n        normals.push(normal4.x, normal4.y, normal4.z);\n        colors.push(color[0], color[1], color[2]);\n        colors.push(color[0], color[1], color[2]);\n        colors.push(color[0], color[1], color[2]);\n        colors.push(color[0], color[1], color[2]);\n        colors.push(color[0], color[1], color[2]);\n        colors.push(color[0], color[1], color[2]);\n      }\n    }\n    const offset = new Vector3();\n    for (let i = 1; i <= divisions; i++) {\n      point.copy(curve.getPointAt(i / divisions));\n      up.set(0, 1, 0);\n      forward.subVectors(point, prevPoint).normalize();\n      right.crossVectors(up, forward).normalize();\n      up.crossVectors(forward, right);\n      const angle = Math.atan2(forward.x, forward.z);\n      quaternion.setFromAxisAngle(up, angle);\n      if (i % 2 === 0) {\n        drawShape(step, color2);\n      }\n      extrudeShape(tube1, offset.set(0, -0.125, 0), color2);\n      extrudeShape(tube2, offset.set(0.2, 0, 0), color1);\n      extrudeShape(tube2, offset.set(-0.2, 0, 0), color1);\n      prevPoint.copy(point);\n      prevQuaternion.copy(quaternion);\n    }\n    this.setAttribute(\"position\", new BufferAttribute(new Float32Array(vertices), 3));\n    this.setAttribute(\"normal\", new BufferAttribute(new Float32Array(normals), 3));\n    this.setAttribute(\"color\", new BufferAttribute(new Float32Array(colors), 3));\n  }\n}\nclass RollerCoasterLiftersGeometry extends BufferGeometry {\n  constructor(curve, divisions) {\n    super();\n    const vertices = [];\n    const normals = [];\n    const quaternion = new Quaternion();\n    const up = new Vector3(0, 1, 0);\n    const point = new Vector3();\n    const tangent = new Vector3();\n    const tube1 = [new Vector3(0, 0.05, -0.05), new Vector3(0, 0.05, 0.05), new Vector3(0, -0.05, 0)];\n    const tube2 = [new Vector3(-0.05, 0, 0.05), new Vector3(-0.05, 0, -0.05), new Vector3(0.05, 0, 0)];\n    const tube3 = [new Vector3(0.05, 0, -0.05), new Vector3(0.05, 0, 0.05), new Vector3(-0.05, 0, 0)];\n    const vector1 = new Vector3();\n    const vector2 = new Vector3();\n    const vector3 = new Vector3();\n    const vector4 = new Vector3();\n    const normal1 = new Vector3();\n    const normal2 = new Vector3();\n    const normal3 = new Vector3();\n    const normal4 = new Vector3();\n    function extrudeShape(shape, fromPoint2, toPoint2) {\n      for (let j = 0, jl = shape.length; j < jl; j++) {\n        const point1 = shape[j];\n        const point2 = shape[(j + 1) % jl];\n        vector1.copy(point1);\n        vector1.applyQuaternion(quaternion);\n        vector1.add(fromPoint2);\n        vector2.copy(point2);\n        vector2.applyQuaternion(quaternion);\n        vector2.add(fromPoint2);\n        vector3.copy(point2);\n        vector3.applyQuaternion(quaternion);\n        vector3.add(toPoint2);\n        vector4.copy(point1);\n        vector4.applyQuaternion(quaternion);\n        vector4.add(toPoint2);\n        vertices.push(vector1.x, vector1.y, vector1.z);\n        vertices.push(vector2.x, vector2.y, vector2.z);\n        vertices.push(vector4.x, vector4.y, vector4.z);\n        vertices.push(vector2.x, vector2.y, vector2.z);\n        vertices.push(vector3.x, vector3.y, vector3.z);\n        vertices.push(vector4.x, vector4.y, vector4.z);\n        normal1.copy(point1);\n        normal1.applyQuaternion(quaternion);\n        normal1.normalize();\n        normal2.copy(point2);\n        normal2.applyQuaternion(quaternion);\n        normal2.normalize();\n        normal3.copy(point2);\n        normal3.applyQuaternion(quaternion);\n        normal3.normalize();\n        normal4.copy(point1);\n        normal4.applyQuaternion(quaternion);\n        normal4.normalize();\n        normals.push(normal1.x, normal1.y, normal1.z);\n        normals.push(normal2.x, normal2.y, normal2.z);\n        normals.push(normal4.x, normal4.y, normal4.z);\n        normals.push(normal2.x, normal2.y, normal2.z);\n        normals.push(normal3.x, normal3.y, normal3.z);\n        normals.push(normal4.x, normal4.y, normal4.z);\n      }\n    }\n    const fromPoint = new Vector3();\n    const toPoint = new Vector3();\n    for (let i = 1; i <= divisions; i++) {\n      point.copy(curve.getPointAt(i / divisions));\n      tangent.copy(curve.getTangentAt(i / divisions));\n      const angle = Math.atan2(tangent.x, tangent.z);\n      quaternion.setFromAxisAngle(up, angle);\n      if (point.y > 10) {\n        fromPoint.set(-0.75, -0.35, 0);\n        fromPoint.applyQuaternion(quaternion);\n        fromPoint.add(point);\n        toPoint.set(0.75, -0.35, 0);\n        toPoint.applyQuaternion(quaternion);\n        toPoint.add(point);\n        extrudeShape(tube1, fromPoint, toPoint);\n        fromPoint.set(-0.7, -0.3, 0);\n        fromPoint.applyQuaternion(quaternion);\n        fromPoint.add(point);\n        toPoint.set(-0.7, -point.y, 0);\n        toPoint.applyQuaternion(quaternion);\n        toPoint.add(point);\n        extrudeShape(tube2, fromPoint, toPoint);\n        fromPoint.set(0.7, -0.3, 0);\n        fromPoint.applyQuaternion(quaternion);\n        fromPoint.add(point);\n        toPoint.set(0.7, -point.y, 0);\n        toPoint.applyQuaternion(quaternion);\n        toPoint.add(point);\n        extrudeShape(tube3, fromPoint, toPoint);\n      } else {\n        fromPoint.set(0, -0.2, 0);\n        fromPoint.applyQuaternion(quaternion);\n        fromPoint.add(point);\n        toPoint.set(0, -point.y, 0);\n        toPoint.applyQuaternion(quaternion);\n        toPoint.add(point);\n        extrudeShape(tube3, fromPoint, toPoint);\n      }\n    }\n    this.setAttribute(\"position\", new BufferAttribute(new Float32Array(vertices), 3));\n    this.setAttribute(\"normal\", new BufferAttribute(new Float32Array(normals), 3));\n  }\n}\nclass RollerCoasterShadowGeometry extends BufferGeometry {\n  constructor(curve, divisions) {\n    super();\n    const vertices = [];\n    const up = new Vector3(0, 1, 0);\n    const forward = new Vector3();\n    const quaternion = new Quaternion();\n    const prevQuaternion = new Quaternion();\n    prevQuaternion.setFromAxisAngle(up, Math.PI / 2);\n    const point = new Vector3();\n    const prevPoint = new Vector3();\n    prevPoint.copy(curve.getPointAt(0));\n    prevPoint.y = 0;\n    const vector1 = new Vector3();\n    const vector2 = new Vector3();\n    const vector3 = new Vector3();\n    const vector4 = new Vector3();\n    for (let i = 1; i <= divisions; i++) {\n      point.copy(curve.getPointAt(i / divisions));\n      point.y = 0;\n      forward.subVectors(point, prevPoint);\n      const angle = Math.atan2(forward.x, forward.z);\n      quaternion.setFromAxisAngle(up, angle);\n      vector1.set(-0.3, 0, 0);\n      vector1.applyQuaternion(quaternion);\n      vector1.add(point);\n      vector2.set(0.3, 0, 0);\n      vector2.applyQuaternion(quaternion);\n      vector2.add(point);\n      vector3.set(0.3, 0, 0);\n      vector3.applyQuaternion(prevQuaternion);\n      vector3.add(prevPoint);\n      vector4.set(-0.3, 0, 0);\n      vector4.applyQuaternion(prevQuaternion);\n      vector4.add(prevPoint);\n      vertices.push(vector1.x, vector1.y, vector1.z);\n      vertices.push(vector2.x, vector2.y, vector2.z);\n      vertices.push(vector4.x, vector4.y, vector4.z);\n      vertices.push(vector2.x, vector2.y, vector2.z);\n      vertices.push(vector3.x, vector3.y, vector3.z);\n      vertices.push(vector4.x, vector4.y, vector4.z);\n      prevPoint.copy(point);\n      prevQuaternion.copy(quaternion);\n    }\n    this.setAttribute(\"position\", new BufferAttribute(new Float32Array(vertices), 3));\n  }\n}\nclass SkyGeometry extends BufferGeometry {\n  constructor() {\n    super();\n    const vertices = [];\n    for (let i = 0; i < 100; i++) {\n      const x = Math.random() * 800 - 400;\n      const y = Math.random() * 50 + 50;\n      const z = Math.random() * 800 - 400;\n      const size = Math.random() * 40 + 20;\n      vertices.push(x - size, y, z - size);\n      vertices.push(x + size, y, z - size);\n      vertices.push(x - size, y, z + size);\n      vertices.push(x + size, y, z - size);\n      vertices.push(x + size, y, z + size);\n      vertices.push(x - size, y, z + size);\n    }\n    this.setAttribute(\"position\", new BufferAttribute(new Float32Array(vertices), 3));\n  }\n}\nclass TreesGeometry extends BufferGeometry {\n  constructor(landscape) {\n    super();\n    const vertices = [];\n    const colors = [];\n    const raycaster = new Raycaster();\n    raycaster.ray.direction.set(0, -1, 0);\n    const _color = new Color();\n    for (let i = 0; i < 2e3; i++) {\n      const x = Math.random() * 500 - 250;\n      const z = Math.random() * 500 - 250;\n      raycaster.ray.origin.set(x, 50, z);\n      const intersections = raycaster.intersectObject(landscape);\n      if (intersections.length === 0) continue;\n      const y = intersections[0].point.y;\n      const height = Math.random() * 5 + 0.5;\n      let angle = Math.random() * Math.PI * 2;\n      vertices.push(x + Math.sin(angle), y, z + Math.cos(angle));\n      vertices.push(x, y + height, z);\n      vertices.push(x + Math.sin(angle + Math.PI), y, z + Math.cos(angle + Math.PI));\n      angle += Math.PI / 2;\n      vertices.push(x + Math.sin(angle), y, z + Math.cos(angle));\n      vertices.push(x, y + height, z);\n      vertices.push(x + Math.sin(angle + Math.PI), y, z + Math.cos(angle + Math.PI));\n      const random = Math.random() * 0.1;\n      for (let j = 0; j < 6; j++) {\n        _color.setRGB(0.2 + random, 0.4 + random, 0, \"srgb\");\n        colors.push(_color.r, _color.g, _color.b);\n      }\n    }\n    this.setAttribute(\"position\", new BufferAttribute(new Float32Array(vertices), 3));\n    this.setAttribute(\"color\", new BufferAttribute(new Float32Array(colors), 3));\n  }\n}\nexport { RollerCoasterGeometry, RollerCoasterLiftersGeometry, RollerCoasterShadowGeometry, SkyGeometry, TreesGeometry };", "map": {"version": 3, "names": ["RollerCoasterGeometry", "BufferGeometry", "constructor", "curve", "divisions", "vertices", "normals", "colors", "color1", "color2", "up", "Vector3", "forward", "right", "quaternion", "Quaternion", "prevQuaternion", "setFromAxisAngle", "Math", "PI", "point", "prevPoint", "copy", "getPointAt", "step", "PI2", "sides", "tube1", "i", "angle", "push", "sin", "cos", "tube2", "vector", "normal", "drawShape", "shape", "color", "set", "applyQuaternion", "j", "length", "add", "x", "y", "z", "vector1", "vector2", "vector3", "vector4", "normal1", "normal2", "normal3", "normal4", "extrudeShape", "offset2", "jl", "point1", "point2", "normalize", "offset", "subVectors", "crossVectors", "atan2", "setAttribute", "BufferAttribute", "Float32Array", "RollerCoasterLiftersGeometry", "tangent", "tube3", "fromPoint2", "toPoint2", "fromPoint", "toPoint", "getTangentAt", "RollerCoasterShadowGeometry", "SkyGeometry", "random", "size", "TreesGeometry", "landscape", "raycaster", "Raycaster", "ray", "direction", "_color", "Color", "origin", "intersections", "intersectObject", "height", "setRGB", "r", "g", "b"], "sources": ["C:\\Users\\<USER>\\Documents\\Apps_Creations\\SiahMan_Racing4Speed_WebGame\\siah-man-racing\\node_modules\\src\\misc\\RollerCoaster.js"], "sourcesContent": ["import { <PERSON><PERSON>erAttribute, BufferGeometry, Color, Quaternion, Raycaster, Vector3 } from 'three'\n\nclass RollerCoasterGeometry extends BufferGeometry {\n  constructor(curve, divisions) {\n    super()\n\n    const vertices = []\n    const normals = []\n    const colors = []\n\n    const color1 = [1, 1, 1]\n    const color2 = [1, 1, 0]\n\n    const up = new Vector3(0, 1, 0)\n    const forward = new Vector3()\n    const right = new Vector3()\n\n    const quaternion = new Quaternion()\n    const prevQuaternion = new Quaternion()\n    prevQuaternion.setFromAxisAngle(up, Math.PI / 2)\n\n    const point = new Vector3()\n    const prevPoint = new Vector3()\n    prevPoint.copy(curve.getPointAt(0))\n\n    // shapes\n\n    const step = [\n      new Vector3(-0.225, 0, 0),\n      new Vector3(0, -0.05, 0),\n      new Vector3(0, -0.175, 0),\n\n      new Vector3(0, -0.05, 0),\n      new Vector3(0.225, 0, 0),\n      new Vector3(0, -0.175, 0),\n    ]\n\n    const PI2 = Math.PI * 2\n\n    let sides = 5\n    const tube1 = []\n\n    for (let i = 0; i < sides; i++) {\n      const angle = (i / sides) * PI2\n      tube1.push(new Vector3(Math.sin(angle) * 0.06, Math.cos(angle) * 0.06, 0))\n    }\n\n    sides = 6\n    const tube2 = []\n\n    for (let i = 0; i < sides; i++) {\n      const angle = (i / sides) * PI2\n      tube2.push(new Vector3(Math.sin(angle) * 0.025, Math.cos(angle) * 0.025, 0))\n    }\n\n    const vector = new Vector3()\n    const normal = new Vector3()\n\n    function drawShape(shape, color) {\n      normal.set(0, 0, -1).applyQuaternion(quaternion)\n\n      for (let j = 0; j < shape.length; j++) {\n        vector.copy(shape[j])\n        vector.applyQuaternion(quaternion)\n        vector.add(point)\n\n        vertices.push(vector.x, vector.y, vector.z)\n        normals.push(normal.x, normal.y, normal.z)\n        colors.push(color[0], color[1], color[2])\n      }\n\n      normal.set(0, 0, 1).applyQuaternion(quaternion)\n\n      for (let j = shape.length - 1; j >= 0; j--) {\n        vector.copy(shape[j])\n        vector.applyQuaternion(quaternion)\n        vector.add(point)\n\n        vertices.push(vector.x, vector.y, vector.z)\n        normals.push(normal.x, normal.y, normal.z)\n        colors.push(color[0], color[1], color[2])\n      }\n    }\n\n    const vector1 = new Vector3()\n    const vector2 = new Vector3()\n    const vector3 = new Vector3()\n    const vector4 = new Vector3()\n\n    const normal1 = new Vector3()\n    const normal2 = new Vector3()\n    const normal3 = new Vector3()\n    const normal4 = new Vector3()\n\n    function extrudeShape(shape, offset, color) {\n      for (let j = 0, jl = shape.length; j < jl; j++) {\n        const point1 = shape[j]\n        const point2 = shape[(j + 1) % jl]\n\n        vector1.copy(point1).add(offset)\n        vector1.applyQuaternion(quaternion)\n        vector1.add(point)\n\n        vector2.copy(point2).add(offset)\n        vector2.applyQuaternion(quaternion)\n        vector2.add(point)\n\n        vector3.copy(point2).add(offset)\n        vector3.applyQuaternion(prevQuaternion)\n        vector3.add(prevPoint)\n\n        vector4.copy(point1).add(offset)\n        vector4.applyQuaternion(prevQuaternion)\n        vector4.add(prevPoint)\n\n        vertices.push(vector1.x, vector1.y, vector1.z)\n        vertices.push(vector2.x, vector2.y, vector2.z)\n        vertices.push(vector4.x, vector4.y, vector4.z)\n\n        vertices.push(vector2.x, vector2.y, vector2.z)\n        vertices.push(vector3.x, vector3.y, vector3.z)\n        vertices.push(vector4.x, vector4.y, vector4.z)\n\n        //\n\n        normal1.copy(point1)\n        normal1.applyQuaternion(quaternion)\n        normal1.normalize()\n\n        normal2.copy(point2)\n        normal2.applyQuaternion(quaternion)\n        normal2.normalize()\n\n        normal3.copy(point2)\n        normal3.applyQuaternion(prevQuaternion)\n        normal3.normalize()\n\n        normal4.copy(point1)\n        normal4.applyQuaternion(prevQuaternion)\n        normal4.normalize()\n\n        normals.push(normal1.x, normal1.y, normal1.z)\n        normals.push(normal2.x, normal2.y, normal2.z)\n        normals.push(normal4.x, normal4.y, normal4.z)\n\n        normals.push(normal2.x, normal2.y, normal2.z)\n        normals.push(normal3.x, normal3.y, normal3.z)\n        normals.push(normal4.x, normal4.y, normal4.z)\n\n        colors.push(color[0], color[1], color[2])\n        colors.push(color[0], color[1], color[2])\n        colors.push(color[0], color[1], color[2])\n\n        colors.push(color[0], color[1], color[2])\n        colors.push(color[0], color[1], color[2])\n        colors.push(color[0], color[1], color[2])\n      }\n    }\n\n    const offset = new Vector3()\n\n    for (let i = 1; i <= divisions; i++) {\n      point.copy(curve.getPointAt(i / divisions))\n\n      up.set(0, 1, 0)\n\n      forward.subVectors(point, prevPoint).normalize()\n      right.crossVectors(up, forward).normalize()\n      up.crossVectors(forward, right)\n\n      const angle = Math.atan2(forward.x, forward.z)\n\n      quaternion.setFromAxisAngle(up, angle)\n\n      if (i % 2 === 0) {\n        drawShape(step, color2)\n      }\n\n      extrudeShape(tube1, offset.set(0, -0.125, 0), color2)\n      extrudeShape(tube2, offset.set(0.2, 0, 0), color1)\n      extrudeShape(tube2, offset.set(-0.2, 0, 0), color1)\n\n      prevPoint.copy(point)\n      prevQuaternion.copy(quaternion)\n    }\n\n    // console.log( vertices.length );\n\n    this.setAttribute('position', new BufferAttribute(new Float32Array(vertices), 3))\n    this.setAttribute('normal', new BufferAttribute(new Float32Array(normals), 3))\n    this.setAttribute('color', new BufferAttribute(new Float32Array(colors), 3))\n  }\n}\n\nclass RollerCoasterLiftersGeometry extends BufferGeometry {\n  constructor(curve, divisions) {\n    super()\n\n    const vertices = []\n    const normals = []\n\n    const quaternion = new Quaternion()\n\n    const up = new Vector3(0, 1, 0)\n\n    const point = new Vector3()\n    const tangent = new Vector3()\n\n    // shapes\n\n    const tube1 = [new Vector3(0, 0.05, -0.05), new Vector3(0, 0.05, 0.05), new Vector3(0, -0.05, 0)]\n\n    const tube2 = [new Vector3(-0.05, 0, 0.05), new Vector3(-0.05, 0, -0.05), new Vector3(0.05, 0, 0)]\n\n    const tube3 = [new Vector3(0.05, 0, -0.05), new Vector3(0.05, 0, 0.05), new Vector3(-0.05, 0, 0)]\n\n    const vector1 = new Vector3()\n    const vector2 = new Vector3()\n    const vector3 = new Vector3()\n    const vector4 = new Vector3()\n\n    const normal1 = new Vector3()\n    const normal2 = new Vector3()\n    const normal3 = new Vector3()\n    const normal4 = new Vector3()\n\n    function extrudeShape(shape, fromPoint, toPoint) {\n      for (let j = 0, jl = shape.length; j < jl; j++) {\n        const point1 = shape[j]\n        const point2 = shape[(j + 1) % jl]\n\n        vector1.copy(point1)\n        vector1.applyQuaternion(quaternion)\n        vector1.add(fromPoint)\n\n        vector2.copy(point2)\n        vector2.applyQuaternion(quaternion)\n        vector2.add(fromPoint)\n\n        vector3.copy(point2)\n        vector3.applyQuaternion(quaternion)\n        vector3.add(toPoint)\n\n        vector4.copy(point1)\n        vector4.applyQuaternion(quaternion)\n        vector4.add(toPoint)\n\n        vertices.push(vector1.x, vector1.y, vector1.z)\n        vertices.push(vector2.x, vector2.y, vector2.z)\n        vertices.push(vector4.x, vector4.y, vector4.z)\n\n        vertices.push(vector2.x, vector2.y, vector2.z)\n        vertices.push(vector3.x, vector3.y, vector3.z)\n        vertices.push(vector4.x, vector4.y, vector4.z)\n\n        //\n\n        normal1.copy(point1)\n        normal1.applyQuaternion(quaternion)\n        normal1.normalize()\n\n        normal2.copy(point2)\n        normal2.applyQuaternion(quaternion)\n        normal2.normalize()\n\n        normal3.copy(point2)\n        normal3.applyQuaternion(quaternion)\n        normal3.normalize()\n\n        normal4.copy(point1)\n        normal4.applyQuaternion(quaternion)\n        normal4.normalize()\n\n        normals.push(normal1.x, normal1.y, normal1.z)\n        normals.push(normal2.x, normal2.y, normal2.z)\n        normals.push(normal4.x, normal4.y, normal4.z)\n\n        normals.push(normal2.x, normal2.y, normal2.z)\n        normals.push(normal3.x, normal3.y, normal3.z)\n        normals.push(normal4.x, normal4.y, normal4.z)\n      }\n    }\n\n    const fromPoint = new Vector3()\n    const toPoint = new Vector3()\n\n    for (let i = 1; i <= divisions; i++) {\n      point.copy(curve.getPointAt(i / divisions))\n      tangent.copy(curve.getTangentAt(i / divisions))\n\n      const angle = Math.atan2(tangent.x, tangent.z)\n\n      quaternion.setFromAxisAngle(up, angle)\n\n      //\n\n      if (point.y > 10) {\n        fromPoint.set(-0.75, -0.35, 0)\n        fromPoint.applyQuaternion(quaternion)\n        fromPoint.add(point)\n\n        toPoint.set(0.75, -0.35, 0)\n        toPoint.applyQuaternion(quaternion)\n        toPoint.add(point)\n\n        extrudeShape(tube1, fromPoint, toPoint)\n\n        fromPoint.set(-0.7, -0.3, 0)\n        fromPoint.applyQuaternion(quaternion)\n        fromPoint.add(point)\n\n        toPoint.set(-0.7, -point.y, 0)\n        toPoint.applyQuaternion(quaternion)\n        toPoint.add(point)\n\n        extrudeShape(tube2, fromPoint, toPoint)\n\n        fromPoint.set(0.7, -0.3, 0)\n        fromPoint.applyQuaternion(quaternion)\n        fromPoint.add(point)\n\n        toPoint.set(0.7, -point.y, 0)\n        toPoint.applyQuaternion(quaternion)\n        toPoint.add(point)\n\n        extrudeShape(tube3, fromPoint, toPoint)\n      } else {\n        fromPoint.set(0, -0.2, 0)\n        fromPoint.applyQuaternion(quaternion)\n        fromPoint.add(point)\n\n        toPoint.set(0, -point.y, 0)\n        toPoint.applyQuaternion(quaternion)\n        toPoint.add(point)\n\n        extrudeShape(tube3, fromPoint, toPoint)\n      }\n    }\n\n    this.setAttribute('position', new BufferAttribute(new Float32Array(vertices), 3))\n    this.setAttribute('normal', new BufferAttribute(new Float32Array(normals), 3))\n  }\n}\n\nclass RollerCoasterShadowGeometry extends BufferGeometry {\n  constructor(curve, divisions) {\n    super()\n\n    const vertices = []\n\n    const up = new Vector3(0, 1, 0)\n    const forward = new Vector3()\n\n    const quaternion = new Quaternion()\n    const prevQuaternion = new Quaternion()\n    prevQuaternion.setFromAxisAngle(up, Math.PI / 2)\n\n    const point = new Vector3()\n\n    const prevPoint = new Vector3()\n    prevPoint.copy(curve.getPointAt(0))\n    prevPoint.y = 0\n\n    const vector1 = new Vector3()\n    const vector2 = new Vector3()\n    const vector3 = new Vector3()\n    const vector4 = new Vector3()\n\n    for (let i = 1; i <= divisions; i++) {\n      point.copy(curve.getPointAt(i / divisions))\n      point.y = 0\n\n      forward.subVectors(point, prevPoint)\n\n      const angle = Math.atan2(forward.x, forward.z)\n\n      quaternion.setFromAxisAngle(up, angle)\n\n      vector1.set(-0.3, 0, 0)\n      vector1.applyQuaternion(quaternion)\n      vector1.add(point)\n\n      vector2.set(0.3, 0, 0)\n      vector2.applyQuaternion(quaternion)\n      vector2.add(point)\n\n      vector3.set(0.3, 0, 0)\n      vector3.applyQuaternion(prevQuaternion)\n      vector3.add(prevPoint)\n\n      vector4.set(-0.3, 0, 0)\n      vector4.applyQuaternion(prevQuaternion)\n      vector4.add(prevPoint)\n\n      vertices.push(vector1.x, vector1.y, vector1.z)\n      vertices.push(vector2.x, vector2.y, vector2.z)\n      vertices.push(vector4.x, vector4.y, vector4.z)\n\n      vertices.push(vector2.x, vector2.y, vector2.z)\n      vertices.push(vector3.x, vector3.y, vector3.z)\n      vertices.push(vector4.x, vector4.y, vector4.z)\n\n      prevPoint.copy(point)\n      prevQuaternion.copy(quaternion)\n    }\n\n    this.setAttribute('position', new BufferAttribute(new Float32Array(vertices), 3))\n  }\n}\n\nclass SkyGeometry extends BufferGeometry {\n  constructor() {\n    super()\n\n    const vertices = []\n\n    for (let i = 0; i < 100; i++) {\n      const x = Math.random() * 800 - 400\n      const y = Math.random() * 50 + 50\n      const z = Math.random() * 800 - 400\n\n      const size = Math.random() * 40 + 20\n\n      vertices.push(x - size, y, z - size)\n      vertices.push(x + size, y, z - size)\n      vertices.push(x - size, y, z + size)\n\n      vertices.push(x + size, y, z - size)\n      vertices.push(x + size, y, z + size)\n      vertices.push(x - size, y, z + size)\n    }\n\n    this.setAttribute('position', new BufferAttribute(new Float32Array(vertices), 3))\n  }\n}\n\nclass TreesGeometry extends BufferGeometry {\n  constructor(landscape) {\n    super()\n\n    const vertices = []\n    const colors = []\n\n    const raycaster = new Raycaster()\n    raycaster.ray.direction.set(0, -1, 0)\n\n    const _color = new Color()\n\n    for (let i = 0; i < 2000; i++) {\n      const x = Math.random() * 500 - 250\n      const z = Math.random() * 500 - 250\n\n      raycaster.ray.origin.set(x, 50, z)\n\n      const intersections = raycaster.intersectObject(landscape)\n\n      if (intersections.length === 0) continue\n\n      const y = intersections[0].point.y\n\n      const height = Math.random() * 5 + 0.5\n\n      let angle = Math.random() * Math.PI * 2\n\n      vertices.push(x + Math.sin(angle), y, z + Math.cos(angle))\n      vertices.push(x, y + height, z)\n      vertices.push(x + Math.sin(angle + Math.PI), y, z + Math.cos(angle + Math.PI))\n\n      angle += Math.PI / 2\n\n      vertices.push(x + Math.sin(angle), y, z + Math.cos(angle))\n      vertices.push(x, y + height, z)\n      vertices.push(x + Math.sin(angle + Math.PI), y, z + Math.cos(angle + Math.PI))\n\n      const random = Math.random() * 0.1\n\n      for (let j = 0; j < 6; j++) {\n        _color.setRGB(0.2 + random, 0.4 + random, 0, 'srgb')\n\n        colors.push(_color.r, _color.g, _color.b)\n      }\n    }\n\n    this.setAttribute('position', new BufferAttribute(new Float32Array(vertices), 3))\n    this.setAttribute('color', new BufferAttribute(new Float32Array(colors), 3))\n  }\n}\n\nexport { RollerCoasterGeometry, RollerCoasterLiftersGeometry, RollerCoasterShadowGeometry, SkyGeometry, TreesGeometry }\n"], "mappings": ";AAEA,MAAMA,qBAAA,SAA8BC,cAAA,CAAe;EACjDC,YAAYC,KAAA,EAAOC,SAAA,EAAW;IAC5B,MAAO;IAEP,MAAMC,QAAA,GAAW,EAAE;IACnB,MAAMC,OAAA,GAAU,EAAE;IAClB,MAAMC,MAAA,GAAS,EAAE;IAEjB,MAAMC,MAAA,GAAS,CAAC,GAAG,GAAG,CAAC;IACvB,MAAMC,MAAA,GAAS,CAAC,GAAG,GAAG,CAAC;IAEvB,MAAMC,EAAA,GAAK,IAAIC,OAAA,CAAQ,GAAG,GAAG,CAAC;IAC9B,MAAMC,OAAA,GAAU,IAAID,OAAA,CAAS;IAC7B,MAAME,KAAA,GAAQ,IAAIF,OAAA,CAAS;IAE3B,MAAMG,UAAA,GAAa,IAAIC,UAAA,CAAY;IACnC,MAAMC,cAAA,GAAiB,IAAID,UAAA,CAAY;IACvCC,cAAA,CAAeC,gBAAA,CAAiBP,EAAA,EAAIQ,IAAA,CAAKC,EAAA,GAAK,CAAC;IAE/C,MAAMC,KAAA,GAAQ,IAAIT,OAAA,CAAS;IAC3B,MAAMU,SAAA,GAAY,IAAIV,OAAA,CAAS;IAC/BU,SAAA,CAAUC,IAAA,CAAKnB,KAAA,CAAMoB,UAAA,CAAW,CAAC,CAAC;IAIlC,MAAMC,IAAA,GAAO,CACX,IAAIb,OAAA,CAAQ,QAAQ,GAAG,CAAC,GACxB,IAAIA,OAAA,CAAQ,GAAG,OAAO,CAAC,GACvB,IAAIA,OAAA,CAAQ,GAAG,QAAQ,CAAC,GAExB,IAAIA,OAAA,CAAQ,GAAG,OAAO,CAAC,GACvB,IAAIA,OAAA,CAAQ,OAAO,GAAG,CAAC,GACvB,IAAIA,OAAA,CAAQ,GAAG,QAAQ,CAAC,EACzB;IAED,MAAMc,GAAA,GAAMP,IAAA,CAAKC,EAAA,GAAK;IAEtB,IAAIO,KAAA,GAAQ;IACZ,MAAMC,KAAA,GAAQ,EAAE;IAEhB,SAASC,CAAA,GAAI,GAAGA,CAAA,GAAIF,KAAA,EAAOE,CAAA,IAAK;MAC9B,MAAMC,KAAA,GAASD,CAAA,GAAIF,KAAA,GAASD,GAAA;MAC5BE,KAAA,CAAMG,IAAA,CAAK,IAAInB,OAAA,CAAQO,IAAA,CAAKa,GAAA,CAAIF,KAAK,IAAI,MAAMX,IAAA,CAAKc,GAAA,CAAIH,KAAK,IAAI,MAAM,CAAC,CAAC;IAC1E;IAEDH,KAAA,GAAQ;IACR,MAAMO,KAAA,GAAQ,EAAE;IAEhB,SAASL,CAAA,GAAI,GAAGA,CAAA,GAAIF,KAAA,EAAOE,CAAA,IAAK;MAC9B,MAAMC,KAAA,GAASD,CAAA,GAAIF,KAAA,GAASD,GAAA;MAC5BQ,KAAA,CAAMH,IAAA,CAAK,IAAInB,OAAA,CAAQO,IAAA,CAAKa,GAAA,CAAIF,KAAK,IAAI,OAAOX,IAAA,CAAKc,GAAA,CAAIH,KAAK,IAAI,OAAO,CAAC,CAAC;IAC5E;IAED,MAAMK,MAAA,GAAS,IAAIvB,OAAA,CAAS;IAC5B,MAAMwB,MAAA,GAAS,IAAIxB,OAAA,CAAS;IAE5B,SAASyB,UAAUC,KAAA,EAAOC,KAAA,EAAO;MAC/BH,MAAA,CAAOI,GAAA,CAAI,GAAG,GAAG,EAAE,EAAEC,eAAA,CAAgB1B,UAAU;MAE/C,SAAS2B,CAAA,GAAI,GAAGA,CAAA,GAAIJ,KAAA,CAAMK,MAAA,EAAQD,CAAA,IAAK;QACrCP,MAAA,CAAOZ,IAAA,CAAKe,KAAA,CAAMI,CAAC,CAAC;QACpBP,MAAA,CAAOM,eAAA,CAAgB1B,UAAU;QACjCoB,MAAA,CAAOS,GAAA,CAAIvB,KAAK;QAEhBf,QAAA,CAASyB,IAAA,CAAKI,MAAA,CAAOU,CAAA,EAAGV,MAAA,CAAOW,CAAA,EAAGX,MAAA,CAAOY,CAAC;QAC1CxC,OAAA,CAAQwB,IAAA,CAAKK,MAAA,CAAOS,CAAA,EAAGT,MAAA,CAAOU,CAAA,EAAGV,MAAA,CAAOW,CAAC;QACzCvC,MAAA,CAAOuB,IAAA,CAAKQ,KAAA,CAAM,CAAC,GAAGA,KAAA,CAAM,CAAC,GAAGA,KAAA,CAAM,CAAC,CAAC;MACzC;MAEDH,MAAA,CAAOI,GAAA,CAAI,GAAG,GAAG,CAAC,EAAEC,eAAA,CAAgB1B,UAAU;MAE9C,SAAS2B,CAAA,GAAIJ,KAAA,CAAMK,MAAA,GAAS,GAAGD,CAAA,IAAK,GAAGA,CAAA,IAAK;QAC1CP,MAAA,CAAOZ,IAAA,CAAKe,KAAA,CAAMI,CAAC,CAAC;QACpBP,MAAA,CAAOM,eAAA,CAAgB1B,UAAU;QACjCoB,MAAA,CAAOS,GAAA,CAAIvB,KAAK;QAEhBf,QAAA,CAASyB,IAAA,CAAKI,MAAA,CAAOU,CAAA,EAAGV,MAAA,CAAOW,CAAA,EAAGX,MAAA,CAAOY,CAAC;QAC1CxC,OAAA,CAAQwB,IAAA,CAAKK,MAAA,CAAOS,CAAA,EAAGT,MAAA,CAAOU,CAAA,EAAGV,MAAA,CAAOW,CAAC;QACzCvC,MAAA,CAAOuB,IAAA,CAAKQ,KAAA,CAAM,CAAC,GAAGA,KAAA,CAAM,CAAC,GAAGA,KAAA,CAAM,CAAC,CAAC;MACzC;IACF;IAED,MAAMS,OAAA,GAAU,IAAIpC,OAAA,CAAS;IAC7B,MAAMqC,OAAA,GAAU,IAAIrC,OAAA,CAAS;IAC7B,MAAMsC,OAAA,GAAU,IAAItC,OAAA,CAAS;IAC7B,MAAMuC,OAAA,GAAU,IAAIvC,OAAA,CAAS;IAE7B,MAAMwC,OAAA,GAAU,IAAIxC,OAAA,CAAS;IAC7B,MAAMyC,OAAA,GAAU,IAAIzC,OAAA,CAAS;IAC7B,MAAM0C,OAAA,GAAU,IAAI1C,OAAA,CAAS;IAC7B,MAAM2C,OAAA,GAAU,IAAI3C,OAAA,CAAS;IAE7B,SAAS4C,aAAalB,KAAA,EAAOmB,OAAA,EAAQlB,KAAA,EAAO;MAC1C,SAASG,CAAA,GAAI,GAAGgB,EAAA,GAAKpB,KAAA,CAAMK,MAAA,EAAQD,CAAA,GAAIgB,EAAA,EAAIhB,CAAA,IAAK;QAC9C,MAAMiB,MAAA,GAASrB,KAAA,CAAMI,CAAC;QACtB,MAAMkB,MAAA,GAAStB,KAAA,EAAOI,CAAA,GAAI,KAAKgB,EAAE;QAEjCV,OAAA,CAAQzB,IAAA,CAAKoC,MAAM,EAAEf,GAAA,CAAIa,OAAM;QAC/BT,OAAA,CAAQP,eAAA,CAAgB1B,UAAU;QAClCiC,OAAA,CAAQJ,GAAA,CAAIvB,KAAK;QAEjB4B,OAAA,CAAQ1B,IAAA,CAAKqC,MAAM,EAAEhB,GAAA,CAAIa,OAAM;QAC/BR,OAAA,CAAQR,eAAA,CAAgB1B,UAAU;QAClCkC,OAAA,CAAQL,GAAA,CAAIvB,KAAK;QAEjB6B,OAAA,CAAQ3B,IAAA,CAAKqC,MAAM,EAAEhB,GAAA,CAAIa,OAAM;QAC/BP,OAAA,CAAQT,eAAA,CAAgBxB,cAAc;QACtCiC,OAAA,CAAQN,GAAA,CAAItB,SAAS;QAErB6B,OAAA,CAAQ5B,IAAA,CAAKoC,MAAM,EAAEf,GAAA,CAAIa,OAAM;QAC/BN,OAAA,CAAQV,eAAA,CAAgBxB,cAAc;QACtCkC,OAAA,CAAQP,GAAA,CAAItB,SAAS;QAErBhB,QAAA,CAASyB,IAAA,CAAKiB,OAAA,CAAQH,CAAA,EAAGG,OAAA,CAAQF,CAAA,EAAGE,OAAA,CAAQD,CAAC;QAC7CzC,QAAA,CAASyB,IAAA,CAAKkB,OAAA,CAAQJ,CAAA,EAAGI,OAAA,CAAQH,CAAA,EAAGG,OAAA,CAAQF,CAAC;QAC7CzC,QAAA,CAASyB,IAAA,CAAKoB,OAAA,CAAQN,CAAA,EAAGM,OAAA,CAAQL,CAAA,EAAGK,OAAA,CAAQJ,CAAC;QAE7CzC,QAAA,CAASyB,IAAA,CAAKkB,OAAA,CAAQJ,CAAA,EAAGI,OAAA,CAAQH,CAAA,EAAGG,OAAA,CAAQF,CAAC;QAC7CzC,QAAA,CAASyB,IAAA,CAAKmB,OAAA,CAAQL,CAAA,EAAGK,OAAA,CAAQJ,CAAA,EAAGI,OAAA,CAAQH,CAAC;QAC7CzC,QAAA,CAASyB,IAAA,CAAKoB,OAAA,CAAQN,CAAA,EAAGM,OAAA,CAAQL,CAAA,EAAGK,OAAA,CAAQJ,CAAC;QAI7CK,OAAA,CAAQ7B,IAAA,CAAKoC,MAAM;QACnBP,OAAA,CAAQX,eAAA,CAAgB1B,UAAU;QAClCqC,OAAA,CAAQS,SAAA,CAAW;QAEnBR,OAAA,CAAQ9B,IAAA,CAAKqC,MAAM;QACnBP,OAAA,CAAQZ,eAAA,CAAgB1B,UAAU;QAClCsC,OAAA,CAAQQ,SAAA,CAAW;QAEnBP,OAAA,CAAQ/B,IAAA,CAAKqC,MAAM;QACnBN,OAAA,CAAQb,eAAA,CAAgBxB,cAAc;QACtCqC,OAAA,CAAQO,SAAA,CAAW;QAEnBN,OAAA,CAAQhC,IAAA,CAAKoC,MAAM;QACnBJ,OAAA,CAAQd,eAAA,CAAgBxB,cAAc;QACtCsC,OAAA,CAAQM,SAAA,CAAW;QAEnBtD,OAAA,CAAQwB,IAAA,CAAKqB,OAAA,CAAQP,CAAA,EAAGO,OAAA,CAAQN,CAAA,EAAGM,OAAA,CAAQL,CAAC;QAC5CxC,OAAA,CAAQwB,IAAA,CAAKsB,OAAA,CAAQR,CAAA,EAAGQ,OAAA,CAAQP,CAAA,EAAGO,OAAA,CAAQN,CAAC;QAC5CxC,OAAA,CAAQwB,IAAA,CAAKwB,OAAA,CAAQV,CAAA,EAAGU,OAAA,CAAQT,CAAA,EAAGS,OAAA,CAAQR,CAAC;QAE5CxC,OAAA,CAAQwB,IAAA,CAAKsB,OAAA,CAAQR,CAAA,EAAGQ,OAAA,CAAQP,CAAA,EAAGO,OAAA,CAAQN,CAAC;QAC5CxC,OAAA,CAAQwB,IAAA,CAAKuB,OAAA,CAAQT,CAAA,EAAGS,OAAA,CAAQR,CAAA,EAAGQ,OAAA,CAAQP,CAAC;QAC5CxC,OAAA,CAAQwB,IAAA,CAAKwB,OAAA,CAAQV,CAAA,EAAGU,OAAA,CAAQT,CAAA,EAAGS,OAAA,CAAQR,CAAC;QAE5CvC,MAAA,CAAOuB,IAAA,CAAKQ,KAAA,CAAM,CAAC,GAAGA,KAAA,CAAM,CAAC,GAAGA,KAAA,CAAM,CAAC,CAAC;QACxC/B,MAAA,CAAOuB,IAAA,CAAKQ,KAAA,CAAM,CAAC,GAAGA,KAAA,CAAM,CAAC,GAAGA,KAAA,CAAM,CAAC,CAAC;QACxC/B,MAAA,CAAOuB,IAAA,CAAKQ,KAAA,CAAM,CAAC,GAAGA,KAAA,CAAM,CAAC,GAAGA,KAAA,CAAM,CAAC,CAAC;QAExC/B,MAAA,CAAOuB,IAAA,CAAKQ,KAAA,CAAM,CAAC,GAAGA,KAAA,CAAM,CAAC,GAAGA,KAAA,CAAM,CAAC,CAAC;QACxC/B,MAAA,CAAOuB,IAAA,CAAKQ,KAAA,CAAM,CAAC,GAAGA,KAAA,CAAM,CAAC,GAAGA,KAAA,CAAM,CAAC,CAAC;QACxC/B,MAAA,CAAOuB,IAAA,CAAKQ,KAAA,CAAM,CAAC,GAAGA,KAAA,CAAM,CAAC,GAAGA,KAAA,CAAM,CAAC,CAAC;MACzC;IACF;IAED,MAAMuB,MAAA,GAAS,IAAIlD,OAAA,CAAS;IAE5B,SAASiB,CAAA,GAAI,GAAGA,CAAA,IAAKxB,SAAA,EAAWwB,CAAA,IAAK;MACnCR,KAAA,CAAME,IAAA,CAAKnB,KAAA,CAAMoB,UAAA,CAAWK,CAAA,GAAIxB,SAAS,CAAC;MAE1CM,EAAA,CAAG6B,GAAA,CAAI,GAAG,GAAG,CAAC;MAEd3B,OAAA,CAAQkD,UAAA,CAAW1C,KAAA,EAAOC,SAAS,EAAEuC,SAAA,CAAW;MAChD/C,KAAA,CAAMkD,YAAA,CAAarD,EAAA,EAAIE,OAAO,EAAEgD,SAAA,CAAW;MAC3ClD,EAAA,CAAGqD,YAAA,CAAanD,OAAA,EAASC,KAAK;MAE9B,MAAMgB,KAAA,GAAQX,IAAA,CAAK8C,KAAA,CAAMpD,OAAA,CAAQgC,CAAA,EAAGhC,OAAA,CAAQkC,CAAC;MAE7ChC,UAAA,CAAWG,gBAAA,CAAiBP,EAAA,EAAImB,KAAK;MAErC,IAAID,CAAA,GAAI,MAAM,GAAG;QACfQ,SAAA,CAAUZ,IAAA,EAAMf,MAAM;MACvB;MAED8C,YAAA,CAAa5B,KAAA,EAAOkC,MAAA,CAAOtB,GAAA,CAAI,GAAG,QAAQ,CAAC,GAAG9B,MAAM;MACpD8C,YAAA,CAAatB,KAAA,EAAO4B,MAAA,CAAOtB,GAAA,CAAI,KAAK,GAAG,CAAC,GAAG/B,MAAM;MACjD+C,YAAA,CAAatB,KAAA,EAAO4B,MAAA,CAAOtB,GAAA,CAAI,MAAM,GAAG,CAAC,GAAG/B,MAAM;MAElDa,SAAA,CAAUC,IAAA,CAAKF,KAAK;MACpBJ,cAAA,CAAeM,IAAA,CAAKR,UAAU;IAC/B;IAID,KAAKmD,YAAA,CAAa,YAAY,IAAIC,eAAA,CAAgB,IAAIC,YAAA,CAAa9D,QAAQ,GAAG,CAAC,CAAC;IAChF,KAAK4D,YAAA,CAAa,UAAU,IAAIC,eAAA,CAAgB,IAAIC,YAAA,CAAa7D,OAAO,GAAG,CAAC,CAAC;IAC7E,KAAK2D,YAAA,CAAa,SAAS,IAAIC,eAAA,CAAgB,IAAIC,YAAA,CAAa5D,MAAM,GAAG,CAAC,CAAC;EAC5E;AACH;AAEA,MAAM6D,4BAAA,SAAqCnE,cAAA,CAAe;EACxDC,YAAYC,KAAA,EAAOC,SAAA,EAAW;IAC5B,MAAO;IAEP,MAAMC,QAAA,GAAW,EAAE;IACnB,MAAMC,OAAA,GAAU,EAAE;IAElB,MAAMQ,UAAA,GAAa,IAAIC,UAAA,CAAY;IAEnC,MAAML,EAAA,GAAK,IAAIC,OAAA,CAAQ,GAAG,GAAG,CAAC;IAE9B,MAAMS,KAAA,GAAQ,IAAIT,OAAA,CAAS;IAC3B,MAAM0D,OAAA,GAAU,IAAI1D,OAAA,CAAS;IAI7B,MAAMgB,KAAA,GAAQ,CAAC,IAAIhB,OAAA,CAAQ,GAAG,MAAM,KAAK,GAAG,IAAIA,OAAA,CAAQ,GAAG,MAAM,IAAI,GAAG,IAAIA,OAAA,CAAQ,GAAG,OAAO,CAAC,CAAC;IAEhG,MAAMsB,KAAA,GAAQ,CAAC,IAAItB,OAAA,CAAQ,OAAO,GAAG,IAAI,GAAG,IAAIA,OAAA,CAAQ,OAAO,GAAG,KAAK,GAAG,IAAIA,OAAA,CAAQ,MAAM,GAAG,CAAC,CAAC;IAEjG,MAAM2D,KAAA,GAAQ,CAAC,IAAI3D,OAAA,CAAQ,MAAM,GAAG,KAAK,GAAG,IAAIA,OAAA,CAAQ,MAAM,GAAG,IAAI,GAAG,IAAIA,OAAA,CAAQ,OAAO,GAAG,CAAC,CAAC;IAEhG,MAAMoC,OAAA,GAAU,IAAIpC,OAAA,CAAS;IAC7B,MAAMqC,OAAA,GAAU,IAAIrC,OAAA,CAAS;IAC7B,MAAMsC,OAAA,GAAU,IAAItC,OAAA,CAAS;IAC7B,MAAMuC,OAAA,GAAU,IAAIvC,OAAA,CAAS;IAE7B,MAAMwC,OAAA,GAAU,IAAIxC,OAAA,CAAS;IAC7B,MAAMyC,OAAA,GAAU,IAAIzC,OAAA,CAAS;IAC7B,MAAM0C,OAAA,GAAU,IAAI1C,OAAA,CAAS;IAC7B,MAAM2C,OAAA,GAAU,IAAI3C,OAAA,CAAS;IAE7B,SAAS4C,aAAalB,KAAA,EAAOkC,UAAA,EAAWC,QAAA,EAAS;MAC/C,SAAS/B,CAAA,GAAI,GAAGgB,EAAA,GAAKpB,KAAA,CAAMK,MAAA,EAAQD,CAAA,GAAIgB,EAAA,EAAIhB,CAAA,IAAK;QAC9C,MAAMiB,MAAA,GAASrB,KAAA,CAAMI,CAAC;QACtB,MAAMkB,MAAA,GAAStB,KAAA,EAAOI,CAAA,GAAI,KAAKgB,EAAE;QAEjCV,OAAA,CAAQzB,IAAA,CAAKoC,MAAM;QACnBX,OAAA,CAAQP,eAAA,CAAgB1B,UAAU;QAClCiC,OAAA,CAAQJ,GAAA,CAAI4B,UAAS;QAErBvB,OAAA,CAAQ1B,IAAA,CAAKqC,MAAM;QACnBX,OAAA,CAAQR,eAAA,CAAgB1B,UAAU;QAClCkC,OAAA,CAAQL,GAAA,CAAI4B,UAAS;QAErBtB,OAAA,CAAQ3B,IAAA,CAAKqC,MAAM;QACnBV,OAAA,CAAQT,eAAA,CAAgB1B,UAAU;QAClCmC,OAAA,CAAQN,GAAA,CAAI6B,QAAO;QAEnBtB,OAAA,CAAQ5B,IAAA,CAAKoC,MAAM;QACnBR,OAAA,CAAQV,eAAA,CAAgB1B,UAAU;QAClCoC,OAAA,CAAQP,GAAA,CAAI6B,QAAO;QAEnBnE,QAAA,CAASyB,IAAA,CAAKiB,OAAA,CAAQH,CAAA,EAAGG,OAAA,CAAQF,CAAA,EAAGE,OAAA,CAAQD,CAAC;QAC7CzC,QAAA,CAASyB,IAAA,CAAKkB,OAAA,CAAQJ,CAAA,EAAGI,OAAA,CAAQH,CAAA,EAAGG,OAAA,CAAQF,CAAC;QAC7CzC,QAAA,CAASyB,IAAA,CAAKoB,OAAA,CAAQN,CAAA,EAAGM,OAAA,CAAQL,CAAA,EAAGK,OAAA,CAAQJ,CAAC;QAE7CzC,QAAA,CAASyB,IAAA,CAAKkB,OAAA,CAAQJ,CAAA,EAAGI,OAAA,CAAQH,CAAA,EAAGG,OAAA,CAAQF,CAAC;QAC7CzC,QAAA,CAASyB,IAAA,CAAKmB,OAAA,CAAQL,CAAA,EAAGK,OAAA,CAAQJ,CAAA,EAAGI,OAAA,CAAQH,CAAC;QAC7CzC,QAAA,CAASyB,IAAA,CAAKoB,OAAA,CAAQN,CAAA,EAAGM,OAAA,CAAQL,CAAA,EAAGK,OAAA,CAAQJ,CAAC;QAI7CK,OAAA,CAAQ7B,IAAA,CAAKoC,MAAM;QACnBP,OAAA,CAAQX,eAAA,CAAgB1B,UAAU;QAClCqC,OAAA,CAAQS,SAAA,CAAW;QAEnBR,OAAA,CAAQ9B,IAAA,CAAKqC,MAAM;QACnBP,OAAA,CAAQZ,eAAA,CAAgB1B,UAAU;QAClCsC,OAAA,CAAQQ,SAAA,CAAW;QAEnBP,OAAA,CAAQ/B,IAAA,CAAKqC,MAAM;QACnBN,OAAA,CAAQb,eAAA,CAAgB1B,UAAU;QAClCuC,OAAA,CAAQO,SAAA,CAAW;QAEnBN,OAAA,CAAQhC,IAAA,CAAKoC,MAAM;QACnBJ,OAAA,CAAQd,eAAA,CAAgB1B,UAAU;QAClCwC,OAAA,CAAQM,SAAA,CAAW;QAEnBtD,OAAA,CAAQwB,IAAA,CAAKqB,OAAA,CAAQP,CAAA,EAAGO,OAAA,CAAQN,CAAA,EAAGM,OAAA,CAAQL,CAAC;QAC5CxC,OAAA,CAAQwB,IAAA,CAAKsB,OAAA,CAAQR,CAAA,EAAGQ,OAAA,CAAQP,CAAA,EAAGO,OAAA,CAAQN,CAAC;QAC5CxC,OAAA,CAAQwB,IAAA,CAAKwB,OAAA,CAAQV,CAAA,EAAGU,OAAA,CAAQT,CAAA,EAAGS,OAAA,CAAQR,CAAC;QAE5CxC,OAAA,CAAQwB,IAAA,CAAKsB,OAAA,CAAQR,CAAA,EAAGQ,OAAA,CAAQP,CAAA,EAAGO,OAAA,CAAQN,CAAC;QAC5CxC,OAAA,CAAQwB,IAAA,CAAKuB,OAAA,CAAQT,CAAA,EAAGS,OAAA,CAAQR,CAAA,EAAGQ,OAAA,CAAQP,CAAC;QAC5CxC,OAAA,CAAQwB,IAAA,CAAKwB,OAAA,CAAQV,CAAA,EAAGU,OAAA,CAAQT,CAAA,EAAGS,OAAA,CAAQR,CAAC;MAC7C;IACF;IAED,MAAM2B,SAAA,GAAY,IAAI9D,OAAA,CAAS;IAC/B,MAAM+D,OAAA,GAAU,IAAI/D,OAAA,CAAS;IAE7B,SAASiB,CAAA,GAAI,GAAGA,CAAA,IAAKxB,SAAA,EAAWwB,CAAA,IAAK;MACnCR,KAAA,CAAME,IAAA,CAAKnB,KAAA,CAAMoB,UAAA,CAAWK,CAAA,GAAIxB,SAAS,CAAC;MAC1CiE,OAAA,CAAQ/C,IAAA,CAAKnB,KAAA,CAAMwE,YAAA,CAAa/C,CAAA,GAAIxB,SAAS,CAAC;MAE9C,MAAMyB,KAAA,GAAQX,IAAA,CAAK8C,KAAA,CAAMK,OAAA,CAAQzB,CAAA,EAAGyB,OAAA,CAAQvB,CAAC;MAE7ChC,UAAA,CAAWG,gBAAA,CAAiBP,EAAA,EAAImB,KAAK;MAIrC,IAAIT,KAAA,CAAMyB,CAAA,GAAI,IAAI;QAChB4B,SAAA,CAAUlC,GAAA,CAAI,OAAO,OAAO,CAAC;QAC7BkC,SAAA,CAAUjC,eAAA,CAAgB1B,UAAU;QACpC2D,SAAA,CAAU9B,GAAA,CAAIvB,KAAK;QAEnBsD,OAAA,CAAQnC,GAAA,CAAI,MAAM,OAAO,CAAC;QAC1BmC,OAAA,CAAQlC,eAAA,CAAgB1B,UAAU;QAClC4D,OAAA,CAAQ/B,GAAA,CAAIvB,KAAK;QAEjBmC,YAAA,CAAa5B,KAAA,EAAO8C,SAAA,EAAWC,OAAO;QAEtCD,SAAA,CAAUlC,GAAA,CAAI,MAAM,MAAM,CAAC;QAC3BkC,SAAA,CAAUjC,eAAA,CAAgB1B,UAAU;QACpC2D,SAAA,CAAU9B,GAAA,CAAIvB,KAAK;QAEnBsD,OAAA,CAAQnC,GAAA,CAAI,MAAM,CAACnB,KAAA,CAAMyB,CAAA,EAAG,CAAC;QAC7B6B,OAAA,CAAQlC,eAAA,CAAgB1B,UAAU;QAClC4D,OAAA,CAAQ/B,GAAA,CAAIvB,KAAK;QAEjBmC,YAAA,CAAatB,KAAA,EAAOwC,SAAA,EAAWC,OAAO;QAEtCD,SAAA,CAAUlC,GAAA,CAAI,KAAK,MAAM,CAAC;QAC1BkC,SAAA,CAAUjC,eAAA,CAAgB1B,UAAU;QACpC2D,SAAA,CAAU9B,GAAA,CAAIvB,KAAK;QAEnBsD,OAAA,CAAQnC,GAAA,CAAI,KAAK,CAACnB,KAAA,CAAMyB,CAAA,EAAG,CAAC;QAC5B6B,OAAA,CAAQlC,eAAA,CAAgB1B,UAAU;QAClC4D,OAAA,CAAQ/B,GAAA,CAAIvB,KAAK;QAEjBmC,YAAA,CAAae,KAAA,EAAOG,SAAA,EAAWC,OAAO;MAC9C,OAAa;QACLD,SAAA,CAAUlC,GAAA,CAAI,GAAG,MAAM,CAAC;QACxBkC,SAAA,CAAUjC,eAAA,CAAgB1B,UAAU;QACpC2D,SAAA,CAAU9B,GAAA,CAAIvB,KAAK;QAEnBsD,OAAA,CAAQnC,GAAA,CAAI,GAAG,CAACnB,KAAA,CAAMyB,CAAA,EAAG,CAAC;QAC1B6B,OAAA,CAAQlC,eAAA,CAAgB1B,UAAU;QAClC4D,OAAA,CAAQ/B,GAAA,CAAIvB,KAAK;QAEjBmC,YAAA,CAAae,KAAA,EAAOG,SAAA,EAAWC,OAAO;MACvC;IACF;IAED,KAAKT,YAAA,CAAa,YAAY,IAAIC,eAAA,CAAgB,IAAIC,YAAA,CAAa9D,QAAQ,GAAG,CAAC,CAAC;IAChF,KAAK4D,YAAA,CAAa,UAAU,IAAIC,eAAA,CAAgB,IAAIC,YAAA,CAAa7D,OAAO,GAAG,CAAC,CAAC;EAC9E;AACH;AAEA,MAAMsE,2BAAA,SAAoC3E,cAAA,CAAe;EACvDC,YAAYC,KAAA,EAAOC,SAAA,EAAW;IAC5B,MAAO;IAEP,MAAMC,QAAA,GAAW,EAAE;IAEnB,MAAMK,EAAA,GAAK,IAAIC,OAAA,CAAQ,GAAG,GAAG,CAAC;IAC9B,MAAMC,OAAA,GAAU,IAAID,OAAA,CAAS;IAE7B,MAAMG,UAAA,GAAa,IAAIC,UAAA,CAAY;IACnC,MAAMC,cAAA,GAAiB,IAAID,UAAA,CAAY;IACvCC,cAAA,CAAeC,gBAAA,CAAiBP,EAAA,EAAIQ,IAAA,CAAKC,EAAA,GAAK,CAAC;IAE/C,MAAMC,KAAA,GAAQ,IAAIT,OAAA,CAAS;IAE3B,MAAMU,SAAA,GAAY,IAAIV,OAAA,CAAS;IAC/BU,SAAA,CAAUC,IAAA,CAAKnB,KAAA,CAAMoB,UAAA,CAAW,CAAC,CAAC;IAClCF,SAAA,CAAUwB,CAAA,GAAI;IAEd,MAAME,OAAA,GAAU,IAAIpC,OAAA,CAAS;IAC7B,MAAMqC,OAAA,GAAU,IAAIrC,OAAA,CAAS;IAC7B,MAAMsC,OAAA,GAAU,IAAItC,OAAA,CAAS;IAC7B,MAAMuC,OAAA,GAAU,IAAIvC,OAAA,CAAS;IAE7B,SAASiB,CAAA,GAAI,GAAGA,CAAA,IAAKxB,SAAA,EAAWwB,CAAA,IAAK;MACnCR,KAAA,CAAME,IAAA,CAAKnB,KAAA,CAAMoB,UAAA,CAAWK,CAAA,GAAIxB,SAAS,CAAC;MAC1CgB,KAAA,CAAMyB,CAAA,GAAI;MAEVjC,OAAA,CAAQkD,UAAA,CAAW1C,KAAA,EAAOC,SAAS;MAEnC,MAAMQ,KAAA,GAAQX,IAAA,CAAK8C,KAAA,CAAMpD,OAAA,CAAQgC,CAAA,EAAGhC,OAAA,CAAQkC,CAAC;MAE7ChC,UAAA,CAAWG,gBAAA,CAAiBP,EAAA,EAAImB,KAAK;MAErCkB,OAAA,CAAQR,GAAA,CAAI,MAAM,GAAG,CAAC;MACtBQ,OAAA,CAAQP,eAAA,CAAgB1B,UAAU;MAClCiC,OAAA,CAAQJ,GAAA,CAAIvB,KAAK;MAEjB4B,OAAA,CAAQT,GAAA,CAAI,KAAK,GAAG,CAAC;MACrBS,OAAA,CAAQR,eAAA,CAAgB1B,UAAU;MAClCkC,OAAA,CAAQL,GAAA,CAAIvB,KAAK;MAEjB6B,OAAA,CAAQV,GAAA,CAAI,KAAK,GAAG,CAAC;MACrBU,OAAA,CAAQT,eAAA,CAAgBxB,cAAc;MACtCiC,OAAA,CAAQN,GAAA,CAAItB,SAAS;MAErB6B,OAAA,CAAQX,GAAA,CAAI,MAAM,GAAG,CAAC;MACtBW,OAAA,CAAQV,eAAA,CAAgBxB,cAAc;MACtCkC,OAAA,CAAQP,GAAA,CAAItB,SAAS;MAErBhB,QAAA,CAASyB,IAAA,CAAKiB,OAAA,CAAQH,CAAA,EAAGG,OAAA,CAAQF,CAAA,EAAGE,OAAA,CAAQD,CAAC;MAC7CzC,QAAA,CAASyB,IAAA,CAAKkB,OAAA,CAAQJ,CAAA,EAAGI,OAAA,CAAQH,CAAA,EAAGG,OAAA,CAAQF,CAAC;MAC7CzC,QAAA,CAASyB,IAAA,CAAKoB,OAAA,CAAQN,CAAA,EAAGM,OAAA,CAAQL,CAAA,EAAGK,OAAA,CAAQJ,CAAC;MAE7CzC,QAAA,CAASyB,IAAA,CAAKkB,OAAA,CAAQJ,CAAA,EAAGI,OAAA,CAAQH,CAAA,EAAGG,OAAA,CAAQF,CAAC;MAC7CzC,QAAA,CAASyB,IAAA,CAAKmB,OAAA,CAAQL,CAAA,EAAGK,OAAA,CAAQJ,CAAA,EAAGI,OAAA,CAAQH,CAAC;MAC7CzC,QAAA,CAASyB,IAAA,CAAKoB,OAAA,CAAQN,CAAA,EAAGM,OAAA,CAAQL,CAAA,EAAGK,OAAA,CAAQJ,CAAC;MAE7CzB,SAAA,CAAUC,IAAA,CAAKF,KAAK;MACpBJ,cAAA,CAAeM,IAAA,CAAKR,UAAU;IAC/B;IAED,KAAKmD,YAAA,CAAa,YAAY,IAAIC,eAAA,CAAgB,IAAIC,YAAA,CAAa9D,QAAQ,GAAG,CAAC,CAAC;EACjF;AACH;AAEA,MAAMwE,WAAA,SAAoB5E,cAAA,CAAe;EACvCC,YAAA,EAAc;IACZ,MAAO;IAEP,MAAMG,QAAA,GAAW,EAAE;IAEnB,SAASuB,CAAA,GAAI,GAAGA,CAAA,GAAI,KAAKA,CAAA,IAAK;MAC5B,MAAMgB,CAAA,GAAI1B,IAAA,CAAK4D,MAAA,CAAQ,IAAG,MAAM;MAChC,MAAMjC,CAAA,GAAI3B,IAAA,CAAK4D,MAAA,CAAQ,IAAG,KAAK;MAC/B,MAAMhC,CAAA,GAAI5B,IAAA,CAAK4D,MAAA,CAAQ,IAAG,MAAM;MAEhC,MAAMC,IAAA,GAAO7D,IAAA,CAAK4D,MAAA,CAAQ,IAAG,KAAK;MAElCzE,QAAA,CAASyB,IAAA,CAAKc,CAAA,GAAImC,IAAA,EAAMlC,CAAA,EAAGC,CAAA,GAAIiC,IAAI;MACnC1E,QAAA,CAASyB,IAAA,CAAKc,CAAA,GAAImC,IAAA,EAAMlC,CAAA,EAAGC,CAAA,GAAIiC,IAAI;MACnC1E,QAAA,CAASyB,IAAA,CAAKc,CAAA,GAAImC,IAAA,EAAMlC,CAAA,EAAGC,CAAA,GAAIiC,IAAI;MAEnC1E,QAAA,CAASyB,IAAA,CAAKc,CAAA,GAAImC,IAAA,EAAMlC,CAAA,EAAGC,CAAA,GAAIiC,IAAI;MACnC1E,QAAA,CAASyB,IAAA,CAAKc,CAAA,GAAImC,IAAA,EAAMlC,CAAA,EAAGC,CAAA,GAAIiC,IAAI;MACnC1E,QAAA,CAASyB,IAAA,CAAKc,CAAA,GAAImC,IAAA,EAAMlC,CAAA,EAAGC,CAAA,GAAIiC,IAAI;IACpC;IAED,KAAKd,YAAA,CAAa,YAAY,IAAIC,eAAA,CAAgB,IAAIC,YAAA,CAAa9D,QAAQ,GAAG,CAAC,CAAC;EACjF;AACH;AAEA,MAAM2E,aAAA,SAAsB/E,cAAA,CAAe;EACzCC,YAAY+E,SAAA,EAAW;IACrB,MAAO;IAEP,MAAM5E,QAAA,GAAW,EAAE;IACnB,MAAME,MAAA,GAAS,EAAE;IAEjB,MAAM2E,SAAA,GAAY,IAAIC,SAAA,CAAW;IACjCD,SAAA,CAAUE,GAAA,CAAIC,SAAA,CAAU9C,GAAA,CAAI,GAAG,IAAI,CAAC;IAEpC,MAAM+C,MAAA,GAAS,IAAIC,KAAA,CAAO;IAE1B,SAAS3D,CAAA,GAAI,GAAGA,CAAA,GAAI,KAAMA,CAAA,IAAK;MAC7B,MAAMgB,CAAA,GAAI1B,IAAA,CAAK4D,MAAA,CAAQ,IAAG,MAAM;MAChC,MAAMhC,CAAA,GAAI5B,IAAA,CAAK4D,MAAA,CAAQ,IAAG,MAAM;MAEhCI,SAAA,CAAUE,GAAA,CAAII,MAAA,CAAOjD,GAAA,CAAIK,CAAA,EAAG,IAAIE,CAAC;MAEjC,MAAM2C,aAAA,GAAgBP,SAAA,CAAUQ,eAAA,CAAgBT,SAAS;MAEzD,IAAIQ,aAAA,CAAc/C,MAAA,KAAW,GAAG;MAEhC,MAAMG,CAAA,GAAI4C,aAAA,CAAc,CAAC,EAAErE,KAAA,CAAMyB,CAAA;MAEjC,MAAM8C,MAAA,GAASzE,IAAA,CAAK4D,MAAA,CAAQ,IAAG,IAAI;MAEnC,IAAIjD,KAAA,GAAQX,IAAA,CAAK4D,MAAA,CAAQ,IAAG5D,IAAA,CAAKC,EAAA,GAAK;MAEtCd,QAAA,CAASyB,IAAA,CAAKc,CAAA,GAAI1B,IAAA,CAAKa,GAAA,CAAIF,KAAK,GAAGgB,CAAA,EAAGC,CAAA,GAAI5B,IAAA,CAAKc,GAAA,CAAIH,KAAK,CAAC;MACzDxB,QAAA,CAASyB,IAAA,CAAKc,CAAA,EAAGC,CAAA,GAAI8C,MAAA,EAAQ7C,CAAC;MAC9BzC,QAAA,CAASyB,IAAA,CAAKc,CAAA,GAAI1B,IAAA,CAAKa,GAAA,CAAIF,KAAA,GAAQX,IAAA,CAAKC,EAAE,GAAG0B,CAAA,EAAGC,CAAA,GAAI5B,IAAA,CAAKc,GAAA,CAAIH,KAAA,GAAQX,IAAA,CAAKC,EAAE,CAAC;MAE7EU,KAAA,IAASX,IAAA,CAAKC,EAAA,GAAK;MAEnBd,QAAA,CAASyB,IAAA,CAAKc,CAAA,GAAI1B,IAAA,CAAKa,GAAA,CAAIF,KAAK,GAAGgB,CAAA,EAAGC,CAAA,GAAI5B,IAAA,CAAKc,GAAA,CAAIH,KAAK,CAAC;MACzDxB,QAAA,CAASyB,IAAA,CAAKc,CAAA,EAAGC,CAAA,GAAI8C,MAAA,EAAQ7C,CAAC;MAC9BzC,QAAA,CAASyB,IAAA,CAAKc,CAAA,GAAI1B,IAAA,CAAKa,GAAA,CAAIF,KAAA,GAAQX,IAAA,CAAKC,EAAE,GAAG0B,CAAA,EAAGC,CAAA,GAAI5B,IAAA,CAAKc,GAAA,CAAIH,KAAA,GAAQX,IAAA,CAAKC,EAAE,CAAC;MAE7E,MAAM2D,MAAA,GAAS5D,IAAA,CAAK4D,MAAA,CAAM,IAAK;MAE/B,SAASrC,CAAA,GAAI,GAAGA,CAAA,GAAI,GAAGA,CAAA,IAAK;QAC1B6C,MAAA,CAAOM,MAAA,CAAO,MAAMd,MAAA,EAAQ,MAAMA,MAAA,EAAQ,GAAG,MAAM;QAEnDvE,MAAA,CAAOuB,IAAA,CAAKwD,MAAA,CAAOO,CAAA,EAAGP,MAAA,CAAOQ,CAAA,EAAGR,MAAA,CAAOS,CAAC;MACzC;IACF;IAED,KAAK9B,YAAA,CAAa,YAAY,IAAIC,eAAA,CAAgB,IAAIC,YAAA,CAAa9D,QAAQ,GAAG,CAAC,CAAC;IAChF,KAAK4D,YAAA,CAAa,SAAS,IAAIC,eAAA,CAAgB,IAAIC,YAAA,CAAa5D,MAAM,GAAG,CAAC,CAAC;EAC5E;AACH", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}