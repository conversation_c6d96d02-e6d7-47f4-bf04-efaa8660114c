{"ast": null, "code": "import { <PERSON>bject3<PERSON>, Box3, <PERSON><PERSON><PERSON><PERSON>, MeshLambertMaterial, TextureLoader, UVMapping } from \"three\";\nimport { MD2Loader } from \"../loaders/MD2Loader.js\";\nimport { MorphBlendMesh } from \"./MorphBlendMesh.js\";\nclass MD2CharacterComplex {\n  constructor() {\n    this.scale = 1;\n    this.animationFPS = 6;\n    this.transitionFrames = 15;\n    this.maxSpeed = 275;\n    this.maxReverseSpeed = -275;\n    this.frontAcceleration = 600;\n    this.backAcceleration = 600;\n    this.frontDecceleration = 600;\n    this.angularSpeed = 2.5;\n    this.root = new Object3D();\n    this.meshBody = null;\n    this.meshWeapon = null;\n    this.controls = null;\n    this.skinsBody = [];\n    this.skinsWeapon = [];\n    this.weapons = [];\n    this.currentSkin = void 0;\n    this.onLoadComplete = function () {};\n    this.meshes = [];\n    this.animations = {};\n    this.loadCounter = 0;\n    this.speed = 0;\n    this.bodyOrientation = 0;\n    this.walkSpeed = this.maxSpeed;\n    this.crouchSpeed = this.maxSpeed * 0.5;\n    this.activeAnimation = null;\n    this.oldAnimation = null;\n  }\n  enableShadows(enable) {\n    for (let i = 0; i < this.meshes.length; i++) {\n      this.meshes[i].castShadow = enable;\n      this.meshes[i].receiveShadow = enable;\n    }\n  }\n  setVisible(enable) {\n    for (let i = 0; i < this.meshes.length; i++) {\n      this.meshes[i].visible = enable;\n      this.meshes[i].visible = enable;\n    }\n  }\n  shareParts(original) {\n    this.animations = original.animations;\n    this.walkSpeed = original.walkSpeed;\n    this.crouchSpeed = original.crouchSpeed;\n    this.skinsBody = original.skinsBody;\n    this.skinsWeapon = original.skinsWeapon;\n    const mesh = this._createPart(original.meshBody.geometry, this.skinsBody[0]);\n    mesh.scale.set(this.scale, this.scale, this.scale);\n    this.root.position.y = original.root.position.y;\n    this.root.add(mesh);\n    this.meshBody = mesh;\n    this.meshes.push(mesh);\n    for (let i = 0; i < original.weapons.length; i++) {\n      const meshWeapon = this._createPart(original.weapons[i].geometry, this.skinsWeapon[i]);\n      meshWeapon.scale.set(this.scale, this.scale, this.scale);\n      meshWeapon.visible = false;\n      meshWeapon.name = original.weapons[i].name;\n      this.root.add(meshWeapon);\n      this.weapons[i] = meshWeapon;\n      this.meshWeapon = meshWeapon;\n      this.meshes.push(meshWeapon);\n    }\n  }\n  loadParts(config) {\n    const scope = this;\n    function loadTextures(baseUrl, textureUrls) {\n      const textureLoader = new TextureLoader();\n      const textures = [];\n      for (let i = 0; i < textureUrls.length; i++) {\n        textures[i] = textureLoader.load(baseUrl + textureUrls[i], checkLoadingComplete);\n        textures[i].mapping = UVMapping;\n        textures[i].name = textureUrls[i];\n        if (\"colorSpace\" in textures[i]) textures[i].colorSpace = \"srgb\";else textures[i].encoding = 3001;\n      }\n      return textures;\n    }\n    function checkLoadingComplete() {\n      scope.loadCounter -= 1;\n      if (scope.loadCounter === 0) scope.onLoadComplete();\n    }\n    this.animations = config.animations;\n    this.walkSpeed = config.walkSpeed;\n    this.crouchSpeed = config.crouchSpeed;\n    this.loadCounter = config.weapons.length * 2 + config.skins.length + 1;\n    const weaponsTextures = [];\n    for (let i = 0; i < config.weapons.length; i++) weaponsTextures[i] = config.weapons[i][1];\n    this.skinsBody = loadTextures(config.baseUrl + \"skins/\", config.skins);\n    this.skinsWeapon = loadTextures(config.baseUrl + \"skins/\", weaponsTextures);\n    const loader = new MD2Loader();\n    loader.load(config.baseUrl + config.body, function (geo) {\n      const boundingBox = new Box3();\n      boundingBox.setFromBufferAttribute(geo.attributes.position);\n      scope.root.position.y = -scope.scale * boundingBox.min.y;\n      const mesh = scope._createPart(geo, scope.skinsBody[0]);\n      mesh.scale.set(scope.scale, scope.scale, scope.scale);\n      scope.root.add(mesh);\n      scope.meshBody = mesh;\n      scope.meshes.push(mesh);\n      checkLoadingComplete();\n    });\n    const generateCallback = function (index, name) {\n      return function (geo) {\n        const mesh = scope._createPart(geo, scope.skinsWeapon[index]);\n        mesh.scale.set(scope.scale, scope.scale, scope.scale);\n        mesh.visible = false;\n        mesh.name = name;\n        scope.root.add(mesh);\n        scope.weapons[index] = mesh;\n        scope.meshWeapon = mesh;\n        scope.meshes.push(mesh);\n        checkLoadingComplete();\n      };\n    };\n    for (let i = 0; i < config.weapons.length; i++) {\n      loader.load(config.baseUrl + config.weapons[i][0], generateCallback(i, config.weapons[i][0]));\n    }\n  }\n  setPlaybackRate(rate) {\n    if (this.meshBody) this.meshBody.duration = this.meshBody.baseDuration / rate;\n    if (this.meshWeapon) this.meshWeapon.duration = this.meshWeapon.baseDuration / rate;\n  }\n  setWireframe(wireframeEnabled) {\n    if (wireframeEnabled) {\n      if (this.meshBody) this.meshBody.material = this.meshBody.materialWireframe;\n      if (this.meshWeapon) this.meshWeapon.material = this.meshWeapon.materialWireframe;\n    } else {\n      if (this.meshBody) this.meshBody.material = this.meshBody.materialTexture;\n      if (this.meshWeapon) this.meshWeapon.material = this.meshWeapon.materialTexture;\n    }\n  }\n  setSkin(index) {\n    if (this.meshBody && this.meshBody.material.wireframe === false) {\n      this.meshBody.material.map = this.skinsBody[index];\n      this.currentSkin = index;\n    }\n  }\n  setWeapon(index) {\n    for (let i = 0; i < this.weapons.length; i++) this.weapons[i].visible = false;\n    const activeWeapon = this.weapons[index];\n    if (activeWeapon) {\n      activeWeapon.visible = true;\n      this.meshWeapon = activeWeapon;\n      if (this.activeAnimation) {\n        activeWeapon.playAnimation(this.activeAnimation);\n        this.meshWeapon.setAnimationTime(this.activeAnimation, this.meshBody.getAnimationTime(this.activeAnimation));\n      }\n    }\n  }\n  setAnimation(animationName) {\n    if (animationName === this.activeAnimation || !animationName) return;\n    if (this.meshBody) {\n      this.meshBody.setAnimationWeight(animationName, 0);\n      this.meshBody.playAnimation(animationName);\n      this.oldAnimation = this.activeAnimation;\n      this.activeAnimation = animationName;\n      this.blendCounter = this.transitionFrames;\n    }\n    if (this.meshWeapon) {\n      this.meshWeapon.setAnimationWeight(animationName, 0);\n      this.meshWeapon.playAnimation(animationName);\n    }\n  }\n  update(delta) {\n    if (this.controls) this.updateMovementModel(delta);\n    if (this.animations) {\n      this.updateBehaviors();\n      this.updateAnimations(delta);\n    }\n  }\n  updateAnimations(delta) {\n    let mix = 1;\n    if (this.blendCounter > 0) {\n      mix = (this.transitionFrames - this.blendCounter) / this.transitionFrames;\n      this.blendCounter -= 1;\n    }\n    if (this.meshBody) {\n      this.meshBody.update(delta);\n      this.meshBody.setAnimationWeight(this.activeAnimation, mix);\n      this.meshBody.setAnimationWeight(this.oldAnimation, 1 - mix);\n    }\n    if (this.meshWeapon) {\n      this.meshWeapon.update(delta);\n      this.meshWeapon.setAnimationWeight(this.activeAnimation, mix);\n      this.meshWeapon.setAnimationWeight(this.oldAnimation, 1 - mix);\n    }\n  }\n  updateBehaviors() {\n    const controls = this.controls;\n    const animations = this.animations;\n    let moveAnimation, idleAnimation;\n    if (controls.crouch) {\n      moveAnimation = animations[\"crouchMove\"];\n      idleAnimation = animations[\"crouchIdle\"];\n    } else {\n      moveAnimation = animations[\"move\"];\n      idleAnimation = animations[\"idle\"];\n    }\n    if (controls.jump) {\n      moveAnimation = animations[\"jump\"];\n      idleAnimation = animations[\"jump\"];\n    }\n    if (controls.attack) {\n      if (controls.crouch) {\n        moveAnimation = animations[\"crouchAttack\"];\n        idleAnimation = animations[\"crouchAttack\"];\n      } else {\n        moveAnimation = animations[\"attack\"];\n        idleAnimation = animations[\"attack\"];\n      }\n    }\n    if (controls.moveForward || controls.moveBackward || controls.moveLeft || controls.moveRight) {\n      if (this.activeAnimation !== moveAnimation) {\n        this.setAnimation(moveAnimation);\n      }\n    }\n    if (Math.abs(this.speed) < 0.2 * this.maxSpeed && !(controls.moveLeft || controls.moveRight || controls.moveForward || controls.moveBackward)) {\n      if (this.activeAnimation !== idleAnimation) {\n        this.setAnimation(idleAnimation);\n      }\n    }\n    if (controls.moveForward) {\n      if (this.meshBody) {\n        this.meshBody.setAnimationDirectionForward(this.activeAnimation);\n        this.meshBody.setAnimationDirectionForward(this.oldAnimation);\n      }\n      if (this.meshWeapon) {\n        this.meshWeapon.setAnimationDirectionForward(this.activeAnimation);\n        this.meshWeapon.setAnimationDirectionForward(this.oldAnimation);\n      }\n    }\n    if (controls.moveBackward) {\n      if (this.meshBody) {\n        this.meshBody.setAnimationDirectionBackward(this.activeAnimation);\n        this.meshBody.setAnimationDirectionBackward(this.oldAnimation);\n      }\n      if (this.meshWeapon) {\n        this.meshWeapon.setAnimationDirectionBackward(this.activeAnimation);\n        this.meshWeapon.setAnimationDirectionBackward(this.oldAnimation);\n      }\n    }\n  }\n  updateMovementModel(delta) {\n    function exponentialEaseOut(k) {\n      return k === 1 ? 1 : -Math.pow(2, -10 * k) + 1;\n    }\n    const controls = this.controls;\n    if (controls.crouch) this.maxSpeed = this.crouchSpeed;else this.maxSpeed = this.walkSpeed;\n    this.maxReverseSpeed = -this.maxSpeed;\n    if (controls.moveForward) this.speed = MathUtils.clamp(this.speed + delta * this.frontAcceleration, this.maxReverseSpeed, this.maxSpeed);\n    if (controls.moveBackward) this.speed = MathUtils.clamp(this.speed - delta * this.backAcceleration, this.maxReverseSpeed, this.maxSpeed);\n    const dir = 1;\n    if (controls.moveLeft) {\n      this.bodyOrientation += delta * this.angularSpeed;\n      this.speed = MathUtils.clamp(this.speed + dir * delta * this.frontAcceleration, this.maxReverseSpeed, this.maxSpeed);\n    }\n    if (controls.moveRight) {\n      this.bodyOrientation -= delta * this.angularSpeed;\n      this.speed = MathUtils.clamp(this.speed + dir * delta * this.frontAcceleration, this.maxReverseSpeed, this.maxSpeed);\n    }\n    if (!(controls.moveForward || controls.moveBackward)) {\n      if (this.speed > 0) {\n        const k = exponentialEaseOut(this.speed / this.maxSpeed);\n        this.speed = MathUtils.clamp(this.speed - k * delta * this.frontDecceleration, 0, this.maxSpeed);\n      } else {\n        const k = exponentialEaseOut(this.speed / this.maxReverseSpeed);\n        this.speed = MathUtils.clamp(this.speed + k * delta * this.backAcceleration, this.maxReverseSpeed, 0);\n      }\n    }\n    const forwardDelta = this.speed * delta;\n    this.root.position.x += Math.sin(this.bodyOrientation) * forwardDelta;\n    this.root.position.z += Math.cos(this.bodyOrientation) * forwardDelta;\n    this.root.rotation.y = this.bodyOrientation;\n  }\n  // internal\n  _createPart(geometry, skinMap) {\n    const materialWireframe = new MeshLambertMaterial({\n      color: 16755200,\n      wireframe: true,\n      morphTargets: true,\n      morphNormals: true\n    });\n    const materialTexture = new MeshLambertMaterial({\n      color: 16777215,\n      wireframe: false,\n      map: skinMap,\n      morphTargets: true,\n      morphNormals: true\n    });\n    const mesh = new MorphBlendMesh(geometry, materialTexture);\n    mesh.rotation.y = -Math.PI / 2;\n    mesh.materialTexture = materialTexture;\n    mesh.materialWireframe = materialWireframe;\n    mesh.autoCreateAnimations(this.animationFPS);\n    return mesh;\n  }\n}\nexport { MD2CharacterComplex };", "map": {"version": 3, "names": ["MD2CharacterComplex", "constructor", "scale", "animationFPS", "transitionFrames", "maxSpeed", "maxReverseSpeed", "frontAcceleration", "backAcceleration", "frontDecceleration", "angularSpeed", "root", "Object3D", "meshBody", "meshWeapon", "controls", "skinsBody", "skinsWeapon", "weapons", "currentSkin", "onLoadComplete", "meshes", "animations", "loadCounter", "speed", "bodyOrientation", "walkSpeed", "crouchSpeed", "activeAnimation", "oldAnimation", "enableShadows", "enable", "i", "length", "<PERSON><PERSON><PERSON><PERSON>", "receiveShadow", "setVisible", "visible", "shareParts", "original", "mesh", "_createPart", "geometry", "set", "position", "y", "add", "push", "name", "loadParts", "config", "scope", "loadTextures", "baseUrl", "textureUrls", "textureLoader", "TextureLoader", "textures", "load", "checkLoadingComplete", "mapping", "UVMapping", "colorSpace", "encoding", "skins", "weaponsTextures", "loader", "MD2Loader", "body", "geo", "boundingBox", "Box3", "setFromBufferAttribute", "attributes", "min", "generateCallback", "index", "setPlaybackRate", "rate", "duration", "baseDuration", "setWireframe", "wireframeEnabled", "material", "materialWireframe", "materialTexture", "<PERSON><PERSON><PERSON>", "wireframe", "map", "setWeapon", "activeWeapon", "playAnimation", "setAnimationTime", "getAnimationTime", "setAnimation", "animationName", "setAnimationWeight", "blendCounter", "update", "delta", "updateMovementModel", "updateBehaviors", "updateAnimations", "mix", "moveAnimation", "idleAnimation", "crouch", "jump", "attack", "moveForward", "moveBackward", "moveLeft", "moveRight", "Math", "abs", "setAnimationDirectionForward", "setAnimationDirectionBackward", "exponentialEaseOut", "k", "pow", "MathUtils", "clamp", "dir", "<PERSON><PERSON><PERSON><PERSON>", "x", "sin", "z", "cos", "rotation", "skinMap", "MeshLambertMaterial", "color", "morphTargets", "morphNormals", "MorphBlendMesh", "PI", "autoCreateAnimations"], "sources": ["C:\\Users\\<USER>\\Documents\\Apps_Creations\\SiahMan_Racing4Speed_WebGame\\siah-man-racing\\node_modules\\src\\misc\\MD2CharacterComplex.js"], "sourcesContent": ["import { Box3, <PERSON><PERSON><PERSON>s, MeshLambertMaterial, Object3D, TextureLoader, UVMapping } from 'three'\nimport { MD2Loader } from '../loaders/MD2Loader'\nimport { MorphBlendMesh } from '../misc/MorphBlendMesh'\n\nclass MD2CharacterComplex {\n  constructor() {\n    this.scale = 1\n\n    // animation parameters\n\n    this.animationFPS = 6\n    this.transitionFrames = 15\n\n    // movement model parameters\n\n    this.maxSpeed = 275\n    this.maxReverseSpeed = -275\n\n    this.frontAcceleration = 600\n    this.backAcceleration = 600\n\n    this.frontDecceleration = 600\n\n    this.angularSpeed = 2.5\n\n    // rig\n\n    this.root = new Object3D()\n\n    this.meshBody = null\n    this.meshWeapon = null\n\n    this.controls = null\n\n    // skins\n\n    this.skinsBody = []\n    this.skinsWeapon = []\n\n    this.weapons = []\n\n    this.currentSkin = undefined\n\n    //\n\n    this.onLoadComplete = function () {}\n\n    // internals\n\n    this.meshes = []\n    this.animations = {}\n\n    this.loadCounter = 0\n\n    // internal movement control variables\n\n    this.speed = 0\n    this.bodyOrientation = 0\n\n    this.walkSpeed = this.maxSpeed\n    this.crouchSpeed = this.maxSpeed * 0.5\n\n    // internal animation parameters\n\n    this.activeAnimation = null\n    this.oldAnimation = null\n\n    // API\n  }\n\n  enableShadows(enable) {\n    for (let i = 0; i < this.meshes.length; i++) {\n      this.meshes[i].castShadow = enable\n      this.meshes[i].receiveShadow = enable\n    }\n  }\n\n  setVisible(enable) {\n    for (let i = 0; i < this.meshes.length; i++) {\n      this.meshes[i].visible = enable\n      this.meshes[i].visible = enable\n    }\n  }\n\n  shareParts(original) {\n    this.animations = original.animations\n    this.walkSpeed = original.walkSpeed\n    this.crouchSpeed = original.crouchSpeed\n\n    this.skinsBody = original.skinsBody\n    this.skinsWeapon = original.skinsWeapon\n\n    // BODY\n\n    const mesh = this._createPart(original.meshBody.geometry, this.skinsBody[0])\n    mesh.scale.set(this.scale, this.scale, this.scale)\n\n    this.root.position.y = original.root.position.y\n    this.root.add(mesh)\n\n    this.meshBody = mesh\n\n    this.meshes.push(mesh)\n\n    // WEAPONS\n\n    for (let i = 0; i < original.weapons.length; i++) {\n      const meshWeapon = this._createPart(original.weapons[i].geometry, this.skinsWeapon[i])\n      meshWeapon.scale.set(this.scale, this.scale, this.scale)\n      meshWeapon.visible = false\n\n      meshWeapon.name = original.weapons[i].name\n\n      this.root.add(meshWeapon)\n\n      this.weapons[i] = meshWeapon\n      this.meshWeapon = meshWeapon\n\n      this.meshes.push(meshWeapon)\n    }\n  }\n\n  loadParts(config) {\n    const scope = this\n\n    function loadTextures(baseUrl, textureUrls) {\n      const textureLoader = new TextureLoader()\n      const textures = []\n\n      for (let i = 0; i < textureUrls.length; i++) {\n        textures[i] = textureLoader.load(baseUrl + textureUrls[i], checkLoadingComplete)\n        textures[i].mapping = UVMapping\n        textures[i].name = textureUrls[i]\n        if ('colorSpace' in textures[i]) textures[i].colorSpace = 'srgb'\n        else textures[i].encoding = 3001 // sRGBEncoding\n      }\n\n      return textures\n    }\n\n    function checkLoadingComplete() {\n      scope.loadCounter -= 1\n      if (scope.loadCounter === 0) scope.onLoadComplete()\n    }\n\n    this.animations = config.animations\n    this.walkSpeed = config.walkSpeed\n    this.crouchSpeed = config.crouchSpeed\n\n    this.loadCounter = config.weapons.length * 2 + config.skins.length + 1\n\n    const weaponsTextures = []\n    for (let i = 0; i < config.weapons.length; i++) weaponsTextures[i] = config.weapons[i][1]\n\n    // SKINS\n\n    this.skinsBody = loadTextures(config.baseUrl + 'skins/', config.skins)\n    this.skinsWeapon = loadTextures(config.baseUrl + 'skins/', weaponsTextures)\n\n    // BODY\n\n    const loader = new MD2Loader()\n\n    loader.load(config.baseUrl + config.body, function (geo) {\n      const boundingBox = new Box3()\n      boundingBox.setFromBufferAttribute(geo.attributes.position)\n\n      scope.root.position.y = -scope.scale * boundingBox.min.y\n\n      const mesh = scope._createPart(geo, scope.skinsBody[0])\n      mesh.scale.set(scope.scale, scope.scale, scope.scale)\n\n      scope.root.add(mesh)\n\n      scope.meshBody = mesh\n      scope.meshes.push(mesh)\n\n      checkLoadingComplete()\n    })\n\n    // WEAPONS\n\n    const generateCallback = function (index, name) {\n      return function (geo) {\n        const mesh = scope._createPart(geo, scope.skinsWeapon[index])\n        mesh.scale.set(scope.scale, scope.scale, scope.scale)\n        mesh.visible = false\n\n        mesh.name = name\n\n        scope.root.add(mesh)\n\n        scope.weapons[index] = mesh\n        scope.meshWeapon = mesh\n        scope.meshes.push(mesh)\n\n        checkLoadingComplete()\n      }\n    }\n\n    for (let i = 0; i < config.weapons.length; i++) {\n      loader.load(config.baseUrl + config.weapons[i][0], generateCallback(i, config.weapons[i][0]))\n    }\n  }\n\n  setPlaybackRate(rate) {\n    if (this.meshBody) this.meshBody.duration = this.meshBody.baseDuration / rate\n    if (this.meshWeapon) this.meshWeapon.duration = this.meshWeapon.baseDuration / rate\n  }\n\n  setWireframe(wireframeEnabled) {\n    if (wireframeEnabled) {\n      if (this.meshBody) this.meshBody.material = this.meshBody.materialWireframe\n      if (this.meshWeapon) this.meshWeapon.material = this.meshWeapon.materialWireframe\n    } else {\n      if (this.meshBody) this.meshBody.material = this.meshBody.materialTexture\n      if (this.meshWeapon) this.meshWeapon.material = this.meshWeapon.materialTexture\n    }\n  }\n\n  setSkin(index) {\n    if (this.meshBody && this.meshBody.material.wireframe === false) {\n      this.meshBody.material.map = this.skinsBody[index]\n      this.currentSkin = index\n    }\n  }\n\n  setWeapon(index) {\n    for (let i = 0; i < this.weapons.length; i++) this.weapons[i].visible = false\n\n    const activeWeapon = this.weapons[index]\n\n    if (activeWeapon) {\n      activeWeapon.visible = true\n      this.meshWeapon = activeWeapon\n\n      if (this.activeAnimation) {\n        activeWeapon.playAnimation(this.activeAnimation)\n        this.meshWeapon.setAnimationTime(this.activeAnimation, this.meshBody.getAnimationTime(this.activeAnimation))\n      }\n    }\n  }\n\n  setAnimation(animationName) {\n    if (animationName === this.activeAnimation || !animationName) return\n\n    if (this.meshBody) {\n      this.meshBody.setAnimationWeight(animationName, 0)\n      this.meshBody.playAnimation(animationName)\n\n      this.oldAnimation = this.activeAnimation\n      this.activeAnimation = animationName\n\n      this.blendCounter = this.transitionFrames\n    }\n\n    if (this.meshWeapon) {\n      this.meshWeapon.setAnimationWeight(animationName, 0)\n      this.meshWeapon.playAnimation(animationName)\n    }\n  }\n\n  update(delta) {\n    if (this.controls) this.updateMovementModel(delta)\n\n    if (this.animations) {\n      this.updateBehaviors()\n      this.updateAnimations(delta)\n    }\n  }\n\n  updateAnimations(delta) {\n    let mix = 1\n\n    if (this.blendCounter > 0) {\n      mix = (this.transitionFrames - this.blendCounter) / this.transitionFrames\n      this.blendCounter -= 1\n    }\n\n    if (this.meshBody) {\n      this.meshBody.update(delta)\n\n      this.meshBody.setAnimationWeight(this.activeAnimation, mix)\n      this.meshBody.setAnimationWeight(this.oldAnimation, 1 - mix)\n    }\n\n    if (this.meshWeapon) {\n      this.meshWeapon.update(delta)\n\n      this.meshWeapon.setAnimationWeight(this.activeAnimation, mix)\n      this.meshWeapon.setAnimationWeight(this.oldAnimation, 1 - mix)\n    }\n  }\n\n  updateBehaviors() {\n    const controls = this.controls\n    const animations = this.animations\n\n    let moveAnimation, idleAnimation\n\n    // crouch vs stand\n\n    if (controls.crouch) {\n      moveAnimation = animations['crouchMove']\n      idleAnimation = animations['crouchIdle']\n    } else {\n      moveAnimation = animations['move']\n      idleAnimation = animations['idle']\n    }\n\n    // actions\n\n    if (controls.jump) {\n      moveAnimation = animations['jump']\n      idleAnimation = animations['jump']\n    }\n\n    if (controls.attack) {\n      if (controls.crouch) {\n        moveAnimation = animations['crouchAttack']\n        idleAnimation = animations['crouchAttack']\n      } else {\n        moveAnimation = animations['attack']\n        idleAnimation = animations['attack']\n      }\n    }\n\n    // set animations\n\n    if (controls.moveForward || controls.moveBackward || controls.moveLeft || controls.moveRight) {\n      if (this.activeAnimation !== moveAnimation) {\n        this.setAnimation(moveAnimation)\n      }\n    }\n\n    if (\n      Math.abs(this.speed) < 0.2 * this.maxSpeed &&\n      !(controls.moveLeft || controls.moveRight || controls.moveForward || controls.moveBackward)\n    ) {\n      if (this.activeAnimation !== idleAnimation) {\n        this.setAnimation(idleAnimation)\n      }\n    }\n\n    // set animation direction\n\n    if (controls.moveForward) {\n      if (this.meshBody) {\n        this.meshBody.setAnimationDirectionForward(this.activeAnimation)\n        this.meshBody.setAnimationDirectionForward(this.oldAnimation)\n      }\n\n      if (this.meshWeapon) {\n        this.meshWeapon.setAnimationDirectionForward(this.activeAnimation)\n        this.meshWeapon.setAnimationDirectionForward(this.oldAnimation)\n      }\n    }\n\n    if (controls.moveBackward) {\n      if (this.meshBody) {\n        this.meshBody.setAnimationDirectionBackward(this.activeAnimation)\n        this.meshBody.setAnimationDirectionBackward(this.oldAnimation)\n      }\n\n      if (this.meshWeapon) {\n        this.meshWeapon.setAnimationDirectionBackward(this.activeAnimation)\n        this.meshWeapon.setAnimationDirectionBackward(this.oldAnimation)\n      }\n    }\n  }\n\n  updateMovementModel(delta) {\n    function exponentialEaseOut(k) {\n      return k === 1 ? 1 : -Math.pow(2, -10 * k) + 1\n    }\n\n    const controls = this.controls\n\n    // speed based on controls\n\n    if (controls.crouch) this.maxSpeed = this.crouchSpeed\n    else this.maxSpeed = this.walkSpeed\n\n    this.maxReverseSpeed = -this.maxSpeed\n\n    if (controls.moveForward)\n      this.speed = MathUtils.clamp(this.speed + delta * this.frontAcceleration, this.maxReverseSpeed, this.maxSpeed)\n    if (controls.moveBackward)\n      this.speed = MathUtils.clamp(this.speed - delta * this.backAcceleration, this.maxReverseSpeed, this.maxSpeed)\n\n    // orientation based on controls\n    // (don't just stand while turning)\n\n    const dir = 1\n\n    if (controls.moveLeft) {\n      this.bodyOrientation += delta * this.angularSpeed\n      this.speed = MathUtils.clamp(\n        this.speed + dir * delta * this.frontAcceleration,\n        this.maxReverseSpeed,\n        this.maxSpeed,\n      )\n    }\n\n    if (controls.moveRight) {\n      this.bodyOrientation -= delta * this.angularSpeed\n      this.speed = MathUtils.clamp(\n        this.speed + dir * delta * this.frontAcceleration,\n        this.maxReverseSpeed,\n        this.maxSpeed,\n      )\n    }\n\n    // speed decay\n\n    if (!(controls.moveForward || controls.moveBackward)) {\n      if (this.speed > 0) {\n        const k = exponentialEaseOut(this.speed / this.maxSpeed)\n        this.speed = MathUtils.clamp(this.speed - k * delta * this.frontDecceleration, 0, this.maxSpeed)\n      } else {\n        const k = exponentialEaseOut(this.speed / this.maxReverseSpeed)\n        this.speed = MathUtils.clamp(this.speed + k * delta * this.backAcceleration, this.maxReverseSpeed, 0)\n      }\n    }\n\n    // displacement\n\n    const forwardDelta = this.speed * delta\n\n    this.root.position.x += Math.sin(this.bodyOrientation) * forwardDelta\n    this.root.position.z += Math.cos(this.bodyOrientation) * forwardDelta\n\n    // steering\n\n    this.root.rotation.y = this.bodyOrientation\n  }\n\n  // internal\n\n  _createPart(geometry, skinMap) {\n    const materialWireframe = new MeshLambertMaterial({\n      color: 0xffaa00,\n      wireframe: true,\n      morphTargets: true,\n      morphNormals: true,\n    })\n    const materialTexture = new MeshLambertMaterial({\n      color: 0xffffff,\n      wireframe: false,\n      map: skinMap,\n      morphTargets: true,\n      morphNormals: true,\n    })\n\n    //\n\n    const mesh = new MorphBlendMesh(geometry, materialTexture)\n    mesh.rotation.y = -Math.PI / 2\n\n    //\n\n    mesh.materialTexture = materialTexture\n    mesh.materialWireframe = materialWireframe\n\n    //\n\n    mesh.autoCreateAnimations(this.animationFPS)\n\n    return mesh\n  }\n}\n\nexport { MD2CharacterComplex }\n"], "mappings": ";;;AAIA,MAAMA,mBAAA,CAAoB;EACxBC,YAAA,EAAc;IACZ,KAAKC,KAAA,GAAQ;IAIb,KAAKC,YAAA,GAAe;IACpB,KAAKC,gBAAA,GAAmB;IAIxB,KAAKC,QAAA,GAAW;IAChB,KAAKC,eAAA,GAAkB;IAEvB,KAAKC,iBAAA,GAAoB;IACzB,KAAKC,gBAAA,GAAmB;IAExB,KAAKC,kBAAA,GAAqB;IAE1B,KAAKC,YAAA,GAAe;IAIpB,KAAKC,IAAA,GAAO,IAAIC,QAAA,CAAU;IAE1B,KAAKC,QAAA,GAAW;IAChB,KAAKC,UAAA,GAAa;IAElB,KAAKC,QAAA,GAAW;IAIhB,KAAKC,SAAA,GAAY,EAAE;IACnB,KAAKC,WAAA,GAAc,EAAE;IAErB,KAAKC,OAAA,GAAU,EAAE;IAEjB,KAAKC,WAAA,GAAc;IAInB,KAAKC,cAAA,GAAiB,YAAY,CAAE;IAIpC,KAAKC,MAAA,GAAS,EAAE;IAChB,KAAKC,UAAA,GAAa,CAAE;IAEpB,KAAKC,WAAA,GAAc;IAInB,KAAKC,KAAA,GAAQ;IACb,KAAKC,eAAA,GAAkB;IAEvB,KAAKC,SAAA,GAAY,KAAKrB,QAAA;IACtB,KAAKsB,WAAA,GAAc,KAAKtB,QAAA,GAAW;IAInC,KAAKuB,eAAA,GAAkB;IACvB,KAAKC,YAAA,GAAe;EAGrB;EAEDC,cAAcC,MAAA,EAAQ;IACpB,SAASC,CAAA,GAAI,GAAGA,CAAA,GAAI,KAAKX,MAAA,CAAOY,MAAA,EAAQD,CAAA,IAAK;MAC3C,KAAKX,MAAA,CAAOW,CAAC,EAAEE,UAAA,GAAaH,MAAA;MAC5B,KAAKV,MAAA,CAAOW,CAAC,EAAEG,aAAA,GAAgBJ,MAAA;IAChC;EACF;EAEDK,WAAWL,MAAA,EAAQ;IACjB,SAASC,CAAA,GAAI,GAAGA,CAAA,GAAI,KAAKX,MAAA,CAAOY,MAAA,EAAQD,CAAA,IAAK;MAC3C,KAAKX,MAAA,CAAOW,CAAC,EAAEK,OAAA,GAAUN,MAAA;MACzB,KAAKV,MAAA,CAAOW,CAAC,EAAEK,OAAA,GAAUN,MAAA;IAC1B;EACF;EAEDO,WAAWC,QAAA,EAAU;IACnB,KAAKjB,UAAA,GAAaiB,QAAA,CAASjB,UAAA;IAC3B,KAAKI,SAAA,GAAYa,QAAA,CAASb,SAAA;IAC1B,KAAKC,WAAA,GAAcY,QAAA,CAASZ,WAAA;IAE5B,KAAKX,SAAA,GAAYuB,QAAA,CAASvB,SAAA;IAC1B,KAAKC,WAAA,GAAcsB,QAAA,CAAStB,WAAA;IAI5B,MAAMuB,IAAA,GAAO,KAAKC,WAAA,CAAYF,QAAA,CAAS1B,QAAA,CAAS6B,QAAA,EAAU,KAAK1B,SAAA,CAAU,CAAC,CAAC;IAC3EwB,IAAA,CAAKtC,KAAA,CAAMyC,GAAA,CAAI,KAAKzC,KAAA,EAAO,KAAKA,KAAA,EAAO,KAAKA,KAAK;IAEjD,KAAKS,IAAA,CAAKiC,QAAA,CAASC,CAAA,GAAIN,QAAA,CAAS5B,IAAA,CAAKiC,QAAA,CAASC,CAAA;IAC9C,KAAKlC,IAAA,CAAKmC,GAAA,CAAIN,IAAI;IAElB,KAAK3B,QAAA,GAAW2B,IAAA;IAEhB,KAAKnB,MAAA,CAAO0B,IAAA,CAAKP,IAAI;IAIrB,SAASR,CAAA,GAAI,GAAGA,CAAA,GAAIO,QAAA,CAASrB,OAAA,CAAQe,MAAA,EAAQD,CAAA,IAAK;MAChD,MAAMlB,UAAA,GAAa,KAAK2B,WAAA,CAAYF,QAAA,CAASrB,OAAA,CAAQc,CAAC,EAAEU,QAAA,EAAU,KAAKzB,WAAA,CAAYe,CAAC,CAAC;MACrFlB,UAAA,CAAWZ,KAAA,CAAMyC,GAAA,CAAI,KAAKzC,KAAA,EAAO,KAAKA,KAAA,EAAO,KAAKA,KAAK;MACvDY,UAAA,CAAWuB,OAAA,GAAU;MAErBvB,UAAA,CAAWkC,IAAA,GAAOT,QAAA,CAASrB,OAAA,CAAQc,CAAC,EAAEgB,IAAA;MAEtC,KAAKrC,IAAA,CAAKmC,GAAA,CAAIhC,UAAU;MAExB,KAAKI,OAAA,CAAQc,CAAC,IAAIlB,UAAA;MAClB,KAAKA,UAAA,GAAaA,UAAA;MAElB,KAAKO,MAAA,CAAO0B,IAAA,CAAKjC,UAAU;IAC5B;EACF;EAEDmC,UAAUC,MAAA,EAAQ;IAChB,MAAMC,KAAA,GAAQ;IAEd,SAASC,aAAaC,OAAA,EAASC,WAAA,EAAa;MAC1C,MAAMC,aAAA,GAAgB,IAAIC,aAAA,CAAe;MACzC,MAAMC,QAAA,GAAW,EAAE;MAEnB,SAASzB,CAAA,GAAI,GAAGA,CAAA,GAAIsB,WAAA,CAAYrB,MAAA,EAAQD,CAAA,IAAK;QAC3CyB,QAAA,CAASzB,CAAC,IAAIuB,aAAA,CAAcG,IAAA,CAAKL,OAAA,GAAUC,WAAA,CAAYtB,CAAC,GAAG2B,oBAAoB;QAC/EF,QAAA,CAASzB,CAAC,EAAE4B,OAAA,GAAUC,SAAA;QACtBJ,QAAA,CAASzB,CAAC,EAAEgB,IAAA,GAAOM,WAAA,CAAYtB,CAAC;QAChC,IAAI,gBAAgByB,QAAA,CAASzB,CAAC,GAAGyB,QAAA,CAASzB,CAAC,EAAE8B,UAAA,GAAa,YACrDL,QAAA,CAASzB,CAAC,EAAE+B,QAAA,GAAW;MAC7B;MAED,OAAON,QAAA;IACR;IAED,SAASE,qBAAA,EAAuB;MAC9BR,KAAA,CAAM5B,WAAA,IAAe;MACrB,IAAI4B,KAAA,CAAM5B,WAAA,KAAgB,GAAG4B,KAAA,CAAM/B,cAAA,CAAgB;IACpD;IAED,KAAKE,UAAA,GAAa4B,MAAA,CAAO5B,UAAA;IACzB,KAAKI,SAAA,GAAYwB,MAAA,CAAOxB,SAAA;IACxB,KAAKC,WAAA,GAAcuB,MAAA,CAAOvB,WAAA;IAE1B,KAAKJ,WAAA,GAAc2B,MAAA,CAAOhC,OAAA,CAAQe,MAAA,GAAS,IAAIiB,MAAA,CAAOc,KAAA,CAAM/B,MAAA,GAAS;IAErE,MAAMgC,eAAA,GAAkB,EAAE;IAC1B,SAASjC,CAAA,GAAI,GAAGA,CAAA,GAAIkB,MAAA,CAAOhC,OAAA,CAAQe,MAAA,EAAQD,CAAA,IAAKiC,eAAA,CAAgBjC,CAAC,IAAIkB,MAAA,CAAOhC,OAAA,CAAQc,CAAC,EAAE,CAAC;IAIxF,KAAKhB,SAAA,GAAYoC,YAAA,CAAaF,MAAA,CAAOG,OAAA,GAAU,UAAUH,MAAA,CAAOc,KAAK;IACrE,KAAK/C,WAAA,GAAcmC,YAAA,CAAaF,MAAA,CAAOG,OAAA,GAAU,UAAUY,eAAe;IAI1E,MAAMC,MAAA,GAAS,IAAIC,SAAA,CAAW;IAE9BD,MAAA,CAAOR,IAAA,CAAKR,MAAA,CAAOG,OAAA,GAAUH,MAAA,CAAOkB,IAAA,EAAM,UAAUC,GAAA,EAAK;MACvD,MAAMC,WAAA,GAAc,IAAIC,IAAA,CAAM;MAC9BD,WAAA,CAAYE,sBAAA,CAAuBH,GAAA,CAAII,UAAA,CAAW7B,QAAQ;MAE1DO,KAAA,CAAMxC,IAAA,CAAKiC,QAAA,CAASC,CAAA,GAAI,CAACM,KAAA,CAAMjD,KAAA,GAAQoE,WAAA,CAAYI,GAAA,CAAI7B,CAAA;MAEvD,MAAML,IAAA,GAAOW,KAAA,CAAMV,WAAA,CAAY4B,GAAA,EAAKlB,KAAA,CAAMnC,SAAA,CAAU,CAAC,CAAC;MACtDwB,IAAA,CAAKtC,KAAA,CAAMyC,GAAA,CAAIQ,KAAA,CAAMjD,KAAA,EAAOiD,KAAA,CAAMjD,KAAA,EAAOiD,KAAA,CAAMjD,KAAK;MAEpDiD,KAAA,CAAMxC,IAAA,CAAKmC,GAAA,CAAIN,IAAI;MAEnBW,KAAA,CAAMtC,QAAA,GAAW2B,IAAA;MACjBW,KAAA,CAAM9B,MAAA,CAAO0B,IAAA,CAAKP,IAAI;MAEtBmB,oBAAA,CAAsB;IAC5B,CAAK;IAID,MAAMgB,gBAAA,GAAmB,SAAAA,CAAUC,KAAA,EAAO5B,IAAA,EAAM;MAC9C,OAAO,UAAUqB,GAAA,EAAK;QACpB,MAAM7B,IAAA,GAAOW,KAAA,CAAMV,WAAA,CAAY4B,GAAA,EAAKlB,KAAA,CAAMlC,WAAA,CAAY2D,KAAK,CAAC;QAC5DpC,IAAA,CAAKtC,KAAA,CAAMyC,GAAA,CAAIQ,KAAA,CAAMjD,KAAA,EAAOiD,KAAA,CAAMjD,KAAA,EAAOiD,KAAA,CAAMjD,KAAK;QACpDsC,IAAA,CAAKH,OAAA,GAAU;QAEfG,IAAA,CAAKQ,IAAA,GAAOA,IAAA;QAEZG,KAAA,CAAMxC,IAAA,CAAKmC,GAAA,CAAIN,IAAI;QAEnBW,KAAA,CAAMjC,OAAA,CAAQ0D,KAAK,IAAIpC,IAAA;QACvBW,KAAA,CAAMrC,UAAA,GAAa0B,IAAA;QACnBW,KAAA,CAAM9B,MAAA,CAAO0B,IAAA,CAAKP,IAAI;QAEtBmB,oBAAA,CAAsB;MACvB;IACF;IAED,SAAS3B,CAAA,GAAI,GAAGA,CAAA,GAAIkB,MAAA,CAAOhC,OAAA,CAAQe,MAAA,EAAQD,CAAA,IAAK;MAC9CkC,MAAA,CAAOR,IAAA,CAAKR,MAAA,CAAOG,OAAA,GAAUH,MAAA,CAAOhC,OAAA,CAAQc,CAAC,EAAE,CAAC,GAAG2C,gBAAA,CAAiB3C,CAAA,EAAGkB,MAAA,CAAOhC,OAAA,CAAQc,CAAC,EAAE,CAAC,CAAC,CAAC;IAC7F;EACF;EAED6C,gBAAgBC,IAAA,EAAM;IACpB,IAAI,KAAKjE,QAAA,EAAU,KAAKA,QAAA,CAASkE,QAAA,GAAW,KAAKlE,QAAA,CAASmE,YAAA,GAAeF,IAAA;IACzE,IAAI,KAAKhE,UAAA,EAAY,KAAKA,UAAA,CAAWiE,QAAA,GAAW,KAAKjE,UAAA,CAAWkE,YAAA,GAAeF,IAAA;EAChF;EAEDG,aAAaC,gBAAA,EAAkB;IAC7B,IAAIA,gBAAA,EAAkB;MACpB,IAAI,KAAKrE,QAAA,EAAU,KAAKA,QAAA,CAASsE,QAAA,GAAW,KAAKtE,QAAA,CAASuE,iBAAA;MAC1D,IAAI,KAAKtE,UAAA,EAAY,KAAKA,UAAA,CAAWqE,QAAA,GAAW,KAAKrE,UAAA,CAAWsE,iBAAA;IACtE,OAAW;MACL,IAAI,KAAKvE,QAAA,EAAU,KAAKA,QAAA,CAASsE,QAAA,GAAW,KAAKtE,QAAA,CAASwE,eAAA;MAC1D,IAAI,KAAKvE,UAAA,EAAY,KAAKA,UAAA,CAAWqE,QAAA,GAAW,KAAKrE,UAAA,CAAWuE,eAAA;IACjE;EACF;EAEDC,QAAQV,KAAA,EAAO;IACb,IAAI,KAAK/D,QAAA,IAAY,KAAKA,QAAA,CAASsE,QAAA,CAASI,SAAA,KAAc,OAAO;MAC/D,KAAK1E,QAAA,CAASsE,QAAA,CAASK,GAAA,GAAM,KAAKxE,SAAA,CAAU4D,KAAK;MACjD,KAAKzD,WAAA,GAAcyD,KAAA;IACpB;EACF;EAEDa,UAAUb,KAAA,EAAO;IACf,SAAS5C,CAAA,GAAI,GAAGA,CAAA,GAAI,KAAKd,OAAA,CAAQe,MAAA,EAAQD,CAAA,IAAK,KAAKd,OAAA,CAAQc,CAAC,EAAEK,OAAA,GAAU;IAExE,MAAMqD,YAAA,GAAe,KAAKxE,OAAA,CAAQ0D,KAAK;IAEvC,IAAIc,YAAA,EAAc;MAChBA,YAAA,CAAarD,OAAA,GAAU;MACvB,KAAKvB,UAAA,GAAa4E,YAAA;MAElB,IAAI,KAAK9D,eAAA,EAAiB;QACxB8D,YAAA,CAAaC,aAAA,CAAc,KAAK/D,eAAe;QAC/C,KAAKd,UAAA,CAAW8E,gBAAA,CAAiB,KAAKhE,eAAA,EAAiB,KAAKf,QAAA,CAASgF,gBAAA,CAAiB,KAAKjE,eAAe,CAAC;MAC5G;IACF;EACF;EAEDkE,aAAaC,aAAA,EAAe;IAC1B,IAAIA,aAAA,KAAkB,KAAKnE,eAAA,IAAmB,CAACmE,aAAA,EAAe;IAE9D,IAAI,KAAKlF,QAAA,EAAU;MACjB,KAAKA,QAAA,CAASmF,kBAAA,CAAmBD,aAAA,EAAe,CAAC;MACjD,KAAKlF,QAAA,CAAS8E,aAAA,CAAcI,aAAa;MAEzC,KAAKlE,YAAA,GAAe,KAAKD,eAAA;MACzB,KAAKA,eAAA,GAAkBmE,aAAA;MAEvB,KAAKE,YAAA,GAAe,KAAK7F,gBAAA;IAC1B;IAED,IAAI,KAAKU,UAAA,EAAY;MACnB,KAAKA,UAAA,CAAWkF,kBAAA,CAAmBD,aAAA,EAAe,CAAC;MACnD,KAAKjF,UAAA,CAAW6E,aAAA,CAAcI,aAAa;IAC5C;EACF;EAEDG,OAAOC,KAAA,EAAO;IACZ,IAAI,KAAKpF,QAAA,EAAU,KAAKqF,mBAAA,CAAoBD,KAAK;IAEjD,IAAI,KAAK7E,UAAA,EAAY;MACnB,KAAK+E,eAAA,CAAiB;MACtB,KAAKC,gBAAA,CAAiBH,KAAK;IAC5B;EACF;EAEDG,iBAAiBH,KAAA,EAAO;IACtB,IAAII,GAAA,GAAM;IAEV,IAAI,KAAKN,YAAA,GAAe,GAAG;MACzBM,GAAA,IAAO,KAAKnG,gBAAA,GAAmB,KAAK6F,YAAA,IAAgB,KAAK7F,gBAAA;MACzD,KAAK6F,YAAA,IAAgB;IACtB;IAED,IAAI,KAAKpF,QAAA,EAAU;MACjB,KAAKA,QAAA,CAASqF,MAAA,CAAOC,KAAK;MAE1B,KAAKtF,QAAA,CAASmF,kBAAA,CAAmB,KAAKpE,eAAA,EAAiB2E,GAAG;MAC1D,KAAK1F,QAAA,CAASmF,kBAAA,CAAmB,KAAKnE,YAAA,EAAc,IAAI0E,GAAG;IAC5D;IAED,IAAI,KAAKzF,UAAA,EAAY;MACnB,KAAKA,UAAA,CAAWoF,MAAA,CAAOC,KAAK;MAE5B,KAAKrF,UAAA,CAAWkF,kBAAA,CAAmB,KAAKpE,eAAA,EAAiB2E,GAAG;MAC5D,KAAKzF,UAAA,CAAWkF,kBAAA,CAAmB,KAAKnE,YAAA,EAAc,IAAI0E,GAAG;IAC9D;EACF;EAEDF,gBAAA,EAAkB;IAChB,MAAMtF,QAAA,GAAW,KAAKA,QAAA;IACtB,MAAMO,UAAA,GAAa,KAAKA,UAAA;IAExB,IAAIkF,aAAA,EAAeC,aAAA;IAInB,IAAI1F,QAAA,CAAS2F,MAAA,EAAQ;MACnBF,aAAA,GAAgBlF,UAAA,CAAW,YAAY;MACvCmF,aAAA,GAAgBnF,UAAA,CAAW,YAAY;IAC7C,OAAW;MACLkF,aAAA,GAAgBlF,UAAA,CAAW,MAAM;MACjCmF,aAAA,GAAgBnF,UAAA,CAAW,MAAM;IAClC;IAID,IAAIP,QAAA,CAAS4F,IAAA,EAAM;MACjBH,aAAA,GAAgBlF,UAAA,CAAW,MAAM;MACjCmF,aAAA,GAAgBnF,UAAA,CAAW,MAAM;IAClC;IAED,IAAIP,QAAA,CAAS6F,MAAA,EAAQ;MACnB,IAAI7F,QAAA,CAAS2F,MAAA,EAAQ;QACnBF,aAAA,GAAgBlF,UAAA,CAAW,cAAc;QACzCmF,aAAA,GAAgBnF,UAAA,CAAW,cAAc;MACjD,OAAa;QACLkF,aAAA,GAAgBlF,UAAA,CAAW,QAAQ;QACnCmF,aAAA,GAAgBnF,UAAA,CAAW,QAAQ;MACpC;IACF;IAID,IAAIP,QAAA,CAAS8F,WAAA,IAAe9F,QAAA,CAAS+F,YAAA,IAAgB/F,QAAA,CAASgG,QAAA,IAAYhG,QAAA,CAASiG,SAAA,EAAW;MAC5F,IAAI,KAAKpF,eAAA,KAAoB4E,aAAA,EAAe;QAC1C,KAAKV,YAAA,CAAaU,aAAa;MAChC;IACF;IAED,IACES,IAAA,CAAKC,GAAA,CAAI,KAAK1F,KAAK,IAAI,MAAM,KAAKnB,QAAA,IAClC,EAAEU,QAAA,CAASgG,QAAA,IAAYhG,QAAA,CAASiG,SAAA,IAAajG,QAAA,CAAS8F,WAAA,IAAe9F,QAAA,CAAS+F,YAAA,GAC9E;MACA,IAAI,KAAKlF,eAAA,KAAoB6E,aAAA,EAAe;QAC1C,KAAKX,YAAA,CAAaW,aAAa;MAChC;IACF;IAID,IAAI1F,QAAA,CAAS8F,WAAA,EAAa;MACxB,IAAI,KAAKhG,QAAA,EAAU;QACjB,KAAKA,QAAA,CAASsG,4BAAA,CAA6B,KAAKvF,eAAe;QAC/D,KAAKf,QAAA,CAASsG,4BAAA,CAA6B,KAAKtF,YAAY;MAC7D;MAED,IAAI,KAAKf,UAAA,EAAY;QACnB,KAAKA,UAAA,CAAWqG,4BAAA,CAA6B,KAAKvF,eAAe;QACjE,KAAKd,UAAA,CAAWqG,4BAAA,CAA6B,KAAKtF,YAAY;MAC/D;IACF;IAED,IAAId,QAAA,CAAS+F,YAAA,EAAc;MACzB,IAAI,KAAKjG,QAAA,EAAU;QACjB,KAAKA,QAAA,CAASuG,6BAAA,CAA8B,KAAKxF,eAAe;QAChE,KAAKf,QAAA,CAASuG,6BAAA,CAA8B,KAAKvF,YAAY;MAC9D;MAED,IAAI,KAAKf,UAAA,EAAY;QACnB,KAAKA,UAAA,CAAWsG,6BAAA,CAA8B,KAAKxF,eAAe;QAClE,KAAKd,UAAA,CAAWsG,6BAAA,CAA8B,KAAKvF,YAAY;MAChE;IACF;EACF;EAEDuE,oBAAoBD,KAAA,EAAO;IACzB,SAASkB,mBAAmBC,CAAA,EAAG;MAC7B,OAAOA,CAAA,KAAM,IAAI,IAAI,CAACL,IAAA,CAAKM,GAAA,CAAI,GAAG,MAAMD,CAAC,IAAI;IAC9C;IAED,MAAMvG,QAAA,GAAW,KAAKA,QAAA;IAItB,IAAIA,QAAA,CAAS2F,MAAA,EAAQ,KAAKrG,QAAA,GAAW,KAAKsB,WAAA,MACrC,KAAKtB,QAAA,GAAW,KAAKqB,SAAA;IAE1B,KAAKpB,eAAA,GAAkB,CAAC,KAAKD,QAAA;IAE7B,IAAIU,QAAA,CAAS8F,WAAA,EACX,KAAKrF,KAAA,GAAQgG,SAAA,CAAUC,KAAA,CAAM,KAAKjG,KAAA,GAAQ2E,KAAA,GAAQ,KAAK5F,iBAAA,EAAmB,KAAKD,eAAA,EAAiB,KAAKD,QAAQ;IAC/G,IAAIU,QAAA,CAAS+F,YAAA,EACX,KAAKtF,KAAA,GAAQgG,SAAA,CAAUC,KAAA,CAAM,KAAKjG,KAAA,GAAQ2E,KAAA,GAAQ,KAAK3F,gBAAA,EAAkB,KAAKF,eAAA,EAAiB,KAAKD,QAAQ;IAK9G,MAAMqH,GAAA,GAAM;IAEZ,IAAI3G,QAAA,CAASgG,QAAA,EAAU;MACrB,KAAKtF,eAAA,IAAmB0E,KAAA,GAAQ,KAAKzF,YAAA;MACrC,KAAKc,KAAA,GAAQgG,SAAA,CAAUC,KAAA,CACrB,KAAKjG,KAAA,GAAQkG,GAAA,GAAMvB,KAAA,GAAQ,KAAK5F,iBAAA,EAChC,KAAKD,eAAA,EACL,KAAKD,QACN;IACF;IAED,IAAIU,QAAA,CAASiG,SAAA,EAAW;MACtB,KAAKvF,eAAA,IAAmB0E,KAAA,GAAQ,KAAKzF,YAAA;MACrC,KAAKc,KAAA,GAAQgG,SAAA,CAAUC,KAAA,CACrB,KAAKjG,KAAA,GAAQkG,GAAA,GAAMvB,KAAA,GAAQ,KAAK5F,iBAAA,EAChC,KAAKD,eAAA,EACL,KAAKD,QACN;IACF;IAID,IAAI,EAAEU,QAAA,CAAS8F,WAAA,IAAe9F,QAAA,CAAS+F,YAAA,GAAe;MACpD,IAAI,KAAKtF,KAAA,GAAQ,GAAG;QAClB,MAAM8F,CAAA,GAAID,kBAAA,CAAmB,KAAK7F,KAAA,GAAQ,KAAKnB,QAAQ;QACvD,KAAKmB,KAAA,GAAQgG,SAAA,CAAUC,KAAA,CAAM,KAAKjG,KAAA,GAAQ8F,CAAA,GAAInB,KAAA,GAAQ,KAAK1F,kBAAA,EAAoB,GAAG,KAAKJ,QAAQ;MACvG,OAAa;QACL,MAAMiH,CAAA,GAAID,kBAAA,CAAmB,KAAK7F,KAAA,GAAQ,KAAKlB,eAAe;QAC9D,KAAKkB,KAAA,GAAQgG,SAAA,CAAUC,KAAA,CAAM,KAAKjG,KAAA,GAAQ8F,CAAA,GAAInB,KAAA,GAAQ,KAAK3F,gBAAA,EAAkB,KAAKF,eAAA,EAAiB,CAAC;MACrG;IACF;IAID,MAAMqH,YAAA,GAAe,KAAKnG,KAAA,GAAQ2E,KAAA;IAElC,KAAKxF,IAAA,CAAKiC,QAAA,CAASgF,CAAA,IAAKX,IAAA,CAAKY,GAAA,CAAI,KAAKpG,eAAe,IAAIkG,YAAA;IACzD,KAAKhH,IAAA,CAAKiC,QAAA,CAASkF,CAAA,IAAKb,IAAA,CAAKc,GAAA,CAAI,KAAKtG,eAAe,IAAIkG,YAAA;IAIzD,KAAKhH,IAAA,CAAKqH,QAAA,CAASnF,CAAA,GAAI,KAAKpB,eAAA;EAC7B;EAAA;EAIDgB,YAAYC,QAAA,EAAUuF,OAAA,EAAS;IAC7B,MAAM7C,iBAAA,GAAoB,IAAI8C,mBAAA,CAAoB;MAChDC,KAAA,EAAO;MACP5C,SAAA,EAAW;MACX6C,YAAA,EAAc;MACdC,YAAA,EAAc;IACpB,CAAK;IACD,MAAMhD,eAAA,GAAkB,IAAI6C,mBAAA,CAAoB;MAC9CC,KAAA,EAAO;MACP5C,SAAA,EAAW;MACXC,GAAA,EAAKyC,OAAA;MACLG,YAAA,EAAc;MACdC,YAAA,EAAc;IACpB,CAAK;IAID,MAAM7F,IAAA,GAAO,IAAI8F,cAAA,CAAe5F,QAAA,EAAU2C,eAAe;IACzD7C,IAAA,CAAKwF,QAAA,CAASnF,CAAA,GAAI,CAACoE,IAAA,CAAKsB,EAAA,GAAK;IAI7B/F,IAAA,CAAK6C,eAAA,GAAkBA,eAAA;IACvB7C,IAAA,CAAK4C,iBAAA,GAAoBA,iBAAA;IAIzB5C,IAAA,CAAKgG,oBAAA,CAAqB,KAAKrI,YAAY;IAE3C,OAAOqC,IAAA;EACR;AACH", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}