{"ast": null, "code": "import { Group, BufferGeometry, BufferAttribute, LineSegments, LineBasicMaterial, Box3Helper, Box3, MeshBasicMaterial, DoubleSide, Mesh, PlaneGeometry } from \"three\";\nclass CSMHelper extends Group {\n  constructor(csm) {\n    super();\n    this.csm = csm;\n    this.displayFrustum = true;\n    this.displayPlanes = true;\n    this.displayShadowBounds = true;\n    const indices = new Uint16Array([0, 1, 1, 2, 2, 3, 3, 0, 4, 5, 5, 6, 6, 7, 7, 4, 0, 4, 1, 5, 2, 6, 3, 7]);\n    const positions = new Float32Array(24);\n    const frustumGeometry = new BufferGeometry();\n    frustumGeometry.setIndex(new BufferAttribute(indices, 1));\n    frustumGeometry.setAttribute(\"position\", new BufferAttribute(positions, 3, false));\n    const frustumLines = new LineSegments(frustumGeometry, new LineBasicMaterial());\n    this.add(frustumLines);\n    this.frustumLines = frustumLines;\n    this.cascadeLines = [];\n    this.cascadePlanes = [];\n    this.shadowLines = [];\n  }\n  updateVisibility() {\n    const displayFrustum = this.displayFrustum;\n    const displayPlanes = this.displayPlanes;\n    const displayShadowBounds = this.displayShadowBounds;\n    const frustumLines = this.frustumLines;\n    const cascadeLines = this.cascadeLines;\n    const cascadePlanes = this.cascadePlanes;\n    const shadowLines = this.shadowLines;\n    for (let i = 0, l = cascadeLines.length; i < l; i++) {\n      const cascadeLine = cascadeLines[i];\n      const cascadePlane = cascadePlanes[i];\n      const shadowLineGroup = shadowLines[i];\n      cascadeLine.visible = displayFrustum;\n      cascadePlane.visible = displayFrustum && displayPlanes;\n      shadowLineGroup.visible = displayShadowBounds;\n    }\n    frustumLines.visible = displayFrustum;\n  }\n  update() {\n    const csm = this.csm;\n    const camera = csm.camera;\n    const cascades = csm.cascades;\n    const mainFrustum = csm.mainFrustum;\n    const frustums = csm.frustums;\n    const lights = csm.lights;\n    const frustumLines = this.frustumLines;\n    const frustumLinePositions = frustumLines.geometry.getAttribute(\"position\");\n    const cascadeLines = this.cascadeLines;\n    const cascadePlanes = this.cascadePlanes;\n    const shadowLines = this.shadowLines;\n    this.position.copy(camera.position);\n    this.quaternion.copy(camera.quaternion);\n    this.scale.copy(camera.scale);\n    this.updateMatrixWorld(true);\n    while (cascadeLines.length > cascades) {\n      this.remove(cascadeLines.pop());\n      this.remove(cascadePlanes.pop());\n      this.remove(shadowLines.pop());\n    }\n    while (cascadeLines.length < cascades) {\n      const cascadeLine = new Box3Helper(new Box3(), 16777215);\n      const planeMat = new MeshBasicMaterial({\n        transparent: true,\n        opacity: 0.1,\n        depthWrite: false,\n        side: DoubleSide\n      });\n      const cascadePlane = new Mesh(new PlaneGeometry(), planeMat);\n      const shadowLineGroup = new Group();\n      const shadowLine = new Box3Helper(new Box3(), 16776960);\n      shadowLineGroup.add(shadowLine);\n      this.add(cascadeLine);\n      this.add(cascadePlane);\n      this.add(shadowLineGroup);\n      cascadeLines.push(cascadeLine);\n      cascadePlanes.push(cascadePlane);\n      shadowLines.push(shadowLineGroup);\n    }\n    for (let i = 0; i < cascades; i++) {\n      const frustum = frustums[i];\n      const light = lights[i];\n      const shadowCam = light.shadow.camera;\n      const farVerts2 = frustum.vertices.far;\n      const cascadeLine = cascadeLines[i];\n      const cascadePlane = cascadePlanes[i];\n      const shadowLineGroup = shadowLines[i];\n      const shadowLine = shadowLineGroup.children[0];\n      cascadeLine.box.min.copy(farVerts2[2]);\n      cascadeLine.box.max.copy(farVerts2[0]);\n      cascadeLine.box.max.z += 1e-4;\n      cascadePlane.position.addVectors(farVerts2[0], farVerts2[2]);\n      cascadePlane.position.multiplyScalar(0.5);\n      cascadePlane.scale.subVectors(farVerts2[0], farVerts2[2]);\n      cascadePlane.scale.z = 1e-4;\n      this.remove(shadowLineGroup);\n      shadowLineGroup.position.copy(shadowCam.position);\n      shadowLineGroup.quaternion.copy(shadowCam.quaternion);\n      shadowLineGroup.scale.copy(shadowCam.scale);\n      shadowLineGroup.updateMatrixWorld(true);\n      this.attach(shadowLineGroup);\n      shadowLine.box.min.set(shadowCam.bottom, shadowCam.left, -shadowCam.far);\n      shadowLine.box.max.set(shadowCam.top, shadowCam.right, -shadowCam.near);\n    }\n    const nearVerts = mainFrustum.vertices.near;\n    const farVerts = mainFrustum.vertices.far;\n    frustumLinePositions.setXYZ(0, farVerts[0].x, farVerts[0].y, farVerts[0].z);\n    frustumLinePositions.setXYZ(1, farVerts[3].x, farVerts[3].y, farVerts[3].z);\n    frustumLinePositions.setXYZ(2, farVerts[2].x, farVerts[2].y, farVerts[2].z);\n    frustumLinePositions.setXYZ(3, farVerts[1].x, farVerts[1].y, farVerts[1].z);\n    frustumLinePositions.setXYZ(4, nearVerts[0].x, nearVerts[0].y, nearVerts[0].z);\n    frustumLinePositions.setXYZ(5, nearVerts[3].x, nearVerts[3].y, nearVerts[3].z);\n    frustumLinePositions.setXYZ(6, nearVerts[2].x, nearVerts[2].y, nearVerts[2].z);\n    frustumLinePositions.setXYZ(7, nearVerts[1].x, nearVerts[1].y, nearVerts[1].z);\n    frustumLinePositions.needsUpdate = true;\n  }\n}\nexport { CSMHelper };", "map": {"version": 3, "names": ["CSMHelper", "Group", "constructor", "csm", "displayFrustum", "displayPlanes", "displayShadowBounds", "indices", "Uint16Array", "positions", "Float32Array", "frustumGeometry", "BufferGeometry", "setIndex", "BufferAttribute", "setAttribute", "frustumLines", "LineSegments", "LineBasicMaterial", "add", "cascadeLines", "cascadePlanes", "shadowLines", "updateVisibility", "i", "l", "length", "cascadeLine", "cascadePlane", "shadowLineGroup", "visible", "update", "camera", "cascades", "mainFrustum", "frustums", "lights", "frustumLinePositions", "geometry", "getAttribute", "position", "copy", "quaternion", "scale", "updateMatrixWorld", "remove", "pop", "Box3Helper", "Box3", "planeMat", "MeshBasicMaterial", "transparent", "opacity", "depthWrite", "side", "DoubleSide", "<PERSON><PERSON>", "PlaneGeometry", "shadowLine", "push", "frustum", "light", "shadowCam", "shadow", "farVerts2", "vertices", "far", "children", "box", "min", "max", "z", "addVectors", "multiplyScalar", "subVectors", "attach", "set", "bottom", "left", "top", "right", "near", "nearVerts", "<PERSON><PERSON><PERSON><PERSON>", "setXYZ", "x", "y", "needsUpdate"], "sources": ["C:\\Users\\<USER>\\Documents\\Apps_Creations\\SiahMan_Racing4Speed_WebGame\\siah-man-racing\\node_modules\\src\\csm\\CSMHelper.js"], "sourcesContent": ["import {\n  Group,\n  Mesh,\n  LineSegments,\n  BufferGeometry,\n  LineBasicMaterial,\n  Box3Helper,\n  Box3,\n  PlaneGeometry,\n  MeshBasicMaterial,\n  BufferAttribute,\n  DoubleSide,\n} from 'three'\n\nclass CSMHelper extends Group {\n  constructor(csm) {\n    super()\n    this.csm = csm\n    this.displayFrustum = true\n    this.displayPlanes = true\n    this.displayShadowBounds = true\n\n    const indices = new Uint16Array([0, 1, 1, 2, 2, 3, 3, 0, 4, 5, 5, 6, 6, 7, 7, 4, 0, 4, 1, 5, 2, 6, 3, 7])\n    const positions = new Float32Array(24)\n    const frustumGeometry = new BufferGeometry()\n    frustumGeometry.setIndex(new BufferAttribute(indices, 1))\n    frustumGeometry.setAttribute('position', new BufferAttribute(positions, 3, false))\n    const frustumLines = new LineSegments(frustumGeometry, new LineBasicMaterial())\n    this.add(frustumLines)\n\n    this.frustumLines = frustumLines\n    this.cascadeLines = []\n    this.cascadePlanes = []\n    this.shadowLines = []\n  }\n\n  updateVisibility() {\n    const displayFrustum = this.displayFrustum\n    const displayPlanes = this.displayPlanes\n    const displayShadowBounds = this.displayShadowBounds\n\n    const frustumLines = this.frustumLines\n    const cascadeLines = this.cascadeLines\n    const cascadePlanes = this.cascadePlanes\n    const shadowLines = this.shadowLines\n    for (let i = 0, l = cascadeLines.length; i < l; i++) {\n      const cascadeLine = cascadeLines[i]\n      const cascadePlane = cascadePlanes[i]\n      const shadowLineGroup = shadowLines[i]\n\n      cascadeLine.visible = displayFrustum\n      cascadePlane.visible = displayFrustum && displayPlanes\n      shadowLineGroup.visible = displayShadowBounds\n    }\n\n    frustumLines.visible = displayFrustum\n  }\n\n  update() {\n    const csm = this.csm\n    const camera = csm.camera\n    const cascades = csm.cascades\n    const mainFrustum = csm.mainFrustum\n    const frustums = csm.frustums\n    const lights = csm.lights\n\n    const frustumLines = this.frustumLines\n    const frustumLinePositions = frustumLines.geometry.getAttribute('position')\n    const cascadeLines = this.cascadeLines\n    const cascadePlanes = this.cascadePlanes\n    const shadowLines = this.shadowLines\n\n    this.position.copy(camera.position)\n    this.quaternion.copy(camera.quaternion)\n    this.scale.copy(camera.scale)\n    this.updateMatrixWorld(true)\n\n    while (cascadeLines.length > cascades) {\n      this.remove(cascadeLines.pop())\n      this.remove(cascadePlanes.pop())\n      this.remove(shadowLines.pop())\n    }\n\n    while (cascadeLines.length < cascades) {\n      const cascadeLine = new Box3Helper(new Box3(), 0xffffff)\n      const planeMat = new MeshBasicMaterial({ transparent: true, opacity: 0.1, depthWrite: false, side: DoubleSide })\n      const cascadePlane = new Mesh(new PlaneGeometry(), planeMat)\n      const shadowLineGroup = new Group()\n      const shadowLine = new Box3Helper(new Box3(), 0xffff00)\n      shadowLineGroup.add(shadowLine)\n\n      this.add(cascadeLine)\n      this.add(cascadePlane)\n      this.add(shadowLineGroup)\n\n      cascadeLines.push(cascadeLine)\n      cascadePlanes.push(cascadePlane)\n      shadowLines.push(shadowLineGroup)\n    }\n\n    for (let i = 0; i < cascades; i++) {\n      const frustum = frustums[i]\n      const light = lights[i]\n      const shadowCam = light.shadow.camera\n      const farVerts = frustum.vertices.far\n\n      const cascadeLine = cascadeLines[i]\n      const cascadePlane = cascadePlanes[i]\n      const shadowLineGroup = shadowLines[i]\n      const shadowLine = shadowLineGroup.children[0]\n\n      cascadeLine.box.min.copy(farVerts[2])\n      cascadeLine.box.max.copy(farVerts[0])\n      cascadeLine.box.max.z += 1e-4\n\n      cascadePlane.position.addVectors(farVerts[0], farVerts[2])\n      cascadePlane.position.multiplyScalar(0.5)\n      cascadePlane.scale.subVectors(farVerts[0], farVerts[2])\n      cascadePlane.scale.z = 1e-4\n\n      this.remove(shadowLineGroup)\n      shadowLineGroup.position.copy(shadowCam.position)\n      shadowLineGroup.quaternion.copy(shadowCam.quaternion)\n      shadowLineGroup.scale.copy(shadowCam.scale)\n      shadowLineGroup.updateMatrixWorld(true)\n      this.attach(shadowLineGroup)\n\n      shadowLine.box.min.set(shadowCam.bottom, shadowCam.left, -shadowCam.far)\n      shadowLine.box.max.set(shadowCam.top, shadowCam.right, -shadowCam.near)\n    }\n\n    const nearVerts = mainFrustum.vertices.near\n    const farVerts = mainFrustum.vertices.far\n    frustumLinePositions.setXYZ(0, farVerts[0].x, farVerts[0].y, farVerts[0].z)\n    frustumLinePositions.setXYZ(1, farVerts[3].x, farVerts[3].y, farVerts[3].z)\n    frustumLinePositions.setXYZ(2, farVerts[2].x, farVerts[2].y, farVerts[2].z)\n    frustumLinePositions.setXYZ(3, farVerts[1].x, farVerts[1].y, farVerts[1].z)\n\n    frustumLinePositions.setXYZ(4, nearVerts[0].x, nearVerts[0].y, nearVerts[0].z)\n    frustumLinePositions.setXYZ(5, nearVerts[3].x, nearVerts[3].y, nearVerts[3].z)\n    frustumLinePositions.setXYZ(6, nearVerts[2].x, nearVerts[2].y, nearVerts[2].z)\n    frustumLinePositions.setXYZ(7, nearVerts[1].x, nearVerts[1].y, nearVerts[1].z)\n    frustumLinePositions.needsUpdate = true\n  }\n}\n\nexport { CSMHelper }\n"], "mappings": ";AAcA,MAAMA,SAAA,SAAkBC,KAAA,CAAM;EAC5BC,YAAYC,GAAA,EAAK;IACf,MAAO;IACP,KAAKA,GAAA,GAAMA,GAAA;IACX,KAAKC,cAAA,GAAiB;IACtB,KAAKC,aAAA,GAAgB;IACrB,KAAKC,mBAAA,GAAsB;IAE3B,MAAMC,OAAA,GAAU,IAAIC,WAAA,CAAY,CAAC,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,CAAC,CAAC;IACxG,MAAMC,SAAA,GAAY,IAAIC,YAAA,CAAa,EAAE;IACrC,MAAMC,eAAA,GAAkB,IAAIC,cAAA,CAAgB;IAC5CD,eAAA,CAAgBE,QAAA,CAAS,IAAIC,eAAA,CAAgBP,OAAA,EAAS,CAAC,CAAC;IACxDI,eAAA,CAAgBI,YAAA,CAAa,YAAY,IAAID,eAAA,CAAgBL,SAAA,EAAW,GAAG,KAAK,CAAC;IACjF,MAAMO,YAAA,GAAe,IAAIC,YAAA,CAAaN,eAAA,EAAiB,IAAIO,iBAAA,CAAiB,CAAE;IAC9E,KAAKC,GAAA,CAAIH,YAAY;IAErB,KAAKA,YAAA,GAAeA,YAAA;IACpB,KAAKI,YAAA,GAAe,EAAE;IACtB,KAAKC,aAAA,GAAgB,EAAE;IACvB,KAAKC,WAAA,GAAc,EAAE;EACtB;EAEDC,iBAAA,EAAmB;IACjB,MAAMnB,cAAA,GAAiB,KAAKA,cAAA;IAC5B,MAAMC,aAAA,GAAgB,KAAKA,aAAA;IAC3B,MAAMC,mBAAA,GAAsB,KAAKA,mBAAA;IAEjC,MAAMU,YAAA,GAAe,KAAKA,YAAA;IAC1B,MAAMI,YAAA,GAAe,KAAKA,YAAA;IAC1B,MAAMC,aAAA,GAAgB,KAAKA,aAAA;IAC3B,MAAMC,WAAA,GAAc,KAAKA,WAAA;IACzB,SAASE,CAAA,GAAI,GAAGC,CAAA,GAAIL,YAAA,CAAaM,MAAA,EAAQF,CAAA,GAAIC,CAAA,EAAGD,CAAA,IAAK;MACnD,MAAMG,WAAA,GAAcP,YAAA,CAAaI,CAAC;MAClC,MAAMI,YAAA,GAAeP,aAAA,CAAcG,CAAC;MACpC,MAAMK,eAAA,GAAkBP,WAAA,CAAYE,CAAC;MAErCG,WAAA,CAAYG,OAAA,GAAU1B,cAAA;MACtBwB,YAAA,CAAaE,OAAA,GAAU1B,cAAA,IAAkBC,aAAA;MACzCwB,eAAA,CAAgBC,OAAA,GAAUxB,mBAAA;IAC3B;IAEDU,YAAA,CAAac,OAAA,GAAU1B,cAAA;EACxB;EAED2B,OAAA,EAAS;IACP,MAAM5B,GAAA,GAAM,KAAKA,GAAA;IACjB,MAAM6B,MAAA,GAAS7B,GAAA,CAAI6B,MAAA;IACnB,MAAMC,QAAA,GAAW9B,GAAA,CAAI8B,QAAA;IACrB,MAAMC,WAAA,GAAc/B,GAAA,CAAI+B,WAAA;IACxB,MAAMC,QAAA,GAAWhC,GAAA,CAAIgC,QAAA;IACrB,MAAMC,MAAA,GAASjC,GAAA,CAAIiC,MAAA;IAEnB,MAAMpB,YAAA,GAAe,KAAKA,YAAA;IAC1B,MAAMqB,oBAAA,GAAuBrB,YAAA,CAAasB,QAAA,CAASC,YAAA,CAAa,UAAU;IAC1E,MAAMnB,YAAA,GAAe,KAAKA,YAAA;IAC1B,MAAMC,aAAA,GAAgB,KAAKA,aAAA;IAC3B,MAAMC,WAAA,GAAc,KAAKA,WAAA;IAEzB,KAAKkB,QAAA,CAASC,IAAA,CAAKT,MAAA,CAAOQ,QAAQ;IAClC,KAAKE,UAAA,CAAWD,IAAA,CAAKT,MAAA,CAAOU,UAAU;IACtC,KAAKC,KAAA,CAAMF,IAAA,CAAKT,MAAA,CAAOW,KAAK;IAC5B,KAAKC,iBAAA,CAAkB,IAAI;IAE3B,OAAOxB,YAAA,CAAaM,MAAA,GAASO,QAAA,EAAU;MACrC,KAAKY,MAAA,CAAOzB,YAAA,CAAa0B,GAAA,EAAK;MAC9B,KAAKD,MAAA,CAAOxB,aAAA,CAAcyB,GAAA,EAAK;MAC/B,KAAKD,MAAA,CAAOvB,WAAA,CAAYwB,GAAA,EAAK;IAC9B;IAED,OAAO1B,YAAA,CAAaM,MAAA,GAASO,QAAA,EAAU;MACrC,MAAMN,WAAA,GAAc,IAAIoB,UAAA,CAAW,IAAIC,IAAA,CAAI,GAAI,QAAQ;MACvD,MAAMC,QAAA,GAAW,IAAIC,iBAAA,CAAkB;QAAEC,WAAA,EAAa;QAAMC,OAAA,EAAS;QAAKC,UAAA,EAAY;QAAOC,IAAA,EAAMC;MAAU,CAAE;MAC/G,MAAM3B,YAAA,GAAe,IAAI4B,IAAA,CAAK,IAAIC,aAAA,CAAa,GAAIR,QAAQ;MAC3D,MAAMpB,eAAA,GAAkB,IAAI5B,KAAA,CAAO;MACnC,MAAMyD,UAAA,GAAa,IAAIX,UAAA,CAAW,IAAIC,IAAA,CAAI,GAAI,QAAQ;MACtDnB,eAAA,CAAgBV,GAAA,CAAIuC,UAAU;MAE9B,KAAKvC,GAAA,CAAIQ,WAAW;MACpB,KAAKR,GAAA,CAAIS,YAAY;MACrB,KAAKT,GAAA,CAAIU,eAAe;MAExBT,YAAA,CAAauC,IAAA,CAAKhC,WAAW;MAC7BN,aAAA,CAAcsC,IAAA,CAAK/B,YAAY;MAC/BN,WAAA,CAAYqC,IAAA,CAAK9B,eAAe;IACjC;IAED,SAASL,CAAA,GAAI,GAAGA,CAAA,GAAIS,QAAA,EAAUT,CAAA,IAAK;MACjC,MAAMoC,OAAA,GAAUzB,QAAA,CAASX,CAAC;MAC1B,MAAMqC,KAAA,GAAQzB,MAAA,CAAOZ,CAAC;MACtB,MAAMsC,SAAA,GAAYD,KAAA,CAAME,MAAA,CAAO/B,MAAA;MAC/B,MAAMgC,SAAA,GAAWJ,OAAA,CAAQK,QAAA,CAASC,GAAA;MAElC,MAAMvC,WAAA,GAAcP,YAAA,CAAaI,CAAC;MAClC,MAAMI,YAAA,GAAeP,aAAA,CAAcG,CAAC;MACpC,MAAMK,eAAA,GAAkBP,WAAA,CAAYE,CAAC;MACrC,MAAMkC,UAAA,GAAa7B,eAAA,CAAgBsC,QAAA,CAAS,CAAC;MAE7CxC,WAAA,CAAYyC,GAAA,CAAIC,GAAA,CAAI5B,IAAA,CAAKuB,SAAA,CAAS,CAAC,CAAC;MACpCrC,WAAA,CAAYyC,GAAA,CAAIE,GAAA,CAAI7B,IAAA,CAAKuB,SAAA,CAAS,CAAC,CAAC;MACpCrC,WAAA,CAAYyC,GAAA,CAAIE,GAAA,CAAIC,CAAA,IAAK;MAEzB3C,YAAA,CAAaY,QAAA,CAASgC,UAAA,CAAWR,SAAA,CAAS,CAAC,GAAGA,SAAA,CAAS,CAAC,CAAC;MACzDpC,YAAA,CAAaY,QAAA,CAASiC,cAAA,CAAe,GAAG;MACxC7C,YAAA,CAAae,KAAA,CAAM+B,UAAA,CAAWV,SAAA,CAAS,CAAC,GAAGA,SAAA,CAAS,CAAC,CAAC;MACtDpC,YAAA,CAAae,KAAA,CAAM4B,CAAA,GAAI;MAEvB,KAAK1B,MAAA,CAAOhB,eAAe;MAC3BA,eAAA,CAAgBW,QAAA,CAASC,IAAA,CAAKqB,SAAA,CAAUtB,QAAQ;MAChDX,eAAA,CAAgBa,UAAA,CAAWD,IAAA,CAAKqB,SAAA,CAAUpB,UAAU;MACpDb,eAAA,CAAgBc,KAAA,CAAMF,IAAA,CAAKqB,SAAA,CAAUnB,KAAK;MAC1Cd,eAAA,CAAgBe,iBAAA,CAAkB,IAAI;MACtC,KAAK+B,MAAA,CAAO9C,eAAe;MAE3B6B,UAAA,CAAWU,GAAA,CAAIC,GAAA,CAAIO,GAAA,CAAId,SAAA,CAAUe,MAAA,EAAQf,SAAA,CAAUgB,IAAA,EAAM,CAAChB,SAAA,CAAUI,GAAG;MACvER,UAAA,CAAWU,GAAA,CAAIE,GAAA,CAAIM,GAAA,CAAId,SAAA,CAAUiB,GAAA,EAAKjB,SAAA,CAAUkB,KAAA,EAAO,CAAClB,SAAA,CAAUmB,IAAI;IACvE;IAED,MAAMC,SAAA,GAAYhD,WAAA,CAAY+B,QAAA,CAASgB,IAAA;IACvC,MAAME,QAAA,GAAWjD,WAAA,CAAY+B,QAAA,CAASC,GAAA;IACtC7B,oBAAA,CAAqB+C,MAAA,CAAO,GAAGD,QAAA,CAAS,CAAC,EAAEE,CAAA,EAAGF,QAAA,CAAS,CAAC,EAAEG,CAAA,EAAGH,QAAA,CAAS,CAAC,EAAEZ,CAAC;IAC1ElC,oBAAA,CAAqB+C,MAAA,CAAO,GAAGD,QAAA,CAAS,CAAC,EAAEE,CAAA,EAAGF,QAAA,CAAS,CAAC,EAAEG,CAAA,EAAGH,QAAA,CAAS,CAAC,EAAEZ,CAAC;IAC1ElC,oBAAA,CAAqB+C,MAAA,CAAO,GAAGD,QAAA,CAAS,CAAC,EAAEE,CAAA,EAAGF,QAAA,CAAS,CAAC,EAAEG,CAAA,EAAGH,QAAA,CAAS,CAAC,EAAEZ,CAAC;IAC1ElC,oBAAA,CAAqB+C,MAAA,CAAO,GAAGD,QAAA,CAAS,CAAC,EAAEE,CAAA,EAAGF,QAAA,CAAS,CAAC,EAAEG,CAAA,EAAGH,QAAA,CAAS,CAAC,EAAEZ,CAAC;IAE1ElC,oBAAA,CAAqB+C,MAAA,CAAO,GAAGF,SAAA,CAAU,CAAC,EAAEG,CAAA,EAAGH,SAAA,CAAU,CAAC,EAAEI,CAAA,EAAGJ,SAAA,CAAU,CAAC,EAAEX,CAAC;IAC7ElC,oBAAA,CAAqB+C,MAAA,CAAO,GAAGF,SAAA,CAAU,CAAC,EAAEG,CAAA,EAAGH,SAAA,CAAU,CAAC,EAAEI,CAAA,EAAGJ,SAAA,CAAU,CAAC,EAAEX,CAAC;IAC7ElC,oBAAA,CAAqB+C,MAAA,CAAO,GAAGF,SAAA,CAAU,CAAC,EAAEG,CAAA,EAAGH,SAAA,CAAU,CAAC,EAAEI,CAAA,EAAGJ,SAAA,CAAU,CAAC,EAAEX,CAAC;IAC7ElC,oBAAA,CAAqB+C,MAAA,CAAO,GAAGF,SAAA,CAAU,CAAC,EAAEG,CAAA,EAAGH,SAAA,CAAU,CAAC,EAAEI,CAAA,EAAGJ,SAAA,CAAU,CAAC,EAAEX,CAAC;IAC7ElC,oBAAA,CAAqBkD,WAAA,GAAc;EACpC;AACH", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}