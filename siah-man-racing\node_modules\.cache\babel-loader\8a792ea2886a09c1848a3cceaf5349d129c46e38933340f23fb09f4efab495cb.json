{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Apps_Creations\\\\SiahMan_Racing4Speed_WebGame\\\\siah-man-racing\\\\src\\\\components\\\\AssetLoader.tsx\",\n  _s = $RefreshSig$();\nimport React, { useState, useEffect } from 'react';\nimport { AssetLoader } from '../game/AssetLoader';\nimport styled from 'styled-components';\n\n// Styled components\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst LoadingScreen = styled.div`\n  position: fixed;\n  top: 0;\n  left: 0;\n  width: 100%;\n  height: 100vh;\n  display: flex;\n  flex-direction: column;\n  justify-content: center;\n  align-items: center;\n  background: ${props => props.theme.backgroundGradient || 'linear-gradient(145deg, #1a1a2e, #16213e, #0f3460)'};\n  color: white;\n  z-index: 1000;\n  transition: opacity 0.5s ease-out;\n`;\n_c = LoadingScreen;\nconst LoadingLogo = styled.img`\n  width: 180px;\n  height: 180px;\n  margin-bottom: 30px;\n  animation: pulse 2s infinite;\n  \n  @keyframes pulse {\n    0% { transform: scale(1); }\n    50% { transform: scale(1.05); }\n    100% { transform: scale(1); }\n  }\n`;\n_c2 = LoadingLogo;\nconst LoadingTitle = styled.h1`\n  font-size: 3.5rem;\n  margin-bottom: 10px;\n  font-weight: 800;\n  background: linear-gradient(45deg, #ff0000, #ff8000, #ffff00, #00ff00, #0080ff, #8000ff);\n  background-size: 400% 400%;\n  -webkit-background-clip: text;\n  -webkit-text-fill-color: transparent;\n  animation: rainbow 3s ease infinite, bounce 2s ease-in-out infinite;\n  text-shadow: 0 0 20px rgba(255, 255, 255, 0.5);\n  font-family: 'Arial Black', Arial, sans-serif;\n\n  @keyframes rainbow {\n    0%, 100% { background-position: 0% 50%; }\n    50% { background-position: 100% 50%; }\n  }\n\n  @keyframes bounce {\n    0%, 20%, 50%, 80%, 100% { transform: translateY(0); }\n    40% { transform: translateY(-10px); }\n    60% { transform: translateY(-5px); }\n  }\n`;\n_c3 = LoadingTitle;\nconst LoadingSubtitle = styled.h2`\n  font-size: 1.2rem;\n  margin-bottom: 30px;\n  font-weight: 400;\n  opacity: 0.8;\n`;\n_c4 = LoadingSubtitle;\nconst ProgressBarContainer = styled.div`\n  width: 80%;\n  max-width: 500px;\n  background: rgba(255, 255, 255, 0.1);\n  border-radius: 10px;\n  padding: 3px;\n  margin-bottom: 10px;\n`;\n_c5 = ProgressBarContainer;\nconst ProgressBarFill = styled.div`\n  height: 20px;\n  border-radius: 8px;\n  width: ${props => `${props.$progress}%`};\n  background: linear-gradient(90deg, #00aaff, #0088ff);\n  box-shadow: 0 0 10px rgba(0, 170, 255, 0.7);\n  transition: width 0.3s ease;\n`;\n_c6 = ProgressBarFill;\nconst ProgressText = styled.p`\n  font-size: 1rem;\n  margin-bottom: 40px;\n`;\n_c7 = ProgressText;\nconst LoadingStatus = styled.p`\n  font-style: italic;\n  opacity: 0.7;\n`;\n_c8 = LoadingStatus;\nexport const AssetLoaderComponent = ({\n  onLoadingComplete\n}) => {\n  _s();\n  const [progress, setProgress] = useState(0);\n  const [loadingStatus, setLoadingStatus] = useState('Initializing game...');\n  const [error, setError] = useState(null);\n  const [isComplete, setIsComplete] = useState(false);\n  useEffect(() => {\n    const assetLoader = AssetLoader.getInstance();\n    const updateStatus = progress => {\n      setProgress(progress * 100);\n\n      // Update loading status text based on progress\n      if (progress < 0.2) {\n        setLoadingStatus('Loading game assets...');\n      } else if (progress < 0.4) {\n        setLoadingStatus('Loading vehicles...');\n      } else if (progress < 0.6) {\n        setLoadingStatus('Loading race tracks...');\n      } else if (progress < 0.8) {\n        setLoadingStatus('Loading audio...');\n      } else {\n        setLoadingStatus('Finalizing setup...');\n      }\n    };\n    const handleComplete = () => {\n      setProgress(100);\n      setLoadingStatus('Ready to race!');\n      setIsComplete(true);\n\n      // Add a small delay before completing to show the 100% state\n      setTimeout(() => {\n        onLoadingComplete();\n      }, 1000);\n    };\n    const handleError = errorMsg => {\n      setError(errorMsg);\n      setLoadingStatus('Error loading assets. Please refresh the page.');\n    };\n    assetLoader.initializeAssets(updateStatus, handleComplete, handleError);\n\n    // Cleanup effect (not really needed for this component but good practice)\n    return () => {\n      // Any cleanup if needed\n    };\n  }, [onLoadingComplete]);\n  return /*#__PURE__*/_jsxDEV(LoadingScreen, {\n    style: {\n      opacity: isComplete ? 0.95 : 1\n    },\n    children: [/*#__PURE__*/_jsxDEV(LoadingLogo, {\n      src: \"/assets/images/logo.png\",\n      alt: \"Super Siah Man Racing\"\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 151,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(LoadingTitle, {\n      children: \"Super Siah Man Racing\"\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 152,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(LoadingSubtitle, {\n      children: \"Racing For Speed\"\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 153,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(ProgressBarContainer, {\n      children: /*#__PURE__*/_jsxDEV(ProgressBarFill, {\n        $progress: progress\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 156,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 155,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(ProgressText, {\n      children: [progress.toFixed(0), \"% Complete\"]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 159,\n      columnNumber: 7\n    }, this), error ? /*#__PURE__*/_jsxDEV(LoadingStatus, {\n      style: {\n        color: '#ff3366'\n      },\n      children: error\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 162,\n      columnNumber: 9\n    }, this) : /*#__PURE__*/_jsxDEV(LoadingStatus, {\n      children: loadingStatus\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 164,\n      columnNumber: 9\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 150,\n    columnNumber: 5\n  }, this);\n};\n_s(AssetLoaderComponent, \"kvbqZO/BczWHLU7K36/pLYJ5kwo=\");\n_c9 = AssetLoaderComponent;\nexport default AssetLoaderComponent;\nvar _c, _c2, _c3, _c4, _c5, _c6, _c7, _c8, _c9;\n$RefreshReg$(_c, \"LoadingScreen\");\n$RefreshReg$(_c2, \"LoadingLogo\");\n$RefreshReg$(_c3, \"LoadingTitle\");\n$RefreshReg$(_c4, \"LoadingSubtitle\");\n$RefreshReg$(_c5, \"ProgressBarContainer\");\n$RefreshReg$(_c6, \"ProgressBarFill\");\n$RefreshReg$(_c7, \"ProgressText\");\n$RefreshReg$(_c8, \"LoadingStatus\");\n$RefreshReg$(_c9, \"AssetLoaderComponent\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "styled", "jsxDEV", "_jsxDEV", "LoadingScreen", "div", "props", "theme", "backgroundGradient", "_c", "LoadingLogo", "img", "_c2", "LoadingTitle", "h1", "_c3", "LoadingSubtitle", "h2", "_c4", "ProgressBarContainer", "_c5", "ProgressBarFill", "$progress", "_c6", "ProgressText", "p", "_c7", "LoadingStatus", "_c8", "AssetLoaderComponent", "onLoadingComplete", "_s", "progress", "setProgress", "loadingStatus", "setLoadingStatus", "error", "setError", "isComplete", "setIsComplete", "assetLoader", "getInstance", "updateStatus", "handleComplete", "setTimeout", "handleError", "errorMsg", "initializeAssets", "style", "opacity", "children", "src", "alt", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "toFixed", "color", "_c9", "$RefreshReg$"], "sources": ["C:/Users/<USER>/Documents/Apps_Creations/SiahMan_Racing4Speed_WebGame/siah-man-racing/src/components/AssetLoader.tsx"], "sourcesContent": ["import React, { useState, useEffect } from 'react';\nimport { AssetLoader } from '../game/AssetLoader';\nimport styled from 'styled-components';\n\n// Styled components\nconst LoadingScreen = styled.div`\n  position: fixed;\n  top: 0;\n  left: 0;\n  width: 100%;\n  height: 100vh;\n  display: flex;\n  flex-direction: column;\n  justify-content: center;\n  align-items: center;\n  background: ${props => props.theme.backgroundGradient || 'linear-gradient(145deg, #1a1a2e, #16213e, #0f3460)'};\n  color: white;\n  z-index: 1000;\n  transition: opacity 0.5s ease-out;\n`;\n\nconst LoadingLogo = styled.img`\n  width: 180px;\n  height: 180px;\n  margin-bottom: 30px;\n  animation: pulse 2s infinite;\n  \n  @keyframes pulse {\n    0% { transform: scale(1); }\n    50% { transform: scale(1.05); }\n    100% { transform: scale(1); }\n  }\n`;\n\nconst LoadingTitle = styled.h1`\n  font-size: 3.5rem;\n  margin-bottom: 10px;\n  font-weight: 800;\n  background: linear-gradient(45deg, #ff0000, #ff8000, #ffff00, #00ff00, #0080ff, #8000ff);\n  background-size: 400% 400%;\n  -webkit-background-clip: text;\n  -webkit-text-fill-color: transparent;\n  animation: rainbow 3s ease infinite, bounce 2s ease-in-out infinite;\n  text-shadow: 0 0 20px rgba(255, 255, 255, 0.5);\n  font-family: 'Arial Black', Arial, sans-serif;\n\n  @keyframes rainbow {\n    0%, 100% { background-position: 0% 50%; }\n    50% { background-position: 100% 50%; }\n  }\n\n  @keyframes bounce {\n    0%, 20%, 50%, 80%, 100% { transform: translateY(0); }\n    40% { transform: translateY(-10px); }\n    60% { transform: translateY(-5px); }\n  }\n`;\n\nconst LoadingSubtitle = styled.h2`\n  font-size: 1.2rem;\n  margin-bottom: 30px;\n  font-weight: 400;\n  opacity: 0.8;\n`;\n\nconst ProgressBarContainer = styled.div`\n  width: 80%;\n  max-width: 500px;\n  background: rgba(255, 255, 255, 0.1);\n  border-radius: 10px;\n  padding: 3px;\n  margin-bottom: 10px;\n`;\n\nconst ProgressBarFill = styled.div<{ $progress: number }>`\n  height: 20px;\n  border-radius: 8px;\n  width: ${props => `${props.$progress}%`};\n  background: linear-gradient(90deg, #00aaff, #0088ff);\n  box-shadow: 0 0 10px rgba(0, 170, 255, 0.7);\n  transition: width 0.3s ease;\n`;\n\nconst ProgressText = styled.p`\n  font-size: 1rem;\n  margin-bottom: 40px;\n`;\n\nconst LoadingStatus = styled.p`\n  font-style: italic;\n  opacity: 0.7;\n`;\n\ninterface AssetLoaderComponentProps {\n  onLoadingComplete: () => void;\n  audioInitialized?: boolean;\n}\n\nexport const AssetLoaderComponent: React.FC<AssetLoaderComponentProps> = ({ onLoadingComplete }) => {\n  const [progress, setProgress] = useState(0);\n  const [loadingStatus, setLoadingStatus] = useState('Initializing game...');\n  const [error, setError] = useState<string | null>(null);\n  const [isComplete, setIsComplete] = useState(false);\n  \n  useEffect(() => {\n    const assetLoader = AssetLoader.getInstance();\n    \n    const updateStatus = (progress: number) => {\n      setProgress(progress * 100);\n      \n      // Update loading status text based on progress\n      if (progress < 0.2) {\n        setLoadingStatus('Loading game assets...');\n      } else if (progress < 0.4) {\n        setLoadingStatus('Loading vehicles...');\n      } else if (progress < 0.6) {\n        setLoadingStatus('Loading race tracks...');\n      } else if (progress < 0.8) {\n        setLoadingStatus('Loading audio...');\n      } else {\n        setLoadingStatus('Finalizing setup...');\n      }\n    };\n    \n    const handleComplete = () => {\n      setProgress(100);\n      setLoadingStatus('Ready to race!');\n      setIsComplete(true);\n      \n      // Add a small delay before completing to show the 100% state\n      setTimeout(() => {\n        onLoadingComplete();\n      }, 1000);\n    };\n    \n    const handleError = (errorMsg: string) => {\n      setError(errorMsg);\n      setLoadingStatus('Error loading assets. Please refresh the page.');\n    };\n    \n    assetLoader.initializeAssets(updateStatus, handleComplete, handleError);\n    \n    // Cleanup effect (not really needed for this component but good practice)\n    return () => {\n      // Any cleanup if needed\n    };\n  }, [onLoadingComplete]);\n  \n  return (\n    <LoadingScreen style={{ opacity: isComplete ? 0.95 : 1 }}>\n      <LoadingLogo src=\"/assets/images/logo.png\" alt=\"Super Siah Man Racing\" />\n      <LoadingTitle>Super Siah Man Racing</LoadingTitle>\n      <LoadingSubtitle>Racing For Speed</LoadingSubtitle>\n      \n      <ProgressBarContainer>\n        <ProgressBarFill $progress={progress} />\n      </ProgressBarContainer>\n      \n      <ProgressText>{progress.toFixed(0)}% Complete</ProgressText>\n      \n      {error ? (\n        <LoadingStatus style={{ color: '#ff3366' }}>{error}</LoadingStatus>\n      ) : (\n        <LoadingStatus>{loadingStatus}</LoadingStatus>\n      )}\n    </LoadingScreen>\n  );\n};\n\nexport default AssetLoaderComponent;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,QAAQ,OAAO;AAClD,SAASC,WAAW,QAAQ,qBAAqB;AACjD,OAAOC,MAAM,MAAM,mBAAmB;;AAEtC;AAAA,SAAAC,MAAA,IAAAC,OAAA;AACA,MAAMC,aAAa,GAAGH,MAAM,CAACI,GAAG;AAChC;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,gBAAgBC,KAAK,IAAIA,KAAK,CAACC,KAAK,CAACC,kBAAkB,IAAI,oDAAoD;AAC/G;AACA;AACA;AACA,CAAC;AAACC,EAAA,GAdIL,aAAa;AAgBnB,MAAMM,WAAW,GAAGT,MAAM,CAACU,GAAG;AAC9B;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,CAAC;AAACC,GAAA,GAXIF,WAAW;AAajB,MAAMG,YAAY,GAAGZ,MAAM,CAACa,EAAE;AAC9B;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,CAAC;AAACC,GAAA,GAtBIF,YAAY;AAwBlB,MAAMG,eAAe,GAAGf,MAAM,CAACgB,EAAE;AACjC;AACA;AACA;AACA;AACA,CAAC;AAACC,GAAA,GALIF,eAAe;AAOrB,MAAMG,oBAAoB,GAAGlB,MAAM,CAACI,GAAG;AACvC;AACA;AACA;AACA;AACA;AACA;AACA,CAAC;AAACe,GAAA,GAPID,oBAAoB;AAS1B,MAAME,eAAe,GAAGpB,MAAM,CAACI,GAA0B;AACzD;AACA;AACA,WAAWC,KAAK,IAAI,GAAGA,KAAK,CAACgB,SAAS,GAAG;AACzC;AACA;AACA;AACA,CAAC;AAACC,GAAA,GAPIF,eAAe;AASrB,MAAMG,YAAY,GAAGvB,MAAM,CAACwB,CAAC;AAC7B;AACA;AACA,CAAC;AAACC,GAAA,GAHIF,YAAY;AAKlB,MAAMG,aAAa,GAAG1B,MAAM,CAACwB,CAAC;AAC9B;AACA;AACA,CAAC;AAACG,GAAA,GAHID,aAAa;AAUnB,OAAO,MAAME,oBAAyD,GAAGA,CAAC;EAAEC;AAAkB,CAAC,KAAK;EAAAC,EAAA;EAClG,MAAM,CAACC,QAAQ,EAAEC,WAAW,CAAC,GAAGnC,QAAQ,CAAC,CAAC,CAAC;EAC3C,MAAM,CAACoC,aAAa,EAAEC,gBAAgB,CAAC,GAAGrC,QAAQ,CAAC,sBAAsB,CAAC;EAC1E,MAAM,CAACsC,KAAK,EAAEC,QAAQ,CAAC,GAAGvC,QAAQ,CAAgB,IAAI,CAAC;EACvD,MAAM,CAACwC,UAAU,EAAEC,aAAa,CAAC,GAAGzC,QAAQ,CAAC,KAAK,CAAC;EAEnDC,SAAS,CAAC,MAAM;IACd,MAAMyC,WAAW,GAAGxC,WAAW,CAACyC,WAAW,CAAC,CAAC;IAE7C,MAAMC,YAAY,GAAIV,QAAgB,IAAK;MACzCC,WAAW,CAACD,QAAQ,GAAG,GAAG,CAAC;;MAE3B;MACA,IAAIA,QAAQ,GAAG,GAAG,EAAE;QAClBG,gBAAgB,CAAC,wBAAwB,CAAC;MAC5C,CAAC,MAAM,IAAIH,QAAQ,GAAG,GAAG,EAAE;QACzBG,gBAAgB,CAAC,qBAAqB,CAAC;MACzC,CAAC,MAAM,IAAIH,QAAQ,GAAG,GAAG,EAAE;QACzBG,gBAAgB,CAAC,wBAAwB,CAAC;MAC5C,CAAC,MAAM,IAAIH,QAAQ,GAAG,GAAG,EAAE;QACzBG,gBAAgB,CAAC,kBAAkB,CAAC;MACtC,CAAC,MAAM;QACLA,gBAAgB,CAAC,qBAAqB,CAAC;MACzC;IACF,CAAC;IAED,MAAMQ,cAAc,GAAGA,CAAA,KAAM;MAC3BV,WAAW,CAAC,GAAG,CAAC;MAChBE,gBAAgB,CAAC,gBAAgB,CAAC;MAClCI,aAAa,CAAC,IAAI,CAAC;;MAEnB;MACAK,UAAU,CAAC,MAAM;QACfd,iBAAiB,CAAC,CAAC;MACrB,CAAC,EAAE,IAAI,CAAC;IACV,CAAC;IAED,MAAMe,WAAW,GAAIC,QAAgB,IAAK;MACxCT,QAAQ,CAACS,QAAQ,CAAC;MAClBX,gBAAgB,CAAC,gDAAgD,CAAC;IACpE,CAAC;IAEDK,WAAW,CAACO,gBAAgB,CAACL,YAAY,EAAEC,cAAc,EAAEE,WAAW,CAAC;;IAEvE;IACA,OAAO,MAAM;MACX;IAAA,CACD;EACH,CAAC,EAAE,CAACf,iBAAiB,CAAC,CAAC;EAEvB,oBACE3B,OAAA,CAACC,aAAa;IAAC4C,KAAK,EAAE;MAAEC,OAAO,EAAEX,UAAU,GAAG,IAAI,GAAG;IAAE,CAAE;IAAAY,QAAA,gBACvD/C,OAAA,CAACO,WAAW;MAACyC,GAAG,EAAC,yBAAyB;MAACC,GAAG,EAAC;IAAuB;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC,eACzErD,OAAA,CAACU,YAAY;MAAAqC,QAAA,EAAC;IAAqB;MAAAG,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAc,CAAC,eAClDrD,OAAA,CAACa,eAAe;MAAAkC,QAAA,EAAC;IAAgB;MAAAG,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAiB,CAAC,eAEnDrD,OAAA,CAACgB,oBAAoB;MAAA+B,QAAA,eACnB/C,OAAA,CAACkB,eAAe;QAACC,SAAS,EAAEU;MAAS;QAAAqB,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACpB,CAAC,eAEvBrD,OAAA,CAACqB,YAAY;MAAA0B,QAAA,GAAElB,QAAQ,CAACyB,OAAO,CAAC,CAAC,CAAC,EAAC,YAAU;IAAA;MAAAJ,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAc,CAAC,EAE3DpB,KAAK,gBACJjC,OAAA,CAACwB,aAAa;MAACqB,KAAK,EAAE;QAAEU,KAAK,EAAE;MAAU,CAAE;MAAAR,QAAA,EAAEd;IAAK;MAAAiB,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAgB,CAAC,gBAEnErD,OAAA,CAACwB,aAAa;MAAAuB,QAAA,EAAEhB;IAAa;MAAAmB,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAgB,CAC9C;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACY,CAAC;AAEpB,CAAC;AAACzB,EAAA,CArEWF,oBAAyD;AAAA8B,GAAA,GAAzD9B,oBAAyD;AAuEtE,eAAeA,oBAAoB;AAAC,IAAApB,EAAA,EAAAG,GAAA,EAAAG,GAAA,EAAAG,GAAA,EAAAE,GAAA,EAAAG,GAAA,EAAAG,GAAA,EAAAE,GAAA,EAAA+B,GAAA;AAAAC,YAAA,CAAAnD,EAAA;AAAAmD,YAAA,CAAAhD,GAAA;AAAAgD,YAAA,CAAA7C,GAAA;AAAA6C,YAAA,CAAA1C,GAAA;AAAA0C,YAAA,CAAAxC,GAAA;AAAAwC,YAAA,CAAArC,GAAA;AAAAqC,YAAA,CAAAlC,GAAA;AAAAkC,YAAA,CAAAhC,GAAA;AAAAgC,YAAA,CAAAD,GAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}