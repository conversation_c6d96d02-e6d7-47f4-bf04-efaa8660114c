{"ast": null, "code": "var _AudioSystem;\n// Dynamic Music System for Super Siah Man Racing\nimport { How<PERSON>, How<PERSON> } from 'howler';\nexport class AudioSystem {\n  constructor() {\n    this.musicTracks = void 0;\n    this.soundEffects = void 0;\n    this.voiceDialogues = void 0;\n    this.currentMusic = void 0;\n    this.nextMusic = void 0;\n    this.currentVoice = void 0;\n    this.voiceQueue = void 0;\n    this.fadeDuration = void 0;\n    this.musicVolume = void 0;\n    this.sfxVolume = void 0;\n    this.voiceVolume = void 0;\n    this.isMusicMuted = void 0;\n    this.isSfxMuted = void 0;\n    this.isVoiceMuted = void 0;\n    this.raceIntensity = void 0;\n    // 0-1 value representing race intensity\n    this.customizationMood = void 0;\n    // Mood based on vehicle customization\n    this.storyChapter = void 0;\n    // Current story chapter\n    // Audio processing settings\n    this.audioContext = null;\n    this.masterCompressor = null;\n    this.voiceCompressor = null;\n    this.musicEQ = [];\n    this.voiceEQ = [];\n    this.sfxEQ = [];\n    this.musicTracks = new Map();\n    this.soundEffects = new Map();\n    this.voiceDialogues = new Map();\n    this.currentMusic = null;\n    this.nextMusic = null;\n    this.currentVoice = null;\n    this.voiceQueue = [];\n    this.fadeDuration = 2000; // 2 seconds fade duration\n    this.musicVolume = 0.7;\n    this.sfxVolume = 0.8;\n    this.voiceVolume = 0.9;\n    this.isMusicMuted = false;\n    this.isSfxMuted = false;\n    this.isVoiceMuted = false;\n    this.raceIntensity = 0;\n    this.customizationMood = 'sporty';\n    this.storyChapter = 1;\n    this.initializeAudioProcessing();\n\n    // Set global Howler settings\n    Howler.autoUnlock = true;\n    Howler.html5PoolSize = 10;\n  }\n  static getInstance() {\n    if (!AudioSystem.instance) {\n      AudioSystem.instance = new AudioSystem();\n    }\n    return AudioSystem.instance;\n  }\n  async initializeAudioProcessing() {\n    try {\n      // Initialize Web Audio API context\n      this.audioContext = new (window.AudioContext || window.webkitAudioContext)();\n\n      // Create master compressor for overall dynamics control\n      this.masterCompressor = this.audioContext.createDynamicsCompressor();\n      this.masterCompressor.threshold.setValueAtTime(-24, this.audioContext.currentTime);\n      this.masterCompressor.knee.setValueAtTime(30, this.audioContext.currentTime);\n      this.masterCompressor.ratio.setValueAtTime(12, this.audioContext.currentTime);\n      this.masterCompressor.attack.setValueAtTime(0.003, this.audioContext.currentTime);\n      this.masterCompressor.release.setValueAtTime(0.25, this.audioContext.currentTime);\n\n      // Create voice-specific compressor for dialogue clarity\n      this.voiceCompressor = this.audioContext.createDynamicsCompressor();\n      this.voiceCompressor.threshold.setValueAtTime(-18, this.audioContext.currentTime);\n      this.voiceCompressor.knee.setValueAtTime(20, this.audioContext.currentTime);\n      this.voiceCompressor.ratio.setValueAtTime(8, this.audioContext.currentTime);\n      this.voiceCompressor.attack.setValueAtTime(0.001, this.audioContext.currentTime);\n      this.voiceCompressor.release.setValueAtTime(0.1, this.audioContext.currentTime);\n\n      // Create EQ for music (3-band)\n      this.musicEQ = [this.createBiquadFilter('lowshelf', 200, 0, 1),\n      // Bass\n      this.createBiquadFilter('peaking', 1000, 0, 1),\n      // Mids\n      this.createBiquadFilter('highshelf', 8000, 0, 1) // Treble\n      ];\n\n      // Create EQ for voice (optimized for speech clarity)\n      this.voiceEQ = [this.createBiquadFilter('highpass', 80, 0, 0.7),\n      // Remove low rumble\n      this.createBiquadFilter('peaking', 2500, 3, 1.5),\n      // Boost presence\n      this.createBiquadFilter('peaking', 5000, 2, 1.2),\n      // Boost clarity\n      this.createBiquadFilter('lowpass', 12000, 0, 0.7) // Remove harsh highs\n      ];\n\n      // Create EQ for SFX (wide range)\n      this.sfxEQ = [this.createBiquadFilter('lowshelf', 100, 0, 1), this.createBiquadFilter('peaking', 2000, 0, 1), this.createBiquadFilter('highshelf', 10000, 0, 1)];\n      console.log('Audio processing initialized with compression and EQ');\n    } catch (error) {\n      console.warn('Web Audio API not supported, falling back to basic audio:', error);\n    }\n  }\n  createBiquadFilter(type, frequency, gain, Q) {\n    if (!this.audioContext) throw new Error('Audio context not initialized');\n    const filter = this.audioContext.createBiquadFilter();\n    filter.type = type;\n    filter.frequency.setValueAtTime(frequency, this.audioContext.currentTime);\n    filter.gain.setValueAtTime(gain, this.audioContext.currentTime);\n    filter.Q.setValueAtTime(Q, this.audioContext.currentTime);\n    return filter;\n  }\n\n  // Load music tracks\n  loadMusicTrack(trackInfo) {\n    return new Promise((resolve, reject) => {\n      try {\n        const howl = new Howl({\n          src: [trackInfo.src],\n          loop: trackInfo.loop,\n          volume: 0,\n          // Start at 0 volume for crossfading\n          html5: true,\n          onload: () => {\n            this.musicTracks.set(trackInfo.id, {\n              howl,\n              info: trackInfo\n            });\n            resolve();\n          },\n          onloaderror: (id, error) => {\n            reject(`Error loading music track ${trackInfo.name}: ${error}`);\n          }\n        });\n      } catch (error) {\n        reject(`Error creating Howl for ${trackInfo.name}: ${error}`);\n      }\n    });\n  }\n\n  // Load sound effect\n  loadSoundEffect(effectInfo) {\n    return new Promise((resolve, reject) => {\n      try {\n        const howl = new Howl({\n          src: [effectInfo.src],\n          loop: effectInfo.loop,\n          volume: effectInfo.volume * this.sfxVolume,\n          html5: false,\n          // WebAudio for sound effects for better performance\n          onload: () => {\n            this.soundEffects.set(effectInfo.id, {\n              howl,\n              info: effectInfo\n            });\n            resolve();\n          },\n          onloaderror: (id, error) => {\n            reject(`Error loading sound effect ${effectInfo.name}: ${error}`);\n          }\n        });\n      } catch (error) {\n        reject(`Error creating Howl for ${effectInfo.name}: ${error}`);\n      }\n    });\n  }\n\n  // Play music with crossfade\n  playMusic(trackId) {\n    if (!this.musicTracks.has(trackId)) {\n      console.error(`Music track ${trackId} not found`);\n      return;\n    }\n    if (this.currentMusic === trackId) {\n      return; // Already playing this track\n    }\n    this.nextMusic = trackId;\n\n    // If no music is currently playing, start immediately\n    if (this.currentMusic === null) {\n      this.startMusic(trackId);\n      return;\n    }\n\n    // Otherwise crossfade\n    this.crossfade(this.currentMusic, trackId);\n  }\n\n  // Start playing music\n  startMusic(trackId) {\n    const track = this.musicTracks.get(trackId);\n    if (!track) return;\n    const {\n      howl,\n      info\n    } = track;\n\n    // Set volume based on mute state\n    const targetVolume = this.isMusicMuted ? 0 : info.volume * this.musicVolume;\n    howl.volume(targetVolume);\n    howl.play();\n    this.currentMusic = trackId;\n    this.nextMusic = null;\n  }\n\n  // Crossfade between tracks\n  crossfade(fromTrackId, toTrackId) {\n    const fromTrack = this.musicTracks.get(fromTrackId);\n    const toTrack = this.musicTracks.get(toTrackId);\n    if (!fromTrack || !toTrack) return;\n    const {\n      howl: fromHowl\n    } = fromTrack;\n    const {\n      howl: toHowl,\n      info: toInfo\n    } = toTrack; // 'toInfo' might be used later if track properties influence crossfade\n\n    // Calculate target volumes\n    const fromTargetVolume = 0;\n    const toTargetVolume = this.isMusicMuted ? 0 : toInfo.volume * this.musicVolume;\n\n    // Start the new track\n    toHowl.volume(0);\n    toHowl.play();\n\n    // Fade out the current track\n    fromHowl.fade(fromHowl.volume(), fromTargetVolume, this.fadeDuration);\n\n    // Fade in the new track\n    toHowl.fade(0, toTargetVolume, this.fadeDuration);\n\n    // Update current track after fade duration\n    setTimeout(() => {\n      fromHowl.stop();\n      this.currentMusic = toTrackId;\n      this.nextMusic = null;\n    }, this.fadeDuration);\n  }\n\n  // Play sound effect\n  playSoundEffect(effectId) {\n    if (this.isSfxMuted) return -1;\n    const effect = this.soundEffects.get(effectId);\n    if (!effect) {\n      console.error(`Sound effect ${effectId} not found`);\n      return -1;\n    }\n    const {\n      howl\n    } = effect;\n    return howl.play();\n  }\n\n  // Stop sound effect\n  stopSoundEffect(effectId, id) {\n    const effect = this.soundEffects.get(effectId);\n    if (!effect) return;\n    if (id !== undefined) {\n      effect.howl.stop(id);\n    } else {\n      effect.howl.stop();\n    }\n  }\n\n  // Set music volume\n  setMusicVolume(volume) {\n    this.musicVolume = Math.max(0, Math.min(1, volume));\n    if (this.currentMusic && !this.isMusicMuted) {\n      const track = this.musicTracks.get(this.currentMusic);\n      if (track) {\n        track.howl.volume(track.info.volume * this.musicVolume);\n      }\n    }\n  }\n\n  // Set sound effects volume\n  setSfxVolume(volume) {\n    this.sfxVolume = Math.max(0, Math.min(1, volume));\n    if (!this.isSfxMuted) {\n      this.soundEffects.forEach(({\n        howl,\n        info\n      }) => {\n        howl.volume(info.volume * this.sfxVolume);\n      });\n    }\n  }\n\n  // Mute/unmute music\n  toggleMuteMusic() {\n    this.isMusicMuted = !this.isMusicMuted;\n    if (this.currentMusic) {\n      const track = this.musicTracks.get(this.currentMusic);\n      if (track) {\n        track.howl.volume(this.isMusicMuted ? 0 : track.info.volume * this.musicVolume);\n      }\n    }\n    return this.isMusicMuted;\n  }\n\n  // Mute/unmute sound effects\n  toggleMuteSfx() {\n    this.isSfxMuted = !this.isSfxMuted;\n    this.soundEffects.forEach(({\n      howl,\n      info\n    }) => {\n      howl.volume(this.isSfxMuted ? 0 : info.volume * this.sfxVolume);\n    });\n    return this.isSfxMuted;\n  }\n\n  // Voice volume control\n  setVoiceVolume(volume) {\n    this.voiceVolume = Math.max(0, Math.min(1, volume));\n    if (!this.isVoiceMuted) {\n      this.voiceDialogues.forEach(({\n        howl,\n        info\n      }) => {\n        howl.volume(this.voiceVolume);\n      });\n    }\n  }\n\n  // Mute/unmute voices\n  toggleMuteVoice() {\n    this.isVoiceMuted = !this.isVoiceMuted;\n    this.voiceDialogues.forEach(({\n      howl\n    }) => {\n      howl.volume(this.isVoiceMuted ? 0 : this.voiceVolume);\n    });\n    return this.isVoiceMuted;\n  }\n\n  // Load voice dialogue\n  loadVoiceDialogue(dialogue) {\n    return new Promise((resolve, reject) => {\n      try {\n        const howl = new Howl({\n          src: [dialogue.audioSrc],\n          volume: this.isVoiceMuted ? 0 : this.voiceVolume,\n          html5: true,\n          preload: true,\n          onload: () => {\n            // Get actual duration from the loaded audio\n            dialogue.duration = howl.duration() * 1000; // Convert to milliseconds\n            resolve();\n          },\n          onerror: error => {\n            console.error(`Failed to load voice dialogue ${dialogue.id}:`, error);\n            reject(error);\n          }\n        });\n        this.voiceDialogues.set(dialogue.id, {\n          howl,\n          info: dialogue\n        });\n      } catch (error) {\n        console.error(`Error loading voice dialogue ${dialogue.id}:`, error);\n        reject(error);\n      }\n    });\n  }\n\n  // Play voice dialogue with character synchronization\n  playVoiceDialogue(dialogueId, onComplete) {\n    return new Promise((resolve, reject) => {\n      const dialogue = this.voiceDialogues.get(dialogueId);\n      if (!dialogue) {\n        console.error(`Voice dialogue ${dialogueId} not found`);\n        reject(new Error(`Voice dialogue ${dialogueId} not found`));\n        return;\n      }\n\n      // Stop current voice if playing\n      if (this.currentVoice) {\n        this.stopVoiceDialogue(this.currentVoice);\n      }\n      const {\n        howl,\n        info\n      } = dialogue;\n\n      // Set up completion handler\n      const handleComplete = () => {\n        this.currentVoice = null;\n        if (onComplete) onComplete();\n        resolve();\n\n        // Process voice queue\n        this.processVoiceQueue();\n      };\n      howl.once('end', handleComplete);\n      howl.once('stop', handleComplete);\n\n      // Play with proper volume and processing\n      howl.volume(this.isVoiceMuted ? 0 : this.voiceVolume);\n      howl.play();\n      this.currentVoice = dialogueId;\n      console.log(`Playing voice dialogue: ${info.character} - \"${info.text}\"`);\n    });\n  }\n\n  // Queue voice dialogue for sequential playback\n  queueVoiceDialogue(dialogue) {\n    this.voiceQueue.push(dialogue);\n\n    // If no voice is currently playing, start processing queue\n    if (!this.currentVoice) {\n      this.processVoiceQueue();\n    }\n  }\n\n  // Process voice queue\n  processVoiceQueue() {\n    if (this.voiceQueue.length === 0 || this.currentVoice) {\n      return;\n    }\n    const nextDialogue = this.voiceQueue.shift();\n    if (nextDialogue) {\n      // Load and play the dialogue\n      this.loadVoiceDialogue(nextDialogue).then(() => {\n        this.playVoiceDialogue(nextDialogue.id);\n      }).catch(error => {\n        console.error('Failed to load queued dialogue:', error);\n        this.processVoiceQueue(); // Try next in queue\n      });\n    }\n  }\n\n  // Stop voice dialogue\n  stopVoiceDialogue(dialogueId) {\n    if (dialogueId) {\n      const dialogue = this.voiceDialogues.get(dialogueId);\n      if (dialogue) {\n        dialogue.howl.stop();\n      }\n      if (this.currentVoice === dialogueId) {\n        this.currentVoice = null;\n      }\n    } else {\n      // Stop all voices\n      this.voiceDialogues.forEach(({\n        howl\n      }) => {\n        howl.stop();\n      });\n      this.currentVoice = null;\n      this.voiceQueue = [];\n    }\n  }\n\n  // Set race intensity (0-1) for adaptive music\n  setRaceIntensity(intensity) {\n    this.raceIntensity = Math.max(0, Math.min(1, intensity));\n    this.updateAdaptiveMusic();\n  }\n\n  // Set customization mood for adaptive music\n  setCustomizationMood(mood) {\n    this.customizationMood = mood;\n    this.updateAdaptiveMusic();\n  }\n\n  // Set story chapter for adaptive music\n  setStoryChapter(chapter) {\n    this.storyChapter = chapter;\n    this.updateAdaptiveMusic();\n  }\n\n  // Update adaptive music based on current game state\n  updateAdaptiveMusic() {\n    // Get current game screen from the current music track\n    if (!this.currentMusic) return;\n    const currentTrack = this.musicTracks.get(this.currentMusic);\n    if (!currentTrack) return;\n    const category = currentTrack.info.category;\n\n    // Select appropriate music based on current state\n    let targetTrackId = null;\n    switch (category) {\n      case 'race':\n        // Select race music based on intensity\n        if (this.raceIntensity < 0.3) {\n          targetTrackId = this.findTrackByIntensity('race', 'low');\n        } else if (this.raceIntensity < 0.7) {\n          targetTrackId = this.findTrackByIntensity('race', 'medium');\n        } else {\n          targetTrackId = this.findTrackByIntensity('race', 'high');\n        }\n        break;\n      case 'lab':\n        // Select lab music based on customization mood\n        targetTrackId = this.findTrackByMood(this.customizationMood);\n        break;\n      case 'story':\n        // Select story music based on chapter\n        targetTrackId = this.findTrackByChapter(this.storyChapter);\n        break;\n    }\n\n    // Play the selected track if different from current\n    if (targetTrackId && targetTrackId !== this.currentMusic && targetTrackId !== this.nextMusic) {\n      this.playMusic(targetTrackId);\n    }\n  }\n\n  // Find a track by intensity and category\n  findTrackByIntensity(category, intensity) {\n    for (const [id, {\n      info\n    }] of this.musicTracks.entries()) {\n      if (info.category === category && info.intensity === intensity) {\n        return id;\n      }\n    }\n    return null;\n  }\n\n  // Find a track by customization mood\n  findTrackByMood(mood) {\n    // This would require additional metadata on tracks\n    // For now, we'll use a simple mapping\n    const moodToIntensity = {\n      'sporty': 'medium',\n      'elegant': 'low',\n      'aggressive': 'high',\n      'mystical': 'medium'\n    };\n    return this.findTrackByIntensity('lab', moodToIntensity[mood]);\n  }\n\n  // Find a track by story chapter\n  findTrackByChapter(chapter) {\n    // This would require additional metadata on tracks\n    // For now, we'll use a simple approach based on intensity\n    const chapterToIntensity = {\n      1: 'low',\n      2: 'medium',\n      3: 'high',\n      4: 'high',\n      5: 'high'\n    };\n    const intensity = chapterToIntensity[chapter] || 'medium';\n    return this.findTrackByIntensity('story', intensity);\n  }\n\n  // Stop all audio\n  stopAll() {\n    this.musicTracks.forEach(({\n      howl\n    }) => {\n      howl.stop();\n      howl.unload(); // Unload each music track\n    });\n    this.soundEffects.forEach(({\n      howl\n    }) => {\n      howl.stop();\n      howl.unload(); // Unload each sound effect\n    });\n    Howler.stop(); // Global stop as a final measure\n    this.currentMusic = null;\n    this.nextMusic = null;\n  }\n\n  // Clean up resources\n  dispose() {\n    this.stopAll(); // stopAll now also unloads\n    this.musicTracks.clear();\n    this.soundEffects.clear();\n    // Howler.unload(); // Consider if a global unload is needed here, but stopAll is quite thorough.\n  }\n}\n_AudioSystem = AudioSystem;\nAudioSystem.instance = void 0;", "map": {"version": 3, "names": ["Howl", "Howler", "AudioSystem", "constructor", "musicTracks", "soundEffects", "voiceDialogues", "currentMusic", "nextMusic", "currentVoice", "voiceQueue", "fadeDuration", "musicVolume", "sfxVolume", "voiceVolume", "isMusicMuted", "isSfxMuted", "isVoiceMuted", "raceIntensity", "customizationMood", "story<PERSON><PERSON><PERSON>er", "audioContext", "masterCompressor", "voiceCompressor", "musicEQ", "voiceEQ", "sfxEQ", "Map", "initializeAudioProcessing", "autoUnlock", "html5PoolSize", "getInstance", "instance", "window", "AudioContext", "webkitAudioContext", "createDynamicsCompressor", "threshold", "setValueAtTime", "currentTime", "knee", "ratio", "attack", "release", "createBiquadFilter", "console", "log", "error", "warn", "type", "frequency", "gain", "Q", "Error", "filter", "loadMusicTrack", "trackInfo", "Promise", "resolve", "reject", "howl", "src", "loop", "volume", "html5", "onload", "set", "id", "info", "onloaderror", "name", "loadSoundEffect", "effectInfo", "playMusic", "trackId", "has", "startMusic", "crossfade", "track", "get", "targetVolume", "play", "fromTrackId", "toTrackId", "fromTrack", "toTrack", "fromHowl", "toHowl", "toInfo", "fromTargetVolume", "toTargetVolume", "fade", "setTimeout", "stop", "playSoundEffect", "effectId", "effect", "stopSoundEffect", "undefined", "setMusicVolume", "Math", "max", "min", "setSfxVolume", "for<PERSON>ach", "toggleMuteMusic", "toggleMuteSfx", "setVoiceVolume", "toggleMuteVoice", "loadVoiceDialogue", "dialogue", "audioSrc", "preload", "duration", "onerror", "playVoiceDialogue", "dialogueId", "onComplete", "stopVoiceDialogue", "handleComplete", "processVoiceQueue", "once", "character", "text", "queueVoiceDialogue", "push", "length", "nextDialogue", "shift", "then", "catch", "setRaceIntensity", "intensity", "updateAdaptiveMusic", "setCustomizationMood", "mood", "setStoryChapter", "chapter", "currentTrack", "category", "targetTrackId", "findTrackByIntensity", "findTrackByMood", "findTrackByChapter", "entries", "moodToIntensity", "chapterToIntensity", "stopAll", "unload", "dispose", "clear", "_AudioSystem"], "sources": ["C:/Users/<USER>/Documents/Apps_Creations/SiahMan_Racing4Speed_WebGame/siah-man-racing/src/game/AudioSystem.ts"], "sourcesContent": ["// Dynamic Music System for Super Siah Man Racing\nimport { Howl, Howler } from 'howler';\n\nexport interface TrackInfo {\n  id: string;\n  name: string;\n  src: string;\n  intensity: 'low' | 'medium' | 'high';\n  loop: boolean;\n  volume: number;\n  category: 'menu' | 'race' | 'lab' | 'story' | 'customization';\n}\n\nexport interface SoundEffect {\n  id: string;\n  name: string;\n  src: string;\n  volume: number;\n  loop: boolean;\n}\n\nexport interface VoiceDialogue {\n  id: string;\n  character: string;\n  text: string;\n  audioSrc: string;\n  duration?: number;\n  priority: 'low' | 'medium' | 'high';\n}\n\nexport class AudioSystem {\n  private static instance: AudioSystem;\n  private musicTracks: Map<string, { howl: Howl, info: TrackInfo }>;\n  private soundEffects: Map<string, { howl: Howl, info: SoundEffect }>;\n  private voiceDialogues: Map<string, { howl: Howl, info: VoiceDialogue }>;\n  private currentMusic: string | null;\n  private nextMusic: string | null;\n  private currentVoice: string | null;\n  private voiceQueue: VoiceDialogue[];\n  private fadeDuration: number;\n  private musicVolume: number;\n  private sfxVolume: number;\n  private voiceVolume: number;\n  private isMusicMuted: boolean;\n  private isSfxMuted: boolean;\n  private isVoiceMuted: boolean;\n  private raceIntensity: number; // 0-1 value representing race intensity\n  private customizationMood: 'sporty' | 'elegant' | 'aggressive' | 'mystical'; // Mood based on vehicle customization\n  private storyChapter: number; // Current story chapter\n\n  // Audio processing settings\n  private audioContext: AudioContext | null = null;\n  private masterCompressor: DynamicsCompressorNode | null = null;\n  private voiceCompressor: DynamicsCompressorNode | null = null;\n  private musicEQ: BiquadFilterNode[] = [];\n  private voiceEQ: BiquadFilterNode[] = [];\n  private sfxEQ: BiquadFilterNode[] = [];\n  \n  private constructor() {\n    this.musicTracks = new Map();\n    this.soundEffects = new Map();\n    this.voiceDialogues = new Map();\n    this.currentMusic = null;\n    this.nextMusic = null;\n    this.currentVoice = null;\n    this.voiceQueue = [];\n    this.fadeDuration = 2000; // 2 seconds fade duration\n    this.musicVolume = 0.7;\n    this.sfxVolume = 0.8;\n    this.voiceVolume = 0.9;\n    this.isMusicMuted = false;\n    this.isSfxMuted = false;\n    this.isVoiceMuted = false;\n    this.raceIntensity = 0;\n    this.customizationMood = 'sporty';\n    this.storyChapter = 1;\n\n    this.initializeAudioProcessing();\n    \n    // Set global Howler settings\n    Howler.autoUnlock = true;\n    Howler.html5PoolSize = 10;\n  }\n  \n  public static getInstance(): AudioSystem {\n    if (!AudioSystem.instance) {\n      AudioSystem.instance = new AudioSystem();\n    }\n    return AudioSystem.instance;\n  }\n\n  private async initializeAudioProcessing() {\n    try {\n      // Initialize Web Audio API context\n      this.audioContext = new (window.AudioContext || (window as any).webkitAudioContext)();\n\n      // Create master compressor for overall dynamics control\n      this.masterCompressor = this.audioContext.createDynamicsCompressor();\n      this.masterCompressor.threshold.setValueAtTime(-24, this.audioContext.currentTime);\n      this.masterCompressor.knee.setValueAtTime(30, this.audioContext.currentTime);\n      this.masterCompressor.ratio.setValueAtTime(12, this.audioContext.currentTime);\n      this.masterCompressor.attack.setValueAtTime(0.003, this.audioContext.currentTime);\n      this.masterCompressor.release.setValueAtTime(0.25, this.audioContext.currentTime);\n\n      // Create voice-specific compressor for dialogue clarity\n      this.voiceCompressor = this.audioContext.createDynamicsCompressor();\n      this.voiceCompressor.threshold.setValueAtTime(-18, this.audioContext.currentTime);\n      this.voiceCompressor.knee.setValueAtTime(20, this.audioContext.currentTime);\n      this.voiceCompressor.ratio.setValueAtTime(8, this.audioContext.currentTime);\n      this.voiceCompressor.attack.setValueAtTime(0.001, this.audioContext.currentTime);\n      this.voiceCompressor.release.setValueAtTime(0.1, this.audioContext.currentTime);\n\n      // Create EQ for music (3-band)\n      this.musicEQ = [\n        this.createBiquadFilter('lowshelf', 200, 0, 1), // Bass\n        this.createBiquadFilter('peaking', 1000, 0, 1), // Mids\n        this.createBiquadFilter('highshelf', 8000, 0, 1) // Treble\n      ];\n\n      // Create EQ for voice (optimized for speech clarity)\n      this.voiceEQ = [\n        this.createBiquadFilter('highpass', 80, 0, 0.7), // Remove low rumble\n        this.createBiquadFilter('peaking', 2500, 3, 1.5), // Boost presence\n        this.createBiquadFilter('peaking', 5000, 2, 1.2), // Boost clarity\n        this.createBiquadFilter('lowpass', 12000, 0, 0.7) // Remove harsh highs\n      ];\n\n      // Create EQ for SFX (wide range)\n      this.sfxEQ = [\n        this.createBiquadFilter('lowshelf', 100, 0, 1),\n        this.createBiquadFilter('peaking', 2000, 0, 1),\n        this.createBiquadFilter('highshelf', 10000, 0, 1)\n      ];\n\n      console.log('Audio processing initialized with compression and EQ');\n\n    } catch (error) {\n      console.warn('Web Audio API not supported, falling back to basic audio:', error);\n    }\n  }\n\n  private createBiquadFilter(type: BiquadFilterType, frequency: number, gain: number, Q: number): BiquadFilterNode {\n    if (!this.audioContext) throw new Error('Audio context not initialized');\n\n    const filter = this.audioContext.createBiquadFilter();\n    filter.type = type;\n    filter.frequency.setValueAtTime(frequency, this.audioContext.currentTime);\n    filter.gain.setValueAtTime(gain, this.audioContext.currentTime);\n    filter.Q.setValueAtTime(Q, this.audioContext.currentTime);\n    return filter;\n  }\n\n  // Load music tracks\n  public loadMusicTrack(trackInfo: TrackInfo): Promise<void> {\n    return new Promise((resolve, reject) => {\n      try {\n        const howl = new Howl({\n          src: [trackInfo.src],\n          loop: trackInfo.loop,\n          volume: 0, // Start at 0 volume for crossfading\n          html5: true,\n          onload: () => {\n            this.musicTracks.set(trackInfo.id, { howl, info: trackInfo });\n            resolve();\n          },\n          onloaderror: (id: number | undefined, error: any) => {\n            reject(`Error loading music track ${trackInfo.name}: ${error}`);\n          }\n        });\n      } catch (error) {\n        reject(`Error creating Howl for ${trackInfo.name}: ${error}`);\n      }\n    });\n  }\n  \n  // Load sound effect\n  public loadSoundEffect(effectInfo: SoundEffect): Promise<void> {\n    return new Promise((resolve, reject) => {\n      try {\n        const howl = new Howl({\n          src: [effectInfo.src],\n          loop: effectInfo.loop,\n          volume: effectInfo.volume * this.sfxVolume,\n          html5: false, // WebAudio for sound effects for better performance\n          onload: () => {\n            this.soundEffects.set(effectInfo.id, { howl, info: effectInfo });\n            resolve();\n          },\n          onloaderror: (id: number | undefined, error: any) => {\n            reject(`Error loading sound effect ${effectInfo.name}: ${error}`);\n          }\n        });\n      } catch (error) {\n        reject(`Error creating Howl for ${effectInfo.name}: ${error}`);\n      }\n    });\n  }\n  \n  // Play music with crossfade\n  public playMusic(trackId: string): void {\n    if (!this.musicTracks.has(trackId)) {\n      console.error(`Music track ${trackId} not found`);\n      return;\n    }\n    \n    if (this.currentMusic === trackId) {\n      return; // Already playing this track\n    }\n    \n    this.nextMusic = trackId;\n    \n    // If no music is currently playing, start immediately\n    if (this.currentMusic === null) {\n      this.startMusic(trackId);\n      return;\n    }\n    \n    // Otherwise crossfade\n    this.crossfade(this.currentMusic, trackId);\n  }\n  \n  // Start playing music\n  private startMusic(trackId: string): void {\n    const track = this.musicTracks.get(trackId);\n    if (!track) return;\n    \n    const { howl, info } = track;\n    \n    // Set volume based on mute state\n    const targetVolume = this.isMusicMuted ? 0 : info.volume * this.musicVolume;\n    \n    howl.volume(targetVolume);\n    howl.play();\n    \n    this.currentMusic = trackId;\n    this.nextMusic = null;\n  }\n  \n  // Crossfade between tracks\n  private crossfade(fromTrackId: string, toTrackId: string): void {\n    const fromTrack = this.musicTracks.get(fromTrackId);\n    const toTrack = this.musicTracks.get(toTrackId);\n    \n    if (!fromTrack || !toTrack) return;\n    \n    const { howl: fromHowl } = fromTrack;\n    const { howl: toHowl, info: toInfo } = toTrack; // 'toInfo' might be used later if track properties influence crossfade\n    \n    // Calculate target volumes\n    const fromTargetVolume = 0;\n    const toTargetVolume = this.isMusicMuted ? 0 : toInfo.volume * this.musicVolume;\n    \n    // Start the new track\n    toHowl.volume(0);\n    toHowl.play();\n    \n    // Fade out the current track\n    fromHowl.fade(fromHowl.volume(), fromTargetVolume, this.fadeDuration);\n    \n    // Fade in the new track\n    toHowl.fade(0, toTargetVolume, this.fadeDuration);\n    \n    // Update current track after fade duration\n    setTimeout(() => {\n      fromHowl.stop();\n      this.currentMusic = toTrackId;\n      this.nextMusic = null;\n    }, this.fadeDuration);\n  }\n  \n  // Play sound effect\n  public playSoundEffect(effectId: string): number {\n    if (this.isSfxMuted) return -1;\n    \n    const effect = this.soundEffects.get(effectId);\n    if (!effect) {\n      console.error(`Sound effect ${effectId} not found`);\n      return -1;\n    }\n    \n    const { howl } = effect;\n    return howl.play();\n  }\n  \n  // Stop sound effect\n  public stopSoundEffect(effectId: string, id?: number): void {\n    const effect = this.soundEffects.get(effectId);\n    if (!effect) return;\n    \n    if (id !== undefined) {\n      effect.howl.stop(id);\n    } else {\n      effect.howl.stop();\n    }\n  }\n  \n  // Set music volume\n  public setMusicVolume(volume: number): void {\n    this.musicVolume = Math.max(0, Math.min(1, volume));\n    \n    if (this.currentMusic && !this.isMusicMuted) {\n      const track = this.musicTracks.get(this.currentMusic);\n      if (track) {\n        track.howl.volume(track.info.volume * this.musicVolume);\n      }\n    }\n  }\n  \n  // Set sound effects volume\n  public setSfxVolume(volume: number): void {\n    this.sfxVolume = Math.max(0, Math.min(1, volume));\n    \n    if (!this.isSfxMuted) {\n      this.soundEffects.forEach(({ howl, info }) => {\n        howl.volume(info.volume * this.sfxVolume);\n      });\n    }\n  }\n  \n  // Mute/unmute music\n  public toggleMuteMusic(): boolean {\n    this.isMusicMuted = !this.isMusicMuted;\n    \n    if (this.currentMusic) {\n      const track = this.musicTracks.get(this.currentMusic);\n      if (track) {\n        track.howl.volume(this.isMusicMuted ? 0 : track.info.volume * this.musicVolume);\n      }\n    }\n    \n    return this.isMusicMuted;\n  }\n  \n  // Mute/unmute sound effects\n  public toggleMuteSfx(): boolean {\n    this.isSfxMuted = !this.isSfxMuted;\n\n    this.soundEffects.forEach(({ howl, info }) => {\n      howl.volume(this.isSfxMuted ? 0 : info.volume * this.sfxVolume);\n    });\n\n    return this.isSfxMuted;\n  }\n\n  // Voice volume control\n  public setVoiceVolume(volume: number): void {\n    this.voiceVolume = Math.max(0, Math.min(1, volume));\n\n    if (!this.isVoiceMuted) {\n      this.voiceDialogues.forEach(({ howl, info }) => {\n        howl.volume(this.voiceVolume);\n      });\n    }\n  }\n\n  // Mute/unmute voices\n  public toggleMuteVoice(): boolean {\n    this.isVoiceMuted = !this.isVoiceMuted;\n\n    this.voiceDialogues.forEach(({ howl }) => {\n      howl.volume(this.isVoiceMuted ? 0 : this.voiceVolume);\n    });\n\n    return this.isVoiceMuted;\n  }\n\n  // Load voice dialogue\n  public loadVoiceDialogue(dialogue: VoiceDialogue): Promise<void> {\n    return new Promise((resolve, reject) => {\n      try {\n        const howl = new Howl({\n          src: [dialogue.audioSrc],\n          volume: this.isVoiceMuted ? 0 : this.voiceVolume,\n          html5: true,\n          preload: true,\n          onload: () => {\n            // Get actual duration from the loaded audio\n            dialogue.duration = howl.duration() * 1000; // Convert to milliseconds\n            resolve();\n          },\n          onerror: (error) => {\n            console.error(`Failed to load voice dialogue ${dialogue.id}:`, error);\n            reject(error);\n          }\n        });\n\n        this.voiceDialogues.set(dialogue.id, { howl, info: dialogue });\n\n      } catch (error) {\n        console.error(`Error loading voice dialogue ${dialogue.id}:`, error);\n        reject(error);\n      }\n    });\n  }\n\n  // Play voice dialogue with character synchronization\n  public playVoiceDialogue(dialogueId: string, onComplete?: () => void): Promise<void> {\n    return new Promise((resolve, reject) => {\n      const dialogue = this.voiceDialogues.get(dialogueId);\n      if (!dialogue) {\n        console.error(`Voice dialogue ${dialogueId} not found`);\n        reject(new Error(`Voice dialogue ${dialogueId} not found`));\n        return;\n      }\n\n      // Stop current voice if playing\n      if (this.currentVoice) {\n        this.stopVoiceDialogue(this.currentVoice);\n      }\n\n      const { howl, info } = dialogue;\n\n      // Set up completion handler\n      const handleComplete = () => {\n        this.currentVoice = null;\n        if (onComplete) onComplete();\n        resolve();\n\n        // Process voice queue\n        this.processVoiceQueue();\n      };\n\n      howl.once('end', handleComplete);\n      howl.once('stop', handleComplete);\n\n      // Play with proper volume and processing\n      howl.volume(this.isVoiceMuted ? 0 : this.voiceVolume);\n      howl.play();\n\n      this.currentVoice = dialogueId;\n\n      console.log(`Playing voice dialogue: ${info.character} - \"${info.text}\"`);\n    });\n  }\n\n  // Queue voice dialogue for sequential playback\n  public queueVoiceDialogue(dialogue: VoiceDialogue): void {\n    this.voiceQueue.push(dialogue);\n\n    // If no voice is currently playing, start processing queue\n    if (!this.currentVoice) {\n      this.processVoiceQueue();\n    }\n  }\n\n  // Process voice queue\n  private processVoiceQueue(): void {\n    if (this.voiceQueue.length === 0 || this.currentVoice) {\n      return;\n    }\n\n    const nextDialogue = this.voiceQueue.shift();\n    if (nextDialogue) {\n      // Load and play the dialogue\n      this.loadVoiceDialogue(nextDialogue).then(() => {\n        this.playVoiceDialogue(nextDialogue.id);\n      }).catch(error => {\n        console.error('Failed to load queued dialogue:', error);\n        this.processVoiceQueue(); // Try next in queue\n      });\n    }\n  }\n\n  // Stop voice dialogue\n  public stopVoiceDialogue(dialogueId?: string): void {\n    if (dialogueId) {\n      const dialogue = this.voiceDialogues.get(dialogueId);\n      if (dialogue) {\n        dialogue.howl.stop();\n      }\n      if (this.currentVoice === dialogueId) {\n        this.currentVoice = null;\n      }\n    } else {\n      // Stop all voices\n      this.voiceDialogues.forEach(({ howl }) => {\n        howl.stop();\n      });\n      this.currentVoice = null;\n      this.voiceQueue = [];\n    }\n  }\n\n  // Set race intensity (0-1) for adaptive music\n  public setRaceIntensity(intensity: number): void {\n    this.raceIntensity = Math.max(0, Math.min(1, intensity));\n    this.updateAdaptiveMusic();\n  }\n  \n  // Set customization mood for adaptive music\n  public setCustomizationMood(mood: 'sporty' | 'elegant' | 'aggressive' | 'mystical'): void {\n    this.customizationMood = mood;\n    this.updateAdaptiveMusic();\n  }\n  \n  // Set story chapter for adaptive music\n  public setStoryChapter(chapter: number): void {\n    this.storyChapter = chapter;\n    this.updateAdaptiveMusic();\n  }\n  \n  // Update adaptive music based on current game state\n  private updateAdaptiveMusic(): void {\n    // Get current game screen from the current music track\n    if (!this.currentMusic) return;\n    \n    const currentTrack = this.musicTracks.get(this.currentMusic);\n    if (!currentTrack) return;\n    \n    const category = currentTrack.info.category;\n    \n    // Select appropriate music based on current state\n    let targetTrackId: string | null = null;\n    \n    switch (category) {\n      case 'race':\n        // Select race music based on intensity\n        if (this.raceIntensity < 0.3) {\n          targetTrackId = this.findTrackByIntensity('race', 'low');\n        } else if (this.raceIntensity < 0.7) {\n          targetTrackId = this.findTrackByIntensity('race', 'medium');\n        } else {\n          targetTrackId = this.findTrackByIntensity('race', 'high');\n        }\n        break;\n        \n      case 'lab':\n        // Select lab music based on customization mood\n        targetTrackId = this.findTrackByMood(this.customizationMood);\n        break;\n        \n      case 'story':\n        // Select story music based on chapter\n        targetTrackId = this.findTrackByChapter(this.storyChapter);\n        break;\n    }\n    \n    // Play the selected track if different from current\n    if (targetTrackId && targetTrackId !== this.currentMusic && targetTrackId !== this.nextMusic) {\n      this.playMusic(targetTrackId);\n    }\n  }\n  \n  // Find a track by intensity and category\n  private findTrackByIntensity(category: 'menu' | 'race' | 'lab' | 'story', intensity: 'low' | 'medium' | 'high'): string | null {\n    for (const [id, { info }] of this.musicTracks.entries()) {\n      if (info.category === category && info.intensity === intensity) {\n        return id;\n      }\n    }\n    return null;\n  }\n  \n  // Find a track by customization mood\n  private findTrackByMood(mood: 'sporty' | 'elegant' | 'aggressive' | 'mystical'): string | null {\n    // This would require additional metadata on tracks\n    // For now, we'll use a simple mapping\n    const moodToIntensity: { [key: string]: 'low' | 'medium' | 'high' } = {\n      'sporty': 'medium',\n      'elegant': 'low',\n      'aggressive': 'high',\n      'mystical': 'medium'\n    };\n    \n    return this.findTrackByIntensity('lab', moodToIntensity[mood]);\n  }\n  \n  // Find a track by story chapter\n  private findTrackByChapter(chapter: number): string | null {\n    // This would require additional metadata on tracks\n    // For now, we'll use a simple approach based on intensity\n    const chapterToIntensity: { [key: number]: 'low' | 'medium' | 'high' } = {\n      1: 'low',\n      2: 'medium',\n      3: 'high',\n      4: 'high',\n      5: 'high'\n    };\n    \n    const intensity = chapterToIntensity[chapter] || 'medium';\n    return this.findTrackByIntensity('story', intensity);\n  }\n  \n  // Stop all audio\n  public stopAll(): void {\n    this.musicTracks.forEach(({ howl }) => {\n      howl.stop();\n      howl.unload(); // Unload each music track\n    });\n    this.soundEffects.forEach(({ howl }) => {\n      howl.stop();\n      howl.unload(); // Unload each sound effect\n    });\n    Howler.stop(); // Global stop as a final measure\n    this.currentMusic = null;\n    this.nextMusic = null;\n  }\n  \n  // Clean up resources\n  public dispose(): void {\n    this.stopAll(); // stopAll now also unloads\n    this.musicTracks.clear();\n    this.soundEffects.clear();\n    // Howler.unload(); // Consider if a global unload is needed here, but stopAll is quite thorough.\n  }\n}\n"], "mappings": ";AAAA;AACA,SAASA,IAAI,EAAEC,MAAM,QAAQ,QAAQ;AA6BrC,OAAO,MAAMC,WAAW,CAAC;EA4BfC,WAAWA,CAAA,EAAG;IAAA,KA1BdC,WAAW;IAAA,KACXC,YAAY;IAAA,KACZC,cAAc;IAAA,KACdC,YAAY;IAAA,KACZC,SAAS;IAAA,KACTC,YAAY;IAAA,KACZC,UAAU;IAAA,KACVC,YAAY;IAAA,KACZC,WAAW;IAAA,KACXC,SAAS;IAAA,KACTC,WAAW;IAAA,KACXC,YAAY;IAAA,KACZC,UAAU;IAAA,KACVC,YAAY;IAAA,KACZC,aAAa;IAAU;IAAA,KACvBC,iBAAiB;IAAoD;IAAA,KACrEC,YAAY;IAAU;IAE9B;IAAA,KACQC,YAAY,GAAwB,IAAI;IAAA,KACxCC,gBAAgB,GAAkC,IAAI;IAAA,KACtDC,eAAe,GAAkC,IAAI;IAAA,KACrDC,OAAO,GAAuB,EAAE;IAAA,KAChCC,OAAO,GAAuB,EAAE;IAAA,KAChCC,KAAK,GAAuB,EAAE;IAGpC,IAAI,CAACtB,WAAW,GAAG,IAAIuB,GAAG,CAAC,CAAC;IAC5B,IAAI,CAACtB,YAAY,GAAG,IAAIsB,GAAG,CAAC,CAAC;IAC7B,IAAI,CAACrB,cAAc,GAAG,IAAIqB,GAAG,CAAC,CAAC;IAC/B,IAAI,CAACpB,YAAY,GAAG,IAAI;IACxB,IAAI,CAACC,SAAS,GAAG,IAAI;IACrB,IAAI,CAACC,YAAY,GAAG,IAAI;IACxB,IAAI,CAACC,UAAU,GAAG,EAAE;IACpB,IAAI,CAACC,YAAY,GAAG,IAAI,CAAC,CAAC;IAC1B,IAAI,CAACC,WAAW,GAAG,GAAG;IACtB,IAAI,CAACC,SAAS,GAAG,GAAG;IACpB,IAAI,CAACC,WAAW,GAAG,GAAG;IACtB,IAAI,CAACC,YAAY,GAAG,KAAK;IACzB,IAAI,CAACC,UAAU,GAAG,KAAK;IACvB,IAAI,CAACC,YAAY,GAAG,KAAK;IACzB,IAAI,CAACC,aAAa,GAAG,CAAC;IACtB,IAAI,CAACC,iBAAiB,GAAG,QAAQ;IACjC,IAAI,CAACC,YAAY,GAAG,CAAC;IAErB,IAAI,CAACQ,yBAAyB,CAAC,CAAC;;IAEhC;IACA3B,MAAM,CAAC4B,UAAU,GAAG,IAAI;IACxB5B,MAAM,CAAC6B,aAAa,GAAG,EAAE;EAC3B;EAEA,OAAcC,WAAWA,CAAA,EAAgB;IACvC,IAAI,CAAC7B,WAAW,CAAC8B,QAAQ,EAAE;MACzB9B,WAAW,CAAC8B,QAAQ,GAAG,IAAI9B,WAAW,CAAC,CAAC;IAC1C;IACA,OAAOA,WAAW,CAAC8B,QAAQ;EAC7B;EAEA,MAAcJ,yBAAyBA,CAAA,EAAG;IACxC,IAAI;MACF;MACA,IAAI,CAACP,YAAY,GAAG,KAAKY,MAAM,CAACC,YAAY,IAAKD,MAAM,CAASE,kBAAkB,EAAE,CAAC;;MAErF;MACA,IAAI,CAACb,gBAAgB,GAAG,IAAI,CAACD,YAAY,CAACe,wBAAwB,CAAC,CAAC;MACpE,IAAI,CAACd,gBAAgB,CAACe,SAAS,CAACC,cAAc,CAAC,CAAC,EAAE,EAAE,IAAI,CAACjB,YAAY,CAACkB,WAAW,CAAC;MAClF,IAAI,CAACjB,gBAAgB,CAACkB,IAAI,CAACF,cAAc,CAAC,EAAE,EAAE,IAAI,CAACjB,YAAY,CAACkB,WAAW,CAAC;MAC5E,IAAI,CAACjB,gBAAgB,CAACmB,KAAK,CAACH,cAAc,CAAC,EAAE,EAAE,IAAI,CAACjB,YAAY,CAACkB,WAAW,CAAC;MAC7E,IAAI,CAACjB,gBAAgB,CAACoB,MAAM,CAACJ,cAAc,CAAC,KAAK,EAAE,IAAI,CAACjB,YAAY,CAACkB,WAAW,CAAC;MACjF,IAAI,CAACjB,gBAAgB,CAACqB,OAAO,CAACL,cAAc,CAAC,IAAI,EAAE,IAAI,CAACjB,YAAY,CAACkB,WAAW,CAAC;;MAEjF;MACA,IAAI,CAAChB,eAAe,GAAG,IAAI,CAACF,YAAY,CAACe,wBAAwB,CAAC,CAAC;MACnE,IAAI,CAACb,eAAe,CAACc,SAAS,CAACC,cAAc,CAAC,CAAC,EAAE,EAAE,IAAI,CAACjB,YAAY,CAACkB,WAAW,CAAC;MACjF,IAAI,CAAChB,eAAe,CAACiB,IAAI,CAACF,cAAc,CAAC,EAAE,EAAE,IAAI,CAACjB,YAAY,CAACkB,WAAW,CAAC;MAC3E,IAAI,CAAChB,eAAe,CAACkB,KAAK,CAACH,cAAc,CAAC,CAAC,EAAE,IAAI,CAACjB,YAAY,CAACkB,WAAW,CAAC;MAC3E,IAAI,CAAChB,eAAe,CAACmB,MAAM,CAACJ,cAAc,CAAC,KAAK,EAAE,IAAI,CAACjB,YAAY,CAACkB,WAAW,CAAC;MAChF,IAAI,CAAChB,eAAe,CAACoB,OAAO,CAACL,cAAc,CAAC,GAAG,EAAE,IAAI,CAACjB,YAAY,CAACkB,WAAW,CAAC;;MAE/E;MACA,IAAI,CAACf,OAAO,GAAG,CACb,IAAI,CAACoB,kBAAkB,CAAC,UAAU,EAAE,GAAG,EAAE,CAAC,EAAE,CAAC,CAAC;MAAE;MAChD,IAAI,CAACA,kBAAkB,CAAC,SAAS,EAAE,IAAI,EAAE,CAAC,EAAE,CAAC,CAAC;MAAE;MAChD,IAAI,CAACA,kBAAkB,CAAC,WAAW,EAAE,IAAI,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC;MAAA,CAClD;;MAED;MACA,IAAI,CAACnB,OAAO,GAAG,CACb,IAAI,CAACmB,kBAAkB,CAAC,UAAU,EAAE,EAAE,EAAE,CAAC,EAAE,GAAG,CAAC;MAAE;MACjD,IAAI,CAACA,kBAAkB,CAAC,SAAS,EAAE,IAAI,EAAE,CAAC,EAAE,GAAG,CAAC;MAAE;MAClD,IAAI,CAACA,kBAAkB,CAAC,SAAS,EAAE,IAAI,EAAE,CAAC,EAAE,GAAG,CAAC;MAAE;MAClD,IAAI,CAACA,kBAAkB,CAAC,SAAS,EAAE,KAAK,EAAE,CAAC,EAAE,GAAG,CAAC,CAAC;MAAA,CACnD;;MAED;MACA,IAAI,CAAClB,KAAK,GAAG,CACX,IAAI,CAACkB,kBAAkB,CAAC,UAAU,EAAE,GAAG,EAAE,CAAC,EAAE,CAAC,CAAC,EAC9C,IAAI,CAACA,kBAAkB,CAAC,SAAS,EAAE,IAAI,EAAE,CAAC,EAAE,CAAC,CAAC,EAC9C,IAAI,CAACA,kBAAkB,CAAC,WAAW,EAAE,KAAK,EAAE,CAAC,EAAE,CAAC,CAAC,CAClD;MAEDC,OAAO,CAACC,GAAG,CAAC,sDAAsD,CAAC;IAErE,CAAC,CAAC,OAAOC,KAAK,EAAE;MACdF,OAAO,CAACG,IAAI,CAAC,2DAA2D,EAAED,KAAK,CAAC;IAClF;EACF;EAEQH,kBAAkBA,CAACK,IAAsB,EAAEC,SAAiB,EAAEC,IAAY,EAAEC,CAAS,EAAoB;IAC/G,IAAI,CAAC,IAAI,CAAC/B,YAAY,EAAE,MAAM,IAAIgC,KAAK,CAAC,+BAA+B,CAAC;IAExE,MAAMC,MAAM,GAAG,IAAI,CAACjC,YAAY,CAACuB,kBAAkB,CAAC,CAAC;IACrDU,MAAM,CAACL,IAAI,GAAGA,IAAI;IAClBK,MAAM,CAACJ,SAAS,CAACZ,cAAc,CAACY,SAAS,EAAE,IAAI,CAAC7B,YAAY,CAACkB,WAAW,CAAC;IACzEe,MAAM,CAACH,IAAI,CAACb,cAAc,CAACa,IAAI,EAAE,IAAI,CAAC9B,YAAY,CAACkB,WAAW,CAAC;IAC/De,MAAM,CAACF,CAAC,CAACd,cAAc,CAACc,CAAC,EAAE,IAAI,CAAC/B,YAAY,CAACkB,WAAW,CAAC;IACzD,OAAOe,MAAM;EACf;;EAEA;EACOC,cAAcA,CAACC,SAAoB,EAAiB;IACzD,OAAO,IAAIC,OAAO,CAAC,CAACC,OAAO,EAAEC,MAAM,KAAK;MACtC,IAAI;QACF,MAAMC,IAAI,GAAG,IAAI5D,IAAI,CAAC;UACpB6D,GAAG,EAAE,CAACL,SAAS,CAACK,GAAG,CAAC;UACpBC,IAAI,EAAEN,SAAS,CAACM,IAAI;UACpBC,MAAM,EAAE,CAAC;UAAE;UACXC,KAAK,EAAE,IAAI;UACXC,MAAM,EAAEA,CAAA,KAAM;YACZ,IAAI,CAAC7D,WAAW,CAAC8D,GAAG,CAACV,SAAS,CAACW,EAAE,EAAE;cAAEP,IAAI;cAAEQ,IAAI,EAAEZ;YAAU,CAAC,CAAC;YAC7DE,OAAO,CAAC,CAAC;UACX,CAAC;UACDW,WAAW,EAAEA,CAACF,EAAsB,EAAEpB,KAAU,KAAK;YACnDY,MAAM,CAAC,6BAA6BH,SAAS,CAACc,IAAI,KAAKvB,KAAK,EAAE,CAAC;UACjE;QACF,CAAC,CAAC;MACJ,CAAC,CAAC,OAAOA,KAAK,EAAE;QACdY,MAAM,CAAC,2BAA2BH,SAAS,CAACc,IAAI,KAAKvB,KAAK,EAAE,CAAC;MAC/D;IACF,CAAC,CAAC;EACJ;;EAEA;EACOwB,eAAeA,CAACC,UAAuB,EAAiB;IAC7D,OAAO,IAAIf,OAAO,CAAC,CAACC,OAAO,EAAEC,MAAM,KAAK;MACtC,IAAI;QACF,MAAMC,IAAI,GAAG,IAAI5D,IAAI,CAAC;UACpB6D,GAAG,EAAE,CAACW,UAAU,CAACX,GAAG,CAAC;UACrBC,IAAI,EAAEU,UAAU,CAACV,IAAI;UACrBC,MAAM,EAAES,UAAU,CAACT,MAAM,GAAG,IAAI,CAAClD,SAAS;UAC1CmD,KAAK,EAAE,KAAK;UAAE;UACdC,MAAM,EAAEA,CAAA,KAAM;YACZ,IAAI,CAAC5D,YAAY,CAAC6D,GAAG,CAACM,UAAU,CAACL,EAAE,EAAE;cAAEP,IAAI;cAAEQ,IAAI,EAAEI;YAAW,CAAC,CAAC;YAChEd,OAAO,CAAC,CAAC;UACX,CAAC;UACDW,WAAW,EAAEA,CAACF,EAAsB,EAAEpB,KAAU,KAAK;YACnDY,MAAM,CAAC,8BAA8Ba,UAAU,CAACF,IAAI,KAAKvB,KAAK,EAAE,CAAC;UACnE;QACF,CAAC,CAAC;MACJ,CAAC,CAAC,OAAOA,KAAK,EAAE;QACdY,MAAM,CAAC,2BAA2Ba,UAAU,CAACF,IAAI,KAAKvB,KAAK,EAAE,CAAC;MAChE;IACF,CAAC,CAAC;EACJ;;EAEA;EACO0B,SAASA,CAACC,OAAe,EAAQ;IACtC,IAAI,CAAC,IAAI,CAACtE,WAAW,CAACuE,GAAG,CAACD,OAAO,CAAC,EAAE;MAClC7B,OAAO,CAACE,KAAK,CAAC,eAAe2B,OAAO,YAAY,CAAC;MACjD;IACF;IAEA,IAAI,IAAI,CAACnE,YAAY,KAAKmE,OAAO,EAAE;MACjC,OAAO,CAAC;IACV;IAEA,IAAI,CAAClE,SAAS,GAAGkE,OAAO;;IAExB;IACA,IAAI,IAAI,CAACnE,YAAY,KAAK,IAAI,EAAE;MAC9B,IAAI,CAACqE,UAAU,CAACF,OAAO,CAAC;MACxB;IACF;;IAEA;IACA,IAAI,CAACG,SAAS,CAAC,IAAI,CAACtE,YAAY,EAAEmE,OAAO,CAAC;EAC5C;;EAEA;EACQE,UAAUA,CAACF,OAAe,EAAQ;IACxC,MAAMI,KAAK,GAAG,IAAI,CAAC1E,WAAW,CAAC2E,GAAG,CAACL,OAAO,CAAC;IAC3C,IAAI,CAACI,KAAK,EAAE;IAEZ,MAAM;MAAElB,IAAI;MAAEQ;IAAK,CAAC,GAAGU,KAAK;;IAE5B;IACA,MAAME,YAAY,GAAG,IAAI,CAACjE,YAAY,GAAG,CAAC,GAAGqD,IAAI,CAACL,MAAM,GAAG,IAAI,CAACnD,WAAW;IAE3EgD,IAAI,CAACG,MAAM,CAACiB,YAAY,CAAC;IACzBpB,IAAI,CAACqB,IAAI,CAAC,CAAC;IAEX,IAAI,CAAC1E,YAAY,GAAGmE,OAAO;IAC3B,IAAI,CAAClE,SAAS,GAAG,IAAI;EACvB;;EAEA;EACQqE,SAASA,CAACK,WAAmB,EAAEC,SAAiB,EAAQ;IAC9D,MAAMC,SAAS,GAAG,IAAI,CAAChF,WAAW,CAAC2E,GAAG,CAACG,WAAW,CAAC;IACnD,MAAMG,OAAO,GAAG,IAAI,CAACjF,WAAW,CAAC2E,GAAG,CAACI,SAAS,CAAC;IAE/C,IAAI,CAACC,SAAS,IAAI,CAACC,OAAO,EAAE;IAE5B,MAAM;MAAEzB,IAAI,EAAE0B;IAAS,CAAC,GAAGF,SAAS;IACpC,MAAM;MAAExB,IAAI,EAAE2B,MAAM;MAAEnB,IAAI,EAAEoB;IAAO,CAAC,GAAGH,OAAO,CAAC,CAAC;;IAEhD;IACA,MAAMI,gBAAgB,GAAG,CAAC;IAC1B,MAAMC,cAAc,GAAG,IAAI,CAAC3E,YAAY,GAAG,CAAC,GAAGyE,MAAM,CAACzB,MAAM,GAAG,IAAI,CAACnD,WAAW;;IAE/E;IACA2E,MAAM,CAACxB,MAAM,CAAC,CAAC,CAAC;IAChBwB,MAAM,CAACN,IAAI,CAAC,CAAC;;IAEb;IACAK,QAAQ,CAACK,IAAI,CAACL,QAAQ,CAACvB,MAAM,CAAC,CAAC,EAAE0B,gBAAgB,EAAE,IAAI,CAAC9E,YAAY,CAAC;;IAErE;IACA4E,MAAM,CAACI,IAAI,CAAC,CAAC,EAAED,cAAc,EAAE,IAAI,CAAC/E,YAAY,CAAC;;IAEjD;IACAiF,UAAU,CAAC,MAAM;MACfN,QAAQ,CAACO,IAAI,CAAC,CAAC;MACf,IAAI,CAACtF,YAAY,GAAG4E,SAAS;MAC7B,IAAI,CAAC3E,SAAS,GAAG,IAAI;IACvB,CAAC,EAAE,IAAI,CAACG,YAAY,CAAC;EACvB;;EAEA;EACOmF,eAAeA,CAACC,QAAgB,EAAU;IAC/C,IAAI,IAAI,CAAC/E,UAAU,EAAE,OAAO,CAAC,CAAC;IAE9B,MAAMgF,MAAM,GAAG,IAAI,CAAC3F,YAAY,CAAC0E,GAAG,CAACgB,QAAQ,CAAC;IAC9C,IAAI,CAACC,MAAM,EAAE;MACXnD,OAAO,CAACE,KAAK,CAAC,gBAAgBgD,QAAQ,YAAY,CAAC;MACnD,OAAO,CAAC,CAAC;IACX;IAEA,MAAM;MAAEnC;IAAK,CAAC,GAAGoC,MAAM;IACvB,OAAOpC,IAAI,CAACqB,IAAI,CAAC,CAAC;EACpB;;EAEA;EACOgB,eAAeA,CAACF,QAAgB,EAAE5B,EAAW,EAAQ;IAC1D,MAAM6B,MAAM,GAAG,IAAI,CAAC3F,YAAY,CAAC0E,GAAG,CAACgB,QAAQ,CAAC;IAC9C,IAAI,CAACC,MAAM,EAAE;IAEb,IAAI7B,EAAE,KAAK+B,SAAS,EAAE;MACpBF,MAAM,CAACpC,IAAI,CAACiC,IAAI,CAAC1B,EAAE,CAAC;IACtB,CAAC,MAAM;MACL6B,MAAM,CAACpC,IAAI,CAACiC,IAAI,CAAC,CAAC;IACpB;EACF;;EAEA;EACOM,cAAcA,CAACpC,MAAc,EAAQ;IAC1C,IAAI,CAACnD,WAAW,GAAGwF,IAAI,CAACC,GAAG,CAAC,CAAC,EAAED,IAAI,CAACE,GAAG,CAAC,CAAC,EAAEvC,MAAM,CAAC,CAAC;IAEnD,IAAI,IAAI,CAACxD,YAAY,IAAI,CAAC,IAAI,CAACQ,YAAY,EAAE;MAC3C,MAAM+D,KAAK,GAAG,IAAI,CAAC1E,WAAW,CAAC2E,GAAG,CAAC,IAAI,CAACxE,YAAY,CAAC;MACrD,IAAIuE,KAAK,EAAE;QACTA,KAAK,CAAClB,IAAI,CAACG,MAAM,CAACe,KAAK,CAACV,IAAI,CAACL,MAAM,GAAG,IAAI,CAACnD,WAAW,CAAC;MACzD;IACF;EACF;;EAEA;EACO2F,YAAYA,CAACxC,MAAc,EAAQ;IACxC,IAAI,CAAClD,SAAS,GAAGuF,IAAI,CAACC,GAAG,CAAC,CAAC,EAAED,IAAI,CAACE,GAAG,CAAC,CAAC,EAAEvC,MAAM,CAAC,CAAC;IAEjD,IAAI,CAAC,IAAI,CAAC/C,UAAU,EAAE;MACpB,IAAI,CAACX,YAAY,CAACmG,OAAO,CAAC,CAAC;QAAE5C,IAAI;QAAEQ;MAAK,CAAC,KAAK;QAC5CR,IAAI,CAACG,MAAM,CAACK,IAAI,CAACL,MAAM,GAAG,IAAI,CAAClD,SAAS,CAAC;MAC3C,CAAC,CAAC;IACJ;EACF;;EAEA;EACO4F,eAAeA,CAAA,EAAY;IAChC,IAAI,CAAC1F,YAAY,GAAG,CAAC,IAAI,CAACA,YAAY;IAEtC,IAAI,IAAI,CAACR,YAAY,EAAE;MACrB,MAAMuE,KAAK,GAAG,IAAI,CAAC1E,WAAW,CAAC2E,GAAG,CAAC,IAAI,CAACxE,YAAY,CAAC;MACrD,IAAIuE,KAAK,EAAE;QACTA,KAAK,CAAClB,IAAI,CAACG,MAAM,CAAC,IAAI,CAAChD,YAAY,GAAG,CAAC,GAAG+D,KAAK,CAACV,IAAI,CAACL,MAAM,GAAG,IAAI,CAACnD,WAAW,CAAC;MACjF;IACF;IAEA,OAAO,IAAI,CAACG,YAAY;EAC1B;;EAEA;EACO2F,aAAaA,CAAA,EAAY;IAC9B,IAAI,CAAC1F,UAAU,GAAG,CAAC,IAAI,CAACA,UAAU;IAElC,IAAI,CAACX,YAAY,CAACmG,OAAO,CAAC,CAAC;MAAE5C,IAAI;MAAEQ;IAAK,CAAC,KAAK;MAC5CR,IAAI,CAACG,MAAM,CAAC,IAAI,CAAC/C,UAAU,GAAG,CAAC,GAAGoD,IAAI,CAACL,MAAM,GAAG,IAAI,CAAClD,SAAS,CAAC;IACjE,CAAC,CAAC;IAEF,OAAO,IAAI,CAACG,UAAU;EACxB;;EAEA;EACO2F,cAAcA,CAAC5C,MAAc,EAAQ;IAC1C,IAAI,CAACjD,WAAW,GAAGsF,IAAI,CAACC,GAAG,CAAC,CAAC,EAAED,IAAI,CAACE,GAAG,CAAC,CAAC,EAAEvC,MAAM,CAAC,CAAC;IAEnD,IAAI,CAAC,IAAI,CAAC9C,YAAY,EAAE;MACtB,IAAI,CAACX,cAAc,CAACkG,OAAO,CAAC,CAAC;QAAE5C,IAAI;QAAEQ;MAAK,CAAC,KAAK;QAC9CR,IAAI,CAACG,MAAM,CAAC,IAAI,CAACjD,WAAW,CAAC;MAC/B,CAAC,CAAC;IACJ;EACF;;EAEA;EACO8F,eAAeA,CAAA,EAAY;IAChC,IAAI,CAAC3F,YAAY,GAAG,CAAC,IAAI,CAACA,YAAY;IAEtC,IAAI,CAACX,cAAc,CAACkG,OAAO,CAAC,CAAC;MAAE5C;IAAK,CAAC,KAAK;MACxCA,IAAI,CAACG,MAAM,CAAC,IAAI,CAAC9C,YAAY,GAAG,CAAC,GAAG,IAAI,CAACH,WAAW,CAAC;IACvD,CAAC,CAAC;IAEF,OAAO,IAAI,CAACG,YAAY;EAC1B;;EAEA;EACO4F,iBAAiBA,CAACC,QAAuB,EAAiB;IAC/D,OAAO,IAAIrD,OAAO,CAAC,CAACC,OAAO,EAAEC,MAAM,KAAK;MACtC,IAAI;QACF,MAAMC,IAAI,GAAG,IAAI5D,IAAI,CAAC;UACpB6D,GAAG,EAAE,CAACiD,QAAQ,CAACC,QAAQ,CAAC;UACxBhD,MAAM,EAAE,IAAI,CAAC9C,YAAY,GAAG,CAAC,GAAG,IAAI,CAACH,WAAW;UAChDkD,KAAK,EAAE,IAAI;UACXgD,OAAO,EAAE,IAAI;UACb/C,MAAM,EAAEA,CAAA,KAAM;YACZ;YACA6C,QAAQ,CAACG,QAAQ,GAAGrD,IAAI,CAACqD,QAAQ,CAAC,CAAC,GAAG,IAAI,CAAC,CAAC;YAC5CvD,OAAO,CAAC,CAAC;UACX,CAAC;UACDwD,OAAO,EAAGnE,KAAK,IAAK;YAClBF,OAAO,CAACE,KAAK,CAAC,iCAAiC+D,QAAQ,CAAC3C,EAAE,GAAG,EAAEpB,KAAK,CAAC;YACrEY,MAAM,CAACZ,KAAK,CAAC;UACf;QACF,CAAC,CAAC;QAEF,IAAI,CAACzC,cAAc,CAAC4D,GAAG,CAAC4C,QAAQ,CAAC3C,EAAE,EAAE;UAAEP,IAAI;UAAEQ,IAAI,EAAE0C;QAAS,CAAC,CAAC;MAEhE,CAAC,CAAC,OAAO/D,KAAK,EAAE;QACdF,OAAO,CAACE,KAAK,CAAC,gCAAgC+D,QAAQ,CAAC3C,EAAE,GAAG,EAAEpB,KAAK,CAAC;QACpEY,MAAM,CAACZ,KAAK,CAAC;MACf;IACF,CAAC,CAAC;EACJ;;EAEA;EACOoE,iBAAiBA,CAACC,UAAkB,EAAEC,UAAuB,EAAiB;IACnF,OAAO,IAAI5D,OAAO,CAAC,CAACC,OAAO,EAAEC,MAAM,KAAK;MACtC,MAAMmD,QAAQ,GAAG,IAAI,CAACxG,cAAc,CAACyE,GAAG,CAACqC,UAAU,CAAC;MACpD,IAAI,CAACN,QAAQ,EAAE;QACbjE,OAAO,CAACE,KAAK,CAAC,kBAAkBqE,UAAU,YAAY,CAAC;QACvDzD,MAAM,CAAC,IAAIN,KAAK,CAAC,kBAAkB+D,UAAU,YAAY,CAAC,CAAC;QAC3D;MACF;;MAEA;MACA,IAAI,IAAI,CAAC3G,YAAY,EAAE;QACrB,IAAI,CAAC6G,iBAAiB,CAAC,IAAI,CAAC7G,YAAY,CAAC;MAC3C;MAEA,MAAM;QAAEmD,IAAI;QAAEQ;MAAK,CAAC,GAAG0C,QAAQ;;MAE/B;MACA,MAAMS,cAAc,GAAGA,CAAA,KAAM;QAC3B,IAAI,CAAC9G,YAAY,GAAG,IAAI;QACxB,IAAI4G,UAAU,EAAEA,UAAU,CAAC,CAAC;QAC5B3D,OAAO,CAAC,CAAC;;QAET;QACA,IAAI,CAAC8D,iBAAiB,CAAC,CAAC;MAC1B,CAAC;MAED5D,IAAI,CAAC6D,IAAI,CAAC,KAAK,EAAEF,cAAc,CAAC;MAChC3D,IAAI,CAAC6D,IAAI,CAAC,MAAM,EAAEF,cAAc,CAAC;;MAEjC;MACA3D,IAAI,CAACG,MAAM,CAAC,IAAI,CAAC9C,YAAY,GAAG,CAAC,GAAG,IAAI,CAACH,WAAW,CAAC;MACrD8C,IAAI,CAACqB,IAAI,CAAC,CAAC;MAEX,IAAI,CAACxE,YAAY,GAAG2G,UAAU;MAE9BvE,OAAO,CAACC,GAAG,CAAC,2BAA2BsB,IAAI,CAACsD,SAAS,OAAOtD,IAAI,CAACuD,IAAI,GAAG,CAAC;IAC3E,CAAC,CAAC;EACJ;;EAEA;EACOC,kBAAkBA,CAACd,QAAuB,EAAQ;IACvD,IAAI,CAACpG,UAAU,CAACmH,IAAI,CAACf,QAAQ,CAAC;;IAE9B;IACA,IAAI,CAAC,IAAI,CAACrG,YAAY,EAAE;MACtB,IAAI,CAAC+G,iBAAiB,CAAC,CAAC;IAC1B;EACF;;EAEA;EACQA,iBAAiBA,CAAA,EAAS;IAChC,IAAI,IAAI,CAAC9G,UAAU,CAACoH,MAAM,KAAK,CAAC,IAAI,IAAI,CAACrH,YAAY,EAAE;MACrD;IACF;IAEA,MAAMsH,YAAY,GAAG,IAAI,CAACrH,UAAU,CAACsH,KAAK,CAAC,CAAC;IAC5C,IAAID,YAAY,EAAE;MAChB;MACA,IAAI,CAAClB,iBAAiB,CAACkB,YAAY,CAAC,CAACE,IAAI,CAAC,MAAM;QAC9C,IAAI,CAACd,iBAAiB,CAACY,YAAY,CAAC5D,EAAE,CAAC;MACzC,CAAC,CAAC,CAAC+D,KAAK,CAACnF,KAAK,IAAI;QAChBF,OAAO,CAACE,KAAK,CAAC,iCAAiC,EAAEA,KAAK,CAAC;QACvD,IAAI,CAACyE,iBAAiB,CAAC,CAAC,CAAC,CAAC;MAC5B,CAAC,CAAC;IACJ;EACF;;EAEA;EACOF,iBAAiBA,CAACF,UAAmB,EAAQ;IAClD,IAAIA,UAAU,EAAE;MACd,MAAMN,QAAQ,GAAG,IAAI,CAACxG,cAAc,CAACyE,GAAG,CAACqC,UAAU,CAAC;MACpD,IAAIN,QAAQ,EAAE;QACZA,QAAQ,CAAClD,IAAI,CAACiC,IAAI,CAAC,CAAC;MACtB;MACA,IAAI,IAAI,CAACpF,YAAY,KAAK2G,UAAU,EAAE;QACpC,IAAI,CAAC3G,YAAY,GAAG,IAAI;MAC1B;IACF,CAAC,MAAM;MACL;MACA,IAAI,CAACH,cAAc,CAACkG,OAAO,CAAC,CAAC;QAAE5C;MAAK,CAAC,KAAK;QACxCA,IAAI,CAACiC,IAAI,CAAC,CAAC;MACb,CAAC,CAAC;MACF,IAAI,CAACpF,YAAY,GAAG,IAAI;MACxB,IAAI,CAACC,UAAU,GAAG,EAAE;IACtB;EACF;;EAEA;EACOyH,gBAAgBA,CAACC,SAAiB,EAAQ;IAC/C,IAAI,CAAClH,aAAa,GAAGkF,IAAI,CAACC,GAAG,CAAC,CAAC,EAAED,IAAI,CAACE,GAAG,CAAC,CAAC,EAAE8B,SAAS,CAAC,CAAC;IACxD,IAAI,CAACC,mBAAmB,CAAC,CAAC;EAC5B;;EAEA;EACOC,oBAAoBA,CAACC,IAAsD,EAAQ;IACxF,IAAI,CAACpH,iBAAiB,GAAGoH,IAAI;IAC7B,IAAI,CAACF,mBAAmB,CAAC,CAAC;EAC5B;;EAEA;EACOG,eAAeA,CAACC,OAAe,EAAQ;IAC5C,IAAI,CAACrH,YAAY,GAAGqH,OAAO;IAC3B,IAAI,CAACJ,mBAAmB,CAAC,CAAC;EAC5B;;EAEA;EACQA,mBAAmBA,CAAA,EAAS;IAClC;IACA,IAAI,CAAC,IAAI,CAAC9H,YAAY,EAAE;IAExB,MAAMmI,YAAY,GAAG,IAAI,CAACtI,WAAW,CAAC2E,GAAG,CAAC,IAAI,CAACxE,YAAY,CAAC;IAC5D,IAAI,CAACmI,YAAY,EAAE;IAEnB,MAAMC,QAAQ,GAAGD,YAAY,CAACtE,IAAI,CAACuE,QAAQ;;IAE3C;IACA,IAAIC,aAA4B,GAAG,IAAI;IAEvC,QAAQD,QAAQ;MACd,KAAK,MAAM;QACT;QACA,IAAI,IAAI,CAACzH,aAAa,GAAG,GAAG,EAAE;UAC5B0H,aAAa,GAAG,IAAI,CAACC,oBAAoB,CAAC,MAAM,EAAE,KAAK,CAAC;QAC1D,CAAC,MAAM,IAAI,IAAI,CAAC3H,aAAa,GAAG,GAAG,EAAE;UACnC0H,aAAa,GAAG,IAAI,CAACC,oBAAoB,CAAC,MAAM,EAAE,QAAQ,CAAC;QAC7D,CAAC,MAAM;UACLD,aAAa,GAAG,IAAI,CAACC,oBAAoB,CAAC,MAAM,EAAE,MAAM,CAAC;QAC3D;QACA;MAEF,KAAK,KAAK;QACR;QACAD,aAAa,GAAG,IAAI,CAACE,eAAe,CAAC,IAAI,CAAC3H,iBAAiB,CAAC;QAC5D;MAEF,KAAK,OAAO;QACV;QACAyH,aAAa,GAAG,IAAI,CAACG,kBAAkB,CAAC,IAAI,CAAC3H,YAAY,CAAC;QAC1D;IACJ;;IAEA;IACA,IAAIwH,aAAa,IAAIA,aAAa,KAAK,IAAI,CAACrI,YAAY,IAAIqI,aAAa,KAAK,IAAI,CAACpI,SAAS,EAAE;MAC5F,IAAI,CAACiE,SAAS,CAACmE,aAAa,CAAC;IAC/B;EACF;;EAEA;EACQC,oBAAoBA,CAACF,QAA2C,EAAEP,SAAoC,EAAiB;IAC7H,KAAK,MAAM,CAACjE,EAAE,EAAE;MAAEC;IAAK,CAAC,CAAC,IAAI,IAAI,CAAChE,WAAW,CAAC4I,OAAO,CAAC,CAAC,EAAE;MACvD,IAAI5E,IAAI,CAACuE,QAAQ,KAAKA,QAAQ,IAAIvE,IAAI,CAACgE,SAAS,KAAKA,SAAS,EAAE;QAC9D,OAAOjE,EAAE;MACX;IACF;IACA,OAAO,IAAI;EACb;;EAEA;EACQ2E,eAAeA,CAACP,IAAsD,EAAiB;IAC7F;IACA;IACA,MAAMU,eAA6D,GAAG;MACpE,QAAQ,EAAE,QAAQ;MAClB,SAAS,EAAE,KAAK;MAChB,YAAY,EAAE,MAAM;MACpB,UAAU,EAAE;IACd,CAAC;IAED,OAAO,IAAI,CAACJ,oBAAoB,CAAC,KAAK,EAAEI,eAAe,CAACV,IAAI,CAAC,CAAC;EAChE;;EAEA;EACQQ,kBAAkBA,CAACN,OAAe,EAAiB;IACzD;IACA;IACA,MAAMS,kBAAgE,GAAG;MACvE,CAAC,EAAE,KAAK;MACR,CAAC,EAAE,QAAQ;MACX,CAAC,EAAE,MAAM;MACT,CAAC,EAAE,MAAM;MACT,CAAC,EAAE;IACL,CAAC;IAED,MAAMd,SAAS,GAAGc,kBAAkB,CAACT,OAAO,CAAC,IAAI,QAAQ;IACzD,OAAO,IAAI,CAACI,oBAAoB,CAAC,OAAO,EAAET,SAAS,CAAC;EACtD;;EAEA;EACOe,OAAOA,CAAA,EAAS;IACrB,IAAI,CAAC/I,WAAW,CAACoG,OAAO,CAAC,CAAC;MAAE5C;IAAK,CAAC,KAAK;MACrCA,IAAI,CAACiC,IAAI,CAAC,CAAC;MACXjC,IAAI,CAACwF,MAAM,CAAC,CAAC,CAAC,CAAC;IACjB,CAAC,CAAC;IACF,IAAI,CAAC/I,YAAY,CAACmG,OAAO,CAAC,CAAC;MAAE5C;IAAK,CAAC,KAAK;MACtCA,IAAI,CAACiC,IAAI,CAAC,CAAC;MACXjC,IAAI,CAACwF,MAAM,CAAC,CAAC,CAAC,CAAC;IACjB,CAAC,CAAC;IACFnJ,MAAM,CAAC4F,IAAI,CAAC,CAAC,CAAC,CAAC;IACf,IAAI,CAACtF,YAAY,GAAG,IAAI;IACxB,IAAI,CAACC,SAAS,GAAG,IAAI;EACvB;;EAEA;EACO6I,OAAOA,CAAA,EAAS;IACrB,IAAI,CAACF,OAAO,CAAC,CAAC,CAAC,CAAC;IAChB,IAAI,CAAC/I,WAAW,CAACkJ,KAAK,CAAC,CAAC;IACxB,IAAI,CAACjJ,YAAY,CAACiJ,KAAK,CAAC,CAAC;IACzB;EACF;AACF;AAACC,YAAA,GA/jBYrJ,WAAW;AAAXA,WAAW,CACP8B,QAAQ", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}