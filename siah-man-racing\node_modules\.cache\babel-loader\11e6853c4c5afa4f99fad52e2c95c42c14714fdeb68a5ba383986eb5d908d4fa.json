{"ast": null, "code": "var _TTSSystem;\n// Text-to-Speech System for Super Siah Man Racing\n// Provides character voices for game dialogue and events\n\nimport { Howl } from 'howler';\nexport class TTSSystem {\n  constructor() {\n    this.voiceProfiles = void 0;\n    this.speechSynthesis = void 0;\n    this.voices = [];\n    this.messageQueue = [];\n    this.isSpeaking = false;\n    this.isEnabled = true;\n    this.volume = 1.0;\n    this.currentUtterance = null;\n    this.currentHowl = null;\n    // Audio pool management to prevent WebMediaPlayer errors\n    this.MAX_AUDIO_INSTANCES = 8;\n    this.audioPool = [];\n    this.activeAudioCount = 0;\n    this.audioInstanceMap = new Map();\n    this.voiceProfiles = new Map();\n\n    // Check if speech synthesis is available\n    if (typeof window !== 'undefined' && window.speechSynthesis) {\n      this.speechSynthesis = window.speechSynthesis;\n\n      // Load available voices\n      this.loadVoices();\n\n      // Some browsers need a delay to load voices\n      if (this.voices.length === 0) {\n        setTimeout(() => this.loadVoices(), 1000);\n      }\n    } else {\n      this.speechSynthesis = null;\n      console.warn('Speech synthesis not supported in this browser. Character audio will still work if provided.');\n    }\n\n    // Initialize default voice profiles\n    this.initializeDefaultVoices();\n  }\n  static getInstance() {\n    if (!TTSSystem.instance) {\n      TTSSystem.instance = new TTSSystem();\n    }\n    return TTSSystem.instance;\n  }\n\n  /**\n   * Load available voices from the browser\n   */\n  loadVoices() {\n    if (!this.speechSynthesis) return;\n\n    // Ensure voices are loaded before trying to get them\n    this.speechSynthesis.onvoiceschanged = () => {\n      this.voices = this.speechSynthesis.getVoices();\n      console.log(`Loaded ${this.voices.length} voices from speech synthesis API`);\n    };\n\n    // Attempt to load voices immediately in case they are already available\n    this.voices = this.speechSynthesis.getVoices();\n    if (this.voices.length > 0) {\n      console.log(`Loaded ${this.voices.length} voices from speech synthesis API`);\n    }\n  }\n\n  /**\n   * Initialize default voice profiles for game characters\n   */\n  initializeDefaultVoices() {\n    // Main character - Siah Man\n    this.addVoiceProfile({\n      id: 'siah_man',\n      name: 'Siah Man',\n      gender: 'male',\n      pitch: 1.0,\n      rate: 1.0,\n      volume: 1.0,\n      accent: 'heroic',\n      character: 'Siah Man'\n    });\n\n    // Dr. Neal\n    this.addVoiceProfile({\n      id: 'dr_neal',\n      name: 'Dr. Neal',\n      gender: 'male',\n      pitch: 0.9,\n      rate: 1.1,\n      volume: 1.0,\n      accent: 'scientific',\n      character: 'Dr. Neal'\n    });\n\n    // AI Opponents\n    this.addVoiceProfile({\n      id: 'max_velocity',\n      name: 'Max Velocity',\n      gender: 'male',\n      pitch: 1.1,\n      rate: 1.2,\n      volume: 0.9,\n      accent: 'confident',\n      character: 'Max Velocity'\n    });\n    this.addVoiceProfile({\n      id: 'turbo_tina',\n      name: 'Turbo Tina',\n      gender: 'female',\n      pitch: 1.2,\n      rate: 1.1,\n      volume: 0.9,\n      accent: 'energetic',\n      character: 'Turbo Tina'\n    });\n    this.addVoiceProfile({\n      id: 'drift_king',\n      name: 'Drift King',\n      gender: 'male',\n      pitch: 0.9,\n      rate: 0.9,\n      volume: 1.0,\n      accent: 'smooth',\n      character: 'Drift King'\n    });\n\n    // Race announcer\n    this.addVoiceProfile({\n      id: 'announcer',\n      name: 'Race Announcer',\n      gender: 'male',\n      pitch: 1.0,\n      rate: 1.3,\n      volume: 1.0,\n      accent: 'announcer',\n      character: 'Race Announcer'\n    });\n\n    // Navigation system\n    this.addVoiceProfile({\n      id: 'nav_system',\n      name: 'Navigation System',\n      gender: 'female',\n      pitch: 1.1,\n      rate: 1.0,\n      volume: 0.8,\n      accent: 'robotic',\n      character: 'Navigation System'\n    });\n\n    // Car Whisperer\n    this.addVoiceProfile({\n      id: 'car_whisperer',\n      name: 'Car Whisperer',\n      gender: 'neutral',\n      pitch: 0.8,\n      rate: 0.9,\n      volume: 0.7,\n      accent: 'mystical',\n      character: 'Car Whisperer'\n    });\n  }\n\n  /**\n   * Add a new voice profile\n   */\n  addVoiceProfile(profile) {\n    if (this.voiceProfiles.has(profile.id)) {\n      console.warn(`Voice profile with ID ${profile.id} already exists. Overwriting.`);\n    }\n\n    // Find a matching system voice if available\n    if (this.speechSynthesis && !profile.voiceURI) {\n      const matchingVoice = this.findMatchingSystemVoice(profile);\n      if (matchingVoice) {\n        profile.voiceURI = matchingVoice.voiceURI;\n      }\n    }\n    this.voiceProfiles.set(profile.id, profile);\n  }\n\n  /**\n   * Find a matching system voice based on profile preferences\n   */\n  findMatchingSystemVoice(profile) {\n    if (!this.speechSynthesis || this.voices.length === 0) return null;\n\n    // Filter voices by gender if specified\n    let matchingVoices = this.voices;\n    if (profile.gender === 'male') {\n      matchingVoices = matchingVoices.filter(v => v.name.toLowerCase().includes('male') || v.name.toLowerCase().includes('guy') || v.name.toLowerCase().includes('man'));\n    } else if (profile.gender === 'female') {\n      matchingVoices = matchingVoices.filter(v => v.name.toLowerCase().includes('female') || v.name.toLowerCase().includes('woman') || v.name.toLowerCase().includes('girl'));\n    }\n\n    // If we have accent info, try to match that too\n    if (profile.accent && matchingVoices.length > 1) {\n      const accentMatches = matchingVoices.filter(v => v.name.toLowerCase().includes(profile.accent.toLowerCase()));\n      if (accentMatches.length > 0) {\n        matchingVoices = accentMatches;\n      }\n    }\n\n    // Return the first matching voice or null if none found\n    return matchingVoices.length > 0 ? matchingVoices[0] : null;\n  }\n\n  /**\n   * Speak a message using TTS\n   */\n  speak(message) {\n    if (!this.isEnabled) return;\n\n    // Add to queue based on priority\n    this.addToQueue(message);\n\n    // Start processing the queue if not already speaking\n    if (!this.isSpeaking) {\n      this.processQueue();\n    }\n  }\n\n  /**\n   * Set the AudioSystem instance for synchronized voice playback\n   */\n  setAudioSystem(audioSystem) {\n    this.audioSystem = audioSystem;\n  }\n\n  /**\n   * Play character voice with proper synchronization\n   */\n  async playCharacterVoice(character, dialogueId, text, onComplete) {\n    if (this.audioSystem) {\n      try {\n        // Try to play the actual voice file first\n        await this.audioSystem.playVoiceDialogue(dialogueId, onComplete);\n        console.log(`Playing character voice: ${character} - \"${text}\"`);\n      } catch (error) {\n        console.warn(`Voice file not found for ${dialogueId}, falling back to TTS`);\n        // Fallback to TTS if voice file is not available\n        this.speak({\n          text,\n          voiceId: character,\n          priority: 'high',\n          category: 'dialogue',\n          onEnd: onComplete\n        });\n      }\n    } else {\n      // No AudioSystem available, use TTS\n      this.speak({\n        text,\n        voiceId: character,\n        priority: 'high',\n        category: 'dialogue',\n        onEnd: onComplete\n      });\n    }\n  }\n\n  /**\n   * Play synchronized story dialogue with character voices\n   */\n  async playStoryDialogue(character, text, audioSrc, onComplete) {\n    if (audioSrc && this.audioSystem) {\n      // Create a temporary dialogue entry\n      const dialogueId = `temp_${character}_${Date.now()}`;\n      const dialogue = {\n        id: dialogueId,\n        character,\n        text,\n        audioSrc,\n        priority: 'high'\n      };\n      try {\n        await this.audioSystem.loadVoiceDialogue(dialogue);\n        await this.audioSystem.playVoiceDialogue(dialogueId, onComplete);\n        console.log(`Playing story dialogue: ${character} - \"${text}\"`);\n      } catch (error) {\n        console.warn(`Failed to play voice file for ${character}, falling back to TTS:`, error);\n        this.speak({\n          text,\n          voiceId: character,\n          priority: 'high',\n          category: 'dialogue',\n          onEnd: onComplete\n        });\n      }\n    } else {\n      // Fallback to TTS\n      this.speak({\n        text,\n        voiceId: character,\n        priority: 'high',\n        category: 'dialogue',\n        onEnd: onComplete\n      });\n    }\n  }\n\n  /**\n   * Add a message to the queue based on priority\n   */\n  addToQueue(message) {\n    if (!message.text || message.text.trim() === '') {\n      console.warn('Empty message text, ignoring');\n      return;\n    }\n\n    // Ensure the voice profile exists\n    if (!this.voiceProfiles.has(message.voiceId)) {\n      console.warn(`Voice profile not found for ID: ${message.voiceId}, using default`);\n      message.voiceId = 'announcer'; // Default fallback\n    }\n\n    // Add to queue based on priority\n    if (message.priority === 'high') {\n      // High priority - add to front of queue\n      this.messageQueue.unshift(message);\n    } else if (message.priority === 'medium') {\n      // Medium priority - add after other high priority messages\n      const lastHighPriorityIndex = this.messageQueue.findIndex(m => m.priority !== 'high');\n      if (lastHighPriorityIndex === -1) {\n        // No high priority messages, add to end\n        this.messageQueue.push(message);\n      } else {\n        // Insert after last high priority message\n        this.messageQueue.splice(lastHighPriorityIndex, 0, message);\n      }\n    } else {\n      // Low priority - add to end of queue\n      this.messageQueue.push(message);\n    }\n  }\n\n  /**\n   * Get an available audio instance from the pool or create a new one\n   */\n  getAudioInstance(src, options) {\n    // Check if we already have an instance for this source that's not playing\n    if (this.audioInstanceMap.has(src)) {\n      const existingHowl = this.audioInstanceMap.get(src);\n      if (!existingHowl.playing()) {\n        return existingHowl;\n      }\n    }\n\n    // Check if we've reached the maximum number of instances\n    if (this.activeAudioCount >= this.MAX_AUDIO_INSTANCES) {\n      // Find any non-playing instance to reuse\n      for (const howl of this.audioPool) {\n        if (!howl.playing()) {\n          howl.unload();\n          this.audioPool = this.audioPool.filter(h => h !== howl);\n          this.activeAudioCount--;\n\n          // Also remove from the instance map\n          for (const [key, value] of this.audioInstanceMap.entries()) {\n            if (value === howl) {\n              this.audioInstanceMap.delete(key);\n              break;\n            }\n          }\n          break;\n        }\n      }\n\n      // If we still have too many active instances, we can't create a new one\n      if (this.activeAudioCount >= this.MAX_AUDIO_INSTANCES) {\n        console.warn('All audio instances are in use. Skipping audio playback to prevent WebMediaPlayer error.');\n        return null;\n      }\n    }\n\n    // Create a new instance\n    const newInstance = new Howl(options);\n    this.audioPool.push(newInstance);\n    this.audioInstanceMap.set(src, newInstance);\n    this.activeAudioCount++;\n    return newInstance;\n  }\n\n  /**\n   * Release an audio instance back to the pool\n   */\n  releaseAudioInstance(howl, src) {\n    // Stop the audio if it's playing\n    if (howl.playing()) {\n      howl.stop();\n    }\n\n    // If we have too many instances, remove this one\n    if (this.audioPool.length > this.MAX_AUDIO_INSTANCES * 1.5) {\n      howl.unload();\n      this.audioPool = this.audioPool.filter(h => h !== howl);\n      this.activeAudioCount--;\n\n      // Remove from the instance map if we have the source\n      if (src && this.audioInstanceMap.get(src) === howl) {\n        this.audioInstanceMap.delete(src);\n      }\n    }\n  }\n\n  /**\n   * Process the message queue\n   */\n  processQueue() {\n    if (!this.isEnabled || this.isSpeaking || this.messageQueue.length === 0) {\n      return;\n    }\n\n    // Get the next message\n    const message = this.messageQueue.shift();\n\n    // Get the voice profile\n    const profile = this.voiceProfiles.get(message.voiceId);\n    if (!profile) {\n      console.error(`Voice profile not found for ID: ${message.voiceId}`);\n      this.processQueue(); // Move to next message\n      return;\n    }\n\n    // Determine audio source - message specific or from profile\n    const audioSource = message.audioSrc || profile.audioSrc;\n\n    // If there's an audio file for this message, use it\n    if (audioSource) {\n      // Configure audio options\n      const audioOptions = {\n        src: [audioSource],\n        volume: profile.volume * this.volume,\n        html5: true,\n        // Use HTML5 Audio to prevent WebMediaPlayer issues\n        onplay: () => {\n          this.isSpeaking = true;\n          if (message.onStart) message.onStart();\n        },\n        onend: () => {\n          this.isSpeaking = false;\n          if (this.currentHowl) {\n            this.releaseAudioInstance(this.currentHowl, audioSource);\n            this.currentHowl = null;\n          }\n          if (message.onEnd) message.onEnd();\n\n          // Process next message after a short delay\n          setTimeout(() => this.processQueue(), 100);\n        },\n        onloaderror: (id, error) => {\n          console.error(`Error loading audio: ${error}`);\n          this.isSpeaking = false;\n          if (this.currentHowl) {\n            this.releaseAudioInstance(this.currentHowl, audioSource);\n            this.currentHowl = null;\n          }\n\n          // Process next message\n          setTimeout(() => this.processQueue(), 100);\n        },\n        onplayerror: (id, error) => {\n          console.error(`Error playing audio: ${error}`);\n          this.isSpeaking = false;\n          if (this.currentHowl) {\n            this.releaseAudioInstance(this.currentHowl, audioSource);\n            this.currentHowl = null;\n          }\n\n          // Process next message\n          setTimeout(() => this.processQueue(), 100);\n        }\n      };\n\n      // Get an audio instance from the pool\n      const howl = this.getAudioInstance(audioSource, audioOptions);\n      if (howl) {\n        this.currentHowl = howl;\n        howl.play();\n      } else {\n        // If we couldn't get an audio instance, skip to next message\n        console.warn('Could not play audio due to WebMediaPlayer limitations. Skipping message.');\n        this.processQueue();\n      }\n    } else if (this.speechSynthesis) {\n      // Use browser's built-in TTS if no audio file is provided\n      const utterance = new SpeechSynthesisUtterance(message.text);\n\n      // Set voice if available\n      if (profile.voiceURI) {\n        const systemVoice = this.voices.find(v => v.voiceURI === profile.voiceURI);\n        if (systemVoice) {\n          utterance.voice = systemVoice;\n        }\n      } else {\n        // Try to find a matching voice based on gender and accent\n        const matchingVoice = this.findMatchingSystemVoice(profile);\n        if (matchingVoice) {\n          utterance.voice = matchingVoice;\n        }\n      }\n\n      // Set voice properties\n      utterance.pitch = profile.pitch;\n      utterance.rate = profile.rate;\n      utterance.volume = profile.volume * this.volume;\n\n      // Set callbacks\n      utterance.onstart = () => {\n        this.isSpeaking = true;\n        if (message.onStart) message.onStart();\n      };\n      utterance.onend = () => {\n        this.isSpeaking = false;\n        this.currentUtterance = null;\n        if (message.onEnd) message.onEnd();\n\n        // Process next message\n        setTimeout(() => this.processQueue(), 100);\n      };\n      utterance.onerror = event => {\n        console.error('TTS error:', event);\n        this.isSpeaking = false;\n        this.currentUtterance = null;\n\n        // Process next message\n        setTimeout(() => this.processQueue(), 100);\n      };\n\n      // Store current utterance\n      this.currentUtterance = utterance;\n\n      // Speak\n      this.speechSynthesis.speak(utterance);\n    } else {\n      console.warn('No audio source provided and speech synthesis not supported.');\n      this.processQueue(); // Move to the next message\n    }\n  }\n\n  /**\n   * Stop all speech and audio\n   */\n  stop() {\n    if (this.speechSynthesis) {\n      this.speechSynthesis.cancel();\n    }\n\n    // Stop and release all audio instances\n    this.audioPool.forEach(howl => {\n      if (howl.playing()) {\n        howl.stop();\n      }\n    });\n\n    // Clear the current howl reference\n    this.currentHowl = null;\n\n    // Reset state\n    this.isSpeaking = false;\n    this.currentUtterance = null;\n    this.messageQueue = []; // Clear the queue on stop\n  }\n\n  /**\n   * Clean up audio resources\n   */\n  cleanup() {\n    // Unload all audio instances to free up memory\n    this.audioPool.forEach(howl => {\n      howl.unload();\n    });\n    this.audioPool = [];\n    this.audioInstanceMap.clear();\n    this.activeAudioCount = 0;\n\n    // Stop any ongoing speech\n    this.stop();\n  }\n\n  /**\n   * Pause speech or audio\n   */\n  pause() {\n    if (this.speechSynthesis) {\n      this.speechSynthesis.pause();\n    }\n    if (this.currentHowl && this.currentHowl.playing()) {\n      this.currentHowl.pause();\n    }\n  }\n\n  /**\n   * Resume speech\n   */\n  resume() {\n    if (this.speechSynthesis) {\n      this.speechSynthesis.resume();\n    }\n    if (this.currentHowl) {\n      this.currentHowl.play();\n    }\n  }\n\n  /**\n   * Enable/disable TTS\n   */\n  setEnabled(enabled) {\n    this.isEnabled = enabled;\n    if (!enabled) {\n      this.stop();\n      this.messageQueue = [];\n    }\n  }\n\n  /**\n   * Set global TTS volume\n   */\n  setVolume(volume) {\n    this.volume = Math.max(0, Math.min(1, volume));\n\n    // Update current utterance if speaking\n    if (this.currentUtterance) {\n      this.currentUtterance.volume = this.volume;\n    }\n\n    // Update current Howl if playing\n    if (this.currentHowl) {\n      this.currentHowl.volume(this.volume);\n    }\n  }\n\n  /**\n   * Get all voice profiles\n   */\n  getAllVoiceProfiles() {\n    return Array.from(this.voiceProfiles.values());\n  }\n\n  /**\n   * Get a voice profile by ID\n   */\n  getVoiceProfile(id) {\n    return this.voiceProfiles.get(id);\n  }\n\n  /**\n   * Speak a dialogue line for a character\n   */\n  speakDialogue(characterId, text, priority = 'medium') {\n    this.speak({\n      text,\n      voiceId: characterId,\n      priority,\n      category: 'dialogue'\n    });\n  }\n\n  /**\n   * Announce a race event\n   */\n  announceRaceEvent(text, priority = 'medium') {\n    this.speak({\n      text,\n      voiceId: 'announcer',\n      priority,\n      category: 'race_event'\n    });\n  }\n\n  /**\n   * Speak a tutorial instruction\n   */\n  speakTutorial(text, priority = 'medium') {\n    this.speak({\n      text,\n      voiceId: 'nav_system',\n      priority,\n      category: 'tutorial'\n    });\n  }\n\n  /**\n   * Announce an achievement\n   */\n  announceAchievement(text) {\n    this.speak({\n      text,\n      voiceId: 'announcer',\n      priority: 'high',\n      category: 'achievement'\n    });\n  }\n}\n_TTSSystem = TTSSystem;\nTTSSystem.instance = void 0;", "map": {"version": 3, "names": ["Howl", "TTSSystem", "constructor", "voiceProfiles", "speechSynthesis", "voices", "messageQueue", "isSpeaking", "isEnabled", "volume", "currentUtterance", "currentHowl", "MAX_AUDIO_INSTANCES", "audioPool", "activeAudioCount", "audioInstanceMap", "Map", "window", "loadVoices", "length", "setTimeout", "console", "warn", "initializeDefaultVoices", "getInstance", "instance", "onvoiceschanged", "getVoices", "log", "addVoiceProfile", "id", "name", "gender", "pitch", "rate", "accent", "character", "profile", "has", "voiceURI", "matchingVoice", "findMatchingSystemVoice", "set", "matchingVoices", "filter", "v", "toLowerCase", "includes", "accentMatches", "speak", "message", "addToQueue", "processQueue", "setAudioSystem", "audioSystem", "playCharacterVoice", "dialogueId", "text", "onComplete", "playVoiceDialogue", "error", "voiceId", "priority", "category", "onEnd", "playStoryDialogue", "audioSrc", "Date", "now", "dialogue", "loadVoiceDialogue", "trim", "unshift", "lastHighPriorityIndex", "findIndex", "m", "push", "splice", "getAudioInstance", "src", "options", "existingHowl", "get", "playing", "howl", "unload", "h", "key", "value", "entries", "delete", "newInstance", "releaseAudioInstance", "stop", "shift", "audioSource", "audioOptions", "html5", "onplay", "onStart", "onend", "onloaderror", "onplayerror", "play", "utterance", "SpeechSynthesisUtterance", "systemVoice", "find", "voice", "onstart", "onerror", "event", "cancel", "for<PERSON>ach", "cleanup", "clear", "pause", "resume", "setEnabled", "enabled", "setVolume", "Math", "max", "min", "getAllVoiceProfiles", "Array", "from", "values", "getVoiceProfile", "speakDialogue", "characterId", "announceRaceEvent", "speakTutorial", "announceAchievement", "_TTSSystem"], "sources": ["C:/Users/<USER>/Documents/Apps_Creations/SiahMan_Racing4Speed_WebGame/siah-man-racing/src/game/TTSSystem.ts"], "sourcesContent": ["// Text-to-Speech System for Super Siah Man Racing\n// Provides character voices for game dialogue and events\n\nimport { How<PERSON>, How<PERSON> } from 'howler';\n\nexport interface VoiceProfile {\n  id: string;\n  name: string;\n  gender: 'male' | 'female' | 'neutral';\n  pitch: number;       // 0.5-2.0\n  rate: number;        // 0.5-2.0\n  volume: number;      // 0-1\n  voiceURI?: string;   // For Web Speech API\n  accent?: string;     // Description of accent\n  character: string;   // Character this voice belongs to\n  audioSrc?: string;   // Optional: Source for character-specific audio file\n}\n\nexport interface TTSMessage {\n  text: string;\n  voiceId: string;\n  priority: 'low' | 'medium' | 'high';\n  category: 'dialogue' | 'race_event' | 'tutorial' | 'menu' | 'achievement' | 'gameplay';\n  onStart?: () => void;\n  onEnd?: () => void;\n  audioSrc?: string; // Optional: Source for this specific message's audio file\n}\n\nexport class TTSSystem {\n  private static instance: TTSSystem;\n  private voiceProfiles: Map<string, VoiceProfile>;\n  private speechSynthesis: SpeechSynthesis | null;\n  private voices: SpeechSynthesisVoice[] = [];\n  private messageQueue: TTSMessage[] = [];\n  private isSpeaking: boolean = false;\n  private isEnabled: boolean = true;\n  private volume: number = 1.0;\n  private currentUtterance: SpeechSynthesisUtterance | null = null;\n  private currentHowl: Howl | null = null;\n  \n  // Audio pool management to prevent WebMediaPlayer errors\n  private readonly MAX_AUDIO_INSTANCES = 8;\n  private audioPool: Howl[] = [];\n  private activeAudioCount: number = 0;\n  private audioInstanceMap: Map<string, Howl> = new Map();\n  \n  private constructor() {\n    this.voiceProfiles = new Map();\n    \n    // Check if speech synthesis is available\n    if (typeof window !== 'undefined' && window.speechSynthesis) {\n      this.speechSynthesis = window.speechSynthesis;\n      \n      // Load available voices\n      this.loadVoices();\n      \n      // Some browsers need a delay to load voices\n      if (this.voices.length === 0) {\n        setTimeout(() => this.loadVoices(), 1000);\n      }\n    } else {\n      this.speechSynthesis = null;\n      console.warn('Speech synthesis not supported in this browser. Character audio will still work if provided.');\n    }\n    \n    // Initialize default voice profiles\n    this.initializeDefaultVoices();\n  }\n  \n  public static getInstance(): TTSSystem {\n    if (!TTSSystem.instance) {\n      TTSSystem.instance = new TTSSystem();\n    }\n    return TTSSystem.instance;\n  }\n  \n  /**\n   * Load available voices from the browser\n   */\n  private loadVoices(): void {\n    if (!this.speechSynthesis) return;\n    \n    // Ensure voices are loaded before trying to get them\n    this.speechSynthesis.onvoiceschanged = () => {\n      this.voices = this.speechSynthesis!.getVoices();\n      console.log(`Loaded ${this.voices.length} voices from speech synthesis API`);\n    };\n    \n    // Attempt to load voices immediately in case they are already available\n    this.voices = this.speechSynthesis.getVoices();\n    if (this.voices.length > 0) {\n        console.log(`Loaded ${this.voices.length} voices from speech synthesis API`);\n    }\n  }\n  \n  /**\n   * Initialize default voice profiles for game characters\n   */\n  private initializeDefaultVoices(): void {\n    // Main character - Siah Man\n    this.addVoiceProfile({\n      id: 'siah_man',\n      name: 'Siah Man',\n      gender: 'male',\n      pitch: 1.0,\n      rate: 1.0,\n      volume: 1.0,\n      accent: 'heroic',\n      character: 'Siah Man'\n    });\n    \n    // Dr. Neal\n    this.addVoiceProfile({\n      id: 'dr_neal',\n      name: 'Dr. Neal',\n      gender: 'male',\n      pitch: 0.9,\n      rate: 1.1,\n      volume: 1.0,\n      accent: 'scientific',\n      character: 'Dr. Neal'\n    });\n    \n    // AI Opponents\n    this.addVoiceProfile({\n      id: 'max_velocity',\n      name: 'Max Velocity',\n      gender: 'male',\n      pitch: 1.1,\n      rate: 1.2,\n      volume: 0.9,\n      accent: 'confident',\n      character: 'Max Velocity'\n    });\n    \n    this.addVoiceProfile({\n      id: 'turbo_tina',\n      name: 'Turbo Tina',\n      gender: 'female',\n      pitch: 1.2,\n      rate: 1.1,\n      volume: 0.9,\n      accent: 'energetic',\n      character: 'Turbo Tina'\n    });\n    \n    this.addVoiceProfile({\n      id: 'drift_king',\n      name: 'Drift King',\n      gender: 'male',\n      pitch: 0.9,\n      rate: 0.9,\n      volume: 1.0,\n      accent: 'smooth',\n      character: 'Drift King'\n    });\n    \n    // Race announcer\n    this.addVoiceProfile({\n      id: 'announcer',\n      name: 'Race Announcer',\n      gender: 'male',\n      pitch: 1.0,\n      rate: 1.3,\n      volume: 1.0,\n      accent: 'announcer',\n      character: 'Race Announcer'\n    });\n    \n    // Navigation system\n    this.addVoiceProfile({\n      id: 'nav_system',\n      name: 'Navigation System',\n      gender: 'female',\n      pitch: 1.1,\n      rate: 1.0,\n      volume: 0.8,\n      accent: 'robotic',\n      character: 'Navigation System'\n    });\n    \n    // Car Whisperer\n    this.addVoiceProfile({\n      id: 'car_whisperer',\n      name: 'Car Whisperer',\n      gender: 'neutral',\n      pitch: 0.8,\n      rate: 0.9,\n      volume: 0.7,\n      accent: 'mystical',\n      character: 'Car Whisperer'\n    });\n  }\n  \n  /**\n   * Add a new voice profile\n   */\n  public addVoiceProfile(profile: VoiceProfile): void {\n    if (this.voiceProfiles.has(profile.id)) {\n      console.warn(`Voice profile with ID ${profile.id} already exists. Overwriting.`);\n    }\n    \n    // Find a matching system voice if available\n    if (this.speechSynthesis && !profile.voiceURI) {\n      const matchingVoice = this.findMatchingSystemVoice(profile);\n      if (matchingVoice) {\n        profile.voiceURI = matchingVoice.voiceURI;\n      }\n    }\n    \n    this.voiceProfiles.set(profile.id, profile);\n  }\n  \n  /**\n   * Find a matching system voice based on profile preferences\n   */\n  private findMatchingSystemVoice(profile: VoiceProfile): SpeechSynthesisVoice | null {\n    if (!this.speechSynthesis || this.voices.length === 0) return null;\n    \n    // Filter voices by gender if specified\n    let matchingVoices = this.voices;\n    \n    if (profile.gender === 'male') {\n      matchingVoices = matchingVoices.filter(v => v.name.toLowerCase().includes('male') || \n                                               v.name.toLowerCase().includes('guy') || \n                                               v.name.toLowerCase().includes('man'));\n    } else if (profile.gender === 'female') {\n      matchingVoices = matchingVoices.filter(v => v.name.toLowerCase().includes('female') || \n                                               v.name.toLowerCase().includes('woman') || \n                                               v.name.toLowerCase().includes('girl'));\n    }\n    \n    // If we have accent info, try to match that too\n    if (profile.accent && matchingVoices.length > 1) {\n      const accentMatches = matchingVoices.filter(v => \n        v.name.toLowerCase().includes(profile.accent!.toLowerCase())\n      );\n      \n      if (accentMatches.length > 0) {\n        matchingVoices = accentMatches;\n      }\n    }\n    \n    // Return the first matching voice or null if none found\n    return matchingVoices.length > 0 ? matchingVoices[0] : null;\n  }\n  \n  /**\n   * Speak a message using TTS\n   */\n  public speak(message: TTSMessage): void {\n    if (!this.isEnabled) return;\n    \n    // Add to queue based on priority\n    this.addToQueue(message);\n    \n    // Start processing the queue if not already speaking\n    if (!this.isSpeaking) {\n      this.processQueue();\n    }\n  }\n  \n  /**\n   * Set the AudioSystem instance for synchronized voice playback\n   */\n  public setAudioSystem(audioSystem: any): void {\n    this.audioSystem = audioSystem;\n  }\n\n  /**\n   * Play character voice with proper synchronization\n   */\n  public async playCharacterVoice(character: string, dialogueId: string, text: string, onComplete?: () => void): Promise<void> {\n    if (this.audioSystem) {\n      try {\n        // Try to play the actual voice file first\n        await this.audioSystem.playVoiceDialogue(dialogueId, onComplete);\n        console.log(`Playing character voice: ${character} - \"${text}\"`);\n      } catch (error) {\n        console.warn(`Voice file not found for ${dialogueId}, falling back to TTS`);\n        // Fallback to TTS if voice file is not available\n        this.speak({\n          text,\n          voiceId: character,\n          priority: 'high',\n          category: 'dialogue',\n          onEnd: onComplete\n        });\n      }\n    } else {\n      // No AudioSystem available, use TTS\n      this.speak({\n        text,\n        voiceId: character,\n        priority: 'high',\n        category: 'dialogue',\n        onEnd: onComplete\n      });\n    }\n  }\n\n  /**\n   * Play synchronized story dialogue with character voices\n   */\n  public async playStoryDialogue(character: string, text: string, audioSrc?: string, onComplete?: () => void): Promise<void> {\n    if (audioSrc && this.audioSystem) {\n      // Create a temporary dialogue entry\n      const dialogueId = `temp_${character}_${Date.now()}`;\n      const dialogue = {\n        id: dialogueId,\n        character,\n        text,\n        audioSrc,\n        priority: 'high' as const\n      };\n\n      try {\n        await this.audioSystem.loadVoiceDialogue(dialogue);\n        await this.audioSystem.playVoiceDialogue(dialogueId, onComplete);\n        console.log(`Playing story dialogue: ${character} - \"${text}\"`);\n      } catch (error) {\n        console.warn(`Failed to play voice file for ${character}, falling back to TTS:`, error);\n        this.speak({\n          text,\n          voiceId: character,\n          priority: 'high',\n          category: 'dialogue',\n          onEnd: onComplete\n        });\n      }\n    } else {\n      // Fallback to TTS\n      this.speak({\n        text,\n        voiceId: character,\n        priority: 'high',\n        category: 'dialogue',\n        onEnd: onComplete\n      });\n    }\n  }\n\n  /**\n   * Add a message to the queue based on priority\n   */\n  private addToQueue(message: TTSMessage): void {\n    if (!message.text || message.text.trim() === '') {\n      console.warn('Empty message text, ignoring');\n      return;\n    }\n    \n    // Ensure the voice profile exists\n    if (!this.voiceProfiles.has(message.voiceId)) {\n      console.warn(`Voice profile not found for ID: ${message.voiceId}, using default`);\n      message.voiceId = 'announcer'; // Default fallback\n    }\n    \n    // Add to queue based on priority\n    if (message.priority === 'high') {\n      // High priority - add to front of queue\n      this.messageQueue.unshift(message);\n    } else if (message.priority === 'medium') {\n      // Medium priority - add after other high priority messages\n      const lastHighPriorityIndex = this.messageQueue.findIndex(m => m.priority !== 'high');\n      \n      if (lastHighPriorityIndex === -1) {\n        // No high priority messages, add to end\n        this.messageQueue.push(message);\n      } else {\n        // Insert after last high priority message\n        this.messageQueue.splice(lastHighPriorityIndex, 0, message);\n      }\n    } else {\n      // Low priority - add to end of queue\n      this.messageQueue.push(message);\n    }\n  }\n  \n  /**\n   * Get an available audio instance from the pool or create a new one\n   */\n  private getAudioInstance(src: string, options: any): Howl | null {\n    // Check if we already have an instance for this source that's not playing\n    if (this.audioInstanceMap.has(src)) {\n      const existingHowl = this.audioInstanceMap.get(src)!;\n      if (!existingHowl.playing()) {\n        return existingHowl;\n      }\n    }\n    \n    // Check if we've reached the maximum number of instances\n    if (this.activeAudioCount >= this.MAX_AUDIO_INSTANCES) {\n      // Find any non-playing instance to reuse\n      for (const howl of this.audioPool) {\n        if (!howl.playing()) {\n          howl.unload();\n          this.audioPool = this.audioPool.filter(h => h !== howl);\n          this.activeAudioCount--;\n          \n          // Also remove from the instance map\n          for (const [key, value] of this.audioInstanceMap.entries()) {\n            if (value === howl) {\n              this.audioInstanceMap.delete(key);\n              break;\n            }\n          }\n          break;\n        }\n      }\n      \n      // If we still have too many active instances, we can't create a new one\n      if (this.activeAudioCount >= this.MAX_AUDIO_INSTANCES) {\n        console.warn('All audio instances are in use. Skipping audio playback to prevent WebMediaPlayer error.');\n        return null;\n      }\n    }\n    \n    // Create a new instance\n    const newInstance = new Howl(options);\n    this.audioPool.push(newInstance);\n    this.audioInstanceMap.set(src, newInstance);\n    this.activeAudioCount++;\n    return newInstance;\n  }\n  \n  /**\n   * Release an audio instance back to the pool\n   */\n  private releaseAudioInstance(howl: Howl, src?: string): void {\n    // Stop the audio if it's playing\n    if (howl.playing()) {\n      howl.stop();\n    }\n    \n    // If we have too many instances, remove this one\n    if (this.audioPool.length > this.MAX_AUDIO_INSTANCES * 1.5) {\n      howl.unload();\n      this.audioPool = this.audioPool.filter(h => h !== howl);\n      this.activeAudioCount--;\n      \n      // Remove from the instance map if we have the source\n      if (src && this.audioInstanceMap.get(src) === howl) {\n        this.audioInstanceMap.delete(src);\n      }\n    }\n  }\n  \n  /**\n   * Process the message queue\n   */\n  private processQueue(): void {\n    if (!this.isEnabled || this.isSpeaking || this.messageQueue.length === 0) {\n      return;\n    }\n    \n    // Get the next message\n    const message = this.messageQueue.shift()!;\n    \n    // Get the voice profile\n    const profile = this.voiceProfiles.get(message.voiceId);\n    if (!profile) {\n      console.error(`Voice profile not found for ID: ${message.voiceId}`);\n      this.processQueue(); // Move to next message\n      return;\n    }\n    \n    // Determine audio source - message specific or from profile\n    const audioSource = message.audioSrc || profile.audioSrc;\n    \n    // If there's an audio file for this message, use it\n    if (audioSource) {\n      // Configure audio options\n      const audioOptions = {\n        src: [audioSource],\n        volume: profile.volume * this.volume,\n        html5: true, // Use HTML5 Audio to prevent WebMediaPlayer issues\n        onplay: () => {\n          this.isSpeaking = true;\n          if (message.onStart) message.onStart();\n        },\n        onend: () => {\n          this.isSpeaking = false;\n          if (this.currentHowl) {\n            this.releaseAudioInstance(this.currentHowl, audioSource);\n            this.currentHowl = null;\n          }\n          if (message.onEnd) message.onEnd();\n          \n          // Process next message after a short delay\n          setTimeout(() => this.processQueue(), 100);\n        },\n        onloaderror: (id: number, error: any) => {\n          console.error(`Error loading audio: ${error}`);\n          this.isSpeaking = false;\n          if (this.currentHowl) {\n            this.releaseAudioInstance(this.currentHowl, audioSource);\n            this.currentHowl = null;\n          }\n          \n          // Process next message\n          setTimeout(() => this.processQueue(), 100);\n        },\n        onplayerror: (id: number, error: any) => {\n          console.error(`Error playing audio: ${error}`);\n          this.isSpeaking = false;\n          if (this.currentHowl) {\n            this.releaseAudioInstance(this.currentHowl, audioSource);\n            this.currentHowl = null;\n          }\n          \n          // Process next message\n          setTimeout(() => this.processQueue(), 100);\n        }\n      };\n      \n      // Get an audio instance from the pool\n      const howl = this.getAudioInstance(audioSource, audioOptions);\n      if (howl) {\n        this.currentHowl = howl;\n        howl.play();\n      } else {\n        // If we couldn't get an audio instance, skip to next message\n        console.warn('Could not play audio due to WebMediaPlayer limitations. Skipping message.');\n        this.processQueue();\n      }\n    } else if (this.speechSynthesis) {\n      // Use browser's built-in TTS if no audio file is provided\n      const utterance = new SpeechSynthesisUtterance(message.text);\n      \n      // Set voice if available\n      if (profile.voiceURI) {\n        const systemVoice = this.voices.find(v => v.voiceURI === profile.voiceURI);\n        if (systemVoice) {\n          utterance.voice = systemVoice;\n        }\n      } else {\n        // Try to find a matching voice based on gender and accent\n        const matchingVoice = this.findMatchingSystemVoice(profile);\n        if (matchingVoice) {\n          utterance.voice = matchingVoice;\n        }\n      }\n      \n      // Set voice properties\n      utterance.pitch = profile.pitch;\n      utterance.rate = profile.rate;\n      utterance.volume = profile.volume * this.volume;\n      \n      // Set callbacks\n      utterance.onstart = () => {\n        this.isSpeaking = true;\n        if (message.onStart) message.onStart();\n      };\n      \n      utterance.onend = () => {\n        this.isSpeaking = false;\n        this.currentUtterance = null;\n        if (message.onEnd) message.onEnd();\n        \n        // Process next message\n        setTimeout(() => this.processQueue(), 100);\n      };\n      \n      utterance.onerror = (event) => {\n        console.error('TTS error:', event);\n        this.isSpeaking = false;\n        this.currentUtterance = null;\n        \n        // Process next message\n        setTimeout(() => this.processQueue(), 100);\n      };\n      \n      // Store current utterance\n      this.currentUtterance = utterance;\n      \n      // Speak\n      this.speechSynthesis.speak(utterance);\n    } else {\n      console.warn('No audio source provided and speech synthesis not supported.');\n      this.processQueue(); // Move to the next message\n    }\n  }\n  \n  /**\n   * Stop all speech and audio\n   */\n  public stop(): void {\n    if (this.speechSynthesis) {\n      this.speechSynthesis.cancel();\n    }\n    \n    // Stop and release all audio instances\n    this.audioPool.forEach(howl => {\n      if (howl.playing()) {\n        howl.stop();\n      }\n    });\n    \n    // Clear the current howl reference\n    this.currentHowl = null;\n    \n    // Reset state\n    this.isSpeaking = false;\n    this.currentUtterance = null;\n    this.messageQueue = []; // Clear the queue on stop\n  }\n  \n  /**\n   * Clean up audio resources\n   */\n  public cleanup(): void {\n    // Unload all audio instances to free up memory\n    this.audioPool.forEach(howl => {\n      howl.unload();\n    });\n    this.audioPool = [];\n    this.audioInstanceMap.clear();\n    this.activeAudioCount = 0;\n    \n    // Stop any ongoing speech\n    this.stop();\n  }\n  \n  /**\n   * Pause speech or audio\n   */\n  public pause(): void {\n    if (this.speechSynthesis) {\n      this.speechSynthesis.pause();\n    }\n    \n    if (this.currentHowl && this.currentHowl.playing()) {\n      this.currentHowl.pause();\n    }\n  }\n  \n  /**\n   * Resume speech\n   */\n  public resume(): void {\n    if (this.speechSynthesis) {\n      this.speechSynthesis.resume();\n    }\n    \n    if (this.currentHowl) {\n      this.currentHowl.play();\n    }\n  }\n  \n  /**\n   * Enable/disable TTS\n   */\n  public setEnabled(enabled: boolean): void {\n    this.isEnabled = enabled;\n    \n    if (!enabled) {\n      this.stop();\n      this.messageQueue = [];\n    }\n  }\n  \n  /**\n   * Set global TTS volume\n   */\n  public setVolume(volume: number): void {\n    this.volume = Math.max(0, Math.min(1, volume));\n    \n    // Update current utterance if speaking\n    if (this.currentUtterance) {\n      this.currentUtterance.volume = this.volume;\n    }\n    \n    // Update current Howl if playing\n    if (this.currentHowl) {\n      this.currentHowl.volume(this.volume);\n    }\n  }\n  \n  /**\n   * Get all voice profiles\n   */\n  public getAllVoiceProfiles(): VoiceProfile[] {\n    return Array.from(this.voiceProfiles.values());\n  }\n  \n  /**\n   * Get a voice profile by ID\n   */\n  public getVoiceProfile(id: string): VoiceProfile | undefined {\n    return this.voiceProfiles.get(id);\n  }\n  \n  /**\n   * Speak a dialogue line for a character\n   */\n  public speakDialogue(characterId: string, text: string, priority: 'low' | 'medium' | 'high' = 'medium'): void {\n    this.speak({\n      text,\n      voiceId: characterId,\n      priority,\n      category: 'dialogue'\n    });\n  }\n  \n  /**\n   * Announce a race event\n   */\n  public announceRaceEvent(text: string, priority: 'low' | 'medium' | 'high' = 'medium'): void {\n    this.speak({\n      text,\n      voiceId: 'announcer',\n      priority,\n      category: 'race_event'\n    });\n  }\n  \n  /**\n   * Speak a tutorial instruction\n   */\n  public speakTutorial(text: string, priority: 'low' | 'medium' | 'high' = 'medium'): void {\n    this.speak({\n      text,\n      voiceId: 'nav_system',\n      priority,\n      category: 'tutorial'\n    });\n  }\n  \n  /**\n   * Announce an achievement\n   */\n  public announceAchievement(text: string): void {\n    this.speak({\n      text,\n      voiceId: 'announcer',\n      priority: 'high',\n      category: 'achievement'\n    });\n  }\n}\n"], "mappings": ";AAAA;AACA;;AAEA,SAASA,IAAI,QAAgB,QAAQ;AAyBrC,OAAO,MAAMC,SAAS,CAAC;EAkBbC,WAAWA,CAAA,EAAG;IAAA,KAhBdC,aAAa;IAAA,KACbC,eAAe;IAAA,KACfC,MAAM,GAA2B,EAAE;IAAA,KACnCC,YAAY,GAAiB,EAAE;IAAA,KAC/BC,UAAU,GAAY,KAAK;IAAA,KAC3BC,SAAS,GAAY,IAAI;IAAA,KACzBC,MAAM,GAAW,GAAG;IAAA,KACpBC,gBAAgB,GAAoC,IAAI;IAAA,KACxDC,WAAW,GAAgB,IAAI;IAEvC;IAAA,KACiBC,mBAAmB,GAAG,CAAC;IAAA,KAChCC,SAAS,GAAW,EAAE;IAAA,KACtBC,gBAAgB,GAAW,CAAC;IAAA,KAC5BC,gBAAgB,GAAsB,IAAIC,GAAG,CAAC,CAAC;IAGrD,IAAI,CAACb,aAAa,GAAG,IAAIa,GAAG,CAAC,CAAC;;IAE9B;IACA,IAAI,OAAOC,MAAM,KAAK,WAAW,IAAIA,MAAM,CAACb,eAAe,EAAE;MAC3D,IAAI,CAACA,eAAe,GAAGa,MAAM,CAACb,eAAe;;MAE7C;MACA,IAAI,CAACc,UAAU,CAAC,CAAC;;MAEjB;MACA,IAAI,IAAI,CAACb,MAAM,CAACc,MAAM,KAAK,CAAC,EAAE;QAC5BC,UAAU,CAAC,MAAM,IAAI,CAACF,UAAU,CAAC,CAAC,EAAE,IAAI,CAAC;MAC3C;IACF,CAAC,MAAM;MACL,IAAI,CAACd,eAAe,GAAG,IAAI;MAC3BiB,OAAO,CAACC,IAAI,CAAC,8FAA8F,CAAC;IAC9G;;IAEA;IACA,IAAI,CAACC,uBAAuB,CAAC,CAAC;EAChC;EAEA,OAAcC,WAAWA,CAAA,EAAc;IACrC,IAAI,CAACvB,SAAS,CAACwB,QAAQ,EAAE;MACvBxB,SAAS,CAACwB,QAAQ,GAAG,IAAIxB,SAAS,CAAC,CAAC;IACtC;IACA,OAAOA,SAAS,CAACwB,QAAQ;EAC3B;;EAEA;AACF;AACA;EACUP,UAAUA,CAAA,EAAS;IACzB,IAAI,CAAC,IAAI,CAACd,eAAe,EAAE;;IAE3B;IACA,IAAI,CAACA,eAAe,CAACsB,eAAe,GAAG,MAAM;MAC3C,IAAI,CAACrB,MAAM,GAAG,IAAI,CAACD,eAAe,CAAEuB,SAAS,CAAC,CAAC;MAC/CN,OAAO,CAACO,GAAG,CAAC,UAAU,IAAI,CAACvB,MAAM,CAACc,MAAM,mCAAmC,CAAC;IAC9E,CAAC;;IAED;IACA,IAAI,CAACd,MAAM,GAAG,IAAI,CAACD,eAAe,CAACuB,SAAS,CAAC,CAAC;IAC9C,IAAI,IAAI,CAACtB,MAAM,CAACc,MAAM,GAAG,CAAC,EAAE;MACxBE,OAAO,CAACO,GAAG,CAAC,UAAU,IAAI,CAACvB,MAAM,CAACc,MAAM,mCAAmC,CAAC;IAChF;EACF;;EAEA;AACF;AACA;EACUI,uBAAuBA,CAAA,EAAS;IACtC;IACA,IAAI,CAACM,eAAe,CAAC;MACnBC,EAAE,EAAE,UAAU;MACdC,IAAI,EAAE,UAAU;MAChBC,MAAM,EAAE,MAAM;MACdC,KAAK,EAAE,GAAG;MACVC,IAAI,EAAE,GAAG;MACTzB,MAAM,EAAE,GAAG;MACX0B,MAAM,EAAE,QAAQ;MAChBC,SAAS,EAAE;IACb,CAAC,CAAC;;IAEF;IACA,IAAI,CAACP,eAAe,CAAC;MACnBC,EAAE,EAAE,SAAS;MACbC,IAAI,EAAE,UAAU;MAChBC,MAAM,EAAE,MAAM;MACdC,KAAK,EAAE,GAAG;MACVC,IAAI,EAAE,GAAG;MACTzB,MAAM,EAAE,GAAG;MACX0B,MAAM,EAAE,YAAY;MACpBC,SAAS,EAAE;IACb,CAAC,CAAC;;IAEF;IACA,IAAI,CAACP,eAAe,CAAC;MACnBC,EAAE,EAAE,cAAc;MAClBC,IAAI,EAAE,cAAc;MACpBC,MAAM,EAAE,MAAM;MACdC,KAAK,EAAE,GAAG;MACVC,IAAI,EAAE,GAAG;MACTzB,MAAM,EAAE,GAAG;MACX0B,MAAM,EAAE,WAAW;MACnBC,SAAS,EAAE;IACb,CAAC,CAAC;IAEF,IAAI,CAACP,eAAe,CAAC;MACnBC,EAAE,EAAE,YAAY;MAChBC,IAAI,EAAE,YAAY;MAClBC,MAAM,EAAE,QAAQ;MAChBC,KAAK,EAAE,GAAG;MACVC,IAAI,EAAE,GAAG;MACTzB,MAAM,EAAE,GAAG;MACX0B,MAAM,EAAE,WAAW;MACnBC,SAAS,EAAE;IACb,CAAC,CAAC;IAEF,IAAI,CAACP,eAAe,CAAC;MACnBC,EAAE,EAAE,YAAY;MAChBC,IAAI,EAAE,YAAY;MAClBC,MAAM,EAAE,MAAM;MACdC,KAAK,EAAE,GAAG;MACVC,IAAI,EAAE,GAAG;MACTzB,MAAM,EAAE,GAAG;MACX0B,MAAM,EAAE,QAAQ;MAChBC,SAAS,EAAE;IACb,CAAC,CAAC;;IAEF;IACA,IAAI,CAACP,eAAe,CAAC;MACnBC,EAAE,EAAE,WAAW;MACfC,IAAI,EAAE,gBAAgB;MACtBC,MAAM,EAAE,MAAM;MACdC,KAAK,EAAE,GAAG;MACVC,IAAI,EAAE,GAAG;MACTzB,MAAM,EAAE,GAAG;MACX0B,MAAM,EAAE,WAAW;MACnBC,SAAS,EAAE;IACb,CAAC,CAAC;;IAEF;IACA,IAAI,CAACP,eAAe,CAAC;MACnBC,EAAE,EAAE,YAAY;MAChBC,IAAI,EAAE,mBAAmB;MACzBC,MAAM,EAAE,QAAQ;MAChBC,KAAK,EAAE,GAAG;MACVC,IAAI,EAAE,GAAG;MACTzB,MAAM,EAAE,GAAG;MACX0B,MAAM,EAAE,SAAS;MACjBC,SAAS,EAAE;IACb,CAAC,CAAC;;IAEF;IACA,IAAI,CAACP,eAAe,CAAC;MACnBC,EAAE,EAAE,eAAe;MACnBC,IAAI,EAAE,eAAe;MACrBC,MAAM,EAAE,SAAS;MACjBC,KAAK,EAAE,GAAG;MACVC,IAAI,EAAE,GAAG;MACTzB,MAAM,EAAE,GAAG;MACX0B,MAAM,EAAE,UAAU;MAClBC,SAAS,EAAE;IACb,CAAC,CAAC;EACJ;;EAEA;AACF;AACA;EACSP,eAAeA,CAACQ,OAAqB,EAAQ;IAClD,IAAI,IAAI,CAAClC,aAAa,CAACmC,GAAG,CAACD,OAAO,CAACP,EAAE,CAAC,EAAE;MACtCT,OAAO,CAACC,IAAI,CAAC,yBAAyBe,OAAO,CAACP,EAAE,+BAA+B,CAAC;IAClF;;IAEA;IACA,IAAI,IAAI,CAAC1B,eAAe,IAAI,CAACiC,OAAO,CAACE,QAAQ,EAAE;MAC7C,MAAMC,aAAa,GAAG,IAAI,CAACC,uBAAuB,CAACJ,OAAO,CAAC;MAC3D,IAAIG,aAAa,EAAE;QACjBH,OAAO,CAACE,QAAQ,GAAGC,aAAa,CAACD,QAAQ;MAC3C;IACF;IAEA,IAAI,CAACpC,aAAa,CAACuC,GAAG,CAACL,OAAO,CAACP,EAAE,EAAEO,OAAO,CAAC;EAC7C;;EAEA;AACF;AACA;EACUI,uBAAuBA,CAACJ,OAAqB,EAA+B;IAClF,IAAI,CAAC,IAAI,CAACjC,eAAe,IAAI,IAAI,CAACC,MAAM,CAACc,MAAM,KAAK,CAAC,EAAE,OAAO,IAAI;;IAElE;IACA,IAAIwB,cAAc,GAAG,IAAI,CAACtC,MAAM;IAEhC,IAAIgC,OAAO,CAACL,MAAM,KAAK,MAAM,EAAE;MAC7BW,cAAc,GAAGA,cAAc,CAACC,MAAM,CAACC,CAAC,IAAIA,CAAC,CAACd,IAAI,CAACe,WAAW,CAAC,CAAC,CAACC,QAAQ,CAAC,MAAM,CAAC,IACxCF,CAAC,CAACd,IAAI,CAACe,WAAW,CAAC,CAAC,CAACC,QAAQ,CAAC,KAAK,CAAC,IACpCF,CAAC,CAACd,IAAI,CAACe,WAAW,CAAC,CAAC,CAACC,QAAQ,CAAC,KAAK,CAAC,CAAC;IAChF,CAAC,MAAM,IAAIV,OAAO,CAACL,MAAM,KAAK,QAAQ,EAAE;MACtCW,cAAc,GAAGA,cAAc,CAACC,MAAM,CAACC,CAAC,IAAIA,CAAC,CAACd,IAAI,CAACe,WAAW,CAAC,CAAC,CAACC,QAAQ,CAAC,QAAQ,CAAC,IAC1CF,CAAC,CAACd,IAAI,CAACe,WAAW,CAAC,CAAC,CAACC,QAAQ,CAAC,OAAO,CAAC,IACtCF,CAAC,CAACd,IAAI,CAACe,WAAW,CAAC,CAAC,CAACC,QAAQ,CAAC,MAAM,CAAC,CAAC;IACjF;;IAEA;IACA,IAAIV,OAAO,CAACF,MAAM,IAAIQ,cAAc,CAACxB,MAAM,GAAG,CAAC,EAAE;MAC/C,MAAM6B,aAAa,GAAGL,cAAc,CAACC,MAAM,CAACC,CAAC,IAC3CA,CAAC,CAACd,IAAI,CAACe,WAAW,CAAC,CAAC,CAACC,QAAQ,CAACV,OAAO,CAACF,MAAM,CAAEW,WAAW,CAAC,CAAC,CAC7D,CAAC;MAED,IAAIE,aAAa,CAAC7B,MAAM,GAAG,CAAC,EAAE;QAC5BwB,cAAc,GAAGK,aAAa;MAChC;IACF;;IAEA;IACA,OAAOL,cAAc,CAACxB,MAAM,GAAG,CAAC,GAAGwB,cAAc,CAAC,CAAC,CAAC,GAAG,IAAI;EAC7D;;EAEA;AACF;AACA;EACSM,KAAKA,CAACC,OAAmB,EAAQ;IACtC,IAAI,CAAC,IAAI,CAAC1C,SAAS,EAAE;;IAErB;IACA,IAAI,CAAC2C,UAAU,CAACD,OAAO,CAAC;;IAExB;IACA,IAAI,CAAC,IAAI,CAAC3C,UAAU,EAAE;MACpB,IAAI,CAAC6C,YAAY,CAAC,CAAC;IACrB;EACF;;EAEA;AACF;AACA;EACSC,cAAcA,CAACC,WAAgB,EAAQ;IAC5C,IAAI,CAACA,WAAW,GAAGA,WAAW;EAChC;;EAEA;AACF;AACA;EACE,MAAaC,kBAAkBA,CAACnB,SAAiB,EAAEoB,UAAkB,EAAEC,IAAY,EAAEC,UAAuB,EAAiB;IAC3H,IAAI,IAAI,CAACJ,WAAW,EAAE;MACpB,IAAI;QACF;QACA,MAAM,IAAI,CAACA,WAAW,CAACK,iBAAiB,CAACH,UAAU,EAAEE,UAAU,CAAC;QAChErC,OAAO,CAACO,GAAG,CAAC,4BAA4BQ,SAAS,OAAOqB,IAAI,GAAG,CAAC;MAClE,CAAC,CAAC,OAAOG,KAAK,EAAE;QACdvC,OAAO,CAACC,IAAI,CAAC,4BAA4BkC,UAAU,uBAAuB,CAAC;QAC3E;QACA,IAAI,CAACP,KAAK,CAAC;UACTQ,IAAI;UACJI,OAAO,EAAEzB,SAAS;UAClB0B,QAAQ,EAAE,MAAM;UAChBC,QAAQ,EAAE,UAAU;UACpBC,KAAK,EAAEN;QACT,CAAC,CAAC;MACJ;IACF,CAAC,MAAM;MACL;MACA,IAAI,CAACT,KAAK,CAAC;QACTQ,IAAI;QACJI,OAAO,EAAEzB,SAAS;QAClB0B,QAAQ,EAAE,MAAM;QAChBC,QAAQ,EAAE,UAAU;QACpBC,KAAK,EAAEN;MACT,CAAC,CAAC;IACJ;EACF;;EAEA;AACF;AACA;EACE,MAAaO,iBAAiBA,CAAC7B,SAAiB,EAAEqB,IAAY,EAAES,QAAiB,EAAER,UAAuB,EAAiB;IACzH,IAAIQ,QAAQ,IAAI,IAAI,CAACZ,WAAW,EAAE;MAChC;MACA,MAAME,UAAU,GAAG,QAAQpB,SAAS,IAAI+B,IAAI,CAACC,GAAG,CAAC,CAAC,EAAE;MACpD,MAAMC,QAAQ,GAAG;QACfvC,EAAE,EAAE0B,UAAU;QACdpB,SAAS;QACTqB,IAAI;QACJS,QAAQ;QACRJ,QAAQ,EAAE;MACZ,CAAC;MAED,IAAI;QACF,MAAM,IAAI,CAACR,WAAW,CAACgB,iBAAiB,CAACD,QAAQ,CAAC;QAClD,MAAM,IAAI,CAACf,WAAW,CAACK,iBAAiB,CAACH,UAAU,EAAEE,UAAU,CAAC;QAChErC,OAAO,CAACO,GAAG,CAAC,2BAA2BQ,SAAS,OAAOqB,IAAI,GAAG,CAAC;MACjE,CAAC,CAAC,OAAOG,KAAK,EAAE;QACdvC,OAAO,CAACC,IAAI,CAAC,iCAAiCc,SAAS,wBAAwB,EAAEwB,KAAK,CAAC;QACvF,IAAI,CAACX,KAAK,CAAC;UACTQ,IAAI;UACJI,OAAO,EAAEzB,SAAS;UAClB0B,QAAQ,EAAE,MAAM;UAChBC,QAAQ,EAAE,UAAU;UACpBC,KAAK,EAAEN;QACT,CAAC,CAAC;MACJ;IACF,CAAC,MAAM;MACL;MACA,IAAI,CAACT,KAAK,CAAC;QACTQ,IAAI;QACJI,OAAO,EAAEzB,SAAS;QAClB0B,QAAQ,EAAE,MAAM;QAChBC,QAAQ,EAAE,UAAU;QACpBC,KAAK,EAAEN;MACT,CAAC,CAAC;IACJ;EACF;;EAEA;AACF;AACA;EACUP,UAAUA,CAACD,OAAmB,EAAQ;IAC5C,IAAI,CAACA,OAAO,CAACO,IAAI,IAAIP,OAAO,CAACO,IAAI,CAACc,IAAI,CAAC,CAAC,KAAK,EAAE,EAAE;MAC/ClD,OAAO,CAACC,IAAI,CAAC,8BAA8B,CAAC;MAC5C;IACF;;IAEA;IACA,IAAI,CAAC,IAAI,CAACnB,aAAa,CAACmC,GAAG,CAACY,OAAO,CAACW,OAAO,CAAC,EAAE;MAC5CxC,OAAO,CAACC,IAAI,CAAC,mCAAmC4B,OAAO,CAACW,OAAO,iBAAiB,CAAC;MACjFX,OAAO,CAACW,OAAO,GAAG,WAAW,CAAC,CAAC;IACjC;;IAEA;IACA,IAAIX,OAAO,CAACY,QAAQ,KAAK,MAAM,EAAE;MAC/B;MACA,IAAI,CAACxD,YAAY,CAACkE,OAAO,CAACtB,OAAO,CAAC;IACpC,CAAC,MAAM,IAAIA,OAAO,CAACY,QAAQ,KAAK,QAAQ,EAAE;MACxC;MACA,MAAMW,qBAAqB,GAAG,IAAI,CAACnE,YAAY,CAACoE,SAAS,CAACC,CAAC,IAAIA,CAAC,CAACb,QAAQ,KAAK,MAAM,CAAC;MAErF,IAAIW,qBAAqB,KAAK,CAAC,CAAC,EAAE;QAChC;QACA,IAAI,CAACnE,YAAY,CAACsE,IAAI,CAAC1B,OAAO,CAAC;MACjC,CAAC,MAAM;QACL;QACA,IAAI,CAAC5C,YAAY,CAACuE,MAAM,CAACJ,qBAAqB,EAAE,CAAC,EAAEvB,OAAO,CAAC;MAC7D;IACF,CAAC,MAAM;MACL;MACA,IAAI,CAAC5C,YAAY,CAACsE,IAAI,CAAC1B,OAAO,CAAC;IACjC;EACF;;EAEA;AACF;AACA;EACU4B,gBAAgBA,CAACC,GAAW,EAAEC,OAAY,EAAe;IAC/D;IACA,IAAI,IAAI,CAACjE,gBAAgB,CAACuB,GAAG,CAACyC,GAAG,CAAC,EAAE;MAClC,MAAME,YAAY,GAAG,IAAI,CAAClE,gBAAgB,CAACmE,GAAG,CAACH,GAAG,CAAE;MACpD,IAAI,CAACE,YAAY,CAACE,OAAO,CAAC,CAAC,EAAE;QAC3B,OAAOF,YAAY;MACrB;IACF;;IAEA;IACA,IAAI,IAAI,CAACnE,gBAAgB,IAAI,IAAI,CAACF,mBAAmB,EAAE;MACrD;MACA,KAAK,MAAMwE,IAAI,IAAI,IAAI,CAACvE,SAAS,EAAE;QACjC,IAAI,CAACuE,IAAI,CAACD,OAAO,CAAC,CAAC,EAAE;UACnBC,IAAI,CAACC,MAAM,CAAC,CAAC;UACb,IAAI,CAACxE,SAAS,GAAG,IAAI,CAACA,SAAS,CAAC+B,MAAM,CAAC0C,CAAC,IAAIA,CAAC,KAAKF,IAAI,CAAC;UACvD,IAAI,CAACtE,gBAAgB,EAAE;;UAEvB;UACA,KAAK,MAAM,CAACyE,GAAG,EAAEC,KAAK,CAAC,IAAI,IAAI,CAACzE,gBAAgB,CAAC0E,OAAO,CAAC,CAAC,EAAE;YAC1D,IAAID,KAAK,KAAKJ,IAAI,EAAE;cAClB,IAAI,CAACrE,gBAAgB,CAAC2E,MAAM,CAACH,GAAG,CAAC;cACjC;YACF;UACF;UACA;QACF;MACF;;MAEA;MACA,IAAI,IAAI,CAACzE,gBAAgB,IAAI,IAAI,CAACF,mBAAmB,EAAE;QACrDS,OAAO,CAACC,IAAI,CAAC,0FAA0F,CAAC;QACxG,OAAO,IAAI;MACb;IACF;;IAEA;IACA,MAAMqE,WAAW,GAAG,IAAI3F,IAAI,CAACgF,OAAO,CAAC;IACrC,IAAI,CAACnE,SAAS,CAAC+D,IAAI,CAACe,WAAW,CAAC;IAChC,IAAI,CAAC5E,gBAAgB,CAAC2B,GAAG,CAACqC,GAAG,EAAEY,WAAW,CAAC;IAC3C,IAAI,CAAC7E,gBAAgB,EAAE;IACvB,OAAO6E,WAAW;EACpB;;EAEA;AACF;AACA;EACUC,oBAAoBA,CAACR,IAAU,EAAEL,GAAY,EAAQ;IAC3D;IACA,IAAIK,IAAI,CAACD,OAAO,CAAC,CAAC,EAAE;MAClBC,IAAI,CAACS,IAAI,CAAC,CAAC;IACb;;IAEA;IACA,IAAI,IAAI,CAAChF,SAAS,CAACM,MAAM,GAAG,IAAI,CAACP,mBAAmB,GAAG,GAAG,EAAE;MAC1DwE,IAAI,CAACC,MAAM,CAAC,CAAC;MACb,IAAI,CAACxE,SAAS,GAAG,IAAI,CAACA,SAAS,CAAC+B,MAAM,CAAC0C,CAAC,IAAIA,CAAC,KAAKF,IAAI,CAAC;MACvD,IAAI,CAACtE,gBAAgB,EAAE;;MAEvB;MACA,IAAIiE,GAAG,IAAI,IAAI,CAAChE,gBAAgB,CAACmE,GAAG,CAACH,GAAG,CAAC,KAAKK,IAAI,EAAE;QAClD,IAAI,CAACrE,gBAAgB,CAAC2E,MAAM,CAACX,GAAG,CAAC;MACnC;IACF;EACF;;EAEA;AACF;AACA;EACU3B,YAAYA,CAAA,EAAS;IAC3B,IAAI,CAAC,IAAI,CAAC5C,SAAS,IAAI,IAAI,CAACD,UAAU,IAAI,IAAI,CAACD,YAAY,CAACa,MAAM,KAAK,CAAC,EAAE;MACxE;IACF;;IAEA;IACA,MAAM+B,OAAO,GAAG,IAAI,CAAC5C,YAAY,CAACwF,KAAK,CAAC,CAAE;;IAE1C;IACA,MAAMzD,OAAO,GAAG,IAAI,CAAClC,aAAa,CAAC+E,GAAG,CAAChC,OAAO,CAACW,OAAO,CAAC;IACvD,IAAI,CAACxB,OAAO,EAAE;MACZhB,OAAO,CAACuC,KAAK,CAAC,mCAAmCV,OAAO,CAACW,OAAO,EAAE,CAAC;MACnE,IAAI,CAACT,YAAY,CAAC,CAAC,CAAC,CAAC;MACrB;IACF;;IAEA;IACA,MAAM2C,WAAW,GAAG7C,OAAO,CAACgB,QAAQ,IAAI7B,OAAO,CAAC6B,QAAQ;;IAExD;IACA,IAAI6B,WAAW,EAAE;MACf;MACA,MAAMC,YAAY,GAAG;QACnBjB,GAAG,EAAE,CAACgB,WAAW,CAAC;QAClBtF,MAAM,EAAE4B,OAAO,CAAC5B,MAAM,GAAG,IAAI,CAACA,MAAM;QACpCwF,KAAK,EAAE,IAAI;QAAE;QACbC,MAAM,EAAEA,CAAA,KAAM;UACZ,IAAI,CAAC3F,UAAU,GAAG,IAAI;UACtB,IAAI2C,OAAO,CAACiD,OAAO,EAAEjD,OAAO,CAACiD,OAAO,CAAC,CAAC;QACxC,CAAC;QACDC,KAAK,EAAEA,CAAA,KAAM;UACX,IAAI,CAAC7F,UAAU,GAAG,KAAK;UACvB,IAAI,IAAI,CAACI,WAAW,EAAE;YACpB,IAAI,CAACiF,oBAAoB,CAAC,IAAI,CAACjF,WAAW,EAAEoF,WAAW,CAAC;YACxD,IAAI,CAACpF,WAAW,GAAG,IAAI;UACzB;UACA,IAAIuC,OAAO,CAACc,KAAK,EAAEd,OAAO,CAACc,KAAK,CAAC,CAAC;;UAElC;UACA5C,UAAU,CAAC,MAAM,IAAI,CAACgC,YAAY,CAAC,CAAC,EAAE,GAAG,CAAC;QAC5C,CAAC;QACDiD,WAAW,EAAEA,CAACvE,EAAU,EAAE8B,KAAU,KAAK;UACvCvC,OAAO,CAACuC,KAAK,CAAC,wBAAwBA,KAAK,EAAE,CAAC;UAC9C,IAAI,CAACrD,UAAU,GAAG,KAAK;UACvB,IAAI,IAAI,CAACI,WAAW,EAAE;YACpB,IAAI,CAACiF,oBAAoB,CAAC,IAAI,CAACjF,WAAW,EAAEoF,WAAW,CAAC;YACxD,IAAI,CAACpF,WAAW,GAAG,IAAI;UACzB;;UAEA;UACAS,UAAU,CAAC,MAAM,IAAI,CAACgC,YAAY,CAAC,CAAC,EAAE,GAAG,CAAC;QAC5C,CAAC;QACDkD,WAAW,EAAEA,CAACxE,EAAU,EAAE8B,KAAU,KAAK;UACvCvC,OAAO,CAACuC,KAAK,CAAC,wBAAwBA,KAAK,EAAE,CAAC;UAC9C,IAAI,CAACrD,UAAU,GAAG,KAAK;UACvB,IAAI,IAAI,CAACI,WAAW,EAAE;YACpB,IAAI,CAACiF,oBAAoB,CAAC,IAAI,CAACjF,WAAW,EAAEoF,WAAW,CAAC;YACxD,IAAI,CAACpF,WAAW,GAAG,IAAI;UACzB;;UAEA;UACAS,UAAU,CAAC,MAAM,IAAI,CAACgC,YAAY,CAAC,CAAC,EAAE,GAAG,CAAC;QAC5C;MACF,CAAC;;MAED;MACA,MAAMgC,IAAI,GAAG,IAAI,CAACN,gBAAgB,CAACiB,WAAW,EAAEC,YAAY,CAAC;MAC7D,IAAIZ,IAAI,EAAE;QACR,IAAI,CAACzE,WAAW,GAAGyE,IAAI;QACvBA,IAAI,CAACmB,IAAI,CAAC,CAAC;MACb,CAAC,MAAM;QACL;QACAlF,OAAO,CAACC,IAAI,CAAC,2EAA2E,CAAC;QACzF,IAAI,CAAC8B,YAAY,CAAC,CAAC;MACrB;IACF,CAAC,MAAM,IAAI,IAAI,CAAChD,eAAe,EAAE;MAC/B;MACA,MAAMoG,SAAS,GAAG,IAAIC,wBAAwB,CAACvD,OAAO,CAACO,IAAI,CAAC;;MAE5D;MACA,IAAIpB,OAAO,CAACE,QAAQ,EAAE;QACpB,MAAMmE,WAAW,GAAG,IAAI,CAACrG,MAAM,CAACsG,IAAI,CAAC9D,CAAC,IAAIA,CAAC,CAACN,QAAQ,KAAKF,OAAO,CAACE,QAAQ,CAAC;QAC1E,IAAImE,WAAW,EAAE;UACfF,SAAS,CAACI,KAAK,GAAGF,WAAW;QAC/B;MACF,CAAC,MAAM;QACL;QACA,MAAMlE,aAAa,GAAG,IAAI,CAACC,uBAAuB,CAACJ,OAAO,CAAC;QAC3D,IAAIG,aAAa,EAAE;UACjBgE,SAAS,CAACI,KAAK,GAAGpE,aAAa;QACjC;MACF;;MAEA;MACAgE,SAAS,CAACvE,KAAK,GAAGI,OAAO,CAACJ,KAAK;MAC/BuE,SAAS,CAACtE,IAAI,GAAGG,OAAO,CAACH,IAAI;MAC7BsE,SAAS,CAAC/F,MAAM,GAAG4B,OAAO,CAAC5B,MAAM,GAAG,IAAI,CAACA,MAAM;;MAE/C;MACA+F,SAAS,CAACK,OAAO,GAAG,MAAM;QACxB,IAAI,CAACtG,UAAU,GAAG,IAAI;QACtB,IAAI2C,OAAO,CAACiD,OAAO,EAAEjD,OAAO,CAACiD,OAAO,CAAC,CAAC;MACxC,CAAC;MAEDK,SAAS,CAACJ,KAAK,GAAG,MAAM;QACtB,IAAI,CAAC7F,UAAU,GAAG,KAAK;QACvB,IAAI,CAACG,gBAAgB,GAAG,IAAI;QAC5B,IAAIwC,OAAO,CAACc,KAAK,EAAEd,OAAO,CAACc,KAAK,CAAC,CAAC;;QAElC;QACA5C,UAAU,CAAC,MAAM,IAAI,CAACgC,YAAY,CAAC,CAAC,EAAE,GAAG,CAAC;MAC5C,CAAC;MAEDoD,SAAS,CAACM,OAAO,GAAIC,KAAK,IAAK;QAC7B1F,OAAO,CAACuC,KAAK,CAAC,YAAY,EAAEmD,KAAK,CAAC;QAClC,IAAI,CAACxG,UAAU,GAAG,KAAK;QACvB,IAAI,CAACG,gBAAgB,GAAG,IAAI;;QAE5B;QACAU,UAAU,CAAC,MAAM,IAAI,CAACgC,YAAY,CAAC,CAAC,EAAE,GAAG,CAAC;MAC5C,CAAC;;MAED;MACA,IAAI,CAAC1C,gBAAgB,GAAG8F,SAAS;;MAEjC;MACA,IAAI,CAACpG,eAAe,CAAC6C,KAAK,CAACuD,SAAS,CAAC;IACvC,CAAC,MAAM;MACLnF,OAAO,CAACC,IAAI,CAAC,8DAA8D,CAAC;MAC5E,IAAI,CAAC8B,YAAY,CAAC,CAAC,CAAC,CAAC;IACvB;EACF;;EAEA;AACF;AACA;EACSyC,IAAIA,CAAA,EAAS;IAClB,IAAI,IAAI,CAACzF,eAAe,EAAE;MACxB,IAAI,CAACA,eAAe,CAAC4G,MAAM,CAAC,CAAC;IAC/B;;IAEA;IACA,IAAI,CAACnG,SAAS,CAACoG,OAAO,CAAC7B,IAAI,IAAI;MAC7B,IAAIA,IAAI,CAACD,OAAO,CAAC,CAAC,EAAE;QAClBC,IAAI,CAACS,IAAI,CAAC,CAAC;MACb;IACF,CAAC,CAAC;;IAEF;IACA,IAAI,CAAClF,WAAW,GAAG,IAAI;;IAEvB;IACA,IAAI,CAACJ,UAAU,GAAG,KAAK;IACvB,IAAI,CAACG,gBAAgB,GAAG,IAAI;IAC5B,IAAI,CAACJ,YAAY,GAAG,EAAE,CAAC,CAAC;EAC1B;;EAEA;AACF;AACA;EACS4G,OAAOA,CAAA,EAAS;IACrB;IACA,IAAI,CAACrG,SAAS,CAACoG,OAAO,CAAC7B,IAAI,IAAI;MAC7BA,IAAI,CAACC,MAAM,CAAC,CAAC;IACf,CAAC,CAAC;IACF,IAAI,CAACxE,SAAS,GAAG,EAAE;IACnB,IAAI,CAACE,gBAAgB,CAACoG,KAAK,CAAC,CAAC;IAC7B,IAAI,CAACrG,gBAAgB,GAAG,CAAC;;IAEzB;IACA,IAAI,CAAC+E,IAAI,CAAC,CAAC;EACb;;EAEA;AACF;AACA;EACSuB,KAAKA,CAAA,EAAS;IACnB,IAAI,IAAI,CAAChH,eAAe,EAAE;MACxB,IAAI,CAACA,eAAe,CAACgH,KAAK,CAAC,CAAC;IAC9B;IAEA,IAAI,IAAI,CAACzG,WAAW,IAAI,IAAI,CAACA,WAAW,CAACwE,OAAO,CAAC,CAAC,EAAE;MAClD,IAAI,CAACxE,WAAW,CAACyG,KAAK,CAAC,CAAC;IAC1B;EACF;;EAEA;AACF;AACA;EACSC,MAAMA,CAAA,EAAS;IACpB,IAAI,IAAI,CAACjH,eAAe,EAAE;MACxB,IAAI,CAACA,eAAe,CAACiH,MAAM,CAAC,CAAC;IAC/B;IAEA,IAAI,IAAI,CAAC1G,WAAW,EAAE;MACpB,IAAI,CAACA,WAAW,CAAC4F,IAAI,CAAC,CAAC;IACzB;EACF;;EAEA;AACF;AACA;EACSe,UAAUA,CAACC,OAAgB,EAAQ;IACxC,IAAI,CAAC/G,SAAS,GAAG+G,OAAO;IAExB,IAAI,CAACA,OAAO,EAAE;MACZ,IAAI,CAAC1B,IAAI,CAAC,CAAC;MACX,IAAI,CAACvF,YAAY,GAAG,EAAE;IACxB;EACF;;EAEA;AACF;AACA;EACSkH,SAASA,CAAC/G,MAAc,EAAQ;IACrC,IAAI,CAACA,MAAM,GAAGgH,IAAI,CAACC,GAAG,CAAC,CAAC,EAAED,IAAI,CAACE,GAAG,CAAC,CAAC,EAAElH,MAAM,CAAC,CAAC;;IAE9C;IACA,IAAI,IAAI,CAACC,gBAAgB,EAAE;MACzB,IAAI,CAACA,gBAAgB,CAACD,MAAM,GAAG,IAAI,CAACA,MAAM;IAC5C;;IAEA;IACA,IAAI,IAAI,CAACE,WAAW,EAAE;MACpB,IAAI,CAACA,WAAW,CAACF,MAAM,CAAC,IAAI,CAACA,MAAM,CAAC;IACtC;EACF;;EAEA;AACF;AACA;EACSmH,mBAAmBA,CAAA,EAAmB;IAC3C,OAAOC,KAAK,CAACC,IAAI,CAAC,IAAI,CAAC3H,aAAa,CAAC4H,MAAM,CAAC,CAAC,CAAC;EAChD;;EAEA;AACF;AACA;EACSC,eAAeA,CAAClG,EAAU,EAA4B;IAC3D,OAAO,IAAI,CAAC3B,aAAa,CAAC+E,GAAG,CAACpD,EAAE,CAAC;EACnC;;EAEA;AACF;AACA;EACSmG,aAAaA,CAACC,WAAmB,EAAEzE,IAAY,EAAEK,QAAmC,GAAG,QAAQ,EAAQ;IAC5G,IAAI,CAACb,KAAK,CAAC;MACTQ,IAAI;MACJI,OAAO,EAAEqE,WAAW;MACpBpE,QAAQ;MACRC,QAAQ,EAAE;IACZ,CAAC,CAAC;EACJ;;EAEA;AACF;AACA;EACSoE,iBAAiBA,CAAC1E,IAAY,EAAEK,QAAmC,GAAG,QAAQ,EAAQ;IAC3F,IAAI,CAACb,KAAK,CAAC;MACTQ,IAAI;MACJI,OAAO,EAAE,WAAW;MACpBC,QAAQ;MACRC,QAAQ,EAAE;IACZ,CAAC,CAAC;EACJ;;EAEA;AACF;AACA;EACSqE,aAAaA,CAAC3E,IAAY,EAAEK,QAAmC,GAAG,QAAQ,EAAQ;IACvF,IAAI,CAACb,KAAK,CAAC;MACTQ,IAAI;MACJI,OAAO,EAAE,YAAY;MACrBC,QAAQ;MACRC,QAAQ,EAAE;IACZ,CAAC,CAAC;EACJ;;EAEA;AACF;AACA;EACSsE,mBAAmBA,CAAC5E,IAAY,EAAQ;IAC7C,IAAI,CAACR,KAAK,CAAC;MACTQ,IAAI;MACJI,OAAO,EAAE,WAAW;MACpBC,QAAQ,EAAE,MAAM;MAChBC,QAAQ,EAAE;IACZ,CAAC,CAAC;EACJ;AACF;AAACuE,UAAA,GAvsBYrI,SAAS;AAATA,SAAS,CACLwB,QAAQ", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}