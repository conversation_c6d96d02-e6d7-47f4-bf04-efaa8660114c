{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Apps_Creations\\\\SiahMan_Racing4Speed_WebGame\\\\siah-man-racing\\\\src\\\\components\\\\AudioController.tsx\",\n  _s = $RefreshSig$();\nimport React, { useEffect, useState, useCallback } from 'react';\nimport { AudioSystem } from '../game/AudioSystem';\nimport { useSelector } from 'react-redux';\nimport styled from 'styled-components';\n\n// Styled components\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst AudioControlsContainer = styled.div`\n  position: fixed;\n  bottom: 15px;\n  right: 15px;\n  display: flex;\n  gap: 10px;\n  z-index: 100;\n`;\n_c = AudioControlsContainer;\nconst AudioButton = styled.button`\n  width: 40px;\n  height: 40px;\n  border-radius: 50%;\n  background: ${props => props.$active ? 'linear-gradient(145deg, #0088ff, #00aaff)' : 'rgba(0, 0, 0, 0.5)'};\n  border: 2px solid ${props => props.$active ? '#ffffff' : 'rgba(255, 255, 255, 0.3)'};\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  cursor: pointer;\n  transition: all 0.3s ease;\n  \n  &:hover {\n    transform: scale(1.1);\n    box-shadow: 0 0 15px rgba(0, 170, 255, 0.5);\n  }\n  \n  svg {\n    width: 20px;\n    height: 20px;\n    fill: white;\n  }\n`;\n_c2 = AudioButton;\nconst MusicIcon = () => /*#__PURE__*/_jsxDEV(\"svg\", {\n  viewBox: \"0 0 24 24\",\n  xmlns: \"http://www.w3.org/2000/svg\",\n  children: [/*#__PURE__*/_jsxDEV(\"path\", {\n    d: \"M9 18V5l12-2v13\"\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 46,\n    columnNumber: 5\n  }, this), /*#__PURE__*/_jsxDEV(\"circle\", {\n    cx: \"6\",\n    cy: \"18\",\n    r: \"3\"\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 47,\n    columnNumber: 5\n  }, this), /*#__PURE__*/_jsxDEV(\"circle\", {\n    cx: \"18\",\n    cy: \"16\",\n    r: \"3\"\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 48,\n    columnNumber: 5\n  }, this)]\n}, void 0, true, {\n  fileName: _jsxFileName,\n  lineNumber: 45,\n  columnNumber: 3\n}, this);\n_c3 = MusicIcon;\nconst MusicMuteIcon = () => /*#__PURE__*/_jsxDEV(\"svg\", {\n  viewBox: \"0 0 24 24\",\n  xmlns: \"http://www.w3.org/2000/svg\",\n  children: [/*#__PURE__*/_jsxDEV(\"path\", {\n    d: \"M9 18V5l12-2v13\"\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 54,\n    columnNumber: 5\n  }, this), /*#__PURE__*/_jsxDEV(\"circle\", {\n    cx: \"6\",\n    cy: \"18\",\n    r: \"3\"\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 55,\n    columnNumber: 5\n  }, this), /*#__PURE__*/_jsxDEV(\"circle\", {\n    cx: \"18\",\n    cy: \"16\",\n    r: \"3\"\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 56,\n    columnNumber: 5\n  }, this), /*#__PURE__*/_jsxDEV(\"line\", {\n    x1: \"3\",\n    y1: \"3\",\n    x2: \"21\",\n    y2: \"21\",\n    stroke: \"white\",\n    strokeWidth: \"2\"\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 57,\n    columnNumber: 5\n  }, this)]\n}, void 0, true, {\n  fileName: _jsxFileName,\n  lineNumber: 53,\n  columnNumber: 3\n}, this);\n_c4 = MusicMuteIcon;\nconst SoundIcon = () => /*#__PURE__*/_jsxDEV(\"svg\", {\n  viewBox: \"0 0 24 24\",\n  xmlns: \"http://www.w3.org/2000/svg\",\n  children: [/*#__PURE__*/_jsxDEV(\"path\", {\n    d: \"M12 4L8 8H4v8h4l4 4V4z\"\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 63,\n    columnNumber: 5\n  }, this), /*#__PURE__*/_jsxDEV(\"path\", {\n    d: \"M16 8a5 5 0 0 1 0 8M19 5a10 10 0 0 1 0 14\"\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 64,\n    columnNumber: 5\n  }, this)]\n}, void 0, true, {\n  fileName: _jsxFileName,\n  lineNumber: 62,\n  columnNumber: 3\n}, this);\n_c5 = SoundIcon;\nconst SoundMuteIcon = () => /*#__PURE__*/_jsxDEV(\"svg\", {\n  viewBox: \"0 0 24 24\",\n  xmlns: \"http://www.w3.org/2000/svg\",\n  children: [/*#__PURE__*/_jsxDEV(\"path\", {\n    d: \"M12 4L8 8H4v8h4l4 4V4z\"\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 70,\n    columnNumber: 5\n  }, this), /*#__PURE__*/_jsxDEV(\"line\", {\n    x1: \"17\",\n    y1: \"7\",\n    x2: \"17\",\n    y2: \"17\",\n    stroke: \"white\",\n    strokeWidth: \"2\"\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 71,\n    columnNumber: 5\n  }, this), /*#__PURE__*/_jsxDEV(\"line\", {\n    x1: \"21\",\n    y1: \"11\",\n    x2: \"21\",\n    y2: \"13\",\n    stroke: \"white\",\n    strokeWidth: \"2\"\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 72,\n    columnNumber: 5\n  }, this), /*#__PURE__*/_jsxDEV(\"line\", {\n    x1: \"3\",\n    y1: \"3\",\n    x2: \"21\",\n    y2: \"21\",\n    stroke: \"white\",\n    strokeWidth: \"2\"\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 73,\n    columnNumber: 5\n  }, this)]\n}, void 0, true, {\n  fileName: _jsxFileName,\n  lineNumber: 69,\n  columnNumber: 3\n}, this);\n_c6 = SoundMuteIcon;\nconst AudioController = () => {\n  _s();\n  const [isMusicMuted, setIsMusicMuted] = useState(false);\n  const [isSfxMuted, setIsSfxMuted] = useState(false);\n  const audioSystem = AudioSystem.getInstance();\n  const {\n    currentScreen\n  } = useSelector(state => state.game);\n\n  // Load sound assets\n  useEffect(() => {\n    const loadRaceAudio = async () => {\n      try {\n        // Load music tracks\n        await audioSystem.loadMusicTrack({\n          id: 'menu_music',\n          name: 'Menu Theme',\n          src: '/assets/sounds/music/menu_theme.mp3',\n          intensity: 'low',\n          loop: true,\n          volume: 0.6,\n          category: 'menu'\n        });\n        await audioSystem.loadMusicTrack({\n          id: 'race_music_intro',\n          name: 'Race Intro',\n          src: '/assets/sounds/music/race_intro.mp3',\n          intensity: 'medium',\n          loop: false,\n          volume: 0.7,\n          category: 'race'\n        });\n        await audioSystem.loadMusicTrack({\n          id: 'race_music',\n          name: 'Race Theme',\n          src: '/assets/sounds/music/race_theme.mp3',\n          intensity: 'high',\n          loop: true,\n          volume: 0.7,\n          category: 'race'\n        });\n        await audioSystem.loadMusicTrack({\n          id: 'victory_music',\n          name: 'Victory Theme',\n          src: '/assets/sounds/music/victory.mp3',\n          intensity: 'medium',\n          loop: false,\n          volume: 0.6,\n          category: 'results'\n        });\n\n        // Load sound effects\n        await audioSystem.loadSoundEffect({\n          id: 'ui_select',\n          name: 'UI Select',\n          src: '/assets/sounds/sfx/ui_select.mp3',\n          volume: 0.5,\n          loop: false\n        });\n        await audioSystem.loadSoundEffect({\n          id: 'engine_idle',\n          name: 'Engine Idle',\n          src: '/assets/sounds/sfx/engine_idle.mp3',\n          volume: 0.4,\n          loop: true\n        });\n        await audioSystem.loadSoundEffect({\n          id: 'tire_skid',\n          name: 'Tire Skid',\n          src: '/assets/sounds/sfx/tire_skid.mp3',\n          volume: 0.5,\n          loop: false\n        });\n        await audioSystem.loadSoundEffect({\n          id: 'brake_skid',\n          name: 'Brake Skid',\n          src: '/assets/sounds/sfx/brake_skid.mp3',\n          volume: 0.5\n        });\n        await audioSystem.loadSoundEffect({\n          id: 'nitro_boost',\n          name: 'Nitro Boost',\n          src: '/assets/sounds/sfx/nitro_boost.mp3',\n          volume: 0.7\n        });\n        await audioSystem.loadSoundEffect({\n          id: 'collision',\n          name: 'Collision',\n          src: '/assets/sounds/sfx/collision.mp3',\n          volume: 0.6\n        });\n        await audioSystem.loadSoundEffect({\n          id: 'checkpoint',\n          name: 'Checkpoint',\n          src: '/assets/sounds/sfx/checkpoint.mp3',\n          volume: 0.5\n        });\n        await audioSystem.loadSoundEffect({\n          id: 'race_start_countdown',\n          name: 'Race Start Countdown',\n          src: '/assets/sounds/sfx/race_countdown.mp3',\n          volume: 0.8\n        });\n        await audioSystem.loadSoundEffect({\n          id: 'race_start',\n          name: 'Race Start',\n          src: '/assets/sounds/sfx/race_start.mp3',\n          volume: 0.8\n        });\n        await audioSystem.loadSoundEffect({\n          id: 'race_complete',\n          name: 'Race Complete',\n          src: '/assets/sounds/sfx/race_complete.mp3',\n          volume: 0.8\n        });\n      } catch (error) {\n        console.error('Failed to load audio assets:', error);\n      }\n    };\n    loadRaceAudio();\n  }, []);\n\n  // Handle screen transitions for music changes\n  useEffect(() => {\n    // Use the appropriate music based on the current screen\n    const screenMusicMap = {\n      'main-menu': 'menu_music',\n      'game-mode': 'menu_music',\n      'customize': 'menu_music',\n      'lab': 'menu_music',\n      'race': 'race_music_intro',\n      'story': 'menu_music'\n    };\n    const musicId = screenMusicMap[currentScreen];\n    if (musicId && !isMusicMuted) {\n      audioSystem.fadeOutCurrentMusic(1000);\n      setTimeout(() => {\n        audioSystem.playMusic(musicId);\n      }, 1000);\n    }\n\n    // Clean up function\n    return () => {\n      // No need to stop music here, as we're using crossfade between screens\n    };\n  }, [currentScreen, isMusicMuted]);\n\n  // Toggle music mute state\n  const toggleMusic = useCallback(() => {\n    const newMuteState = !isMusicMuted;\n    setIsMusicMuted(newMuteState);\n    if (newMuteState) {\n      audioSystem.muteMusic();\n    } else {\n      audioSystem.unmuteMusic();\n\n      // Resume music based on current screen\n      const screenMusicMap = {\n        'main-menu': 'menu_music',\n        'game-mode': 'menu_music',\n        'customize': 'customization_music',\n        'lab': 'lab_music',\n        'racing': 'race_music_intro',\n        'story': 'story_music'\n      };\n      const musicId = screenMusicMap[currentScreen];\n      if (musicId) {\n        audioSystem.playMusic(musicId, 1000);\n      }\n    }\n  }, [isMusicMuted, audioSystem, currentScreen]);\n\n  // Toggle sound effects mute state\n  const toggleSfx = useCallback(() => {\n    const newMuteState = !isSfxMuted;\n    setIsSfxMuted(newMuteState);\n    if (newMuteState) {\n      audioSystem.muteSoundEffects();\n      // Play a muted UI sound to verify it's muted\n      audioSystem.playSoundEffect('ui_click');\n    } else {\n      audioSystem.unmuteSoundEffects();\n      // Play a UI sound to verify it's unmuted\n      audioSystem.playSoundEffect('ui_click');\n    }\n  }, [isSfxMuted, audioSystem]);\n  return /*#__PURE__*/_jsxDEV(AudioControlsContainer, {\n    children: [/*#__PURE__*/_jsxDEV(AudioButton, {\n      $active: !isMusicMuted,\n      onClick: toggleMusic,\n      \"aria-label\": isMusicMuted ? \"Unmute Music\" : \"Mute Music\",\n      children: isMusicMuted ? /*#__PURE__*/_jsxDEV(MusicMuteIcon, {}, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 286,\n        columnNumber: 25\n      }, this) : /*#__PURE__*/_jsxDEV(MusicIcon, {}, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 286,\n        columnNumber: 45\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 281,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(AudioButton, {\n      $active: !isSfxMuted,\n      onClick: toggleSfx,\n      \"aria-label\": isSfxMuted ? \"Unmute Sound Effects\" : \"Mute Sound Effects\",\n      children: isSfxMuted ? /*#__PURE__*/_jsxDEV(SoundMuteIcon, {}, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 294,\n        columnNumber: 23\n      }, this) : /*#__PURE__*/_jsxDEV(SoundIcon, {}, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 294,\n        columnNumber: 43\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 289,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 280,\n    columnNumber: 5\n  }, this);\n};\n_s(AudioController, \"GtT5PJomjTkBwpvN3ChEUoCIMhE=\", false, function () {\n  return [useSelector];\n});\n_c7 = AudioController;\nexport default AudioController;\nvar _c, _c2, _c3, _c4, _c5, _c6, _c7;\n$RefreshReg$(_c, \"AudioControlsContainer\");\n$RefreshReg$(_c2, \"AudioButton\");\n$RefreshReg$(_c3, \"MusicIcon\");\n$RefreshReg$(_c4, \"MusicMuteIcon\");\n$RefreshReg$(_c5, \"SoundIcon\");\n$RefreshReg$(_c6, \"SoundMuteIcon\");\n$RefreshReg$(_c7, \"AudioController\");", "map": {"version": 3, "names": ["React", "useEffect", "useState", "useCallback", "AudioSystem", "useSelector", "styled", "jsxDEV", "_jsxDEV", "AudioControlsContainer", "div", "_c", "AudioButton", "button", "props", "$active", "_c2", "MusicIcon", "viewBox", "xmlns", "children", "d", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "cx", "cy", "r", "_c3", "MusicMuteIcon", "x1", "y1", "x2", "y2", "stroke", "strokeWidth", "_c4", "SoundIcon", "_c5", "SoundMuteIcon", "_c6", "AudioController", "_s", "isMusicMuted", "setIsMusicMuted", "isSfxMuted", "setIsSfxMuted", "audioSystem", "getInstance", "currentScreen", "state", "game", "loadRaceAudio", "loadMusicTrack", "id", "name", "src", "intensity", "loop", "volume", "category", "loadSoundEffect", "error", "console", "screenMusicMap", "musicId", "fadeOutCurrentMusic", "setTimeout", "playMusic", "toggleMusic", "newMuteState", "muteMusic", "unmuteMusic", "toggleSfx", "muteSoundEffects", "playSoundEffect", "unmuteSoundEffects", "onClick", "_c7", "$RefreshReg$"], "sources": ["C:/Users/<USER>/Documents/Apps_Creations/SiahMan_Racing4Speed_WebGame/siah-man-racing/src/components/AudioController.tsx"], "sourcesContent": ["import React, { useEffect, useState, useCallback } from 'react';\nimport { AudioSystem } from '../game/AudioSystem';\nimport { useSelector } from 'react-redux';\nimport { RootState } from '../store';\nimport styled from 'styled-components';\n\n// Styled components\nconst AudioControlsContainer = styled.div`\n  position: fixed;\n  bottom: 15px;\n  right: 15px;\n  display: flex;\n  gap: 10px;\n  z-index: 100;\n`;\n\nconst AudioButton = styled.button<{ $active: boolean }>`\n  width: 40px;\n  height: 40px;\n  border-radius: 50%;\n  background: ${props => props.$active \n    ? 'linear-gradient(145deg, #0088ff, #00aaff)'\n    : 'rgba(0, 0, 0, 0.5)'\n  };\n  border: 2px solid ${props => props.$active ? '#ffffff' : 'rgba(255, 255, 255, 0.3)'};\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  cursor: pointer;\n  transition: all 0.3s ease;\n  \n  &:hover {\n    transform: scale(1.1);\n    box-shadow: 0 0 15px rgba(0, 170, 255, 0.5);\n  }\n  \n  svg {\n    width: 20px;\n    height: 20px;\n    fill: white;\n  }\n`;\n\nconst MusicIcon = () => (\n  <svg viewBox=\"0 0 24 24\" xmlns=\"http://www.w3.org/2000/svg\">\n    <path d=\"M9 18V5l12-2v13\" />\n    <circle cx=\"6\" cy=\"18\" r=\"3\" />\n    <circle cx=\"18\" cy=\"16\" r=\"3\" />\n  </svg>\n);\n\nconst MusicMuteIcon = () => (\n  <svg viewBox=\"0 0 24 24\" xmlns=\"http://www.w3.org/2000/svg\">\n    <path d=\"M9 18V5l12-2v13\" />\n    <circle cx=\"6\" cy=\"18\" r=\"3\" />\n    <circle cx=\"18\" cy=\"16\" r=\"3\" />\n    <line x1=\"3\" y1=\"3\" x2=\"21\" y2=\"21\" stroke=\"white\" strokeWidth=\"2\" />\n  </svg>\n);\n\nconst SoundIcon = () => (\n  <svg viewBox=\"0 0 24 24\" xmlns=\"http://www.w3.org/2000/svg\">\n    <path d=\"M12 4L8 8H4v8h4l4 4V4z\" />\n    <path d=\"M16 8a5 5 0 0 1 0 8M19 5a10 10 0 0 1 0 14\" />\n  </svg>\n);\n\nconst SoundMuteIcon = () => (\n  <svg viewBox=\"0 0 24 24\" xmlns=\"http://www.w3.org/2000/svg\">\n    <path d=\"M12 4L8 8H4v8h4l4 4V4z\" />\n    <line x1=\"17\" y1=\"7\" x2=\"17\" y2=\"17\" stroke=\"white\" strokeWidth=\"2\" />\n    <line x1=\"21\" y1=\"11\" x2=\"21\" y2=\"13\" stroke=\"white\" strokeWidth=\"2\" />\n    <line x1=\"3\" y1=\"3\" x2=\"21\" y2=\"21\" stroke=\"white\" strokeWidth=\"2\" />\n  </svg>\n);\n\nconst AudioController: React.FC = () => {\n  const [isMusicMuted, setIsMusicMuted] = useState(false);\n  const [isSfxMuted, setIsSfxMuted] = useState(false);\n  const audioSystem = AudioSystem.getInstance();\n  const { currentScreen } = useSelector((state: RootState) => state.game);\n  \n  // Load sound assets\n  useEffect(() => {\n    const loadRaceAudio = async () => {\n      try {\n        // Load music tracks\n        await audioSystem.loadMusicTrack({\n          id: 'menu_music',\n          name: 'Menu Theme',\n          src: '/assets/sounds/music/menu_theme.mp3',\n          intensity: 'low',\n          loop: true,\n          volume: 0.6,\n          category: 'menu'\n        });\n        \n        await audioSystem.loadMusicTrack({\n          id: 'race_music_intro',\n          name: 'Race Intro',\n          src: '/assets/sounds/music/race_intro.mp3',\n          intensity: 'medium',\n          loop: false,\n          volume: 0.7,\n          category: 'race'\n        });\n        \n        await audioSystem.loadMusicTrack({\n          id: 'race_music',\n          name: 'Race Theme',\n          src: '/assets/sounds/music/race_theme.mp3',\n          intensity: 'high',\n          loop: true,\n          volume: 0.7,\n          category: 'race'\n        });\n        \n        await audioSystem.loadMusicTrack({\n          id: 'victory_music',\n          name: 'Victory Theme',\n          src: '/assets/sounds/music/victory.mp3',\n          intensity: 'medium',\n          loop: false,\n          volume: 0.6,\n          category: 'results'\n        });\n        \n        // Load sound effects\n        await audioSystem.loadSoundEffect({\n          id: 'ui_select',\n          name: 'UI Select',\n          src: '/assets/sounds/sfx/ui_select.mp3',\n          volume: 0.5,\n          loop: false\n        });\n\n        await audioSystem.loadSoundEffect({\n          id: 'engine_idle',\n          name: 'Engine Idle',\n          src: '/assets/sounds/sfx/engine_idle.mp3',\n          volume: 0.4,\n          loop: true\n        });\n\n        await audioSystem.loadSoundEffect({\n          id: 'tire_skid',\n          name: 'Tire Skid',\n          src: '/assets/sounds/sfx/tire_skid.mp3',\n          volume: 0.5,\n          loop: false\n        });\n        \n        await audioSystem.loadSoundEffect({\n          id: 'brake_skid',\n          name: 'Brake Skid',\n          src: '/assets/sounds/sfx/brake_skid.mp3',\n          volume: 0.5\n        });\n        \n        await audioSystem.loadSoundEffect({\n          id: 'nitro_boost',\n          name: 'Nitro Boost',\n          src: '/assets/sounds/sfx/nitro_boost.mp3',\n          volume: 0.7\n        });\n        \n        await audioSystem.loadSoundEffect({\n          id: 'collision',\n          name: 'Collision',\n          src: '/assets/sounds/sfx/collision.mp3',\n          volume: 0.6\n        });\n        \n        await audioSystem.loadSoundEffect({\n          id: 'checkpoint',\n          name: 'Checkpoint',\n          src: '/assets/sounds/sfx/checkpoint.mp3',\n          volume: 0.5\n        });\n        \n        await audioSystem.loadSoundEffect({\n          id: 'race_start_countdown',\n          name: 'Race Start Countdown',\n          src: '/assets/sounds/sfx/race_countdown.mp3',\n          volume: 0.8\n        });\n        \n        await audioSystem.loadSoundEffect({\n          id: 'race_start',\n          name: 'Race Start',\n          src: '/assets/sounds/sfx/race_start.mp3',\n          volume: 0.8\n        });\n        \n        await audioSystem.loadSoundEffect({\n          id: 'race_complete',\n          name: 'Race Complete',\n          src: '/assets/sounds/sfx/race_complete.mp3',\n          volume: 0.8\n        });\n      } catch (error) {\n        console.error('Failed to load audio assets:', error);\n      }\n    };\n    \n    loadRaceAudio();\n  }, []);\n  \n  // Handle screen transitions for music changes\n  useEffect(() => {\n    // Use the appropriate music based on the current screen\n    const screenMusicMap: Record<string, string> = {\n      'main-menu': 'menu_music',\n      'game-mode': 'menu_music',\n      'customize': 'menu_music',\n      'lab': 'menu_music',\n      'race': 'race_music_intro',\n      'story': 'menu_music'\n    };\n    \n    const musicId = screenMusicMap[currentScreen];\n    \n    if (musicId && !isMusicMuted) {\n      audioSystem.fadeOutCurrentMusic(1000);\n      setTimeout(() => {\n        audioSystem.playMusic(musicId);\n      }, 1000);\n    }\n    \n    // Clean up function\n    return () => {\n      // No need to stop music here, as we're using crossfade between screens\n    };\n  }, [currentScreen, isMusicMuted]);\n  \n  // Toggle music mute state\n  const toggleMusic = useCallback(() => {\n    const newMuteState = !isMusicMuted;\n    setIsMusicMuted(newMuteState);\n    \n    if (newMuteState) {\n      audioSystem.muteMusic();\n    } else {\n      audioSystem.unmuteMusic();\n      \n      // Resume music based on current screen\n      const screenMusicMap: Record<string, string> = {\n        'main-menu': 'menu_music',\n        'game-mode': 'menu_music',\n        'customize': 'customization_music',\n        'lab': 'lab_music',\n        'racing': 'race_music_intro',\n        'story': 'story_music'\n      };\n      \n      const musicId = screenMusicMap[currentScreen];\n      if (musicId) {\n        audioSystem.playMusic(musicId, 1000);\n      }\n    }\n  }, [isMusicMuted, audioSystem, currentScreen]);\n  \n  // Toggle sound effects mute state\n  const toggleSfx = useCallback(() => {\n    const newMuteState = !isSfxMuted;\n    setIsSfxMuted(newMuteState);\n    \n    if (newMuteState) {\n      audioSystem.muteSoundEffects();\n      // Play a muted UI sound to verify it's muted\n      audioSystem.playSoundEffect('ui_click');\n    } else {\n      audioSystem.unmuteSoundEffects();\n      // Play a UI sound to verify it's unmuted\n      audioSystem.playSoundEffect('ui_click');\n    }\n  }, [isSfxMuted, audioSystem]);\n  \n  return (\n    <AudioControlsContainer>\n      <AudioButton \n        $active={!isMusicMuted} \n        onClick={toggleMusic}\n        aria-label={isMusicMuted ? \"Unmute Music\" : \"Mute Music\"}\n      >\n        {isMusicMuted ? <MusicMuteIcon /> : <MusicIcon />}\n      </AudioButton>\n      \n      <AudioButton \n        $active={!isSfxMuted} \n        onClick={toggleSfx}\n        aria-label={isSfxMuted ? \"Unmute Sound Effects\" : \"Mute Sound Effects\"}\n      >\n        {isSfxMuted ? <SoundMuteIcon /> : <SoundIcon />}\n      </AudioButton>\n    </AudioControlsContainer>\n  );\n};\n\nexport default AudioController;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,SAAS,EAAEC,QAAQ,EAAEC,WAAW,QAAQ,OAAO;AAC/D,SAASC,WAAW,QAAQ,qBAAqB;AACjD,SAASC,WAAW,QAAQ,aAAa;AAEzC,OAAOC,MAAM,MAAM,mBAAmB;;AAEtC;AAAA,SAAAC,MAAA,IAAAC,OAAA;AACA,MAAMC,sBAAsB,GAAGH,MAAM,CAACI,GAAG;AACzC;AACA;AACA;AACA;AACA;AACA;AACA,CAAC;AAACC,EAAA,GAPIF,sBAAsB;AAS5B,MAAMG,WAAW,GAAGN,MAAM,CAACO,MAA4B;AACvD;AACA;AACA;AACA,gBAAgBC,KAAK,IAAIA,KAAK,CAACC,OAAO,GAChC,2CAA2C,GAC3C,oBAAoB;AAC1B,sBACsBD,KAAK,IAAIA,KAAK,CAACC,OAAO,GAAG,SAAS,GAAG,0BAA0B;AACrF;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,CAAC;AAACC,GAAA,GAzBIJ,WAAW;AA2BjB,MAAMK,SAAS,GAAGA,CAAA,kBAChBT,OAAA;EAAKU,OAAO,EAAC,WAAW;EAACC,KAAK,EAAC,4BAA4B;EAAAC,QAAA,gBACzDZ,OAAA;IAAMa,CAAC,EAAC;EAAiB;IAAAC,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OAAE,CAAC,eAC5BjB,OAAA;IAAQkB,EAAE,EAAC,GAAG;IAACC,EAAE,EAAC,IAAI;IAACC,CAAC,EAAC;EAAG;IAAAN,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OAAE,CAAC,eAC/BjB,OAAA;IAAQkB,EAAE,EAAC,IAAI;IAACC,EAAE,EAAC,IAAI;IAACC,CAAC,EAAC;EAAG;IAAAN,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OAAE,CAAC;AAAA;EAAAH,QAAA,EAAAC,YAAA;EAAAC,UAAA;EAAAC,YAAA;AAAA,OAC7B,CACN;AAACI,GAAA,GANIZ,SAAS;AAQf,MAAMa,aAAa,GAAGA,CAAA,kBACpBtB,OAAA;EAAKU,OAAO,EAAC,WAAW;EAACC,KAAK,EAAC,4BAA4B;EAAAC,QAAA,gBACzDZ,OAAA;IAAMa,CAAC,EAAC;EAAiB;IAAAC,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OAAE,CAAC,eAC5BjB,OAAA;IAAQkB,EAAE,EAAC,GAAG;IAACC,EAAE,EAAC,IAAI;IAACC,CAAC,EAAC;EAAG;IAAAN,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OAAE,CAAC,eAC/BjB,OAAA;IAAQkB,EAAE,EAAC,IAAI;IAACC,EAAE,EAAC,IAAI;IAACC,CAAC,EAAC;EAAG;IAAAN,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OAAE,CAAC,eAChCjB,OAAA;IAAMuB,EAAE,EAAC,GAAG;IAACC,EAAE,EAAC,GAAG;IAACC,EAAE,EAAC,IAAI;IAACC,EAAE,EAAC,IAAI;IAACC,MAAM,EAAC,OAAO;IAACC,WAAW,EAAC;EAAG;IAAAd,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OAAE,CAAC;AAAA;EAAAH,QAAA,EAAAC,YAAA;EAAAC,UAAA;EAAAC,YAAA;AAAA,OAClE,CACN;AAACY,GAAA,GAPIP,aAAa;AASnB,MAAMQ,SAAS,GAAGA,CAAA,kBAChB9B,OAAA;EAAKU,OAAO,EAAC,WAAW;EAACC,KAAK,EAAC,4BAA4B;EAAAC,QAAA,gBACzDZ,OAAA;IAAMa,CAAC,EAAC;EAAwB;IAAAC,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OAAE,CAAC,eACnCjB,OAAA;IAAMa,CAAC,EAAC;EAA2C;IAAAC,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OAAE,CAAC;AAAA;EAAAH,QAAA,EAAAC,YAAA;EAAAC,UAAA;EAAAC,YAAA;AAAA,OACnD,CACN;AAACc,GAAA,GALID,SAAS;AAOf,MAAME,aAAa,GAAGA,CAAA,kBACpBhC,OAAA;EAAKU,OAAO,EAAC,WAAW;EAACC,KAAK,EAAC,4BAA4B;EAAAC,QAAA,gBACzDZ,OAAA;IAAMa,CAAC,EAAC;EAAwB;IAAAC,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OAAE,CAAC,eACnCjB,OAAA;IAAMuB,EAAE,EAAC,IAAI;IAACC,EAAE,EAAC,GAAG;IAACC,EAAE,EAAC,IAAI;IAACC,EAAE,EAAC,IAAI;IAACC,MAAM,EAAC,OAAO;IAACC,WAAW,EAAC;EAAG;IAAAd,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OAAE,CAAC,eACtEjB,OAAA;IAAMuB,EAAE,EAAC,IAAI;IAACC,EAAE,EAAC,IAAI;IAACC,EAAE,EAAC,IAAI;IAACC,EAAE,EAAC,IAAI;IAACC,MAAM,EAAC,OAAO;IAACC,WAAW,EAAC;EAAG;IAAAd,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OAAE,CAAC,eACvEjB,OAAA;IAAMuB,EAAE,EAAC,GAAG;IAACC,EAAE,EAAC,GAAG;IAACC,EAAE,EAAC,IAAI;IAACC,EAAE,EAAC,IAAI;IAACC,MAAM,EAAC,OAAO;IAACC,WAAW,EAAC;EAAG;IAAAd,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OAAE,CAAC;AAAA;EAAAH,QAAA,EAAAC,YAAA;EAAAC,UAAA;EAAAC,YAAA;AAAA,OAClE,CACN;AAACgB,GAAA,GAPID,aAAa;AASnB,MAAME,eAAyB,GAAGA,CAAA,KAAM;EAAAC,EAAA;EACtC,MAAM,CAACC,YAAY,EAAEC,eAAe,CAAC,GAAG3C,QAAQ,CAAC,KAAK,CAAC;EACvD,MAAM,CAAC4C,UAAU,EAAEC,aAAa,CAAC,GAAG7C,QAAQ,CAAC,KAAK,CAAC;EACnD,MAAM8C,WAAW,GAAG5C,WAAW,CAAC6C,WAAW,CAAC,CAAC;EAC7C,MAAM;IAAEC;EAAc,CAAC,GAAG7C,WAAW,CAAE8C,KAAgB,IAAKA,KAAK,CAACC,IAAI,CAAC;;EAEvE;EACAnD,SAAS,CAAC,MAAM;IACd,MAAMoD,aAAa,GAAG,MAAAA,CAAA,KAAY;MAChC,IAAI;QACF;QACA,MAAML,WAAW,CAACM,cAAc,CAAC;UAC/BC,EAAE,EAAE,YAAY;UAChBC,IAAI,EAAE,YAAY;UAClBC,GAAG,EAAE,qCAAqC;UAC1CC,SAAS,EAAE,KAAK;UAChBC,IAAI,EAAE,IAAI;UACVC,MAAM,EAAE,GAAG;UACXC,QAAQ,EAAE;QACZ,CAAC,CAAC;QAEF,MAAMb,WAAW,CAACM,cAAc,CAAC;UAC/BC,EAAE,EAAE,kBAAkB;UACtBC,IAAI,EAAE,YAAY;UAClBC,GAAG,EAAE,qCAAqC;UAC1CC,SAAS,EAAE,QAAQ;UACnBC,IAAI,EAAE,KAAK;UACXC,MAAM,EAAE,GAAG;UACXC,QAAQ,EAAE;QACZ,CAAC,CAAC;QAEF,MAAMb,WAAW,CAACM,cAAc,CAAC;UAC/BC,EAAE,EAAE,YAAY;UAChBC,IAAI,EAAE,YAAY;UAClBC,GAAG,EAAE,qCAAqC;UAC1CC,SAAS,EAAE,MAAM;UACjBC,IAAI,EAAE,IAAI;UACVC,MAAM,EAAE,GAAG;UACXC,QAAQ,EAAE;QACZ,CAAC,CAAC;QAEF,MAAMb,WAAW,CAACM,cAAc,CAAC;UAC/BC,EAAE,EAAE,eAAe;UACnBC,IAAI,EAAE,eAAe;UACrBC,GAAG,EAAE,kCAAkC;UACvCC,SAAS,EAAE,QAAQ;UACnBC,IAAI,EAAE,KAAK;UACXC,MAAM,EAAE,GAAG;UACXC,QAAQ,EAAE;QACZ,CAAC,CAAC;;QAEF;QACA,MAAMb,WAAW,CAACc,eAAe,CAAC;UAChCP,EAAE,EAAE,WAAW;UACfC,IAAI,EAAE,WAAW;UACjBC,GAAG,EAAE,kCAAkC;UACvCG,MAAM,EAAE,GAAG;UACXD,IAAI,EAAE;QACR,CAAC,CAAC;QAEF,MAAMX,WAAW,CAACc,eAAe,CAAC;UAChCP,EAAE,EAAE,aAAa;UACjBC,IAAI,EAAE,aAAa;UACnBC,GAAG,EAAE,oCAAoC;UACzCG,MAAM,EAAE,GAAG;UACXD,IAAI,EAAE;QACR,CAAC,CAAC;QAEF,MAAMX,WAAW,CAACc,eAAe,CAAC;UAChCP,EAAE,EAAE,WAAW;UACfC,IAAI,EAAE,WAAW;UACjBC,GAAG,EAAE,kCAAkC;UACvCG,MAAM,EAAE,GAAG;UACXD,IAAI,EAAE;QACR,CAAC,CAAC;QAEF,MAAMX,WAAW,CAACc,eAAe,CAAC;UAChCP,EAAE,EAAE,YAAY;UAChBC,IAAI,EAAE,YAAY;UAClBC,GAAG,EAAE,mCAAmC;UACxCG,MAAM,EAAE;QACV,CAAC,CAAC;QAEF,MAAMZ,WAAW,CAACc,eAAe,CAAC;UAChCP,EAAE,EAAE,aAAa;UACjBC,IAAI,EAAE,aAAa;UACnBC,GAAG,EAAE,oCAAoC;UACzCG,MAAM,EAAE;QACV,CAAC,CAAC;QAEF,MAAMZ,WAAW,CAACc,eAAe,CAAC;UAChCP,EAAE,EAAE,WAAW;UACfC,IAAI,EAAE,WAAW;UACjBC,GAAG,EAAE,kCAAkC;UACvCG,MAAM,EAAE;QACV,CAAC,CAAC;QAEF,MAAMZ,WAAW,CAACc,eAAe,CAAC;UAChCP,EAAE,EAAE,YAAY;UAChBC,IAAI,EAAE,YAAY;UAClBC,GAAG,EAAE,mCAAmC;UACxCG,MAAM,EAAE;QACV,CAAC,CAAC;QAEF,MAAMZ,WAAW,CAACc,eAAe,CAAC;UAChCP,EAAE,EAAE,sBAAsB;UAC1BC,IAAI,EAAE,sBAAsB;UAC5BC,GAAG,EAAE,uCAAuC;UAC5CG,MAAM,EAAE;QACV,CAAC,CAAC;QAEF,MAAMZ,WAAW,CAACc,eAAe,CAAC;UAChCP,EAAE,EAAE,YAAY;UAChBC,IAAI,EAAE,YAAY;UAClBC,GAAG,EAAE,mCAAmC;UACxCG,MAAM,EAAE;QACV,CAAC,CAAC;QAEF,MAAMZ,WAAW,CAACc,eAAe,CAAC;UAChCP,EAAE,EAAE,eAAe;UACnBC,IAAI,EAAE,eAAe;UACrBC,GAAG,EAAE,sCAAsC;UAC3CG,MAAM,EAAE;QACV,CAAC,CAAC;MACJ,CAAC,CAAC,OAAOG,KAAK,EAAE;QACdC,OAAO,CAACD,KAAK,CAAC,8BAA8B,EAAEA,KAAK,CAAC;MACtD;IACF,CAAC;IAEDV,aAAa,CAAC,CAAC;EACjB,CAAC,EAAE,EAAE,CAAC;;EAEN;EACApD,SAAS,CAAC,MAAM;IACd;IACA,MAAMgE,cAAsC,GAAG;MAC7C,WAAW,EAAE,YAAY;MACzB,WAAW,EAAE,YAAY;MACzB,WAAW,EAAE,YAAY;MACzB,KAAK,EAAE,YAAY;MACnB,MAAM,EAAE,kBAAkB;MAC1B,OAAO,EAAE;IACX,CAAC;IAED,MAAMC,OAAO,GAAGD,cAAc,CAACf,aAAa,CAAC;IAE7C,IAAIgB,OAAO,IAAI,CAACtB,YAAY,EAAE;MAC5BI,WAAW,CAACmB,mBAAmB,CAAC,IAAI,CAAC;MACrCC,UAAU,CAAC,MAAM;QACfpB,WAAW,CAACqB,SAAS,CAACH,OAAO,CAAC;MAChC,CAAC,EAAE,IAAI,CAAC;IACV;;IAEA;IACA,OAAO,MAAM;MACX;IAAA,CACD;EACH,CAAC,EAAE,CAAChB,aAAa,EAAEN,YAAY,CAAC,CAAC;;EAEjC;EACA,MAAM0B,WAAW,GAAGnE,WAAW,CAAC,MAAM;IACpC,MAAMoE,YAAY,GAAG,CAAC3B,YAAY;IAClCC,eAAe,CAAC0B,YAAY,CAAC;IAE7B,IAAIA,YAAY,EAAE;MAChBvB,WAAW,CAACwB,SAAS,CAAC,CAAC;IACzB,CAAC,MAAM;MACLxB,WAAW,CAACyB,WAAW,CAAC,CAAC;;MAEzB;MACA,MAAMR,cAAsC,GAAG;QAC7C,WAAW,EAAE,YAAY;QACzB,WAAW,EAAE,YAAY;QACzB,WAAW,EAAE,qBAAqB;QAClC,KAAK,EAAE,WAAW;QAClB,QAAQ,EAAE,kBAAkB;QAC5B,OAAO,EAAE;MACX,CAAC;MAED,MAAMC,OAAO,GAAGD,cAAc,CAACf,aAAa,CAAC;MAC7C,IAAIgB,OAAO,EAAE;QACXlB,WAAW,CAACqB,SAAS,CAACH,OAAO,EAAE,IAAI,CAAC;MACtC;IACF;EACF,CAAC,EAAE,CAACtB,YAAY,EAAEI,WAAW,EAAEE,aAAa,CAAC,CAAC;;EAE9C;EACA,MAAMwB,SAAS,GAAGvE,WAAW,CAAC,MAAM;IAClC,MAAMoE,YAAY,GAAG,CAACzB,UAAU;IAChCC,aAAa,CAACwB,YAAY,CAAC;IAE3B,IAAIA,YAAY,EAAE;MAChBvB,WAAW,CAAC2B,gBAAgB,CAAC,CAAC;MAC9B;MACA3B,WAAW,CAAC4B,eAAe,CAAC,UAAU,CAAC;IACzC,CAAC,MAAM;MACL5B,WAAW,CAAC6B,kBAAkB,CAAC,CAAC;MAChC;MACA7B,WAAW,CAAC4B,eAAe,CAAC,UAAU,CAAC;IACzC;EACF,CAAC,EAAE,CAAC9B,UAAU,EAAEE,WAAW,CAAC,CAAC;EAE7B,oBACExC,OAAA,CAACC,sBAAsB;IAAAW,QAAA,gBACrBZ,OAAA,CAACI,WAAW;MACVG,OAAO,EAAE,CAAC6B,YAAa;MACvBkC,OAAO,EAAER,WAAY;MACrB,cAAY1B,YAAY,GAAG,cAAc,GAAG,YAAa;MAAAxB,QAAA,EAExDwB,YAAY,gBAAGpC,OAAA,CAACsB,aAAa;QAAAR,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC,gBAAGjB,OAAA,CAACS,SAAS;QAAAK,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACtC,CAAC,eAEdjB,OAAA,CAACI,WAAW;MACVG,OAAO,EAAE,CAAC+B,UAAW;MACrBgC,OAAO,EAAEJ,SAAU;MACnB,cAAY5B,UAAU,GAAG,sBAAsB,GAAG,oBAAqB;MAAA1B,QAAA,EAEtE0B,UAAU,gBAAGtC,OAAA,CAACgC,aAAa;QAAAlB,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC,gBAAGjB,OAAA,CAAC8B,SAAS;QAAAhB,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACpC,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACQ,CAAC;AAE7B,CAAC;AAACkB,EAAA,CA7NID,eAAyB;EAAA,QAIHrC,WAAW;AAAA;AAAA0E,GAAA,GAJjCrC,eAAyB;AA+N/B,eAAeA,eAAe;AAAC,IAAA/B,EAAA,EAAAK,GAAA,EAAAa,GAAA,EAAAQ,GAAA,EAAAE,GAAA,EAAAE,GAAA,EAAAsC,GAAA;AAAAC,YAAA,CAAArE,EAAA;AAAAqE,YAAA,CAAAhE,GAAA;AAAAgE,YAAA,CAAAnD,GAAA;AAAAmD,YAAA,CAAA3C,GAAA;AAAA2C,YAAA,CAAAzC,GAAA;AAAAyC,YAAA,CAAAvC,GAAA;AAAAuC,YAAA,CAAAD,GAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}