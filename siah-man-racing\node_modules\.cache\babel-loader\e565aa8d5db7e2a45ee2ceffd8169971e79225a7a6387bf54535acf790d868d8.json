{"ast": null, "code": "import _extends from '@babel/runtime/helpers/esm/extends';\nimport * as React from 'react';\nimport * as THREE from 'three';\nimport { extend, useThree, useFrame } from '@react-three/fiber';\nimport { version } from '../helpers/constants.js';\nclass SparklesImplMaterial extends THREE.ShaderMaterial {\n  constructor() {\n    super({\n      uniforms: {\n        time: {\n          value: 0\n        },\n        pixelRatio: {\n          value: 1\n        }\n      },\n      vertexShader: /* glsl */`\n        uniform float pixelRatio;\n        uniform float time;\n        attribute float size;  \n        attribute float speed;  \n        attribute float opacity;\n        attribute vec3 noise;\n        attribute vec3 color;\n        varying vec3 vColor;\n        varying float vOpacity;\n\n        void main() {\n          vec4 modelPosition = modelMatrix * vec4(position, 1.0);\n          modelPosition.y += sin(time * speed + modelPosition.x * noise.x * 100.0) * 0.2;\n          modelPosition.z += cos(time * speed + modelPosition.x * noise.y * 100.0) * 0.2;\n          modelPosition.x += cos(time * speed + modelPosition.x * noise.z * 100.0) * 0.2;\n          vec4 viewPosition = viewMatrix * modelPosition;\n          vec4 projectionPostion = projectionMatrix * viewPosition;\n          gl_Position = projectionPostion;\n          gl_PointSize = size * 25. * pixelRatio;\n          gl_PointSize *= (1.0 / - viewPosition.z);\n          vColor = color;\n          vOpacity = opacity;\n        }\n      `,\n      fragmentShader: /* glsl */`\n        varying vec3 vColor;\n        varying float vOpacity;\n        void main() {\n          float distanceToCenter = distance(gl_PointCoord, vec2(0.5));\n          float strength = 0.05 / distanceToCenter - 0.1;\n          gl_FragColor = vec4(vColor, strength * vOpacity);\n          #include <tonemapping_fragment>\n          #include <${version >= 154 ? 'colorspace_fragment' : 'encodings_fragment'}>\n        }\n      `\n    });\n  }\n  get time() {\n    return this.uniforms.time.value;\n  }\n  set time(value) {\n    this.uniforms.time.value = value;\n  }\n  get pixelRatio() {\n    return this.uniforms.pixelRatio.value;\n  }\n  set pixelRatio(value) {\n    this.uniforms.pixelRatio.value = value;\n  }\n}\nconst isFloat32Array = def => def && def.constructor === Float32Array;\nconst expandColor = v => [v.r, v.g, v.b];\nconst isVector = v => v instanceof THREE.Vector2 || v instanceof THREE.Vector3 || v instanceof THREE.Vector4;\nconst normalizeVector = v => {\n  if (Array.isArray(v)) return v;else if (isVector(v)) return v.toArray();\n  return [v, v, v];\n};\nfunction usePropAsIsOrAsAttribute(count, prop, setDefault) {\n  return React.useMemo(() => {\n    if (prop !== undefined) {\n      if (isFloat32Array(prop)) {\n        return prop;\n      } else {\n        if (prop instanceof THREE.Color) {\n          const a = Array.from({\n            length: count * 3\n          }, () => expandColor(prop)).flat();\n          return Float32Array.from(a);\n        } else if (isVector(prop) || Array.isArray(prop)) {\n          const a = Array.from({\n            length: count * 3\n          }, () => normalizeVector(prop)).flat();\n          return Float32Array.from(a);\n        }\n        return Float32Array.from({\n          length: count\n        }, () => prop);\n      }\n    }\n    return Float32Array.from({\n      length: count\n    }, setDefault);\n  }, [prop]);\n}\nconst Sparkles = /* @__PURE__ */React.forwardRef(({\n  noise = 1,\n  count = 100,\n  speed = 1,\n  opacity = 1,\n  scale = 1,\n  size,\n  color,\n  children,\n  ...props\n}, forwardRef) => {\n  React.useMemo(() => extend({\n    SparklesImplMaterial\n  }), []);\n  const ref = React.useRef(null);\n  const dpr = useThree(state => state.viewport.dpr);\n  const _scale = normalizeVector(scale);\n  const positions = React.useMemo(() => Float32Array.from(Array.from({\n    length: count\n  }, () => _scale.map(THREE.MathUtils.randFloatSpread)).flat()), [count, ..._scale]);\n  const sizes = usePropAsIsOrAsAttribute(count, size, Math.random);\n  const opacities = usePropAsIsOrAsAttribute(count, opacity);\n  const speeds = usePropAsIsOrAsAttribute(count, speed);\n  const noises = usePropAsIsOrAsAttribute(count * 3, noise);\n  const colors = usePropAsIsOrAsAttribute(color === undefined ? count * 3 : count, !isFloat32Array(color) ? new THREE.Color(color) : color, () => 1);\n  useFrame(state => {\n    if (ref.current && ref.current.material) ref.current.material.time = state.clock.elapsedTime;\n  });\n  React.useImperativeHandle(forwardRef, () => ref.current, []);\n  return /*#__PURE__*/React.createElement(\"points\", _extends({\n    key: `particle-${count}-${JSON.stringify(scale)}`\n  }, props, {\n    ref: ref\n  }), /*#__PURE__*/React.createElement(\"bufferGeometry\", null, /*#__PURE__*/React.createElement(\"bufferAttribute\", {\n    attach: \"attributes-position\",\n    args: [positions, 3]\n  }), /*#__PURE__*/React.createElement(\"bufferAttribute\", {\n    attach: \"attributes-size\",\n    args: [sizes, 1]\n  }), /*#__PURE__*/React.createElement(\"bufferAttribute\", {\n    attach: \"attributes-opacity\",\n    args: [opacities, 1]\n  }), /*#__PURE__*/React.createElement(\"bufferAttribute\", {\n    attach: \"attributes-speed\",\n    args: [speeds, 1]\n  }), /*#__PURE__*/React.createElement(\"bufferAttribute\", {\n    attach: \"attributes-color\",\n    args: [colors, 3]\n  }), /*#__PURE__*/React.createElement(\"bufferAttribute\", {\n    attach: \"attributes-noise\",\n    args: [noises, 3]\n  })), children ? children : /*#__PURE__*/React.createElement(\"sparklesImplMaterial\", {\n    transparent: true,\n    pixelRatio: dpr,\n    depthWrite: false\n  }));\n});\nexport { Sparkles };", "map": {"version": 3, "names": ["_extends", "React", "THREE", "extend", "useThree", "useFrame", "version", "SparklesImplMaterial", "ShaderMaterial", "constructor", "uniforms", "time", "value", "pixelRatio", "vertexShader", "fragmentShader", "isFloat32Array", "def", "Float32Array", "expandColor", "v", "r", "g", "b", "isVector", "Vector2", "Vector3", "Vector4", "normalizeVector", "Array", "isArray", "toArray", "usePropAsIsOrAsAttribute", "count", "prop", "<PERSON><PERSON><PERSON><PERSON>", "useMemo", "undefined", "Color", "a", "from", "length", "flat", "<PERSON><PERSON><PERSON>", "forwardRef", "noise", "speed", "opacity", "scale", "size", "color", "children", "props", "ref", "useRef", "dpr", "state", "viewport", "_scale", "positions", "map", "MathUtils", "randFloatSpread", "sizes", "Math", "random", "opacities", "speeds", "noises", "colors", "current", "material", "clock", "elapsedTime", "useImperativeHandle", "createElement", "key", "JSON", "stringify", "attach", "args", "transparent", "depthWrite"], "sources": ["C:/Users/<USER>/Documents/Apps_Creations/SiahMan_Racing4Speed_WebGame/siah-man-racing/node_modules/@react-three/drei/core/Sparkles.js"], "sourcesContent": ["import _extends from '@babel/runtime/helpers/esm/extends';\nimport * as React from 'react';\nimport * as THREE from 'three';\nimport { extend, useThree, useFrame } from '@react-three/fiber';\nimport { version } from '../helpers/constants.js';\n\nclass SparklesImplMaterial extends THREE.ShaderMaterial {\n  constructor() {\n    super({\n      uniforms: {\n        time: {\n          value: 0\n        },\n        pixelRatio: {\n          value: 1\n        }\n      },\n      vertexShader: /* glsl */`\n        uniform float pixelRatio;\n        uniform float time;\n        attribute float size;  \n        attribute float speed;  \n        attribute float opacity;\n        attribute vec3 noise;\n        attribute vec3 color;\n        varying vec3 vColor;\n        varying float vOpacity;\n\n        void main() {\n          vec4 modelPosition = modelMatrix * vec4(position, 1.0);\n          modelPosition.y += sin(time * speed + modelPosition.x * noise.x * 100.0) * 0.2;\n          modelPosition.z += cos(time * speed + modelPosition.x * noise.y * 100.0) * 0.2;\n          modelPosition.x += cos(time * speed + modelPosition.x * noise.z * 100.0) * 0.2;\n          vec4 viewPosition = viewMatrix * modelPosition;\n          vec4 projectionPostion = projectionMatrix * viewPosition;\n          gl_Position = projectionPostion;\n          gl_PointSize = size * 25. * pixelRatio;\n          gl_PointSize *= (1.0 / - viewPosition.z);\n          vColor = color;\n          vOpacity = opacity;\n        }\n      `,\n      fragmentShader: /* glsl */`\n        varying vec3 vColor;\n        varying float vOpacity;\n        void main() {\n          float distanceToCenter = distance(gl_PointCoord, vec2(0.5));\n          float strength = 0.05 / distanceToCenter - 0.1;\n          gl_FragColor = vec4(vColor, strength * vOpacity);\n          #include <tonemapping_fragment>\n          #include <${version >= 154 ? 'colorspace_fragment' : 'encodings_fragment'}>\n        }\n      `\n    });\n  }\n  get time() {\n    return this.uniforms.time.value;\n  }\n  set time(value) {\n    this.uniforms.time.value = value;\n  }\n  get pixelRatio() {\n    return this.uniforms.pixelRatio.value;\n  }\n  set pixelRatio(value) {\n    this.uniforms.pixelRatio.value = value;\n  }\n}\nconst isFloat32Array = def => def && def.constructor === Float32Array;\nconst expandColor = v => [v.r, v.g, v.b];\nconst isVector = v => v instanceof THREE.Vector2 || v instanceof THREE.Vector3 || v instanceof THREE.Vector4;\nconst normalizeVector = v => {\n  if (Array.isArray(v)) return v;else if (isVector(v)) return v.toArray();\n  return [v, v, v];\n};\nfunction usePropAsIsOrAsAttribute(count, prop, setDefault) {\n  return React.useMemo(() => {\n    if (prop !== undefined) {\n      if (isFloat32Array(prop)) {\n        return prop;\n      } else {\n        if (prop instanceof THREE.Color) {\n          const a = Array.from({\n            length: count * 3\n          }, () => expandColor(prop)).flat();\n          return Float32Array.from(a);\n        } else if (isVector(prop) || Array.isArray(prop)) {\n          const a = Array.from({\n            length: count * 3\n          }, () => normalizeVector(prop)).flat();\n          return Float32Array.from(a);\n        }\n        return Float32Array.from({\n          length: count\n        }, () => prop);\n      }\n    }\n    return Float32Array.from({\n      length: count\n    }, setDefault);\n  }, [prop]);\n}\nconst Sparkles = /* @__PURE__ */React.forwardRef(({\n  noise = 1,\n  count = 100,\n  speed = 1,\n  opacity = 1,\n  scale = 1,\n  size,\n  color,\n  children,\n  ...props\n}, forwardRef) => {\n  React.useMemo(() => extend({\n    SparklesImplMaterial\n  }), []);\n  const ref = React.useRef(null);\n  const dpr = useThree(state => state.viewport.dpr);\n  const _scale = normalizeVector(scale);\n  const positions = React.useMemo(() => Float32Array.from(Array.from({\n    length: count\n  }, () => _scale.map(THREE.MathUtils.randFloatSpread)).flat()), [count, ..._scale]);\n  const sizes = usePropAsIsOrAsAttribute(count, size, Math.random);\n  const opacities = usePropAsIsOrAsAttribute(count, opacity);\n  const speeds = usePropAsIsOrAsAttribute(count, speed);\n  const noises = usePropAsIsOrAsAttribute(count * 3, noise);\n  const colors = usePropAsIsOrAsAttribute(color === undefined ? count * 3 : count, !isFloat32Array(color) ? new THREE.Color(color) : color, () => 1);\n  useFrame(state => {\n    if (ref.current && ref.current.material) ref.current.material.time = state.clock.elapsedTime;\n  });\n  React.useImperativeHandle(forwardRef, () => ref.current, []);\n  return /*#__PURE__*/React.createElement(\"points\", _extends({\n    key: `particle-${count}-${JSON.stringify(scale)}`\n  }, props, {\n    ref: ref\n  }), /*#__PURE__*/React.createElement(\"bufferGeometry\", null, /*#__PURE__*/React.createElement(\"bufferAttribute\", {\n    attach: \"attributes-position\",\n    args: [positions, 3]\n  }), /*#__PURE__*/React.createElement(\"bufferAttribute\", {\n    attach: \"attributes-size\",\n    args: [sizes, 1]\n  }), /*#__PURE__*/React.createElement(\"bufferAttribute\", {\n    attach: \"attributes-opacity\",\n    args: [opacities, 1]\n  }), /*#__PURE__*/React.createElement(\"bufferAttribute\", {\n    attach: \"attributes-speed\",\n    args: [speeds, 1]\n  }), /*#__PURE__*/React.createElement(\"bufferAttribute\", {\n    attach: \"attributes-color\",\n    args: [colors, 3]\n  }), /*#__PURE__*/React.createElement(\"bufferAttribute\", {\n    attach: \"attributes-noise\",\n    args: [noises, 3]\n  })), children ? children : /*#__PURE__*/React.createElement(\"sparklesImplMaterial\", {\n    transparent: true,\n    pixelRatio: dpr,\n    depthWrite: false\n  }));\n});\n\nexport { Sparkles };\n"], "mappings": "AAAA,OAAOA,QAAQ,MAAM,oCAAoC;AACzD,OAAO,KAAKC,KAAK,MAAM,OAAO;AAC9B,OAAO,KAAKC,KAAK,MAAM,OAAO;AAC9B,SAASC,MAAM,EAAEC,QAAQ,EAAEC,QAAQ,QAAQ,oBAAoB;AAC/D,SAASC,OAAO,QAAQ,yBAAyB;AAEjD,MAAMC,oBAAoB,SAASL,KAAK,CAACM,cAAc,CAAC;EACtDC,WAAWA,CAAA,EAAG;IACZ,KAAK,CAAC;MACJC,QAAQ,EAAE;QACRC,IAAI,EAAE;UACJC,KAAK,EAAE;QACT,CAAC;QACDC,UAAU,EAAE;UACVD,KAAK,EAAE;QACT;MACF,CAAC;MACDE,YAAY,EAAE,UAAU;AAC9B;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO;MACDC,cAAc,EAAE,UAAU;AAChC;AACA;AACA;AACA;AACA;AACA;AACA;AACA,sBAAsBT,OAAO,IAAI,GAAG,GAAG,qBAAqB,GAAG,oBAAoB;AACnF;AACA;IACI,CAAC,CAAC;EACJ;EACA,IAAIK,IAAIA,CAAA,EAAG;IACT,OAAO,IAAI,CAACD,QAAQ,CAACC,IAAI,CAACC,KAAK;EACjC;EACA,IAAID,IAAIA,CAACC,KAAK,EAAE;IACd,IAAI,CAACF,QAAQ,CAACC,IAAI,CAACC,KAAK,GAAGA,KAAK;EAClC;EACA,IAAIC,UAAUA,CAAA,EAAG;IACf,OAAO,IAAI,CAACH,QAAQ,CAACG,UAAU,CAACD,KAAK;EACvC;EACA,IAAIC,UAAUA,CAACD,KAAK,EAAE;IACpB,IAAI,CAACF,QAAQ,CAACG,UAAU,CAACD,KAAK,GAAGA,KAAK;EACxC;AACF;AACA,MAAMI,cAAc,GAAGC,GAAG,IAAIA,GAAG,IAAIA,GAAG,CAACR,WAAW,KAAKS,YAAY;AACrE,MAAMC,WAAW,GAAGC,CAAC,IAAI,CAACA,CAAC,CAACC,CAAC,EAAED,CAAC,CAACE,CAAC,EAAEF,CAAC,CAACG,CAAC,CAAC;AACxC,MAAMC,QAAQ,GAAGJ,CAAC,IAAIA,CAAC,YAAYlB,KAAK,CAACuB,OAAO,IAAIL,CAAC,YAAYlB,KAAK,CAACwB,OAAO,IAAIN,CAAC,YAAYlB,KAAK,CAACyB,OAAO;AAC5G,MAAMC,eAAe,GAAGR,CAAC,IAAI;EAC3B,IAAIS,KAAK,CAACC,OAAO,CAACV,CAAC,CAAC,EAAE,OAAOA,CAAC,CAAC,KAAK,IAAII,QAAQ,CAACJ,CAAC,CAAC,EAAE,OAAOA,CAAC,CAACW,OAAO,CAAC,CAAC;EACvE,OAAO,CAACX,CAAC,EAAEA,CAAC,EAAEA,CAAC,CAAC;AAClB,CAAC;AACD,SAASY,wBAAwBA,CAACC,KAAK,EAAEC,IAAI,EAAEC,UAAU,EAAE;EACzD,OAAOlC,KAAK,CAACmC,OAAO,CAAC,MAAM;IACzB,IAAIF,IAAI,KAAKG,SAAS,EAAE;MACtB,IAAIrB,cAAc,CAACkB,IAAI,CAAC,EAAE;QACxB,OAAOA,IAAI;MACb,CAAC,MAAM;QACL,IAAIA,IAAI,YAAYhC,KAAK,CAACoC,KAAK,EAAE;UAC/B,MAAMC,CAAC,GAAGV,KAAK,CAACW,IAAI,CAAC;YACnBC,MAAM,EAAER,KAAK,GAAG;UAClB,CAAC,EAAE,MAAMd,WAAW,CAACe,IAAI,CAAC,CAAC,CAACQ,IAAI,CAAC,CAAC;UAClC,OAAOxB,YAAY,CAACsB,IAAI,CAACD,CAAC,CAAC;QAC7B,CAAC,MAAM,IAAIf,QAAQ,CAACU,IAAI,CAAC,IAAIL,KAAK,CAACC,OAAO,CAACI,IAAI,CAAC,EAAE;UAChD,MAAMK,CAAC,GAAGV,KAAK,CAACW,IAAI,CAAC;YACnBC,MAAM,EAAER,KAAK,GAAG;UAClB,CAAC,EAAE,MAAML,eAAe,CAACM,IAAI,CAAC,CAAC,CAACQ,IAAI,CAAC,CAAC;UACtC,OAAOxB,YAAY,CAACsB,IAAI,CAACD,CAAC,CAAC;QAC7B;QACA,OAAOrB,YAAY,CAACsB,IAAI,CAAC;UACvBC,MAAM,EAAER;QACV,CAAC,EAAE,MAAMC,IAAI,CAAC;MAChB;IACF;IACA,OAAOhB,YAAY,CAACsB,IAAI,CAAC;MACvBC,MAAM,EAAER;IACV,CAAC,EAAEE,UAAU,CAAC;EAChB,CAAC,EAAE,CAACD,IAAI,CAAC,CAAC;AACZ;AACA,MAAMS,QAAQ,GAAG,eAAe1C,KAAK,CAAC2C,UAAU,CAAC,CAAC;EAChDC,KAAK,GAAG,CAAC;EACTZ,KAAK,GAAG,GAAG;EACXa,KAAK,GAAG,CAAC;EACTC,OAAO,GAAG,CAAC;EACXC,KAAK,GAAG,CAAC;EACTC,IAAI;EACJC,KAAK;EACLC,QAAQ;EACR,GAAGC;AACL,CAAC,EAAER,UAAU,KAAK;EAChB3C,KAAK,CAACmC,OAAO,CAAC,MAAMjC,MAAM,CAAC;IACzBI;EACF,CAAC,CAAC,EAAE,EAAE,CAAC;EACP,MAAM8C,GAAG,GAAGpD,KAAK,CAACqD,MAAM,CAAC,IAAI,CAAC;EAC9B,MAAMC,GAAG,GAAGnD,QAAQ,CAACoD,KAAK,IAAIA,KAAK,CAACC,QAAQ,CAACF,GAAG,CAAC;EACjD,MAAMG,MAAM,GAAG9B,eAAe,CAACoB,KAAK,CAAC;EACrC,MAAMW,SAAS,GAAG1D,KAAK,CAACmC,OAAO,CAAC,MAAMlB,YAAY,CAACsB,IAAI,CAACX,KAAK,CAACW,IAAI,CAAC;IACjEC,MAAM,EAAER;EACV,CAAC,EAAE,MAAMyB,MAAM,CAACE,GAAG,CAAC1D,KAAK,CAAC2D,SAAS,CAACC,eAAe,CAAC,CAAC,CAACpB,IAAI,CAAC,CAAC,CAAC,EAAE,CAACT,KAAK,EAAE,GAAGyB,MAAM,CAAC,CAAC;EAClF,MAAMK,KAAK,GAAG/B,wBAAwB,CAACC,KAAK,EAAEgB,IAAI,EAAEe,IAAI,CAACC,MAAM,CAAC;EAChE,MAAMC,SAAS,GAAGlC,wBAAwB,CAACC,KAAK,EAAEc,OAAO,CAAC;EAC1D,MAAMoB,MAAM,GAAGnC,wBAAwB,CAACC,KAAK,EAAEa,KAAK,CAAC;EACrD,MAAMsB,MAAM,GAAGpC,wBAAwB,CAACC,KAAK,GAAG,CAAC,EAAEY,KAAK,CAAC;EACzD,MAAMwB,MAAM,GAAGrC,wBAAwB,CAACkB,KAAK,KAAKb,SAAS,GAAGJ,KAAK,GAAG,CAAC,GAAGA,KAAK,EAAE,CAACjB,cAAc,CAACkC,KAAK,CAAC,GAAG,IAAIhD,KAAK,CAACoC,KAAK,CAACY,KAAK,CAAC,GAAGA,KAAK,EAAE,MAAM,CAAC,CAAC;EAClJ7C,QAAQ,CAACmD,KAAK,IAAI;IAChB,IAAIH,GAAG,CAACiB,OAAO,IAAIjB,GAAG,CAACiB,OAAO,CAACC,QAAQ,EAAElB,GAAG,CAACiB,OAAO,CAACC,QAAQ,CAAC5D,IAAI,GAAG6C,KAAK,CAACgB,KAAK,CAACC,WAAW;EAC9F,CAAC,CAAC;EACFxE,KAAK,CAACyE,mBAAmB,CAAC9B,UAAU,EAAE,MAAMS,GAAG,CAACiB,OAAO,EAAE,EAAE,CAAC;EAC5D,OAAO,aAAarE,KAAK,CAAC0E,aAAa,CAAC,QAAQ,EAAE3E,QAAQ,CAAC;IACzD4E,GAAG,EAAE,YAAY3C,KAAK,IAAI4C,IAAI,CAACC,SAAS,CAAC9B,KAAK,CAAC;EACjD,CAAC,EAAEI,KAAK,EAAE;IACRC,GAAG,EAAEA;EACP,CAAC,CAAC,EAAE,aAAapD,KAAK,CAAC0E,aAAa,CAAC,gBAAgB,EAAE,IAAI,EAAE,aAAa1E,KAAK,CAAC0E,aAAa,CAAC,iBAAiB,EAAE;IAC/GI,MAAM,EAAE,qBAAqB;IAC7BC,IAAI,EAAE,CAACrB,SAAS,EAAE,CAAC;EACrB,CAAC,CAAC,EAAE,aAAa1D,KAAK,CAAC0E,aAAa,CAAC,iBAAiB,EAAE;IACtDI,MAAM,EAAE,iBAAiB;IACzBC,IAAI,EAAE,CAACjB,KAAK,EAAE,CAAC;EACjB,CAAC,CAAC,EAAE,aAAa9D,KAAK,CAAC0E,aAAa,CAAC,iBAAiB,EAAE;IACtDI,MAAM,EAAE,oBAAoB;IAC5BC,IAAI,EAAE,CAACd,SAAS,EAAE,CAAC;EACrB,CAAC,CAAC,EAAE,aAAajE,KAAK,CAAC0E,aAAa,CAAC,iBAAiB,EAAE;IACtDI,MAAM,EAAE,kBAAkB;IAC1BC,IAAI,EAAE,CAACb,MAAM,EAAE,CAAC;EAClB,CAAC,CAAC,EAAE,aAAalE,KAAK,CAAC0E,aAAa,CAAC,iBAAiB,EAAE;IACtDI,MAAM,EAAE,kBAAkB;IAC1BC,IAAI,EAAE,CAACX,MAAM,EAAE,CAAC;EAClB,CAAC,CAAC,EAAE,aAAapE,KAAK,CAAC0E,aAAa,CAAC,iBAAiB,EAAE;IACtDI,MAAM,EAAE,kBAAkB;IAC1BC,IAAI,EAAE,CAACZ,MAAM,EAAE,CAAC;EAClB,CAAC,CAAC,CAAC,EAAEjB,QAAQ,GAAGA,QAAQ,GAAG,aAAalD,KAAK,CAAC0E,aAAa,CAAC,sBAAsB,EAAE;IAClFM,WAAW,EAAE,IAAI;IACjBpE,UAAU,EAAE0C,GAAG;IACf2B,UAAU,EAAE;EACd,CAAC,CAAC,CAAC;AACL,CAAC,CAAC;AAEF,SAASvC,QAAQ", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}