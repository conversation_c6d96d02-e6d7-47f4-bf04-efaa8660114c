{"ast": null, "code": "// Split strategy constants\nexport const CENTER = 0;\nexport const AVERAGE = 1;\nexport const SAH = 2;\n\n// Traversal constants\nexport const NOT_INTERSECTED = 0;\nexport const INTERSECTED = 1;\nexport const CONTAINED = 2;\n\n// SAH cost constants\n// TODO: hone these costs more. The relative difference between them should be the\n// difference in measured time to perform a triangle intersection vs traversing\n// bounds.\nexport const TRIANGLE_INTERSECT_COST = 1.25;\nexport const TRAVERSAL_COST = 1;\n\n// Build constants\nexport const BYTES_PER_NODE = 6 * 4 + 4 + 4;\nexport const IS_LEAFNODE_FLAG = 0xFFFF;\n\n// EPSILON for computing floating point error during build\n// https://en.wikipedia.org/wiki/Machine_epsilon#Values_for_standard_hardware_floating_point_arithmetics\nexport const FLOAT32_EPSILON = Math.pow(2, -24);\nexport const SKIP_GENERATION = Symbol('SKIP_GENERATION');", "map": {"version": 3, "names": ["CENTER", "AVERAGE", "SAH", "NOT_INTERSECTED", "INTERSECTED", "CONTAINED", "TRIANGLE_INTERSECT_COST", "TRAVERSAL_COST", "BYTES_PER_NODE", "IS_LEAFNODE_FLAG", "FLOAT32_EPSILON", "Math", "pow", "SKIP_GENERATION", "Symbol"], "sources": ["C:/Users/<USER>/Documents/Apps_Creations/SiahMan_Racing4Speed_WebGame/siah-man-racing/node_modules/three-mesh-bvh/src/core/Constants.js"], "sourcesContent": ["// Split strategy constants\nexport const CENTER = 0;\nexport const AVERAGE = 1;\nexport const SAH = 2;\n\n// Traversal constants\nexport const NOT_INTERSECTED = 0;\nexport const INTERSECTED = 1;\nexport const CONTAINED = 2;\n\n// SAH cost constants\n// TODO: hone these costs more. The relative difference between them should be the\n// difference in measured time to perform a triangle intersection vs traversing\n// bounds.\nexport const TRIANGLE_INTERSECT_COST = 1.25;\nexport const TRAVERSAL_COST = 1;\n\n\n// Build constants\nexport const BYTES_PER_NODE = 6 * 4 + 4 + 4;\nexport const IS_LEAFNODE_FLAG = 0xFFFF;\n\n// EPSILON for computing floating point error during build\n// https://en.wikipedia.org/wiki/Machine_epsilon#Values_for_standard_hardware_floating_point_arithmetics\nexport const FLOAT32_EPSILON = Math.pow( 2, - 24 );\n\nexport const SKIP_GENERATION = Symbol( 'SKIP_GENERATION' );\n"], "mappings": "AAAA;AACA,OAAO,MAAMA,MAAM,GAAG,CAAC;AACvB,OAAO,MAAMC,OAAO,GAAG,CAAC;AACxB,OAAO,MAAMC,GAAG,GAAG,CAAC;;AAEpB;AACA,OAAO,MAAMC,eAAe,GAAG,CAAC;AAChC,OAAO,MAAMC,WAAW,GAAG,CAAC;AAC5B,OAAO,MAAMC,SAAS,GAAG,CAAC;;AAE1B;AACA;AACA;AACA;AACA,OAAO,MAAMC,uBAAuB,GAAG,IAAI;AAC3C,OAAO,MAAMC,cAAc,GAAG,CAAC;;AAG/B;AACA,OAAO,MAAMC,cAAc,GAAG,CAAC,GAAG,CAAC,GAAG,CAAC,GAAG,CAAC;AAC3C,OAAO,MAAMC,gBAAgB,GAAG,MAAM;;AAEtC;AACA;AACA,OAAO,MAAMC,eAAe,GAAGC,IAAI,CAACC,GAAG,CAAE,CAAC,EAAE,CAAE,EAAG,CAAC;AAElD,OAAO,MAAMC,eAAe,GAAGC,MAAM,CAAE,iBAAkB,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}