{"ast": null, "code": "import * as THREE from 'three';\nconst tV0 = /* @__PURE__ */new THREE.Vector3();\nconst tV1 = /* @__PURE__ */new THREE.Vector3();\nconst tV2 = /* @__PURE__ */new THREE.Vector3();\nconst getPoint2 = (point3, camera, size) => {\n  const widthHalf = size.width / 2;\n  const heightHalf = size.height / 2;\n  camera.updateMatrixWorld(false);\n  const vector = point3.project(camera);\n  vector.x = vector.x * widthHalf + widthHalf;\n  vector.y = -(vector.y * heightHalf) + heightHalf;\n  return vector;\n};\nconst getPoint3 = (point2, camera, size, zValue = 1) => {\n  const vector = tV0.set(point2.x / size.width * 2 - 1, -(point2.y / size.height) * 2 + 1, zValue);\n  vector.unproject(camera);\n  return vector;\n};\nconst calculateScaleFactor = (point3, radiusPx, camera, size) => {\n  const point2 = getPoint2(tV2.copy(point3), camera, size);\n  let scale = 0;\n  for (let i = 0; i < 2; ++i) {\n    const point2off = tV1.copy(point2).setComponent(i, point2.getComponent(i) + radiusPx);\n    const point3off = getPoint3(point2off, camera, size, point2off.z);\n    scale = Math.max(scale, point3.distanceTo(point3off));\n  }\n  return scale;\n};\nexport { calculateScaleFactor };", "map": {"version": 3, "names": ["THREE", "tV0", "Vector3", "tV1", "tV2", "getPoint2", "point3", "camera", "size", "widthHalf", "width", "heightHalf", "height", "updateMatrixWorld", "vector", "project", "x", "y", "getPoint3", "point2", "zValue", "set", "unproject", "calculateScaleFactor", "radiusPx", "copy", "scale", "i", "point2off", "setComponent", "getComponent", "point3off", "z", "Math", "max", "distanceTo"], "sources": ["C:/Users/<USER>/Documents/Apps_Creations/SiahMan_Racing4Speed_WebGame/siah-man-racing/node_modules/@react-three/drei/core/calculateScaleFactor.js"], "sourcesContent": ["import * as THREE from 'three';\n\nconst tV0 = /* @__PURE__ */new THREE.Vector3();\nconst tV1 = /* @__PURE__ */new THREE.Vector3();\nconst tV2 = /* @__PURE__ */new THREE.Vector3();\nconst getPoint2 = (point3, camera, size) => {\n  const widthHalf = size.width / 2;\n  const heightHalf = size.height / 2;\n  camera.updateMatrixWorld(false);\n  const vector = point3.project(camera);\n  vector.x = vector.x * widthHalf + widthHalf;\n  vector.y = -(vector.y * heightHalf) + heightHalf;\n  return vector;\n};\nconst getPoint3 = (point2, camera, size, zValue = 1) => {\n  const vector = tV0.set(point2.x / size.width * 2 - 1, -(point2.y / size.height) * 2 + 1, zValue);\n  vector.unproject(camera);\n  return vector;\n};\nconst calculateScaleFactor = (point3, radiusPx, camera, size) => {\n  const point2 = getPoint2(tV2.copy(point3), camera, size);\n  let scale = 0;\n  for (let i = 0; i < 2; ++i) {\n    const point2off = tV1.copy(point2).setComponent(i, point2.getComponent(i) + radiusPx);\n    const point3off = getPoint3(point2off, camera, size, point2off.z);\n    scale = Math.max(scale, point3.distanceTo(point3off));\n  }\n  return scale;\n};\n\nexport { calculateScaleFactor };\n"], "mappings": "AAAA,OAAO,KAAKA,KAAK,MAAM,OAAO;AAE9B,MAAMC,GAAG,GAAG,eAAe,IAAID,KAAK,CAACE,OAAO,CAAC,CAAC;AAC9C,MAAMC,GAAG,GAAG,eAAe,IAAIH,KAAK,CAACE,OAAO,CAAC,CAAC;AAC9C,MAAME,GAAG,GAAG,eAAe,IAAIJ,KAAK,CAACE,OAAO,CAAC,CAAC;AAC9C,MAAMG,SAAS,GAAGA,CAACC,MAAM,EAAEC,MAAM,EAAEC,IAAI,KAAK;EAC1C,MAAMC,SAAS,GAAGD,IAAI,CAACE,KAAK,GAAG,CAAC;EAChC,MAAMC,UAAU,GAAGH,IAAI,CAACI,MAAM,GAAG,CAAC;EAClCL,MAAM,CAACM,iBAAiB,CAAC,KAAK,CAAC;EAC/B,MAAMC,MAAM,GAAGR,MAAM,CAACS,OAAO,CAACR,MAAM,CAAC;EACrCO,MAAM,CAACE,CAAC,GAAGF,MAAM,CAACE,CAAC,GAAGP,SAAS,GAAGA,SAAS;EAC3CK,MAAM,CAACG,CAAC,GAAG,EAAEH,MAAM,CAACG,CAAC,GAAGN,UAAU,CAAC,GAAGA,UAAU;EAChD,OAAOG,MAAM;AACf,CAAC;AACD,MAAMI,SAAS,GAAGA,CAACC,MAAM,EAAEZ,MAAM,EAAEC,IAAI,EAAEY,MAAM,GAAG,CAAC,KAAK;EACtD,MAAMN,MAAM,GAAGb,GAAG,CAACoB,GAAG,CAACF,MAAM,CAACH,CAAC,GAAGR,IAAI,CAACE,KAAK,GAAG,CAAC,GAAG,CAAC,EAAE,EAAES,MAAM,CAACF,CAAC,GAAGT,IAAI,CAACI,MAAM,CAAC,GAAG,CAAC,GAAG,CAAC,EAAEQ,MAAM,CAAC;EAChGN,MAAM,CAACQ,SAAS,CAACf,MAAM,CAAC;EACxB,OAAOO,MAAM;AACf,CAAC;AACD,MAAMS,oBAAoB,GAAGA,CAACjB,MAAM,EAAEkB,QAAQ,EAAEjB,MAAM,EAAEC,IAAI,KAAK;EAC/D,MAAMW,MAAM,GAAGd,SAAS,CAACD,GAAG,CAACqB,IAAI,CAACnB,MAAM,CAAC,EAAEC,MAAM,EAAEC,IAAI,CAAC;EACxD,IAAIkB,KAAK,GAAG,CAAC;EACb,KAAK,IAAIC,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAG,CAAC,EAAE,EAAEA,CAAC,EAAE;IAC1B,MAAMC,SAAS,GAAGzB,GAAG,CAACsB,IAAI,CAACN,MAAM,CAAC,CAACU,YAAY,CAACF,CAAC,EAAER,MAAM,CAACW,YAAY,CAACH,CAAC,CAAC,GAAGH,QAAQ,CAAC;IACrF,MAAMO,SAAS,GAAGb,SAAS,CAACU,SAAS,EAAErB,MAAM,EAAEC,IAAI,EAAEoB,SAAS,CAACI,CAAC,CAAC;IACjEN,KAAK,GAAGO,IAAI,CAACC,GAAG,CAACR,KAAK,EAAEpB,MAAM,CAAC6B,UAAU,CAACJ,SAAS,CAAC,CAAC;EACvD;EACA,OAAOL,KAAK;AACd,CAAC;AAED,SAASH,oBAAoB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}