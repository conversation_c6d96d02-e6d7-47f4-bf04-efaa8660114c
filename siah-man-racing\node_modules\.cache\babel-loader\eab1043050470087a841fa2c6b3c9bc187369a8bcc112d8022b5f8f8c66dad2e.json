{"ast": null, "code": "var __defProp = Object.defineProperty;\nvar __defNormalProp = (obj, key, value) => key in obj ? __defProp(obj, key, {\n  enumerable: true,\n  configurable: true,\n  writable: true,\n  value\n}) : obj[key] = value;\nvar __publicField = (obj, key, value) => {\n  __defNormalProp(obj, typeof key !== \"symbol\" ? key + \"\" : key, value);\n  return value;\n};\nimport * as THREE from \"three\";\nfunction memcpy(src, srcOffset, dst, dstOffset, length) {\n  let i;\n  src = src.subarray || src.slice ? src : src.buffer;\n  dst = dst.subarray || dst.slice ? dst : dst.buffer;\n  src = srcOffset ? src.subarray ? src.subarray(srcOffset, length && srcOffset + length) : src.slice(srcOffset, length && srcOffset + length) : src;\n  if (dst.set) {\n    dst.set(src, dstOffset);\n  } else {\n    for (i = 0; i < src.length; i++) dst[i + dstOffset] = src[i];\n  }\n  return dst;\n}\nfunction convertPoints(points) {\n  if (points instanceof Float32Array) return points;\n  if (points instanceof THREE.BufferGeometry) return points.getAttribute(\"position\").array;\n  return points.map(p => {\n    const isArray = Array.isArray(p);\n    return p instanceof THREE.Vector3 ? [p.x, p.y, p.z] : p instanceof THREE.Vector2 ? [p.x, p.y, 0] : isArray && p.length === 3 ? [p[0], p[1], p[2]] : isArray && p.length === 2 ? [p[0], p[1], 0] : p;\n  }).flat();\n}\nclass MeshLineGeometry extends THREE.BufferGeometry {\n  constructor() {\n    super();\n    __publicField(this, \"type\", \"MeshLine\");\n    __publicField(this, \"isMeshLine\", true);\n    __publicField(this, \"positions\", []);\n    __publicField(this, \"previous\", []);\n    __publicField(this, \"next\", []);\n    __publicField(this, \"side\", []);\n    __publicField(this, \"width\", []);\n    __publicField(this, \"indices_array\", []);\n    __publicField(this, \"uvs\", []);\n    __publicField(this, \"counters\", []);\n    __publicField(this, \"widthCallback\", null);\n    __publicField(this, \"_attributes\");\n    __publicField(this, \"_points\", []);\n    __publicField(this, \"points\");\n    __publicField(this, \"matrixWorld\", new THREE.Matrix4());\n    Object.defineProperties(this, {\n      points: {\n        enumerable: true,\n        get() {\n          return this._points;\n        },\n        set(value) {\n          this.setPoints(value, this.widthCallback);\n        }\n      }\n    });\n  }\n  setMatrixWorld(matrixWorld) {\n    this.matrixWorld = matrixWorld;\n  }\n  setPoints(points, wcb) {\n    points = convertPoints(points);\n    this._points = points;\n    this.widthCallback = wcb != null ? wcb : null;\n    this.positions = [];\n    this.counters = [];\n    if (points.length && points[0] instanceof THREE.Vector3) {\n      for (let j = 0; j < points.length; j++) {\n        const p = points[j];\n        const c = j / (points.length - 1);\n        this.positions.push(p.x, p.y, p.z);\n        this.positions.push(p.x, p.y, p.z);\n        this.counters.push(c);\n        this.counters.push(c);\n      }\n    } else {\n      for (let j = 0; j < points.length; j += 3) {\n        const c = j / (points.length - 1);\n        this.positions.push(points[j], points[j + 1], points[j + 2]);\n        this.positions.push(points[j], points[j + 1], points[j + 2]);\n        this.counters.push(c);\n        this.counters.push(c);\n      }\n    }\n    this.process();\n  }\n  compareV3(a, b) {\n    const aa = a * 6;\n    const ab = b * 6;\n    return this.positions[aa] === this.positions[ab] && this.positions[aa + 1] === this.positions[ab + 1] && this.positions[aa + 2] === this.positions[ab + 2];\n  }\n  copyV3(a) {\n    const aa = a * 6;\n    return [this.positions[aa], this.positions[aa + 1], this.positions[aa + 2]];\n  }\n  process() {\n    const l = this.positions.length / 6;\n    this.previous = [];\n    this.next = [];\n    this.side = [];\n    this.width = [];\n    this.indices_array = [];\n    this.uvs = [];\n    let w;\n    let v;\n    if (this.compareV3(0, l - 1)) {\n      v = this.copyV3(l - 2);\n    } else {\n      v = this.copyV3(0);\n    }\n    this.previous.push(v[0], v[1], v[2]);\n    this.previous.push(v[0], v[1], v[2]);\n    for (let j = 0; j < l; j++) {\n      this.side.push(1);\n      this.side.push(-1);\n      if (this.widthCallback) w = this.widthCallback(j / (l - 1));else w = 1;\n      this.width.push(w);\n      this.width.push(w);\n      this.uvs.push(j / (l - 1), 0);\n      this.uvs.push(j / (l - 1), 1);\n      if (j < l - 1) {\n        v = this.copyV3(j);\n        this.previous.push(v[0], v[1], v[2]);\n        this.previous.push(v[0], v[1], v[2]);\n        const n = j * 2;\n        this.indices_array.push(n, n + 1, n + 2);\n        this.indices_array.push(n + 2, n + 1, n + 3);\n      }\n      if (j > 0) {\n        v = this.copyV3(j);\n        this.next.push(v[0], v[1], v[2]);\n        this.next.push(v[0], v[1], v[2]);\n      }\n    }\n    if (this.compareV3(l - 1, 0)) {\n      v = this.copyV3(1);\n    } else {\n      v = this.copyV3(l - 1);\n    }\n    this.next.push(v[0], v[1], v[2]);\n    this.next.push(v[0], v[1], v[2]);\n    if (!this._attributes || this._attributes.position.count !== this.counters.length) {\n      this._attributes = {\n        position: new THREE.BufferAttribute(new Float32Array(this.positions), 3),\n        previous: new THREE.BufferAttribute(new Float32Array(this.previous), 3),\n        next: new THREE.BufferAttribute(new Float32Array(this.next), 3),\n        side: new THREE.BufferAttribute(new Float32Array(this.side), 1),\n        width: new THREE.BufferAttribute(new Float32Array(this.width), 1),\n        uv: new THREE.BufferAttribute(new Float32Array(this.uvs), 2),\n        index: new THREE.BufferAttribute(new Uint16Array(this.indices_array), 1),\n        counters: new THREE.BufferAttribute(new Float32Array(this.counters), 1)\n      };\n    } else {\n      this._attributes.position.copyArray(new Float32Array(this.positions));\n      this._attributes.position.needsUpdate = true;\n      this._attributes.previous.copyArray(new Float32Array(this.previous));\n      this._attributes.previous.needsUpdate = true;\n      this._attributes.next.copyArray(new Float32Array(this.next));\n      this._attributes.next.needsUpdate = true;\n      this._attributes.side.copyArray(new Float32Array(this.side));\n      this._attributes.side.needsUpdate = true;\n      this._attributes.width.copyArray(new Float32Array(this.width));\n      this._attributes.width.needsUpdate = true;\n      this._attributes.uv.copyArray(new Float32Array(this.uvs));\n      this._attributes.uv.needsUpdate = true;\n      this._attributes.index.copyArray(new Uint16Array(this.indices_array));\n      this._attributes.index.needsUpdate = true;\n    }\n    this.setAttribute(\"position\", this._attributes.position);\n    this.setAttribute(\"previous\", this._attributes.previous);\n    this.setAttribute(\"next\", this._attributes.next);\n    this.setAttribute(\"side\", this._attributes.side);\n    this.setAttribute(\"width\", this._attributes.width);\n    this.setAttribute(\"uv\", this._attributes.uv);\n    this.setAttribute(\"counters\", this._attributes.counters);\n    this.setAttribute(\"position\", this._attributes.position);\n    this.setAttribute(\"previous\", this._attributes.previous);\n    this.setAttribute(\"next\", this._attributes.next);\n    this.setAttribute(\"side\", this._attributes.side);\n    this.setAttribute(\"width\", this._attributes.width);\n    this.setAttribute(\"uv\", this._attributes.uv);\n    this.setAttribute(\"counters\", this._attributes.counters);\n    this.setIndex(this._attributes.index);\n    this.computeBoundingSphere();\n    this.computeBoundingBox();\n  }\n  advance({\n    x,\n    y,\n    z\n  }) {\n    const positions = this._attributes.position.array;\n    const previous = this._attributes.previous.array;\n    const next = this._attributes.next.array;\n    const l = positions.length;\n    memcpy(positions, 0, previous, 0, l);\n    memcpy(positions, 6, positions, 0, l - 6);\n    positions[l - 6] = x;\n    positions[l - 5] = y;\n    positions[l - 4] = z;\n    positions[l - 3] = x;\n    positions[l - 2] = y;\n    positions[l - 1] = z;\n    memcpy(positions, 6, next, 0, l - 6);\n    next[l - 6] = x;\n    next[l - 5] = y;\n    next[l - 4] = z;\n    next[l - 3] = x;\n    next[l - 2] = y;\n    next[l - 1] = z;\n    this._attributes.position.needsUpdate = true;\n    this._attributes.previous.needsUpdate = true;\n    this._attributes.next.needsUpdate = true;\n  }\n}\nconst vertexShader = `\n  #include <common>\n  #include <logdepthbuf_pars_vertex>\n  #include <fog_pars_vertex>\n  #include <clipping_planes_pars_vertex>\n\n  attribute vec3 previous;\n  attribute vec3 next;\n  attribute float side;\n  attribute float width;\n  attribute float counters;\n  \n  uniform vec2 resolution;\n  uniform float lineWidth;\n  uniform vec3 color;\n  uniform float opacity;\n  uniform float sizeAttenuation;\n  \n  varying vec2 vUV;\n  varying vec4 vColor;\n  varying float vCounters;\n  \n  vec2 fix(vec4 i, float aspect) {\n    vec2 res = i.xy / i.w;\n    res.x *= aspect;\n    return res;\n  }\n  \n  void main() {\n    float aspect = resolution.x / resolution.y;\n    vColor = vec4(color, opacity);\n    vUV = uv;\n    vCounters = counters;\n  \n    mat4 m = projectionMatrix * modelViewMatrix;\n    vec4 finalPosition = m * vec4(position, 1.0) * aspect;\n    vec4 prevPos = m * vec4(previous, 1.0);\n    vec4 nextPos = m * vec4(next, 1.0);\n  \n    vec2 currentP = fix(finalPosition, aspect);\n    vec2 prevP = fix(prevPos, aspect);\n    vec2 nextP = fix(nextPos, aspect);\n  \n    float w = lineWidth * width;\n  \n    vec2 dir;\n    if (nextP == currentP) dir = normalize(currentP - prevP);\n    else if (prevP == currentP) dir = normalize(nextP - currentP);\n    else {\n      vec2 dir1 = normalize(currentP - prevP);\n      vec2 dir2 = normalize(nextP - currentP);\n      dir = normalize(dir1 + dir2);\n  \n      vec2 perp = vec2(-dir1.y, dir1.x);\n      vec2 miter = vec2(-dir.y, dir.x);\n      //w = clamp(w / dot(miter, perp), 0., 4. * lineWidth * width);\n    }\n  \n    //vec2 normal = (cross(vec3(dir, 0.), vec3(0., 0., 1.))).xy;\n    vec4 normal = vec4(-dir.y, dir.x, 0., 1.);\n    normal.xy *= .5 * w;\n    //normal *= projectionMatrix;\n    if (sizeAttenuation == 0.) {\n      normal.xy *= finalPosition.w;\n      normal.xy /= (vec4(resolution, 0., 1.) * projectionMatrix).xy * aspect;\n    }\n  \n    finalPosition.xy += normal.xy * side;\n    gl_Position = finalPosition;\n    #include <logdepthbuf_vertex>\n    #include <fog_vertex>\n    vec4 mvPosition = modelViewMatrix * vec4(position, 1.0);\n    #include <clipping_planes_vertex>\n    #include <fog_vertex>\n  }\n`;\nconst version = /* @__PURE__ */(() => parseInt(THREE.REVISION.replace(/\\D+/g, \"\")))();\nconst colorspace_fragment = version >= 154 ? \"colorspace_fragment\" : \"encodings_fragment\";\nconst fragmentShader = `\n  #include <fog_pars_fragment>\n  #include <logdepthbuf_pars_fragment>\n  #include <clipping_planes_pars_fragment>\n  \n  uniform sampler2D map;\n  uniform sampler2D alphaMap;\n  uniform float useGradient;\n  uniform float useMap;\n  uniform float useAlphaMap;\n  uniform float useDash;\n  uniform float dashArray;\n  uniform float dashOffset;\n  uniform float dashRatio;\n  uniform float visibility;\n  uniform float alphaTest;\n  uniform vec2 repeat;\n  uniform vec3 gradient[2];\n  \n  varying vec2 vUV;\n  varying vec4 vColor;\n  varying float vCounters;\n  \n  void main() {\n    #include <logdepthbuf_fragment>\n    vec4 diffuseColor = vColor;\n    if (useGradient == 1.) diffuseColor = vec4(mix(gradient[0], gradient[1], vCounters), 1.0);\n    if (useMap == 1.) diffuseColor *= texture2D(map, vUV * repeat);\n    if (useAlphaMap == 1.) diffuseColor.a *= texture2D(alphaMap, vUV * repeat).a;\n    if (diffuseColor.a < alphaTest) discard;\n    if (useDash == 1.) diffuseColor.a *= ceil(mod(vCounters + dashOffset, dashArray) - (dashArray * dashRatio));\n    diffuseColor.a *= step(vCounters, visibility);\n    #include <clipping_planes_fragment>\n    gl_FragColor = diffuseColor;     \n    #include <fog_fragment>\n    #include <tonemapping_fragment>\n    #include <${colorspace_fragment}>\n  }\n`;\nclass MeshLineMaterial extends THREE.ShaderMaterial {\n  constructor(parameters) {\n    super({\n      uniforms: {\n        ...THREE.UniformsLib.fog,\n        lineWidth: {\n          value: 1\n        },\n        map: {\n          value: null\n        },\n        useMap: {\n          value: 0\n        },\n        alphaMap: {\n          value: null\n        },\n        useAlphaMap: {\n          value: 0\n        },\n        color: {\n          value: new THREE.Color(16777215)\n        },\n        gradient: {\n          value: [new THREE.Color(16711680), new THREE.Color(65280)]\n        },\n        opacity: {\n          value: 1\n        },\n        resolution: {\n          value: new THREE.Vector2(1, 1)\n        },\n        sizeAttenuation: {\n          value: 1\n        },\n        dashArray: {\n          value: 0\n        },\n        dashOffset: {\n          value: 0\n        },\n        dashRatio: {\n          value: 0.5\n        },\n        useDash: {\n          value: 0\n        },\n        useGradient: {\n          value: 0\n        },\n        visibility: {\n          value: 1\n        },\n        alphaTest: {\n          value: 0\n        },\n        repeat: {\n          value: new THREE.Vector2(1, 1)\n        }\n      },\n      vertexShader,\n      fragmentShader\n    });\n    __publicField(this, \"lineWidth\");\n    __publicField(this, \"map\");\n    __publicField(this, \"useMap\");\n    __publicField(this, \"alphaMap\");\n    __publicField(this, \"useAlphaMap\");\n    __publicField(this, \"color\");\n    __publicField(this, \"gradient\");\n    __publicField(this, \"resolution\");\n    __publicField(this, \"sizeAttenuation\");\n    __publicField(this, \"dashArray\");\n    __publicField(this, \"dashOffset\");\n    __publicField(this, \"dashRatio\");\n    __publicField(this, \"useDash\");\n    __publicField(this, \"useGradient\");\n    __publicField(this, \"visibility\");\n    __publicField(this, \"repeat\");\n    this.type = \"MeshLineMaterial\";\n    Object.defineProperties(this, {\n      lineWidth: {\n        enumerable: true,\n        get() {\n          return this.uniforms.lineWidth.value;\n        },\n        set(value) {\n          this.uniforms.lineWidth.value = value;\n        }\n      },\n      map: {\n        enumerable: true,\n        get() {\n          return this.uniforms.map.value;\n        },\n        set(value) {\n          this.uniforms.map.value = value;\n        }\n      },\n      useMap: {\n        enumerable: true,\n        get() {\n          return this.uniforms.useMap.value;\n        },\n        set(value) {\n          this.uniforms.useMap.value = value;\n        }\n      },\n      alphaMap: {\n        enumerable: true,\n        get() {\n          return this.uniforms.alphaMap.value;\n        },\n        set(value) {\n          this.uniforms.alphaMap.value = value;\n        }\n      },\n      useAlphaMap: {\n        enumerable: true,\n        get() {\n          return this.uniforms.useAlphaMap.value;\n        },\n        set(value) {\n          this.uniforms.useAlphaMap.value = value;\n        }\n      },\n      color: {\n        enumerable: true,\n        get() {\n          return this.uniforms.color.value;\n        },\n        set(value) {\n          this.uniforms.color.value = value;\n        }\n      },\n      gradient: {\n        enumerable: true,\n        get() {\n          return this.uniforms.gradient.value;\n        },\n        set(value) {\n          this.uniforms.gradient.value = value;\n        }\n      },\n      opacity: {\n        enumerable: true,\n        get() {\n          return this.uniforms.opacity.value;\n        },\n        set(value) {\n          this.uniforms.opacity.value = value;\n        }\n      },\n      resolution: {\n        enumerable: true,\n        get() {\n          return this.uniforms.resolution.value;\n        },\n        set(value) {\n          this.uniforms.resolution.value.copy(value);\n        }\n      },\n      sizeAttenuation: {\n        enumerable: true,\n        get() {\n          return this.uniforms.sizeAttenuation.value;\n        },\n        set(value) {\n          this.uniforms.sizeAttenuation.value = value;\n        }\n      },\n      dashArray: {\n        enumerable: true,\n        get() {\n          return this.uniforms.dashArray.value;\n        },\n        set(value) {\n          this.uniforms.dashArray.value = value;\n          this.useDash = value !== 0 ? 1 : 0;\n        }\n      },\n      dashOffset: {\n        enumerable: true,\n        get() {\n          return this.uniforms.dashOffset.value;\n        },\n        set(value) {\n          this.uniforms.dashOffset.value = value;\n        }\n      },\n      dashRatio: {\n        enumerable: true,\n        get() {\n          return this.uniforms.dashRatio.value;\n        },\n        set(value) {\n          this.uniforms.dashRatio.value = value;\n        }\n      },\n      useDash: {\n        enumerable: true,\n        get() {\n          return this.uniforms.useDash.value;\n        },\n        set(value) {\n          this.uniforms.useDash.value = value;\n        }\n      },\n      useGradient: {\n        enumerable: true,\n        get() {\n          return this.uniforms.useGradient.value;\n        },\n        set(value) {\n          this.uniforms.useGradient.value = value;\n        }\n      },\n      visibility: {\n        enumerable: true,\n        get() {\n          return this.uniforms.visibility.value;\n        },\n        set(value) {\n          this.uniforms.visibility.value = value;\n        }\n      },\n      alphaTest: {\n        enumerable: true,\n        get() {\n          return this.uniforms.alphaTest.value;\n        },\n        set(value) {\n          this.uniforms.alphaTest.value = value;\n        }\n      },\n      repeat: {\n        enumerable: true,\n        get() {\n          return this.uniforms.repeat.value;\n        },\n        set(value) {\n          this.uniforms.repeat.value.copy(value);\n        }\n      }\n    });\n    this.setValues(parameters);\n  }\n  copy(source) {\n    super.copy(source);\n    this.lineWidth = source.lineWidth;\n    this.map = source.map;\n    this.useMap = source.useMap;\n    this.alphaMap = source.alphaMap;\n    this.useAlphaMap = source.useAlphaMap;\n    this.color.copy(source.color);\n    this.gradient = source.gradient;\n    this.opacity = source.opacity;\n    this.resolution.copy(source.resolution);\n    this.sizeAttenuation = source.sizeAttenuation;\n    this.dashArray = source.dashArray;\n    this.dashOffset = source.dashOffset;\n    this.dashRatio = source.dashRatio;\n    this.useDash = source.useDash;\n    this.useGradient = source.useGradient;\n    this.visibility = source.visibility;\n    this.alphaTest = source.alphaTest;\n    this.repeat.copy(source.repeat);\n    return this;\n  }\n}\nfunction raycast(raycaster, intersects) {\n  const inverseMatrix = new THREE.Matrix4();\n  const ray = new THREE.Ray();\n  const sphere = new THREE.Sphere();\n  const interRay = new THREE.Vector3();\n  const geometry = this.geometry;\n  sphere.copy(geometry.boundingSphere);\n  sphere.applyMatrix4(this.matrixWorld);\n  if (!raycaster.ray.intersectSphere(sphere, interRay)) return;\n  inverseMatrix.copy(this.matrixWorld).invert();\n  ray.copy(raycaster.ray).applyMatrix4(inverseMatrix);\n  const vStart = new THREE.Vector3();\n  const vEnd = new THREE.Vector3();\n  const interSegment = new THREE.Vector3();\n  const step = this instanceof THREE.LineSegments ? 2 : 1;\n  const index = geometry.index;\n  const attributes = geometry.attributes;\n  if (index !== null) {\n    const indices = index.array;\n    const positions = attributes.position.array;\n    const widths = attributes.width.array;\n    for (let i = 0, l = indices.length - 1; i < l; i += step) {\n      const a = indices[i];\n      const b = indices[i + 1];\n      vStart.fromArray(positions, a * 3);\n      vEnd.fromArray(positions, b * 3);\n      const width = widths[Math.floor(i / 3)] != void 0 ? widths[Math.floor(i / 3)] : 1;\n      const precision = raycaster.params.Line.threshold + this.material.lineWidth * width / 2;\n      const precisionSq = precision * precision;\n      const distSq = ray.distanceSqToSegment(vStart, vEnd, interRay, interSegment);\n      if (distSq > precisionSq) continue;\n      interRay.applyMatrix4(this.matrixWorld);\n      const distance = raycaster.ray.origin.distanceTo(interRay);\n      if (distance < raycaster.near || distance > raycaster.far) continue;\n      intersects.push({\n        distance,\n        point: interSegment.clone().applyMatrix4(this.matrixWorld),\n        index: i,\n        face: null,\n        faceIndex: void 0,\n        object: this\n      });\n      i = l;\n    }\n  }\n}\nexport { MeshLineGeometry, MeshLineMaterial, raycast };", "map": {"version": 3, "names": ["__defProp", "Object", "defineProperty", "__defNormalProp", "obj", "key", "value", "enumerable", "configurable", "writable", "__publicField", "THREE", "memcpy", "src", "srcOffset", "dst", "dstOffset", "length", "i", "subarray", "slice", "buffer", "set", "convertPoints", "points", "Float32Array", "BufferGeometry", "getAttribute", "array", "map", "p", "isArray", "Array", "Vector3", "x", "y", "z", "Vector2", "flat", "MeshLineGeometry", "constructor", "Matrix4", "defineProperties", "get", "_points", "setPoints", "widthCallback", "setMatrixWorld", "matrixWorld", "wcb", "positions", "counters", "j", "c", "push", "process", "compareV3", "a", "b", "aa", "ab", "copyV3", "l", "previous", "next", "side", "width", "indices_array", "uvs", "w", "v", "n", "_attributes", "position", "count", "BufferAttribute", "uv", "index", "Uint16Array", "copyArray", "needsUpdate", "setAttribute", "setIndex", "computeBoundingSphere", "computeBoundingBox", "advance", "vertexShader", "version", "parseInt", "REVISION", "replace", "colorspace_fragment", "fragmentShader", "MeshLineMaterial", "ShaderMaterial", "parameters", "uniforms", "UniformsLib", "fog", "lineWidth", "useMap", "alphaMap", "useAlphaMap", "color", "Color", "gradient", "opacity", "resolution", "sizeAttenuation", "dashArray", "dashOffset", "dashRatio", "useDash", "useGradient", "visibility", "alphaTest", "repeat", "type", "copy", "set<PERSON><PERSON><PERSON>", "source", "raycast", "raycaster", "intersects", "inverseMatrix", "ray", "<PERSON>", "sphere", "Sphere", "interRay", "geometry", "boundingSphere", "applyMatrix4", "intersectSphere", "invert", "vStart", "vEnd", "interSegment", "step", "LineSegments", "attributes", "indices", "widths", "fromArray", "Math", "floor", "precision", "params", "Line", "threshold", "material", "precisionSq", "distSq", "distanceSqToSegment", "distance", "origin", "distanceTo", "near", "far", "point", "clone", "face", "faceIndex", "object"], "sources": ["C:/Users/<USER>/Documents/Apps_Creations/SiahMan_Racing4Speed_WebGame/siah-man-racing/node_modules/meshline/dist/index.js"], "sourcesContent": ["var __defProp = Object.defineProperty;\nvar __defNormalProp = (obj, key, value) => key in obj ? __defProp(obj, key, { enumerable: true, configurable: true, writable: true, value }) : obj[key] = value;\nvar __publicField = (obj, key, value) => {\n  __defNormalProp(obj, typeof key !== \"symbol\" ? key + \"\" : key, value);\n  return value;\n};\nimport * as THREE from \"three\";\nfunction memcpy(src, srcOffset, dst, dstOffset, length) {\n  let i;\n  src = src.subarray || src.slice ? src : src.buffer;\n  dst = dst.subarray || dst.slice ? dst : dst.buffer;\n  src = srcOffset ? src.subarray ? src.subarray(srcOffset, length && srcOffset + length) : src.slice(srcOffset, length && srcOffset + length) : src;\n  if (dst.set) {\n    dst.set(src, dstOffset);\n  } else {\n    for (i = 0; i < src.length; i++)\n      dst[i + dstOffset] = src[i];\n  }\n  return dst;\n}\nfunction convertPoints(points) {\n  if (points instanceof Float32Array)\n    return points;\n  if (points instanceof THREE.BufferGeometry)\n    return points.getAttribute(\"position\").array;\n  return points.map((p) => {\n    const isArray = Array.isArray(p);\n    return p instanceof THREE.Vector3 ? [p.x, p.y, p.z] : p instanceof THREE.Vector2 ? [p.x, p.y, 0] : isArray && p.length === 3 ? [p[0], p[1], p[2]] : isArray && p.length === 2 ? [p[0], p[1], 0] : p;\n  }).flat();\n}\nclass MeshLineGeometry extends THREE.BufferGeometry {\n  constructor() {\n    super();\n    __publicField(this, \"type\", \"MeshLine\");\n    __publicField(this, \"isMeshLine\", true);\n    __publicField(this, \"positions\", []);\n    __publicField(this, \"previous\", []);\n    __publicField(this, \"next\", []);\n    __publicField(this, \"side\", []);\n    __publicField(this, \"width\", []);\n    __publicField(this, \"indices_array\", []);\n    __publicField(this, \"uvs\", []);\n    __publicField(this, \"counters\", []);\n    __publicField(this, \"widthCallback\", null);\n    __publicField(this, \"_attributes\");\n    __publicField(this, \"_points\", []);\n    __publicField(this, \"points\");\n    __publicField(this, \"matrixWorld\", new THREE.Matrix4());\n    Object.defineProperties(this, {\n      points: {\n        enumerable: true,\n        get() {\n          return this._points;\n        },\n        set(value) {\n          this.setPoints(value, this.widthCallback);\n        }\n      }\n    });\n  }\n  setMatrixWorld(matrixWorld) {\n    this.matrixWorld = matrixWorld;\n  }\n  setPoints(points, wcb) {\n    points = convertPoints(points);\n    this._points = points;\n    this.widthCallback = wcb != null ? wcb : null;\n    this.positions = [];\n    this.counters = [];\n    if (points.length && points[0] instanceof THREE.Vector3) {\n      for (let j = 0; j < points.length; j++) {\n        const p = points[j];\n        const c = j / (points.length - 1);\n        this.positions.push(p.x, p.y, p.z);\n        this.positions.push(p.x, p.y, p.z);\n        this.counters.push(c);\n        this.counters.push(c);\n      }\n    } else {\n      for (let j = 0; j < points.length; j += 3) {\n        const c = j / (points.length - 1);\n        this.positions.push(points[j], points[j + 1], points[j + 2]);\n        this.positions.push(points[j], points[j + 1], points[j + 2]);\n        this.counters.push(c);\n        this.counters.push(c);\n      }\n    }\n    this.process();\n  }\n  compareV3(a, b) {\n    const aa = a * 6;\n    const ab = b * 6;\n    return this.positions[aa] === this.positions[ab] && this.positions[aa + 1] === this.positions[ab + 1] && this.positions[aa + 2] === this.positions[ab + 2];\n  }\n  copyV3(a) {\n    const aa = a * 6;\n    return [this.positions[aa], this.positions[aa + 1], this.positions[aa + 2]];\n  }\n  process() {\n    const l = this.positions.length / 6;\n    this.previous = [];\n    this.next = [];\n    this.side = [];\n    this.width = [];\n    this.indices_array = [];\n    this.uvs = [];\n    let w;\n    let v;\n    if (this.compareV3(0, l - 1)) {\n      v = this.copyV3(l - 2);\n    } else {\n      v = this.copyV3(0);\n    }\n    this.previous.push(v[0], v[1], v[2]);\n    this.previous.push(v[0], v[1], v[2]);\n    for (let j = 0; j < l; j++) {\n      this.side.push(1);\n      this.side.push(-1);\n      if (this.widthCallback)\n        w = this.widthCallback(j / (l - 1));\n      else\n        w = 1;\n      this.width.push(w);\n      this.width.push(w);\n      this.uvs.push(j / (l - 1), 0);\n      this.uvs.push(j / (l - 1), 1);\n      if (j < l - 1) {\n        v = this.copyV3(j);\n        this.previous.push(v[0], v[1], v[2]);\n        this.previous.push(v[0], v[1], v[2]);\n        const n = j * 2;\n        this.indices_array.push(n, n + 1, n + 2);\n        this.indices_array.push(n + 2, n + 1, n + 3);\n      }\n      if (j > 0) {\n        v = this.copyV3(j);\n        this.next.push(v[0], v[1], v[2]);\n        this.next.push(v[0], v[1], v[2]);\n      }\n    }\n    if (this.compareV3(l - 1, 0)) {\n      v = this.copyV3(1);\n    } else {\n      v = this.copyV3(l - 1);\n    }\n    this.next.push(v[0], v[1], v[2]);\n    this.next.push(v[0], v[1], v[2]);\n    if (!this._attributes || this._attributes.position.count !== this.counters.length) {\n      this._attributes = {\n        position: new THREE.BufferAttribute(new Float32Array(this.positions), 3),\n        previous: new THREE.BufferAttribute(new Float32Array(this.previous), 3),\n        next: new THREE.BufferAttribute(new Float32Array(this.next), 3),\n        side: new THREE.BufferAttribute(new Float32Array(this.side), 1),\n        width: new THREE.BufferAttribute(new Float32Array(this.width), 1),\n        uv: new THREE.BufferAttribute(new Float32Array(this.uvs), 2),\n        index: new THREE.BufferAttribute(new Uint16Array(this.indices_array), 1),\n        counters: new THREE.BufferAttribute(new Float32Array(this.counters), 1)\n      };\n    } else {\n      this._attributes.position.copyArray(new Float32Array(this.positions));\n      this._attributes.position.needsUpdate = true;\n      this._attributes.previous.copyArray(new Float32Array(this.previous));\n      this._attributes.previous.needsUpdate = true;\n      this._attributes.next.copyArray(new Float32Array(this.next));\n      this._attributes.next.needsUpdate = true;\n      this._attributes.side.copyArray(new Float32Array(this.side));\n      this._attributes.side.needsUpdate = true;\n      this._attributes.width.copyArray(new Float32Array(this.width));\n      this._attributes.width.needsUpdate = true;\n      this._attributes.uv.copyArray(new Float32Array(this.uvs));\n      this._attributes.uv.needsUpdate = true;\n      this._attributes.index.copyArray(new Uint16Array(this.indices_array));\n      this._attributes.index.needsUpdate = true;\n    }\n    this.setAttribute(\"position\", this._attributes.position);\n    this.setAttribute(\"previous\", this._attributes.previous);\n    this.setAttribute(\"next\", this._attributes.next);\n    this.setAttribute(\"side\", this._attributes.side);\n    this.setAttribute(\"width\", this._attributes.width);\n    this.setAttribute(\"uv\", this._attributes.uv);\n    this.setAttribute(\"counters\", this._attributes.counters);\n    this.setAttribute(\"position\", this._attributes.position);\n    this.setAttribute(\"previous\", this._attributes.previous);\n    this.setAttribute(\"next\", this._attributes.next);\n    this.setAttribute(\"side\", this._attributes.side);\n    this.setAttribute(\"width\", this._attributes.width);\n    this.setAttribute(\"uv\", this._attributes.uv);\n    this.setAttribute(\"counters\", this._attributes.counters);\n    this.setIndex(this._attributes.index);\n    this.computeBoundingSphere();\n    this.computeBoundingBox();\n  }\n  advance({ x, y, z }) {\n    const positions = this._attributes.position.array;\n    const previous = this._attributes.previous.array;\n    const next = this._attributes.next.array;\n    const l = positions.length;\n    memcpy(positions, 0, previous, 0, l);\n    memcpy(positions, 6, positions, 0, l - 6);\n    positions[l - 6] = x;\n    positions[l - 5] = y;\n    positions[l - 4] = z;\n    positions[l - 3] = x;\n    positions[l - 2] = y;\n    positions[l - 1] = z;\n    memcpy(positions, 6, next, 0, l - 6);\n    next[l - 6] = x;\n    next[l - 5] = y;\n    next[l - 4] = z;\n    next[l - 3] = x;\n    next[l - 2] = y;\n    next[l - 1] = z;\n    this._attributes.position.needsUpdate = true;\n    this._attributes.previous.needsUpdate = true;\n    this._attributes.next.needsUpdate = true;\n  }\n}\nconst vertexShader = `\n  #include <common>\n  #include <logdepthbuf_pars_vertex>\n  #include <fog_pars_vertex>\n  #include <clipping_planes_pars_vertex>\n\n  attribute vec3 previous;\n  attribute vec3 next;\n  attribute float side;\n  attribute float width;\n  attribute float counters;\n  \n  uniform vec2 resolution;\n  uniform float lineWidth;\n  uniform vec3 color;\n  uniform float opacity;\n  uniform float sizeAttenuation;\n  \n  varying vec2 vUV;\n  varying vec4 vColor;\n  varying float vCounters;\n  \n  vec2 fix(vec4 i, float aspect) {\n    vec2 res = i.xy / i.w;\n    res.x *= aspect;\n    return res;\n  }\n  \n  void main() {\n    float aspect = resolution.x / resolution.y;\n    vColor = vec4(color, opacity);\n    vUV = uv;\n    vCounters = counters;\n  \n    mat4 m = projectionMatrix * modelViewMatrix;\n    vec4 finalPosition = m * vec4(position, 1.0) * aspect;\n    vec4 prevPos = m * vec4(previous, 1.0);\n    vec4 nextPos = m * vec4(next, 1.0);\n  \n    vec2 currentP = fix(finalPosition, aspect);\n    vec2 prevP = fix(prevPos, aspect);\n    vec2 nextP = fix(nextPos, aspect);\n  \n    float w = lineWidth * width;\n  \n    vec2 dir;\n    if (nextP == currentP) dir = normalize(currentP - prevP);\n    else if (prevP == currentP) dir = normalize(nextP - currentP);\n    else {\n      vec2 dir1 = normalize(currentP - prevP);\n      vec2 dir2 = normalize(nextP - currentP);\n      dir = normalize(dir1 + dir2);\n  \n      vec2 perp = vec2(-dir1.y, dir1.x);\n      vec2 miter = vec2(-dir.y, dir.x);\n      //w = clamp(w / dot(miter, perp), 0., 4. * lineWidth * width);\n    }\n  \n    //vec2 normal = (cross(vec3(dir, 0.), vec3(0., 0., 1.))).xy;\n    vec4 normal = vec4(-dir.y, dir.x, 0., 1.);\n    normal.xy *= .5 * w;\n    //normal *= projectionMatrix;\n    if (sizeAttenuation == 0.) {\n      normal.xy *= finalPosition.w;\n      normal.xy /= (vec4(resolution, 0., 1.) * projectionMatrix).xy * aspect;\n    }\n  \n    finalPosition.xy += normal.xy * side;\n    gl_Position = finalPosition;\n    #include <logdepthbuf_vertex>\n    #include <fog_vertex>\n    vec4 mvPosition = modelViewMatrix * vec4(position, 1.0);\n    #include <clipping_planes_vertex>\n    #include <fog_vertex>\n  }\n`;\nconst version = /* @__PURE__ */ (() => parseInt(THREE.REVISION.replace(/\\D+/g, \"\")))();\nconst colorspace_fragment = version >= 154 ? \"colorspace_fragment\" : \"encodings_fragment\";\nconst fragmentShader = `\n  #include <fog_pars_fragment>\n  #include <logdepthbuf_pars_fragment>\n  #include <clipping_planes_pars_fragment>\n  \n  uniform sampler2D map;\n  uniform sampler2D alphaMap;\n  uniform float useGradient;\n  uniform float useMap;\n  uniform float useAlphaMap;\n  uniform float useDash;\n  uniform float dashArray;\n  uniform float dashOffset;\n  uniform float dashRatio;\n  uniform float visibility;\n  uniform float alphaTest;\n  uniform vec2 repeat;\n  uniform vec3 gradient[2];\n  \n  varying vec2 vUV;\n  varying vec4 vColor;\n  varying float vCounters;\n  \n  void main() {\n    #include <logdepthbuf_fragment>\n    vec4 diffuseColor = vColor;\n    if (useGradient == 1.) diffuseColor = vec4(mix(gradient[0], gradient[1], vCounters), 1.0);\n    if (useMap == 1.) diffuseColor *= texture2D(map, vUV * repeat);\n    if (useAlphaMap == 1.) diffuseColor.a *= texture2D(alphaMap, vUV * repeat).a;\n    if (diffuseColor.a < alphaTest) discard;\n    if (useDash == 1.) diffuseColor.a *= ceil(mod(vCounters + dashOffset, dashArray) - (dashArray * dashRatio));\n    diffuseColor.a *= step(vCounters, visibility);\n    #include <clipping_planes_fragment>\n    gl_FragColor = diffuseColor;     \n    #include <fog_fragment>\n    #include <tonemapping_fragment>\n    #include <${colorspace_fragment}>\n  }\n`;\nclass MeshLineMaterial extends THREE.ShaderMaterial {\n  constructor(parameters) {\n    super({\n      uniforms: {\n        ...THREE.UniformsLib.fog,\n        lineWidth: { value: 1 },\n        map: { value: null },\n        useMap: { value: 0 },\n        alphaMap: { value: null },\n        useAlphaMap: { value: 0 },\n        color: { value: new THREE.Color(16777215) },\n        gradient: { value: [new THREE.Color(16711680), new THREE.Color(65280)] },\n        opacity: { value: 1 },\n        resolution: { value: new THREE.Vector2(1, 1) },\n        sizeAttenuation: { value: 1 },\n        dashArray: { value: 0 },\n        dashOffset: { value: 0 },\n        dashRatio: { value: 0.5 },\n        useDash: { value: 0 },\n        useGradient: { value: 0 },\n        visibility: { value: 1 },\n        alphaTest: { value: 0 },\n        repeat: { value: new THREE.Vector2(1, 1) }\n      },\n      vertexShader,\n      fragmentShader\n    });\n    __publicField(this, \"lineWidth\");\n    __publicField(this, \"map\");\n    __publicField(this, \"useMap\");\n    __publicField(this, \"alphaMap\");\n    __publicField(this, \"useAlphaMap\");\n    __publicField(this, \"color\");\n    __publicField(this, \"gradient\");\n    __publicField(this, \"resolution\");\n    __publicField(this, \"sizeAttenuation\");\n    __publicField(this, \"dashArray\");\n    __publicField(this, \"dashOffset\");\n    __publicField(this, \"dashRatio\");\n    __publicField(this, \"useDash\");\n    __publicField(this, \"useGradient\");\n    __publicField(this, \"visibility\");\n    __publicField(this, \"repeat\");\n    this.type = \"MeshLineMaterial\";\n    Object.defineProperties(this, {\n      lineWidth: {\n        enumerable: true,\n        get() {\n          return this.uniforms.lineWidth.value;\n        },\n        set(value) {\n          this.uniforms.lineWidth.value = value;\n        }\n      },\n      map: {\n        enumerable: true,\n        get() {\n          return this.uniforms.map.value;\n        },\n        set(value) {\n          this.uniforms.map.value = value;\n        }\n      },\n      useMap: {\n        enumerable: true,\n        get() {\n          return this.uniforms.useMap.value;\n        },\n        set(value) {\n          this.uniforms.useMap.value = value;\n        }\n      },\n      alphaMap: {\n        enumerable: true,\n        get() {\n          return this.uniforms.alphaMap.value;\n        },\n        set(value) {\n          this.uniforms.alphaMap.value = value;\n        }\n      },\n      useAlphaMap: {\n        enumerable: true,\n        get() {\n          return this.uniforms.useAlphaMap.value;\n        },\n        set(value) {\n          this.uniforms.useAlphaMap.value = value;\n        }\n      },\n      color: {\n        enumerable: true,\n        get() {\n          return this.uniforms.color.value;\n        },\n        set(value) {\n          this.uniforms.color.value = value;\n        }\n      },\n      gradient: {\n        enumerable: true,\n        get() {\n          return this.uniforms.gradient.value;\n        },\n        set(value) {\n          this.uniforms.gradient.value = value;\n        }\n      },\n      opacity: {\n        enumerable: true,\n        get() {\n          return this.uniforms.opacity.value;\n        },\n        set(value) {\n          this.uniforms.opacity.value = value;\n        }\n      },\n      resolution: {\n        enumerable: true,\n        get() {\n          return this.uniforms.resolution.value;\n        },\n        set(value) {\n          this.uniforms.resolution.value.copy(value);\n        }\n      },\n      sizeAttenuation: {\n        enumerable: true,\n        get() {\n          return this.uniforms.sizeAttenuation.value;\n        },\n        set(value) {\n          this.uniforms.sizeAttenuation.value = value;\n        }\n      },\n      dashArray: {\n        enumerable: true,\n        get() {\n          return this.uniforms.dashArray.value;\n        },\n        set(value) {\n          this.uniforms.dashArray.value = value;\n          this.useDash = value !== 0 ? 1 : 0;\n        }\n      },\n      dashOffset: {\n        enumerable: true,\n        get() {\n          return this.uniforms.dashOffset.value;\n        },\n        set(value) {\n          this.uniforms.dashOffset.value = value;\n        }\n      },\n      dashRatio: {\n        enumerable: true,\n        get() {\n          return this.uniforms.dashRatio.value;\n        },\n        set(value) {\n          this.uniforms.dashRatio.value = value;\n        }\n      },\n      useDash: {\n        enumerable: true,\n        get() {\n          return this.uniforms.useDash.value;\n        },\n        set(value) {\n          this.uniforms.useDash.value = value;\n        }\n      },\n      useGradient: {\n        enumerable: true,\n        get() {\n          return this.uniforms.useGradient.value;\n        },\n        set(value) {\n          this.uniforms.useGradient.value = value;\n        }\n      },\n      visibility: {\n        enumerable: true,\n        get() {\n          return this.uniforms.visibility.value;\n        },\n        set(value) {\n          this.uniforms.visibility.value = value;\n        }\n      },\n      alphaTest: {\n        enumerable: true,\n        get() {\n          return this.uniforms.alphaTest.value;\n        },\n        set(value) {\n          this.uniforms.alphaTest.value = value;\n        }\n      },\n      repeat: {\n        enumerable: true,\n        get() {\n          return this.uniforms.repeat.value;\n        },\n        set(value) {\n          this.uniforms.repeat.value.copy(value);\n        }\n      }\n    });\n    this.setValues(parameters);\n  }\n  copy(source) {\n    super.copy(source);\n    this.lineWidth = source.lineWidth;\n    this.map = source.map;\n    this.useMap = source.useMap;\n    this.alphaMap = source.alphaMap;\n    this.useAlphaMap = source.useAlphaMap;\n    this.color.copy(source.color);\n    this.gradient = source.gradient;\n    this.opacity = source.opacity;\n    this.resolution.copy(source.resolution);\n    this.sizeAttenuation = source.sizeAttenuation;\n    this.dashArray = source.dashArray;\n    this.dashOffset = source.dashOffset;\n    this.dashRatio = source.dashRatio;\n    this.useDash = source.useDash;\n    this.useGradient = source.useGradient;\n    this.visibility = source.visibility;\n    this.alphaTest = source.alphaTest;\n    this.repeat.copy(source.repeat);\n    return this;\n  }\n}\nfunction raycast(raycaster, intersects) {\n  const inverseMatrix = new THREE.Matrix4();\n  const ray = new THREE.Ray();\n  const sphere = new THREE.Sphere();\n  const interRay = new THREE.Vector3();\n  const geometry = this.geometry;\n  sphere.copy(geometry.boundingSphere);\n  sphere.applyMatrix4(this.matrixWorld);\n  if (!raycaster.ray.intersectSphere(sphere, interRay))\n    return;\n  inverseMatrix.copy(this.matrixWorld).invert();\n  ray.copy(raycaster.ray).applyMatrix4(inverseMatrix);\n  const vStart = new THREE.Vector3();\n  const vEnd = new THREE.Vector3();\n  const interSegment = new THREE.Vector3();\n  const step = this instanceof THREE.LineSegments ? 2 : 1;\n  const index = geometry.index;\n  const attributes = geometry.attributes;\n  if (index !== null) {\n    const indices = index.array;\n    const positions = attributes.position.array;\n    const widths = attributes.width.array;\n    for (let i = 0, l = indices.length - 1; i < l; i += step) {\n      const a = indices[i];\n      const b = indices[i + 1];\n      vStart.fromArray(positions, a * 3);\n      vEnd.fromArray(positions, b * 3);\n      const width = widths[Math.floor(i / 3)] != void 0 ? widths[Math.floor(i / 3)] : 1;\n      const precision = raycaster.params.Line.threshold + this.material.lineWidth * width / 2;\n      const precisionSq = precision * precision;\n      const distSq = ray.distanceSqToSegment(vStart, vEnd, interRay, interSegment);\n      if (distSq > precisionSq)\n        continue;\n      interRay.applyMatrix4(this.matrixWorld);\n      const distance = raycaster.ray.origin.distanceTo(interRay);\n      if (distance < raycaster.near || distance > raycaster.far)\n        continue;\n      intersects.push({\n        distance,\n        point: interSegment.clone().applyMatrix4(this.matrixWorld),\n        index: i,\n        face: null,\n        faceIndex: void 0,\n        object: this\n      });\n      i = l;\n    }\n  }\n}\nexport {\n  MeshLineGeometry,\n  MeshLineMaterial,\n  raycast\n};\n"], "mappings": "AAAA,IAAIA,SAAS,GAAGC,MAAM,CAACC,cAAc;AACrC,IAAIC,eAAe,GAAGA,CAACC,GAAG,EAAEC,GAAG,EAAEC,KAAK,KAAKD,GAAG,IAAID,GAAG,GAAGJ,SAAS,CAACI,GAAG,EAAEC,GAAG,EAAE;EAAEE,UAAU,EAAE,IAAI;EAAEC,YAAY,EAAE,IAAI;EAAEC,QAAQ,EAAE,IAAI;EAAEH;AAAM,CAAC,CAAC,GAAGF,GAAG,CAACC,GAAG,CAAC,GAAGC,KAAK;AAC/J,IAAII,aAAa,GAAGA,CAACN,GAAG,EAAEC,GAAG,EAAEC,KAAK,KAAK;EACvCH,eAAe,CAACC,GAAG,EAAE,OAAOC,GAAG,KAAK,QAAQ,GAAGA,GAAG,GAAG,EAAE,GAAGA,GAAG,EAAEC,KAAK,CAAC;EACrE,OAAOA,KAAK;AACd,CAAC;AACD,OAAO,KAAKK,KAAK,MAAM,OAAO;AAC9B,SAASC,MAAMA,CAACC,GAAG,EAAEC,SAAS,EAAEC,GAAG,EAAEC,SAAS,EAAEC,MAAM,EAAE;EACtD,IAAIC,CAAC;EACLL,GAAG,GAAGA,GAAG,CAACM,QAAQ,IAAIN,GAAG,CAACO,KAAK,GAAGP,GAAG,GAAGA,GAAG,CAACQ,MAAM;EAClDN,GAAG,GAAGA,GAAG,CAACI,QAAQ,IAAIJ,GAAG,CAACK,KAAK,GAAGL,GAAG,GAAGA,GAAG,CAACM,MAAM;EAClDR,GAAG,GAAGC,SAAS,GAAGD,GAAG,CAACM,QAAQ,GAAGN,GAAG,CAACM,QAAQ,CAACL,SAAS,EAAEG,MAAM,IAAIH,SAAS,GAAGG,MAAM,CAAC,GAAGJ,GAAG,CAACO,KAAK,CAACN,SAAS,EAAEG,MAAM,IAAIH,SAAS,GAAGG,MAAM,CAAC,GAAGJ,GAAG;EACjJ,IAAIE,GAAG,CAACO,GAAG,EAAE;IACXP,GAAG,CAACO,GAAG,CAACT,GAAG,EAAEG,SAAS,CAAC;EACzB,CAAC,MAAM;IACL,KAAKE,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGL,GAAG,CAACI,MAAM,EAAEC,CAAC,EAAE,EAC7BH,GAAG,CAACG,CAAC,GAAGF,SAAS,CAAC,GAAGH,GAAG,CAACK,CAAC,CAAC;EAC/B;EACA,OAAOH,GAAG;AACZ;AACA,SAASQ,aAAaA,CAACC,MAAM,EAAE;EAC7B,IAAIA,MAAM,YAAYC,YAAY,EAChC,OAAOD,MAAM;EACf,IAAIA,MAAM,YAAYb,KAAK,CAACe,cAAc,EACxC,OAAOF,MAAM,CAACG,YAAY,CAAC,UAAU,CAAC,CAACC,KAAK;EAC9C,OAAOJ,MAAM,CAACK,GAAG,CAAEC,CAAC,IAAK;IACvB,MAAMC,OAAO,GAAGC,KAAK,CAACD,OAAO,CAACD,CAAC,CAAC;IAChC,OAAOA,CAAC,YAAYnB,KAAK,CAACsB,OAAO,GAAG,CAACH,CAAC,CAACI,CAAC,EAAEJ,CAAC,CAACK,CAAC,EAAEL,CAAC,CAACM,CAAC,CAAC,GAAGN,CAAC,YAAYnB,KAAK,CAAC0B,OAAO,GAAG,CAACP,CAAC,CAACI,CAAC,EAAEJ,CAAC,CAACK,CAAC,EAAE,CAAC,CAAC,GAAGJ,OAAO,IAAID,CAAC,CAACb,MAAM,KAAK,CAAC,GAAG,CAACa,CAAC,CAAC,CAAC,CAAC,EAAEA,CAAC,CAAC,CAAC,CAAC,EAAEA,CAAC,CAAC,CAAC,CAAC,CAAC,GAAGC,OAAO,IAAID,CAAC,CAACb,MAAM,KAAK,CAAC,GAAG,CAACa,CAAC,CAAC,CAAC,CAAC,EAAEA,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,GAAGA,CAAC;EACrM,CAAC,CAAC,CAACQ,IAAI,CAAC,CAAC;AACX;AACA,MAAMC,gBAAgB,SAAS5B,KAAK,CAACe,cAAc,CAAC;EAClDc,WAAWA,CAAA,EAAG;IACZ,KAAK,CAAC,CAAC;IACP9B,aAAa,CAAC,IAAI,EAAE,MAAM,EAAE,UAAU,CAAC;IACvCA,aAAa,CAAC,IAAI,EAAE,YAAY,EAAE,IAAI,CAAC;IACvCA,aAAa,CAAC,IAAI,EAAE,WAAW,EAAE,EAAE,CAAC;IACpCA,aAAa,CAAC,IAAI,EAAE,UAAU,EAAE,EAAE,CAAC;IACnCA,aAAa,CAAC,IAAI,EAAE,MAAM,EAAE,EAAE,CAAC;IAC/BA,aAAa,CAAC,IAAI,EAAE,MAAM,EAAE,EAAE,CAAC;IAC/BA,aAAa,CAAC,IAAI,EAAE,OAAO,EAAE,EAAE,CAAC;IAChCA,aAAa,CAAC,IAAI,EAAE,eAAe,EAAE,EAAE,CAAC;IACxCA,aAAa,CAAC,IAAI,EAAE,KAAK,EAAE,EAAE,CAAC;IAC9BA,aAAa,CAAC,IAAI,EAAE,UAAU,EAAE,EAAE,CAAC;IACnCA,aAAa,CAAC,IAAI,EAAE,eAAe,EAAE,IAAI,CAAC;IAC1CA,aAAa,CAAC,IAAI,EAAE,aAAa,CAAC;IAClCA,aAAa,CAAC,IAAI,EAAE,SAAS,EAAE,EAAE,CAAC;IAClCA,aAAa,CAAC,IAAI,EAAE,QAAQ,CAAC;IAC7BA,aAAa,CAAC,IAAI,EAAE,aAAa,EAAE,IAAIC,KAAK,CAAC8B,OAAO,CAAC,CAAC,CAAC;IACvDxC,MAAM,CAACyC,gBAAgB,CAAC,IAAI,EAAE;MAC5BlB,MAAM,EAAE;QACNjB,UAAU,EAAE,IAAI;QAChBoC,GAAGA,CAAA,EAAG;UACJ,OAAO,IAAI,CAACC,OAAO;QACrB,CAAC;QACDtB,GAAGA,CAAChB,KAAK,EAAE;UACT,IAAI,CAACuC,SAAS,CAACvC,KAAK,EAAE,IAAI,CAACwC,aAAa,CAAC;QAC3C;MACF;IACF,CAAC,CAAC;EACJ;EACAC,cAAcA,CAACC,WAAW,EAAE;IAC1B,IAAI,CAACA,WAAW,GAAGA,WAAW;EAChC;EACAH,SAASA,CAACrB,MAAM,EAAEyB,GAAG,EAAE;IACrBzB,MAAM,GAAGD,aAAa,CAACC,MAAM,CAAC;IAC9B,IAAI,CAACoB,OAAO,GAAGpB,MAAM;IACrB,IAAI,CAACsB,aAAa,GAAGG,GAAG,IAAI,IAAI,GAAGA,GAAG,GAAG,IAAI;IAC7C,IAAI,CAACC,SAAS,GAAG,EAAE;IACnB,IAAI,CAACC,QAAQ,GAAG,EAAE;IAClB,IAAI3B,MAAM,CAACP,MAAM,IAAIO,MAAM,CAAC,CAAC,CAAC,YAAYb,KAAK,CAACsB,OAAO,EAAE;MACvD,KAAK,IAAImB,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAG5B,MAAM,CAACP,MAAM,EAAEmC,CAAC,EAAE,EAAE;QACtC,MAAMtB,CAAC,GAAGN,MAAM,CAAC4B,CAAC,CAAC;QACnB,MAAMC,CAAC,GAAGD,CAAC,IAAI5B,MAAM,CAACP,MAAM,GAAG,CAAC,CAAC;QACjC,IAAI,CAACiC,SAAS,CAACI,IAAI,CAACxB,CAAC,CAACI,CAAC,EAAEJ,CAAC,CAACK,CAAC,EAAEL,CAAC,CAACM,CAAC,CAAC;QAClC,IAAI,CAACc,SAAS,CAACI,IAAI,CAACxB,CAAC,CAACI,CAAC,EAAEJ,CAAC,CAACK,CAAC,EAAEL,CAAC,CAACM,CAAC,CAAC;QAClC,IAAI,CAACe,QAAQ,CAACG,IAAI,CAACD,CAAC,CAAC;QACrB,IAAI,CAACF,QAAQ,CAACG,IAAI,CAACD,CAAC,CAAC;MACvB;IACF,CAAC,MAAM;MACL,KAAK,IAAID,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAG5B,MAAM,CAACP,MAAM,EAAEmC,CAAC,IAAI,CAAC,EAAE;QACzC,MAAMC,CAAC,GAAGD,CAAC,IAAI5B,MAAM,CAACP,MAAM,GAAG,CAAC,CAAC;QACjC,IAAI,CAACiC,SAAS,CAACI,IAAI,CAAC9B,MAAM,CAAC4B,CAAC,CAAC,EAAE5B,MAAM,CAAC4B,CAAC,GAAG,CAAC,CAAC,EAAE5B,MAAM,CAAC4B,CAAC,GAAG,CAAC,CAAC,CAAC;QAC5D,IAAI,CAACF,SAAS,CAACI,IAAI,CAAC9B,MAAM,CAAC4B,CAAC,CAAC,EAAE5B,MAAM,CAAC4B,CAAC,GAAG,CAAC,CAAC,EAAE5B,MAAM,CAAC4B,CAAC,GAAG,CAAC,CAAC,CAAC;QAC5D,IAAI,CAACD,QAAQ,CAACG,IAAI,CAACD,CAAC,CAAC;QACrB,IAAI,CAACF,QAAQ,CAACG,IAAI,CAACD,CAAC,CAAC;MACvB;IACF;IACA,IAAI,CAACE,OAAO,CAAC,CAAC;EAChB;EACAC,SAASA,CAACC,CAAC,EAAEC,CAAC,EAAE;IACd,MAAMC,EAAE,GAAGF,CAAC,GAAG,CAAC;IAChB,MAAMG,EAAE,GAAGF,CAAC,GAAG,CAAC;IAChB,OAAO,IAAI,CAACR,SAAS,CAACS,EAAE,CAAC,KAAK,IAAI,CAACT,SAAS,CAACU,EAAE,CAAC,IAAI,IAAI,CAACV,SAAS,CAACS,EAAE,GAAG,CAAC,CAAC,KAAK,IAAI,CAACT,SAAS,CAACU,EAAE,GAAG,CAAC,CAAC,IAAI,IAAI,CAACV,SAAS,CAACS,EAAE,GAAG,CAAC,CAAC,KAAK,IAAI,CAACT,SAAS,CAACU,EAAE,GAAG,CAAC,CAAC;EAC5J;EACAC,MAAMA,CAACJ,CAAC,EAAE;IACR,MAAME,EAAE,GAAGF,CAAC,GAAG,CAAC;IAChB,OAAO,CAAC,IAAI,CAACP,SAAS,CAACS,EAAE,CAAC,EAAE,IAAI,CAACT,SAAS,CAACS,EAAE,GAAG,CAAC,CAAC,EAAE,IAAI,CAACT,SAAS,CAACS,EAAE,GAAG,CAAC,CAAC,CAAC;EAC7E;EACAJ,OAAOA,CAAA,EAAG;IACR,MAAMO,CAAC,GAAG,IAAI,CAACZ,SAAS,CAACjC,MAAM,GAAG,CAAC;IACnC,IAAI,CAAC8C,QAAQ,GAAG,EAAE;IAClB,IAAI,CAACC,IAAI,GAAG,EAAE;IACd,IAAI,CAACC,IAAI,GAAG,EAAE;IACd,IAAI,CAACC,KAAK,GAAG,EAAE;IACf,IAAI,CAACC,aAAa,GAAG,EAAE;IACvB,IAAI,CAACC,GAAG,GAAG,EAAE;IACb,IAAIC,CAAC;IACL,IAAIC,CAAC;IACL,IAAI,IAAI,CAACd,SAAS,CAAC,CAAC,EAAEM,CAAC,GAAG,CAAC,CAAC,EAAE;MAC5BQ,CAAC,GAAG,IAAI,CAACT,MAAM,CAACC,CAAC,GAAG,CAAC,CAAC;IACxB,CAAC,MAAM;MACLQ,CAAC,GAAG,IAAI,CAACT,MAAM,CAAC,CAAC,CAAC;IACpB;IACA,IAAI,CAACE,QAAQ,CAACT,IAAI,CAACgB,CAAC,CAAC,CAAC,CAAC,EAAEA,CAAC,CAAC,CAAC,CAAC,EAAEA,CAAC,CAAC,CAAC,CAAC,CAAC;IACpC,IAAI,CAACP,QAAQ,CAACT,IAAI,CAACgB,CAAC,CAAC,CAAC,CAAC,EAAEA,CAAC,CAAC,CAAC,CAAC,EAAEA,CAAC,CAAC,CAAC,CAAC,CAAC;IACpC,KAAK,IAAIlB,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGU,CAAC,EAAEV,CAAC,EAAE,EAAE;MAC1B,IAAI,CAACa,IAAI,CAACX,IAAI,CAAC,CAAC,CAAC;MACjB,IAAI,CAACW,IAAI,CAACX,IAAI,CAAC,CAAC,CAAC,CAAC;MAClB,IAAI,IAAI,CAACR,aAAa,EACpBuB,CAAC,GAAG,IAAI,CAACvB,aAAa,CAACM,CAAC,IAAIU,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,KAEpCO,CAAC,GAAG,CAAC;MACP,IAAI,CAACH,KAAK,CAACZ,IAAI,CAACe,CAAC,CAAC;MAClB,IAAI,CAACH,KAAK,CAACZ,IAAI,CAACe,CAAC,CAAC;MAClB,IAAI,CAACD,GAAG,CAACd,IAAI,CAACF,CAAC,IAAIU,CAAC,GAAG,CAAC,CAAC,EAAE,CAAC,CAAC;MAC7B,IAAI,CAACM,GAAG,CAACd,IAAI,CAACF,CAAC,IAAIU,CAAC,GAAG,CAAC,CAAC,EAAE,CAAC,CAAC;MAC7B,IAAIV,CAAC,GAAGU,CAAC,GAAG,CAAC,EAAE;QACbQ,CAAC,GAAG,IAAI,CAACT,MAAM,CAACT,CAAC,CAAC;QAClB,IAAI,CAACW,QAAQ,CAACT,IAAI,CAACgB,CAAC,CAAC,CAAC,CAAC,EAAEA,CAAC,CAAC,CAAC,CAAC,EAAEA,CAAC,CAAC,CAAC,CAAC,CAAC;QACpC,IAAI,CAACP,QAAQ,CAACT,IAAI,CAACgB,CAAC,CAAC,CAAC,CAAC,EAAEA,CAAC,CAAC,CAAC,CAAC,EAAEA,CAAC,CAAC,CAAC,CAAC,CAAC;QACpC,MAAMC,CAAC,GAAGnB,CAAC,GAAG,CAAC;QACf,IAAI,CAACe,aAAa,CAACb,IAAI,CAACiB,CAAC,EAAEA,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAG,CAAC,CAAC;QACxC,IAAI,CAACJ,aAAa,CAACb,IAAI,CAACiB,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAG,CAAC,CAAC;MAC9C;MACA,IAAInB,CAAC,GAAG,CAAC,EAAE;QACTkB,CAAC,GAAG,IAAI,CAACT,MAAM,CAACT,CAAC,CAAC;QAClB,IAAI,CAACY,IAAI,CAACV,IAAI,CAACgB,CAAC,CAAC,CAAC,CAAC,EAAEA,CAAC,CAAC,CAAC,CAAC,EAAEA,CAAC,CAAC,CAAC,CAAC,CAAC;QAChC,IAAI,CAACN,IAAI,CAACV,IAAI,CAACgB,CAAC,CAAC,CAAC,CAAC,EAAEA,CAAC,CAAC,CAAC,CAAC,EAAEA,CAAC,CAAC,CAAC,CAAC,CAAC;MAClC;IACF;IACA,IAAI,IAAI,CAACd,SAAS,CAACM,CAAC,GAAG,CAAC,EAAE,CAAC,CAAC,EAAE;MAC5BQ,CAAC,GAAG,IAAI,CAACT,MAAM,CAAC,CAAC,CAAC;IACpB,CAAC,MAAM;MACLS,CAAC,GAAG,IAAI,CAACT,MAAM,CAACC,CAAC,GAAG,CAAC,CAAC;IACxB;IACA,IAAI,CAACE,IAAI,CAACV,IAAI,CAACgB,CAAC,CAAC,CAAC,CAAC,EAAEA,CAAC,CAAC,CAAC,CAAC,EAAEA,CAAC,CAAC,CAAC,CAAC,CAAC;IAChC,IAAI,CAACN,IAAI,CAACV,IAAI,CAACgB,CAAC,CAAC,CAAC,CAAC,EAAEA,CAAC,CAAC,CAAC,CAAC,EAAEA,CAAC,CAAC,CAAC,CAAC,CAAC;IAChC,IAAI,CAAC,IAAI,CAACE,WAAW,IAAI,IAAI,CAACA,WAAW,CAACC,QAAQ,CAACC,KAAK,KAAK,IAAI,CAACvB,QAAQ,CAAClC,MAAM,EAAE;MACjF,IAAI,CAACuD,WAAW,GAAG;QACjBC,QAAQ,EAAE,IAAI9D,KAAK,CAACgE,eAAe,CAAC,IAAIlD,YAAY,CAAC,IAAI,CAACyB,SAAS,CAAC,EAAE,CAAC,CAAC;QACxEa,QAAQ,EAAE,IAAIpD,KAAK,CAACgE,eAAe,CAAC,IAAIlD,YAAY,CAAC,IAAI,CAACsC,QAAQ,CAAC,EAAE,CAAC,CAAC;QACvEC,IAAI,EAAE,IAAIrD,KAAK,CAACgE,eAAe,CAAC,IAAIlD,YAAY,CAAC,IAAI,CAACuC,IAAI,CAAC,EAAE,CAAC,CAAC;QAC/DC,IAAI,EAAE,IAAItD,KAAK,CAACgE,eAAe,CAAC,IAAIlD,YAAY,CAAC,IAAI,CAACwC,IAAI,CAAC,EAAE,CAAC,CAAC;QAC/DC,KAAK,EAAE,IAAIvD,KAAK,CAACgE,eAAe,CAAC,IAAIlD,YAAY,CAAC,IAAI,CAACyC,KAAK,CAAC,EAAE,CAAC,CAAC;QACjEU,EAAE,EAAE,IAAIjE,KAAK,CAACgE,eAAe,CAAC,IAAIlD,YAAY,CAAC,IAAI,CAAC2C,GAAG,CAAC,EAAE,CAAC,CAAC;QAC5DS,KAAK,EAAE,IAAIlE,KAAK,CAACgE,eAAe,CAAC,IAAIG,WAAW,CAAC,IAAI,CAACX,aAAa,CAAC,EAAE,CAAC,CAAC;QACxEhB,QAAQ,EAAE,IAAIxC,KAAK,CAACgE,eAAe,CAAC,IAAIlD,YAAY,CAAC,IAAI,CAAC0B,QAAQ,CAAC,EAAE,CAAC;MACxE,CAAC;IACH,CAAC,MAAM;MACL,IAAI,CAACqB,WAAW,CAACC,QAAQ,CAACM,SAAS,CAAC,IAAItD,YAAY,CAAC,IAAI,CAACyB,SAAS,CAAC,CAAC;MACrE,IAAI,CAACsB,WAAW,CAACC,QAAQ,CAACO,WAAW,GAAG,IAAI;MAC5C,IAAI,CAACR,WAAW,CAACT,QAAQ,CAACgB,SAAS,CAAC,IAAItD,YAAY,CAAC,IAAI,CAACsC,QAAQ,CAAC,CAAC;MACpE,IAAI,CAACS,WAAW,CAACT,QAAQ,CAACiB,WAAW,GAAG,IAAI;MAC5C,IAAI,CAACR,WAAW,CAACR,IAAI,CAACe,SAAS,CAAC,IAAItD,YAAY,CAAC,IAAI,CAACuC,IAAI,CAAC,CAAC;MAC5D,IAAI,CAACQ,WAAW,CAACR,IAAI,CAACgB,WAAW,GAAG,IAAI;MACxC,IAAI,CAACR,WAAW,CAACP,IAAI,CAACc,SAAS,CAAC,IAAItD,YAAY,CAAC,IAAI,CAACwC,IAAI,CAAC,CAAC;MAC5D,IAAI,CAACO,WAAW,CAACP,IAAI,CAACe,WAAW,GAAG,IAAI;MACxC,IAAI,CAACR,WAAW,CAACN,KAAK,CAACa,SAAS,CAAC,IAAItD,YAAY,CAAC,IAAI,CAACyC,KAAK,CAAC,CAAC;MAC9D,IAAI,CAACM,WAAW,CAACN,KAAK,CAACc,WAAW,GAAG,IAAI;MACzC,IAAI,CAACR,WAAW,CAACI,EAAE,CAACG,SAAS,CAAC,IAAItD,YAAY,CAAC,IAAI,CAAC2C,GAAG,CAAC,CAAC;MACzD,IAAI,CAACI,WAAW,CAACI,EAAE,CAACI,WAAW,GAAG,IAAI;MACtC,IAAI,CAACR,WAAW,CAACK,KAAK,CAACE,SAAS,CAAC,IAAID,WAAW,CAAC,IAAI,CAACX,aAAa,CAAC,CAAC;MACrE,IAAI,CAACK,WAAW,CAACK,KAAK,CAACG,WAAW,GAAG,IAAI;IAC3C;IACA,IAAI,CAACC,YAAY,CAAC,UAAU,EAAE,IAAI,CAACT,WAAW,CAACC,QAAQ,CAAC;IACxD,IAAI,CAACQ,YAAY,CAAC,UAAU,EAAE,IAAI,CAACT,WAAW,CAACT,QAAQ,CAAC;IACxD,IAAI,CAACkB,YAAY,CAAC,MAAM,EAAE,IAAI,CAACT,WAAW,CAACR,IAAI,CAAC;IAChD,IAAI,CAACiB,YAAY,CAAC,MAAM,EAAE,IAAI,CAACT,WAAW,CAACP,IAAI,CAAC;IAChD,IAAI,CAACgB,YAAY,CAAC,OAAO,EAAE,IAAI,CAACT,WAAW,CAACN,KAAK,CAAC;IAClD,IAAI,CAACe,YAAY,CAAC,IAAI,EAAE,IAAI,CAACT,WAAW,CAACI,EAAE,CAAC;IAC5C,IAAI,CAACK,YAAY,CAAC,UAAU,EAAE,IAAI,CAACT,WAAW,CAACrB,QAAQ,CAAC;IACxD,IAAI,CAAC8B,YAAY,CAAC,UAAU,EAAE,IAAI,CAACT,WAAW,CAACC,QAAQ,CAAC;IACxD,IAAI,CAACQ,YAAY,CAAC,UAAU,EAAE,IAAI,CAACT,WAAW,CAACT,QAAQ,CAAC;IACxD,IAAI,CAACkB,YAAY,CAAC,MAAM,EAAE,IAAI,CAACT,WAAW,CAACR,IAAI,CAAC;IAChD,IAAI,CAACiB,YAAY,CAAC,MAAM,EAAE,IAAI,CAACT,WAAW,CAACP,IAAI,CAAC;IAChD,IAAI,CAACgB,YAAY,CAAC,OAAO,EAAE,IAAI,CAACT,WAAW,CAACN,KAAK,CAAC;IAClD,IAAI,CAACe,YAAY,CAAC,IAAI,EAAE,IAAI,CAACT,WAAW,CAACI,EAAE,CAAC;IAC5C,IAAI,CAACK,YAAY,CAAC,UAAU,EAAE,IAAI,CAACT,WAAW,CAACrB,QAAQ,CAAC;IACxD,IAAI,CAAC+B,QAAQ,CAAC,IAAI,CAACV,WAAW,CAACK,KAAK,CAAC;IACrC,IAAI,CAACM,qBAAqB,CAAC,CAAC;IAC5B,IAAI,CAACC,kBAAkB,CAAC,CAAC;EAC3B;EACAC,OAAOA,CAAC;IAAEnD,CAAC;IAAEC,CAAC;IAAEC;EAAE,CAAC,EAAE;IACnB,MAAMc,SAAS,GAAG,IAAI,CAACsB,WAAW,CAACC,QAAQ,CAAC7C,KAAK;IACjD,MAAMmC,QAAQ,GAAG,IAAI,CAACS,WAAW,CAACT,QAAQ,CAACnC,KAAK;IAChD,MAAMoC,IAAI,GAAG,IAAI,CAACQ,WAAW,CAACR,IAAI,CAACpC,KAAK;IACxC,MAAMkC,CAAC,GAAGZ,SAAS,CAACjC,MAAM;IAC1BL,MAAM,CAACsC,SAAS,EAAE,CAAC,EAAEa,QAAQ,EAAE,CAAC,EAAED,CAAC,CAAC;IACpClD,MAAM,CAACsC,SAAS,EAAE,CAAC,EAAEA,SAAS,EAAE,CAAC,EAAEY,CAAC,GAAG,CAAC,CAAC;IACzCZ,SAAS,CAACY,CAAC,GAAG,CAAC,CAAC,GAAG5B,CAAC;IACpBgB,SAAS,CAACY,CAAC,GAAG,CAAC,CAAC,GAAG3B,CAAC;IACpBe,SAAS,CAACY,CAAC,GAAG,CAAC,CAAC,GAAG1B,CAAC;IACpBc,SAAS,CAACY,CAAC,GAAG,CAAC,CAAC,GAAG5B,CAAC;IACpBgB,SAAS,CAACY,CAAC,GAAG,CAAC,CAAC,GAAG3B,CAAC;IACpBe,SAAS,CAACY,CAAC,GAAG,CAAC,CAAC,GAAG1B,CAAC;IACpBxB,MAAM,CAACsC,SAAS,EAAE,CAAC,EAAEc,IAAI,EAAE,CAAC,EAAEF,CAAC,GAAG,CAAC,CAAC;IACpCE,IAAI,CAACF,CAAC,GAAG,CAAC,CAAC,GAAG5B,CAAC;IACf8B,IAAI,CAACF,CAAC,GAAG,CAAC,CAAC,GAAG3B,CAAC;IACf6B,IAAI,CAACF,CAAC,GAAG,CAAC,CAAC,GAAG1B,CAAC;IACf4B,IAAI,CAACF,CAAC,GAAG,CAAC,CAAC,GAAG5B,CAAC;IACf8B,IAAI,CAACF,CAAC,GAAG,CAAC,CAAC,GAAG3B,CAAC;IACf6B,IAAI,CAACF,CAAC,GAAG,CAAC,CAAC,GAAG1B,CAAC;IACf,IAAI,CAACoC,WAAW,CAACC,QAAQ,CAACO,WAAW,GAAG,IAAI;IAC5C,IAAI,CAACR,WAAW,CAACT,QAAQ,CAACiB,WAAW,GAAG,IAAI;IAC5C,IAAI,CAACR,WAAW,CAACR,IAAI,CAACgB,WAAW,GAAG,IAAI;EAC1C;AACF;AACA,MAAMM,YAAY,GAAG;AACrB;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,CAAC;AACD,MAAMC,OAAO,GAAG,eAAgB,CAAC,MAAMC,QAAQ,CAAC7E,KAAK,CAAC8E,QAAQ,CAACC,OAAO,CAAC,MAAM,EAAE,EAAE,CAAC,CAAC,EAAE,CAAC;AACtF,MAAMC,mBAAmB,GAAGJ,OAAO,IAAI,GAAG,GAAG,qBAAqB,GAAG,oBAAoB;AACzF,MAAMK,cAAc,GAAG;AACvB;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,gBAAgBD,mBAAmB;AACnC;AACA,CAAC;AACD,MAAME,gBAAgB,SAASlF,KAAK,CAACmF,cAAc,CAAC;EAClDtD,WAAWA,CAACuD,UAAU,EAAE;IACtB,KAAK,CAAC;MACJC,QAAQ,EAAE;QACR,GAAGrF,KAAK,CAACsF,WAAW,CAACC,GAAG;QACxBC,SAAS,EAAE;UAAE7F,KAAK,EAAE;QAAE,CAAC;QACvBuB,GAAG,EAAE;UAAEvB,KAAK,EAAE;QAAK,CAAC;QACpB8F,MAAM,EAAE;UAAE9F,KAAK,EAAE;QAAE,CAAC;QACpB+F,QAAQ,EAAE;UAAE/F,KAAK,EAAE;QAAK,CAAC;QACzBgG,WAAW,EAAE;UAAEhG,KAAK,EAAE;QAAE,CAAC;QACzBiG,KAAK,EAAE;UAAEjG,KAAK,EAAE,IAAIK,KAAK,CAAC6F,KAAK,CAAC,QAAQ;QAAE,CAAC;QAC3CC,QAAQ,EAAE;UAAEnG,KAAK,EAAE,CAAC,IAAIK,KAAK,CAAC6F,KAAK,CAAC,QAAQ,CAAC,EAAE,IAAI7F,KAAK,CAAC6F,KAAK,CAAC,KAAK,CAAC;QAAE,CAAC;QACxEE,OAAO,EAAE;UAAEpG,KAAK,EAAE;QAAE,CAAC;QACrBqG,UAAU,EAAE;UAAErG,KAAK,EAAE,IAAIK,KAAK,CAAC0B,OAAO,CAAC,CAAC,EAAE,CAAC;QAAE,CAAC;QAC9CuE,eAAe,EAAE;UAAEtG,KAAK,EAAE;QAAE,CAAC;QAC7BuG,SAAS,EAAE;UAAEvG,KAAK,EAAE;QAAE,CAAC;QACvBwG,UAAU,EAAE;UAAExG,KAAK,EAAE;QAAE,CAAC;QACxByG,SAAS,EAAE;UAAEzG,KAAK,EAAE;QAAI,CAAC;QACzB0G,OAAO,EAAE;UAAE1G,KAAK,EAAE;QAAE,CAAC;QACrB2G,WAAW,EAAE;UAAE3G,KAAK,EAAE;QAAE,CAAC;QACzB4G,UAAU,EAAE;UAAE5G,KAAK,EAAE;QAAE,CAAC;QACxB6G,SAAS,EAAE;UAAE7G,KAAK,EAAE;QAAE,CAAC;QACvB8G,MAAM,EAAE;UAAE9G,KAAK,EAAE,IAAIK,KAAK,CAAC0B,OAAO,CAAC,CAAC,EAAE,CAAC;QAAE;MAC3C,CAAC;MACDiD,YAAY;MACZM;IACF,CAAC,CAAC;IACFlF,aAAa,CAAC,IAAI,EAAE,WAAW,CAAC;IAChCA,aAAa,CAAC,IAAI,EAAE,KAAK,CAAC;IAC1BA,aAAa,CAAC,IAAI,EAAE,QAAQ,CAAC;IAC7BA,aAAa,CAAC,IAAI,EAAE,UAAU,CAAC;IAC/BA,aAAa,CAAC,IAAI,EAAE,aAAa,CAAC;IAClCA,aAAa,CAAC,IAAI,EAAE,OAAO,CAAC;IAC5BA,aAAa,CAAC,IAAI,EAAE,UAAU,CAAC;IAC/BA,aAAa,CAAC,IAAI,EAAE,YAAY,CAAC;IACjCA,aAAa,CAAC,IAAI,EAAE,iBAAiB,CAAC;IACtCA,aAAa,CAAC,IAAI,EAAE,WAAW,CAAC;IAChCA,aAAa,CAAC,IAAI,EAAE,YAAY,CAAC;IACjCA,aAAa,CAAC,IAAI,EAAE,WAAW,CAAC;IAChCA,aAAa,CAAC,IAAI,EAAE,SAAS,CAAC;IAC9BA,aAAa,CAAC,IAAI,EAAE,aAAa,CAAC;IAClCA,aAAa,CAAC,IAAI,EAAE,YAAY,CAAC;IACjCA,aAAa,CAAC,IAAI,EAAE,QAAQ,CAAC;IAC7B,IAAI,CAAC2G,IAAI,GAAG,kBAAkB;IAC9BpH,MAAM,CAACyC,gBAAgB,CAAC,IAAI,EAAE;MAC5ByD,SAAS,EAAE;QACT5F,UAAU,EAAE,IAAI;QAChBoC,GAAGA,CAAA,EAAG;UACJ,OAAO,IAAI,CAACqD,QAAQ,CAACG,SAAS,CAAC7F,KAAK;QACtC,CAAC;QACDgB,GAAGA,CAAChB,KAAK,EAAE;UACT,IAAI,CAAC0F,QAAQ,CAACG,SAAS,CAAC7F,KAAK,GAAGA,KAAK;QACvC;MACF,CAAC;MACDuB,GAAG,EAAE;QACHtB,UAAU,EAAE,IAAI;QAChBoC,GAAGA,CAAA,EAAG;UACJ,OAAO,IAAI,CAACqD,QAAQ,CAACnE,GAAG,CAACvB,KAAK;QAChC,CAAC;QACDgB,GAAGA,CAAChB,KAAK,EAAE;UACT,IAAI,CAAC0F,QAAQ,CAACnE,GAAG,CAACvB,KAAK,GAAGA,KAAK;QACjC;MACF,CAAC;MACD8F,MAAM,EAAE;QACN7F,UAAU,EAAE,IAAI;QAChBoC,GAAGA,CAAA,EAAG;UACJ,OAAO,IAAI,CAACqD,QAAQ,CAACI,MAAM,CAAC9F,KAAK;QACnC,CAAC;QACDgB,GAAGA,CAAChB,KAAK,EAAE;UACT,IAAI,CAAC0F,QAAQ,CAACI,MAAM,CAAC9F,KAAK,GAAGA,KAAK;QACpC;MACF,CAAC;MACD+F,QAAQ,EAAE;QACR9F,UAAU,EAAE,IAAI;QAChBoC,GAAGA,CAAA,EAAG;UACJ,OAAO,IAAI,CAACqD,QAAQ,CAACK,QAAQ,CAAC/F,KAAK;QACrC,CAAC;QACDgB,GAAGA,CAAChB,KAAK,EAAE;UACT,IAAI,CAAC0F,QAAQ,CAACK,QAAQ,CAAC/F,KAAK,GAAGA,KAAK;QACtC;MACF,CAAC;MACDgG,WAAW,EAAE;QACX/F,UAAU,EAAE,IAAI;QAChBoC,GAAGA,CAAA,EAAG;UACJ,OAAO,IAAI,CAACqD,QAAQ,CAACM,WAAW,CAAChG,KAAK;QACxC,CAAC;QACDgB,GAAGA,CAAChB,KAAK,EAAE;UACT,IAAI,CAAC0F,QAAQ,CAACM,WAAW,CAAChG,KAAK,GAAGA,KAAK;QACzC;MACF,CAAC;MACDiG,KAAK,EAAE;QACLhG,UAAU,EAAE,IAAI;QAChBoC,GAAGA,CAAA,EAAG;UACJ,OAAO,IAAI,CAACqD,QAAQ,CAACO,KAAK,CAACjG,KAAK;QAClC,CAAC;QACDgB,GAAGA,CAAChB,KAAK,EAAE;UACT,IAAI,CAAC0F,QAAQ,CAACO,KAAK,CAACjG,KAAK,GAAGA,KAAK;QACnC;MACF,CAAC;MACDmG,QAAQ,EAAE;QACRlG,UAAU,EAAE,IAAI;QAChBoC,GAAGA,CAAA,EAAG;UACJ,OAAO,IAAI,CAACqD,QAAQ,CAACS,QAAQ,CAACnG,KAAK;QACrC,CAAC;QACDgB,GAAGA,CAAChB,KAAK,EAAE;UACT,IAAI,CAAC0F,QAAQ,CAACS,QAAQ,CAACnG,KAAK,GAAGA,KAAK;QACtC;MACF,CAAC;MACDoG,OAAO,EAAE;QACPnG,UAAU,EAAE,IAAI;QAChBoC,GAAGA,CAAA,EAAG;UACJ,OAAO,IAAI,CAACqD,QAAQ,CAACU,OAAO,CAACpG,KAAK;QACpC,CAAC;QACDgB,GAAGA,CAAChB,KAAK,EAAE;UACT,IAAI,CAAC0F,QAAQ,CAACU,OAAO,CAACpG,KAAK,GAAGA,KAAK;QACrC;MACF,CAAC;MACDqG,UAAU,EAAE;QACVpG,UAAU,EAAE,IAAI;QAChBoC,GAAGA,CAAA,EAAG;UACJ,OAAO,IAAI,CAACqD,QAAQ,CAACW,UAAU,CAACrG,KAAK;QACvC,CAAC;QACDgB,GAAGA,CAAChB,KAAK,EAAE;UACT,IAAI,CAAC0F,QAAQ,CAACW,UAAU,CAACrG,KAAK,CAACgH,IAAI,CAAChH,KAAK,CAAC;QAC5C;MACF,CAAC;MACDsG,eAAe,EAAE;QACfrG,UAAU,EAAE,IAAI;QAChBoC,GAAGA,CAAA,EAAG;UACJ,OAAO,IAAI,CAACqD,QAAQ,CAACY,eAAe,CAACtG,KAAK;QAC5C,CAAC;QACDgB,GAAGA,CAAChB,KAAK,EAAE;UACT,IAAI,CAAC0F,QAAQ,CAACY,eAAe,CAACtG,KAAK,GAAGA,KAAK;QAC7C;MACF,CAAC;MACDuG,SAAS,EAAE;QACTtG,UAAU,EAAE,IAAI;QAChBoC,GAAGA,CAAA,EAAG;UACJ,OAAO,IAAI,CAACqD,QAAQ,CAACa,SAAS,CAACvG,KAAK;QACtC,CAAC;QACDgB,GAAGA,CAAChB,KAAK,EAAE;UACT,IAAI,CAAC0F,QAAQ,CAACa,SAAS,CAACvG,KAAK,GAAGA,KAAK;UACrC,IAAI,CAAC0G,OAAO,GAAG1G,KAAK,KAAK,CAAC,GAAG,CAAC,GAAG,CAAC;QACpC;MACF,CAAC;MACDwG,UAAU,EAAE;QACVvG,UAAU,EAAE,IAAI;QAChBoC,GAAGA,CAAA,EAAG;UACJ,OAAO,IAAI,CAACqD,QAAQ,CAACc,UAAU,CAACxG,KAAK;QACvC,CAAC;QACDgB,GAAGA,CAAChB,KAAK,EAAE;UACT,IAAI,CAAC0F,QAAQ,CAACc,UAAU,CAACxG,KAAK,GAAGA,KAAK;QACxC;MACF,CAAC;MACDyG,SAAS,EAAE;QACTxG,UAAU,EAAE,IAAI;QAChBoC,GAAGA,CAAA,EAAG;UACJ,OAAO,IAAI,CAACqD,QAAQ,CAACe,SAAS,CAACzG,KAAK;QACtC,CAAC;QACDgB,GAAGA,CAAChB,KAAK,EAAE;UACT,IAAI,CAAC0F,QAAQ,CAACe,SAAS,CAACzG,KAAK,GAAGA,KAAK;QACvC;MACF,CAAC;MACD0G,OAAO,EAAE;QACPzG,UAAU,EAAE,IAAI;QAChBoC,GAAGA,CAAA,EAAG;UACJ,OAAO,IAAI,CAACqD,QAAQ,CAACgB,OAAO,CAAC1G,KAAK;QACpC,CAAC;QACDgB,GAAGA,CAAChB,KAAK,EAAE;UACT,IAAI,CAAC0F,QAAQ,CAACgB,OAAO,CAAC1G,KAAK,GAAGA,KAAK;QACrC;MACF,CAAC;MACD2G,WAAW,EAAE;QACX1G,UAAU,EAAE,IAAI;QAChBoC,GAAGA,CAAA,EAAG;UACJ,OAAO,IAAI,CAACqD,QAAQ,CAACiB,WAAW,CAAC3G,KAAK;QACxC,CAAC;QACDgB,GAAGA,CAAChB,KAAK,EAAE;UACT,IAAI,CAAC0F,QAAQ,CAACiB,WAAW,CAAC3G,KAAK,GAAGA,KAAK;QACzC;MACF,CAAC;MACD4G,UAAU,EAAE;QACV3G,UAAU,EAAE,IAAI;QAChBoC,GAAGA,CAAA,EAAG;UACJ,OAAO,IAAI,CAACqD,QAAQ,CAACkB,UAAU,CAAC5G,KAAK;QACvC,CAAC;QACDgB,GAAGA,CAAChB,KAAK,EAAE;UACT,IAAI,CAAC0F,QAAQ,CAACkB,UAAU,CAAC5G,KAAK,GAAGA,KAAK;QACxC;MACF,CAAC;MACD6G,SAAS,EAAE;QACT5G,UAAU,EAAE,IAAI;QAChBoC,GAAGA,CAAA,EAAG;UACJ,OAAO,IAAI,CAACqD,QAAQ,CAACmB,SAAS,CAAC7G,KAAK;QACtC,CAAC;QACDgB,GAAGA,CAAChB,KAAK,EAAE;UACT,IAAI,CAAC0F,QAAQ,CAACmB,SAAS,CAAC7G,KAAK,GAAGA,KAAK;QACvC;MACF,CAAC;MACD8G,MAAM,EAAE;QACN7G,UAAU,EAAE,IAAI;QAChBoC,GAAGA,CAAA,EAAG;UACJ,OAAO,IAAI,CAACqD,QAAQ,CAACoB,MAAM,CAAC9G,KAAK;QACnC,CAAC;QACDgB,GAAGA,CAAChB,KAAK,EAAE;UACT,IAAI,CAAC0F,QAAQ,CAACoB,MAAM,CAAC9G,KAAK,CAACgH,IAAI,CAAChH,KAAK,CAAC;QACxC;MACF;IACF,CAAC,CAAC;IACF,IAAI,CAACiH,SAAS,CAACxB,UAAU,CAAC;EAC5B;EACAuB,IAAIA,CAACE,MAAM,EAAE;IACX,KAAK,CAACF,IAAI,CAACE,MAAM,CAAC;IAClB,IAAI,CAACrB,SAAS,GAAGqB,MAAM,CAACrB,SAAS;IACjC,IAAI,CAACtE,GAAG,GAAG2F,MAAM,CAAC3F,GAAG;IACrB,IAAI,CAACuE,MAAM,GAAGoB,MAAM,CAACpB,MAAM;IAC3B,IAAI,CAACC,QAAQ,GAAGmB,MAAM,CAACnB,QAAQ;IAC/B,IAAI,CAACC,WAAW,GAAGkB,MAAM,CAAClB,WAAW;IACrC,IAAI,CAACC,KAAK,CAACe,IAAI,CAACE,MAAM,CAACjB,KAAK,CAAC;IAC7B,IAAI,CAACE,QAAQ,GAAGe,MAAM,CAACf,QAAQ;IAC/B,IAAI,CAACC,OAAO,GAAGc,MAAM,CAACd,OAAO;IAC7B,IAAI,CAACC,UAAU,CAACW,IAAI,CAACE,MAAM,CAACb,UAAU,CAAC;IACvC,IAAI,CAACC,eAAe,GAAGY,MAAM,CAACZ,eAAe;IAC7C,IAAI,CAACC,SAAS,GAAGW,MAAM,CAACX,SAAS;IACjC,IAAI,CAACC,UAAU,GAAGU,MAAM,CAACV,UAAU;IACnC,IAAI,CAACC,SAAS,GAAGS,MAAM,CAACT,SAAS;IACjC,IAAI,CAACC,OAAO,GAAGQ,MAAM,CAACR,OAAO;IAC7B,IAAI,CAACC,WAAW,GAAGO,MAAM,CAACP,WAAW;IACrC,IAAI,CAACC,UAAU,GAAGM,MAAM,CAACN,UAAU;IACnC,IAAI,CAACC,SAAS,GAAGK,MAAM,CAACL,SAAS;IACjC,IAAI,CAACC,MAAM,CAACE,IAAI,CAACE,MAAM,CAACJ,MAAM,CAAC;IAC/B,OAAO,IAAI;EACb;AACF;AACA,SAASK,OAAOA,CAACC,SAAS,EAAEC,UAAU,EAAE;EACtC,MAAMC,aAAa,GAAG,IAAIjH,KAAK,CAAC8B,OAAO,CAAC,CAAC;EACzC,MAAMoF,GAAG,GAAG,IAAIlH,KAAK,CAACmH,GAAG,CAAC,CAAC;EAC3B,MAAMC,MAAM,GAAG,IAAIpH,KAAK,CAACqH,MAAM,CAAC,CAAC;EACjC,MAAMC,QAAQ,GAAG,IAAItH,KAAK,CAACsB,OAAO,CAAC,CAAC;EACpC,MAAMiG,QAAQ,GAAG,IAAI,CAACA,QAAQ;EAC9BH,MAAM,CAACT,IAAI,CAACY,QAAQ,CAACC,cAAc,CAAC;EACpCJ,MAAM,CAACK,YAAY,CAAC,IAAI,CAACpF,WAAW,CAAC;EACrC,IAAI,CAAC0E,SAAS,CAACG,GAAG,CAACQ,eAAe,CAACN,MAAM,EAAEE,QAAQ,CAAC,EAClD;EACFL,aAAa,CAACN,IAAI,CAAC,IAAI,CAACtE,WAAW,CAAC,CAACsF,MAAM,CAAC,CAAC;EAC7CT,GAAG,CAACP,IAAI,CAACI,SAAS,CAACG,GAAG,CAAC,CAACO,YAAY,CAACR,aAAa,CAAC;EACnD,MAAMW,MAAM,GAAG,IAAI5H,KAAK,CAACsB,OAAO,CAAC,CAAC;EAClC,MAAMuG,IAAI,GAAG,IAAI7H,KAAK,CAACsB,OAAO,CAAC,CAAC;EAChC,MAAMwG,YAAY,GAAG,IAAI9H,KAAK,CAACsB,OAAO,CAAC,CAAC;EACxC,MAAMyG,IAAI,GAAG,IAAI,YAAY/H,KAAK,CAACgI,YAAY,GAAG,CAAC,GAAG,CAAC;EACvD,MAAM9D,KAAK,GAAGqD,QAAQ,CAACrD,KAAK;EAC5B,MAAM+D,UAAU,GAAGV,QAAQ,CAACU,UAAU;EACtC,IAAI/D,KAAK,KAAK,IAAI,EAAE;IAClB,MAAMgE,OAAO,GAAGhE,KAAK,CAACjD,KAAK;IAC3B,MAAMsB,SAAS,GAAG0F,UAAU,CAACnE,QAAQ,CAAC7C,KAAK;IAC3C,MAAMkH,MAAM,GAAGF,UAAU,CAAC1E,KAAK,CAACtC,KAAK;IACrC,KAAK,IAAIV,CAAC,GAAG,CAAC,EAAE4C,CAAC,GAAG+E,OAAO,CAAC5H,MAAM,GAAG,CAAC,EAAEC,CAAC,GAAG4C,CAAC,EAAE5C,CAAC,IAAIwH,IAAI,EAAE;MACxD,MAAMjF,CAAC,GAAGoF,OAAO,CAAC3H,CAAC,CAAC;MACpB,MAAMwC,CAAC,GAAGmF,OAAO,CAAC3H,CAAC,GAAG,CAAC,CAAC;MACxBqH,MAAM,CAACQ,SAAS,CAAC7F,SAAS,EAAEO,CAAC,GAAG,CAAC,CAAC;MAClC+E,IAAI,CAACO,SAAS,CAAC7F,SAAS,EAAEQ,CAAC,GAAG,CAAC,CAAC;MAChC,MAAMQ,KAAK,GAAG4E,MAAM,CAACE,IAAI,CAACC,KAAK,CAAC/H,CAAC,GAAG,CAAC,CAAC,CAAC,IAAI,KAAK,CAAC,GAAG4H,MAAM,CAACE,IAAI,CAACC,KAAK,CAAC/H,CAAC,GAAG,CAAC,CAAC,CAAC,GAAG,CAAC;MACjF,MAAMgI,SAAS,GAAGxB,SAAS,CAACyB,MAAM,CAACC,IAAI,CAACC,SAAS,GAAG,IAAI,CAACC,QAAQ,CAACnD,SAAS,GAAGjC,KAAK,GAAG,CAAC;MACvF,MAAMqF,WAAW,GAAGL,SAAS,GAAGA,SAAS;MACzC,MAAMM,MAAM,GAAG3B,GAAG,CAAC4B,mBAAmB,CAAClB,MAAM,EAAEC,IAAI,EAAEP,QAAQ,EAAEQ,YAAY,CAAC;MAC5E,IAAIe,MAAM,GAAGD,WAAW,EACtB;MACFtB,QAAQ,CAACG,YAAY,CAAC,IAAI,CAACpF,WAAW,CAAC;MACvC,MAAM0G,QAAQ,GAAGhC,SAAS,CAACG,GAAG,CAAC8B,MAAM,CAACC,UAAU,CAAC3B,QAAQ,CAAC;MAC1D,IAAIyB,QAAQ,GAAGhC,SAAS,CAACmC,IAAI,IAAIH,QAAQ,GAAGhC,SAAS,CAACoC,GAAG,EACvD;MACFnC,UAAU,CAACrE,IAAI,CAAC;QACdoG,QAAQ;QACRK,KAAK,EAAEtB,YAAY,CAACuB,KAAK,CAAC,CAAC,CAAC5B,YAAY,CAAC,IAAI,CAACpF,WAAW,CAAC;QAC1D6B,KAAK,EAAE3D,CAAC;QACR+I,IAAI,EAAE,IAAI;QACVC,SAAS,EAAE,KAAK,CAAC;QACjBC,MAAM,EAAE;MACV,CAAC,CAAC;MACFjJ,CAAC,GAAG4C,CAAC;IACP;EACF;AACF;AACA,SACEvB,gBAAgB,EAChBsD,gBAAgB,EAChB4B,OAAO", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}