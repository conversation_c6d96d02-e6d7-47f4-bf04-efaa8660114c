{"ast": null, "code": "import { Vector2 } from \"three\";\nconst <PERSON><PERSON><PERSON><PERSON><PERSON>had<PERSON> = {\n  uniforms: {\n    tDiffuse: {\n      value: null\n    },\n    aspect: {\n      value: /* @__PURE__ */new Vector2(512, 512)\n    }\n  },\n  vertexShader: (/* glsl */\n  `\n    varying vec2 vUv;\n\n    void main() {\n\n    \tvUv = uv;\n    \tgl_Position = projectionMatrix * modelViewMatrix * vec4( position, 1.0 );\n\n    }\n  `),\n  fragmentShader: (/* glsl */\n  `\n    uniform sampler2D tDiffuse;\n    varying vec2 vUv;\n\n    uniform vec2 aspect;\n\n    vec2 texel = vec2(1.0 / aspect.x, 1.0 / aspect.y);\n\n    mat3 G[9];\n\n    // hard coded matrix values!!!! as suggested in https://github.com/neilmendoza/ofxPostProcessing/blob/master/src/EdgePass.cpp#L45\n\n    const mat3 g0 = mat3( 0.3535533845424652, 0, -0.3535533845424652, 0.5, 0, -0.5, 0.3535533845424652, 0, -0.3535533845424652 );\n    const mat3 g1 = mat3( 0.3535533845424652, 0.5, 0.3535533845424652, 0, 0, 0, -0.3535533845424652, -0.5, -0.3535533845424652 );\n    const mat3 g2 = mat3( 0, 0.3535533845424652, -0.5, -0.3535533845424652, 0, 0.3535533845424652, 0.5, -0.3535533845424652, 0 );\n    const mat3 g3 = mat3( 0.5, -0.3535533845424652, 0, -0.3535533845424652, 0, 0.3535533845424652, 0, 0.3535533845424652, -0.5 );\n    const mat3 g4 = mat3( 0, -0.5, 0, 0.5, 0, 0.5, 0, -0.5, 0 );\n    const mat3 g5 = mat3( -0.5, 0, 0.5, 0, 0, 0, 0.5, 0, -0.5 );\n    const mat3 g6 = mat3( 0.1666666716337204, -0.3333333432674408, 0.1666666716337204, -0.3333333432674408, 0.6666666865348816, -0.3333333432674408, 0.1666666716337204, -0.3333333432674408, 0.1666666716337204 );\n    const mat3 g7 = mat3( -0.3333333432674408, 0.1666666716337204, -0.3333333432674408, 0.1666666716337204, 0.6666666865348816, 0.1666666716337204, -0.3333333432674408, 0.1666666716337204, -0.3333333432674408 );\n    const mat3 g8 = mat3( 0.3333333432674408, 0.3333333432674408, 0.3333333432674408, 0.3333333432674408, 0.3333333432674408, 0.3333333432674408, 0.3333333432674408, 0.3333333432674408, 0.3333333432674408 );\n\n    void main(void)\n    {\n\n    \tG[0] = g0,\n    \tG[1] = g1,\n    \tG[2] = g2,\n    \tG[3] = g3,\n    \tG[4] = g4,\n    \tG[5] = g5,\n    \tG[6] = g6,\n    \tG[7] = g7,\n    \tG[8] = g8;\n\n    \tmat3 I;\n    \tfloat cnv[9];\n    \tvec3 sample;\n\n    /* fetch the 3x3 neighbourhood and use the RGB vectors length as intensity value */\n    \tfor (float i=0.0; i<3.0; i++) {\n    \t\tfor (float j=0.0; j<3.0; j++) {\n    \t\t\tsample = texture2D(tDiffuse, vUv + texel * vec2(i-1.0,j-1.0) ).rgb;\n    \t\t\tI[int(i)][int(j)] = length(sample);\n    \t\t}\n    \t}\n\n    /* calculate the convolution values for all the masks */\n    \tfor (int i=0; i<9; i++) {\n    \t\tfloat dp3 = dot(G[i][0], I[0]) + dot(G[i][1], I[1]) + dot(G[i][2], I[2]);\n    \t\tcnv[i] = dp3 * dp3;\n    \t}\n\n    \tfloat M = (cnv[0] + cnv[1]) + (cnv[2] + cnv[3]);\n    \tfloat S = (cnv[4] + cnv[5]) + (cnv[6] + cnv[7]) + (cnv[8] + M);\n\n    \tgl_FragColor = vec4(vec3(sqrt(M/S)), 1.0);\n    }\n  `)\n};\nexport { FreiChenShader };", "map": {"version": 3, "names": ["<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "uniforms", "tDiffuse", "value", "aspect", "Vector2", "vertexShader", "fragmentShader"], "sources": ["C:\\Users\\<USER>\\Documents\\Apps_Creations\\SiahMan_Racing4Speed_WebGame\\siah-man-racing\\node_modules\\src\\shaders\\FreiChenShader.ts"], "sourcesContent": ["import { Vector2 } from 'three'\n\n/**\n * Edge Detection Shader using <PERSON><PERSON><PERSON>Chen filter\n * Based on http://rastergrid.com/blog/2011/01/frei-chen-edge-detector\n *\n * aspect: vec2 of (1/width, 1/height)\n */\n\nexport const Frei<PERSON>hen<PERSON>hader = {\n  uniforms: {\n    tDiffuse: { value: null },\n    aspect: { value: /* @__PURE__ */ new Vector2(512, 512) },\n  },\n\n  vertexShader: /* glsl */ `\n    varying vec2 vUv;\n\n    void main() {\n\n    \tvUv = uv;\n    \tgl_Position = projectionMatrix * modelViewMatrix * vec4( position, 1.0 );\n\n    }\n  `,\n\n  fragmentShader: /* glsl */ `\n    uniform sampler2D tDiffuse;\n    varying vec2 vUv;\n\n    uniform vec2 aspect;\n\n    vec2 texel = vec2(1.0 / aspect.x, 1.0 / aspect.y);\n\n    mat3 G[9];\n\n    // hard coded matrix values!!!! as suggested in https://github.com/neilmendoza/ofxPostProcessing/blob/master/src/EdgePass.cpp#L45\n\n    const mat3 g0 = mat3( 0.3535533845424652, 0, -0.3535533845424652, 0.5, 0, -0.5, 0.3535533845424652, 0, -0.3535533845424652 );\n    const mat3 g1 = mat3( 0.3535533845424652, 0.5, 0.3535533845424652, 0, 0, 0, -0.3535533845424652, -0.5, -0.3535533845424652 );\n    const mat3 g2 = mat3( 0, 0.3535533845424652, -0.5, -0.3535533845424652, 0, 0.3535533845424652, 0.5, -0.3535533845424652, 0 );\n    const mat3 g3 = mat3( 0.5, -0.3535533845424652, 0, -0.3535533845424652, 0, 0.3535533845424652, 0, 0.3535533845424652, -0.5 );\n    const mat3 g4 = mat3( 0, -0.5, 0, 0.5, 0, 0.5, 0, -0.5, 0 );\n    const mat3 g5 = mat3( -0.5, 0, 0.5, 0, 0, 0, 0.5, 0, -0.5 );\n    const mat3 g6 = mat3( 0.1666666716337204, -0.3333333432674408, 0.1666666716337204, -0.3333333432674408, 0.6666666865348816, -0.3333333432674408, 0.1666666716337204, -0.3333333432674408, 0.1666666716337204 );\n    const mat3 g7 = mat3( -0.3333333432674408, 0.1666666716337204, -0.3333333432674408, 0.1666666716337204, 0.6666666865348816, 0.1666666716337204, -0.3333333432674408, 0.1666666716337204, -0.3333333432674408 );\n    const mat3 g8 = mat3( 0.3333333432674408, 0.3333333432674408, 0.3333333432674408, 0.3333333432674408, 0.3333333432674408, 0.3333333432674408, 0.3333333432674408, 0.3333333432674408, 0.3333333432674408 );\n\n    void main(void)\n    {\n\n    \tG[0] = g0,\n    \tG[1] = g1,\n    \tG[2] = g2,\n    \tG[3] = g3,\n    \tG[4] = g4,\n    \tG[5] = g5,\n    \tG[6] = g6,\n    \tG[7] = g7,\n    \tG[8] = g8;\n\n    \tmat3 I;\n    \tfloat cnv[9];\n    \tvec3 sample;\n\n    /* fetch the 3x3 neighbourhood and use the RGB vectors length as intensity value */\n    \tfor (float i=0.0; i<3.0; i++) {\n    \t\tfor (float j=0.0; j<3.0; j++) {\n    \t\t\tsample = texture2D(tDiffuse, vUv + texel * vec2(i-1.0,j-1.0) ).rgb;\n    \t\t\tI[int(i)][int(j)] = length(sample);\n    \t\t}\n    \t}\n\n    /* calculate the convolution values for all the masks */\n    \tfor (int i=0; i<9; i++) {\n    \t\tfloat dp3 = dot(G[i][0], I[0]) + dot(G[i][1], I[1]) + dot(G[i][2], I[2]);\n    \t\tcnv[i] = dp3 * dp3;\n    \t}\n\n    \tfloat M = (cnv[0] + cnv[1]) + (cnv[2] + cnv[3]);\n    \tfloat S = (cnv[4] + cnv[5]) + (cnv[6] + cnv[7]) + (cnv[8] + M);\n\n    \tgl_FragColor = vec4(vec3(sqrt(M/S)), 1.0);\n    }\n  `,\n}\n"], "mappings": ";AASO,MAAMA,cAAA,GAAiB;EAC5BC,QAAA,EAAU;IACRC,QAAA,EAAU;MAAEC,KAAA,EAAO;IAAK;IACxBC,MAAA,EAAQ;MAAED,KAAA,qBAA2BE,OAAA,CAAQ,KAAK,GAAG;IAAE;EACzD;EAEAC,YAAA;EAAyB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;EAWzBC,cAAA;EAA2B;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AA2D7B", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}