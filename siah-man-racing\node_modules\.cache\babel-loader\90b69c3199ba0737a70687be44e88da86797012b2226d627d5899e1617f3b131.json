{"ast": null, "code": "var __defProp = Object.defineProperty;\nvar __defNormalProp = (obj, key, value) => key in obj ? __defProp(obj, key, {\n  enumerable: true,\n  configurable: true,\n  writable: true,\n  value\n}) : obj[key] = value;\nvar __publicField = (obj, key, value) => {\n  __defNormalProp(obj, typeof key !== \"symbol\" ? key + \"\" : key, value);\n  return value;\n};\nimport { Mesh, BufferGeometry, MathUtils, DataTexture, RGBAFormat, FloatType, BufferAttribute, Matrix4 } from \"three\";\nconst ID_ATTR_NAME = \"_batch_id_\";\nconst _identityMatrix = /* @__PURE__ */new Matrix4();\nconst _zeroScaleMatrix = /* @__PURE__ */(() => new Matrix4().set(0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1))();\nconst batchingParsVertex = /* glsl */\n`\n#ifdef BATCHING\n\tattribute float ${ID_ATTR_NAME};\n\tuniform highp sampler2D batchingTexture;\n\tmat4 getBatchingMatrix( const in float i ) {\n\n\t\tint size = textureSize( batchingTexture, 0 ).x;\n\t\tint j = int( i ) * 4;\n\t\tint x = j % size;\n\t\tint y = j / size;\n\t\tvec4 v1 = texelFetch( batchingTexture, ivec2( x, y ), 0 );\n\t\tvec4 v2 = texelFetch( batchingTexture, ivec2( x + 1, y ), 0 );\n\t\tvec4 v3 = texelFetch( batchingTexture, ivec2( x + 2, y ), 0 );\n\t\tvec4 v4 = texelFetch( batchingTexture, ivec2( x + 3, y ), 0 );\n\t\treturn mat4( v1, v2, v3, v4 );\n\n\t}\n#endif\n`;\nconst batchingbaseVertex = /* glsl */\n`\n#ifdef BATCHING\n\tmat4 batchingMatrix = getBatchingMatrix( ${ID_ATTR_NAME} );\n#endif\n`;\nconst batchingnormalVertex = /* glsl */\n`\n#ifdef BATCHING\n\tobjectNormal = vec4( batchingMatrix * vec4( objectNormal, 0.0 ) ).xyz;\n\t#ifdef USE_TANGENT\n\t\tobjectTangent = vec4( batchingMatrix * vec4( objectTangent, 0.0 ) ).xyz;\n\t#endif\n#endif\n`;\nconst batchingVertex = /* glsl */\n`\n#ifdef BATCHING\n\ttransformed = ( batchingMatrix * vec4( transformed, 1.0 ) ).xyz;\n#endif\n`;\nfunction copyAttributeData(src, target, targetOffset = 0) {\n  const itemSize = target.itemSize;\n  if (src.isInterleavedBufferAttribute || src.array.constructor !== target.array.constructor) {\n    const vertexCount = src.count;\n    for (let i = 0; i < vertexCount; i++) {\n      for (let c = 0; c < itemSize; c++) {\n        target.setComponent(i + targetOffset, c, src.getComponent(i, c));\n      }\n    }\n  } else {\n    target.array.set(src.array, targetOffset * itemSize);\n  }\n  target.needsUpdate = true;\n}\nclass BatchedMesh extends Mesh {\n  constructor(maxGeometryCount, maxVertexCount, maxIndexCount = maxVertexCount * 2, material) {\n    super(new BufferGeometry(), material);\n    __publicField(this, \"_vertexStarts\");\n    __publicField(this, \"_vertexCounts\");\n    __publicField(this, \"_indexStarts\");\n    __publicField(this, \"_indexCounts\");\n    __publicField(this, \"_reservedRanges\");\n    __publicField(this, \"_visible\");\n    __publicField(this, \"_active\");\n    __publicField(this, \"_maxGeometryCount\");\n    __publicField(this, \"_maxVertexCount\");\n    __publicField(this, \"_maxIndexCount\");\n    __publicField(this, \"_geometryInitialized\");\n    __publicField(this, \"_geometryCount\");\n    __publicField(this, \"_matrices\");\n    __publicField(this, \"_matricesTexture\");\n    __publicField(this, \"_customUniforms\");\n    this._vertexStarts = [];\n    this._vertexCounts = [];\n    this._indexStarts = [];\n    this._indexCounts = [];\n    this._reservedRanges = [];\n    this._visible = [];\n    this._active = [];\n    this._maxGeometryCount = maxGeometryCount;\n    this._maxVertexCount = maxVertexCount;\n    this._maxIndexCount = maxIndexCount;\n    this._geometryInitialized = false;\n    this._geometryCount = 0;\n    this._matrices = [];\n    this._matricesTexture = null;\n    this.frustumCulled = false;\n    this._customUniforms = {\n      batchingTexture: {\n        value: null\n      }\n    };\n    this._initMatricesTexture();\n    this._initShader();\n    this.onBeforeRender = function () {\n      if (this.material.defines) {\n        this.material.defines.BATCHING = true;\n      }\n    };\n    this.onAfterRender = function () {\n      if (this.material.defines) {\n        this.material.defines.BATCHING = false;\n      }\n    };\n  }\n  _initMatricesTexture() {\n    let size = Math.sqrt(this._maxGeometryCount * 4);\n    size = MathUtils.ceilPowerOfTwo(size);\n    size = Math.max(size, 4);\n    const matricesArray = new Float32Array(size * size * 4);\n    const matricesTexture = new DataTexture(matricesArray, size, size, RGBAFormat, FloatType);\n    this._matricesTexture = matricesTexture;\n    this._customUniforms.batchingTexture.value = this._matricesTexture;\n  }\n  _initShader() {\n    const material = this.material;\n    const currentOnBeforeCompile = material.onBeforeCompile;\n    const customUniforms = this._customUniforms;\n    material.onBeforeCompile = function onBeforeCompile(parameters, renderer) {\n      parameters.vertexShader = parameters.vertexShader.replace(\"#include <skinning_pars_vertex>\", \"#include <skinning_pars_vertex>\\n\" + batchingParsVertex).replace(\"#include <uv_vertex>\", \"#include <uv_vertex>\\n\" + batchingbaseVertex).replace(\"#include <skinnormal_vertex>\", \"#include <skinnormal_vertex>\\n\" + batchingnormalVertex).replace(\"#include <skinning_vertex>\", \"#include <skinning_vertex>\\n\" + batchingVertex);\n      for (const uniformName in customUniforms) {\n        parameters.uniforms[uniformName] = customUniforms[uniformName];\n      }\n      currentOnBeforeCompile.call(this, parameters, renderer);\n    };\n    material.defines = material.defines || {};\n    material.defines.BATCHING = false;\n  }\n  _initializeGeometry(reference) {\n    const geometry = this.geometry;\n    const maxVertexCount = this._maxVertexCount;\n    const maxGeometryCount = this._maxGeometryCount;\n    const maxIndexCount = this._maxIndexCount;\n    if (this._geometryInitialized === false) {\n      for (const attributeName in reference.attributes) {\n        const srcAttribute = reference.getAttribute(attributeName);\n        const {\n          array,\n          itemSize,\n          normalized\n        } = srcAttribute;\n        const dstArray = new array.constructor(maxVertexCount * itemSize);\n        const dstAttribute = new srcAttribute.constructor(dstArray, itemSize, normalized);\n        dstAttribute.setUsage(srcAttribute.usage);\n        geometry.setAttribute(attributeName, dstAttribute);\n      }\n      if (reference.getIndex() !== null) {\n        const indexArray = maxVertexCount > 65536 ? new Uint32Array(maxIndexCount) : new Uint16Array(maxIndexCount);\n        geometry.setIndex(new BufferAttribute(indexArray, 1));\n      }\n      const idArray = maxGeometryCount > 65536 ? new Uint32Array(maxVertexCount) : new Uint16Array(maxVertexCount);\n      geometry.setAttribute(ID_ATTR_NAME, new BufferAttribute(idArray, 1));\n      this._geometryInitialized = true;\n    }\n  }\n  // Make sure the geometry is compatible with the existing combined geometry atributes\n  _validateGeometry(geometry) {\n    if (geometry.getAttribute(ID_ATTR_NAME)) {\n      throw new Error(`BatchedMesh: Geometry cannot use attribute \"${ID_ATTR_NAME}\"`);\n    }\n    const batchGeometry = this.geometry;\n    if (Boolean(geometry.getIndex()) !== Boolean(batchGeometry.getIndex())) {\n      throw new Error('BatchedMesh: All geometries must consistently have \"index\".');\n    }\n    for (const attributeName in batchGeometry.attributes) {\n      if (attributeName === ID_ATTR_NAME) {\n        continue;\n      }\n      if (!geometry.hasAttribute(attributeName)) {\n        throw new Error(`BatchedMesh: Added geometry missing \"${attributeName}\". All geometries must have consistent attributes.`);\n      }\n      const srcAttribute = geometry.getAttribute(attributeName);\n      const dstAttribute = batchGeometry.getAttribute(attributeName);\n      if (srcAttribute.itemSize !== dstAttribute.itemSize || srcAttribute.normalized !== dstAttribute.normalized) {\n        throw new Error(\"BatchedMesh: All attributes must have a consistent itemSize and normalized value.\");\n      }\n    }\n  }\n  getGeometryCount() {\n    return this._geometryCount;\n  }\n  getVertexCount() {\n    const reservedRanges = this._reservedRanges;\n    if (reservedRanges.length === 0) {\n      return 0;\n    } else {\n      const finalRange = reservedRanges[reservedRanges.length - 1];\n      return finalRange.vertexStart + finalRange.vertexCount;\n    }\n  }\n  getIndexCount() {\n    const reservedRanges = this._reservedRanges;\n    const geometry = this.geometry;\n    if (geometry.getIndex() === null || reservedRanges.length === 0) {\n      return 0;\n    } else {\n      const finalRange = reservedRanges[reservedRanges.length - 1];\n      return finalRange.indexStart + finalRange.indexCount;\n    }\n  }\n  addGeometry(geometry, vertexCount = -1, indexCount = -1) {\n    this._initializeGeometry(geometry);\n    this._validateGeometry(geometry);\n    if (this._geometryCount >= this._maxGeometryCount) {\n      throw new Error(\"BatchedMesh: Maximum geometry count reached.\");\n    }\n    const range = {\n      vertexStart: -1,\n      vertexCount: -1,\n      indexStart: -1,\n      indexCount: -1\n    };\n    let lastRange = null;\n    const reservedRanges = this._reservedRanges;\n    if (this._geometryCount !== 0) {\n      lastRange = reservedRanges[reservedRanges.length - 1];\n    }\n    if (vertexCount === -1) {\n      range.vertexCount = geometry.getAttribute(\"position\").count;\n    } else {\n      range.vertexCount = vertexCount;\n    }\n    if (lastRange === null) {\n      range.vertexStart = 0;\n    } else {\n      range.vertexStart = lastRange.vertexStart + lastRange.vertexCount;\n    }\n    if (geometry.getIndex() !== null) {\n      if (indexCount === -1) {\n        range.indexCount = geometry.getIndex().count;\n      } else {\n        range.indexCount = indexCount;\n      }\n      if (lastRange === null) {\n        range.indexStart = 0;\n      } else {\n        range.indexStart = lastRange.indexStart + lastRange.indexCount;\n      }\n    }\n    if (range.indexStart !== -1 && range.indexStart + range.indexCount > this._maxIndexCount || range.vertexStart + range.vertexCount > this._maxVertexCount) {\n      throw new Error(\"BatchedMesh: Reserved space request exceeds the maximum buffer size.\");\n    }\n    const indexCounts = this._indexCounts;\n    const indexStarts = this._indexStarts;\n    const vertexCounts = this._vertexCounts;\n    const vertexStarts = this._vertexStarts;\n    const visible = this._visible;\n    const active = this._active;\n    const matricesTexture = this._matricesTexture;\n    const matrices = this._matrices;\n    const matricesArray = this._matricesTexture.image.data;\n    visible.push(true);\n    active.push(true);\n    const geometryId = this._geometryCount;\n    this._geometryCount++;\n    matrices.push(new Matrix4());\n    _identityMatrix.toArray(matricesArray, geometryId * 16);\n    matricesTexture.needsUpdate = true;\n    reservedRanges.push(range);\n    vertexStarts.push(range.vertexStart);\n    vertexCounts.push(range.vertexCount);\n    if (geometry.getIndex() !== null) {\n      indexStarts.push(range.indexCount);\n      indexCounts.push(range.indexCount);\n    }\n    const idAttribute = this.geometry.getAttribute(ID_ATTR_NAME);\n    for (let i = 0; i < range.vertexCount; i++) {\n      idAttribute.setX(range.vertexStart + i, geometryId);\n    }\n    idAttribute.needsUpdate = true;\n    this.setGeometryAt(geometryId, geometry);\n    return geometryId;\n  }\n  /**\n   * @deprecated use `addGeometry` instead.\n   */\n  applyGeometry(geometry) {\n    return this.addGeometry(geometry);\n  }\n  setGeometryAt(id, geometry) {\n    if (id >= this._geometryCount) {\n      throw new Error(\"BatchedMesh: Maximum geometry count reached.\");\n    }\n    this._validateGeometry(geometry);\n    const range = this._reservedRanges[id];\n    if (geometry.getIndex() !== null && geometry.getIndex().count > range.indexCount || geometry.attributes.position.count > range.vertexCount) {\n      throw new Error(\"BatchedMesh: Reserved space not large enough for provided geometry.\");\n    }\n    const batchGeometry = this.geometry;\n    const srcPositionAttribute = geometry.getAttribute(\"position\");\n    const hasIndex = batchGeometry.getIndex() !== null;\n    const dstIndex = batchGeometry.getIndex();\n    const srcIndex = geometry.getIndex();\n    const vertexStart = range.vertexStart;\n    const vertexCount = range.vertexCount;\n    for (const attributeName in batchGeometry.attributes) {\n      if (attributeName === ID_ATTR_NAME) {\n        continue;\n      }\n      const srcAttribute = geometry.getAttribute(attributeName);\n      const dstAttribute = batchGeometry.getAttribute(attributeName);\n      copyAttributeData(srcAttribute, dstAttribute, vertexStart);\n      const itemSize = srcAttribute.itemSize;\n      for (let i = srcAttribute.count, l = vertexCount; i < l; i++) {\n        const index = vertexStart + i;\n        for (let c = 0; c < itemSize; c++) {\n          dstAttribute.setComponent(index, c, 0);\n        }\n      }\n      dstAttribute.needsUpdate = true;\n    }\n    this._vertexCounts[id] = srcPositionAttribute.count;\n    if (hasIndex) {\n      const indexStart = range.indexStart;\n      for (let i = 0; i < srcIndex.count; i++) {\n        dstIndex.setX(indexStart + i, vertexStart + srcIndex.getX(i));\n      }\n      for (let i = srcIndex.count, l = range.indexCount; i < l; i++) {\n        dstIndex.setX(indexStart + i, vertexStart);\n      }\n      dstIndex.needsUpdate = true;\n      this._indexCounts[id] = srcIndex.count;\n    }\n    return id;\n  }\n  deleteGeometry(geometryId) {\n    const active = this._active;\n    const matricesTexture = this._matricesTexture;\n    const matricesArray = matricesTexture.image.data;\n    if (geometryId >= active.length || active[geometryId] === false) {\n      return this;\n    }\n    active[geometryId] = false;\n    _zeroScaleMatrix.toArray(matricesArray, geometryId * 16);\n    matricesTexture.needsUpdate = true;\n    return this;\n  }\n  optimize() {\n    throw new Error(\"BatchedMesh: Optimize function not implemented.\");\n  }\n  setMatrixAt(geometryId, matrix) {\n    const visible = this._visible;\n    const active = this._active;\n    const matricesTexture = this._matricesTexture;\n    const matrices = this._matrices;\n    const matricesArray = matricesTexture.image.data;\n    if (geometryId >= matrices.length || active[geometryId] === false) {\n      return this;\n    }\n    if (visible[geometryId] === true) {\n      matrix.toArray(matricesArray, geometryId * 16);\n      matricesTexture.needsUpdate = true;\n    }\n    matrices[geometryId].copy(matrix);\n    return this;\n  }\n  getMatrixAt(geometryId, matrix) {\n    const matrices = this._matrices;\n    const active = this._active;\n    if (geometryId >= matrices.length || active[geometryId] === false) {\n      return matrix;\n    }\n    return matrix.copy(matrices[geometryId]);\n  }\n  setVisibleAt(geometryId, value) {\n    const visible = this._visible;\n    const active = this._active;\n    const matricesTexture = this._matricesTexture;\n    const matrices = this._matrices;\n    const matricesArray = matricesTexture.image.data;\n    if (geometryId >= visible.length || active[geometryId] === false || visible[geometryId] === value) {\n      return this;\n    }\n    if (value === true) {\n      matrices[geometryId].toArray(matricesArray, geometryId * 16);\n    } else {\n      _zeroScaleMatrix.toArray(matricesArray, geometryId * 16);\n    }\n    matricesTexture.needsUpdate = true;\n    visible[geometryId] = value;\n    return this;\n  }\n  getVisibleAt(geometryId) {\n    const visible = this._visible;\n    const active = this._active;\n    if (geometryId >= visible.length || active[geometryId] === false) {\n      return false;\n    }\n    return visible[geometryId];\n  }\n  raycast() {\n    console.warn(\"BatchedMesh: Raycast function not implemented.\");\n  }\n  copy() {\n    throw new Error(\"BatchedMesh: Copy function not implemented.\");\n  }\n  toJSON() {\n    throw new Error(\"BatchedMesh: toJSON function not implemented.\");\n  }\n  dispose() {\n    this.geometry.dispose();\n    this._matricesTexture.dispose();\n    this._matricesTexture = null;\n    return this;\n  }\n}\nexport { BatchedMesh };", "map": {"version": 3, "names": ["ID_ATTR_NAME", "_identityMatrix", "Matrix4", "_zeroScaleMatrix", "set", "batchingParsVertex", "batchingbaseVertex", "batchingnormalVertex", "batchingVertex", "copyAttributeData", "src", "target", "targetOffset", "itemSize", "isInterleavedBufferAttribute", "array", "constructor", "vertexCount", "count", "i", "c", "setComponent", "getComponent", "needsUpdate", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "maxGeometryCount", "maxVertexCount", "maxIndexCount", "material", "BufferGeometry", "__publicField", "_vertexStarts", "_vertexCounts", "_indexStarts", "_indexCounts", "_reservedRanges", "_visible", "_active", "_maxGeometryCount", "_maxVertexCount", "_maxIndexCount", "_geometryInitialized", "_geometryCount", "_matrices", "_matricesTexture", "frustumCulled", "_customUniforms", "batchingTexture", "value", "_initMatricesTexture", "_init<PERSON><PERSON>er", "onBeforeRender", "defines", "BATCHING", "onAfterRender", "size", "Math", "sqrt", "MathUtils", "ceilPowerOfTwo", "max", "matricesArray", "Float32Array", "matricesTexture", "DataTexture", "RGBAFormat", "FloatType", "currentOnBeforeCompile", "onBeforeCompile", "customUniforms", "parameters", "renderer", "vertexShader", "replace", "uniformName", "uniforms", "call", "_initializeGeometry", "reference", "geometry", "attributeName", "attributes", "srcAttribute", "getAttribute", "normalized", "dstArray", "dstAttribute", "setUsage", "usage", "setAttribute", "getIndex", "indexArray", "Uint32Array", "Uint16Array", "setIndex", "BufferAttribute", "idArray", "_validateGeometry", "Error", "batchGeometry", "Boolean", "hasAttribute", "getGeometryCount", "getVertexCount", "reservedRanges", "length", "final<PERSON><PERSON><PERSON>", "vertexStart", "getIndexCount", "indexStart", "indexCount", "addGeometry", "range", "<PERSON><PERSON><PERSON><PERSON>", "indexCounts", "indexStarts", "vertexCounts", "vertexStarts", "visible", "active", "matrices", "image", "data", "push", "geometryId", "toArray", "idAttribute", "setX", "setGeometryAt", "applyGeometry", "id", "position", "srcPositionAttribute", "hasIndex", "dstIndex", "srcIndex", "l", "index", "getX", "deleteGeometry", "optimize", "setMatrixAt", "matrix", "copy", "getMatrixAt", "setVisibleAt", "getVisibleAt", "raycast", "console", "warn", "toJSON", "dispose"], "sources": ["C:\\Users\\<USER>\\Documents\\Apps_Creations\\SiahMan_Racing4Speed_WebGame\\siah-man-racing\\node_modules\\src\\objects\\BatchedMesh.ts"], "sourcesContent": ["import {\n  Matrix4,\n  <PERSON><PERSON>er<PERSON><PERSON>ri<PERSON>e,\n  InterleavedBufferAttribute,\n  Mesh,\n  BufferGeometry,\n  Material,\n  DataTexture,\n  IUniform,\n  MathUtils,\n  RGBAFormat,\n  FloatType,\n} from 'three'\n\nconst ID_ATTR_NAME = '_batch_id_'\nconst _identityMatrix = /* @__PURE__ */ new Matrix4()\nconst _zeroScaleMatrix = /* @__PURE__ */ (() => new Matrix4().set(0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1))()\n\n// Custom shaders\nconst batchingParsVertex = /* glsl */ `\n#ifdef BATCHING\n\tattribute float ${ID_ATTR_NAME};\n\tuniform highp sampler2D batchingTexture;\n\tmat4 getBatchingMatrix( const in float i ) {\n\n\t\tint size = textureSize( batchingTexture, 0 ).x;\n\t\tint j = int( i ) * 4;\n\t\tint x = j % size;\n\t\tint y = j / size;\n\t\tvec4 v1 = texelFetch( batchingTexture, ivec2( x, y ), 0 );\n\t\tvec4 v2 = texelFetch( batchingTexture, ivec2( x + 1, y ), 0 );\n\t\tvec4 v3 = texelFetch( batchingTexture, ivec2( x + 2, y ), 0 );\n\t\tvec4 v4 = texelFetch( batchingTexture, ivec2( x + 3, y ), 0 );\n\t\treturn mat4( v1, v2, v3, v4 );\n\n\t}\n#endif\n`\n\nconst batchingbaseVertex = /* glsl */ `\n#ifdef BATCHING\n\tmat4 batchingMatrix = getBatchingMatrix( ${ID_ATTR_NAME} );\n#endif\n`\n\nconst batchingnormalVertex = /* glsl */ `\n#ifdef BATCHING\n\tobjectNormal = vec4( batchingMatrix * vec4( objectNormal, 0.0 ) ).xyz;\n\t#ifdef USE_TANGENT\n\t\tobjectTangent = vec4( batchingMatrix * vec4( objectTangent, 0.0 ) ).xyz;\n\t#endif\n#endif\n`\n\nconst batchingVertex = /* glsl */ `\n#ifdef BATCHING\n\ttransformed = ( batchingMatrix * vec4( transformed, 1.0 ) ).xyz;\n#endif\n`\n\n// @TODO: SkinnedMesh support?\n// @TODO: Future work if needed. Move into the core. Can be optimized more with WEBGL_multi_draw.\n\n// copies data from attribute \"src\" into \"target\" starting at \"targetOffset\"\nfunction copyAttributeData(\n  src: BufferAttribute | InterleavedBufferAttribute,\n  target: BufferAttribute | InterleavedBufferAttribute,\n  targetOffset = 0,\n): void {\n  const itemSize = target.itemSize\n  if (\n    (src as InterleavedBufferAttribute).isInterleavedBufferAttribute ||\n    src.array.constructor !== target.array.constructor\n  ) {\n    // use the component getters and setters if the array data cannot\n    // be copied directly\n    const vertexCount = src.count\n    for (let i = 0; i < vertexCount; i++) {\n      for (let c = 0; c < itemSize; c++) {\n        // @ts-ignore\n        target.setComponent(i + targetOffset, c, src.getComponent(i, c))\n      }\n    }\n  } else {\n    // faster copy approach using typed array set function\n    // @ts-ignore\n    target.array.set(src.array, targetOffset * itemSize)\n  }\n\n  target.needsUpdate = true\n}\n\nclass BatchedMesh extends Mesh<BufferGeometry, Material> {\n  _vertexStarts: number[]\n  _vertexCounts: number[]\n  _indexStarts: number[]\n  _indexCounts: number[]\n  _reservedRanges: { vertexStart: number; vertexCount: number; indexStart: number; indexCount: number }[]\n  _visible: boolean[]\n  _active: boolean[]\n  _maxGeometryCount: number\n  _maxVertexCount: number\n  _maxIndexCount: number\n  _geometryInitialized: boolean\n  _geometryCount: number\n  _matrices: Matrix4[]\n  _matricesTexture: DataTexture | null\n  _customUniforms: Record<string, IUniform>\n\n  constructor(\n    maxGeometryCount: number,\n    maxVertexCount: number,\n    maxIndexCount = maxVertexCount * 2,\n    material?: Material,\n  ) {\n    super(new BufferGeometry(), material)\n\n    this._vertexStarts = []\n    this._vertexCounts = []\n    this._indexStarts = []\n    this._indexCounts = []\n    this._reservedRanges = []\n\n    this._visible = []\n    this._active = []\n\n    this._maxGeometryCount = maxGeometryCount\n    this._maxVertexCount = maxVertexCount\n    this._maxIndexCount = maxIndexCount\n\n    this._geometryInitialized = false\n    this._geometryCount = 0\n\n    // Local matrix per geometry by using data texture\n    // @TODO: Support uniform parameter per geometry\n\n    this._matrices = []\n    this._matricesTexture = null!\n\n    // @TODO: Calculate the entire binding box and make frustumCulled true\n    this.frustumCulled = false\n\n    this._customUniforms = {\n      batchingTexture: { value: null },\n    }\n\n    this._initMatricesTexture()\n    this._initShader()\n\n    this.onBeforeRender = function () {\n      if (this.material.defines) {\n        this.material.defines.BATCHING = true\n      }\n\n      // @TODO: Implement frustum culling for each geometry\n    }\n\n    this.onAfterRender = function () {\n      if (this.material.defines) {\n        this.material.defines.BATCHING = false\n      }\n    }\n  }\n\n  _initMatricesTexture(): void {\n    // layout (1 matrix = 4 pixels)\n    //      RGBA RGBA RGBA RGBA (=> column1, column2, column3, column4)\n    //  with  8x8  pixel texture max   16 matrices * 4 pixels =  (8 * 8)\n    //       16x16 pixel texture max   64 matrices * 4 pixels = (16 * 16)\n    //       32x32 pixel texture max  256 matrices * 4 pixels = (32 * 32)\n    //       64x64 pixel texture max 1024 matrices * 4 pixels = (64 * 64)\n\n    let size = Math.sqrt(this._maxGeometryCount * 4) // 4 pixels needed for 1 matrix\n    size = MathUtils.ceilPowerOfTwo(size)\n    size = Math.max(size, 4)\n\n    const matricesArray = new Float32Array(size * size * 4) // 4 floats per RGBA pixel\n    const matricesTexture = new DataTexture(matricesArray, size, size, RGBAFormat, FloatType)\n\n    this._matricesTexture = matricesTexture\n    this._customUniforms.batchingTexture.value = this._matricesTexture\n  }\n\n  _initShader(): void {\n    const material = this.material\n    const currentOnBeforeCompile = material.onBeforeCompile\n    const customUniforms = this._customUniforms\n\n    material.onBeforeCompile = function onBeforeCompile(parameters, renderer) {\n      // Is this replacement stable across any materials?\n      parameters.vertexShader = parameters.vertexShader\n        .replace('#include <skinning_pars_vertex>', '#include <skinning_pars_vertex>\\n' + batchingParsVertex)\n        .replace('#include <uv_vertex>', '#include <uv_vertex>\\n' + batchingbaseVertex)\n        .replace('#include <skinnormal_vertex>', '#include <skinnormal_vertex>\\n' + batchingnormalVertex)\n        .replace('#include <skinning_vertex>', '#include <skinning_vertex>\\n' + batchingVertex)\n\n      for (const uniformName in customUniforms) {\n        parameters.uniforms[uniformName] = customUniforms[uniformName]\n      }\n\n      currentOnBeforeCompile.call(this, parameters, renderer)\n    }\n\n    material.defines = material.defines || {}\n    material.defines.BATCHING = false\n  }\n\n  _initializeGeometry(reference: BufferGeometry): void {\n    // @TODO: geometry.groups support?\n    // @TODO: geometry.drawRange support?\n    // @TODO: geometry.morphAttributes support?\n\n    const geometry = this.geometry\n    const maxVertexCount = this._maxVertexCount\n    const maxGeometryCount = this._maxGeometryCount\n    const maxIndexCount = this._maxIndexCount\n    if (this._geometryInitialized === false) {\n      for (const attributeName in reference.attributes) {\n        const srcAttribute = reference.getAttribute(attributeName)\n        const { array, itemSize, normalized } = srcAttribute\n\n        const dstArray = new (array.constructor as Float32ArrayConstructor)(maxVertexCount * itemSize)\n        const dstAttribute = new (srcAttribute.constructor as any)(dstArray, itemSize, normalized)\n\n        // TODO: add usage in @types/three\n        // @ts-ignore\n        dstAttribute.setUsage(srcAttribute.usage)\n\n        geometry.setAttribute(attributeName, dstAttribute)\n      }\n\n      if (reference.getIndex() !== null) {\n        const indexArray = maxVertexCount > 65536 ? new Uint32Array(maxIndexCount) : new Uint16Array(maxIndexCount)\n\n        geometry.setIndex(new BufferAttribute(indexArray, 1))\n      }\n\n      const idArray = maxGeometryCount > 65536 ? new Uint32Array(maxVertexCount) : new Uint16Array(maxVertexCount)\n      geometry.setAttribute(ID_ATTR_NAME, new BufferAttribute(idArray, 1))\n\n      this._geometryInitialized = true\n    }\n  }\n\n  // Make sure the geometry is compatible with the existing combined geometry atributes\n  _validateGeometry(geometry: BufferGeometry): void {\n    // check that the geometry doesn't have a version of our reserved id attribute\n    if (geometry.getAttribute(ID_ATTR_NAME)) {\n      throw new Error(`BatchedMesh: Geometry cannot use attribute \"${ID_ATTR_NAME}\"`)\n    }\n\n    // check to ensure the geometries are using consistent attributes and indices\n    const batchGeometry = this.geometry\n    if (Boolean(geometry.getIndex()) !== Boolean(batchGeometry.getIndex())) {\n      throw new Error('BatchedMesh: All geometries must consistently have \"index\".')\n    }\n\n    for (const attributeName in batchGeometry.attributes) {\n      if (attributeName === ID_ATTR_NAME) {\n        continue\n      }\n\n      if (!geometry.hasAttribute(attributeName)) {\n        throw new Error(\n          `BatchedMesh: Added geometry missing \"${attributeName}\". All geometries must have consistent attributes.`,\n        )\n      }\n\n      const srcAttribute = geometry.getAttribute(attributeName)\n      const dstAttribute = batchGeometry.getAttribute(attributeName)\n      if (srcAttribute.itemSize !== dstAttribute.itemSize || srcAttribute.normalized !== dstAttribute.normalized) {\n        throw new Error('BatchedMesh: All attributes must have a consistent itemSize and normalized value.')\n      }\n    }\n  }\n\n  getGeometryCount(): number {\n    return this._geometryCount\n  }\n\n  getVertexCount(): number {\n    const reservedRanges = this._reservedRanges\n    if (reservedRanges.length === 0) {\n      return 0\n    } else {\n      const finalRange = reservedRanges[reservedRanges.length - 1]\n      return finalRange.vertexStart + finalRange.vertexCount\n    }\n  }\n\n  getIndexCount(): number {\n    const reservedRanges = this._reservedRanges\n    const geometry = this.geometry\n    if (geometry.getIndex() === null || reservedRanges.length === 0) {\n      return 0\n    } else {\n      const finalRange = reservedRanges[reservedRanges.length - 1]\n      return finalRange.indexStart + finalRange.indexCount\n    }\n  }\n\n  addGeometry(geometry: BufferGeometry, vertexCount = -1, indexCount = -1): number {\n    this._initializeGeometry(geometry)\n\n    this._validateGeometry(geometry)\n\n    // ensure we're not over geometry\n    if (this._geometryCount >= this._maxGeometryCount) {\n      throw new Error('BatchedMesh: Maximum geometry count reached.')\n    }\n\n    // get the necessary range fo the geometry\n    const range = {\n      vertexStart: -1,\n      vertexCount: -1,\n      indexStart: -1,\n      indexCount: -1,\n    }\n\n    let lastRange = null\n    const reservedRanges = this._reservedRanges\n    if (this._geometryCount !== 0) {\n      lastRange = reservedRanges[reservedRanges.length - 1]\n    }\n\n    if (vertexCount === -1) {\n      range.vertexCount = geometry.getAttribute('position').count\n    } else {\n      range.vertexCount = vertexCount\n    }\n\n    if (lastRange === null) {\n      range.vertexStart = 0\n    } else {\n      range.vertexStart = lastRange.vertexStart + lastRange.vertexCount\n    }\n\n    if (geometry.getIndex() !== null) {\n      if (indexCount === -1) {\n        range.indexCount = geometry.getIndex()!.count\n      } else {\n        range.indexCount = indexCount\n      }\n\n      if (lastRange === null) {\n        range.indexStart = 0\n      } else {\n        range.indexStart = lastRange.indexStart + lastRange.indexCount\n      }\n    }\n\n    if (\n      (range.indexStart !== -1 && range.indexStart + range.indexCount > this._maxIndexCount) ||\n      range.vertexStart + range.vertexCount > this._maxVertexCount\n    ) {\n      throw new Error('BatchedMesh: Reserved space request exceeds the maximum buffer size.')\n    }\n\n    const indexCounts = this._indexCounts\n    const indexStarts = this._indexStarts\n    const vertexCounts = this._vertexCounts\n    const vertexStarts = this._vertexStarts\n\n    const visible = this._visible\n    const active = this._active\n    const matricesTexture = this._matricesTexture\n    const matrices = this._matrices\n    const matricesArray = this._matricesTexture!.image.data\n\n    // push new visibility states\n    visible.push(true)\n    active.push(true)\n\n    // update id\n    const geometryId = this._geometryCount\n    this._geometryCount++\n\n    // initialize matrix information\n    matrices.push(new Matrix4())\n    _identityMatrix.toArray(matricesArray, geometryId * 16)\n    matricesTexture!.needsUpdate = true\n\n    // add the reserved range\n    reservedRanges.push(range)\n\n    // push new geometry data range\n    vertexStarts.push(range.vertexStart)\n    vertexCounts.push(range.vertexCount)\n\n    if (geometry.getIndex() !== null) {\n      // push new index range\n      indexStarts.push(range.indexCount)\n      indexCounts.push(range.indexCount)\n    }\n\n    // set the id for the geometry\n    const idAttribute = this.geometry.getAttribute(ID_ATTR_NAME)\n    for (let i = 0; i < range.vertexCount; i++) {\n      idAttribute.setX(range.vertexStart + i, geometryId)\n    }\n\n    idAttribute.needsUpdate = true\n\n    // update the geometry\n    this.setGeometryAt(geometryId, geometry)\n\n    return geometryId\n  }\n\n  /**\n   * @deprecated use `addGeometry` instead.\n   */\n  applyGeometry(geometry: BufferGeometry): number {\n    return this.addGeometry(geometry)\n  }\n\n  setGeometryAt(id: number, geometry: BufferGeometry): number {\n    if (id >= this._geometryCount) {\n      throw new Error('BatchedMesh: Maximum geometry count reached.')\n    }\n\n    this._validateGeometry(geometry)\n\n    const range = this._reservedRanges[id]\n    if (\n      (geometry.getIndex() !== null && geometry.getIndex()!.count > range.indexCount) ||\n      geometry.attributes.position.count > range.vertexCount\n    ) {\n      throw new Error('BatchedMesh: Reserved space not large enough for provided geometry.')\n    }\n\n    // copy geometry over\n    const batchGeometry = this.geometry\n    const srcPositionAttribute = geometry.getAttribute('position')\n    const hasIndex = batchGeometry.getIndex() !== null\n    const dstIndex = batchGeometry.getIndex()!\n    const srcIndex = geometry.getIndex()!\n\n    // copy attribute data over\n    const vertexStart = range.vertexStart\n    const vertexCount = range.vertexCount\n    for (const attributeName in batchGeometry.attributes) {\n      if (attributeName === ID_ATTR_NAME) {\n        continue\n      }\n\n      const srcAttribute = geometry.getAttribute(attributeName)\n      const dstAttribute = batchGeometry.getAttribute(attributeName)\n      copyAttributeData(srcAttribute, dstAttribute, vertexStart)\n\n      // fill the rest in with zeroes\n      const itemSize = srcAttribute.itemSize\n      for (let i = srcAttribute.count, l = vertexCount; i < l; i++) {\n        const index = vertexStart + i\n        for (let c = 0; c < itemSize; c++) {\n          // @ts-ignore\n          dstAttribute.setComponent(index, c, 0)\n        }\n      }\n\n      dstAttribute.needsUpdate = true\n    }\n\n    this._vertexCounts[id] = srcPositionAttribute.count\n\n    if (hasIndex) {\n      // fill the rest in with zeroes\n      const indexStart = range.indexStart\n\n      // copy index data over\n      for (let i = 0; i < srcIndex.count; i++) {\n        dstIndex.setX(indexStart + i, vertexStart + srcIndex.getX(i))\n      }\n\n      // fill the rest in with zeroes\n      for (let i = srcIndex.count, l = range.indexCount; i < l; i++) {\n        dstIndex.setX(indexStart + i, vertexStart)\n      }\n\n      dstIndex.needsUpdate = true\n      this._indexCounts[id] = srcIndex.count\n    }\n\n    return id\n  }\n\n  deleteGeometry(geometryId: number): this {\n    // Note: User needs to call optimize() afterward to pack the data.\n\n    const active = this._active\n    const matricesTexture = this._matricesTexture!\n    const matricesArray = matricesTexture.image.data\n    if (geometryId >= active.length || active[geometryId] === false) {\n      return this\n    }\n\n    active[geometryId] = false\n    _zeroScaleMatrix.toArray(matricesArray, geometryId * 16)\n    matricesTexture!.needsUpdate = true\n\n    return this\n  }\n\n  optimize(): never {\n    throw new Error('BatchedMesh: Optimize function not implemented.')\n  }\n\n  setMatrixAt(geometryId: number, matrix: Matrix4): this {\n    // @TODO: Map geometryId to index of the arrays because\n    //        optimize() can make geometryId mismatch the index\n\n    const visible = this._visible\n    const active = this._active\n    const matricesTexture = this._matricesTexture!\n    const matrices = this._matrices\n    const matricesArray = matricesTexture.image.data\n    if (geometryId >= matrices.length || active[geometryId] === false) {\n      return this\n    }\n\n    if (visible[geometryId] === true) {\n      matrix.toArray(matricesArray, geometryId * 16)\n      matricesTexture.needsUpdate = true\n    }\n\n    matrices[geometryId].copy(matrix)\n\n    return this\n  }\n\n  getMatrixAt(geometryId: number, matrix: Matrix4): Matrix4 {\n    const matrices = this._matrices\n    const active = this._active\n    if (geometryId >= matrices.length || active[geometryId] === false) {\n      return matrix\n    }\n\n    return matrix.copy(matrices[geometryId])\n  }\n\n  setVisibleAt(geometryId: number, value: boolean): this {\n    const visible = this._visible\n    const active = this._active\n    const matricesTexture = this._matricesTexture!\n    const matrices = this._matrices\n    const matricesArray = matricesTexture.image.data\n\n    // if the geometry is out of range, not active, or visibility state\n    // does not change then return early\n    if (geometryId >= visible.length || active[geometryId] === false || visible[geometryId] === value) {\n      return this\n    }\n\n    // scale the matrix to zero if it's hidden\n    if (value === true) {\n      matrices[geometryId].toArray(matricesArray, geometryId * 16)\n    } else {\n      _zeroScaleMatrix.toArray(matricesArray, geometryId * 16)\n    }\n\n    matricesTexture.needsUpdate = true\n    visible[geometryId] = value\n\n    return this\n  }\n\n  getVisibleAt(geometryId: number): boolean {\n    const visible = this._visible\n    const active = this._active\n\n    // return early if the geometry is out of range or not active\n    if (geometryId >= visible.length || active[geometryId] === false) {\n      return false\n    }\n\n    return visible[geometryId]\n  }\n\n  raycast(): void {\n    console.warn('BatchedMesh: Raycast function not implemented.')\n  }\n\n  copy(): never {\n    // super.copy( source );\n\n    throw new Error('BatchedMesh: Copy function not implemented.')\n  }\n\n  toJSON(): never {\n    throw new Error('BatchedMesh: toJSON function not implemented.')\n  }\n\n  dispose(): this {\n    // Assuming the geometry is not shared with other meshes\n    this.geometry.dispose()\n\n    this._matricesTexture!.dispose()\n    this._matricesTexture = null!\n\n    return this\n  }\n}\n\nexport { BatchedMesh }\n"], "mappings": ";;;;;;;;;;;;AAcA,MAAMA,YAAA,GAAe;AACrB,MAAMC,eAAA,sBAAsCC,OAAA;AAC5C,MAAMC,gBAAA,yBAA0C,IAAID,OAAA,GAAUE,GAAA,CAAI,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,CAAC;AAGhH,MAAMC,kBAAA;AAAgC;AAAA;AAAA,mBAEnBL,YAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAkBnB,MAAMM,kBAAA;AAAgC;AAAA;AAAA,4CAEMN,YAAA;AAAA;AAAA;AAI5C,MAAMO,oBAAA;AAAkC;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AASxC,MAAMC,cAAA;AAA4B;AAAA;AAAA;AAAA;AAAA;AAUlC,SAASC,kBACPC,GAAA,EACAC,MAAA,EACAC,YAAA,GAAe,GACT;EACN,MAAMC,QAAA,GAAWF,MAAA,CAAOE,QAAA;EACxB,IACGH,GAAA,CAAmCI,4BAAA,IACpCJ,GAAA,CAAIK,KAAA,CAAMC,WAAA,KAAgBL,MAAA,CAAOI,KAAA,CAAMC,WAAA,EACvC;IAGA,MAAMC,WAAA,GAAcP,GAAA,CAAIQ,KAAA;IACxB,SAASC,CAAA,GAAI,GAAGA,CAAA,GAAIF,WAAA,EAAaE,CAAA,IAAK;MACpC,SAASC,CAAA,GAAI,GAAGA,CAAA,GAAIP,QAAA,EAAUO,CAAA,IAAK;QAE1BT,MAAA,CAAAU,YAAA,CAAaF,CAAA,GAAIP,YAAA,EAAcQ,CAAA,EAAGV,GAAA,CAAIY,YAAA,CAAaH,CAAA,EAAGC,CAAC,CAAC;MACjE;IACF;EAAA,OACK;IAGLT,MAAA,CAAOI,KAAA,CAAMX,GAAA,CAAIM,GAAA,CAAIK,KAAA,EAAOH,YAAA,GAAeC,QAAQ;EACrD;EAEAF,MAAA,CAAOY,WAAA,GAAc;AACvB;AAEA,MAAMC,WAAA,SAAoBC,IAAA,CAA+B;EAiBvDT,YACEU,gBAAA,EACAC,cAAA,EACAC,aAAA,GAAgBD,cAAA,GAAiB,GACjCE,QAAA,EACA;IACM,UAAIC,cAAA,IAAkBD,QAAQ;IAtBtCE,aAAA;IACAA,aAAA;IACAA,aAAA;IACAA,aAAA;IACAA,aAAA;IACAA,aAAA;IACAA,aAAA;IACAA,aAAA;IACAA,aAAA;IACAA,aAAA;IACAA,aAAA;IACAA,aAAA;IACAA,aAAA;IACAA,aAAA;IACAA,aAAA;IAUE,KAAKC,aAAA,GAAgB;IACrB,KAAKC,aAAA,GAAgB;IACrB,KAAKC,YAAA,GAAe;IACpB,KAAKC,YAAA,GAAe;IACpB,KAAKC,eAAA,GAAkB;IAEvB,KAAKC,QAAA,GAAW;IAChB,KAAKC,OAAA,GAAU;IAEf,KAAKC,iBAAA,GAAoBb,gBAAA;IACzB,KAAKc,eAAA,GAAkBb,cAAA;IACvB,KAAKc,cAAA,GAAiBb,aAAA;IAEtB,KAAKc,oBAAA,GAAuB;IAC5B,KAAKC,cAAA,GAAiB;IAKtB,KAAKC,SAAA,GAAY;IACjB,KAAKC,gBAAA,GAAmB;IAGxB,KAAKC,aAAA,GAAgB;IAErB,KAAKC,eAAA,GAAkB;MACrBC,eAAA,EAAiB;QAAEC,KAAA,EAAO;MAAK;IAAA;IAGjC,KAAKC,oBAAA,CAAqB;IAC1B,KAAKC,WAAA,CAAY;IAEjB,KAAKC,cAAA,GAAiB,YAAY;MAC5B,SAAKvB,QAAA,CAASwB,OAAA,EAAS;QACpB,KAAAxB,QAAA,CAASwB,OAAA,CAAQC,QAAA,GAAW;MACnC;IAAA;IAKF,KAAKC,aAAA,GAAgB,YAAY;MAC3B,SAAK1B,QAAA,CAASwB,OAAA,EAAS;QACpB,KAAAxB,QAAA,CAASwB,OAAA,CAAQC,QAAA,GAAW;MACnC;IAAA;EAEJ;EAEAJ,qBAAA,EAA6B;IAQ3B,IAAIM,IAAA,GAAOC,IAAA,CAAKC,IAAA,CAAK,KAAKnB,iBAAA,GAAoB,CAAC;IACxCiB,IAAA,GAAAG,SAAA,CAAUC,cAAA,CAAeJ,IAAI;IAC7BA,IAAA,GAAAC,IAAA,CAAKI,GAAA,CAAIL,IAAA,EAAM,CAAC;IAEvB,MAAMM,aAAA,GAAgB,IAAIC,YAAA,CAAaP,IAAA,GAAOA,IAAA,GAAO,CAAC;IACtD,MAAMQ,eAAA,GAAkB,IAAIC,WAAA,CAAYH,aAAA,EAAeN,IAAA,EAAMA,IAAA,EAAMU,UAAA,EAAYC,SAAS;IAExF,KAAKtB,gBAAA,GAAmBmB,eAAA;IACnB,KAAAjB,eAAA,CAAgBC,eAAA,CAAgBC,KAAA,GAAQ,KAAKJ,gBAAA;EACpD;EAEAM,YAAA,EAAoB;IAClB,MAAMtB,QAAA,GAAW,KAAKA,QAAA;IACtB,MAAMuC,sBAAA,GAAyBvC,QAAA,CAASwC,eAAA;IACxC,MAAMC,cAAA,GAAiB,KAAKvB,eAAA;IAE5BlB,QAAA,CAASwC,eAAA,GAAkB,SAASA,gBAAgBE,UAAA,EAAYC,QAAA,EAAU;MAE7DD,UAAA,CAAAE,YAAA,GAAeF,UAAA,CAAWE,YAAA,CAClCC,OAAA,CAAQ,mCAAmC,sCAAsCrE,kBAAkB,EACnGqE,OAAA,CAAQ,wBAAwB,2BAA2BpE,kBAAkB,EAC7EoE,OAAA,CAAQ,gCAAgC,mCAAmCnE,oBAAoB,EAC/FmE,OAAA,CAAQ,8BAA8B,iCAAiClE,cAAc;MAExF,WAAWmE,WAAA,IAAeL,cAAA,EAAgB;QACxCC,UAAA,CAAWK,QAAA,CAASD,WAAW,IAAIL,cAAA,CAAeK,WAAW;MAC/D;MAEuBP,sBAAA,CAAAS,IAAA,CAAK,MAAMN,UAAA,EAAYC,QAAQ;IAAA;IAG/C3C,QAAA,CAAAwB,OAAA,GAAUxB,QAAA,CAASwB,OAAA,IAAW;IACvCxB,QAAA,CAASwB,OAAA,CAAQC,QAAA,GAAW;EAC9B;EAEAwB,oBAAoBC,SAAA,EAAiC;IAKnD,MAAMC,QAAA,GAAW,KAAKA,QAAA;IACtB,MAAMrD,cAAA,GAAiB,KAAKa,eAAA;IAC5B,MAAMd,gBAAA,GAAmB,KAAKa,iBAAA;IAC9B,MAAMX,aAAA,GAAgB,KAAKa,cAAA;IACvB,SAAKC,oBAAA,KAAyB,OAAO;MAC5B,WAAAuC,aAAA,IAAiBF,SAAA,CAAUG,UAAA,EAAY;QAC1C,MAAAC,YAAA,GAAeJ,SAAA,CAAUK,YAAA,CAAaH,aAAa;QACzD,MAAM;UAAElE,KAAA;UAAOF,QAAA;UAAUwE;QAAA,IAAeF,YAAA;QAExC,MAAMG,QAAA,GAAW,IAAKvE,KAAA,CAAMC,WAAA,CAAwCW,cAAA,GAAiBd,QAAQ;QAC7F,MAAM0E,YAAA,GAAe,IAAKJ,YAAA,CAAanE,WAAA,CAAoBsE,QAAA,EAAUzE,QAAA,EAAUwE,UAAU;QAI5EE,YAAA,CAAAC,QAAA,CAASL,YAAA,CAAaM,KAAK;QAE/BT,QAAA,CAAAU,YAAA,CAAaT,aAAA,EAAeM,YAAY;MACnD;MAEI,IAAAR,SAAA,CAAUY,QAAA,CAAS,MAAM,MAAM;QAC3B,MAAAC,UAAA,GAAajE,cAAA,GAAiB,QAAQ,IAAIkE,WAAA,CAAYjE,aAAa,IAAI,IAAIkE,WAAA,CAAYlE,aAAa;QAE1GoD,QAAA,CAASe,QAAA,CAAS,IAAIC,eAAA,CAAgBJ,UAAA,EAAY,CAAC,CAAC;MACtD;MAEM,MAAAK,OAAA,GAAUvE,gBAAA,GAAmB,QAAQ,IAAImE,WAAA,CAAYlE,cAAc,IAAI,IAAImE,WAAA,CAAYnE,cAAc;MAC3GqD,QAAA,CAASU,YAAA,CAAa1F,YAAA,EAAc,IAAIgG,eAAA,CAAgBC,OAAA,EAAS,CAAC,CAAC;MAEnE,KAAKvD,oBAAA,GAAuB;IAC9B;EACF;EAAA;EAGAwD,kBAAkBlB,QAAA,EAAgC;IAE5C,IAAAA,QAAA,CAASI,YAAA,CAAapF,YAAY,GAAG;MACjC,UAAImG,KAAA,CAAM,+CAA+CnG,YAAA,GAAe;IAChF;IAGA,MAAMoG,aAAA,GAAgB,KAAKpB,QAAA;IACvB,IAAAqB,OAAA,CAAQrB,QAAA,CAASW,QAAA,CAAU,OAAMU,OAAA,CAAQD,aAAA,CAAcT,QAAA,CAAS,CAAC,GAAG;MAChE,UAAIQ,KAAA,CAAM,6DAA6D;IAC/E;IAEW,WAAAlB,aAAA,IAAiBmB,aAAA,CAAclB,UAAA,EAAY;MACpD,IAAID,aAAA,KAAkBjF,YAAA,EAAc;QAClC;MACF;MAEA,IAAI,CAACgF,QAAA,CAASsB,YAAA,CAAarB,aAAa,GAAG;QACzC,MAAM,IAAIkB,KAAA,CACR,wCAAwClB,aAAA;MAE5C;MAEM,MAAAE,YAAA,GAAeH,QAAA,CAASI,YAAA,CAAaH,aAAa;MAClD,MAAAM,YAAA,GAAea,aAAA,CAAchB,YAAA,CAAaH,aAAa;MAC7D,IAAIE,YAAA,CAAatE,QAAA,KAAa0E,YAAA,CAAa1E,QAAA,IAAYsE,YAAA,CAAaE,UAAA,KAAeE,YAAA,CAAaF,UAAA,EAAY;QACpG,UAAIc,KAAA,CAAM,mFAAmF;MACrG;IACF;EACF;EAEAI,iBAAA,EAA2B;IACzB,OAAO,KAAK5D,cAAA;EACd;EAEA6D,eAAA,EAAyB;IACvB,MAAMC,cAAA,GAAiB,KAAKrE,eAAA;IACxB,IAAAqE,cAAA,CAAeC,MAAA,KAAW,GAAG;MACxB;IAAA,OACF;MACL,MAAMC,UAAA,GAAaF,cAAA,CAAeA,cAAA,CAAeC,MAAA,GAAS,CAAC;MACpD,OAAAC,UAAA,CAAWC,WAAA,GAAcD,UAAA,CAAW1F,WAAA;IAC7C;EACF;EAEA4F,cAAA,EAAwB;IACtB,MAAMJ,cAAA,GAAiB,KAAKrE,eAAA;IAC5B,MAAM4C,QAAA,GAAW,KAAKA,QAAA;IACtB,IAAIA,QAAA,CAASW,QAAA,CAAS,MAAM,QAAQc,cAAA,CAAeC,MAAA,KAAW,GAAG;MACxD;IAAA,OACF;MACL,MAAMC,UAAA,GAAaF,cAAA,CAAeA,cAAA,CAAeC,MAAA,GAAS,CAAC;MACpD,OAAAC,UAAA,CAAWG,UAAA,GAAaH,UAAA,CAAWI,UAAA;IAC5C;EACF;EAEAC,YAAYhC,QAAA,EAA0B/D,WAAA,GAAc,IAAI8F,UAAA,GAAa,IAAY;IAC/E,KAAKjC,mBAAA,CAAoBE,QAAQ;IAEjC,KAAKkB,iBAAA,CAAkBlB,QAAQ;IAG3B,SAAKrC,cAAA,IAAkB,KAAKJ,iBAAA,EAAmB;MAC3C,UAAI4D,KAAA,CAAM,8CAA8C;IAChE;IAGA,MAAMc,KAAA,GAAQ;MACZL,WAAA,EAAa;MACb3F,WAAA,EAAa;MACb6F,UAAA,EAAY;MACZC,UAAA,EAAY;IAAA;IAGd,IAAIG,SAAA,GAAY;IAChB,MAAMT,cAAA,GAAiB,KAAKrE,eAAA;IACxB,SAAKO,cAAA,KAAmB,GAAG;MACjBuE,SAAA,GAAAT,cAAA,CAAeA,cAAA,CAAeC,MAAA,GAAS,CAAC;IACtD;IAEA,IAAIzF,WAAA,KAAgB,IAAI;MACtBgG,KAAA,CAAMhG,WAAA,GAAc+D,QAAA,CAASI,YAAA,CAAa,UAAU,EAAElE,KAAA;IAAA,OACjD;MACL+F,KAAA,CAAMhG,WAAA,GAAcA,WAAA;IACtB;IAEA,IAAIiG,SAAA,KAAc,MAAM;MACtBD,KAAA,CAAML,WAAA,GAAc;IAAA,OACf;MACCK,KAAA,CAAAL,WAAA,GAAcM,SAAA,CAAUN,WAAA,GAAcM,SAAA,CAAUjG,WAAA;IACxD;IAEI,IAAA+D,QAAA,CAASW,QAAA,CAAS,MAAM,MAAM;MAChC,IAAIoB,UAAA,KAAe,IAAI;QACfE,KAAA,CAAAF,UAAA,GAAa/B,QAAA,CAASW,QAAA,GAAYzE,KAAA;MAAA,OACnC;QACL+F,KAAA,CAAMF,UAAA,GAAaA,UAAA;MACrB;MAEA,IAAIG,SAAA,KAAc,MAAM;QACtBD,KAAA,CAAMH,UAAA,GAAa;MAAA,OACd;QACCG,KAAA,CAAAH,UAAA,GAAaI,SAAA,CAAUJ,UAAA,GAAaI,SAAA,CAAUH,UAAA;MACtD;IACF;IAEA,IACGE,KAAA,CAAMH,UAAA,KAAe,MAAMG,KAAA,CAAMH,UAAA,GAAaG,KAAA,CAAMF,UAAA,GAAa,KAAKtE,cAAA,IACvEwE,KAAA,CAAML,WAAA,GAAcK,KAAA,CAAMhG,WAAA,GAAc,KAAKuB,eAAA,EAC7C;MACM,UAAI2D,KAAA,CAAM,sEAAsE;IACxF;IAEA,MAAMgB,WAAA,GAAc,KAAKhF,YAAA;IACzB,MAAMiF,WAAA,GAAc,KAAKlF,YAAA;IACzB,MAAMmF,YAAA,GAAe,KAAKpF,aAAA;IAC1B,MAAMqF,YAAA,GAAe,KAAKtF,aAAA;IAE1B,MAAMuF,OAAA,GAAU,KAAKlF,QAAA;IACrB,MAAMmF,MAAA,GAAS,KAAKlF,OAAA;IACpB,MAAM0B,eAAA,GAAkB,KAAKnB,gBAAA;IAC7B,MAAM4E,QAAA,GAAW,KAAK7E,SAAA;IAChB,MAAAkB,aAAA,GAAgB,KAAKjB,gBAAA,CAAkB6E,KAAA,CAAMC,IAAA;IAGnDJ,OAAA,CAAQK,IAAA,CAAK,IAAI;IACjBJ,MAAA,CAAOI,IAAA,CAAK,IAAI;IAGhB,MAAMC,UAAA,GAAa,KAAKlF,cAAA;IACnB,KAAAA,cAAA;IAGI8E,QAAA,CAAAG,IAAA,CAAK,IAAI1H,OAAA,EAAS;IACXD,eAAA,CAAA6H,OAAA,CAAQhE,aAAA,EAAe+D,UAAA,GAAa,EAAE;IACtD7D,eAAA,CAAiBzC,WAAA,GAAc;IAG/BkF,cAAA,CAAemB,IAAA,CAAKX,KAAK;IAGZK,YAAA,CAAAM,IAAA,CAAKX,KAAA,CAAML,WAAW;IACtBS,YAAA,CAAAO,IAAA,CAAKX,KAAA,CAAMhG,WAAW;IAE/B,IAAA+D,QAAA,CAASW,QAAA,CAAS,MAAM,MAAM;MAEpByB,WAAA,CAAAQ,IAAA,CAAKX,KAAA,CAAMF,UAAU;MACrBI,WAAA,CAAAS,IAAA,CAAKX,KAAA,CAAMF,UAAU;IACnC;IAGA,MAAMgB,WAAA,GAAc,KAAK/C,QAAA,CAASI,YAAA,CAAapF,YAAY;IAC3D,SAASmB,CAAA,GAAI,GAAGA,CAAA,GAAI8F,KAAA,CAAMhG,WAAA,EAAaE,CAAA,IAAK;MAC1C4G,WAAA,CAAYC,IAAA,CAAKf,KAAA,CAAML,WAAA,GAAczF,CAAA,EAAG0G,UAAU;IACpD;IAEAE,WAAA,CAAYxG,WAAA,GAAc;IAGrB,KAAA0G,aAAA,CAAcJ,UAAA,EAAY7C,QAAQ;IAEhC,OAAA6C,UAAA;EACT;EAAA;AAAA;AAAA;EAKAK,cAAclD,QAAA,EAAkC;IACvC,YAAKgC,WAAA,CAAYhC,QAAQ;EAClC;EAEAiD,cAAcE,EAAA,EAAYnD,QAAA,EAAkC;IACtD,IAAAmD,EAAA,IAAM,KAAKxF,cAAA,EAAgB;MACvB,UAAIwD,KAAA,CAAM,8CAA8C;IAChE;IAEA,KAAKD,iBAAA,CAAkBlB,QAAQ;IAEzB,MAAAiC,KAAA,GAAQ,KAAK7E,eAAA,CAAgB+F,EAAE;IACrC,IACGnD,QAAA,CAASW,QAAA,CAAe,cAAQX,QAAA,CAASW,QAAA,CAAS,EAAGzE,KAAA,GAAQ+F,KAAA,CAAMF,UAAA,IACpE/B,QAAA,CAASE,UAAA,CAAWkD,QAAA,CAASlH,KAAA,GAAQ+F,KAAA,CAAMhG,WAAA,EAC3C;MACM,UAAIkF,KAAA,CAAM,qEAAqE;IACvF;IAGA,MAAMC,aAAA,GAAgB,KAAKpB,QAAA;IACrB,MAAAqD,oBAAA,GAAuBrD,QAAA,CAASI,YAAA,CAAa,UAAU;IACvD,MAAAkD,QAAA,GAAWlC,aAAA,CAAcT,QAAA,OAAe;IACxC,MAAA4C,QAAA,GAAWnC,aAAA,CAAcT,QAAA;IACzB,MAAA6C,QAAA,GAAWxD,QAAA,CAASW,QAAA;IAG1B,MAAMiB,WAAA,GAAcK,KAAA,CAAML,WAAA;IAC1B,MAAM3F,WAAA,GAAcgG,KAAA,CAAMhG,WAAA;IACf,WAAAgE,aAAA,IAAiBmB,aAAA,CAAclB,UAAA,EAAY;MACpD,IAAID,aAAA,KAAkBjF,YAAA,EAAc;QAClC;MACF;MAEM,MAAAmF,YAAA,GAAeH,QAAA,CAASI,YAAA,CAAaH,aAAa;MAClD,MAAAM,YAAA,GAAea,aAAA,CAAchB,YAAA,CAAaH,aAAa;MAC3CxE,iBAAA,CAAA0E,YAAA,EAAcI,YAAA,EAAcqB,WAAW;MAGzD,MAAM/F,QAAA,GAAWsE,YAAA,CAAatE,QAAA;MAC9B,SAASM,CAAA,GAAIgE,YAAA,CAAajE,KAAA,EAAOuH,CAAA,GAAIxH,WAAA,EAAaE,CAAA,GAAIsH,CAAA,EAAGtH,CAAA,IAAK;QAC5D,MAAMuH,KAAA,GAAQ9B,WAAA,GAAczF,CAAA;QAC5B,SAASC,CAAA,GAAI,GAAGA,CAAA,GAAIP,QAAA,EAAUO,CAAA,IAAK;UAEpBmE,YAAA,CAAAlE,YAAA,CAAaqH,KAAA,EAAOtH,CAAA,EAAG,CAAC;QACvC;MACF;MAEAmE,YAAA,CAAahE,WAAA,GAAc;IAC7B;IAEK,KAAAU,aAAA,CAAckG,EAAE,IAAIE,oBAAA,CAAqBnH,KAAA;IAE9C,IAAIoH,QAAA,EAAU;MAEZ,MAAMxB,UAAA,GAAaG,KAAA,CAAMH,UAAA;MAGzB,SAAS3F,CAAA,GAAI,GAAGA,CAAA,GAAIqH,QAAA,CAAStH,KAAA,EAAOC,CAAA,IAAK;QACvCoH,QAAA,CAASP,IAAA,CAAKlB,UAAA,GAAa3F,CAAA,EAAGyF,WAAA,GAAc4B,QAAA,CAASG,IAAA,CAAKxH,CAAC,CAAC;MAC9D;MAGS,SAAAA,CAAA,GAAIqH,QAAA,CAAStH,KAAA,EAAOuH,CAAA,GAAIxB,KAAA,CAAMF,UAAA,EAAY5F,CAAA,GAAIsH,CAAA,EAAGtH,CAAA,IAAK;QACpDoH,QAAA,CAAAP,IAAA,CAAKlB,UAAA,GAAa3F,CAAA,EAAGyF,WAAW;MAC3C;MAEA2B,QAAA,CAAShH,WAAA,GAAc;MAClB,KAAAY,YAAA,CAAagG,EAAE,IAAIK,QAAA,CAAStH,KAAA;IACnC;IAEO,OAAAiH,EAAA;EACT;EAEAS,eAAef,UAAA,EAA0B;IAGvC,MAAML,MAAA,GAAS,KAAKlF,OAAA;IACpB,MAAM0B,eAAA,GAAkB,KAAKnB,gBAAA;IACvB,MAAAiB,aAAA,GAAgBE,eAAA,CAAgB0D,KAAA,CAAMC,IAAA;IAC5C,IAAIE,UAAA,IAAcL,MAAA,CAAOd,MAAA,IAAUc,MAAA,CAAOK,UAAU,MAAM,OAAO;MACxD;IACT;IAEAL,MAAA,CAAOK,UAAU,IAAI;IACJ1H,gBAAA,CAAA2H,OAAA,CAAQhE,aAAA,EAAe+D,UAAA,GAAa,EAAE;IACvD7D,eAAA,CAAiBzC,WAAA,GAAc;IAExB;EACT;EAEAsH,SAAA,EAAkB;IACV,UAAI1C,KAAA,CAAM,iDAAiD;EACnE;EAEA2C,YAAYjB,UAAA,EAAoBkB,MAAA,EAAuB;IAIrD,MAAMxB,OAAA,GAAU,KAAKlF,QAAA;IACrB,MAAMmF,MAAA,GAAS,KAAKlF,OAAA;IACpB,MAAM0B,eAAA,GAAkB,KAAKnB,gBAAA;IAC7B,MAAM4E,QAAA,GAAW,KAAK7E,SAAA;IAChB,MAAAkB,aAAA,GAAgBE,eAAA,CAAgB0D,KAAA,CAAMC,IAAA;IAC5C,IAAIE,UAAA,IAAcJ,QAAA,CAASf,MAAA,IAAUc,MAAA,CAAOK,UAAU,MAAM,OAAO;MAC1D;IACT;IAEI,IAAAN,OAAA,CAAQM,UAAU,MAAM,MAAM;MACzBkB,MAAA,CAAAjB,OAAA,CAAQhE,aAAA,EAAe+D,UAAA,GAAa,EAAE;MAC7C7D,eAAA,CAAgBzC,WAAA,GAAc;IAChC;IAESkG,QAAA,CAAAI,UAAU,EAAEmB,IAAA,CAAKD,MAAM;IAEzB;EACT;EAEAE,YAAYpB,UAAA,EAAoBkB,MAAA,EAA0B;IACxD,MAAMtB,QAAA,GAAW,KAAK7E,SAAA;IACtB,MAAM4E,MAAA,GAAS,KAAKlF,OAAA;IACpB,IAAIuF,UAAA,IAAcJ,QAAA,CAASf,MAAA,IAAUc,MAAA,CAAOK,UAAU,MAAM,OAAO;MAC1D,OAAAkB,MAAA;IACT;IAEA,OAAOA,MAAA,CAAOC,IAAA,CAAKvB,QAAA,CAASI,UAAU,CAAC;EACzC;EAEAqB,aAAarB,UAAA,EAAoB5E,KAAA,EAAsB;IACrD,MAAMsE,OAAA,GAAU,KAAKlF,QAAA;IACrB,MAAMmF,MAAA,GAAS,KAAKlF,OAAA;IACpB,MAAM0B,eAAA,GAAkB,KAAKnB,gBAAA;IAC7B,MAAM4E,QAAA,GAAW,KAAK7E,SAAA;IAChB,MAAAkB,aAAA,GAAgBE,eAAA,CAAgB0D,KAAA,CAAMC,IAAA;IAIxC,IAAAE,UAAA,IAAcN,OAAA,CAAQb,MAAA,IAAUc,MAAA,CAAOK,UAAU,MAAM,SAASN,OAAA,CAAQM,UAAU,MAAM5E,KAAA,EAAO;MAC1F;IACT;IAGA,IAAIA,KAAA,KAAU,MAAM;MAClBwE,QAAA,CAASI,UAAU,EAAEC,OAAA,CAAQhE,aAAA,EAAe+D,UAAA,GAAa,EAAE;IAAA,OACtD;MACY1H,gBAAA,CAAA2H,OAAA,CAAQhE,aAAA,EAAe+D,UAAA,GAAa,EAAE;IACzD;IAEA7D,eAAA,CAAgBzC,WAAA,GAAc;IAC9BgG,OAAA,CAAQM,UAAU,IAAI5E,KAAA;IAEf;EACT;EAEAkG,aAAatB,UAAA,EAA6B;IACxC,MAAMN,OAAA,GAAU,KAAKlF,QAAA;IACrB,MAAMmF,MAAA,GAAS,KAAKlF,OAAA;IAGpB,IAAIuF,UAAA,IAAcN,OAAA,CAAQb,MAAA,IAAUc,MAAA,CAAOK,UAAU,MAAM,OAAO;MACzD;IACT;IAEA,OAAON,OAAA,CAAQM,UAAU;EAC3B;EAEAuB,QAAA,EAAgB;IACdC,OAAA,CAAQC,IAAA,CAAK,gDAAgD;EAC/D;EAEAN,KAAA,EAAc;IAGN,UAAI7C,KAAA,CAAM,6CAA6C;EAC/D;EAEAoD,OAAA,EAAgB;IACR,UAAIpD,KAAA,CAAM,+CAA+C;EACjE;EAEAqD,QAAA,EAAgB;IAEd,KAAKxE,QAAA,CAASwE,OAAA;IAEd,KAAK3G,gBAAA,CAAkB2G,OAAA;IACvB,KAAK3G,gBAAA,GAAmB;IAEjB;EACT;AACF", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}