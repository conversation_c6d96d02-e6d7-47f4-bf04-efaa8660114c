{"ast": null, "code": "import _extends from '@babel/runtime/helpers/esm/extends';\nimport * as React from 'react';\nimport { forwardRef, useEffect } from 'react';\nimport { suspend, clear } from 'suspend-react';\nimport { VideoTexture } from '../core/VideoTexture.js';\n\n/**\n * Create a video texture from [`getUserMedia`](https://developer.mozilla.org/en-US/docs/Web/API/MediaDevices/getUserMedia)\n */\nconst WebcamVideoTexture = /* @__PURE__ */forwardRef(({\n  constraints = {\n    audio: false,\n    video: {\n      facingMode: 'user'\n    }\n  },\n  ...props\n}, fref) => {\n  const mediaStream = suspend(() => navigator.mediaDevices.getUserMedia(constraints), []);\n  useEffect(() => {\n    return () => {\n      mediaStream == null || mediaStream.getTracks().forEach(track => track.stop());\n      clear([]);\n    };\n  }, [mediaStream]);\n  return /*#__PURE__*/React.createElement(VideoTexture, _extends({\n    ref: fref\n  }, props, {\n    src: mediaStream\n  }));\n});\nexport { WebcamVideoTexture };", "map": {"version": 3, "names": ["_extends", "React", "forwardRef", "useEffect", "suspend", "clear", "VideoTexture", "WebcamVideoTexture", "constraints", "audio", "video", "facingMode", "props", "fref", "mediaStream", "navigator", "mediaDevices", "getUserMedia", "getTracks", "for<PERSON>ach", "track", "stop", "createElement", "ref", "src"], "sources": ["C:/Users/<USER>/Documents/Apps_Creations/SiahMan_Racing4Speed_WebGame/siah-man-racing/node_modules/@react-three/drei/web/WebcamVideoTexture.js"], "sourcesContent": ["import _extends from '@babel/runtime/helpers/esm/extends';\nimport * as React from 'react';\nimport { forwardRef, useEffect } from 'react';\nimport { suspend, clear } from 'suspend-react';\nimport { VideoTexture } from '../core/VideoTexture.js';\n\n/**\n * Create a video texture from [`getUserMedia`](https://developer.mozilla.org/en-US/docs/Web/API/MediaDevices/getUserMedia)\n */\nconst WebcamVideoTexture = /* @__PURE__ */forwardRef(({\n  constraints = {\n    audio: false,\n    video: {\n      facingMode: 'user'\n    }\n  },\n  ...props\n}, fref) => {\n  const mediaStream = suspend(() => navigator.mediaDevices.getUserMedia(constraints), []);\n  useEffect(() => {\n    return () => {\n      mediaStream == null || mediaStream.getTracks().forEach(track => track.stop());\n      clear([]);\n    };\n  }, [mediaStream]);\n  return /*#__PURE__*/React.createElement(VideoTexture, _extends({\n    ref: fref\n  }, props, {\n    src: mediaStream\n  }));\n});\n\nexport { WebcamVideoTexture };\n"], "mappings": "AAAA,OAAOA,QAAQ,MAAM,oCAAoC;AACzD,OAAO,KAAKC,KAAK,MAAM,OAAO;AAC9B,SAASC,UAAU,EAAEC,SAAS,QAAQ,OAAO;AAC7C,SAASC,OAAO,EAAEC,KAAK,QAAQ,eAAe;AAC9C,SAASC,YAAY,QAAQ,yBAAyB;;AAEtD;AACA;AACA;AACA,MAAMC,kBAAkB,GAAG,eAAeL,UAAU,CAAC,CAAC;EACpDM,WAAW,GAAG;IACZC,KAAK,EAAE,KAAK;IACZC,KAAK,EAAE;MACLC,UAAU,EAAE;IACd;EACF,CAAC;EACD,GAAGC;AACL,CAAC,EAAEC,IAAI,KAAK;EACV,MAAMC,WAAW,GAAGV,OAAO,CAAC,MAAMW,SAAS,CAACC,YAAY,CAACC,YAAY,CAACT,WAAW,CAAC,EAAE,EAAE,CAAC;EACvFL,SAAS,CAAC,MAAM;IACd,OAAO,MAAM;MACXW,WAAW,IAAI,IAAI,IAAIA,WAAW,CAACI,SAAS,CAAC,CAAC,CAACC,OAAO,CAACC,KAAK,IAAIA,KAAK,CAACC,IAAI,CAAC,CAAC,CAAC;MAC7EhB,KAAK,CAAC,EAAE,CAAC;IACX,CAAC;EACH,CAAC,EAAE,CAACS,WAAW,CAAC,CAAC;EACjB,OAAO,aAAab,KAAK,CAACqB,aAAa,CAAChB,YAAY,EAAEN,QAAQ,CAAC;IAC7DuB,GAAG,EAAEV;EACP,CAAC,EAAED,KAAK,EAAE;IACRY,GAAG,EAAEV;EACP,CAAC,CAAC,CAAC;AACL,CAAC,CAAC;AAEF,SAASP,kBAAkB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}