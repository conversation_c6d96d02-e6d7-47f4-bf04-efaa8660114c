{"ast": null, "code": "var __defProp = Object.defineProperty;\nvar __defNormalProp = (obj, key, value) => key in obj ? __defProp(obj, key, {\n  enumerable: true,\n  configurable: true,\n  writable: true,\n  value\n}) : obj[key] = value;\nvar __publicField = (obj, key, value) => {\n  __defNormalProp(obj, typeof key !== \"symbol\" ? key + \"\" : key, value);\n  return value;\n};\nimport { Vector3, Spherical, MathUtils } from \"three\";\nimport { EventDispatcher } from \"./EventDispatcher.js\";\nconst targetPosition = /* @__PURE__ */new Vector3();\nclass FirstPersonControls extends EventDispatcher {\n  constructor(object, domElement) {\n    super();\n    __publicField(this, \"object\");\n    __publicField(this, \"domElement\");\n    __publicField(this, \"enabled\", true);\n    __publicField(this, \"movementSpeed\", 1);\n    __publicField(this, \"lookSpeed\", 5e-3);\n    __publicField(this, \"lookVertical\", true);\n    __publicField(this, \"autoForward\", false);\n    __publicField(this, \"activeLook\", true);\n    __publicField(this, \"heightSpeed\", false);\n    __publicField(this, \"heightCoef\", 1);\n    __publicField(this, \"heightMin\", 0);\n    __publicField(this, \"heightMax\", 1);\n    __publicField(this, \"constrainVertical\", false);\n    __publicField(this, \"verticalMin\", 0);\n    __publicField(this, \"verticalMax\", Math.PI);\n    __publicField(this, \"mouseDragOn\", false);\n    // internals\n    __publicField(this, \"autoSpeedFactor\", 0);\n    __publicField(this, \"mouseX\", 0);\n    __publicField(this, \"mouseY\", 0);\n    __publicField(this, \"moveForward\", false);\n    __publicField(this, \"moveBackward\", false);\n    __publicField(this, \"moveLeft\", false);\n    __publicField(this, \"moveRight\", false);\n    __publicField(this, \"moveUp\", false);\n    __publicField(this, \"moveDown\", false);\n    __publicField(this, \"viewHalfX\", 0);\n    __publicField(this, \"viewHalfY\", 0);\n    __publicField(this, \"lat\", 0);\n    __publicField(this, \"lon\", 0);\n    __publicField(this, \"lookDirection\", new Vector3());\n    __publicField(this, \"spherical\", new Spherical());\n    __publicField(this, \"target\", new Vector3());\n    __publicField(this, \"connect\", domElement => {\n      domElement.setAttribute(\"tabindex\", \"-1\");\n      domElement.style.touchAction = \"none\";\n      domElement.addEventListener(\"contextmenu\", this.contextmenu);\n      domElement.addEventListener(\"mousemove\", this.onMouseMove);\n      domElement.addEventListener(\"mousedown\", this.onMouseDown);\n      domElement.addEventListener(\"mouseup\", this.onMouseUp);\n      this.domElement = domElement;\n      window.addEventListener(\"keydown\", this.onKeyDown);\n      window.addEventListener(\"keyup\", this.onKeyUp);\n      this.handleResize();\n    });\n    __publicField(this, \"dispose\", () => {\n      var _a, _b, _c, _d;\n      (_a = this.domElement) == null ? void 0 : _a.removeEventListener(\"contextmenu\", this.contextmenu);\n      (_b = this.domElement) == null ? void 0 : _b.removeEventListener(\"mousedown\", this.onMouseDown);\n      (_c = this.domElement) == null ? void 0 : _c.removeEventListener(\"mousemove\", this.onMouseMove);\n      (_d = this.domElement) == null ? void 0 : _d.removeEventListener(\"mouseup\", this.onMouseUp);\n      window.removeEventListener(\"keydown\", this.onKeyDown);\n      window.removeEventListener(\"keyup\", this.onKeyUp);\n    });\n    __publicField(this, \"handleResize\", () => {\n      if (this.domElement) {\n        this.viewHalfX = this.domElement.offsetWidth / 2;\n        this.viewHalfY = this.domElement.offsetHeight / 2;\n      }\n    });\n    __publicField(this, \"onMouseDown\", event => {\n      var _a;\n      (_a = this.domElement) == null ? void 0 : _a.focus();\n      if (this.activeLook) {\n        switch (event.button) {\n          case 0:\n            this.moveForward = true;\n            break;\n          case 2:\n            this.moveBackward = true;\n            break;\n        }\n      }\n      this.mouseDragOn = true;\n    });\n    __publicField(this, \"onMouseUp\", event => {\n      if (this.activeLook) {\n        switch (event.button) {\n          case 0:\n            this.moveForward = false;\n            break;\n          case 2:\n            this.moveBackward = false;\n            break;\n        }\n      }\n      this.mouseDragOn = false;\n    });\n    __publicField(this, \"onMouseMove\", event => {\n      if (this.domElement) {\n        this.mouseX = event.pageX - this.domElement.offsetLeft - this.viewHalfX;\n        this.mouseY = event.pageY - this.domElement.offsetTop - this.viewHalfY;\n      }\n    });\n    __publicField(this, \"onKeyDown\", event => {\n      switch (event.code) {\n        case \"ArrowUp\":\n        case \"KeyW\":\n          this.moveForward = true;\n          break;\n        case \"ArrowLeft\":\n        case \"KeyA\":\n          this.moveLeft = true;\n          break;\n        case \"ArrowDown\":\n        case \"KeyS\":\n          this.moveBackward = true;\n          break;\n        case \"ArrowRight\":\n        case \"KeyD\":\n          this.moveRight = true;\n          break;\n        case \"KeyR\":\n          this.moveUp = true;\n          break;\n        case \"KeyF\":\n          this.moveDown = true;\n          break;\n      }\n    });\n    __publicField(this, \"onKeyUp\", event => {\n      switch (event.code) {\n        case \"ArrowUp\":\n        case \"KeyW\":\n          this.moveForward = false;\n          break;\n        case \"ArrowLeft\":\n        case \"KeyA\":\n          this.moveLeft = false;\n          break;\n        case \"ArrowDown\":\n        case \"KeyS\":\n          this.moveBackward = false;\n          break;\n        case \"ArrowRight\":\n        case \"KeyD\":\n          this.moveRight = false;\n          break;\n        case \"KeyR\":\n          this.moveUp = false;\n          break;\n        case \"KeyF\":\n          this.moveDown = false;\n          break;\n      }\n    });\n    __publicField(this, \"lookAt\", (x, y, z) => {\n      if (x instanceof Vector3) {\n        this.target.copy(x);\n      } else if (y && z) {\n        this.target.set(x, y, z);\n      }\n      this.object.lookAt(this.target);\n      this.setOrientation();\n      return this;\n    });\n    __publicField(this, \"update\", delta => {\n      if (!this.enabled) return;\n      if (this.heightSpeed) {\n        const y = MathUtils.clamp(this.object.position.y, this.heightMin, this.heightMax);\n        const heightDelta = y - this.heightMin;\n        this.autoSpeedFactor = delta * (heightDelta * this.heightCoef);\n      } else {\n        this.autoSpeedFactor = 0;\n      }\n      const actualMoveSpeed = delta * this.movementSpeed;\n      if (this.moveForward || this.autoForward && !this.moveBackward) {\n        this.object.translateZ(-(actualMoveSpeed + this.autoSpeedFactor));\n      }\n      if (this.moveBackward) this.object.translateZ(actualMoveSpeed);\n      if (this.moveLeft) this.object.translateX(-actualMoveSpeed);\n      if (this.moveRight) this.object.translateX(actualMoveSpeed);\n      if (this.moveUp) this.object.translateY(actualMoveSpeed);\n      if (this.moveDown) this.object.translateY(-actualMoveSpeed);\n      let actualLookSpeed = delta * this.lookSpeed;\n      if (!this.activeLook) {\n        actualLookSpeed = 0;\n      }\n      let verticalLookRatio = 1;\n      if (this.constrainVertical) {\n        verticalLookRatio = Math.PI / (this.verticalMax - this.verticalMin);\n      }\n      this.lon -= this.mouseX * actualLookSpeed;\n      if (this.lookVertical) this.lat -= this.mouseY * actualLookSpeed * verticalLookRatio;\n      this.lat = Math.max(-85, Math.min(85, this.lat));\n      let phi = MathUtils.degToRad(90 - this.lat);\n      const theta = MathUtils.degToRad(this.lon);\n      if (this.constrainVertical) {\n        phi = MathUtils.mapLinear(phi, 0, Math.PI, this.verticalMin, this.verticalMax);\n      }\n      const position = this.object.position;\n      targetPosition.setFromSphericalCoords(1, phi, theta).add(position);\n      this.object.lookAt(targetPosition);\n    });\n    __publicField(this, \"contextmenu\", event => event.preventDefault());\n    __publicField(this, \"setOrientation\", () => {\n      this.lookDirection.set(0, 0, -1).applyQuaternion(this.object.quaternion);\n      this.spherical.setFromVector3(this.lookDirection);\n      this.lat = 90 - MathUtils.radToDeg(this.spherical.phi);\n      this.lon = MathUtils.radToDeg(this.spherical.theta);\n    });\n    this.object = object;\n    this.domElement = domElement;\n    this.setOrientation();\n    if (domElement) this.connect(domElement);\n  }\n}\nexport { FirstPersonControls };", "map": {"version": 3, "names": ["targetPosition", "Vector3", "FirstPersonControls", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "constructor", "object", "dom<PERSON>lement", "__publicField", "Math", "PI", "Spherical", "setAttribute", "style", "touchAction", "addEventListener", "contextmenu", "onMouseMove", "onMouseDown", "onMouseUp", "window", "onKeyDown", "onKeyUp", "handleResize", "_a", "removeEventListener", "_b", "_c", "_d", "viewHalfX", "offsetWidth", "viewHalfY", "offsetHeight", "event", "focus", "activeLook", "button", "moveForward", "moveBackward", "mouseDragOn", "mouseX", "pageX", "offsetLeft", "mouseY", "pageY", "offsetTop", "code", "moveLeft", "moveRight", "moveUp", "moveDown", "x", "y", "z", "target", "copy", "set", "lookAt", "setOrientation", "delta", "enabled", "heightSpeed", "MathUtils", "clamp", "position", "heightMin", "heightMax", "<PERSON><PERSON><PERSON><PERSON>", "autoSpeedFactor", "heightCoef", "actualMoveSpeed", "movementSpeed", "autoForward", "translateZ", "translateX", "translateY", "actualLookSpeed", "lookSpeed", "verticalLookRatio", "constrainVertical", "verticalMax", "verticalMin", "lon", "lookVertical", "lat", "max", "min", "phi", "degToRad", "theta", "mapLinear", "setFromSphericalCoords", "add", "preventDefault", "lookDirection", "applyQuaternion", "quaternion", "spherical", "setFromVector3", "radToDeg", "connect"], "sources": ["C:\\Users\\<USER>\\Documents\\Apps_Creations\\SiahMan_Racing4Speed_WebGame\\siah-man-racing\\node_modules\\src\\controls\\FirstPersonControls.ts"], "sourcesContent": ["import { MathUtils, Spherical, Vector3, Camera } from 'three'\nimport { EventDispatcher } from './EventDispatcher'\nimport { StandardControlsEventMap } from './StandardControlsEventMap'\n\nconst targetPosition = /* @__PURE__ */ new Vector3()\n\nexport class FirstPersonControls extends EventDispatcher<{}> {\n  public object: Camera\n  public domElement?: HTMLElement | null\n\n  public enabled = true\n\n  public movementSpeed = 1.0\n  public lookSpeed = 0.005\n\n  public lookVertical = true\n  public autoForward = false\n\n  public activeLook = true\n\n  public heightSpeed = false\n  public heightCoef = 1.0\n  public heightMin = 0.0\n  public heightMax = 1.0\n\n  public constrainVertical = false\n  public verticalMin = 0\n  public verticalMax = Math.PI\n\n  public mouseDragOn = false\n\n  // internals\n\n  private autoSpeedFactor = 0.0\n\n  private mouseX = 0\n  private mouseY = 0\n\n  private moveForward = false\n  private moveBackward = false\n  private moveLeft = false\n  private moveRight = false\n  private moveUp = false\n  private moveDown = false\n\n  private viewHalfX = 0\n  private viewHalfY = 0\n\n  private lat = 0\n  private lon = 0\n\n  private lookDirection = new Vector3()\n  private spherical = new Spherical()\n  readonly target = new Vector3()\n\n  constructor(object: Camera, domElement?: HTMLElement | null) {\n    super()\n\n    this.object = object\n    this.domElement = domElement\n\n    this.setOrientation()\n\n    if (domElement) this.connect(domElement)\n  }\n\n  public connect = (domElement: HTMLElement): void => {\n    domElement.setAttribute('tabindex', '-1')\n\n    domElement.style.touchAction = 'none'\n\n    domElement.addEventListener('contextmenu', this.contextmenu)\n    domElement.addEventListener('mousemove', this.onMouseMove)\n    domElement.addEventListener('mousedown', this.onMouseDown)\n    domElement.addEventListener('mouseup', this.onMouseUp)\n\n    this.domElement = domElement\n\n    window.addEventListener('keydown', this.onKeyDown)\n    window.addEventListener('keyup', this.onKeyUp)\n\n    this.handleResize()\n  }\n\n  public dispose = (): void => {\n    this.domElement?.removeEventListener('contextmenu', this.contextmenu)\n    this.domElement?.removeEventListener('mousedown', this.onMouseDown)\n    this.domElement?.removeEventListener('mousemove', this.onMouseMove)\n    this.domElement?.removeEventListener('mouseup', this.onMouseUp)\n\n    window.removeEventListener('keydown', this.onKeyDown)\n    window.removeEventListener('keyup', this.onKeyUp)\n  }\n\n  public handleResize = (): void => {\n    if (this.domElement) {\n      this.viewHalfX = this.domElement.offsetWidth / 2\n      this.viewHalfY = this.domElement.offsetHeight / 2\n    }\n  }\n\n  private onMouseDown = (event: MouseEvent): void => {\n    this.domElement?.focus()\n\n    if (this.activeLook) {\n      switch (event.button) {\n        case 0:\n          this.moveForward = true\n          break\n        case 2:\n          this.moveBackward = true\n          break\n      }\n    }\n\n    this.mouseDragOn = true\n  }\n\n  private onMouseUp = (event: MouseEvent): void => {\n    if (this.activeLook) {\n      switch (event.button) {\n        case 0:\n          this.moveForward = false\n          break\n        case 2:\n          this.moveBackward = false\n          break\n      }\n    }\n\n    this.mouseDragOn = false\n  }\n\n  private onMouseMove = (event: MouseEvent): void => {\n    if (this.domElement) {\n      this.mouseX = event.pageX - this.domElement.offsetLeft - this.viewHalfX\n      this.mouseY = event.pageY - this.domElement.offsetTop - this.viewHalfY\n    }\n  }\n\n  private onKeyDown = (event: KeyboardEvent): void => {\n    switch (event.code) {\n      case 'ArrowUp':\n      case 'KeyW':\n        this.moveForward = true\n        break\n\n      case 'ArrowLeft':\n      case 'KeyA':\n        this.moveLeft = true\n        break\n\n      case 'ArrowDown':\n      case 'KeyS':\n        this.moveBackward = true\n        break\n\n      case 'ArrowRight':\n      case 'KeyD':\n        this.moveRight = true\n        break\n\n      case 'KeyR':\n        this.moveUp = true\n        break\n      case 'KeyF':\n        this.moveDown = true\n        break\n    }\n  }\n\n  private onKeyUp = (event: KeyboardEvent): void => {\n    switch (event.code) {\n      case 'ArrowUp':\n      case 'KeyW':\n        this.moveForward = false\n        break\n\n      case 'ArrowLeft':\n      case 'KeyA':\n        this.moveLeft = false\n        break\n\n      case 'ArrowDown':\n      case 'KeyS':\n        this.moveBackward = false\n        break\n\n      case 'ArrowRight':\n      case 'KeyD':\n        this.moveRight = false\n        break\n\n      case 'KeyR':\n        this.moveUp = false\n        break\n      case 'KeyF':\n        this.moveDown = false\n        break\n    }\n  }\n\n  public lookAt = (x: Vector3 | number, y?: number, z?: number): this => {\n    if (x instanceof Vector3) {\n      this.target.copy(x)\n    } else if (y && z) {\n      this.target.set(x, y, z)\n    }\n\n    this.object.lookAt(this.target)\n\n    this.setOrientation()\n\n    return this\n  }\n\n  public update = (delta: number): void => {\n    if (!this.enabled) return\n\n    if (this.heightSpeed) {\n      const y = MathUtils.clamp(this.object.position.y, this.heightMin, this.heightMax)\n      const heightDelta = y - this.heightMin\n\n      this.autoSpeedFactor = delta * (heightDelta * this.heightCoef)\n    } else {\n      this.autoSpeedFactor = 0.0\n    }\n\n    const actualMoveSpeed = delta * this.movementSpeed\n\n    if (this.moveForward || (this.autoForward && !this.moveBackward)) {\n      this.object.translateZ(-(actualMoveSpeed + this.autoSpeedFactor))\n    }\n    if (this.moveBackward) this.object.translateZ(actualMoveSpeed)\n\n    if (this.moveLeft) this.object.translateX(-actualMoveSpeed)\n    if (this.moveRight) this.object.translateX(actualMoveSpeed)\n\n    if (this.moveUp) this.object.translateY(actualMoveSpeed)\n    if (this.moveDown) this.object.translateY(-actualMoveSpeed)\n\n    let actualLookSpeed = delta * this.lookSpeed\n\n    if (!this.activeLook) {\n      actualLookSpeed = 0\n    }\n\n    let verticalLookRatio = 1\n\n    if (this.constrainVertical) {\n      verticalLookRatio = Math.PI / (this.verticalMax - this.verticalMin)\n    }\n\n    this.lon -= this.mouseX * actualLookSpeed\n    if (this.lookVertical) this.lat -= this.mouseY * actualLookSpeed * verticalLookRatio\n\n    this.lat = Math.max(-85, Math.min(85, this.lat))\n\n    let phi = MathUtils.degToRad(90 - this.lat)\n    const theta = MathUtils.degToRad(this.lon)\n\n    if (this.constrainVertical) {\n      phi = MathUtils.mapLinear(phi, 0, Math.PI, this.verticalMin, this.verticalMax)\n    }\n\n    const position = this.object.position\n\n    targetPosition.setFromSphericalCoords(1, phi, theta).add(position)\n\n    this.object.lookAt(targetPosition)\n  }\n\n  private contextmenu = (event: Event): void => event.preventDefault()\n\n  private setOrientation = (): void => {\n    this.lookDirection.set(0, 0, -1).applyQuaternion(this.object.quaternion)\n    this.spherical.setFromVector3(this.lookDirection)\n    this.lat = 90 - MathUtils.radToDeg(this.spherical.phi)\n    this.lon = MathUtils.radToDeg(this.spherical.theta)\n  }\n}\n"], "mappings": ";;;;;;;;;;;;;AAIA,MAAMA,cAAA,sBAAqCC,OAAA;AAEpC,MAAMC,mBAAA,SAA4BC,eAAA,CAAoB;EAiD3DC,YAAYC,MAAA,EAAgBC,UAAA,EAAiC;IACrD;IAjDDC,aAAA;IACAA,aAAA;IAEAA,aAAA,kBAAU;IAEVA,aAAA,wBAAgB;IAChBA,aAAA,oBAAY;IAEZA,aAAA,uBAAe;IACfA,aAAA,sBAAc;IAEdA,aAAA,qBAAa;IAEbA,aAAA,sBAAc;IACdA,aAAA,qBAAa;IACbA,aAAA,oBAAY;IACZA,aAAA,oBAAY;IAEZA,aAAA,4BAAoB;IACpBA,aAAA,sBAAc;IACdA,aAAA,sBAAcC,IAAA,CAAKC,EAAA;IAEnBF,aAAA,sBAAc;IAIb;IAAAA,aAAA,0BAAkB;IAElBA,aAAA,iBAAS;IACTA,aAAA,iBAAS;IAETA,aAAA,sBAAc;IACdA,aAAA,uBAAe;IACfA,aAAA,mBAAW;IACXA,aAAA,oBAAY;IACZA,aAAA,iBAAS;IACTA,aAAA,mBAAW;IAEXA,aAAA,oBAAY;IACZA,aAAA,oBAAY;IAEZA,aAAA,cAAM;IACNA,aAAA,cAAM;IAENA,aAAA,wBAAgB,IAAIN,OAAA;IACpBM,aAAA,oBAAY,IAAIG,SAAA;IACfH,aAAA,iBAAS,IAAIN,OAAA;IAafM,aAAA,kBAAWD,UAAA,IAAkC;MACvCA,UAAA,CAAAK,YAAA,CAAa,YAAY,IAAI;MAExCL,UAAA,CAAWM,KAAA,CAAMC,WAAA,GAAc;MAEpBP,UAAA,CAAAQ,gBAAA,CAAiB,eAAe,KAAKC,WAAW;MAChDT,UAAA,CAAAQ,gBAAA,CAAiB,aAAa,KAAKE,WAAW;MAC9CV,UAAA,CAAAQ,gBAAA,CAAiB,aAAa,KAAKG,WAAW;MAC9CX,UAAA,CAAAQ,gBAAA,CAAiB,WAAW,KAAKI,SAAS;MAErD,KAAKZ,UAAA,GAAaA,UAAA;MAEXa,MAAA,CAAAL,gBAAA,CAAiB,WAAW,KAAKM,SAAS;MAC1CD,MAAA,CAAAL,gBAAA,CAAiB,SAAS,KAAKO,OAAO;MAE7C,KAAKC,YAAA,CAAa;IAAA;IAGbf,aAAA,kBAAU,MAAY;;MAC3B,CAAAgB,EAAA,QAAKjB,UAAA,KAAL,gBAAAiB,EAAA,CAAiBC,mBAAA,CAAoB,eAAe,KAAKT,WAAA;MACzD,CAAAU,EAAA,QAAKnB,UAAA,KAAL,gBAAAmB,EAAA,CAAiBD,mBAAA,CAAoB,aAAa,KAAKP,WAAA;MACvD,CAAAS,EAAA,QAAKpB,UAAA,KAAL,gBAAAoB,EAAA,CAAiBF,mBAAA,CAAoB,aAAa,KAAKR,WAAA;MACvD,CAAAW,EAAA,QAAKrB,UAAA,KAAL,gBAAAqB,EAAA,CAAiBH,mBAAA,CAAoB,WAAW,KAAKN,SAAA;MAE9CC,MAAA,CAAAK,mBAAA,CAAoB,WAAW,KAAKJ,SAAS;MAC7CD,MAAA,CAAAK,mBAAA,CAAoB,SAAS,KAAKH,OAAO;IAAA;IAG3Cd,aAAA,uBAAe,MAAY;MAChC,IAAI,KAAKD,UAAA,EAAY;QACd,KAAAsB,SAAA,GAAY,KAAKtB,UAAA,CAAWuB,WAAA,GAAc;QAC1C,KAAAC,SAAA,GAAY,KAAKxB,UAAA,CAAWyB,YAAA,GAAe;MAClD;IAAA;IAGMxB,aAAA,sBAAeyB,KAAA,IAA4B;;MACjD,CAAAT,EAAA,QAAKjB,UAAA,KAAL,gBAAAiB,EAAA,CAAiBU,KAAA;MAEjB,IAAI,KAAKC,UAAA,EAAY;QACnB,QAAQF,KAAA,CAAMG,MAAA;UACZ,KAAK;YACH,KAAKC,WAAA,GAAc;YACnB;UACF,KAAK;YACH,KAAKC,YAAA,GAAe;YACpB;QACJ;MACF;MAEA,KAAKC,WAAA,GAAc;IAAA;IAGb/B,aAAA,oBAAayB,KAAA,IAA4B;MAC/C,IAAI,KAAKE,UAAA,EAAY;QACnB,QAAQF,KAAA,CAAMG,MAAA;UACZ,KAAK;YACH,KAAKC,WAAA,GAAc;YACnB;UACF,KAAK;YACH,KAAKC,YAAA,GAAe;YACpB;QACJ;MACF;MAEA,KAAKC,WAAA,GAAc;IAAA;IAGb/B,aAAA,sBAAeyB,KAAA,IAA4B;MACjD,IAAI,KAAK1B,UAAA,EAAY;QACnB,KAAKiC,MAAA,GAASP,KAAA,CAAMQ,KAAA,GAAQ,KAAKlC,UAAA,CAAWmC,UAAA,GAAa,KAAKb,SAAA;QAC9D,KAAKc,MAAA,GAASV,KAAA,CAAMW,KAAA,GAAQ,KAAKrC,UAAA,CAAWsC,SAAA,GAAY,KAAKd,SAAA;MAC/D;IAAA;IAGMvB,aAAA,oBAAayB,KAAA,IAA+B;MAClD,QAAQA,KAAA,CAAMa,IAAA;QACZ,KAAK;QACL,KAAK;UACH,KAAKT,WAAA,GAAc;UACnB;QAEF,KAAK;QACL,KAAK;UACH,KAAKU,QAAA,GAAW;UAChB;QAEF,KAAK;QACL,KAAK;UACH,KAAKT,YAAA,GAAe;UACpB;QAEF,KAAK;QACL,KAAK;UACH,KAAKU,SAAA,GAAY;UACjB;QAEF,KAAK;UACH,KAAKC,MAAA,GAAS;UACd;QACF,KAAK;UACH,KAAKC,QAAA,GAAW;UAChB;MACJ;IAAA;IAGM1C,aAAA,kBAAWyB,KAAA,IAA+B;MAChD,QAAQA,KAAA,CAAMa,IAAA;QACZ,KAAK;QACL,KAAK;UACH,KAAKT,WAAA,GAAc;UACnB;QAEF,KAAK;QACL,KAAK;UACH,KAAKU,QAAA,GAAW;UAChB;QAEF,KAAK;QACL,KAAK;UACH,KAAKT,YAAA,GAAe;UACpB;QAEF,KAAK;QACL,KAAK;UACH,KAAKU,SAAA,GAAY;UACjB;QAEF,KAAK;UACH,KAAKC,MAAA,GAAS;UACd;QACF,KAAK;UACH,KAAKC,QAAA,GAAW;UAChB;MACJ;IAAA;IAGK1C,aAAA,iBAAS,CAAC2C,CAAA,EAAqBC,CAAA,EAAYC,CAAA,KAAqB;MACrE,IAAIF,CAAA,YAAajD,OAAA,EAAS;QACnB,KAAAoD,MAAA,CAAOC,IAAA,CAAKJ,CAAC;MAAA,WACTC,CAAA,IAAKC,CAAA,EAAG;QACjB,KAAKC,MAAA,CAAOE,GAAA,CAAIL,CAAA,EAAGC,CAAA,EAAGC,CAAC;MACzB;MAEK,KAAA/C,MAAA,CAAOmD,MAAA,CAAO,KAAKH,MAAM;MAE9B,KAAKI,cAAA,CAAe;MAEb;IAAA;IAGFlD,aAAA,iBAAUmD,KAAA,IAAwB;MACvC,IAAI,CAAC,KAAKC,OAAA,EAAS;MAEnB,IAAI,KAAKC,WAAA,EAAa;QACd,MAAAT,CAAA,GAAIU,SAAA,CAAUC,KAAA,CAAM,KAAKzD,MAAA,CAAO0D,QAAA,CAASZ,CAAA,EAAG,KAAKa,SAAA,EAAW,KAAKC,SAAS;QAC1E,MAAAC,WAAA,GAAcf,CAAA,GAAI,KAAKa,SAAA;QAExB,KAAAG,eAAA,GAAkBT,KAAA,IAASQ,WAAA,GAAc,KAAKE,UAAA;MAAA,OAC9C;QACL,KAAKD,eAAA,GAAkB;MACzB;MAEM,MAAAE,eAAA,GAAkBX,KAAA,GAAQ,KAAKY,aAAA;MAErC,IAAI,KAAKlC,WAAA,IAAgB,KAAKmC,WAAA,IAAe,CAAC,KAAKlC,YAAA,EAAe;QAChE,KAAKhC,MAAA,CAAOmE,UAAA,CAAW,EAAEH,eAAA,GAAkB,KAAKF,eAAA,CAAgB;MAClE;MACA,IAAI,KAAK9B,YAAA,EAAmB,KAAAhC,MAAA,CAAOmE,UAAA,CAAWH,eAAe;MAE7D,IAAI,KAAKvB,QAAA,EAAe,KAAAzC,MAAA,CAAOoE,UAAA,CAAW,CAACJ,eAAe;MAC1D,IAAI,KAAKtB,SAAA,EAAgB,KAAA1C,MAAA,CAAOoE,UAAA,CAAWJ,eAAe;MAE1D,IAAI,KAAKrB,MAAA,EAAa,KAAA3C,MAAA,CAAOqE,UAAA,CAAWL,eAAe;MACvD,IAAI,KAAKpB,QAAA,EAAe,KAAA5C,MAAA,CAAOqE,UAAA,CAAW,CAACL,eAAe;MAEtD,IAAAM,eAAA,GAAkBjB,KAAA,GAAQ,KAAKkB,SAAA;MAE/B,KAAC,KAAK1C,UAAA,EAAY;QACFyC,eAAA;MACpB;MAEA,IAAIE,iBAAA,GAAoB;MAExB,IAAI,KAAKC,iBAAA,EAAmB;QAC1BD,iBAAA,GAAoBrE,IAAA,CAAKC,EAAA,IAAM,KAAKsE,WAAA,GAAc,KAAKC,WAAA;MACzD;MAEK,KAAAC,GAAA,IAAO,KAAK1C,MAAA,GAASoC,eAAA;MAC1B,IAAI,KAAKO,YAAA,EAAmB,KAAAC,GAAA,IAAO,KAAKzC,MAAA,GAASiC,eAAA,GAAkBE,iBAAA;MAE9D,KAAAM,GAAA,GAAM3E,IAAA,CAAK4E,GAAA,CAAI,KAAK5E,IAAA,CAAK6E,GAAA,CAAI,IAAI,KAAKF,GAAG,CAAC;MAE/C,IAAIG,GAAA,GAAMzB,SAAA,CAAU0B,QAAA,CAAS,KAAK,KAAKJ,GAAG;MAC1C,MAAMK,KAAA,GAAQ3B,SAAA,CAAU0B,QAAA,CAAS,KAAKN,GAAG;MAEzC,IAAI,KAAKH,iBAAA,EAAmB;QACpBQ,GAAA,GAAAzB,SAAA,CAAU4B,SAAA,CAAUH,GAAA,EAAK,GAAG9E,IAAA,CAAKC,EAAA,EAAI,KAAKuE,WAAA,EAAa,KAAKD,WAAW;MAC/E;MAEM,MAAAhB,QAAA,GAAW,KAAK1D,MAAA,CAAO0D,QAAA;MAE7B/D,cAAA,CAAe0F,sBAAA,CAAuB,GAAGJ,GAAA,EAAKE,KAAK,EAAEG,GAAA,CAAI5B,QAAQ;MAE5D,KAAA1D,MAAA,CAAOmD,MAAA,CAAOxD,cAAc;IAAA;IAG3BO,aAAA,sBAAeyB,KAAA,IAAuBA,KAAA,CAAM4D,cAAA,CAAe;IAE3DrF,aAAA,yBAAiB,MAAY;MAC9B,KAAAsF,aAAA,CAActC,GAAA,CAAI,GAAG,GAAG,EAAE,EAAEuC,eAAA,CAAgB,KAAKzF,MAAA,CAAO0F,UAAU;MAClE,KAAAC,SAAA,CAAUC,cAAA,CAAe,KAAKJ,aAAa;MAChD,KAAKV,GAAA,GAAM,KAAKtB,SAAA,CAAUqC,QAAA,CAAS,KAAKF,SAAA,CAAUV,GAAG;MACrD,KAAKL,GAAA,GAAMpB,SAAA,CAAUqC,QAAA,CAAS,KAAKF,SAAA,CAAUR,KAAK;IAAA;IA5NlD,KAAKnF,MAAA,GAASA,MAAA;IACd,KAAKC,UAAA,GAAaA,UAAA;IAElB,KAAKmD,cAAA,CAAe;IAEhB,IAAAnD,UAAA,EAAY,KAAK6F,OAAA,CAAQ7F,UAAU;EACzC;AAwNF", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}