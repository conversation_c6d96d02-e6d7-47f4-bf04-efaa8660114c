{"ast": null, "code": "import { WebGLCubeRenderTarget, CubeCamera } from 'three';\nimport * as React from 'react';\nimport { useThree } from '@react-three/fiber';\nfunction Preload({\n  all,\n  scene,\n  camera\n}) {\n  const gl = useThree(({\n    gl\n  }) => gl);\n  const dCamera = useThree(({\n    camera\n  }) => camera);\n  const dScene = useThree(({\n    scene\n  }) => scene);\n\n  // Layout effect because it must run before React commits\n  React.useLayoutEffect(() => {\n    const invisible = [];\n    if (all) {\n      (scene || dScene).traverse(object => {\n        if (object.visible === false) {\n          invisible.push(object);\n          object.visible = true;\n        }\n      });\n    }\n    // Now compile the scene\n    gl.compile(scene || dScene, camera || dCamera);\n    // And for good measure, hit it with a cube camera\n    const cubeRenderTarget = new WebGLCubeRenderTarget(128);\n    const cubeCamera = new CubeCamera(0.01, 100000, cubeRenderTarget);\n    cubeCamera.update(gl, scene || dScene);\n    cubeRenderTarget.dispose();\n    // Flips these objects back\n    invisible.forEach(object => object.visible = false);\n  }, []);\n  return null;\n}\nexport { Preload };", "map": {"version": 3, "names": ["WebGLCubeRenderTarget", "CubeCamera", "React", "useThree", "Preload", "all", "scene", "camera", "gl", "dCamera", "dScene", "useLayoutEffect", "invisible", "traverse", "object", "visible", "push", "compile", "cubeRenderTarget", "cubeCamera", "update", "dispose", "for<PERSON>ach"], "sources": ["C:/Users/<USER>/Documents/Apps_Creations/SiahMan_Racing4Speed_WebGame/siah-man-racing/node_modules/@react-three/drei/core/Preload.js"], "sourcesContent": ["import { WebGLCubeRenderTarget, CubeCamera } from 'three';\nimport * as React from 'react';\nimport { useThree } from '@react-three/fiber';\n\nfunction Preload({\n  all,\n  scene,\n  camera\n}) {\n  const gl = useThree(({\n    gl\n  }) => gl);\n  const dCamera = useThree(({\n    camera\n  }) => camera);\n  const dScene = useThree(({\n    scene\n  }) => scene);\n\n  // Layout effect because it must run before React commits\n  React.useLayoutEffect(() => {\n    const invisible = [];\n    if (all) {\n      (scene || dScene).traverse(object => {\n        if (object.visible === false) {\n          invisible.push(object);\n          object.visible = true;\n        }\n      });\n    }\n    // Now compile the scene\n    gl.compile(scene || dScene, camera || dCamera);\n    // And for good measure, hit it with a cube camera\n    const cubeRenderTarget = new WebGLCubeRenderTarget(128);\n    const cubeCamera = new CubeCamera(0.01, 100000, cubeRenderTarget);\n    cubeCamera.update(gl, scene || dScene);\n    cubeRenderTarget.dispose();\n    // Flips these objects back\n    invisible.forEach(object => object.visible = false);\n  }, []);\n  return null;\n}\n\nexport { Preload };\n"], "mappings": "AAAA,SAASA,qBAAqB,EAAEC,UAAU,QAAQ,OAAO;AACzD,OAAO,KAAKC,KAAK,MAAM,OAAO;AAC9B,SAASC,QAAQ,QAAQ,oBAAoB;AAE7C,SAASC,OAAOA,CAAC;EACfC,GAAG;EACHC,KAAK;EACLC;AACF,CAAC,EAAE;EACD,MAAMC,EAAE,GAAGL,QAAQ,CAAC,CAAC;IACnBK;EACF,CAAC,KAAKA,EAAE,CAAC;EACT,MAAMC,OAAO,GAAGN,QAAQ,CAAC,CAAC;IACxBI;EACF,CAAC,KAAKA,MAAM,CAAC;EACb,MAAMG,MAAM,GAAGP,QAAQ,CAAC,CAAC;IACvBG;EACF,CAAC,KAAKA,KAAK,CAAC;;EAEZ;EACAJ,KAAK,CAACS,eAAe,CAAC,MAAM;IAC1B,MAAMC,SAAS,GAAG,EAAE;IACpB,IAAIP,GAAG,EAAE;MACP,CAACC,KAAK,IAAII,MAAM,EAAEG,QAAQ,CAACC,MAAM,IAAI;QACnC,IAAIA,MAAM,CAACC,OAAO,KAAK,KAAK,EAAE;UAC5BH,SAAS,CAACI,IAAI,CAACF,MAAM,CAAC;UACtBA,MAAM,CAACC,OAAO,GAAG,IAAI;QACvB;MACF,CAAC,CAAC;IACJ;IACA;IACAP,EAAE,CAACS,OAAO,CAACX,KAAK,IAAII,MAAM,EAAEH,MAAM,IAAIE,OAAO,CAAC;IAC9C;IACA,MAAMS,gBAAgB,GAAG,IAAIlB,qBAAqB,CAAC,GAAG,CAAC;IACvD,MAAMmB,UAAU,GAAG,IAAIlB,UAAU,CAAC,IAAI,EAAE,MAAM,EAAEiB,gBAAgB,CAAC;IACjEC,UAAU,CAACC,MAAM,CAACZ,EAAE,EAAEF,KAAK,IAAII,MAAM,CAAC;IACtCQ,gBAAgB,CAACG,OAAO,CAAC,CAAC;IAC1B;IACAT,SAAS,CAACU,OAAO,CAACR,MAAM,IAAIA,MAAM,CAACC,OAAO,GAAG,KAAK,CAAC;EACrD,CAAC,EAAE,EAAE,CAAC;EACN,OAAO,IAAI;AACb;AAEA,SAASX,OAAO", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}