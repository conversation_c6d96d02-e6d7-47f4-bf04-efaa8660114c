{"ast": null, "code": "var __defProp = Object.defineProperty;\nvar __defNormalProp = (obj, key, value) => key in obj ? __defProp(obj, key, {\n  enumerable: true,\n  configurable: true,\n  writable: true,\n  value\n}) : obj[key] = value;\nvar __publicField = (obj, key, value) => {\n  __defNormalProp(obj, typeof key !== \"symbol\" ? key + \"\" : key, value);\n  return value;\n};\nimport { ShaderMaterial, UniformsUtils } from \"three\";\nimport { Pass, FullScreenQuad } from \"./Pass.js\";\nclass ShaderPass extends Pass {\n  constructor(shader, textureID = \"tDiffuse\") {\n    super();\n    __publicField(this, \"textureID\");\n    __publicField(this, \"uniforms\");\n    __publicField(this, \"material\");\n    __publicField(this, \"fsQuad\");\n    this.textureID = textureID;\n    if (shader instanceof ShaderMaterial) {\n      this.uniforms = shader.uniforms;\n      this.material = shader;\n    } else {\n      this.uniforms = UniformsUtils.clone(shader.uniforms);\n      this.material = new ShaderMaterial({\n        defines: Object.assign({}, shader.defines),\n        uniforms: this.uniforms,\n        vertexShader: shader.vertexShader,\n        fragmentShader: shader.fragmentShader\n      });\n    }\n    this.fsQuad = new FullScreenQuad(this.material);\n  }\n  render(renderer, writeBuffer, readBuffer) {\n    if (this.uniforms[this.textureID]) {\n      this.uniforms[this.textureID].value = readBuffer.texture;\n    }\n    this.fsQuad.material = this.material;\n    if (this.renderToScreen) {\n      renderer.setRenderTarget(null);\n      this.fsQuad.render(renderer);\n    } else {\n      renderer.setRenderTarget(writeBuffer);\n      if (this.clear) renderer.clear(renderer.autoClearColor, renderer.autoClearDepth, renderer.autoClearStencil);\n      this.fsQuad.render(renderer);\n    }\n  }\n  dispose() {\n    this.fsQuad.dispose();\n    this.material.dispose();\n  }\n}\nexport { ShaderPass };", "map": {"version": 3, "names": ["<PERSON><PERSON><PERSON><PERSON>", "Pass", "constructor", "shader", "textureID", "__publicField", "ShaderMaterial", "uniforms", "material", "UniformsUtils", "clone", "defines", "Object", "assign", "vertexShader", "fragmentShader", "fsQuad", "FullScreenQuad", "render", "renderer", "writeBuffer", "readBuffer", "value", "texture", "renderToScreen", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "clear", "autoClearColor", "autoClearDepth", "autoClearStencil", "dispose"], "sources": ["C:\\Users\\<USER>\\Documents\\Apps_Creations\\SiahMan_Racing4Speed_WebGame\\siah-man-racing\\node_modules\\src\\postprocessing\\ShaderPass.ts"], "sourcesContent": ["import { ShaderMaterial, UniformsUtils, WebG<PERSON>enderer, WebGLRenderTarget } from 'three'\nimport { Pass, FullScreenQuad } from './Pass'\nimport { Defines, IShader, Uniforms } from '../shaders/types'\n\nclass ShaderPass extends Pass {\n  public textureID: string\n  public uniforms: Uniforms\n  public material: ShaderMaterial\n  public fsQuad: FullScreenQuad\n\n  constructor(shader: ShaderMaterial | IShader<Uniforms, Defines | undefined>, textureID = 'tDiffuse') {\n    super()\n\n    this.textureID = textureID\n\n    if (shader instanceof ShaderMaterial) {\n      this.uniforms = shader.uniforms\n\n      this.material = shader\n    } else {\n      this.uniforms = UniformsUtils.clone(shader.uniforms)\n\n      this.material = new ShaderMaterial({\n        defines: Object.assign({}, shader.defines),\n        uniforms: this.uniforms,\n        vertexShader: shader.vertexShader,\n        fragmentShader: shader.fragmentShader,\n      })\n    }\n\n    this.fsQuad = new FullScreenQuad(this.material)\n  }\n\n  public render(\n    renderer: <PERSON><PERSON><PERSON>enderer,\n    writeBuffer: WebGLRenderTarget,\n    readBuffer: WebGLRenderTarget /*, deltaTime, maskActive */,\n  ): void {\n    if (this.uniforms[this.textureID]) {\n      this.uniforms[this.textureID].value = readBuffer.texture\n    }\n\n    this.fsQuad.material = this.material\n\n    if (this.renderToScreen) {\n      renderer.setRenderTarget(null)\n      this.fsQuad.render(renderer)\n    } else {\n      renderer.setRenderTarget(writeBuffer)\n      // TODO: Avoid using autoClear properties, see https://github.com/mrdoob/three.js/pull/15571#issuecomment-465669600\n      if (this.clear) renderer.clear(renderer.autoClearColor, renderer.autoClearDepth, renderer.autoClearStencil)\n      this.fsQuad.render(renderer)\n    }\n  }\n\n  public dispose() {\n    this.fsQuad.dispose()\n    this.material.dispose()\n  }\n}\n\nexport { ShaderPass }\n"], "mappings": ";;;;;;;;;;;;;AAIA,MAAMA,UAAA,SAAmBC,IAAA,CAAK;EAM5BC,YAAYC,MAAA,EAAiEC,SAAA,GAAY,YAAY;IAC7F;IANDC,aAAA;IACAA,aAAA;IACAA,aAAA;IACAA,aAAA;IAKL,KAAKD,SAAA,GAAYA,SAAA;IAEjB,IAAID,MAAA,YAAkBG,cAAA,EAAgB;MACpC,KAAKC,QAAA,GAAWJ,MAAA,CAAOI,QAAA;MAEvB,KAAKC,QAAA,GAAWL,MAAA;IAAA,OACX;MACL,KAAKI,QAAA,GAAWE,aAAA,CAAcC,KAAA,CAAMP,MAAA,CAAOI,QAAQ;MAE9C,KAAAC,QAAA,GAAW,IAAIF,cAAA,CAAe;QACjCK,OAAA,EAASC,MAAA,CAAOC,MAAA,CAAO,IAAIV,MAAA,CAAOQ,OAAO;QACzCJ,QAAA,EAAU,KAAKA,QAAA;QACfO,YAAA,EAAcX,MAAA,CAAOW,YAAA;QACrBC,cAAA,EAAgBZ,MAAA,CAAOY;MAAA,CACxB;IACH;IAEA,KAAKC,MAAA,GAAS,IAAIC,cAAA,CAAe,KAAKT,QAAQ;EAChD;EAEOU,OACLC,QAAA,EACAC,WAAA,EACAC,UAAA,EACM;IACN,IAAI,KAAKd,QAAA,CAAS,KAAKH,SAAS,GAAG;MACjC,KAAKG,QAAA,CAAS,KAAKH,SAAS,EAAEkB,KAAA,GAAQD,UAAA,CAAWE,OAAA;IACnD;IAEK,KAAAP,MAAA,CAAOR,QAAA,GAAW,KAAKA,QAAA;IAE5B,IAAI,KAAKgB,cAAA,EAAgB;MACvBL,QAAA,CAASM,eAAA,CAAgB,IAAI;MACxB,KAAAT,MAAA,CAAOE,MAAA,CAAOC,QAAQ;IAAA,OACtB;MACLA,QAAA,CAASM,eAAA,CAAgBL,WAAW;MAEpC,IAAI,KAAKM,KAAA,EAAOP,QAAA,CAASO,KAAA,CAAMP,QAAA,CAASQ,cAAA,EAAgBR,QAAA,CAASS,cAAA,EAAgBT,QAAA,CAASU,gBAAgB;MACrG,KAAAb,MAAA,CAAOE,MAAA,CAAOC,QAAQ;IAC7B;EACF;EAEOW,QAAA,EAAU;IACf,KAAKd,MAAA,CAAOc,OAAA;IACZ,KAAKtB,QAAA,CAASsB,OAAA;EAChB;AACF", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}