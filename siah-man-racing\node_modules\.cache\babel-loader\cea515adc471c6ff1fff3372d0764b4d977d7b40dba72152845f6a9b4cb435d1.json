{"ast": null, "code": "import * as React from 'react';\nimport * as THREE from 'three';\nimport { useThree } from '@react-three/fiber';\nimport { Html } from '../Html.js';\nimport { context } from './context.js';\nimport { calculateScaleFactor } from '../../core/calculateScaleFactor.js';\nconst vec1 = /* @__PURE__ */new THREE.Vector3();\nconst vec2 = /* @__PURE__ */new THREE.Vector3();\nconst calculateOffset = (clickPoint, normal, rayStart, rayDir) => {\n  const e1 = normal.dot(normal);\n  const e2 = normal.dot(clickPoint) - normal.dot(rayStart);\n  const e3 = normal.dot(rayDir);\n  if (e3 === 0) {\n    return -e2 / e1;\n  }\n  vec1.copy(rayDir).multiplyScalar(e1 / e3).sub(normal);\n  vec2.copy(rayDir).multiplyScalar(e2 / e3).add(rayStart).sub(clickPoint);\n  const offset = -vec1.dot(vec2) / vec1.dot(vec1);\n  return offset;\n};\nconst upV = /* @__PURE__ */new THREE.Vector3(0, 1, 0);\nconst scaleV = /* @__PURE__ */new THREE.Vector3();\nconst scaleMatrix = /* @__PURE__ */new THREE.Matrix4();\nconst ScalingSphere = ({\n  direction,\n  axis\n}) => {\n  const {\n    scaleLimits,\n    annotations,\n    annotationsClass,\n    depthTest,\n    scale,\n    lineWidth,\n    fixed,\n    axisColors,\n    hoveredColor,\n    opacity,\n    onDragStart,\n    onDrag,\n    onDragEnd,\n    userData\n  } = React.useContext(context);\n  const size = useThree(state => state.size);\n  const camControls = useThree(state => state.controls);\n  const divRef = React.useRef(null);\n  const objRef = React.useRef(null);\n  const meshRef = React.useRef(null);\n  const scale0 = React.useRef(1);\n  const scaleCur = React.useRef(1);\n  const clickInfo = React.useRef(null);\n  const [isHovered, setIsHovered] = React.useState(false);\n  const position = fixed ? 1.2 : 1.2 * scale;\n  const onPointerDown = React.useCallback(e => {\n    if (annotations) {\n      divRef.current.innerText = `${scaleCur.current.toFixed(2)}`;\n      divRef.current.style.display = 'block';\n    }\n    e.stopPropagation();\n    const rotation = new THREE.Matrix4().extractRotation(objRef.current.matrixWorld);\n    const clickPoint = e.point.clone();\n    const origin = new THREE.Vector3().setFromMatrixPosition(objRef.current.matrixWorld);\n    const dir = direction.clone().applyMatrix4(rotation).normalize();\n    const mPLG = objRef.current.matrixWorld.clone();\n    const mPLGInv = mPLG.clone().invert();\n    const offsetMultiplier = fixed ? 1 / calculateScaleFactor(objRef.current.getWorldPosition(vec1), scale, e.camera, size) : 1;\n    clickInfo.current = {\n      clickPoint,\n      dir,\n      mPLG,\n      mPLGInv,\n      offsetMultiplier\n    };\n    onDragStart({\n      component: 'Sphere',\n      axis,\n      origin,\n      directions: [dir]\n    });\n    camControls && (camControls.enabled = false);\n    // @ts-ignore - setPointerCapture is not in the type definition\n    e.target.setPointerCapture(e.pointerId);\n  }, [annotations, camControls, direction, onDragStart, axis, fixed, scale, size]);\n  const onPointerMove = React.useCallback(e => {\n    e.stopPropagation();\n    if (!isHovered) setIsHovered(true);\n    if (clickInfo.current) {\n      const {\n        clickPoint,\n        dir,\n        mPLG,\n        mPLGInv,\n        offsetMultiplier\n      } = clickInfo.current;\n      const [min, max] = (scaleLimits == null ? void 0 : scaleLimits[axis]) || [1e-5, undefined]; // always limit the minimal value, since setting it very low might break the transform\n\n      const offsetW = calculateOffset(clickPoint, dir, e.ray.origin, e.ray.direction);\n      const offsetL = offsetW * offsetMultiplier;\n      const offsetH = fixed ? offsetL : offsetL / scale;\n      let upscale = Math.pow(2, offsetH * 0.2);\n\n      // @ts-ignore\n      if (e.shiftKey) {\n        upscale = Math.round(upscale * 10) / 10;\n      }\n      upscale = Math.max(upscale, min / scale0.current);\n      if (max !== undefined) {\n        upscale = Math.min(upscale, max / scale0.current);\n      }\n      scaleCur.current = scale0.current * upscale;\n      meshRef.current.position.set(0, position + offsetL, 0);\n      if (annotations) {\n        divRef.current.innerText = `${scaleCur.current.toFixed(2)}`;\n      }\n      scaleV.set(1, 1, 1);\n      scaleV.setComponent(axis, upscale);\n      scaleMatrix.makeScale(scaleV.x, scaleV.y, scaleV.z).premultiply(mPLG).multiply(mPLGInv);\n      onDrag(scaleMatrix);\n    }\n  }, [annotations, position, onDrag, isHovered, scaleLimits, axis]);\n  const onPointerUp = React.useCallback(e => {\n    if (annotations) {\n      divRef.current.style.display = 'none';\n    }\n    e.stopPropagation();\n    scale0.current = scaleCur.current;\n    clickInfo.current = null;\n    meshRef.current.position.set(0, position, 0);\n    onDragEnd();\n    camControls && (camControls.enabled = true);\n    // @ts-ignore - releasePointerCapture & PointerEvent#pointerId is not in the type definition\n    e.target.releasePointerCapture(e.pointerId);\n  }, [annotations, camControls, onDragEnd, position]);\n  const onPointerOut = React.useCallback(e => {\n    e.stopPropagation();\n    setIsHovered(false);\n  }, []);\n  const {\n    radius,\n    matrixL\n  } = React.useMemo(() => {\n    const radius = fixed ? lineWidth / scale * 1.8 : scale / 22.5;\n    const quaternion = new THREE.Quaternion().setFromUnitVectors(upV, direction.clone().normalize());\n    const matrixL = new THREE.Matrix4().makeRotationFromQuaternion(quaternion);\n    return {\n      radius,\n      matrixL\n    };\n  }, [direction, scale, lineWidth, fixed]);\n  const color = isHovered ? hoveredColor : axisColors[axis];\n  return /*#__PURE__*/React.createElement(\"group\", {\n    ref: objRef\n  }, /*#__PURE__*/React.createElement(\"group\", {\n    matrix: matrixL,\n    matrixAutoUpdate: false,\n    onPointerDown: onPointerDown,\n    onPointerMove: onPointerMove,\n    onPointerUp: onPointerUp,\n    onPointerOut: onPointerOut\n  }, annotations && /*#__PURE__*/React.createElement(Html, {\n    position: [0, position / 2, 0]\n  }, /*#__PURE__*/React.createElement(\"div\", {\n    style: {\n      display: 'none',\n      background: '#151520',\n      color: 'white',\n      padding: '6px 8px',\n      borderRadius: 7,\n      whiteSpace: 'nowrap'\n    },\n    className: annotationsClass,\n    ref: divRef\n  })), /*#__PURE__*/React.createElement(\"mesh\", {\n    ref: meshRef,\n    position: [0, position, 0],\n    renderOrder: 500,\n    userData: userData\n  }, /*#__PURE__*/React.createElement(\"sphereGeometry\", {\n    args: [radius, 12, 12]\n  }), /*#__PURE__*/React.createElement(\"meshBasicMaterial\", {\n    transparent: true,\n    depthTest: depthTest,\n    color: color,\n    opacity: opacity,\n    polygonOffset: true,\n    polygonOffsetFactor: -10\n  }))));\n};\nexport { ScalingSphere, calculateOffset };", "map": {"version": 3, "names": ["React", "THREE", "useThree", "Html", "context", "calculateScaleFactor", "vec1", "Vector3", "vec2", "calculateOffset", "clickPoint", "normal", "rayStart", "rayDir", "e1", "dot", "e2", "e3", "copy", "multiplyScalar", "sub", "add", "offset", "upV", "scaleV", "scaleMatrix", "Matrix4", "ScalingSphere", "direction", "axis", "scaleLimits", "annotations", "annotationsClass", "depthTest", "scale", "lineWidth", "fixed", "axisColors", "hoveredColor", "opacity", "onDragStart", "onDrag", "onDragEnd", "userData", "useContext", "size", "state", "camControls", "controls", "divRef", "useRef", "objRef", "meshRef", "scale0", "scaleCur", "clickInfo", "isHovered", "setIsHovered", "useState", "position", "onPointerDown", "useCallback", "e", "current", "innerText", "toFixed", "style", "display", "stopPropagation", "rotation", "extractRotation", "matrixWorld", "point", "clone", "origin", "setFromMatrixPosition", "dir", "applyMatrix4", "normalize", "mPLG", "mPLGInv", "invert", "offsetMultiplier", "getWorldPosition", "camera", "component", "directions", "enabled", "target", "setPointerCapture", "pointerId", "onPointerMove", "min", "max", "undefined", "offsetW", "ray", "offsetL", "offsetH", "upscale", "Math", "pow", "shift<PERSON>ey", "round", "set", "setComponent", "makeScale", "x", "y", "z", "premultiply", "multiply", "onPointerUp", "releasePointerCapture", "onPointerOut", "radius", "matrixL", "useMemo", "quaternion", "Quaternion", "setFromUnitVectors", "makeRotationFromQuaternion", "color", "createElement", "ref", "matrix", "matrixAutoUpdate", "background", "padding", "borderRadius", "whiteSpace", "className", "renderOrder", "args", "transparent", "polygonOffset", "polygonOffsetFactor"], "sources": ["C:/Users/<USER>/Documents/Apps_Creations/SiahMan_Racing4Speed_WebGame/siah-man-racing/node_modules/@react-three/drei/web/pivotControls/ScalingSphere.js"], "sourcesContent": ["import * as React from 'react';\nimport * as THREE from 'three';\nimport { useThree } from '@react-three/fiber';\nimport { Html } from '../Html.js';\nimport { context } from './context.js';\nimport { calculateScaleFactor } from '../../core/calculateScaleFactor.js';\n\nconst vec1 = /* @__PURE__ */new THREE.Vector3();\nconst vec2 = /* @__PURE__ */new THREE.Vector3();\nconst calculateOffset = (clickPoint, normal, rayStart, rayDir) => {\n  const e1 = normal.dot(normal);\n  const e2 = normal.dot(clickPoint) - normal.dot(rayStart);\n  const e3 = normal.dot(rayDir);\n  if (e3 === 0) {\n    return -e2 / e1;\n  }\n  vec1.copy(rayDir).multiplyScalar(e1 / e3).sub(normal);\n  vec2.copy(rayDir).multiplyScalar(e2 / e3).add(rayStart).sub(clickPoint);\n  const offset = -vec1.dot(vec2) / vec1.dot(vec1);\n  return offset;\n};\nconst upV = /* @__PURE__ */new THREE.Vector3(0, 1, 0);\nconst scaleV = /* @__PURE__ */new THREE.Vector3();\nconst scaleMatrix = /* @__PURE__ */new THREE.Matrix4();\nconst ScalingSphere = ({\n  direction,\n  axis\n}) => {\n  const {\n    scaleLimits,\n    annotations,\n    annotationsClass,\n    depthTest,\n    scale,\n    lineWidth,\n    fixed,\n    axisColors,\n    hoveredColor,\n    opacity,\n    onDragStart,\n    onDrag,\n    onDragEnd,\n    userData\n  } = React.useContext(context);\n  const size = useThree(state => state.size);\n  const camControls = useThree(state => state.controls);\n  const divRef = React.useRef(null);\n  const objRef = React.useRef(null);\n  const meshRef = React.useRef(null);\n  const scale0 = React.useRef(1);\n  const scaleCur = React.useRef(1);\n  const clickInfo = React.useRef(null);\n  const [isHovered, setIsHovered] = React.useState(false);\n  const position = fixed ? 1.2 : 1.2 * scale;\n  const onPointerDown = React.useCallback(e => {\n    if (annotations) {\n      divRef.current.innerText = `${scaleCur.current.toFixed(2)}`;\n      divRef.current.style.display = 'block';\n    }\n    e.stopPropagation();\n    const rotation = new THREE.Matrix4().extractRotation(objRef.current.matrixWorld);\n    const clickPoint = e.point.clone();\n    const origin = new THREE.Vector3().setFromMatrixPosition(objRef.current.matrixWorld);\n    const dir = direction.clone().applyMatrix4(rotation).normalize();\n    const mPLG = objRef.current.matrixWorld.clone();\n    const mPLGInv = mPLG.clone().invert();\n    const offsetMultiplier = fixed ? 1 / calculateScaleFactor(objRef.current.getWorldPosition(vec1), scale, e.camera, size) : 1;\n    clickInfo.current = {\n      clickPoint,\n      dir,\n      mPLG,\n      mPLGInv,\n      offsetMultiplier\n    };\n    onDragStart({\n      component: 'Sphere',\n      axis,\n      origin,\n      directions: [dir]\n    });\n    camControls && (camControls.enabled = false);\n    // @ts-ignore - setPointerCapture is not in the type definition\n    e.target.setPointerCapture(e.pointerId);\n  }, [annotations, camControls, direction, onDragStart, axis, fixed, scale, size]);\n  const onPointerMove = React.useCallback(e => {\n    e.stopPropagation();\n    if (!isHovered) setIsHovered(true);\n    if (clickInfo.current) {\n      const {\n        clickPoint,\n        dir,\n        mPLG,\n        mPLGInv,\n        offsetMultiplier\n      } = clickInfo.current;\n      const [min, max] = (scaleLimits == null ? void 0 : scaleLimits[axis]) || [1e-5, undefined]; // always limit the minimal value, since setting it very low might break the transform\n\n      const offsetW = calculateOffset(clickPoint, dir, e.ray.origin, e.ray.direction);\n      const offsetL = offsetW * offsetMultiplier;\n      const offsetH = fixed ? offsetL : offsetL / scale;\n      let upscale = Math.pow(2, offsetH * 0.2);\n\n      // @ts-ignore\n      if (e.shiftKey) {\n        upscale = Math.round(upscale * 10) / 10;\n      }\n      upscale = Math.max(upscale, min / scale0.current);\n      if (max !== undefined) {\n        upscale = Math.min(upscale, max / scale0.current);\n      }\n      scaleCur.current = scale0.current * upscale;\n      meshRef.current.position.set(0, position + offsetL, 0);\n      if (annotations) {\n        divRef.current.innerText = `${scaleCur.current.toFixed(2)}`;\n      }\n      scaleV.set(1, 1, 1);\n      scaleV.setComponent(axis, upscale);\n      scaleMatrix.makeScale(scaleV.x, scaleV.y, scaleV.z).premultiply(mPLG).multiply(mPLGInv);\n      onDrag(scaleMatrix);\n    }\n  }, [annotations, position, onDrag, isHovered, scaleLimits, axis]);\n  const onPointerUp = React.useCallback(e => {\n    if (annotations) {\n      divRef.current.style.display = 'none';\n    }\n    e.stopPropagation();\n    scale0.current = scaleCur.current;\n    clickInfo.current = null;\n    meshRef.current.position.set(0, position, 0);\n    onDragEnd();\n    camControls && (camControls.enabled = true);\n    // @ts-ignore - releasePointerCapture & PointerEvent#pointerId is not in the type definition\n    e.target.releasePointerCapture(e.pointerId);\n  }, [annotations, camControls, onDragEnd, position]);\n  const onPointerOut = React.useCallback(e => {\n    e.stopPropagation();\n    setIsHovered(false);\n  }, []);\n  const {\n    radius,\n    matrixL\n  } = React.useMemo(() => {\n    const radius = fixed ? lineWidth / scale * 1.8 : scale / 22.5;\n    const quaternion = new THREE.Quaternion().setFromUnitVectors(upV, direction.clone().normalize());\n    const matrixL = new THREE.Matrix4().makeRotationFromQuaternion(quaternion);\n    return {\n      radius,\n      matrixL\n    };\n  }, [direction, scale, lineWidth, fixed]);\n  const color = isHovered ? hoveredColor : axisColors[axis];\n  return /*#__PURE__*/React.createElement(\"group\", {\n    ref: objRef\n  }, /*#__PURE__*/React.createElement(\"group\", {\n    matrix: matrixL,\n    matrixAutoUpdate: false,\n    onPointerDown: onPointerDown,\n    onPointerMove: onPointerMove,\n    onPointerUp: onPointerUp,\n    onPointerOut: onPointerOut\n  }, annotations && /*#__PURE__*/React.createElement(Html, {\n    position: [0, position / 2, 0]\n  }, /*#__PURE__*/React.createElement(\"div\", {\n    style: {\n      display: 'none',\n      background: '#151520',\n      color: 'white',\n      padding: '6px 8px',\n      borderRadius: 7,\n      whiteSpace: 'nowrap'\n    },\n    className: annotationsClass,\n    ref: divRef\n  })), /*#__PURE__*/React.createElement(\"mesh\", {\n    ref: meshRef,\n    position: [0, position, 0],\n    renderOrder: 500,\n    userData: userData\n  }, /*#__PURE__*/React.createElement(\"sphereGeometry\", {\n    args: [radius, 12, 12]\n  }), /*#__PURE__*/React.createElement(\"meshBasicMaterial\", {\n    transparent: true,\n    depthTest: depthTest,\n    color: color,\n    opacity: opacity,\n    polygonOffset: true,\n    polygonOffsetFactor: -10\n  }))));\n};\n\nexport { ScalingSphere, calculateOffset };\n"], "mappings": "AAAA,OAAO,KAAKA,KAAK,MAAM,OAAO;AAC9B,OAAO,KAAKC,KAAK,MAAM,OAAO;AAC9B,SAASC,QAAQ,QAAQ,oBAAoB;AAC7C,SAASC,IAAI,QAAQ,YAAY;AACjC,SAASC,OAAO,QAAQ,cAAc;AACtC,SAASC,oBAAoB,QAAQ,oCAAoC;AAEzE,MAAMC,IAAI,GAAG,eAAe,IAAIL,KAAK,CAACM,OAAO,CAAC,CAAC;AAC/C,MAAMC,IAAI,GAAG,eAAe,IAAIP,KAAK,CAACM,OAAO,CAAC,CAAC;AAC/C,MAAME,eAAe,GAAGA,CAACC,UAAU,EAAEC,MAAM,EAAEC,QAAQ,EAAEC,MAAM,KAAK;EAChE,MAAMC,EAAE,GAAGH,MAAM,CAACI,GAAG,CAACJ,MAAM,CAAC;EAC7B,MAAMK,EAAE,GAAGL,MAAM,CAACI,GAAG,CAACL,UAAU,CAAC,GAAGC,MAAM,CAACI,GAAG,CAACH,QAAQ,CAAC;EACxD,MAAMK,EAAE,GAAGN,MAAM,CAACI,GAAG,CAACF,MAAM,CAAC;EAC7B,IAAII,EAAE,KAAK,CAAC,EAAE;IACZ,OAAO,CAACD,EAAE,GAAGF,EAAE;EACjB;EACAR,IAAI,CAACY,IAAI,CAACL,MAAM,CAAC,CAACM,cAAc,CAACL,EAAE,GAAGG,EAAE,CAAC,CAACG,GAAG,CAACT,MAAM,CAAC;EACrDH,IAAI,CAACU,IAAI,CAACL,MAAM,CAAC,CAACM,cAAc,CAACH,EAAE,GAAGC,EAAE,CAAC,CAACI,GAAG,CAACT,QAAQ,CAAC,CAACQ,GAAG,CAACV,UAAU,CAAC;EACvE,MAAMY,MAAM,GAAG,CAAChB,IAAI,CAACS,GAAG,CAACP,IAAI,CAAC,GAAGF,IAAI,CAACS,GAAG,CAACT,IAAI,CAAC;EAC/C,OAAOgB,MAAM;AACf,CAAC;AACD,MAAMC,GAAG,GAAG,eAAe,IAAItB,KAAK,CAACM,OAAO,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC;AACrD,MAAMiB,MAAM,GAAG,eAAe,IAAIvB,KAAK,CAACM,OAAO,CAAC,CAAC;AACjD,MAAMkB,WAAW,GAAG,eAAe,IAAIxB,KAAK,CAACyB,OAAO,CAAC,CAAC;AACtD,MAAMC,aAAa,GAAGA,CAAC;EACrBC,SAAS;EACTC;AACF,CAAC,KAAK;EACJ,MAAM;IACJC,WAAW;IACXC,WAAW;IACXC,gBAAgB;IAChBC,SAAS;IACTC,KAAK;IACLC,SAAS;IACTC,KAAK;IACLC,UAAU;IACVC,YAAY;IACZC,OAAO;IACPC,WAAW;IACXC,MAAM;IACNC,SAAS;IACTC;EACF,CAAC,GAAG3C,KAAK,CAAC4C,UAAU,CAACxC,OAAO,CAAC;EAC7B,MAAMyC,IAAI,GAAG3C,QAAQ,CAAC4C,KAAK,IAAIA,KAAK,CAACD,IAAI,CAAC;EAC1C,MAAME,WAAW,GAAG7C,QAAQ,CAAC4C,KAAK,IAAIA,KAAK,CAACE,QAAQ,CAAC;EACrD,MAAMC,MAAM,GAAGjD,KAAK,CAACkD,MAAM,CAAC,IAAI,CAAC;EACjC,MAAMC,MAAM,GAAGnD,KAAK,CAACkD,MAAM,CAAC,IAAI,CAAC;EACjC,MAAME,OAAO,GAAGpD,KAAK,CAACkD,MAAM,CAAC,IAAI,CAAC;EAClC,MAAMG,MAAM,GAAGrD,KAAK,CAACkD,MAAM,CAAC,CAAC,CAAC;EAC9B,MAAMI,QAAQ,GAAGtD,KAAK,CAACkD,MAAM,CAAC,CAAC,CAAC;EAChC,MAAMK,SAAS,GAAGvD,KAAK,CAACkD,MAAM,CAAC,IAAI,CAAC;EACpC,MAAM,CAACM,SAAS,EAAEC,YAAY,CAAC,GAAGzD,KAAK,CAAC0D,QAAQ,CAAC,KAAK,CAAC;EACvD,MAAMC,QAAQ,GAAGvB,KAAK,GAAG,GAAG,GAAG,GAAG,GAAGF,KAAK;EAC1C,MAAM0B,aAAa,GAAG5D,KAAK,CAAC6D,WAAW,CAACC,CAAC,IAAI;IAC3C,IAAI/B,WAAW,EAAE;MACfkB,MAAM,CAACc,OAAO,CAACC,SAAS,GAAG,GAAGV,QAAQ,CAACS,OAAO,CAACE,OAAO,CAAC,CAAC,CAAC,EAAE;MAC3DhB,MAAM,CAACc,OAAO,CAACG,KAAK,CAACC,OAAO,GAAG,OAAO;IACxC;IACAL,CAAC,CAACM,eAAe,CAAC,CAAC;IACnB,MAAMC,QAAQ,GAAG,IAAIpE,KAAK,CAACyB,OAAO,CAAC,CAAC,CAAC4C,eAAe,CAACnB,MAAM,CAACY,OAAO,CAACQ,WAAW,CAAC;IAChF,MAAM7D,UAAU,GAAGoD,CAAC,CAACU,KAAK,CAACC,KAAK,CAAC,CAAC;IAClC,MAAMC,MAAM,GAAG,IAAIzE,KAAK,CAACM,OAAO,CAAC,CAAC,CAACoE,qBAAqB,CAACxB,MAAM,CAACY,OAAO,CAACQ,WAAW,CAAC;IACpF,MAAMK,GAAG,GAAGhD,SAAS,CAAC6C,KAAK,CAAC,CAAC,CAACI,YAAY,CAACR,QAAQ,CAAC,CAACS,SAAS,CAAC,CAAC;IAChE,MAAMC,IAAI,GAAG5B,MAAM,CAACY,OAAO,CAACQ,WAAW,CAACE,KAAK,CAAC,CAAC;IAC/C,MAAMO,OAAO,GAAGD,IAAI,CAACN,KAAK,CAAC,CAAC,CAACQ,MAAM,CAAC,CAAC;IACrC,MAAMC,gBAAgB,GAAG9C,KAAK,GAAG,CAAC,GAAG/B,oBAAoB,CAAC8C,MAAM,CAACY,OAAO,CAACoB,gBAAgB,CAAC7E,IAAI,CAAC,EAAE4B,KAAK,EAAE4B,CAAC,CAACsB,MAAM,EAAEvC,IAAI,CAAC,GAAG,CAAC;IAC3HU,SAAS,CAACQ,OAAO,GAAG;MAClBrD,UAAU;MACVkE,GAAG;MACHG,IAAI;MACJC,OAAO;MACPE;IACF,CAAC;IACD1C,WAAW,CAAC;MACV6C,SAAS,EAAE,QAAQ;MACnBxD,IAAI;MACJ6C,MAAM;MACNY,UAAU,EAAE,CAACV,GAAG;IAClB,CAAC,CAAC;IACF7B,WAAW,KAAKA,WAAW,CAACwC,OAAO,GAAG,KAAK,CAAC;IAC5C;IACAzB,CAAC,CAAC0B,MAAM,CAACC,iBAAiB,CAAC3B,CAAC,CAAC4B,SAAS,CAAC;EACzC,CAAC,EAAE,CAAC3D,WAAW,EAAEgB,WAAW,EAAEnB,SAAS,EAAEY,WAAW,EAAEX,IAAI,EAAEO,KAAK,EAAEF,KAAK,EAAEW,IAAI,CAAC,CAAC;EAChF,MAAM8C,aAAa,GAAG3F,KAAK,CAAC6D,WAAW,CAACC,CAAC,IAAI;IAC3CA,CAAC,CAACM,eAAe,CAAC,CAAC;IACnB,IAAI,CAACZ,SAAS,EAAEC,YAAY,CAAC,IAAI,CAAC;IAClC,IAAIF,SAAS,CAACQ,OAAO,EAAE;MACrB,MAAM;QACJrD,UAAU;QACVkE,GAAG;QACHG,IAAI;QACJC,OAAO;QACPE;MACF,CAAC,GAAG3B,SAAS,CAACQ,OAAO;MACrB,MAAM,CAAC6B,GAAG,EAAEC,GAAG,CAAC,GAAG,CAAC/D,WAAW,IAAI,IAAI,GAAG,KAAK,CAAC,GAAGA,WAAW,CAACD,IAAI,CAAC,KAAK,CAAC,IAAI,EAAEiE,SAAS,CAAC,CAAC,CAAC;;MAE5F,MAAMC,OAAO,GAAGtF,eAAe,CAACC,UAAU,EAAEkE,GAAG,EAAEd,CAAC,CAACkC,GAAG,CAACtB,MAAM,EAAEZ,CAAC,CAACkC,GAAG,CAACpE,SAAS,CAAC;MAC/E,MAAMqE,OAAO,GAAGF,OAAO,GAAGb,gBAAgB;MAC1C,MAAMgB,OAAO,GAAG9D,KAAK,GAAG6D,OAAO,GAAGA,OAAO,GAAG/D,KAAK;MACjD,IAAIiE,OAAO,GAAGC,IAAI,CAACC,GAAG,CAAC,CAAC,EAAEH,OAAO,GAAG,GAAG,CAAC;;MAExC;MACA,IAAIpC,CAAC,CAACwC,QAAQ,EAAE;QACdH,OAAO,GAAGC,IAAI,CAACG,KAAK,CAACJ,OAAO,GAAG,EAAE,CAAC,GAAG,EAAE;MACzC;MACAA,OAAO,GAAGC,IAAI,CAACP,GAAG,CAACM,OAAO,EAAEP,GAAG,GAAGvC,MAAM,CAACU,OAAO,CAAC;MACjD,IAAI8B,GAAG,KAAKC,SAAS,EAAE;QACrBK,OAAO,GAAGC,IAAI,CAACR,GAAG,CAACO,OAAO,EAAEN,GAAG,GAAGxC,MAAM,CAACU,OAAO,CAAC;MACnD;MACAT,QAAQ,CAACS,OAAO,GAAGV,MAAM,CAACU,OAAO,GAAGoC,OAAO;MAC3C/C,OAAO,CAACW,OAAO,CAACJ,QAAQ,CAAC6C,GAAG,CAAC,CAAC,EAAE7C,QAAQ,GAAGsC,OAAO,EAAE,CAAC,CAAC;MACtD,IAAIlE,WAAW,EAAE;QACfkB,MAAM,CAACc,OAAO,CAACC,SAAS,GAAG,GAAGV,QAAQ,CAACS,OAAO,CAACE,OAAO,CAAC,CAAC,CAAC,EAAE;MAC7D;MACAzC,MAAM,CAACgF,GAAG,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC;MACnBhF,MAAM,CAACiF,YAAY,CAAC5E,IAAI,EAAEsE,OAAO,CAAC;MAClC1E,WAAW,CAACiF,SAAS,CAAClF,MAAM,CAACmF,CAAC,EAAEnF,MAAM,CAACoF,CAAC,EAAEpF,MAAM,CAACqF,CAAC,CAAC,CAACC,WAAW,CAAC/B,IAAI,CAAC,CAACgC,QAAQ,CAAC/B,OAAO,CAAC;MACvFvC,MAAM,CAAChB,WAAW,CAAC;IACrB;EACF,CAAC,EAAE,CAACM,WAAW,EAAE4B,QAAQ,EAAElB,MAAM,EAAEe,SAAS,EAAE1B,WAAW,EAAED,IAAI,CAAC,CAAC;EACjE,MAAMmF,WAAW,GAAGhH,KAAK,CAAC6D,WAAW,CAACC,CAAC,IAAI;IACzC,IAAI/B,WAAW,EAAE;MACfkB,MAAM,CAACc,OAAO,CAACG,KAAK,CAACC,OAAO,GAAG,MAAM;IACvC;IACAL,CAAC,CAACM,eAAe,CAAC,CAAC;IACnBf,MAAM,CAACU,OAAO,GAAGT,QAAQ,CAACS,OAAO;IACjCR,SAAS,CAACQ,OAAO,GAAG,IAAI;IACxBX,OAAO,CAACW,OAAO,CAACJ,QAAQ,CAAC6C,GAAG,CAAC,CAAC,EAAE7C,QAAQ,EAAE,CAAC,CAAC;IAC5CjB,SAAS,CAAC,CAAC;IACXK,WAAW,KAAKA,WAAW,CAACwC,OAAO,GAAG,IAAI,CAAC;IAC3C;IACAzB,CAAC,CAAC0B,MAAM,CAACyB,qBAAqB,CAACnD,CAAC,CAAC4B,SAAS,CAAC;EAC7C,CAAC,EAAE,CAAC3D,WAAW,EAAEgB,WAAW,EAAEL,SAAS,EAAEiB,QAAQ,CAAC,CAAC;EACnD,MAAMuD,YAAY,GAAGlH,KAAK,CAAC6D,WAAW,CAACC,CAAC,IAAI;IAC1CA,CAAC,CAACM,eAAe,CAAC,CAAC;IACnBX,YAAY,CAAC,KAAK,CAAC;EACrB,CAAC,EAAE,EAAE,CAAC;EACN,MAAM;IACJ0D,MAAM;IACNC;EACF,CAAC,GAAGpH,KAAK,CAACqH,OAAO,CAAC,MAAM;IACtB,MAAMF,MAAM,GAAG/E,KAAK,GAAGD,SAAS,GAAGD,KAAK,GAAG,GAAG,GAAGA,KAAK,GAAG,IAAI;IAC7D,MAAMoF,UAAU,GAAG,IAAIrH,KAAK,CAACsH,UAAU,CAAC,CAAC,CAACC,kBAAkB,CAACjG,GAAG,EAAEK,SAAS,CAAC6C,KAAK,CAAC,CAAC,CAACK,SAAS,CAAC,CAAC,CAAC;IAChG,MAAMsC,OAAO,GAAG,IAAInH,KAAK,CAACyB,OAAO,CAAC,CAAC,CAAC+F,0BAA0B,CAACH,UAAU,CAAC;IAC1E,OAAO;MACLH,MAAM;MACNC;IACF,CAAC;EACH,CAAC,EAAE,CAACxF,SAAS,EAAEM,KAAK,EAAEC,SAAS,EAAEC,KAAK,CAAC,CAAC;EACxC,MAAMsF,KAAK,GAAGlE,SAAS,GAAGlB,YAAY,GAAGD,UAAU,CAACR,IAAI,CAAC;EACzD,OAAO,aAAa7B,KAAK,CAAC2H,aAAa,CAAC,OAAO,EAAE;IAC/CC,GAAG,EAAEzE;EACP,CAAC,EAAE,aAAanD,KAAK,CAAC2H,aAAa,CAAC,OAAO,EAAE;IAC3CE,MAAM,EAAET,OAAO;IACfU,gBAAgB,EAAE,KAAK;IACvBlE,aAAa,EAAEA,aAAa;IAC5B+B,aAAa,EAAEA,aAAa;IAC5BqB,WAAW,EAAEA,WAAW;IACxBE,YAAY,EAAEA;EAChB,CAAC,EAAEnF,WAAW,IAAI,aAAa/B,KAAK,CAAC2H,aAAa,CAACxH,IAAI,EAAE;IACvDwD,QAAQ,EAAE,CAAC,CAAC,EAAEA,QAAQ,GAAG,CAAC,EAAE,CAAC;EAC/B,CAAC,EAAE,aAAa3D,KAAK,CAAC2H,aAAa,CAAC,KAAK,EAAE;IACzCzD,KAAK,EAAE;MACLC,OAAO,EAAE,MAAM;MACf4D,UAAU,EAAE,SAAS;MACrBL,KAAK,EAAE,OAAO;MACdM,OAAO,EAAE,SAAS;MAClBC,YAAY,EAAE,CAAC;MACfC,UAAU,EAAE;IACd,CAAC;IACDC,SAAS,EAAEnG,gBAAgB;IAC3B4F,GAAG,EAAE3E;EACP,CAAC,CAAC,CAAC,EAAE,aAAajD,KAAK,CAAC2H,aAAa,CAAC,MAAM,EAAE;IAC5CC,GAAG,EAAExE,OAAO;IACZO,QAAQ,EAAE,CAAC,CAAC,EAAEA,QAAQ,EAAE,CAAC,CAAC;IAC1ByE,WAAW,EAAE,GAAG;IAChBzF,QAAQ,EAAEA;EACZ,CAAC,EAAE,aAAa3C,KAAK,CAAC2H,aAAa,CAAC,gBAAgB,EAAE;IACpDU,IAAI,EAAE,CAAClB,MAAM,EAAE,EAAE,EAAE,EAAE;EACvB,CAAC,CAAC,EAAE,aAAanH,KAAK,CAAC2H,aAAa,CAAC,mBAAmB,EAAE;IACxDW,WAAW,EAAE,IAAI;IACjBrG,SAAS,EAAEA,SAAS;IACpByF,KAAK,EAAEA,KAAK;IACZnF,OAAO,EAAEA,OAAO;IAChBgG,aAAa,EAAE,IAAI;IACnBC,mBAAmB,EAAE,CAAC;EACxB,CAAC,CAAC,CAAC,CAAC,CAAC;AACP,CAAC;AAED,SAAS7G,aAAa,EAAElB,eAAe", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}