{"ast": null, "code": "var __defProp = Object.defineProperty;\nvar __defNormalProp = (obj, key, value) => key in obj ? __defProp(obj, key, {\n  enumerable: true,\n  configurable: true,\n  writable: true,\n  value\n}) : obj[key] = value;\nvar __publicField = (obj, key, value) => {\n  __defNormalProp(obj, typeof key !== \"symbol\" ? key + \"\" : key, value);\n  return value;\n};\nimport { Pass, FullScreenQuad } from \"./Pass.js\";\nimport { UniformsUtils, ShaderMaterial } from \"three\";\nimport { FilmShader } from \"../shaders/FilmShader.js\";\nclass FilmPass extends Pass {\n  constructor(noiseIntensity, scanlinesIntensity, scanlinesCount, grayscale) {\n    super();\n    __publicField(this, \"material\");\n    __publicField(this, \"fsQuad\");\n    __publicField(this, \"uniforms\");\n    if (FilmShader === void 0) console.error(\"THREE.FilmPass relies on FilmShader\");\n    const shader = FilmShader;\n    this.uniforms = UniformsUtils.clone(shader.uniforms);\n    this.material = new ShaderMaterial({\n      uniforms: this.uniforms,\n      vertexShader: shader.vertexShader,\n      fragmentShader: shader.fragmentShader\n    });\n    if (grayscale !== void 0) this.uniforms.grayscale.value = grayscale;\n    if (noiseIntensity !== void 0) this.uniforms.nIntensity.value = noiseIntensity;\n    if (scanlinesIntensity !== void 0) this.uniforms.sIntensity.value = scanlinesIntensity;\n    if (scanlinesCount !== void 0) this.uniforms.sCount.value = scanlinesCount;\n    this.fsQuad = new FullScreenQuad(this.material);\n  }\n  render(renderer, writeBuffer, readBuffer, deltaTime) {\n    this.uniforms[\"tDiffuse\"].value = readBuffer.texture;\n    this.uniforms[\"time\"].value += deltaTime;\n    if (this.renderToScreen) {\n      renderer.setRenderTarget(null);\n      this.fsQuad.render(renderer);\n    } else {\n      renderer.setRenderTarget(writeBuffer);\n      if (this.clear) renderer.clear();\n      this.fsQuad.render(renderer);\n    }\n  }\n}\nexport { FilmPass };", "map": {"version": 3, "names": ["FilmPass", "Pass", "constructor", "noiseIntensity", "scanlinesIntensity", "scanlinesCount", "grayscale", "__publicField", "FilmShader", "console", "error", "shader", "uniforms", "UniformsUtils", "clone", "material", "ShaderMaterial", "vertexShader", "fragmentShader", "value", "nIntensity", "sIntensity", "sCount", "fsQuad", "FullScreenQuad", "render", "renderer", "writeBuffer", "readBuffer", "deltaTime", "texture", "renderToScreen", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "clear"], "sources": ["C:\\Users\\<USER>\\Documents\\Apps_Creations\\SiahMan_Racing4Speed_WebGame\\siah-man-racing\\node_modules\\src\\postprocessing\\FilmPass.ts"], "sourcesContent": ["import { Pass, FullScreenQuad } from './Pass'\nimport { IUniform, ShaderMaterial, UniformsUtils, WebGLRenderer, WebGLRenderTarget } from 'three'\nimport { FilmShader } from '../shaders/FilmShader'\n\nclass FilmPass extends Pass {\n  public material: ShaderMaterial\n  public fsQuad: FullScreenQuad\n\n  public uniforms: Record<keyof typeof FilmShader['uniforms'], IUniform<any>>\n\n  constructor(noiseIntensity?: number, scanlinesIntensity?: number, scanlinesCount?: number, grayscale?: boolean) {\n    super()\n\n    if (FilmShader === undefined) console.error('THREE.FilmPass relies on FilmShader')\n\n    const shader = FilmShader\n\n    this.uniforms = UniformsUtils.clone(shader.uniforms)\n\n    this.material = new ShaderMaterial({\n      uniforms: this.uniforms,\n      vertexShader: shader.vertexShader,\n      fragmentShader: shader.fragmentShader,\n    })\n\n    if (grayscale !== undefined) this.uniforms.grayscale.value = grayscale\n    if (noiseIntensity !== undefined) this.uniforms.nIntensity.value = noiseIntensity\n    if (scanlinesIntensity !== undefined) this.uniforms.sIntensity.value = scanlinesIntensity\n    if (scanlinesCount !== undefined) this.uniforms.sCount.value = scanlinesCount\n\n    this.fsQuad = new FullScreenQuad(this.material)\n  }\n\n  public render(\n    renderer: WebGLRenderer,\n    writeBuffer: WebGLRenderTarget,\n    readBuffer: WebGLRenderTarget,\n    deltaTime: number,\n  ): void {\n    this.uniforms['tDiffuse'].value = readBuffer.texture\n    this.uniforms['time'].value += deltaTime\n\n    if (this.renderToScreen) {\n      renderer.setRenderTarget(null)\n      this.fsQuad.render(renderer)\n    } else {\n      renderer.setRenderTarget(writeBuffer)\n      if (this.clear) renderer.clear()\n      this.fsQuad.render(renderer)\n    }\n  }\n}\n\nexport { FilmPass }\n"], "mappings": ";;;;;;;;;;;;;;AAIA,MAAMA,QAAA,SAAiBC,IAAA,CAAK;EAM1BC,YAAYC,cAAA,EAAyBC,kBAAA,EAA6BC,cAAA,EAAyBC,SAAA,EAAqB;IACxG;IANDC,aAAA;IACAA,aAAA;IAEAA,aAAA;IAKL,IAAIC,UAAA,KAAe,QAAWC,OAAA,CAAQC,KAAA,CAAM,qCAAqC;IAEjF,MAAMC,MAAA,GAASH,UAAA;IAEf,KAAKI,QAAA,GAAWC,aAAA,CAAcC,KAAA,CAAMH,MAAA,CAAOC,QAAQ;IAE9C,KAAAG,QAAA,GAAW,IAAIC,cAAA,CAAe;MACjCJ,QAAA,EAAU,KAAKA,QAAA;MACfK,YAAA,EAAcN,MAAA,CAAOM,YAAA;MACrBC,cAAA,EAAgBP,MAAA,CAAOO;IAAA,CACxB;IAED,IAAIZ,SAAA,KAAc,QAAgB,KAAAM,QAAA,CAASN,SAAA,CAAUa,KAAA,GAAQb,SAAA;IAC7D,IAAIH,cAAA,KAAmB,QAAgB,KAAAS,QAAA,CAASQ,UAAA,CAAWD,KAAA,GAAQhB,cAAA;IACnE,IAAIC,kBAAA,KAAuB,QAAgB,KAAAQ,QAAA,CAASS,UAAA,CAAWF,KAAA,GAAQf,kBAAA;IACvE,IAAIC,cAAA,KAAmB,QAAgB,KAAAO,QAAA,CAASU,MAAA,CAAOH,KAAA,GAAQd,cAAA;IAE/D,KAAKkB,MAAA,GAAS,IAAIC,cAAA,CAAe,KAAKT,QAAQ;EAChD;EAEOU,OACLC,QAAA,EACAC,WAAA,EACAC,UAAA,EACAC,SAAA,EACM;IACN,KAAKjB,QAAA,CAAS,UAAU,EAAEO,KAAA,GAAQS,UAAA,CAAWE,OAAA;IACxC,KAAAlB,QAAA,CAAS,MAAM,EAAEO,KAAA,IAASU,SAAA;IAE/B,IAAI,KAAKE,cAAA,EAAgB;MACvBL,QAAA,CAASM,eAAA,CAAgB,IAAI;MACxB,KAAAT,MAAA,CAAOE,MAAA,CAAOC,QAAQ;IAAA,OACtB;MACLA,QAAA,CAASM,eAAA,CAAgBL,WAAW;MACpC,IAAI,KAAKM,KAAA,EAAOP,QAAA,CAASO,KAAA,CAAM;MAC1B,KAAAV,MAAA,CAAOE,MAAA,CAAOC,QAAQ;IAC7B;EACF;AACF", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}