[{"C:\\Users\\<USER>\\Documents\\Apps_Creations\\SiahMan_Racing4Speed_WebGame\\siah-man-racing\\src\\index.tsx": "1", "C:\\Users\\<USER>\\Documents\\Apps_Creations\\SiahMan_Racing4Speed_WebGame\\siah-man-racing\\src\\reportWebVitals.ts": "2", "C:\\Users\\<USER>\\Documents\\Apps_Creations\\SiahMan_Racing4Speed_WebGame\\siah-man-racing\\src\\App.tsx": "3", "C:\\Users\\<USER>\\Documents\\Apps_Creations\\SiahMan_Racing4Speed_WebGame\\siah-man-racing\\src\\store\\index.ts": "4", "C:\\Users\\<USER>\\Documents\\Apps_Creations\\SiahMan_Racing4Speed_WebGame\\siah-man-racing\\src\\GlobalStyle.ts": "5", "C:\\Users\\<USER>\\Documents\\Apps_Creations\\SiahMan_Racing4Speed_WebGame\\siah-man-racing\\src\\theme.ts": "6", "C:\\Users\\<USER>\\Documents\\Apps_Creations\\SiahMan_Racing4Speed_WebGame\\siah-man-racing\\src\\pages\\VehicleCustomization.tsx": "7", "C:\\Users\\<USER>\\Documents\\Apps_Creations\\SiahMan_Racing4Speed_WebGame\\siah-man-racing\\src\\pages\\IntegratedRacing.tsx": "8", "C:\\Users\\<USER>\\Documents\\Apps_Creations\\SiahMan_Racing4Speed_WebGame\\siah-man-racing\\src\\pages\\Laboratory.tsx": "9", "C:\\Users\\<USER>\\Documents\\Apps_Creations\\SiahMan_Racing4Speed_WebGame\\siah-man-racing\\src\\pages\\MainMenu.tsx": "10", "C:\\Users\\<USER>\\Documents\\Apps_Creations\\SiahMan_Racing4Speed_WebGame\\siah-man-racing\\src\\pages\\Settings.tsx": "11", "C:\\Users\\<USER>\\Documents\\Apps_Creations\\SiahMan_Racing4Speed_WebGame\\siah-man-racing\\src\\pages\\StoryMode.tsx": "12", "C:\\Users\\<USER>\\Documents\\Apps_Creations\\SiahMan_Racing4Speed_WebGame\\siah-man-racing\\src\\pages\\GameMode.tsx": "13", "C:\\Users\\<USER>\\Documents\\Apps_Creations\\SiahMan_Racing4Speed_WebGame\\siah-man-racing\\src\\components\\InputController.tsx": "14", "C:\\Users\\<USER>\\Documents\\Apps_Creations\\SiahMan_Racing4Speed_WebGame\\siah-man-racing\\src\\components\\AudioController.tsx": "15", "C:\\Users\\<USER>\\Documents\\Apps_Creations\\SiahMan_Racing4Speed_WebGame\\siah-man-racing\\src\\components\\ErrorBoundary.tsx": "16", "C:\\Users\\<USER>\\Documents\\Apps_Creations\\SiahMan_Racing4Speed_WebGame\\siah-man-racing\\src\\components\\AssetLoader.tsx": "17", "C:\\Users\\<USER>\\Documents\\Apps_Creations\\SiahMan_Racing4Speed_WebGame\\siah-man-racing\\src\\store\\gameSlice.ts": "18", "C:\\Users\\<USER>\\Documents\\Apps_Creations\\SiahMan_Racing4Speed_WebGame\\siah-man-racing\\src\\store\\vehicleSlice.ts": "19", "C:\\Users\\<USER>\\Documents\\Apps_Creations\\SiahMan_Racing4Speed_WebGame\\siah-man-racing\\src\\store\\userSlice.ts": "20", "C:\\Users\\<USER>\\Documents\\Apps_Creations\\SiahMan_Racing4Speed_WebGame\\siah-man-racing\\src\\components\\RacingEnvironment.tsx": "21", "C:\\Users\\<USER>\\Documents\\Apps_Creations\\SiahMan_Racing4Speed_WebGame\\siah-man-racing\\src\\components\\GamePhysics.tsx": "22", "C:\\Users\\<USER>\\Documents\\Apps_Creations\\SiahMan_Racing4Speed_WebGame\\siah-man-racing\\src\\components\\VehicleModel.tsx": "23", "C:\\Users\\<USER>\\Documents\\Apps_Creations\\SiahMan_Racing4Speed_WebGame\\siah-man-racing\\src\\components\\RaceUI.tsx": "24", "C:\\Users\\<USER>\\Documents\\Apps_Creations\\SiahMan_Racing4Speed_WebGame\\siah-man-racing\\src\\game\\TTSSystem.ts": "25", "C:\\Users\\<USER>\\Documents\\Apps_Creations\\SiahMan_Racing4Speed_WebGame\\siah-man-racing\\src\\game\\AudioSystem.ts": "26", "C:\\Users\\<USER>\\Documents\\Apps_Creations\\SiahMan_Racing4Speed_WebGame\\siah-man-racing\\src\\components\\ArcadeHUD.tsx": "27", "C:\\Users\\<USER>\\Documents\\Apps_Creations\\SiahMan_Racing4Speed_WebGame\\siah-man-racing\\src\\components\\GameStateManager.tsx": "28", "C:\\Users\\<USER>\\Documents\\Apps_Creations\\SiahMan_Racing4Speed_WebGame\\siah-man-racing\\src\\game\\CustomMusicManager.ts": "29", "C:\\Users\\<USER>\\Documents\\Apps_Creations\\SiahMan_Racing4Speed_WebGame\\siah-man-racing\\src\\components\\MusicPlayer.tsx": "30", "C:\\Users\\<USER>\\Documents\\Apps_Creations\\SiahMan_Racing4Speed_WebGame\\siah-man-racing\\src\\components\\ChallengeSelector.tsx": "31", "C:\\Users\\<USER>\\Documents\\Apps_Creations\\SiahMan_Racing4Speed_WebGame\\siah-man-racing\\src\\game\\AssetLoader.ts": "32", "C:\\Users\\<USER>\\Documents\\Apps_Creations\\SiahMan_Racing4Speed_WebGame\\siah-man-racing\\src\\components\\GameModeSelector.tsx": "33"}, {"size": 674, "mtime": 1746744394000, "results": "34", "hashOfConfig": "35"}, {"size": 425, "mtime": 1746742002000, "results": "36", "hashOfConfig": "35"}, {"size": 3981, "mtime": 1751782053113, "results": "37", "hashOfConfig": "35"}, {"size": 419, "mtime": 1746742806000, "results": "38", "hashOfConfig": "35"}, {"size": 234, "mtime": 1747091210000, "results": "39", "hashOfConfig": "35"}, {"size": 1068, "mtime": 1747096302000, "results": "40", "hashOfConfig": "35"}, {"size": 25617, "mtime": 1747019194000, "results": "41", "hashOfConfig": "35"}, {"size": 14716, "mtime": 1751777859239, "results": "42", "hashOfConfig": "35"}, {"size": 7615, "mtime": 1746930686000, "results": "43", "hashOfConfig": "35"}, {"size": 8205, "mtime": 1747019194000, "results": "44", "hashOfConfig": "35"}, {"size": 8296, "mtime": 1746756818000, "results": "45", "hashOfConfig": "35"}, {"size": 11844, "mtime": 1751781992935, "results": "46", "hashOfConfig": "35"}, {"size": 2730, "mtime": 1746998134000, "results": "47", "hashOfConfig": "35"}, {"size": 8854, "mtime": 1747112942000, "results": "48", "hashOfConfig": "35"}, {"size": 8470, "mtime": 1751782703764, "results": "49", "hashOfConfig": "35"}, {"size": 2260, "mtime": 1746756762000, "results": "50", "hashOfConfig": "35"}, {"size": 4910, "mtime": 1751782251607, "results": "51", "hashOfConfig": "35"}, {"size": 22026, "mtime": 1751783234968, "results": "52", "hashOfConfig": "35"}, {"size": 17574, "mtime": 1751782874435, "results": "53", "hashOfConfig": "35"}, {"size": 4283, "mtime": 1751778131284, "results": "54", "hashOfConfig": "35"}, {"size": 49271, "mtime": 1751779033043, "results": "55", "hashOfConfig": "35"}, {"size": 24466, "mtime": 1751782304380, "results": "56", "hashOfConfig": "35"}, {"size": 9340, "mtime": 1747422654000, "results": "57", "hashOfConfig": "35"}, {"size": 13555, "mtime": 1747090368000, "results": "58", "hashOfConfig": "35"}, {"size": 21111, "mtime": 1751781977308, "results": "59", "hashOfConfig": "35"}, {"size": 32327, "mtime": 1751781906103, "results": "60", "hashOfConfig": "35"}, {"size": 10192, "mtime": 1751779047033, "results": "61", "hashOfConfig": "35"}, {"size": 7850, "mtime": 1747112942000, "results": "62", "hashOfConfig": "35"}, {"size": 8697, "mtime": 1746930678000, "results": "63", "hashOfConfig": "35"}, {"size": 12240, "mtime": 1746839070000, "results": "64", "hashOfConfig": "35"}, {"size": 13273, "mtime": 1747096950000, "results": "65", "hashOfConfig": "35"}, {"size": 17390, "mtime": 1751777890397, "results": "66", "hashOfConfig": "35"}, {"size": 9464, "mtime": 1747096984000, "results": "67", "hashOfConfig": "35"}, {"filePath": "68", "messages": "69", "suppressedMessages": "70", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "1c98u4u", {"filePath": "71", "messages": "72", "suppressedMessages": "73", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "74", "messages": "75", "suppressedMessages": "76", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "77", "messages": "78", "suppressedMessages": "79", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "80", "messages": "81", "suppressedMessages": "82", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "83", "messages": "84", "suppressedMessages": "85", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "86", "messages": "87", "suppressedMessages": "88", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "89", "messages": "90", "suppressedMessages": "91", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 19, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "92", "messages": "93", "suppressedMessages": "94", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "95", "messages": "96", "suppressedMessages": "97", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "98", "messages": "99", "suppressedMessages": "100", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "101", "messages": "102", "suppressedMessages": "103", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "104", "messages": "105", "suppressedMessages": "106", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "107", "messages": "108", "suppressedMessages": "109", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "110", "messages": "111", "suppressedMessages": "112", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "113", "messages": "114", "suppressedMessages": "115", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "116", "messages": "117", "suppressedMessages": "118", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "119", "messages": "120", "suppressedMessages": "121", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 4, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "122", "messages": "123", "suppressedMessages": "124", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "125", "messages": "126", "suppressedMessages": "127", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "128", "messages": "129", "suppressedMessages": "130", "errorCount": 1, "fatalErrorCount": 1, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "131", "messages": "132", "suppressedMessages": "133", "errorCount": 1, "fatalErrorCount": 1, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "134", "messages": "135", "suppressedMessages": "136", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 7, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "137", "messages": "138", "suppressedMessages": "139", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 5, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "140", "messages": "141", "suppressedMessages": "142", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "143", "messages": "144", "suppressedMessages": "145", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "146", "messages": "147", "suppressedMessages": "148", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "149", "messages": "150", "suppressedMessages": "151", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 4, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "152", "messages": "153", "suppressedMessages": "154", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "155", "messages": "156", "suppressedMessages": "157", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "158", "messages": "159", "suppressedMessages": "160", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "161", "messages": "162", "suppressedMessages": "163", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "164", "messages": "165", "suppressedMessages": "166", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "C:\\Users\\<USER>\\Documents\\Apps_Creations\\SiahMan_Racing4Speed_WebGame\\siah-man-racing\\src\\index.tsx", [], [], "C:\\Users\\<USER>\\Documents\\Apps_Creations\\SiahMan_Racing4Speed_WebGame\\siah-man-racing\\src\\reportWebVitals.ts", [], [], "C:\\Users\\<USER>\\Documents\\Apps_Creations\\SiahMan_Racing4Speed_WebGame\\siah-man-racing\\src\\App.tsx", [], [], "C:\\Users\\<USER>\\Documents\\Apps_Creations\\SiahMan_Racing4Speed_WebGame\\siah-man-racing\\src\\store\\index.ts", [], [], "C:\\Users\\<USER>\\Documents\\Apps_Creations\\SiahMan_Racing4Speed_WebGame\\siah-man-racing\\src\\GlobalStyle.ts", [], [], "C:\\Users\\<USER>\\Documents\\Apps_Creations\\SiahMan_Racing4Speed_WebGame\\siah-man-racing\\src\\theme.ts", [], [], "C:\\Users\\<USER>\\Documents\\Apps_Creations\\SiahMan_Racing4Speed_WebGame\\siah-man-racing\\src\\pages\\VehicleCustomization.tsx", ["167"], [], "C:\\Users\\<USER>\\Documents\\Apps_Creations\\SiahMan_Racing4Speed_WebGame\\siah-man-racing\\src\\pages\\IntegratedRacing.tsx", ["168", "169", "170", "171", "172", "173", "174", "175", "176", "177", "178", "179", "180", "181", "182", "183", "184", "185", "186"], [], "C:\\Users\\<USER>\\Documents\\Apps_Creations\\SiahMan_Racing4Speed_WebGame\\siah-man-racing\\src\\pages\\Laboratory.tsx", ["187"], [], "C:\\Users\\<USER>\\Documents\\Apps_Creations\\SiahMan_Racing4Speed_WebGame\\siah-man-racing\\src\\pages\\MainMenu.tsx", [], [], "C:\\Users\\<USER>\\Documents\\Apps_Creations\\SiahMan_Racing4Speed_WebGame\\siah-man-racing\\src\\pages\\Settings.tsx", [], [], "C:\\Users\\<USER>\\Documents\\Apps_Creations\\SiahMan_Racing4Speed_WebGame\\siah-man-racing\\src\\pages\\StoryMode.tsx", [], [], "C:\\Users\\<USER>\\Documents\\Apps_Creations\\SiahMan_Racing4Speed_WebGame\\siah-man-racing\\src\\pages\\GameMode.tsx", ["188"], [], "C:\\Users\\<USER>\\Documents\\Apps_Creations\\SiahMan_Racing4Speed_WebGame\\siah-man-racing\\src\\components\\InputController.tsx", ["189"], [], "C:\\Users\\<USER>\\Documents\\Apps_Creations\\SiahMan_Racing4Speed_WebGame\\siah-man-racing\\src\\components\\AudioController.tsx", ["190", "191"], [], "C:\\Users\\<USER>\\Documents\\Apps_Creations\\SiahMan_Racing4Speed_WebGame\\siah-man-racing\\src\\components\\ErrorBoundary.tsx", [], [], "C:\\Users\\<USER>\\Documents\\Apps_Creations\\SiahMan_Racing4Speed_WebGame\\siah-man-racing\\src\\components\\AssetLoader.tsx", ["192"], [], "C:\\Users\\<USER>\\Documents\\Apps_Creations\\SiahMan_Racing4Speed_WebGame\\siah-man-racing\\src\\store\\gameSlice.ts", ["193", "194", "195", "196"], [], "C:\\Users\\<USER>\\Documents\\Apps_Creations\\SiahMan_Racing4Speed_WebGame\\siah-man-racing\\src\\store\\vehicleSlice.ts", [], [], "C:\\Users\\<USER>\\Documents\\Apps_Creations\\SiahMan_Racing4Speed_WebGame\\siah-man-racing\\src\\store\\userSlice.ts", [], [], "C:\\Users\\<USER>\\Documents\\Apps_Creations\\SiahMan_Racing4Speed_WebGame\\siah-man-racing\\src\\components\\RacingEnvironment.tsx", ["197"], [], "C:\\Users\\<USER>\\Documents\\Apps_Creations\\SiahMan_Racing4Speed_WebGame\\siah-man-racing\\src\\components\\GamePhysics.tsx", ["198"], [], "C:\\Users\\<USER>\\Documents\\Apps_Creations\\SiahMan_Racing4Speed_WebGame\\siah-man-racing\\src\\components\\VehicleModel.tsx", ["199", "200", "201", "202", "203", "204", "205"], [], "C:\\Users\\<USER>\\Documents\\Apps_Creations\\SiahMan_Racing4Speed_WebGame\\siah-man-racing\\src\\components\\RaceUI.tsx", ["206", "207", "208", "209", "210"], [], "C:\\Users\\<USER>\\Documents\\Apps_Creations\\SiahMan_Racing4Speed_WebGame\\siah-man-racing\\src\\game\\TTSSystem.ts", ["211"], [], "C:\\Users\\<USER>\\Documents\\Apps_Creations\\SiahMan_Racing4Speed_WebGame\\siah-man-racing\\src\\game\\AudioSystem.ts", [], [], "C:\\Users\\<USER>\\Documents\\Apps_Creations\\SiahMan_Racing4Speed_WebGame\\siah-man-racing\\src\\components\\ArcadeHUD.tsx", [], [], "C:\\Users\\<USER>\\Documents\\Apps_Creations\\SiahMan_Racing4Speed_WebGame\\siah-man-racing\\src\\components\\GameStateManager.tsx", ["212", "213", "214", "215"], [], "C:\\Users\\<USER>\\Documents\\Apps_Creations\\SiahMan_Racing4Speed_WebGame\\siah-man-racing\\src\\game\\CustomMusicManager.ts", [], [], "C:\\Users\\<USER>\\Documents\\Apps_Creations\\SiahMan_Racing4Speed_WebGame\\siah-man-racing\\src\\components\\MusicPlayer.tsx", ["216", "217"], [], "C:\\Users\\<USER>\\Documents\\Apps_Creations\\SiahMan_Racing4Speed_WebGame\\siah-man-racing\\src\\components\\ChallengeSelector.tsx", ["218", "219"], [], "C:\\Users\\<USER>\\Documents\\Apps_Creations\\SiahMan_Racing4Speed_WebGame\\siah-man-racing\\src\\game\\AssetLoader.ts", [], [], "C:\\Users\\<USER>\\Documents\\Apps_Creations\\SiahMan_Racing4Speed_WebGame\\siah-man-racing\\src\\components\\GameModeSelector.tsx", [], [], {"ruleId": "220", "severity": 1, "message": "221", "line": 532, "column": 9, "nodeType": "222", "messageId": "223", "endLine": 532, "endColumn": 29}, {"ruleId": "220", "severity": 1, "message": "224", "line": 8, "column": 3, "nodeType": "222", "messageId": "223", "endLine": 8, "endColumn": 16}, {"ruleId": "220", "severity": 1, "message": "225", "line": 9, "column": 3, "nodeType": "222", "messageId": "223", "endLine": 9, "endColumn": 14}, {"ruleId": "220", "severity": 1, "message": "226", "line": 10, "column": 3, "nodeType": "222", "messageId": "223", "endLine": 10, "endColumn": 16}, {"ruleId": "220", "severity": 1, "message": "227", "line": 13, "column": 10, "nodeType": "222", "messageId": "223", "endLine": 13, "endColumn": 27}, {"ruleId": "220", "severity": 1, "message": "228", "line": 15, "column": 16, "nodeType": "222", "messageId": "223", "endLine": 15, "endColumn": 21}, {"ruleId": "220", "severity": 1, "message": "229", "line": 15, "column": 23, "nodeType": "222", "messageId": "223", "endLine": 15, "endColumn": 30}, {"ruleId": "220", "severity": 1, "message": "230", "line": 16, "column": 10, "nodeType": "222", "messageId": "223", "endLine": 16, "endColumn": 18}, {"ruleId": "220", "severity": 1, "message": "231", "line": 16, "column": 20, "nodeType": "222", "messageId": "223", "endLine": 16, "endColumn": 34}, {"ruleId": "220", "severity": 1, "message": "232", "line": 17, "column": 10, "nodeType": "222", "messageId": "223", "endLine": 17, "endColumn": 19}, {"ruleId": "220", "severity": 1, "message": "233", "line": 18, "column": 10, "nodeType": "222", "messageId": "223", "endLine": 18, "endColumn": 28}, {"ruleId": "220", "severity": 1, "message": "234", "line": 18, "column": 30, "nodeType": "222", "messageId": "223", "endLine": 18, "endColumn": 41}, {"ruleId": "220", "severity": 1, "message": "235", "line": 232, "column": 28, "nodeType": "222", "messageId": "223", "endLine": 232, "endColumn": 38}, {"ruleId": "220", "severity": 1, "message": "236", "line": 232, "column": 40, "nodeType": "222", "messageId": "223", "endLine": 232, "endColumn": 49}, {"ruleId": "220", "severity": 1, "message": "237", "line": 235, "column": 9, "nodeType": "222", "messageId": "223", "endLine": 235, "endColumn": 22}, {"ruleId": "220", "severity": 1, "message": "238", "line": 249, "column": 9, "nodeType": "222", "messageId": "223", "endLine": 249, "endColumn": 22}, {"ruleId": "220", "severity": 1, "message": "239", "line": 250, "column": 9, "nodeType": "222", "messageId": "223", "endLine": 250, "endColumn": 26}, {"ruleId": "220", "severity": 1, "message": "240", "line": 259, "column": 9, "nodeType": "222", "messageId": "223", "endLine": 259, "endColumn": 14}, {"ruleId": "220", "severity": 1, "message": "241", "line": 262, "column": 9, "nodeType": "222", "messageId": "223", "endLine": 262, "endColumn": 16}, {"ruleId": "242", "severity": 1, "message": "243", "line": 293, "column": 19, "nodeType": "222", "endLine": 293, "endColumn": 26}, {"ruleId": "220", "severity": 1, "message": "244", "line": 137, "column": 29, "nodeType": "222", "messageId": "223", "endLine": 137, "endColumn": 49}, {"ruleId": "220", "severity": 1, "message": "245", "line": 9, "column": 21, "nodeType": "222", "messageId": "223", "endLine": 9, "endColumn": 29}, {"ruleId": "220", "severity": 1, "message": "246", "line": 96, "column": 11, "nodeType": "222", "messageId": "223", "endLine": 96, "endColumn": 19}, {"ruleId": "242", "severity": 1, "message": "247", "line": 207, "column": 6, "nodeType": "248", "endLine": 207, "endColumn": 8, "suggestions": "249"}, {"ruleId": "242", "severity": 1, "message": "247", "line": 234, "column": 6, "nodeType": "248", "endLine": 234, "endColumn": 35, "suggestions": "250"}, {"ruleId": "242", "severity": 1, "message": "251", "line": 149, "column": 6, "nodeType": "248", "endLine": 149, "endColumn": 25, "suggestions": "252"}, {"ruleId": "220", "severity": 1, "message": "253", "line": 656, "column": 37, "nodeType": "222", "messageId": "223", "endLine": 656, "endColumn": 41}, {"ruleId": "254", "severity": 1, "message": "255", "line": 737, "column": 5, "nodeType": "256", "messageId": "257", "endLine": 737, "endColumn": 18}, {"ruleId": "254", "severity": 1, "message": "258", "line": 745, "column": 5, "nodeType": "256", "messageId": "257", "endLine": 745, "endColumn": 12}, {"ruleId": "259", "severity": 1, "message": "260", "line": 817, "column": 3, "nodeType": "222", "messageId": "261", "endLine": 817, "endColumn": 16}, {"ruleId": null, "fatal": true, "severity": 2, "message": "262", "line": 749, "column": 2, "nodeType": null}, {"ruleId": null, "fatal": true, "severity": 2, "message": "262", "line": 699, "column": 0, "nodeType": null}, {"ruleId": "220", "severity": 1, "message": "263", "line": 1, "column": 51, "nodeType": "222", "messageId": "223", "endLine": 1, "endColumn": 59}, {"ruleId": "220", "severity": 1, "message": "264", "line": 1, "column": 61, "nodeType": "222", "messageId": "223", "endLine": 1, "endColumn": 72}, {"ruleId": "220", "severity": 1, "message": "229", "line": 5, "column": 23, "nodeType": "222", "messageId": "223", "endLine": 5, "endColumn": 30}, {"ruleId": "220", "severity": 1, "message": "265", "line": 8, "column": 6, "nodeType": "222", "messageId": "223", "endLine": 8, "endColumn": 11}, {"ruleId": "220", "severity": 1, "message": "266", "line": 120, "column": 9, "nodeType": "222", "messageId": "223", "endLine": 120, "endColumn": 18}, {"ruleId": "242", "severity": 1, "message": "267", "line": 123, "column": 15, "nodeType": "256", "endLine": 123, "endColumn": 78}, {"ruleId": "242", "severity": 1, "message": "268", "line": 214, "column": 20, "nodeType": "222", "endLine": 214, "endColumn": 27}, {"ruleId": "220", "severity": 1, "message": "269", "line": 5, "column": 10, "nodeType": "222", "messageId": "223", "endLine": 5, "endColumn": 19}, {"ruleId": "220", "severity": 1, "message": "245", "line": 5, "column": 21, "nodeType": "222", "messageId": "223", "endLine": 5, "endColumn": 29}, {"ruleId": "220", "severity": 1, "message": "270", "line": 5, "column": 31, "nodeType": "222", "messageId": "223", "endLine": 5, "endColumn": 43}, {"ruleId": "242", "severity": 1, "message": "271", "line": 295, "column": 6, "nodeType": "248", "endLine": 295, "endColumn": 48, "suggestions": "272"}, {"ruleId": "242", "severity": 1, "message": "271", "line": 318, "column": 6, "nodeType": "248", "endLine": 318, "endColumn": 27, "suggestions": "273"}, {"ruleId": "220", "severity": 1, "message": "274", "line": 4, "column": 16, "nodeType": "222", "messageId": "223", "endLine": 4, "endColumn": 22}, {"ruleId": "220", "severity": 1, "message": "275", "line": 32, "column": 10, "nodeType": "222", "messageId": "223", "endLine": 32, "endColumn": 19}, {"ruleId": "220", "severity": 1, "message": "276", "line": 38, "column": 10, "nodeType": "222", "messageId": "223", "endLine": 38, "endColumn": 26}, {"ruleId": "242", "severity": 1, "message": "277", "line": 54, "column": 6, "nodeType": "248", "endLine": 54, "endColumn": 22, "suggestions": "278"}, {"ruleId": "242", "severity": 1, "message": "279", "line": 163, "column": 6, "nodeType": "248", "endLine": 163, "endColumn": 79, "suggestions": "280"}, {"ruleId": "242", "severity": 1, "message": "281", "line": 147, "column": 9, "nodeType": "282", "endLine": 158, "endColumn": 4}, {"ruleId": "242", "severity": 1, "message": "283", "line": 161, "column": 9, "nodeType": "282", "endLine": 166, "endColumn": 4}, {"ruleId": "220", "severity": 1, "message": "284", "line": 1, "column": 27, "nodeType": "222", "messageId": "223", "endLine": 1, "endColumn": 36}, {"ruleId": "220", "severity": 1, "message": "285", "line": 205, "column": 9, "nodeType": "222", "messageId": "223", "endLine": 205, "endColumn": 17}, "@typescript-eslint/no-unused-vars", "'handleUpgradeVehicle' is assigned a value but never used.", "Identifier", "unusedVar", "'addExperience' is defined but never used.", "'addCurrency' is defined but never used.", "'unlockAbility' is defined but never used.", "'unlockAchievement' is defined but never used.", "'Track' is defined but never used.", "'Vehicle' is defined but never used.", "'Vector2D' is defined but never used.", "'TrackGenerator' is defined but never used.", "'TTSSystem' is defined but never used.", "'CarWhispererSystem' is defined but never used.", "'AbilityType' is defined but never used.", "'activeRace' is assigned a value but never used.", "'isPlaying' is assigned a value but never used.", "'equippedParts' is assigned a value but never used.", "'controlScheme' is assigned a value but never used.", "'unlockedAbilities' is assigned a value but never used.", "'color' is assigned a value but never used.", "'vehicle' is assigned a value but never used.", "react-hooks/exhaustive-deps", "The ref value 'audioSystem.current' will likely have changed by the time this effect cleanup function runs. If this ref points to a node rendered by React, copy 'audioSystem.current' to a variable inside the effect, and use that variable in the cleanup function.", "'setSelectedEquipment' is assigned a value but never used.", "'GameMode' is defined but never used.", "'controls' is assigned a value but never used.", "React Hook useEffect has a missing dependency: 'audioSystem'. Either include it or remove the dependency array.", "ArrayExpression", ["286"], ["287"], "React Hook useEffect has a missing dependency: 'audioInitialized'. Either include it or remove the dependency array.", ["288"], "'time' is assigned a value but never used.", "no-dupe-keys", "Duplicate key 'setInputState'.", "ObjectExpression", "unexpected", "Duplicate key 'endRace'.", "@typescript-eslint/no-redeclare", "'setInputState' is already defined.", "redeclared", "Parsing error: Declaration or statement expected.", "'useState' is defined but never used.", "'useCallback' is defined but never used.", "'Color' is defined but never used.", "'materials' is assigned a value but never used.", "Assignments to the 'color' variable from inside React Hook useMemo will be lost after each render. To preserve the value over time, store it in a useRef Hook and keep the mutable value in the '.current' property. Otherwise, you can move this variable directly inside useMemo.", "The ref value 'groupRef.current' will likely have changed by the time this effect cleanup function runs. If this ref points to a node rendered by React, copy 'groupRef.current' to a variable inside the effect, and use that variable in the cleanup function.", "'RaceState' is defined but never used.", "'GameSettings' is defined but never used.", "React Hook useEffect has a missing dependency: 'raceState'. Either include it or remove the dependency array.", ["289"], ["290"], "'Howler' is defined but never used.", "'raceTimer' is assigned a value but never used.", "'countdownStarted' is assigned a value but never used.", "React Hook useEffect has a missing dependency: 'startCountdown'. Either include it or remove the dependency array.", ["291"], "React Hook useEffect has missing dependencies: 'audioSystem', 'bestLapTime', 'completeRace', 'currentLapStartTime', 'dispatch', and 'raceState.currentLap'. Either include them or remove the dependency array.", ["292"], "The 'updateProgress' function makes the dependencies of useEffect Hook (at line 191) change on every render. Move it inside the useEffect callback. Alternatively, wrap the definition of 'updateProgress' in its own useCallback() Hook.", "VariableDeclarator", "The 'handleTrackEnd' function makes the dependencies of useEffect Hook (at line 191) change on every render. Move it inside the useEffect callback. Alternatively, wrap the definition of 'handleTrackEnd' in its own useCallback() Hook.", "'useEffect' is defined but never used.", "'dispatch' is assigned a value but never used.", {"desc": "293", "fix": "294"}, {"desc": "295", "fix": "296"}, {"desc": "297", "fix": "298"}, {"desc": "299", "fix": "300"}, {"desc": "301", "fix": "302"}, {"desc": "303", "fix": "304"}, {"desc": "305", "fix": "306"}, "Update the dependencies array to be: [audioSystem]", {"range": "307", "text": "308"}, "Update the dependencies array to be: [audioSystem, currentScreen, isMusicMuted]", {"range": "309", "text": "310"}, "Update the dependencies array to be: [audioInitialized, onLoadingComplete]", {"range": "311", "text": "312"}, "Update the dependencies array to be: [raceState.checkpoints, lastCheckpointId, raceState]", {"range": "313", "text": "314"}, "Update the dependencies array to be: [raceState, raceState.raceTime]", {"range": "315", "text": "316"}, "Update the dependencies array to be: [currentTrackId, startCountdown]", {"range": "317", "text": "318"}, "Update the dependencies array to be: [vehicleState, nextCheckpointIndex, checkpoints, raceState.isRaceStarted, raceState.currentLap, audioSystem, currentLapStartTime, bestLapTime, dispatch, completeRace]", {"range": "319", "text": "320"}, [5803, 5805], "[audioSystem]", [6557, 6586], "[audioSystem, currentScreen, isMusicMuted]", [4184, 4203], "[audioInitialized, onLoadingComplete]", [7342, 7384], "[raceState.checkpoints, lastCheckpointId, raceState]", [8002, 8023], "[raceState, raceState.raceTime]", [1703, 1719], "[currentTrackId, startCountdown]", [4947, 5020], "[vehicleState, nextCheckpointIndex, checkpoints, raceState.isRaceStarted, raceState.currentLap, audioSystem, currentLapStartTime, bestLapTime, dispatch, completeRace]"]