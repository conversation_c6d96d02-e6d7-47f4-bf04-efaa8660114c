{"ast": null, "code": "import { <PERSON><PERSON>, <PERSON><PERSON>oa<PERSON>, <PERSON>ector3, Matrix4 } from \"three\";\nimport { gunzipSync } from \"fflate\";\nimport { Volume } from \"../misc/Volume.js\";\nclass NRRDLoader extends Loader {\n  constructor(manager) {\n    super(manager);\n  }\n  load(url, onLoad, onProgress, onError) {\n    const scope = this;\n    const loader = new FileLoader(scope.manager);\n    loader.setPath(scope.path);\n    loader.setResponseType(\"arraybuffer\");\n    loader.setRequestHeader(scope.requestHeader);\n    loader.setWithCredentials(scope.withCredentials);\n    loader.load(url, function (data) {\n      try {\n        onLoad(scope.parse(data));\n      } catch (e) {\n        if (onError) {\n          onError(e);\n        } else {\n          console.error(e);\n        }\n        scope.manager.itemError(url);\n      }\n    }, onProgress, onError);\n  }\n  parse(data) {\n    let _data = data;\n    let _dataPointer = 0;\n    const _nativeLittleEndian = new Int8Array(new Int16Array([1]).buffer)[0] > 0;\n    const _littleEndian = true;\n    const headerObject = {};\n    function scan(type, chunks) {\n      if (chunks === void 0 || chunks === null) {\n        chunks = 1;\n      }\n      let _chunkSize = 1;\n      let _array_type = Uint8Array;\n      switch (type) {\n        case \"uchar\":\n          break;\n        case \"schar\":\n          _array_type = Int8Array;\n          break;\n        case \"ushort\":\n          _array_type = Uint16Array;\n          _chunkSize = 2;\n          break;\n        case \"sshort\":\n          _array_type = Int16Array;\n          _chunkSize = 2;\n          break;\n        case \"uint\":\n          _array_type = Uint32Array;\n          _chunkSize = 4;\n          break;\n        case \"sint\":\n          _array_type = Int32Array;\n          _chunkSize = 4;\n          break;\n        case \"float\":\n          _array_type = Float32Array;\n          _chunkSize = 4;\n          break;\n        case \"complex\":\n          _array_type = Float64Array;\n          _chunkSize = 8;\n          break;\n        case \"double\":\n          _array_type = Float64Array;\n          _chunkSize = 8;\n          break;\n      }\n      let _bytes2 = new _array_type(_data.slice(_dataPointer, _dataPointer += chunks * _chunkSize));\n      if (_nativeLittleEndian != _littleEndian) {\n        _bytes2 = flipEndianness(_bytes2, _chunkSize);\n      }\n      if (chunks == 1) {\n        return _bytes2[0];\n      }\n      return _bytes2;\n    }\n    function flipEndianness(array, chunkSize) {\n      const u8 = new Uint8Array(array.buffer, array.byteOffset, array.byteLength);\n      for (let i2 = 0; i2 < array.byteLength; i2 += chunkSize) {\n        for (let j = i2 + chunkSize - 1, k = i2; j > k; j--, k++) {\n          const tmp = u8[k];\n          u8[k] = u8[j];\n          u8[j] = tmp;\n        }\n      }\n      return array;\n    }\n    function parseHeader(header) {\n      let data2, field, fn, i2, l, m, _i, _len;\n      const lines = header.split(/\\r?\\n/);\n      for (_i = 0, _len = lines.length; _i < _len; _i++) {\n        l = lines[_i];\n        if (l.match(/NRRD\\d+/)) {\n          headerObject.isNrrd = true;\n        } else if (l.match(/^#/)) ;else if (m = l.match(/(.*):(.*)/)) {\n          field = m[1].trim();\n          data2 = m[2].trim();\n          fn = _fieldFunctions[field];\n          if (fn) {\n            fn.call(headerObject, data2);\n          } else {\n            headerObject[field] = data2;\n          }\n        }\n      }\n      if (!headerObject.isNrrd) {\n        throw new Error(\"Not an NRRD file\");\n      }\n      if (headerObject.encoding === \"bz2\" || headerObject.encoding === \"bzip2\") {\n        throw new Error(\"Bzip is not supported\");\n      }\n      if (!headerObject.vectors) {\n        headerObject.vectors = [new Vector3(1, 0, 0), new Vector3(0, 1, 0), new Vector3(0, 0, 1)];\n        if (headerObject.spacings) {\n          for (i2 = 0; i2 <= 2; i2++) {\n            if (!isNaN(headerObject.spacings[i2])) {\n              headerObject.vectors[i2].multiplyScalar(headerObject.spacings[i2]);\n            }\n          }\n        }\n      }\n    }\n    function parseDataAsText(data2, start, end) {\n      let number = \"\";\n      start = start || 0;\n      end = end || data2.length;\n      let value;\n      const lengthOfTheResult = headerObject.sizes.reduce(function (previous, current) {\n        return previous * current;\n      }, 1);\n      let base = 10;\n      if (headerObject.encoding === \"hex\") {\n        base = 16;\n      }\n      const result = new headerObject.__array(lengthOfTheResult);\n      let resultIndex = 0;\n      let parsingFunction = parseInt;\n      if (headerObject.__array === Float32Array || headerObject.__array === Float64Array) {\n        parsingFunction = parseFloat;\n      }\n      for (let i2 = start; i2 < end; i2++) {\n        value = data2[i2];\n        if ((value < 9 || value > 13) && value !== 32) {\n          number += String.fromCharCode(value);\n        } else {\n          if (number !== \"\") {\n            result[resultIndex] = parsingFunction(number, base);\n            resultIndex++;\n          }\n          number = \"\";\n        }\n      }\n      if (number !== \"\") {\n        result[resultIndex] = parsingFunction(number, base);\n        resultIndex++;\n      }\n      return result;\n    }\n    const _bytes = scan(\"uchar\", data.byteLength);\n    const _length = _bytes.length;\n    let _header = null;\n    let _data_start = 0;\n    let i;\n    for (i = 1; i < _length; i++) {\n      if (_bytes[i - 1] == 10 && _bytes[i] == 10) {\n        _header = this.parseChars(_bytes, 0, i - 2);\n        _data_start = i + 1;\n        break;\n      }\n    }\n    parseHeader(_header);\n    _data = _bytes.subarray(_data_start);\n    if (headerObject.encoding.substring(0, 2) === \"gz\") {\n      _data = gunzipSync(new Uint8Array(_data));\n    } else if (headerObject.encoding === \"ascii\" || headerObject.encoding === \"text\" || headerObject.encoding === \"txt\" || headerObject.encoding === \"hex\") {\n      _data = parseDataAsText(_data);\n    } else if (headerObject.encoding === \"raw\") {\n      const _copy = new Uint8Array(_data.length);\n      for (let i2 = 0; i2 < _data.length; i2++) {\n        _copy[i2] = _data[i2];\n      }\n      _data = _copy;\n    }\n    _data = _data.buffer;\n    const volume = new Volume();\n    volume.header = headerObject;\n    volume.data = new headerObject.__array(_data);\n    const min_max = volume.computeMinMax();\n    const min = min_max[0];\n    const max = min_max[1];\n    volume.windowLow = min;\n    volume.windowHigh = max;\n    volume.dimensions = [headerObject.sizes[0], headerObject.sizes[1], headerObject.sizes[2]];\n    volume.xLength = volume.dimensions[0];\n    volume.yLength = volume.dimensions[1];\n    volume.zLength = volume.dimensions[2];\n    const spacingX = new Vector3(headerObject.vectors[0][0], headerObject.vectors[0][1], headerObject.vectors[0][2]).length();\n    const spacingY = new Vector3(headerObject.vectors[1][0], headerObject.vectors[1][1], headerObject.vectors[1][2]).length();\n    const spacingZ = new Vector3(headerObject.vectors[2][0], headerObject.vectors[2][1], headerObject.vectors[2][2]).length();\n    volume.spacing = [spacingX, spacingY, spacingZ];\n    volume.matrix = new Matrix4();\n    let _spaceX = 1;\n    let _spaceY = 1;\n    const _spaceZ = 1;\n    if (headerObject.space == \"left-posterior-superior\") {\n      _spaceX = -1;\n      _spaceY = -1;\n    } else if (headerObject.space === \"left-anterior-superior\") {\n      _spaceX = -1;\n    }\n    if (!headerObject.vectors) {\n      volume.matrix.set(_spaceX, 0, 0, 0, 0, _spaceY, 0, 0, 0, 0, _spaceZ, 0, 0, 0, 0, 1);\n    } else {\n      const v = headerObject.vectors;\n      volume.matrix.set(_spaceX * v[0][0], _spaceX * v[1][0], _spaceX * v[2][0], 0, _spaceY * v[0][1], _spaceY * v[1][1], _spaceY * v[2][1], 0, _spaceZ * v[0][2], _spaceZ * v[1][2], _spaceZ * v[2][2], 0, 0, 0, 0, 1);\n    }\n    volume.inverseMatrix = new Matrix4();\n    volume.inverseMatrix.copy(volume.matrix).invert();\n    volume.RASDimensions = new Vector3(volume.xLength, volume.yLength, volume.zLength).applyMatrix4(volume.matrix).round().toArray().map(Math.abs);\n    if (volume.lowerThreshold === -Infinity) {\n      volume.lowerThreshold = min;\n    }\n    if (volume.upperThreshold === Infinity) {\n      volume.upperThreshold = max;\n    }\n    return volume;\n  }\n  parseChars(array, start, end) {\n    if (start === void 0) {\n      start = 0;\n    }\n    if (end === void 0) {\n      end = array.length;\n    }\n    let output = \"\";\n    let i = 0;\n    for (i = start; i < end; ++i) {\n      output += String.fromCharCode(array[i]);\n    }\n    return output;\n  }\n}\nconst _fieldFunctions = {\n  type: function (data) {\n    switch (data) {\n      case \"uchar\":\n      case \"unsigned char\":\n      case \"uint8\":\n      case \"uint8_t\":\n        this.__array = Uint8Array;\n        break;\n      case \"signed char\":\n      case \"int8\":\n      case \"int8_t\":\n        this.__array = Int8Array;\n        break;\n      case \"short\":\n      case \"short int\":\n      case \"signed short\":\n      case \"signed short int\":\n      case \"int16\":\n      case \"int16_t\":\n        this.__array = Int16Array;\n        break;\n      case \"ushort\":\n      case \"unsigned short\":\n      case \"unsigned short int\":\n      case \"uint16\":\n      case \"uint16_t\":\n        this.__array = Uint16Array;\n        break;\n      case \"int\":\n      case \"signed int\":\n      case \"int32\":\n      case \"int32_t\":\n        this.__array = Int32Array;\n        break;\n      case \"uint\":\n      case \"unsigned int\":\n      case \"uint32\":\n      case \"uint32_t\":\n        this.__array = Uint32Array;\n        break;\n      case \"float\":\n        this.__array = Float32Array;\n        break;\n      case \"double\":\n        this.__array = Float64Array;\n        break;\n      default:\n        throw new Error(\"Unsupported NRRD data type: \" + data);\n    }\n    return this.type = data;\n  },\n  endian: function (data) {\n    return this.endian = data;\n  },\n  encoding: function (data) {\n    return this.encoding = data;\n  },\n  dimension: function (data) {\n    return this.dim = parseInt(data, 10);\n  },\n  sizes: function (data) {\n    let i;\n    return this.sizes = function () {\n      const _ref = data.split(/\\s+/);\n      const _results = [];\n      for (let _i = 0, _len = _ref.length; _i < _len; _i++) {\n        i = _ref[_i];\n        _results.push(parseInt(i, 10));\n      }\n      return _results;\n    }();\n  },\n  space: function (data) {\n    return this.space = data;\n  },\n  \"space origin\": function (data) {\n    return this.space_origin = data.split(\"(\")[1].split(\")\")[0].split(\",\");\n  },\n  \"space directions\": function (data) {\n    let f, v;\n    const parts = data.match(/\\(.*?\\)/g);\n    return this.vectors = function () {\n      const _results = [];\n      for (let _i = 0, _len = parts.length; _i < _len; _i++) {\n        v = parts[_i];\n        _results.push(function () {\n          const _ref = v.slice(1, -1).split(/,/);\n          const _results2 = [];\n          for (let _j = 0, _len2 = _ref.length; _j < _len2; _j++) {\n            f = _ref[_j];\n            _results2.push(parseFloat(f));\n          }\n          return _results2;\n        }());\n      }\n      return _results;\n    }();\n  },\n  spacings: function (data) {\n    let f;\n    const parts = data.split(/\\s+/);\n    return this.spacings = function () {\n      const _results = [];\n      for (let _i = 0, _len = parts.length; _i < _len; _i++) {\n        f = parts[_i];\n        _results.push(parseFloat(f));\n      }\n      return _results;\n    }();\n  }\n};\nexport { NRRDLoader };", "map": {"version": 3, "names": ["NRRDLoader", "Loader", "constructor", "manager", "load", "url", "onLoad", "onProgress", "onError", "scope", "loader", "<PERSON><PERSON><PERSON><PERSON>", "set<PERSON>ath", "path", "setResponseType", "setRequestHeader", "requestHeader", "setWithCredentials", "withCredentials", "data", "parse", "e", "console", "error", "itemError", "_data", "_dataPointer", "_nativeLittleEndian", "Int8Array", "Int16Array", "buffer", "_<PERSON><PERSON><PERSON><PERSON>", "headerObject", "scan", "type", "chunks", "_chunkSize", "_array_type", "Uint8Array", "Uint16Array", "Uint32Array", "Int32Array", "Float32Array", "Float64Array", "_bytes2", "slice", "flipEndianness", "array", "chunkSize", "u8", "byteOffset", "byteLength", "i2", "j", "k", "tmp", "parse<PERSON><PERSON><PERSON>", "header", "data2", "field", "fn", "l", "m", "_i", "_len", "lines", "split", "length", "match", "isNrrd", "trim", "_fieldFunctions", "call", "Error", "encoding", "vectors", "Vector3", "spacings", "isNaN", "multiplyScalar", "parseDataAsText", "start", "end", "number", "value", "lengthOfTheResult", "sizes", "reduce", "previous", "current", "base", "result", "__array", "resultIndex", "parsingFunction", "parseInt", "parseFloat", "String", "fromCharCode", "_bytes", "_length", "_header", "_data_start", "i", "parseChars", "subarray", "substring", "gunzipSync", "_copy", "volume", "Volume", "min_max", "computeMinMax", "min", "max", "windowLow", "windowHigh", "dimensions", "xLength", "y<PERSON><PERSON><PERSON>", "z<PERSON>ength", "spacingX", "spacingY", "spacingZ", "spacing", "matrix", "Matrix4", "_spaceX", "_spaceY", "_spaceZ", "space", "set", "v", "inverseMatrix", "copy", "invert", "RASDimensions", "applyMatrix4", "round", "toArray", "map", "Math", "abs", "lowerThreshold", "Infinity", "upperThreshold", "output", "endian", "dimension", "dim", "_ref", "_results", "push", "space origin", "space_origin", "space directions", "f", "parts", "_results2", "_j", "_len2"], "sources": ["C:\\Users\\<USER>\\Documents\\Apps_Creations\\SiahMan_Racing4Speed_WebGame\\siah-man-racing\\node_modules\\src\\loaders\\NRRDLoader.js"], "sourcesContent": ["import { <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON>, Vector3 } from 'three'\nimport { gunzipSync } from 'fflate'\nimport { Volume } from '../misc/Volume'\n\nclass NRRDLoader extends Loader {\n  constructor(manager) {\n    super(manager)\n  }\n\n  load(url, onLoad, onProgress, onError) {\n    const scope = this\n\n    const loader = new FileLoader(scope.manager)\n    loader.setPath(scope.path)\n    loader.setResponseType('arraybuffer')\n    loader.setRequestHeader(scope.requestHeader)\n    loader.setWithCredentials(scope.withCredentials)\n    loader.load(\n      url,\n      function (data) {\n        try {\n          onLoad(scope.parse(data))\n        } catch (e) {\n          if (onError) {\n            onError(e)\n          } else {\n            console.error(e)\n          }\n\n          scope.manager.itemError(url)\n        }\n      },\n      onProgress,\n      onError,\n    )\n  }\n\n  parse(data) {\n    // this parser is largely inspired from the XTK NRRD parser : https://github.com/xtk/X\n\n    let _data = data\n\n    let _dataPointer = 0\n\n    const _nativeLittleEndian = new Int8Array(new Int16Array([1]).buffer)[0] > 0\n\n    const _littleEndian = true\n\n    const headerObject = {}\n\n    function scan(type, chunks) {\n      if (chunks === undefined || chunks === null) {\n        chunks = 1\n      }\n\n      let _chunkSize = 1\n      let _array_type = Uint8Array\n\n      switch (type) {\n        // 1 byte data types\n        case 'uchar':\n          break\n        case 'schar':\n          _array_type = Int8Array\n          break\n        // 2 byte data types\n        case 'ushort':\n          _array_type = Uint16Array\n          _chunkSize = 2\n          break\n        case 'sshort':\n          _array_type = Int16Array\n          _chunkSize = 2\n          break\n        // 4 byte data types\n        case 'uint':\n          _array_type = Uint32Array\n          _chunkSize = 4\n          break\n        case 'sint':\n          _array_type = Int32Array\n          _chunkSize = 4\n          break\n        case 'float':\n          _array_type = Float32Array\n          _chunkSize = 4\n          break\n        case 'complex':\n          _array_type = Float64Array\n          _chunkSize = 8\n          break\n        case 'double':\n          _array_type = Float64Array\n          _chunkSize = 8\n          break\n      }\n\n      // increase the data pointer in-place\n      let _bytes = new _array_type(_data.slice(_dataPointer, (_dataPointer += chunks * _chunkSize)))\n\n      // if required, flip the endianness of the bytes\n      if (_nativeLittleEndian != _littleEndian) {\n        // we need to flip here since the format doesn't match the native endianness\n        _bytes = flipEndianness(_bytes, _chunkSize)\n      }\n\n      if (chunks == 1) {\n        // if only one chunk was requested, just return one value\n        return _bytes[0]\n      }\n\n      // return the byte array\n      return _bytes\n    }\n\n    //Flips typed array endianness in-place. Based on https://github.com/kig/DataStream.js/blob/master/DataStream.js.\n\n    function flipEndianness(array, chunkSize) {\n      const u8 = new Uint8Array(array.buffer, array.byteOffset, array.byteLength)\n      for (let i = 0; i < array.byteLength; i += chunkSize) {\n        for (let j = i + chunkSize - 1, k = i; j > k; j--, k++) {\n          const tmp = u8[k]\n          u8[k] = u8[j]\n          u8[j] = tmp\n        }\n      }\n\n      return array\n    }\n\n    //parse the header\n    function parseHeader(header) {\n      let data, field, fn, i, l, m, _i, _len\n      const lines = header.split(/\\r?\\n/)\n      for (_i = 0, _len = lines.length; _i < _len; _i++) {\n        l = lines[_i]\n        if (l.match(/NRRD\\d+/)) {\n          headerObject.isNrrd = true\n        } else if (l.match(/^#/)) {\n        } else if ((m = l.match(/(.*):(.*)/))) {\n          field = m[1].trim()\n          data = m[2].trim()\n          fn = _fieldFunctions[field]\n          if (fn) {\n            fn.call(headerObject, data)\n          } else {\n            headerObject[field] = data\n          }\n        }\n      }\n\n      if (!headerObject.isNrrd) {\n        throw new Error('Not an NRRD file')\n      }\n\n      if (headerObject.encoding === 'bz2' || headerObject.encoding === 'bzip2') {\n        throw new Error('Bzip is not supported')\n      }\n\n      if (!headerObject.vectors) {\n        //if no space direction is set, let's use the identity\n        headerObject.vectors = [new Vector3(1, 0, 0), new Vector3(0, 1, 0), new Vector3(0, 0, 1)]\n        //apply spacing if defined\n        if (headerObject.spacings) {\n          for (i = 0; i <= 2; i++) {\n            if (!isNaN(headerObject.spacings[i])) {\n              headerObject.vectors[i].multiplyScalar(headerObject.spacings[i])\n            }\n          }\n        }\n      }\n    }\n\n    //parse the data when registred as one of this type : 'text', 'ascii', 'txt'\n    function parseDataAsText(data, start, end) {\n      let number = ''\n      start = start || 0\n      end = end || data.length\n      let value\n      //length of the result is the product of the sizes\n      const lengthOfTheResult = headerObject.sizes.reduce(function (previous, current) {\n        return previous * current\n      }, 1)\n\n      let base = 10\n      if (headerObject.encoding === 'hex') {\n        base = 16\n      }\n\n      const result = new headerObject.__array(lengthOfTheResult)\n      let resultIndex = 0\n      let parsingFunction = parseInt\n      if (headerObject.__array === Float32Array || headerObject.__array === Float64Array) {\n        parsingFunction = parseFloat\n      }\n\n      for (let i = start; i < end; i++) {\n        value = data[i]\n        //if value is not a space\n        if ((value < 9 || value > 13) && value !== 32) {\n          number += String.fromCharCode(value)\n        } else {\n          if (number !== '') {\n            result[resultIndex] = parsingFunction(number, base)\n            resultIndex++\n          }\n\n          number = ''\n        }\n      }\n\n      if (number !== '') {\n        result[resultIndex] = parsingFunction(number, base)\n        resultIndex++\n      }\n\n      return result\n    }\n\n    const _bytes = scan('uchar', data.byteLength)\n    const _length = _bytes.length\n    let _header = null\n    let _data_start = 0\n    let i\n    for (i = 1; i < _length; i++) {\n      if (_bytes[i - 1] == 10 && _bytes[i] == 10) {\n        // we found two line breaks in a row\n        // now we know what the header is\n        _header = this.parseChars(_bytes, 0, i - 2)\n        // this is were the data starts\n        _data_start = i + 1\n        break\n      }\n    }\n\n    // parse the header\n    parseHeader(_header)\n\n    _data = _bytes.subarray(_data_start) // the data without header\n    if (headerObject.encoding.substring(0, 2) === 'gz') {\n      // we need to decompress the datastream\n      // here we start the unzipping and get a typed Uint8Array back\n      _data = gunzipSync(new Uint8Array(_data))\n    } else if (\n      headerObject.encoding === 'ascii' ||\n      headerObject.encoding === 'text' ||\n      headerObject.encoding === 'txt' ||\n      headerObject.encoding === 'hex'\n    ) {\n      _data = parseDataAsText(_data)\n    } else if (headerObject.encoding === 'raw') {\n      //we need to copy the array to create a new array buffer, else we retrieve the original arraybuffer with the header\n      const _copy = new Uint8Array(_data.length)\n\n      for (let i = 0; i < _data.length; i++) {\n        _copy[i] = _data[i]\n      }\n\n      _data = _copy\n    }\n\n    // .. let's use the underlying array buffer\n    _data = _data.buffer\n\n    const volume = new Volume()\n    volume.header = headerObject\n    //\n    // parse the (unzipped) data to a datastream of the correct type\n    //\n    volume.data = new headerObject.__array(_data)\n    // get the min and max intensities\n    const min_max = volume.computeMinMax()\n    const min = min_max[0]\n    const max = min_max[1]\n    // attach the scalar range to the volume\n    volume.windowLow = min\n    volume.windowHigh = max\n\n    // get the image dimensions\n    volume.dimensions = [headerObject.sizes[0], headerObject.sizes[1], headerObject.sizes[2]]\n    volume.xLength = volume.dimensions[0]\n    volume.yLength = volume.dimensions[1]\n    volume.zLength = volume.dimensions[2]\n    // spacing\n    const spacingX = new Vector3(\n      headerObject.vectors[0][0],\n      headerObject.vectors[0][1],\n      headerObject.vectors[0][2],\n    ).length()\n    const spacingY = new Vector3(\n      headerObject.vectors[1][0],\n      headerObject.vectors[1][1],\n      headerObject.vectors[1][2],\n    ).length()\n    const spacingZ = new Vector3(\n      headerObject.vectors[2][0],\n      headerObject.vectors[2][1],\n      headerObject.vectors[2][2],\n    ).length()\n    volume.spacing = [spacingX, spacingY, spacingZ]\n\n    // Create IJKtoRAS matrix\n    volume.matrix = new Matrix4()\n\n    let _spaceX = 1\n    let _spaceY = 1\n    const _spaceZ = 1\n\n    if (headerObject.space == 'left-posterior-superior') {\n      _spaceX = -1\n      _spaceY = -1\n    } else if (headerObject.space === 'left-anterior-superior') {\n      _spaceX = -1\n    }\n\n    if (!headerObject.vectors) {\n      volume.matrix.set(_spaceX, 0, 0, 0, 0, _spaceY, 0, 0, 0, 0, _spaceZ, 0, 0, 0, 0, 1)\n    } else {\n      const v = headerObject.vectors\n\n      volume.matrix.set(\n        _spaceX * v[0][0],\n        _spaceX * v[1][0],\n        _spaceX * v[2][0],\n        0,\n        _spaceY * v[0][1],\n        _spaceY * v[1][1],\n        _spaceY * v[2][1],\n        0,\n        _spaceZ * v[0][2],\n        _spaceZ * v[1][2],\n        _spaceZ * v[2][2],\n        0,\n        0,\n        0,\n        0,\n        1,\n      )\n    }\n\n    volume.inverseMatrix = new Matrix4()\n    volume.inverseMatrix.copy(volume.matrix).invert()\n    volume.RASDimensions = new Vector3(volume.xLength, volume.yLength, volume.zLength)\n      .applyMatrix4(volume.matrix)\n      .round()\n      .toArray()\n      .map(Math.abs)\n\n    // .. and set the default threshold\n    // only if the threshold was not already set\n    if (volume.lowerThreshold === -Infinity) {\n      volume.lowerThreshold = min\n    }\n\n    if (volume.upperThreshold === Infinity) {\n      volume.upperThreshold = max\n    }\n\n    return volume\n  }\n\n  parseChars(array, start, end) {\n    // without borders, use the whole array\n    if (start === undefined) {\n      start = 0\n    }\n\n    if (end === undefined) {\n      end = array.length\n    }\n\n    let output = ''\n    // create and append the chars\n    let i = 0\n    for (i = start; i < end; ++i) {\n      output += String.fromCharCode(array[i])\n    }\n\n    return output\n  }\n}\n\nconst _fieldFunctions = {\n  type: function (data) {\n    switch (data) {\n      case 'uchar':\n      case 'unsigned char':\n      case 'uint8':\n      case 'uint8_t':\n        this.__array = Uint8Array\n        break\n      case 'signed char':\n      case 'int8':\n      case 'int8_t':\n        this.__array = Int8Array\n        break\n      case 'short':\n      case 'short int':\n      case 'signed short':\n      case 'signed short int':\n      case 'int16':\n      case 'int16_t':\n        this.__array = Int16Array\n        break\n      case 'ushort':\n      case 'unsigned short':\n      case 'unsigned short int':\n      case 'uint16':\n      case 'uint16_t':\n        this.__array = Uint16Array\n        break\n      case 'int':\n      case 'signed int':\n      case 'int32':\n      case 'int32_t':\n        this.__array = Int32Array\n        break\n      case 'uint':\n      case 'unsigned int':\n      case 'uint32':\n      case 'uint32_t':\n        this.__array = Uint32Array\n        break\n      case 'float':\n        this.__array = Float32Array\n        break\n      case 'double':\n        this.__array = Float64Array\n        break\n      default:\n        throw new Error('Unsupported NRRD data type: ' + data)\n    }\n\n    return (this.type = data)\n  },\n\n  endian: function (data) {\n    return (this.endian = data)\n  },\n\n  encoding: function (data) {\n    return (this.encoding = data)\n  },\n\n  dimension: function (data) {\n    return (this.dim = parseInt(data, 10))\n  },\n\n  sizes: function (data) {\n    let i\n    return (this.sizes = (function () {\n      const _ref = data.split(/\\s+/)\n      const _results = []\n\n      for (let _i = 0, _len = _ref.length; _i < _len; _i++) {\n        i = _ref[_i]\n        _results.push(parseInt(i, 10))\n      }\n\n      return _results\n    })())\n  },\n\n  space: function (data) {\n    return (this.space = data)\n  },\n\n  'space origin': function (data) {\n    return (this.space_origin = data.split('(')[1].split(')')[0].split(','))\n  },\n\n  'space directions': function (data) {\n    let f, v\n    const parts = data.match(/\\(.*?\\)/g)\n    return (this.vectors = (function () {\n      const _results = []\n\n      for (let _i = 0, _len = parts.length; _i < _len; _i++) {\n        v = parts[_i]\n        _results.push(\n          (function () {\n            const _ref = v.slice(1, -1).split(/,/)\n            const _results2 = []\n\n            for (let _j = 0, _len2 = _ref.length; _j < _len2; _j++) {\n              f = _ref[_j]\n              _results2.push(parseFloat(f))\n            }\n\n            return _results2\n          })(),\n        )\n      }\n\n      return _results\n    })())\n  },\n\n  spacings: function (data) {\n    let f\n    const parts = data.split(/\\s+/)\n    return (this.spacings = (function () {\n      const _results = []\n\n      for (let _i = 0, _len = parts.length; _i < _len; _i++) {\n        f = parts[_i]\n        _results.push(parseFloat(f))\n      }\n\n      return _results\n    })())\n  },\n}\n\nexport { NRRDLoader }\n"], "mappings": ";;;AAIA,MAAMA,UAAA,SAAmBC,MAAA,CAAO;EAC9BC,YAAYC,OAAA,EAAS;IACnB,MAAMA,OAAO;EACd;EAEDC,KAAKC,GAAA,EAAKC,MAAA,EAAQC,UAAA,EAAYC,OAAA,EAAS;IACrC,MAAMC,KAAA,GAAQ;IAEd,MAAMC,MAAA,GAAS,IAAIC,UAAA,CAAWF,KAAA,CAAMN,OAAO;IAC3CO,MAAA,CAAOE,OAAA,CAAQH,KAAA,CAAMI,IAAI;IACzBH,MAAA,CAAOI,eAAA,CAAgB,aAAa;IACpCJ,MAAA,CAAOK,gBAAA,CAAiBN,KAAA,CAAMO,aAAa;IAC3CN,MAAA,CAAOO,kBAAA,CAAmBR,KAAA,CAAMS,eAAe;IAC/CR,MAAA,CAAON,IAAA,CACLC,GAAA,EACA,UAAUc,IAAA,EAAM;MACd,IAAI;QACFb,MAAA,CAAOG,KAAA,CAAMW,KAAA,CAAMD,IAAI,CAAC;MACzB,SAAQE,CAAA,EAAP;QACA,IAAIb,OAAA,EAAS;UACXA,OAAA,CAAQa,CAAC;QACrB,OAAiB;UACLC,OAAA,CAAQC,KAAA,CAAMF,CAAC;QAChB;QAEDZ,KAAA,CAAMN,OAAA,CAAQqB,SAAA,CAAUnB,GAAG;MAC5B;IACF,GACDE,UAAA,EACAC,OACD;EACF;EAEDY,MAAMD,IAAA,EAAM;IAGV,IAAIM,KAAA,GAAQN,IAAA;IAEZ,IAAIO,YAAA,GAAe;IAEnB,MAAMC,mBAAA,GAAsB,IAAIC,SAAA,CAAU,IAAIC,UAAA,CAAW,CAAC,CAAC,CAAC,EAAEC,MAAM,EAAE,CAAC,IAAI;IAE3E,MAAMC,aAAA,GAAgB;IAEtB,MAAMC,YAAA,GAAe,CAAE;IAEvB,SAASC,KAAKC,IAAA,EAAMC,MAAA,EAAQ;MAC1B,IAAIA,MAAA,KAAW,UAAaA,MAAA,KAAW,MAAM;QAC3CA,MAAA,GAAS;MACV;MAED,IAAIC,UAAA,GAAa;MACjB,IAAIC,WAAA,GAAcC,UAAA;MAElB,QAAQJ,IAAA;QAEN,KAAK;UACH;QACF,KAAK;UACHG,WAAA,GAAcT,SAAA;UACd;QAEF,KAAK;UACHS,WAAA,GAAcE,WAAA;UACdH,UAAA,GAAa;UACb;QACF,KAAK;UACHC,WAAA,GAAcR,UAAA;UACdO,UAAA,GAAa;UACb;QAEF,KAAK;UACHC,WAAA,GAAcG,WAAA;UACdJ,UAAA,GAAa;UACb;QACF,KAAK;UACHC,WAAA,GAAcI,UAAA;UACdL,UAAA,GAAa;UACb;QACF,KAAK;UACHC,WAAA,GAAcK,YAAA;UACdN,UAAA,GAAa;UACb;QACF,KAAK;UACHC,WAAA,GAAcM,YAAA;UACdP,UAAA,GAAa;UACb;QACF,KAAK;UACHC,WAAA,GAAcM,YAAA;UACdP,UAAA,GAAa;UACb;MACH;MAGD,IAAIQ,OAAA,GAAS,IAAIP,WAAA,CAAYZ,KAAA,CAAMoB,KAAA,CAAMnB,YAAA,EAAeA,YAAA,IAAgBS,MAAA,GAASC,UAAA,CAAY;MAG7F,IAAIT,mBAAA,IAAuBI,aAAA,EAAe;QAExCa,OAAA,GAASE,cAAA,CAAeF,OAAA,EAAQR,UAAU;MAC3C;MAED,IAAID,MAAA,IAAU,GAAG;QAEf,OAAOS,OAAA,CAAO,CAAC;MAChB;MAGD,OAAOA,OAAA;IACR;IAID,SAASE,eAAeC,KAAA,EAAOC,SAAA,EAAW;MACxC,MAAMC,EAAA,GAAK,IAAIX,UAAA,CAAWS,KAAA,CAAMjB,MAAA,EAAQiB,KAAA,CAAMG,UAAA,EAAYH,KAAA,CAAMI,UAAU;MAC1E,SAASC,EAAA,GAAI,GAAGA,EAAA,GAAIL,KAAA,CAAMI,UAAA,EAAYC,EAAA,IAAKJ,SAAA,EAAW;QACpD,SAASK,CAAA,GAAID,EAAA,GAAIJ,SAAA,GAAY,GAAGM,CAAA,GAAIF,EAAA,EAAGC,CAAA,GAAIC,CAAA,EAAGD,CAAA,IAAKC,CAAA,IAAK;UACtD,MAAMC,GAAA,GAAMN,EAAA,CAAGK,CAAC;UAChBL,EAAA,CAAGK,CAAC,IAAIL,EAAA,CAAGI,CAAC;UACZJ,EAAA,CAAGI,CAAC,IAAIE,GAAA;QACT;MACF;MAED,OAAOR,KAAA;IACR;IAGD,SAASS,YAAYC,MAAA,EAAQ;MAC3B,IAAIC,KAAA,EAAMC,KAAA,EAAOC,EAAA,EAAIR,EAAA,EAAGS,CAAA,EAAGC,CAAA,EAAGC,EAAA,EAAIC,IAAA;MAClC,MAAMC,KAAA,GAAQR,MAAA,CAAOS,KAAA,CAAM,OAAO;MAClC,KAAKH,EAAA,GAAK,GAAGC,IAAA,GAAOC,KAAA,CAAME,MAAA,EAAQJ,EAAA,GAAKC,IAAA,EAAMD,EAAA,IAAM;QACjDF,CAAA,GAAII,KAAA,CAAMF,EAAE;QACZ,IAAIF,CAAA,CAAEO,KAAA,CAAM,SAAS,GAAG;UACtBpC,YAAA,CAAaqC,MAAA,GAAS;QACvB,WAAUR,CAAA,CAAEO,KAAA,CAAM,IAAI,GAAG,UACdN,CAAA,GAAID,CAAA,CAAEO,KAAA,CAAM,WAAW,GAAI;UACrCT,KAAA,GAAQG,CAAA,CAAE,CAAC,EAAEQ,IAAA,CAAM;UACnBZ,KAAA,GAAOI,CAAA,CAAE,CAAC,EAAEQ,IAAA,CAAM;UAClBV,EAAA,GAAKW,eAAA,CAAgBZ,KAAK;UAC1B,IAAIC,EAAA,EAAI;YACNA,EAAA,CAAGY,IAAA,CAAKxC,YAAA,EAAc0B,KAAI;UACtC,OAAiB;YACL1B,YAAA,CAAa2B,KAAK,IAAID,KAAA;UACvB;QACF;MACF;MAED,IAAI,CAAC1B,YAAA,CAAaqC,MAAA,EAAQ;QACxB,MAAM,IAAII,KAAA,CAAM,kBAAkB;MACnC;MAED,IAAIzC,YAAA,CAAa0C,QAAA,KAAa,SAAS1C,YAAA,CAAa0C,QAAA,KAAa,SAAS;QACxE,MAAM,IAAID,KAAA,CAAM,uBAAuB;MACxC;MAED,IAAI,CAACzC,YAAA,CAAa2C,OAAA,EAAS;QAEzB3C,YAAA,CAAa2C,OAAA,GAAU,CAAC,IAAIC,OAAA,CAAQ,GAAG,GAAG,CAAC,GAAG,IAAIA,OAAA,CAAQ,GAAG,GAAG,CAAC,GAAG,IAAIA,OAAA,CAAQ,GAAG,GAAG,CAAC,CAAC;QAExF,IAAI5C,YAAA,CAAa6C,QAAA,EAAU;UACzB,KAAKzB,EAAA,GAAI,GAAGA,EAAA,IAAK,GAAGA,EAAA,IAAK;YACvB,IAAI,CAAC0B,KAAA,CAAM9C,YAAA,CAAa6C,QAAA,CAASzB,EAAC,CAAC,GAAG;cACpCpB,YAAA,CAAa2C,OAAA,CAAQvB,EAAC,EAAE2B,cAAA,CAAe/C,YAAA,CAAa6C,QAAA,CAASzB,EAAC,CAAC;YAChE;UACF;QACF;MACF;IACF;IAGD,SAAS4B,gBAAgBtB,KAAA,EAAMuB,KAAA,EAAOC,GAAA,EAAK;MACzC,IAAIC,MAAA,GAAS;MACbF,KAAA,GAAQA,KAAA,IAAS;MACjBC,GAAA,GAAMA,GAAA,IAAOxB,KAAA,CAAKS,MAAA;MAClB,IAAIiB,KAAA;MAEJ,MAAMC,iBAAA,GAAoBrD,YAAA,CAAasD,KAAA,CAAMC,MAAA,CAAO,UAAUC,QAAA,EAAUC,OAAA,EAAS;QAC/E,OAAOD,QAAA,GAAWC,OAAA;MACnB,GAAE,CAAC;MAEJ,IAAIC,IAAA,GAAO;MACX,IAAI1D,YAAA,CAAa0C,QAAA,KAAa,OAAO;QACnCgB,IAAA,GAAO;MACR;MAED,MAAMC,MAAA,GAAS,IAAI3D,YAAA,CAAa4D,OAAA,CAAQP,iBAAiB;MACzD,IAAIQ,WAAA,GAAc;MAClB,IAAIC,eAAA,GAAkBC,QAAA;MACtB,IAAI/D,YAAA,CAAa4D,OAAA,KAAYlD,YAAA,IAAgBV,YAAA,CAAa4D,OAAA,KAAYjD,YAAA,EAAc;QAClFmD,eAAA,GAAkBE,UAAA;MACnB;MAED,SAAS5C,EAAA,GAAI6B,KAAA,EAAO7B,EAAA,GAAI8B,GAAA,EAAK9B,EAAA,IAAK;QAChCgC,KAAA,GAAQ1B,KAAA,CAAKN,EAAC;QAEd,KAAKgC,KAAA,GAAQ,KAAKA,KAAA,GAAQ,OAAOA,KAAA,KAAU,IAAI;UAC7CD,MAAA,IAAUc,MAAA,CAAOC,YAAA,CAAad,KAAK;QAC7C,OAAe;UACL,IAAID,MAAA,KAAW,IAAI;YACjBQ,MAAA,CAAOE,WAAW,IAAIC,eAAA,CAAgBX,MAAA,EAAQO,IAAI;YAClDG,WAAA;UACD;UAEDV,MAAA,GAAS;QACV;MACF;MAED,IAAIA,MAAA,KAAW,IAAI;QACjBQ,MAAA,CAAOE,WAAW,IAAIC,eAAA,CAAgBX,MAAA,EAAQO,IAAI;QAClDG,WAAA;MACD;MAED,OAAOF,MAAA;IACR;IAED,MAAMQ,MAAA,GAASlE,IAAA,CAAK,SAASd,IAAA,CAAKgC,UAAU;IAC5C,MAAMiD,OAAA,GAAUD,MAAA,CAAOhC,MAAA;IACvB,IAAIkC,OAAA,GAAU;IACd,IAAIC,WAAA,GAAc;IAClB,IAAIC,CAAA;IACJ,KAAKA,CAAA,GAAI,GAAGA,CAAA,GAAIH,OAAA,EAASG,CAAA,IAAK;MAC5B,IAAIJ,MAAA,CAAOI,CAAA,GAAI,CAAC,KAAK,MAAMJ,MAAA,CAAOI,CAAC,KAAK,IAAI;QAG1CF,OAAA,GAAU,KAAKG,UAAA,CAAWL,MAAA,EAAQ,GAAGI,CAAA,GAAI,CAAC;QAE1CD,WAAA,GAAcC,CAAA,GAAI;QAClB;MACD;IACF;IAGD/C,WAAA,CAAY6C,OAAO;IAEnB5E,KAAA,GAAQ0E,MAAA,CAAOM,QAAA,CAASH,WAAW;IACnC,IAAItE,YAAA,CAAa0C,QAAA,CAASgC,SAAA,CAAU,GAAG,CAAC,MAAM,MAAM;MAGlDjF,KAAA,GAAQkF,UAAA,CAAW,IAAIrE,UAAA,CAAWb,KAAK,CAAC;IAC9C,WACMO,YAAA,CAAa0C,QAAA,KAAa,WAC1B1C,YAAA,CAAa0C,QAAA,KAAa,UAC1B1C,YAAA,CAAa0C,QAAA,KAAa,SAC1B1C,YAAA,CAAa0C,QAAA,KAAa,OAC1B;MACAjD,KAAA,GAAQuD,eAAA,CAAgBvD,KAAK;IACnC,WAAeO,YAAA,CAAa0C,QAAA,KAAa,OAAO;MAE1C,MAAMkC,KAAA,GAAQ,IAAItE,UAAA,CAAWb,KAAA,CAAM0C,MAAM;MAEzC,SAASf,EAAA,GAAI,GAAGA,EAAA,GAAI3B,KAAA,CAAM0C,MAAA,EAAQf,EAAA,IAAK;QACrCwD,KAAA,CAAMxD,EAAC,IAAI3B,KAAA,CAAM2B,EAAC;MACnB;MAED3B,KAAA,GAAQmF,KAAA;IACT;IAGDnF,KAAA,GAAQA,KAAA,CAAMK,MAAA;IAEd,MAAM+E,MAAA,GAAS,IAAIC,MAAA,CAAQ;IAC3BD,MAAA,CAAOpD,MAAA,GAASzB,YAAA;IAIhB6E,MAAA,CAAO1F,IAAA,GAAO,IAAIa,YAAA,CAAa4D,OAAA,CAAQnE,KAAK;IAE5C,MAAMsF,OAAA,GAAUF,MAAA,CAAOG,aAAA,CAAe;IACtC,MAAMC,GAAA,GAAMF,OAAA,CAAQ,CAAC;IACrB,MAAMG,GAAA,GAAMH,OAAA,CAAQ,CAAC;IAErBF,MAAA,CAAOM,SAAA,GAAYF,GAAA;IACnBJ,MAAA,CAAOO,UAAA,GAAaF,GAAA;IAGpBL,MAAA,CAAOQ,UAAA,GAAa,CAACrF,YAAA,CAAasD,KAAA,CAAM,CAAC,GAAGtD,YAAA,CAAasD,KAAA,CAAM,CAAC,GAAGtD,YAAA,CAAasD,KAAA,CAAM,CAAC,CAAC;IACxFuB,MAAA,CAAOS,OAAA,GAAUT,MAAA,CAAOQ,UAAA,CAAW,CAAC;IACpCR,MAAA,CAAOU,OAAA,GAAUV,MAAA,CAAOQ,UAAA,CAAW,CAAC;IACpCR,MAAA,CAAOW,OAAA,GAAUX,MAAA,CAAOQ,UAAA,CAAW,CAAC;IAEpC,MAAMI,QAAA,GAAW,IAAI7C,OAAA,CACnB5C,YAAA,CAAa2C,OAAA,CAAQ,CAAC,EAAE,CAAC,GACzB3C,YAAA,CAAa2C,OAAA,CAAQ,CAAC,EAAE,CAAC,GACzB3C,YAAA,CAAa2C,OAAA,CAAQ,CAAC,EAAE,CAAC,CAC1B,EAACR,MAAA,CAAQ;IACV,MAAMuD,QAAA,GAAW,IAAI9C,OAAA,CACnB5C,YAAA,CAAa2C,OAAA,CAAQ,CAAC,EAAE,CAAC,GACzB3C,YAAA,CAAa2C,OAAA,CAAQ,CAAC,EAAE,CAAC,GACzB3C,YAAA,CAAa2C,OAAA,CAAQ,CAAC,EAAE,CAAC,CAC1B,EAACR,MAAA,CAAQ;IACV,MAAMwD,QAAA,GAAW,IAAI/C,OAAA,CACnB5C,YAAA,CAAa2C,OAAA,CAAQ,CAAC,EAAE,CAAC,GACzB3C,YAAA,CAAa2C,OAAA,CAAQ,CAAC,EAAE,CAAC,GACzB3C,YAAA,CAAa2C,OAAA,CAAQ,CAAC,EAAE,CAAC,CAC1B,EAACR,MAAA,CAAQ;IACV0C,MAAA,CAAOe,OAAA,GAAU,CAACH,QAAA,EAAUC,QAAA,EAAUC,QAAQ;IAG9Cd,MAAA,CAAOgB,MAAA,GAAS,IAAIC,OAAA,CAAS;IAE7B,IAAIC,OAAA,GAAU;IACd,IAAIC,OAAA,GAAU;IACd,MAAMC,OAAA,GAAU;IAEhB,IAAIjG,YAAA,CAAakG,KAAA,IAAS,2BAA2B;MACnDH,OAAA,GAAU;MACVC,OAAA,GAAU;IAChB,WAAehG,YAAA,CAAakG,KAAA,KAAU,0BAA0B;MAC1DH,OAAA,GAAU;IACX;IAED,IAAI,CAAC/F,YAAA,CAAa2C,OAAA,EAAS;MACzBkC,MAAA,CAAOgB,MAAA,CAAOM,GAAA,CAAIJ,OAAA,EAAS,GAAG,GAAG,GAAG,GAAGC,OAAA,EAAS,GAAG,GAAG,GAAG,GAAGC,OAAA,EAAS,GAAG,GAAG,GAAG,GAAG,CAAC;IACxF,OAAW;MACL,MAAMG,CAAA,GAAIpG,YAAA,CAAa2C,OAAA;MAEvBkC,MAAA,CAAOgB,MAAA,CAAOM,GAAA,CACZJ,OAAA,GAAUK,CAAA,CAAE,CAAC,EAAE,CAAC,GAChBL,OAAA,GAAUK,CAAA,CAAE,CAAC,EAAE,CAAC,GAChBL,OAAA,GAAUK,CAAA,CAAE,CAAC,EAAE,CAAC,GAChB,GACAJ,OAAA,GAAUI,CAAA,CAAE,CAAC,EAAE,CAAC,GAChBJ,OAAA,GAAUI,CAAA,CAAE,CAAC,EAAE,CAAC,GAChBJ,OAAA,GAAUI,CAAA,CAAE,CAAC,EAAE,CAAC,GAChB,GACAH,OAAA,GAAUG,CAAA,CAAE,CAAC,EAAE,CAAC,GAChBH,OAAA,GAAUG,CAAA,CAAE,CAAC,EAAE,CAAC,GAChBH,OAAA,GAAUG,CAAA,CAAE,CAAC,EAAE,CAAC,GAChB,GACA,GACA,GACA,GACA,CACD;IACF;IAEDvB,MAAA,CAAOwB,aAAA,GAAgB,IAAIP,OAAA,CAAS;IACpCjB,MAAA,CAAOwB,aAAA,CAAcC,IAAA,CAAKzB,MAAA,CAAOgB,MAAM,EAAEU,MAAA,CAAQ;IACjD1B,MAAA,CAAO2B,aAAA,GAAgB,IAAI5D,OAAA,CAAQiC,MAAA,CAAOS,OAAA,EAAST,MAAA,CAAOU,OAAA,EAASV,MAAA,CAAOW,OAAO,EAC9EiB,YAAA,CAAa5B,MAAA,CAAOgB,MAAM,EAC1Ba,KAAA,CAAO,EACPC,OAAA,CAAS,EACTC,GAAA,CAAIC,IAAA,CAAKC,GAAG;IAIf,IAAIjC,MAAA,CAAOkC,cAAA,KAAmB,CAAAC,QAAA,EAAW;MACvCnC,MAAA,CAAOkC,cAAA,GAAiB9B,GAAA;IACzB;IAED,IAAIJ,MAAA,CAAOoC,cAAA,KAAmBD,QAAA,EAAU;MACtCnC,MAAA,CAAOoC,cAAA,GAAiB/B,GAAA;IACzB;IAED,OAAOL,MAAA;EACR;EAEDL,WAAWzD,KAAA,EAAOkC,KAAA,EAAOC,GAAA,EAAK;IAE5B,IAAID,KAAA,KAAU,QAAW;MACvBA,KAAA,GAAQ;IACT;IAED,IAAIC,GAAA,KAAQ,QAAW;MACrBA,GAAA,GAAMnC,KAAA,CAAMoB,MAAA;IACb;IAED,IAAI+E,MAAA,GAAS;IAEb,IAAI3C,CAAA,GAAI;IACR,KAAKA,CAAA,GAAItB,KAAA,EAAOsB,CAAA,GAAIrB,GAAA,EAAK,EAAEqB,CAAA,EAAG;MAC5B2C,MAAA,IAAUjD,MAAA,CAAOC,YAAA,CAAanD,KAAA,CAAMwD,CAAC,CAAC;IACvC;IAED,OAAO2C,MAAA;EACR;AACH;AAEA,MAAM3E,eAAA,GAAkB;EACtBrC,IAAA,EAAM,SAAAA,CAAUf,IAAA,EAAM;IACpB,QAAQA,IAAA;MACN,KAAK;MACL,KAAK;MACL,KAAK;MACL,KAAK;QACH,KAAKyE,OAAA,GAAUtD,UAAA;QACf;MACF,KAAK;MACL,KAAK;MACL,KAAK;QACH,KAAKsD,OAAA,GAAUhE,SAAA;QACf;MACF,KAAK;MACL,KAAK;MACL,KAAK;MACL,KAAK;MACL,KAAK;MACL,KAAK;QACH,KAAKgE,OAAA,GAAU/D,UAAA;QACf;MACF,KAAK;MACL,KAAK;MACL,KAAK;MACL,KAAK;MACL,KAAK;QACH,KAAK+D,OAAA,GAAUrD,WAAA;QACf;MACF,KAAK;MACL,KAAK;MACL,KAAK;MACL,KAAK;QACH,KAAKqD,OAAA,GAAUnD,UAAA;QACf;MACF,KAAK;MACL,KAAK;MACL,KAAK;MACL,KAAK;QACH,KAAKmD,OAAA,GAAUpD,WAAA;QACf;MACF,KAAK;QACH,KAAKoD,OAAA,GAAUlD,YAAA;QACf;MACF,KAAK;QACH,KAAKkD,OAAA,GAAUjD,YAAA;QACf;MACF;QACE,MAAM,IAAI8B,KAAA,CAAM,iCAAiCtD,IAAI;IACxD;IAED,OAAQ,KAAKe,IAAA,GAAOf,IAAA;EACrB;EAEDgI,MAAA,EAAQ,SAAAA,CAAUhI,IAAA,EAAM;IACtB,OAAQ,KAAKgI,MAAA,GAAShI,IAAA;EACvB;EAEDuD,QAAA,EAAU,SAAAA,CAAUvD,IAAA,EAAM;IACxB,OAAQ,KAAKuD,QAAA,GAAWvD,IAAA;EACzB;EAEDiI,SAAA,EAAW,SAAAA,CAAUjI,IAAA,EAAM;IACzB,OAAQ,KAAKkI,GAAA,GAAMtD,QAAA,CAAS5E,IAAA,EAAM,EAAE;EACrC;EAEDmE,KAAA,EAAO,SAAAA,CAAUnE,IAAA,EAAM;IACrB,IAAIoF,CAAA;IACJ,OAAQ,KAAKjB,KAAA,GAAS,YAAY;MAChC,MAAMgE,IAAA,GAAOnI,IAAA,CAAK+C,KAAA,CAAM,KAAK;MAC7B,MAAMqF,QAAA,GAAW,EAAE;MAEnB,SAASxF,EAAA,GAAK,GAAGC,IAAA,GAAOsF,IAAA,CAAKnF,MAAA,EAAQJ,EAAA,GAAKC,IAAA,EAAMD,EAAA,IAAM;QACpDwC,CAAA,GAAI+C,IAAA,CAAKvF,EAAE;QACXwF,QAAA,CAASC,IAAA,CAAKzD,QAAA,CAASQ,CAAA,EAAG,EAAE,CAAC;MAC9B;MAED,OAAOgD,QAAA;IACb;EACG;EAEDrB,KAAA,EAAO,SAAAA,CAAU/G,IAAA,EAAM;IACrB,OAAQ,KAAK+G,KAAA,GAAQ/G,IAAA;EACtB;EAED,gBAAgB,SAAAsI,CAAUtI,IAAA,EAAM;IAC9B,OAAQ,KAAKuI,YAAA,GAAevI,IAAA,CAAK+C,KAAA,CAAM,GAAG,EAAE,CAAC,EAAEA,KAAA,CAAM,GAAG,EAAE,CAAC,EAAEA,KAAA,CAAM,GAAG;EACvE;EAED,oBAAoB,SAAAyF,CAAUxI,IAAA,EAAM;IAClC,IAAIyI,CAAA,EAAGxB,CAAA;IACP,MAAMyB,KAAA,GAAQ1I,IAAA,CAAKiD,KAAA,CAAM,UAAU;IACnC,OAAQ,KAAKO,OAAA,GAAW,YAAY;MAClC,MAAM4E,QAAA,GAAW,EAAE;MAEnB,SAASxF,EAAA,GAAK,GAAGC,IAAA,GAAO6F,KAAA,CAAM1F,MAAA,EAAQJ,EAAA,GAAKC,IAAA,EAAMD,EAAA,IAAM;QACrDqE,CAAA,GAAIyB,KAAA,CAAM9F,EAAE;QACZwF,QAAA,CAASC,IAAA,CACN,YAAY;UACX,MAAMF,IAAA,GAAOlB,CAAA,CAAEvF,KAAA,CAAM,GAAG,EAAE,EAAEqB,KAAA,CAAM,GAAG;UACrC,MAAM4F,SAAA,GAAY,EAAE;UAEpB,SAASC,EAAA,GAAK,GAAGC,KAAA,GAAQV,IAAA,CAAKnF,MAAA,EAAQ4F,EAAA,GAAKC,KAAA,EAAOD,EAAA,IAAM;YACtDH,CAAA,GAAIN,IAAA,CAAKS,EAAE;YACXD,SAAA,CAAUN,IAAA,CAAKxD,UAAA,CAAW4D,CAAC,CAAC;UAC7B;UAED,OAAOE,SAAA;QACnB,EAAc,CACL;MACF;MAED,OAAOP,QAAA;IACb;EACG;EAED1E,QAAA,EAAU,SAAAA,CAAU1D,IAAA,EAAM;IACxB,IAAIyI,CAAA;IACJ,MAAMC,KAAA,GAAQ1I,IAAA,CAAK+C,KAAA,CAAM,KAAK;IAC9B,OAAQ,KAAKW,QAAA,GAAY,YAAY;MACnC,MAAM0E,QAAA,GAAW,EAAE;MAEnB,SAASxF,EAAA,GAAK,GAAGC,IAAA,GAAO6F,KAAA,CAAM1F,MAAA,EAAQJ,EAAA,GAAKC,IAAA,EAAMD,EAAA,IAAM;QACrD6F,CAAA,GAAIC,KAAA,CAAM9F,EAAE;QACZwF,QAAA,CAASC,IAAA,CAAKxD,UAAA,CAAW4D,CAAC,CAAC;MAC5B;MAED,OAAOL,QAAA;IACb;EACG;AACH", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}