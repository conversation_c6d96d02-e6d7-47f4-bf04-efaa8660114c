{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Apps_Creations\\\\SiahMan_Racing4Speed_WebGame\\\\siah-man-racing\\\\src\\\\App.tsx\",\n  _s = $RefreshSig$();\nimport React, { useState } from 'react';\nimport { Provider } from 'react-redux';\nimport { BrowserRouter as Router, Routes, Route } from 'react-router-dom';\nimport { store } from './store';\nimport './App.css';\nimport { ThemeProvider } from 'styled-components';\nimport { GlobalStyle } from './GlobalStyle';\nimport styled from 'styled-components';\nimport theme from './theme';\n\n// Pages\nimport MainMenu from './pages/MainMenu';\nimport Laboratory from './pages/Laboratory';\nimport VehicleCustomization from './pages/VehicleCustomization';\nimport Racing from './pages/IntegratedRacing';\nimport StoryMode from './pages/StoryMode';\nimport Settings from './pages/Settings';\nimport GameModePage from './pages/GameMode';\nimport ErrorBoundary from './components/ErrorBoundary';\n\n// Game Components\nimport AssetLoaderComponent from './components/AssetLoader';\nimport AudioController from './components/AudioController';\nimport InputController from './components/InputController';\n\n// Game Systems\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst AppContainer = styled.div`\n  position: relative;\n  min-height: 100vh;\n`;\n_c = AppContainer;\nfunction App() {\n  _s();\n  const [assetsLoaded, setAssetsLoaded] = useState(false);\n\n  // Handle asset loading completion\n  const handleLoadingComplete = () => {\n    setAssetsLoaded(true);\n  };\n\n  // Show loading screen if assets aren't loaded yet\n  if (!assetsLoaded) {\n    return /*#__PURE__*/_jsxDEV(Provider, {\n      store: store,\n      children: /*#__PURE__*/_jsxDEV(ThemeProvider, {\n        theme: theme,\n        children: [/*#__PURE__*/_jsxDEV(GlobalStyle, {}, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 48,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(AssetLoaderComponent, {\n          onLoadingComplete: handleLoadingComplete\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 49,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 47,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 46,\n      columnNumber: 7\n    }, this);\n  }\n\n  // Main app with all routes once assets are loaded\n  return /*#__PURE__*/_jsxDEV(Provider, {\n    store: store,\n    children: /*#__PURE__*/_jsxDEV(ThemeProvider, {\n      theme: theme,\n      children: [/*#__PURE__*/_jsxDEV(GlobalStyle, {}, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 59,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Router, {\n        children: /*#__PURE__*/_jsxDEV(ErrorBoundary, {\n          children: /*#__PURE__*/_jsxDEV(AppContainer, {\n            children: [/*#__PURE__*/_jsxDEV(AudioController, {}, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 64,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(InputController, {}, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 67,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Routes, {\n              children: [/*#__PURE__*/_jsxDEV(Route, {\n                path: \"/\",\n                element: /*#__PURE__*/_jsxDEV(MainMenu, {}, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 70,\n                  columnNumber: 42\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 70,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(Route, {\n                path: \"/main-menu\",\n                element: /*#__PURE__*/_jsxDEV(MainMenu, {}, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 71,\n                  columnNumber: 51\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 71,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(Route, {\n                path: \"/laboratory\",\n                element: /*#__PURE__*/_jsxDEV(Laboratory, {}, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 72,\n                  columnNumber: 52\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 72,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(Route, {\n                path: \"/customize\",\n                element: /*#__PURE__*/_jsxDEV(VehicleCustomization, {}, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 73,\n                  columnNumber: 51\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 73,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(Route, {\n                path: \"/race\",\n                element: /*#__PURE__*/_jsxDEV(Racing, {}, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 74,\n                  columnNumber: 46\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 74,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(Route, {\n                path: \"/racing\",\n                element: /*#__PURE__*/_jsxDEV(Racing, {}, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 75,\n                  columnNumber: 48\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 75,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(Route, {\n                path: \"/game-mode\",\n                element: /*#__PURE__*/_jsxDEV(GameModePage, {}, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 76,\n                  columnNumber: 51\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 76,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(Route, {\n                path: \"/story\",\n                element: /*#__PURE__*/_jsxDEV(StoryMode, {}, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 77,\n                  columnNumber: 47\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 77,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(Route, {\n                path: \"/settings\",\n                element: /*#__PURE__*/_jsxDEV(Settings, {}, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 78,\n                  columnNumber: 50\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 78,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 69,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 62,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 61,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 60,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 58,\n      columnNumber: 7\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 57,\n    columnNumber: 5\n  }, this);\n}\n_s(App, \"abS5goBEkKrqJvjmwRlnTWi5pEU=\");\n_c2 = App;\nexport default App;\nvar _c, _c2;\n$RefreshReg$(_c, \"AppContainer\");\n$RefreshReg$(_c2, \"App\");", "map": {"version": 3, "names": ["React", "useState", "Provider", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "Router", "Routes", "Route", "store", "ThemeProvider", "GlobalStyle", "styled", "theme", "MainMenu", "Laboratory", "VehicleCustomization", "Racing", "StoryMode", "Settings", "GameModePage", "Error<PERSON>ou<PERSON><PERSON>", "AssetLoaderComponent", "AudioController", "InputController", "jsxDEV", "_jsxDEV", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "div", "_c", "App", "_s", "assetsLoaded", "setAssetsLoaded", "handleLoadingComplete", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "onLoadingComplete", "path", "element", "_c2", "$RefreshReg$"], "sources": ["C:/Users/<USER>/Documents/Apps_Creations/SiahMan_Racing4Speed_WebGame/siah-man-racing/src/App.tsx"], "sourcesContent": ["import React, { useState } from 'react';\nimport { Provider } from 'react-redux';\nimport { BrowserRouter as Router, Routes, Route } from 'react-router-dom';\nimport { store } from './store';\nimport './App.css';\nimport { ThemeProvider } from 'styled-components';\nimport { GlobalStyle } from './GlobalStyle';\nimport styled from 'styled-components';\nimport theme from './theme';\n\n// Pages\nimport MainMenu from './pages/MainMenu';\nimport Laboratory from './pages/Laboratory';\nimport VehicleCustomization from './pages/VehicleCustomization';\nimport Racing from './pages/IntegratedRacing';\nimport StoryMode from './pages/StoryMode';\nimport Settings from './pages/Settings';\nimport GameModePage from './pages/GameMode';\nimport ErrorBoundary from './components/ErrorBoundary';\n\n// Game Components\nimport AssetLoaderComponent from './components/AssetLoader';\nimport AudioController from './components/AudioController';\nimport InputController from './components/InputController';\n\n// Game Systems\nimport { AudioSystem } from './game/AudioSystem';\nimport { TTSSystem } from './game/TTSSystem';\n\nconst AppContainer = styled.div`\n  position: relative;\n  min-height: 100vh;\n`;\n\nfunction App() {\n  const [assetsLoaded, setAssetsLoaded] = useState(false);\n  \n  // Handle asset loading completion\n  const handleLoadingComplete = () => {\n    setAssetsLoaded(true);\n  };\n  \n  // Show loading screen if assets aren't loaded yet\n  if (!assetsLoaded) {\n    return (\n      <Provider store={store}>\n        <ThemeProvider theme={theme}>\n          <GlobalStyle />\n          <AssetLoaderComponent onLoadingComplete={handleLoadingComplete} />\n        </ThemeProvider>\n      </Provider>\n    );\n  }\n  \n  // Main app with all routes once assets are loaded\n  return (\n    <Provider store={store}>\n      <ThemeProvider theme={theme}>\n        <GlobalStyle />\n        <Router>\n          <ErrorBoundary>\n            <AppContainer>\n              {/* Global Audio Controls */}\n              <AudioController />\n              \n              {/* Global Input Controller */}\n              <InputController />\n              \n              <Routes>\n                <Route path=\"/\" element={<MainMenu />} />\n                <Route path=\"/main-menu\" element={<MainMenu />} />\n                <Route path=\"/laboratory\" element={<Laboratory />} />\n                <Route path=\"/customize\" element={<VehicleCustomization />} />\n                <Route path=\"/race\" element={<Racing />} />\n                <Route path=\"/racing\" element={<Racing />} />\n                <Route path=\"/game-mode\" element={<GameModePage />} />\n                <Route path=\"/story\" element={<StoryMode />} />\n                <Route path=\"/settings\" element={<Settings />} />\n              </Routes>\n            </AppContainer>\n          </ErrorBoundary>\n        </Router>\n      </ThemeProvider>\n    </Provider>\n  );\n}\n\nexport default App;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,QAAQ,OAAO;AACvC,SAASC,QAAQ,QAAQ,aAAa;AACtC,SAASC,aAAa,IAAIC,MAAM,EAAEC,MAAM,EAAEC,KAAK,QAAQ,kBAAkB;AACzE,SAASC,KAAK,QAAQ,SAAS;AAC/B,OAAO,WAAW;AAClB,SAASC,aAAa,QAAQ,mBAAmB;AACjD,SAASC,WAAW,QAAQ,eAAe;AAC3C,OAAOC,MAAM,MAAM,mBAAmB;AACtC,OAAOC,KAAK,MAAM,SAAS;;AAE3B;AACA,OAAOC,QAAQ,MAAM,kBAAkB;AACvC,OAAOC,UAAU,MAAM,oBAAoB;AAC3C,OAAOC,oBAAoB,MAAM,8BAA8B;AAC/D,OAAOC,MAAM,MAAM,0BAA0B;AAC7C,OAAOC,SAAS,MAAM,mBAAmB;AACzC,OAAOC,QAAQ,MAAM,kBAAkB;AACvC,OAAOC,YAAY,MAAM,kBAAkB;AAC3C,OAAOC,aAAa,MAAM,4BAA4B;;AAEtD;AACA,OAAOC,oBAAoB,MAAM,0BAA0B;AAC3D,OAAOC,eAAe,MAAM,8BAA8B;AAC1D,OAAOC,eAAe,MAAM,8BAA8B;;AAE1D;AAAA,SAAAC,MAAA,IAAAC,OAAA;AAIA,MAAMC,YAAY,GAAGf,MAAM,CAACgB,GAAG;AAC/B;AACA;AACA,CAAC;AAACC,EAAA,GAHIF,YAAY;AAKlB,SAASG,GAAGA,CAAA,EAAG;EAAAC,EAAA;EACb,MAAM,CAACC,YAAY,EAAEC,eAAe,CAAC,GAAG9B,QAAQ,CAAC,KAAK,CAAC;;EAEvD;EACA,MAAM+B,qBAAqB,GAAGA,CAAA,KAAM;IAClCD,eAAe,CAAC,IAAI,CAAC;EACvB,CAAC;;EAED;EACA,IAAI,CAACD,YAAY,EAAE;IACjB,oBACEN,OAAA,CAACtB,QAAQ;MAACK,KAAK,EAAEA,KAAM;MAAA0B,QAAA,eACrBT,OAAA,CAAChB,aAAa;QAACG,KAAK,EAAEA,KAAM;QAAAsB,QAAA,gBAC1BT,OAAA,CAACf,WAAW;UAAAyB,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC,eACfb,OAAA,CAACJ,oBAAoB;UAACkB,iBAAiB,EAAEN;QAAsB;UAAAE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACrD;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACR,CAAC;EAEf;;EAEA;EACA,oBACEb,OAAA,CAACtB,QAAQ;IAACK,KAAK,EAAEA,KAAM;IAAA0B,QAAA,eACrBT,OAAA,CAAChB,aAAa;MAACG,KAAK,EAAEA,KAAM;MAAAsB,QAAA,gBAC1BT,OAAA,CAACf,WAAW;QAAAyB,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC,eACfb,OAAA,CAACpB,MAAM;QAAA6B,QAAA,eACLT,OAAA,CAACL,aAAa;UAAAc,QAAA,eACZT,OAAA,CAACC,YAAY;YAAAQ,QAAA,gBAEXT,OAAA,CAACH,eAAe;cAAAa,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,eAGnBb,OAAA,CAACF,eAAe;cAAAY,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,eAEnBb,OAAA,CAACnB,MAAM;cAAA4B,QAAA,gBACLT,OAAA,CAAClB,KAAK;gBAACiC,IAAI,EAAC,GAAG;gBAACC,OAAO,eAAEhB,OAAA,CAACZ,QAAQ;kBAAAsB,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE;cAAE;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC,eACzCb,OAAA,CAAClB,KAAK;gBAACiC,IAAI,EAAC,YAAY;gBAACC,OAAO,eAAEhB,OAAA,CAACZ,QAAQ;kBAAAsB,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE;cAAE;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC,eAClDb,OAAA,CAAClB,KAAK;gBAACiC,IAAI,EAAC,aAAa;gBAACC,OAAO,eAAEhB,OAAA,CAACX,UAAU;kBAAAqB,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE;cAAE;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC,eACrDb,OAAA,CAAClB,KAAK;gBAACiC,IAAI,EAAC,YAAY;gBAACC,OAAO,eAAEhB,OAAA,CAACV,oBAAoB;kBAAAoB,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE;cAAE;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC,eAC9Db,OAAA,CAAClB,KAAK;gBAACiC,IAAI,EAAC,OAAO;gBAACC,OAAO,eAAEhB,OAAA,CAACT,MAAM;kBAAAmB,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE;cAAE;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC,eAC3Cb,OAAA,CAAClB,KAAK;gBAACiC,IAAI,EAAC,SAAS;gBAACC,OAAO,eAAEhB,OAAA,CAACT,MAAM;kBAAAmB,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE;cAAE;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC,eAC7Cb,OAAA,CAAClB,KAAK;gBAACiC,IAAI,EAAC,YAAY;gBAACC,OAAO,eAAEhB,OAAA,CAACN,YAAY;kBAAAgB,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE;cAAE;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC,eACtDb,OAAA,CAAClB,KAAK;gBAACiC,IAAI,EAAC,QAAQ;gBAACC,OAAO,eAAEhB,OAAA,CAACR,SAAS;kBAAAkB,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE;cAAE;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC,eAC/Cb,OAAA,CAAClB,KAAK;gBAACiC,IAAI,EAAC,WAAW;gBAACC,OAAO,eAAEhB,OAAA,CAACP,QAAQ;kBAAAiB,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE;cAAE;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC3C,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACG;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACF;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACV,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACI;EAAC;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACR,CAAC;AAEf;AAACR,EAAA,CAnDQD,GAAG;AAAAa,GAAA,GAAHb,GAAG;AAqDZ,eAAeA,GAAG;AAAC,IAAAD,EAAA,EAAAc,GAAA;AAAAC,YAAA,CAAAf,EAAA;AAAAe,YAAA,CAAAD,GAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}