{"ast": null, "code": "import { Mesh, PlaneGeometry, MeshBasicMaterial, CanvasTexture, LinearFilter, Color } from \"three\";\nclass HTMLMesh extends Mesh {\n  constructor(dom) {\n    const texture = new HTMLTexture(dom);\n    const geometry = new PlaneGeometry(texture.image.width * 1e-3, texture.image.height * 1e-3);\n    const material = new MeshBasicMaterial({\n      map: texture,\n      toneMapped: false,\n      transparent: true\n    });\n    super(geometry, material);\n    function onEvent(event) {\n      material.map.dispatchDOMEvent(event);\n    }\n    this.addEventListener(\"mousedown\", onEvent);\n    this.addEventListener(\"mousemove\", onEvent);\n    this.addEventListener(\"mouseup\", onEvent);\n    this.addEventListener(\"click\", onEvent);\n    this.dispose = function () {\n      geometry.dispose();\n      material.dispose();\n      material.map.dispose();\n      canvases.delete(dom);\n      this.removeEventListener(\"mousedown\", onEvent);\n      this.removeEventListener(\"mousemove\", onEvent);\n      this.removeEventListener(\"mouseup\", onEvent);\n      this.removeEventListener(\"click\", onEvent);\n    };\n  }\n}\nclass HTMLTexture extends CanvasTexture {\n  constructor(dom) {\n    super(html2canvas(dom));\n    this.dom = dom;\n    this.anisotropy = 16;\n    if (\"colorSpace\" in this) this.colorSpace = \"srgb\";else this.encoding = 3001;\n    this.minFilter = LinearFilter;\n    this.magFilter = LinearFilter;\n    const observer = new MutationObserver(() => {\n      if (!this.scheduleUpdate) {\n        this.scheduleUpdate = setTimeout(() => this.update(), 16);\n      }\n    });\n    const config = {\n      attributes: true,\n      childList: true,\n      subtree: true,\n      characterData: true\n    };\n    observer.observe(dom, config);\n    this.observer = observer;\n  }\n  dispatchDOMEvent(event) {\n    if (event.data) {\n      htmlevent(this.dom, event.type, event.data.x, event.data.y);\n    }\n  }\n  update() {\n    this.image = html2canvas(this.dom);\n    this.needsUpdate = true;\n    this.scheduleUpdate = null;\n  }\n  dispose() {\n    if (this.observer) {\n      this.observer.disconnect();\n    }\n    this.scheduleUpdate = clearTimeout(this.scheduleUpdate);\n    super.dispose();\n  }\n}\nconst canvases = /* @__PURE__ */new WeakMap();\nfunction html2canvas(element) {\n  const range = document.createRange();\n  const color = new Color();\n  function Clipper(context2) {\n    const clips = [];\n    let isClipping = false;\n    function doClip() {\n      if (isClipping) {\n        isClipping = false;\n        context2.restore();\n      }\n      if (clips.length === 0) return;\n      let minX = -Infinity,\n        minY = -Infinity;\n      let maxX = Infinity,\n        maxY = Infinity;\n      for (let i = 0; i < clips.length; i++) {\n        const clip = clips[i];\n        minX = Math.max(minX, clip.x);\n        minY = Math.max(minY, clip.y);\n        maxX = Math.min(maxX, clip.x + clip.width);\n        maxY = Math.min(maxY, clip.y + clip.height);\n      }\n      context2.save();\n      context2.beginPath();\n      context2.rect(minX, minY, maxX - minX, maxY - minY);\n      context2.clip();\n      isClipping = true;\n    }\n    return {\n      add: function (clip) {\n        clips.push(clip);\n        doClip();\n      },\n      remove: function () {\n        clips.pop();\n        doClip();\n      }\n    };\n  }\n  function drawText(style, x, y, string) {\n    if (string !== \"\") {\n      if (style.textTransform === \"uppercase\") {\n        string = string.toUpperCase();\n      }\n      context.font = style.fontWeight + \" \" + style.fontSize + \" \" + style.fontFamily;\n      context.textBaseline = \"top\";\n      context.fillStyle = style.color;\n      context.fillText(string, x, y + parseFloat(style.fontSize) * 0.1);\n    }\n  }\n  function buildRectPath(x, y, w, h, r) {\n    if (w < 2 * r) r = w / 2;\n    if (h < 2 * r) r = h / 2;\n    context.beginPath();\n    context.moveTo(x + r, y);\n    context.arcTo(x + w, y, x + w, y + h, r);\n    context.arcTo(x + w, y + h, x, y + h, r);\n    context.arcTo(x, y + h, x, y, r);\n    context.arcTo(x, y, x + w, y, r);\n    context.closePath();\n  }\n  function drawBorder(style, which, x, y, width, height) {\n    const borderWidth = style[which + \"Width\"];\n    const borderStyle = style[which + \"Style\"];\n    const borderColor = style[which + \"Color\"];\n    if (borderWidth !== \"0px\" && borderStyle !== \"none\" && borderColor !== \"transparent\" && borderColor !== \"rgba(0, 0, 0, 0)\") {\n      context.strokeStyle = borderColor;\n      context.lineWidth = parseFloat(borderWidth);\n      context.beginPath();\n      context.moveTo(x, y);\n      context.lineTo(x + width, y + height);\n      context.stroke();\n    }\n  }\n  function drawElement(element2, style) {\n    let x = 0,\n      y = 0,\n      width = 0,\n      height = 0;\n    if (element2.nodeType === Node.TEXT_NODE) {\n      range.selectNode(element2);\n      const rect = range.getBoundingClientRect();\n      x = rect.left - offset.left - 0.5;\n      y = rect.top - offset.top - 0.5;\n      width = rect.width;\n      height = rect.height;\n      drawText(style, x, y, element2.nodeValue.trim());\n    } else if (element2.nodeType === Node.COMMENT_NODE) {\n      return;\n    } else if (element2 instanceof HTMLCanvasElement) {\n      if (element2.style.display === \"none\") return;\n      context.save();\n      const dpr = window.devicePixelRatio;\n      context.scale(1 / dpr, 1 / dpr);\n      context.drawImage(element2, 0, 0);\n      context.restore();\n    } else {\n      if (element2.style.display === \"none\") return;\n      const rect = element2.getBoundingClientRect();\n      x = rect.left - offset.left - 0.5;\n      y = rect.top - offset.top - 0.5;\n      width = rect.width;\n      height = rect.height;\n      style = window.getComputedStyle(element2);\n      buildRectPath(x, y, width, height, parseFloat(style.borderRadius));\n      const backgroundColor = style.backgroundColor;\n      if (backgroundColor !== \"transparent\" && backgroundColor !== \"rgba(0, 0, 0, 0)\") {\n        context.fillStyle = backgroundColor;\n        context.fill();\n      }\n      const borders = [\"borderTop\", \"borderLeft\", \"borderBottom\", \"borderRight\"];\n      let match = true;\n      let prevBorder = null;\n      for (const border of borders) {\n        if (prevBorder !== null) {\n          match = style[border + \"Width\"] === style[prevBorder + \"Width\"] && style[border + \"Color\"] === style[prevBorder + \"Color\"] && style[border + \"Style\"] === style[prevBorder + \"Style\"];\n        }\n        if (match === false) break;\n        prevBorder = border;\n      }\n      if (match === true) {\n        const width2 = parseFloat(style.borderTopWidth);\n        if (style.borderTopWidth !== \"0px\" && style.borderTopStyle !== \"none\" && style.borderTopColor !== \"transparent\" && style.borderTopColor !== \"rgba(0, 0, 0, 0)\") {\n          context.strokeStyle = style.borderTopColor;\n          context.lineWidth = width2;\n          context.stroke();\n        }\n      } else {\n        drawBorder(style, \"borderTop\", x, y, width, 0);\n        drawBorder(style, \"borderLeft\", x, y, 0, height);\n        drawBorder(style, \"borderBottom\", x, y + height, width, 0);\n        drawBorder(style, \"borderRight\", x + width, y, 0, height);\n      }\n      if (element2 instanceof HTMLInputElement) {\n        let accentColor = style.accentColor;\n        if (accentColor === void 0 || accentColor === \"auto\") accentColor = style.color;\n        color.set(accentColor);\n        const luminance = Math.sqrt(0.299 * color.r ** 2 + 0.587 * color.g ** 2 + 0.114 * color.b ** 2);\n        const accentTextColor = luminance < 0.5 ? \"white\" : \"#111111\";\n        if (element2.type === \"radio\") {\n          buildRectPath(x, y, width, height, height);\n          context.fillStyle = \"white\";\n          context.strokeStyle = accentColor;\n          context.lineWidth = 1;\n          context.fill();\n          context.stroke();\n          if (element2.checked) {\n            buildRectPath(x + 2, y + 2, width - 4, height - 4, height);\n            context.fillStyle = accentColor;\n            context.strokeStyle = accentTextColor;\n            context.lineWidth = 2;\n            context.fill();\n            context.stroke();\n          }\n        }\n        if (element2.type === \"checkbox\") {\n          buildRectPath(x, y, width, height, 2);\n          context.fillStyle = element2.checked ? accentColor : \"white\";\n          context.strokeStyle = element2.checked ? accentTextColor : accentColor;\n          context.lineWidth = 1;\n          context.stroke();\n          context.fill();\n          if (element2.checked) {\n            const currentTextAlign = context.textAlign;\n            context.textAlign = \"center\";\n            const properties = {\n              color: accentTextColor,\n              fontFamily: style.fontFamily,\n              fontSize: height + \"px\",\n              fontWeight: \"bold\"\n            };\n            drawText(properties, x + width / 2, y, \"✔\");\n            context.textAlign = currentTextAlign;\n          }\n        }\n        if (element2.type === \"range\") {\n          const [min, max, value] = [\"min\", \"max\", \"value\"].map(property => parseFloat(element2[property]));\n          const position = (value - min) / (max - min) * (width - height);\n          buildRectPath(x, y + height / 4, width, height / 2, height / 4);\n          context.fillStyle = accentTextColor;\n          context.strokeStyle = accentColor;\n          context.lineWidth = 1;\n          context.fill();\n          context.stroke();\n          buildRectPath(x, y + height / 4, position + height / 2, height / 2, height / 4);\n          context.fillStyle = accentColor;\n          context.fill();\n          buildRectPath(x + position, y, height, height, height / 2);\n          context.fillStyle = accentColor;\n          context.fill();\n        }\n        if (element2.type === \"color\" || element2.type === \"text\" || element2.type === \"number\") {\n          clipper.add({\n            x,\n            y,\n            width,\n            height\n          });\n          drawText(style, x + parseInt(style.paddingLeft), y + parseInt(style.paddingTop), element2.value);\n          clipper.remove();\n        }\n      }\n    }\n    const isClipping = style.overflow === \"auto\" || style.overflow === \"hidden\";\n    if (isClipping) clipper.add({\n      x,\n      y,\n      width,\n      height\n    });\n    for (let i = 0; i < element2.childNodes.length; i++) {\n      drawElement(element2.childNodes[i], style);\n    }\n    if (isClipping) clipper.remove();\n  }\n  const offset = element.getBoundingClientRect();\n  let canvas = canvases.get(element);\n  if (canvas === void 0) {\n    canvas = document.createElement(\"canvas\");\n    canvas.width = offset.width;\n    canvas.height = offset.height;\n    canvases.set(element, canvas);\n  }\n  const context = canvas.getContext(\"2d\"\n  /*, { alpha: false }*/);\n  const clipper = new Clipper(context);\n  drawElement(element);\n  return canvas;\n}\nfunction htmlevent(element, event, x, y) {\n  const mouseEventInit = {\n    clientX: x * element.offsetWidth + element.offsetLeft,\n    clientY: y * element.offsetHeight + element.offsetTop,\n    view: element.ownerDocument.defaultView\n  };\n  window.dispatchEvent(new MouseEvent(event, mouseEventInit));\n  const rect = element.getBoundingClientRect();\n  x = x * rect.width + rect.left;\n  y = y * rect.height + rect.top;\n  function traverse(element2) {\n    if (element2.nodeType !== Node.TEXT_NODE && element2.nodeType !== Node.COMMENT_NODE) {\n      const rect2 = element2.getBoundingClientRect();\n      if (x > rect2.left && x < rect2.right && y > rect2.top && y < rect2.bottom) {\n        element2.dispatchEvent(new MouseEvent(event, mouseEventInit));\n        if (element2 instanceof HTMLInputElement && element2.type === \"range\" && (event === \"mousedown\" || event === \"click\")) {\n          const [min, max] = [\"min\", \"max\"].map(property => parseFloat(element2[property]));\n          const width = rect2.width;\n          const offsetX = x - rect2.x;\n          const proportion = offsetX / width;\n          element2.value = min + (max - min) * proportion;\n          element2.dispatchEvent(new InputEvent(\"input\", {\n            bubbles: true\n          }));\n        }\n      }\n      for (let i = 0; i < element2.childNodes.length; i++) {\n        traverse(element2.childNodes[i]);\n      }\n    }\n  }\n  traverse(element);\n}\nexport { HTMLMesh };", "map": {"version": 3, "names": ["HTM<PERSON>esh", "<PERSON><PERSON>", "constructor", "dom", "texture", "HTMLTexture", "geometry", "PlaneGeometry", "image", "width", "height", "material", "MeshBasicMaterial", "map", "toneMapped", "transparent", "onEvent", "event", "dispatchDOMEvent", "addEventListener", "dispose", "canvases", "delete", "removeEventListener", "CanvasTexture", "html2canvas", "anisotropy", "colorSpace", "encoding", "minFilter", "LinearFilter", "magFilter", "observer", "MutationObserver", "scheduleUpdate", "setTimeout", "update", "config", "attributes", "childList", "subtree", "characterData", "observe", "data", "htmlevent", "type", "x", "y", "needsUpdate", "disconnect", "clearTimeout", "WeakMap", "element", "range", "document", "createRange", "color", "Color", "Clipper", "context2", "clips", "isClipping", "do<PERSON><PERSON>", "restore", "length", "minX", "Infinity", "minY", "maxX", "maxY", "i", "clip", "Math", "max", "min", "save", "beginPath", "rect", "add", "push", "remove", "pop", "drawText", "style", "string", "textTransform", "toUpperCase", "context", "font", "fontWeight", "fontSize", "fontFamily", "textBaseline", "fillStyle", "fillText", "parseFloat", "buildRectPath", "w", "h", "r", "moveTo", "arcTo", "closePath", "drawBorder", "which", "borderWidth", "borderStyle", "borderColor", "strokeStyle", "lineWidth", "lineTo", "stroke", "drawElement", "element2", "nodeType", "Node", "TEXT_NODE", "selectNode", "getBoundingClientRect", "left", "offset", "top", "nodeValue", "trim", "COMMENT_NODE", "HTMLCanvasElement", "display", "dpr", "window", "devicePixelRatio", "scale", "drawImage", "getComputedStyle", "borderRadius", "backgroundColor", "fill", "borders", "match", "prevBorder", "border", "width2", "borderTopWidth", "borderTopStyle", "borderTopColor", "HTMLInputElement", "accentColor", "set", "luminance", "sqrt", "g", "b", "accentTextColor", "checked", "currentTextAlign", "textAlign", "properties", "value", "property", "position", "clipper", "parseInt", "paddingLeft", "paddingTop", "overflow", "childNodes", "canvas", "get", "createElement", "getContext", "mouseEventInit", "clientX", "offsetWidth", "offsetLeft", "clientY", "offsetHeight", "offsetTop", "view", "ownerDocument", "defaultView", "dispatchEvent", "MouseEvent", "traverse", "rect2", "right", "bottom", "offsetX", "proportion", "InputEvent", "bubbles"], "sources": ["C:\\Users\\<USER>\\Documents\\Apps_Creations\\SiahMan_Racing4Speed_WebGame\\siah-man-racing\\node_modules\\src\\interactive\\HTMLMesh.js"], "sourcesContent": ["import { CanvasTexture, LinearFilter, Mesh, MeshBasicMaterial, PlaneGeometry, Color } from 'three'\n\nclass HTMLMesh extends Mesh {\n  constructor(dom) {\n    const texture = new HTMLTexture(dom)\n\n    const geometry = new PlaneGeometry(texture.image.width * 0.001, texture.image.height * 0.001)\n    const material = new MeshBasicMaterial({ map: texture, toneMapped: false, transparent: true })\n\n    super(geometry, material)\n\n    function onEvent(event) {\n      material.map.dispatchDOMEvent(event)\n    }\n\n    this.addEventListener('mousedown', onEvent)\n    this.addEventListener('mousemove', onEvent)\n    this.addEventListener('mouseup', onEvent)\n    this.addEventListener('click', onEvent)\n\n    this.dispose = function () {\n      geometry.dispose()\n      material.dispose()\n\n      material.map.dispose()\n\n      canvases.delete(dom)\n\n      this.removeEventListener('mousedown', onEvent)\n      this.removeEventListener('mousemove', onEvent)\n      this.removeEventListener('mouseup', onEvent)\n      this.removeEventListener('click', onEvent)\n    }\n  }\n}\n\nclass HTMLTexture extends CanvasTexture {\n  constructor(dom) {\n    super(html2canvas(dom))\n\n    this.dom = dom\n\n    this.anisotropy = 16\n    if ('colorSpace' in this) this.colorSpace = 'srgb'\n    else this.encoding = 3001 // sRGBEncoding\n    this.minFilter = LinearFilter\n    this.magFilter = LinearFilter\n\n    // Create an observer on the DOM, and run html2canvas update in the next loop\n    const observer = new MutationObserver(() => {\n      if (!this.scheduleUpdate) {\n        // ideally should use xr.requestAnimationFrame, here setTimeout to avoid passing the renderer\n        this.scheduleUpdate = setTimeout(() => this.update(), 16)\n      }\n    })\n\n    const config = { attributes: true, childList: true, subtree: true, characterData: true }\n    observer.observe(dom, config)\n\n    this.observer = observer\n  }\n\n  dispatchDOMEvent(event) {\n    if (event.data) {\n      htmlevent(this.dom, event.type, event.data.x, event.data.y)\n    }\n  }\n\n  update() {\n    this.image = html2canvas(this.dom)\n    this.needsUpdate = true\n\n    this.scheduleUpdate = null\n  }\n\n  dispose() {\n    if (this.observer) {\n      this.observer.disconnect()\n    }\n\n    this.scheduleUpdate = clearTimeout(this.scheduleUpdate)\n\n    super.dispose()\n  }\n}\n\n//\n\nconst canvases = new WeakMap()\n\nfunction html2canvas(element) {\n  const range = document.createRange()\n  const color = new Color()\n\n  function Clipper(context) {\n    const clips = []\n    let isClipping = false\n\n    function doClip() {\n      if (isClipping) {\n        isClipping = false\n        context.restore()\n      }\n\n      if (clips.length === 0) return\n\n      let minX = -Infinity,\n        minY = -Infinity\n      let maxX = Infinity,\n        maxY = Infinity\n\n      for (let i = 0; i < clips.length; i++) {\n        const clip = clips[i]\n\n        minX = Math.max(minX, clip.x)\n        minY = Math.max(minY, clip.y)\n        maxX = Math.min(maxX, clip.x + clip.width)\n        maxY = Math.min(maxY, clip.y + clip.height)\n      }\n\n      context.save()\n      context.beginPath()\n      context.rect(minX, minY, maxX - minX, maxY - minY)\n      context.clip()\n\n      isClipping = true\n    }\n\n    return {\n      add: function (clip) {\n        clips.push(clip)\n        doClip()\n      },\n\n      remove: function () {\n        clips.pop()\n        doClip()\n      },\n    }\n  }\n\n  function drawText(style, x, y, string) {\n    if (string !== '') {\n      if (style.textTransform === 'uppercase') {\n        string = string.toUpperCase()\n      }\n\n      context.font = style.fontWeight + ' ' + style.fontSize + ' ' + style.fontFamily\n      context.textBaseline = 'top'\n      context.fillStyle = style.color\n      context.fillText(string, x, y + parseFloat(style.fontSize) * 0.1)\n    }\n  }\n\n  function buildRectPath(x, y, w, h, r) {\n    if (w < 2 * r) r = w / 2\n    if (h < 2 * r) r = h / 2\n\n    context.beginPath()\n    context.moveTo(x + r, y)\n    context.arcTo(x + w, y, x + w, y + h, r)\n    context.arcTo(x + w, y + h, x, y + h, r)\n    context.arcTo(x, y + h, x, y, r)\n    context.arcTo(x, y, x + w, y, r)\n    context.closePath()\n  }\n\n  function drawBorder(style, which, x, y, width, height) {\n    const borderWidth = style[which + 'Width']\n    const borderStyle = style[which + 'Style']\n    const borderColor = style[which + 'Color']\n\n    if (\n      borderWidth !== '0px' &&\n      borderStyle !== 'none' &&\n      borderColor !== 'transparent' &&\n      borderColor !== 'rgba(0, 0, 0, 0)'\n    ) {\n      context.strokeStyle = borderColor\n      context.lineWidth = parseFloat(borderWidth)\n      context.beginPath()\n      context.moveTo(x, y)\n      context.lineTo(x + width, y + height)\n      context.stroke()\n    }\n  }\n\n  function drawElement(element, style) {\n    let x = 0,\n      y = 0,\n      width = 0,\n      height = 0\n\n    if (element.nodeType === Node.TEXT_NODE) {\n      // text\n\n      range.selectNode(element)\n\n      const rect = range.getBoundingClientRect()\n\n      x = rect.left - offset.left - 0.5\n      y = rect.top - offset.top - 0.5\n      width = rect.width\n      height = rect.height\n\n      drawText(style, x, y, element.nodeValue.trim())\n    } else if (element.nodeType === Node.COMMENT_NODE) {\n      return\n    } else if (element instanceof HTMLCanvasElement) {\n      // Canvas element\n      if (element.style.display === 'none') return\n\n      context.save()\n      const dpr = window.devicePixelRatio\n      context.scale(1 / dpr, 1 / dpr)\n      context.drawImage(element, 0, 0)\n      context.restore()\n    } else {\n      if (element.style.display === 'none') return\n\n      const rect = element.getBoundingClientRect()\n\n      x = rect.left - offset.left - 0.5\n      y = rect.top - offset.top - 0.5\n      width = rect.width\n      height = rect.height\n\n      style = window.getComputedStyle(element)\n\n      // Get the border of the element used for fill and border\n\n      buildRectPath(x, y, width, height, parseFloat(style.borderRadius))\n\n      const backgroundColor = style.backgroundColor\n\n      if (backgroundColor !== 'transparent' && backgroundColor !== 'rgba(0, 0, 0, 0)') {\n        context.fillStyle = backgroundColor\n        context.fill()\n      }\n\n      // If all the borders match then stroke the round rectangle\n\n      const borders = ['borderTop', 'borderLeft', 'borderBottom', 'borderRight']\n\n      let match = true\n      let prevBorder = null\n\n      for (const border of borders) {\n        if (prevBorder !== null) {\n          match =\n            style[border + 'Width'] === style[prevBorder + 'Width'] &&\n            style[border + 'Color'] === style[prevBorder + 'Color'] &&\n            style[border + 'Style'] === style[prevBorder + 'Style']\n        }\n\n        if (match === false) break\n\n        prevBorder = border\n      }\n\n      if (match === true) {\n        // They all match so stroke the rectangle from before allows for border-radius\n\n        const width = parseFloat(style.borderTopWidth)\n\n        if (\n          style.borderTopWidth !== '0px' &&\n          style.borderTopStyle !== 'none' &&\n          style.borderTopColor !== 'transparent' &&\n          style.borderTopColor !== 'rgba(0, 0, 0, 0)'\n        ) {\n          context.strokeStyle = style.borderTopColor\n          context.lineWidth = width\n          context.stroke()\n        }\n      } else {\n        // Otherwise draw individual borders\n\n        drawBorder(style, 'borderTop', x, y, width, 0)\n        drawBorder(style, 'borderLeft', x, y, 0, height)\n        drawBorder(style, 'borderBottom', x, y + height, width, 0)\n        drawBorder(style, 'borderRight', x + width, y, 0, height)\n      }\n\n      if (element instanceof HTMLInputElement) {\n        let accentColor = style.accentColor\n\n        if (accentColor === undefined || accentColor === 'auto') accentColor = style.color\n\n        color.set(accentColor)\n\n        const luminance = Math.sqrt(0.299 * color.r ** 2 + 0.587 * color.g ** 2 + 0.114 * color.b ** 2)\n        const accentTextColor = luminance < 0.5 ? 'white' : '#111111'\n\n        if (element.type === 'radio') {\n          buildRectPath(x, y, width, height, height)\n\n          context.fillStyle = 'white'\n          context.strokeStyle = accentColor\n          context.lineWidth = 1\n          context.fill()\n          context.stroke()\n\n          if (element.checked) {\n            buildRectPath(x + 2, y + 2, width - 4, height - 4, height)\n\n            context.fillStyle = accentColor\n            context.strokeStyle = accentTextColor\n            context.lineWidth = 2\n            context.fill()\n            context.stroke()\n          }\n        }\n\n        if (element.type === 'checkbox') {\n          buildRectPath(x, y, width, height, 2)\n\n          context.fillStyle = element.checked ? accentColor : 'white'\n          context.strokeStyle = element.checked ? accentTextColor : accentColor\n          context.lineWidth = 1\n          context.stroke()\n          context.fill()\n\n          if (element.checked) {\n            const currentTextAlign = context.textAlign\n\n            context.textAlign = 'center'\n\n            const properties = {\n              color: accentTextColor,\n              fontFamily: style.fontFamily,\n              fontSize: height + 'px',\n              fontWeight: 'bold',\n            }\n\n            drawText(properties, x + width / 2, y, '✔')\n\n            context.textAlign = currentTextAlign\n          }\n        }\n\n        if (element.type === 'range') {\n          const [min, max, value] = ['min', 'max', 'value'].map((property) => parseFloat(element[property]))\n          const position = ((value - min) / (max - min)) * (width - height)\n\n          buildRectPath(x, y + height / 4, width, height / 2, height / 4)\n          context.fillStyle = accentTextColor\n          context.strokeStyle = accentColor\n          context.lineWidth = 1\n          context.fill()\n          context.stroke()\n\n          buildRectPath(x, y + height / 4, position + height / 2, height / 2, height / 4)\n          context.fillStyle = accentColor\n          context.fill()\n\n          buildRectPath(x + position, y, height, height, height / 2)\n          context.fillStyle = accentColor\n          context.fill()\n        }\n\n        if (element.type === 'color' || element.type === 'text' || element.type === 'number') {\n          clipper.add({ x: x, y: y, width: width, height: height })\n\n          drawText(style, x + parseInt(style.paddingLeft), y + parseInt(style.paddingTop), element.value)\n\n          clipper.remove()\n        }\n      }\n    }\n\n    /*\n\t\t// debug\n\t\tcontext.strokeStyle = '#' + Math.random().toString( 16 ).slice( - 3 );\n\t\tcontext.strokeRect( x - 0.5, y - 0.5, width + 1, height + 1 );\n\t\t*/\n\n    const isClipping = style.overflow === 'auto' || style.overflow === 'hidden'\n\n    if (isClipping) clipper.add({ x: x, y: y, width: width, height: height })\n\n    for (let i = 0; i < element.childNodes.length; i++) {\n      drawElement(element.childNodes[i], style)\n    }\n\n    if (isClipping) clipper.remove()\n  }\n\n  const offset = element.getBoundingClientRect()\n\n  let canvas = canvases.get(element)\n\n  if (canvas === undefined) {\n    canvas = document.createElement('canvas')\n    canvas.width = offset.width\n    canvas.height = offset.height\n    canvases.set(element, canvas)\n  }\n\n  const context = canvas.getContext('2d' /*, { alpha: false }*/)\n\n  const clipper = new Clipper(context)\n\n  // console.time( 'drawElement' );\n\n  drawElement(element)\n\n  // console.timeEnd( 'drawElement' );\n\n  return canvas\n}\n\nfunction htmlevent(element, event, x, y) {\n  const mouseEventInit = {\n    clientX: x * element.offsetWidth + element.offsetLeft,\n    clientY: y * element.offsetHeight + element.offsetTop,\n    view: element.ownerDocument.defaultView,\n  }\n\n  window.dispatchEvent(new MouseEvent(event, mouseEventInit))\n\n  const rect = element.getBoundingClientRect()\n\n  x = x * rect.width + rect.left\n  y = y * rect.height + rect.top\n\n  function traverse(element) {\n    if (element.nodeType !== Node.TEXT_NODE && element.nodeType !== Node.COMMENT_NODE) {\n      const rect = element.getBoundingClientRect()\n\n      if (x > rect.left && x < rect.right && y > rect.top && y < rect.bottom) {\n        element.dispatchEvent(new MouseEvent(event, mouseEventInit))\n\n        if (\n          element instanceof HTMLInputElement &&\n          element.type === 'range' &&\n          (event === 'mousedown' || event === 'click')\n        ) {\n          const [min, max] = ['min', 'max'].map((property) => parseFloat(element[property]))\n\n          const width = rect.width\n          const offsetX = x - rect.x\n          const proportion = offsetX / width\n          element.value = min + (max - min) * proportion\n          element.dispatchEvent(new InputEvent('input', { bubbles: true }))\n        }\n      }\n\n      for (let i = 0; i < element.childNodes.length; i++) {\n        traverse(element.childNodes[i])\n      }\n    }\n  }\n\n  traverse(element)\n}\n\nexport { HTMLMesh }\n"], "mappings": ";AAEA,MAAMA,QAAA,SAAiBC,IAAA,CAAK;EAC1BC,YAAYC,GAAA,EAAK;IACf,MAAMC,OAAA,GAAU,IAAIC,WAAA,CAAYF,GAAG;IAEnC,MAAMG,QAAA,GAAW,IAAIC,aAAA,CAAcH,OAAA,CAAQI,KAAA,CAAMC,KAAA,GAAQ,MAAOL,OAAA,CAAQI,KAAA,CAAME,MAAA,GAAS,IAAK;IAC5F,MAAMC,QAAA,GAAW,IAAIC,iBAAA,CAAkB;MAAEC,GAAA,EAAKT,OAAA;MAASU,UAAA,EAAY;MAAOC,WAAA,EAAa;IAAA,CAAM;IAE7F,MAAMT,QAAA,EAAUK,QAAQ;IAExB,SAASK,QAAQC,KAAA,EAAO;MACtBN,QAAA,CAASE,GAAA,CAAIK,gBAAA,CAAiBD,KAAK;IACpC;IAED,KAAKE,gBAAA,CAAiB,aAAaH,OAAO;IAC1C,KAAKG,gBAAA,CAAiB,aAAaH,OAAO;IAC1C,KAAKG,gBAAA,CAAiB,WAAWH,OAAO;IACxC,KAAKG,gBAAA,CAAiB,SAASH,OAAO;IAEtC,KAAKI,OAAA,GAAU,YAAY;MACzBd,QAAA,CAASc,OAAA,CAAS;MAClBT,QAAA,CAASS,OAAA,CAAS;MAElBT,QAAA,CAASE,GAAA,CAAIO,OAAA,CAAS;MAEtBC,QAAA,CAASC,MAAA,CAAOnB,GAAG;MAEnB,KAAKoB,mBAAA,CAAoB,aAAaP,OAAO;MAC7C,KAAKO,mBAAA,CAAoB,aAAaP,OAAO;MAC7C,KAAKO,mBAAA,CAAoB,WAAWP,OAAO;MAC3C,KAAKO,mBAAA,CAAoB,SAASP,OAAO;IAC1C;EACF;AACH;AAEA,MAAMX,WAAA,SAAoBmB,aAAA,CAAc;EACtCtB,YAAYC,GAAA,EAAK;IACf,MAAMsB,WAAA,CAAYtB,GAAG,CAAC;IAEtB,KAAKA,GAAA,GAAMA,GAAA;IAEX,KAAKuB,UAAA,GAAa;IAClB,IAAI,gBAAgB,MAAM,KAAKC,UAAA,GAAa,YACvC,KAAKC,QAAA,GAAW;IACrB,KAAKC,SAAA,GAAYC,YAAA;IACjB,KAAKC,SAAA,GAAYD,YAAA;IAGjB,MAAME,QAAA,GAAW,IAAIC,gBAAA,CAAiB,MAAM;MAC1C,IAAI,CAAC,KAAKC,cAAA,EAAgB;QAExB,KAAKA,cAAA,GAAiBC,UAAA,CAAW,MAAM,KAAKC,MAAA,CAAQ,GAAE,EAAE;MACzD;IACP,CAAK;IAED,MAAMC,MAAA,GAAS;MAAEC,UAAA,EAAY;MAAMC,SAAA,EAAW;MAAMC,OAAA,EAAS;MAAMC,aAAA,EAAe;IAAM;IACxFT,QAAA,CAASU,OAAA,CAAQvC,GAAA,EAAKkC,MAAM;IAE5B,KAAKL,QAAA,GAAWA,QAAA;EACjB;EAEDd,iBAAiBD,KAAA,EAAO;IACtB,IAAIA,KAAA,CAAM0B,IAAA,EAAM;MACdC,SAAA,CAAU,KAAKzC,GAAA,EAAKc,KAAA,CAAM4B,IAAA,EAAM5B,KAAA,CAAM0B,IAAA,CAAKG,CAAA,EAAG7B,KAAA,CAAM0B,IAAA,CAAKI,CAAC;IAC3D;EACF;EAEDX,OAAA,EAAS;IACP,KAAK5B,KAAA,GAAQiB,WAAA,CAAY,KAAKtB,GAAG;IACjC,KAAK6C,WAAA,GAAc;IAEnB,KAAKd,cAAA,GAAiB;EACvB;EAEDd,QAAA,EAAU;IACR,IAAI,KAAKY,QAAA,EAAU;MACjB,KAAKA,QAAA,CAASiB,UAAA,CAAY;IAC3B;IAED,KAAKf,cAAA,GAAiBgB,YAAA,CAAa,KAAKhB,cAAc;IAEtD,MAAMd,OAAA,CAAS;EAChB;AACH;AAIA,MAAMC,QAAA,GAAW,mBAAI8B,OAAA,CAAS;AAE9B,SAAS1B,YAAY2B,OAAA,EAAS;EAC5B,MAAMC,KAAA,GAAQC,QAAA,CAASC,WAAA,CAAa;EACpC,MAAMC,KAAA,GAAQ,IAAIC,KAAA,CAAO;EAEzB,SAASC,QAAQC,QAAA,EAAS;IACxB,MAAMC,KAAA,GAAQ,EAAE;IAChB,IAAIC,UAAA,GAAa;IAEjB,SAASC,OAAA,EAAS;MAChB,IAAID,UAAA,EAAY;QACdA,UAAA,GAAa;QACbF,QAAA,CAAQI,OAAA,CAAS;MAClB;MAED,IAAIH,KAAA,CAAMI,MAAA,KAAW,GAAG;MAExB,IAAIC,IAAA,GAAO,CAAAC,QAAA;QACTC,IAAA,GAAO,CAAAD,QAAA;MACT,IAAIE,IAAA,GAAOF,QAAA;QACTG,IAAA,GAAOH,QAAA;MAET,SAASI,CAAA,GAAI,GAAGA,CAAA,GAAIV,KAAA,CAAMI,MAAA,EAAQM,CAAA,IAAK;QACrC,MAAMC,IAAA,GAAOX,KAAA,CAAMU,CAAC;QAEpBL,IAAA,GAAOO,IAAA,CAAKC,GAAA,CAAIR,IAAA,EAAMM,IAAA,CAAKzB,CAAC;QAC5BqB,IAAA,GAAOK,IAAA,CAAKC,GAAA,CAAIN,IAAA,EAAMI,IAAA,CAAKxB,CAAC;QAC5BqB,IAAA,GAAOI,IAAA,CAAKE,GAAA,CAAIN,IAAA,EAAMG,IAAA,CAAKzB,CAAA,GAAIyB,IAAA,CAAK9D,KAAK;QACzC4D,IAAA,GAAOG,IAAA,CAAKE,GAAA,CAAIL,IAAA,EAAME,IAAA,CAAKxB,CAAA,GAAIwB,IAAA,CAAK7D,MAAM;MAC3C;MAEDiD,QAAA,CAAQgB,IAAA,CAAM;MACdhB,QAAA,CAAQiB,SAAA,CAAW;MACnBjB,QAAA,CAAQkB,IAAA,CAAKZ,IAAA,EAAME,IAAA,EAAMC,IAAA,GAAOH,IAAA,EAAMI,IAAA,GAAOF,IAAI;MACjDR,QAAA,CAAQY,IAAA,CAAM;MAEdV,UAAA,GAAa;IACd;IAED,OAAO;MACLiB,GAAA,EAAK,SAAAA,CAAUP,IAAA,EAAM;QACnBX,KAAA,CAAMmB,IAAA,CAAKR,IAAI;QACfT,MAAA,CAAQ;MACT;MAEDkB,MAAA,EAAQ,SAAAA,CAAA,EAAY;QAClBpB,KAAA,CAAMqB,GAAA,CAAK;QACXnB,MAAA,CAAQ;MACT;IACF;EACF;EAED,SAASoB,SAASC,KAAA,EAAOrC,CAAA,EAAGC,CAAA,EAAGqC,MAAA,EAAQ;IACrC,IAAIA,MAAA,KAAW,IAAI;MACjB,IAAID,KAAA,CAAME,aAAA,KAAkB,aAAa;QACvCD,MAAA,GAASA,MAAA,CAAOE,WAAA,CAAa;MAC9B;MAEDC,OAAA,CAAQC,IAAA,GAAOL,KAAA,CAAMM,UAAA,GAAa,MAAMN,KAAA,CAAMO,QAAA,GAAW,MAAMP,KAAA,CAAMQ,UAAA;MACrEJ,OAAA,CAAQK,YAAA,GAAe;MACvBL,OAAA,CAAQM,SAAA,GAAYV,KAAA,CAAM3B,KAAA;MAC1B+B,OAAA,CAAQO,QAAA,CAASV,MAAA,EAAQtC,CAAA,EAAGC,CAAA,GAAIgD,UAAA,CAAWZ,KAAA,CAAMO,QAAQ,IAAI,GAAG;IACjE;EACF;EAED,SAASM,cAAclD,CAAA,EAAGC,CAAA,EAAGkD,CAAA,EAAGC,CAAA,EAAGC,CAAA,EAAG;IACpC,IAAIF,CAAA,GAAI,IAAIE,CAAA,EAAGA,CAAA,GAAIF,CAAA,GAAI;IACvB,IAAIC,CAAA,GAAI,IAAIC,CAAA,EAAGA,CAAA,GAAID,CAAA,GAAI;IAEvBX,OAAA,CAAQX,SAAA,CAAW;IACnBW,OAAA,CAAQa,MAAA,CAAOtD,CAAA,GAAIqD,CAAA,EAAGpD,CAAC;IACvBwC,OAAA,CAAQc,KAAA,CAAMvD,CAAA,GAAImD,CAAA,EAAGlD,CAAA,EAAGD,CAAA,GAAImD,CAAA,EAAGlD,CAAA,GAAImD,CAAA,EAAGC,CAAC;IACvCZ,OAAA,CAAQc,KAAA,CAAMvD,CAAA,GAAImD,CAAA,EAAGlD,CAAA,GAAImD,CAAA,EAAGpD,CAAA,EAAGC,CAAA,GAAImD,CAAA,EAAGC,CAAC;IACvCZ,OAAA,CAAQc,KAAA,CAAMvD,CAAA,EAAGC,CAAA,GAAImD,CAAA,EAAGpD,CAAA,EAAGC,CAAA,EAAGoD,CAAC;IAC/BZ,OAAA,CAAQc,KAAA,CAAMvD,CAAA,EAAGC,CAAA,EAAGD,CAAA,GAAImD,CAAA,EAAGlD,CAAA,EAAGoD,CAAC;IAC/BZ,OAAA,CAAQe,SAAA,CAAW;EACpB;EAED,SAASC,WAAWpB,KAAA,EAAOqB,KAAA,EAAO1D,CAAA,EAAGC,CAAA,EAAGtC,KAAA,EAAOC,MAAA,EAAQ;IACrD,MAAM+F,WAAA,GAActB,KAAA,CAAMqB,KAAA,GAAQ,OAAO;IACzC,MAAME,WAAA,GAAcvB,KAAA,CAAMqB,KAAA,GAAQ,OAAO;IACzC,MAAMG,WAAA,GAAcxB,KAAA,CAAMqB,KAAA,GAAQ,OAAO;IAEzC,IACEC,WAAA,KAAgB,SAChBC,WAAA,KAAgB,UAChBC,WAAA,KAAgB,iBAChBA,WAAA,KAAgB,oBAChB;MACApB,OAAA,CAAQqB,WAAA,GAAcD,WAAA;MACtBpB,OAAA,CAAQsB,SAAA,GAAYd,UAAA,CAAWU,WAAW;MAC1ClB,OAAA,CAAQX,SAAA,CAAW;MACnBW,OAAA,CAAQa,MAAA,CAAOtD,CAAA,EAAGC,CAAC;MACnBwC,OAAA,CAAQuB,MAAA,CAAOhE,CAAA,GAAIrC,KAAA,EAAOsC,CAAA,GAAIrC,MAAM;MACpC6E,OAAA,CAAQwB,MAAA,CAAQ;IACjB;EACF;EAED,SAASC,YAAYC,QAAA,EAAS9B,KAAA,EAAO;IACnC,IAAIrC,CAAA,GAAI;MACNC,CAAA,GAAI;MACJtC,KAAA,GAAQ;MACRC,MAAA,GAAS;IAEX,IAAIuG,QAAA,CAAQC,QAAA,KAAaC,IAAA,CAAKC,SAAA,EAAW;MAGvC/D,KAAA,CAAMgE,UAAA,CAAWJ,QAAO;MAExB,MAAMpC,IAAA,GAAOxB,KAAA,CAAMiE,qBAAA,CAAuB;MAE1CxE,CAAA,GAAI+B,IAAA,CAAK0C,IAAA,GAAOC,MAAA,CAAOD,IAAA,GAAO;MAC9BxE,CAAA,GAAI8B,IAAA,CAAK4C,GAAA,GAAMD,MAAA,CAAOC,GAAA,GAAM;MAC5BhH,KAAA,GAAQoE,IAAA,CAAKpE,KAAA;MACbC,MAAA,GAASmE,IAAA,CAAKnE,MAAA;MAEdwE,QAAA,CAASC,KAAA,EAAOrC,CAAA,EAAGC,CAAA,EAAGkE,QAAA,CAAQS,SAAA,CAAUC,IAAA,EAAM;IAC/C,WAAUV,QAAA,CAAQC,QAAA,KAAaC,IAAA,CAAKS,YAAA,EAAc;MACjD;IACN,WAAeX,QAAA,YAAmBY,iBAAA,EAAmB;MAE/C,IAAIZ,QAAA,CAAQ9B,KAAA,CAAM2C,OAAA,KAAY,QAAQ;MAEtCvC,OAAA,CAAQZ,IAAA,CAAM;MACd,MAAMoD,GAAA,GAAMC,MAAA,CAAOC,gBAAA;MACnB1C,OAAA,CAAQ2C,KAAA,CAAM,IAAIH,GAAA,EAAK,IAAIA,GAAG;MAC9BxC,OAAA,CAAQ4C,SAAA,CAAUlB,QAAA,EAAS,GAAG,CAAC;MAC/B1B,OAAA,CAAQxB,OAAA,CAAS;IACvB,OAAW;MACL,IAAIkD,QAAA,CAAQ9B,KAAA,CAAM2C,OAAA,KAAY,QAAQ;MAEtC,MAAMjD,IAAA,GAAOoC,QAAA,CAAQK,qBAAA,CAAuB;MAE5CxE,CAAA,GAAI+B,IAAA,CAAK0C,IAAA,GAAOC,MAAA,CAAOD,IAAA,GAAO;MAC9BxE,CAAA,GAAI8B,IAAA,CAAK4C,GAAA,GAAMD,MAAA,CAAOC,GAAA,GAAM;MAC5BhH,KAAA,GAAQoE,IAAA,CAAKpE,KAAA;MACbC,MAAA,GAASmE,IAAA,CAAKnE,MAAA;MAEdyE,KAAA,GAAQ6C,MAAA,CAAOI,gBAAA,CAAiBnB,QAAO;MAIvCjB,aAAA,CAAclD,CAAA,EAAGC,CAAA,EAAGtC,KAAA,EAAOC,MAAA,EAAQqF,UAAA,CAAWZ,KAAA,CAAMkD,YAAY,CAAC;MAEjE,MAAMC,eAAA,GAAkBnD,KAAA,CAAMmD,eAAA;MAE9B,IAAIA,eAAA,KAAoB,iBAAiBA,eAAA,KAAoB,oBAAoB;QAC/E/C,OAAA,CAAQM,SAAA,GAAYyC,eAAA;QACpB/C,OAAA,CAAQgD,IAAA,CAAM;MACf;MAID,MAAMC,OAAA,GAAU,CAAC,aAAa,cAAc,gBAAgB,aAAa;MAEzE,IAAIC,KAAA,GAAQ;MACZ,IAAIC,UAAA,GAAa;MAEjB,WAAWC,MAAA,IAAUH,OAAA,EAAS;QAC5B,IAAIE,UAAA,KAAe,MAAM;UACvBD,KAAA,GACEtD,KAAA,CAAMwD,MAAA,GAAS,OAAO,MAAMxD,KAAA,CAAMuD,UAAA,GAAa,OAAO,KACtDvD,KAAA,CAAMwD,MAAA,GAAS,OAAO,MAAMxD,KAAA,CAAMuD,UAAA,GAAa,OAAO,KACtDvD,KAAA,CAAMwD,MAAA,GAAS,OAAO,MAAMxD,KAAA,CAAMuD,UAAA,GAAa,OAAO;QACzD;QAED,IAAID,KAAA,KAAU,OAAO;QAErBC,UAAA,GAAaC,MAAA;MACd;MAED,IAAIF,KAAA,KAAU,MAAM;QAGlB,MAAMG,MAAA,GAAQ7C,UAAA,CAAWZ,KAAA,CAAM0D,cAAc;QAE7C,IACE1D,KAAA,CAAM0D,cAAA,KAAmB,SACzB1D,KAAA,CAAM2D,cAAA,KAAmB,UACzB3D,KAAA,CAAM4D,cAAA,KAAmB,iBACzB5D,KAAA,CAAM4D,cAAA,KAAmB,oBACzB;UACAxD,OAAA,CAAQqB,WAAA,GAAczB,KAAA,CAAM4D,cAAA;UAC5BxD,OAAA,CAAQsB,SAAA,GAAY+B,MAAA;UACpBrD,OAAA,CAAQwB,MAAA,CAAQ;QACjB;MACT,OAAa;QAGLR,UAAA,CAAWpB,KAAA,EAAO,aAAarC,CAAA,EAAGC,CAAA,EAAGtC,KAAA,EAAO,CAAC;QAC7C8F,UAAA,CAAWpB,KAAA,EAAO,cAAcrC,CAAA,EAAGC,CAAA,EAAG,GAAGrC,MAAM;QAC/C6F,UAAA,CAAWpB,KAAA,EAAO,gBAAgBrC,CAAA,EAAGC,CAAA,GAAIrC,MAAA,EAAQD,KAAA,EAAO,CAAC;QACzD8F,UAAA,CAAWpB,KAAA,EAAO,eAAerC,CAAA,GAAIrC,KAAA,EAAOsC,CAAA,EAAG,GAAGrC,MAAM;MACzD;MAED,IAAIuG,QAAA,YAAmB+B,gBAAA,EAAkB;QACvC,IAAIC,WAAA,GAAc9D,KAAA,CAAM8D,WAAA;QAExB,IAAIA,WAAA,KAAgB,UAAaA,WAAA,KAAgB,QAAQA,WAAA,GAAc9D,KAAA,CAAM3B,KAAA;QAE7EA,KAAA,CAAM0F,GAAA,CAAID,WAAW;QAErB,MAAME,SAAA,GAAY3E,IAAA,CAAK4E,IAAA,CAAK,QAAQ5F,KAAA,CAAM2C,CAAA,IAAK,IAAI,QAAQ3C,KAAA,CAAM6F,CAAA,IAAK,IAAI,QAAQ7F,KAAA,CAAM8F,CAAA,IAAK,CAAC;QAC9F,MAAMC,eAAA,GAAkBJ,SAAA,GAAY,MAAM,UAAU;QAEpD,IAAIlC,QAAA,CAAQpE,IAAA,KAAS,SAAS;UAC5BmD,aAAA,CAAclD,CAAA,EAAGC,CAAA,EAAGtC,KAAA,EAAOC,MAAA,EAAQA,MAAM;UAEzC6E,OAAA,CAAQM,SAAA,GAAY;UACpBN,OAAA,CAAQqB,WAAA,GAAcqC,WAAA;UACtB1D,OAAA,CAAQsB,SAAA,GAAY;UACpBtB,OAAA,CAAQgD,IAAA,CAAM;UACdhD,OAAA,CAAQwB,MAAA,CAAQ;UAEhB,IAAIE,QAAA,CAAQuC,OAAA,EAAS;YACnBxD,aAAA,CAAclD,CAAA,GAAI,GAAGC,CAAA,GAAI,GAAGtC,KAAA,GAAQ,GAAGC,MAAA,GAAS,GAAGA,MAAM;YAEzD6E,OAAA,CAAQM,SAAA,GAAYoD,WAAA;YACpB1D,OAAA,CAAQqB,WAAA,GAAc2C,eAAA;YACtBhE,OAAA,CAAQsB,SAAA,GAAY;YACpBtB,OAAA,CAAQgD,IAAA,CAAM;YACdhD,OAAA,CAAQwB,MAAA,CAAQ;UACjB;QACF;QAED,IAAIE,QAAA,CAAQpE,IAAA,KAAS,YAAY;UAC/BmD,aAAA,CAAclD,CAAA,EAAGC,CAAA,EAAGtC,KAAA,EAAOC,MAAA,EAAQ,CAAC;UAEpC6E,OAAA,CAAQM,SAAA,GAAYoB,QAAA,CAAQuC,OAAA,GAAUP,WAAA,GAAc;UACpD1D,OAAA,CAAQqB,WAAA,GAAcK,QAAA,CAAQuC,OAAA,GAAUD,eAAA,GAAkBN,WAAA;UAC1D1D,OAAA,CAAQsB,SAAA,GAAY;UACpBtB,OAAA,CAAQwB,MAAA,CAAQ;UAChBxB,OAAA,CAAQgD,IAAA,CAAM;UAEd,IAAItB,QAAA,CAAQuC,OAAA,EAAS;YACnB,MAAMC,gBAAA,GAAmBlE,OAAA,CAAQmE,SAAA;YAEjCnE,OAAA,CAAQmE,SAAA,GAAY;YAEpB,MAAMC,UAAA,GAAa;cACjBnG,KAAA,EAAO+F,eAAA;cACP5D,UAAA,EAAYR,KAAA,CAAMQ,UAAA;cAClBD,QAAA,EAAUhF,MAAA,GAAS;cACnB+E,UAAA,EAAY;YACb;YAEDP,QAAA,CAASyE,UAAA,EAAY7G,CAAA,GAAIrC,KAAA,GAAQ,GAAGsC,CAAA,EAAG,GAAG;YAE1CwC,OAAA,CAAQmE,SAAA,GAAYD,gBAAA;UACrB;QACF;QAED,IAAIxC,QAAA,CAAQpE,IAAA,KAAS,SAAS;UAC5B,MAAM,CAAC6B,GAAA,EAAKD,GAAA,EAAKmF,KAAK,IAAI,CAAC,OAAO,OAAO,OAAO,EAAE/I,GAAA,CAAKgJ,QAAA,IAAa9D,UAAA,CAAWkB,QAAA,CAAQ4C,QAAQ,CAAC,CAAC;UACjG,MAAMC,QAAA,IAAaF,KAAA,GAAQlF,GAAA,KAAQD,GAAA,GAAMC,GAAA,KAASjE,KAAA,GAAQC,MAAA;UAE1DsF,aAAA,CAAclD,CAAA,EAAGC,CAAA,GAAIrC,MAAA,GAAS,GAAGD,KAAA,EAAOC,MAAA,GAAS,GAAGA,MAAA,GAAS,CAAC;UAC9D6E,OAAA,CAAQM,SAAA,GAAY0D,eAAA;UACpBhE,OAAA,CAAQqB,WAAA,GAAcqC,WAAA;UACtB1D,OAAA,CAAQsB,SAAA,GAAY;UACpBtB,OAAA,CAAQgD,IAAA,CAAM;UACdhD,OAAA,CAAQwB,MAAA,CAAQ;UAEhBf,aAAA,CAAclD,CAAA,EAAGC,CAAA,GAAIrC,MAAA,GAAS,GAAGoJ,QAAA,GAAWpJ,MAAA,GAAS,GAAGA,MAAA,GAAS,GAAGA,MAAA,GAAS,CAAC;UAC9E6E,OAAA,CAAQM,SAAA,GAAYoD,WAAA;UACpB1D,OAAA,CAAQgD,IAAA,CAAM;UAEdvC,aAAA,CAAclD,CAAA,GAAIgH,QAAA,EAAU/G,CAAA,EAAGrC,MAAA,EAAQA,MAAA,EAAQA,MAAA,GAAS,CAAC;UACzD6E,OAAA,CAAQM,SAAA,GAAYoD,WAAA;UACpB1D,OAAA,CAAQgD,IAAA,CAAM;QACf;QAED,IAAItB,QAAA,CAAQpE,IAAA,KAAS,WAAWoE,QAAA,CAAQpE,IAAA,KAAS,UAAUoE,QAAA,CAAQpE,IAAA,KAAS,UAAU;UACpFkH,OAAA,CAAQjF,GAAA,CAAI;YAAEhC,CAAA;YAAMC,CAAA;YAAMtC,KAAA;YAAcC;UAAc,CAAE;UAExDwE,QAAA,CAASC,KAAA,EAAOrC,CAAA,GAAIkH,QAAA,CAAS7E,KAAA,CAAM8E,WAAW,GAAGlH,CAAA,GAAIiH,QAAA,CAAS7E,KAAA,CAAM+E,UAAU,GAAGjD,QAAA,CAAQ2C,KAAK;UAE9FG,OAAA,CAAQ/E,MAAA,CAAQ;QACjB;MACF;IACF;IAQD,MAAMnB,UAAA,GAAasB,KAAA,CAAMgF,QAAA,KAAa,UAAUhF,KAAA,CAAMgF,QAAA,KAAa;IAEnE,IAAItG,UAAA,EAAYkG,OAAA,CAAQjF,GAAA,CAAI;MAAEhC,CAAA;MAAMC,CAAA;MAAMtC,KAAA;MAAcC;IAAc,CAAE;IAExE,SAAS4D,CAAA,GAAI,GAAGA,CAAA,GAAI2C,QAAA,CAAQmD,UAAA,CAAWpG,MAAA,EAAQM,CAAA,IAAK;MAClD0C,WAAA,CAAYC,QAAA,CAAQmD,UAAA,CAAW9F,CAAC,GAAGa,KAAK;IACzC;IAED,IAAItB,UAAA,EAAYkG,OAAA,CAAQ/E,MAAA,CAAQ;EACjC;EAED,MAAMwC,MAAA,GAASpE,OAAA,CAAQkE,qBAAA,CAAuB;EAE9C,IAAI+C,MAAA,GAAShJ,QAAA,CAASiJ,GAAA,CAAIlH,OAAO;EAEjC,IAAIiH,MAAA,KAAW,QAAW;IACxBA,MAAA,GAAS/G,QAAA,CAASiH,aAAA,CAAc,QAAQ;IACxCF,MAAA,CAAO5J,KAAA,GAAQ+G,MAAA,CAAO/G,KAAA;IACtB4J,MAAA,CAAO3J,MAAA,GAAS8G,MAAA,CAAO9G,MAAA;IACvBW,QAAA,CAAS6H,GAAA,CAAI9F,OAAA,EAASiH,MAAM;EAC7B;EAED,MAAM9E,OAAA,GAAU8E,MAAA,CAAOG,UAAA,CAAW;EAAA,sBAA4B;EAE9D,MAAMT,OAAA,GAAU,IAAIrG,OAAA,CAAQ6B,OAAO;EAInCyB,WAAA,CAAY5D,OAAO;EAInB,OAAOiH,MAAA;AACT;AAEA,SAASzH,UAAUQ,OAAA,EAASnC,KAAA,EAAO6B,CAAA,EAAGC,CAAA,EAAG;EACvC,MAAM0H,cAAA,GAAiB;IACrBC,OAAA,EAAS5H,CAAA,GAAIM,OAAA,CAAQuH,WAAA,GAAcvH,OAAA,CAAQwH,UAAA;IAC3CC,OAAA,EAAS9H,CAAA,GAAIK,OAAA,CAAQ0H,YAAA,GAAe1H,OAAA,CAAQ2H,SAAA;IAC5CC,IAAA,EAAM5H,OAAA,CAAQ6H,aAAA,CAAcC;EAC7B;EAEDlD,MAAA,CAAOmD,aAAA,CAAc,IAAIC,UAAA,CAAWnK,KAAA,EAAOwJ,cAAc,CAAC;EAE1D,MAAM5F,IAAA,GAAOzB,OAAA,CAAQkE,qBAAA,CAAuB;EAE5CxE,CAAA,GAAIA,CAAA,GAAI+B,IAAA,CAAKpE,KAAA,GAAQoE,IAAA,CAAK0C,IAAA;EAC1BxE,CAAA,GAAIA,CAAA,GAAI8B,IAAA,CAAKnE,MAAA,GAASmE,IAAA,CAAK4C,GAAA;EAE3B,SAAS4D,SAASpE,QAAA,EAAS;IACzB,IAAIA,QAAA,CAAQC,QAAA,KAAaC,IAAA,CAAKC,SAAA,IAAaH,QAAA,CAAQC,QAAA,KAAaC,IAAA,CAAKS,YAAA,EAAc;MACjF,MAAM0D,KAAA,GAAOrE,QAAA,CAAQK,qBAAA,CAAuB;MAE5C,IAAIxE,CAAA,GAAIwI,KAAA,CAAK/D,IAAA,IAAQzE,CAAA,GAAIwI,KAAA,CAAKC,KAAA,IAASxI,CAAA,GAAIuI,KAAA,CAAK7D,GAAA,IAAO1E,CAAA,GAAIuI,KAAA,CAAKE,MAAA,EAAQ;QACtEvE,QAAA,CAAQkE,aAAA,CAAc,IAAIC,UAAA,CAAWnK,KAAA,EAAOwJ,cAAc,CAAC;QAE3D,IACExD,QAAA,YAAmB+B,gBAAA,IACnB/B,QAAA,CAAQpE,IAAA,KAAS,YAChB5B,KAAA,KAAU,eAAeA,KAAA,KAAU,UACpC;UACA,MAAM,CAACyD,GAAA,EAAKD,GAAG,IAAI,CAAC,OAAO,KAAK,EAAE5D,GAAA,CAAKgJ,QAAA,IAAa9D,UAAA,CAAWkB,QAAA,CAAQ4C,QAAQ,CAAC,CAAC;UAEjF,MAAMpJ,KAAA,GAAQ6K,KAAA,CAAK7K,KAAA;UACnB,MAAMgL,OAAA,GAAU3I,CAAA,GAAIwI,KAAA,CAAKxI,CAAA;UACzB,MAAM4I,UAAA,GAAaD,OAAA,GAAUhL,KAAA;UAC7BwG,QAAA,CAAQ2C,KAAA,GAAQlF,GAAA,IAAOD,GAAA,GAAMC,GAAA,IAAOgH,UAAA;UACpCzE,QAAA,CAAQkE,aAAA,CAAc,IAAIQ,UAAA,CAAW,SAAS;YAAEC,OAAA,EAAS;UAAI,CAAE,CAAC;QACjE;MACF;MAED,SAAStH,CAAA,GAAI,GAAGA,CAAA,GAAI2C,QAAA,CAAQmD,UAAA,CAAWpG,MAAA,EAAQM,CAAA,IAAK;QAClD+G,QAAA,CAASpE,QAAA,CAAQmD,UAAA,CAAW9F,CAAC,CAAC;MAC/B;IACF;EACF;EAED+G,QAAA,CAASjI,OAAO;AAClB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}