{"ast": null, "code": "class LWO2Parser {\n  constructor(IFFParser) {\n    this.IFF = IFFParser;\n  }\n  parseBlock() {\n    this.IFF.debugger.offset = this.IFF.reader.offset;\n    this.IFF.debugger.closeForms();\n    const blockID = this.IFF.reader.getIDTag();\n    let length = this.IFF.reader.getUint32();\n    if (length > this.IFF.reader.dv.byteLength - this.IFF.reader.offset) {\n      this.IFF.reader.offset -= 4;\n      length = this.IFF.reader.getUint16();\n    }\n    this.IFF.debugger.dataOffset = this.IFF.reader.offset;\n    this.IFF.debugger.length = length;\n    switch (blockID) {\n      case \"FORM\":\n        this.IFF.parseForm(length);\n        break;\n      case \"ICON\":\n      case \"VMPA\":\n      case \"BBOX\":\n      case \"NORM\":\n      case \"PRE \":\n      case \"POST\":\n      case \"KEY \":\n      case \"SPAN\":\n      case \"TIME\":\n      case \"CLRS\":\n      case \"CLRA\":\n      case \"FILT\":\n      case \"DITH\":\n      case \"CONT\":\n      case \"BRIT\":\n      case \"SATR\":\n      case \"HUE \":\n      case \"GAMM\":\n      case \"NEGA\":\n      case \"IFLT\":\n      case \"PFLT\":\n      case \"PROJ\":\n      case \"AXIS\":\n      case \"AAST\":\n      case \"PIXB\":\n      case \"AUVO\":\n      case \"STCK\":\n      case \"PROC\":\n      case \"VALU\":\n      case \"FUNC\":\n      case \"PNAM\":\n      case \"INAM\":\n      case \"GRST\":\n      case \"GREN\":\n      case \"GRPT\":\n      case \"FKEY\":\n      case \"IKEY\":\n      case \"CSYS\":\n      case \"OPAQ\":\n      case \"CMAP\":\n      case \"NLOC\":\n      case \"NZOM\":\n      case \"NVER\":\n      case \"NSRV\":\n      case \"NVSK\":\n      case \"NCRD\":\n      case \"WRPW\":\n      case \"WRPH\":\n      case \"NMOD\":\n      case \"NSEL\":\n      case \"NPRW\":\n      case \"NPLA\":\n      case \"NODS\":\n      case \"VERS\":\n      case \"ENUM\":\n      case \"TAG \":\n      case \"OPAC\":\n      case \"CGMD\":\n      case \"CGTY\":\n      case \"CGST\":\n      case \"CGEN\":\n      case \"CGTS\":\n      case \"CGTE\":\n      case \"OSMP\":\n      case \"OMDE\":\n      case \"OUTR\":\n      case \"FLAG\":\n      case \"TRNL\":\n      case \"GLOW\":\n      case \"GVAL\":\n      case \"SHRP\":\n      case \"RFOP\":\n      case \"RSAN\":\n      case \"TROP\":\n      case \"RBLR\":\n      case \"TBLR\":\n      case \"CLRH\":\n      case \"CLRF\":\n      case \"ADTR\":\n      case \"LINE\":\n      case \"ALPH\":\n      case \"VCOL\":\n      case \"ENAB\":\n        this.IFF.debugger.skipped = true;\n        this.IFF.reader.skip(length);\n        break;\n      case \"SURF\":\n        this.IFF.parseSurfaceLwo2(length);\n        break;\n      case \"CLIP\":\n        this.IFF.parseClipLwo2(length);\n        break;\n      case \"IPIX\":\n      case \"IMIP\":\n      case \"IMOD\":\n      case \"AMOD\":\n      case \"IINV\":\n      case \"INCR\":\n      case \"IAXS\":\n      case \"IFOT\":\n      case \"ITIM\":\n      case \"IWRL\":\n      case \"IUTI\":\n      case \"IINX\":\n      case \"IINY\":\n      case \"IINZ\":\n      case \"IREF\":\n        if (length === 4) this.IFF.currentNode[blockID] = this.IFF.reader.getInt32();else this.IFF.reader.skip(length);\n        break;\n      case \"OTAG\":\n        this.IFF.parseObjectTag();\n        break;\n      case \"LAYR\":\n        this.IFF.parseLayer(length);\n        break;\n      case \"PNTS\":\n        this.IFF.parsePoints(length);\n        break;\n      case \"VMAP\":\n        this.IFF.parseVertexMapping(length);\n        break;\n      case \"AUVU\":\n      case \"AUVN\":\n        this.IFF.reader.skip(length - 1);\n        this.IFF.reader.getVariableLengthIndex();\n        break;\n      case \"POLS\":\n        this.IFF.parsePolygonList(length);\n        break;\n      case \"TAGS\":\n        this.IFF.parseTagStrings(length);\n        break;\n      case \"PTAG\":\n        this.IFF.parsePolygonTagMapping(length);\n        break;\n      case \"VMAD\":\n        this.IFF.parseVertexMapping(length, true);\n        break;\n      case \"DESC\":\n        this.IFF.currentForm.description = this.IFF.reader.getString();\n        break;\n      case \"TEXT\":\n      case \"CMNT\":\n      case \"NCOM\":\n        this.IFF.currentForm.comment = this.IFF.reader.getString();\n        break;\n      case \"NAME\":\n        this.IFF.currentForm.channelName = this.IFF.reader.getString();\n        break;\n      case \"WRAP\":\n        this.IFF.currentForm.wrap = {\n          w: this.IFF.reader.getUint16(),\n          h: this.IFF.reader.getUint16()\n        };\n        break;\n      case \"IMAG\":\n        const index = this.IFF.reader.getVariableLengthIndex();\n        this.IFF.currentForm.imageIndex = index;\n        break;\n      case \"OREF\":\n        this.IFF.currentForm.referenceObject = this.IFF.reader.getString();\n        break;\n      case \"ROID\":\n        this.IFF.currentForm.referenceObjectID = this.IFF.reader.getUint32();\n        break;\n      case \"SSHN\":\n        this.IFF.currentSurface.surfaceShaderName = this.IFF.reader.getString();\n        break;\n      case \"AOVN\":\n        this.IFF.currentSurface.surfaceCustomAOVName = this.IFF.reader.getString();\n        break;\n      case \"NSTA\":\n        this.IFF.currentForm.disabled = this.IFF.reader.getUint16();\n        break;\n      case \"NRNM\":\n        this.IFF.currentForm.realName = this.IFF.reader.getString();\n        break;\n      case \"NNME\":\n        this.IFF.currentForm.refName = this.IFF.reader.getString();\n        this.IFF.currentSurface.nodes[this.IFF.currentForm.refName] = this.IFF.currentForm;\n        break;\n      case \"INME\":\n        if (!this.IFF.currentForm.nodeName) this.IFF.currentForm.nodeName = [];\n        this.IFF.currentForm.nodeName.push(this.IFF.reader.getString());\n        break;\n      case \"IINN\":\n        if (!this.IFF.currentForm.inputNodeName) this.IFF.currentForm.inputNodeName = [];\n        this.IFF.currentForm.inputNodeName.push(this.IFF.reader.getString());\n        break;\n      case \"IINM\":\n        if (!this.IFF.currentForm.inputName) this.IFF.currentForm.inputName = [];\n        this.IFF.currentForm.inputName.push(this.IFF.reader.getString());\n        break;\n      case \"IONM\":\n        if (!this.IFF.currentForm.inputOutputName) this.IFF.currentForm.inputOutputName = [];\n        this.IFF.currentForm.inputOutputName.push(this.IFF.reader.getString());\n        break;\n      case \"FNAM\":\n        this.IFF.currentForm.fileName = this.IFF.reader.getString();\n        break;\n      case \"CHAN\":\n        if (length === 4) this.IFF.currentForm.textureChannel = this.IFF.reader.getIDTag();else this.IFF.reader.skip(length);\n        break;\n      case \"SMAN\":\n        const maxSmoothingAngle = this.IFF.reader.getFloat32();\n        this.IFF.currentSurface.attributes.smooth = maxSmoothingAngle < 0 ? false : true;\n        break;\n      case \"COLR\":\n        this.IFF.currentSurface.attributes.Color = {\n          value: this.IFF.reader.getFloat32Array(3)\n        };\n        this.IFF.reader.skip(2);\n        break;\n      case \"LUMI\":\n        this.IFF.currentSurface.attributes.Luminosity = {\n          value: this.IFF.reader.getFloat32()\n        };\n        this.IFF.reader.skip(2);\n        break;\n      case \"SPEC\":\n        this.IFF.currentSurface.attributes.Specular = {\n          value: this.IFF.reader.getFloat32()\n        };\n        this.IFF.reader.skip(2);\n        break;\n      case \"DIFF\":\n        this.IFF.currentSurface.attributes.Diffuse = {\n          value: this.IFF.reader.getFloat32()\n        };\n        this.IFF.reader.skip(2);\n        break;\n      case \"REFL\":\n        this.IFF.currentSurface.attributes.Reflection = {\n          value: this.IFF.reader.getFloat32()\n        };\n        this.IFF.reader.skip(2);\n        break;\n      case \"GLOS\":\n        this.IFF.currentSurface.attributes.Glossiness = {\n          value: this.IFF.reader.getFloat32()\n        };\n        this.IFF.reader.skip(2);\n        break;\n      case \"TRAN\":\n        this.IFF.currentSurface.attributes.opacity = this.IFF.reader.getFloat32();\n        this.IFF.reader.skip(2);\n        break;\n      case \"BUMP\":\n        this.IFF.currentSurface.attributes.bumpStrength = this.IFF.reader.getFloat32();\n        this.IFF.reader.skip(2);\n        break;\n      case \"SIDE\":\n        this.IFF.currentSurface.attributes.side = this.IFF.reader.getUint16();\n        break;\n      case \"RIMG\":\n        this.IFF.currentSurface.attributes.reflectionMap = this.IFF.reader.getVariableLengthIndex();\n        break;\n      case \"RIND\":\n        this.IFF.currentSurface.attributes.refractiveIndex = this.IFF.reader.getFloat32();\n        this.IFF.reader.skip(2);\n        break;\n      case \"TIMG\":\n        this.IFF.currentSurface.attributes.refractionMap = this.IFF.reader.getVariableLengthIndex();\n        break;\n      case \"IMAP\":\n        this.IFF.reader.skip(2);\n        break;\n      case \"TMAP\":\n        this.IFF.debugger.skipped = true;\n        this.IFF.reader.skip(length);\n        break;\n      case \"IUVI\":\n        this.IFF.currentNode.UVChannel = this.IFF.reader.getString(length);\n        break;\n      case \"IUTL\":\n        this.IFF.currentNode.widthWrappingMode = this.IFF.reader.getUint32();\n        break;\n      case \"IVTL\":\n        this.IFF.currentNode.heightWrappingMode = this.IFF.reader.getUint32();\n        break;\n      case \"BLOK\":\n        break;\n      default:\n        this.IFF.parseUnknownCHUNK(blockID, length);\n    }\n    if (blockID != \"FORM\") {\n      this.IFF.debugger.node = 1;\n      this.IFF.debugger.nodeID = blockID;\n      this.IFF.debugger.log();\n    }\n    if (this.IFF.reader.offset >= this.IFF.currentFormEnd) {\n      this.IFF.currentForm = this.IFF.parentForm;\n    }\n  }\n}\nexport { LWO2Parser };", "map": {"version": 3, "names": ["LWO2Parser", "constructor", "IFFParser", "IFF", "parseBlock", "debugger", "offset", "reader", "closeForms", "blockID", "getIDTag", "length", "getUint32", "dv", "byteLength", "getUint16", "dataOffset", "parseForm", "skipped", "skip", "parseSurfaceLwo2", "parseClipLwo2", "currentNode", "getInt32", "parseObjectTag", "<PERSON>se<PERSON><PERSON><PERSON>", "parsePoints", "parseVertexMapping", "getVariableLengthIndex", "parsePolygonList", "parseTagStrings", "parsePolygonTagMapping", "currentForm", "description", "getString", "comment", "channelName", "wrap", "w", "h", "index", "imageIndex", "referenceObject", "referenceObjectID", "currentSurface", "surfaceShaderName", "surfaceCustomAOVName", "disabled", "realName", "refName", "nodes", "nodeName", "push", "inputNodeName", "inputName", "inputOutputName", "fileName", "textureChannel", "maxSmoothingAngle", "getFloat32", "attributes", "smooth", "Color", "value", "getFloat32Array", "Luminosity", "Specular", "Diffuse", "Reflection", "Glossiness", "opacity", "bumpStrength", "side", "reflectionMap", "refractiveIndex", "refractionMap", "UVChannel", "widthWrappingMode", "heightWrappingMode", "parseUnknownCHUNK", "node", "nodeID", "log", "currentFormEnd", "parentForm"], "sources": ["C:\\Users\\<USER>\\Documents\\Apps_Creations\\SiahMan_Racing4Speed_WebGame\\siah-man-racing\\node_modules\\src\\loaders\\lwo\\LWO2Parser.js"], "sourcesContent": ["class LWO2Parser {\n  constructor(IFFParser) {\n    this.IFF = IFFParser\n  }\n\n  parseBlock() {\n    this.IFF.debugger.offset = this.IFF.reader.offset\n    this.IFF.debugger.closeForms()\n\n    const blockID = this.IFF.reader.getIDTag()\n    let length = this.IFF.reader.getUint32() // size of data in bytes\n    if (length > this.IFF.reader.dv.byteLength - this.IFF.reader.offset) {\n      this.IFF.reader.offset -= 4\n      length = this.IFF.reader.getUint16()\n    }\n\n    this.IFF.debugger.dataOffset = this.IFF.reader.offset\n    this.IFF.debugger.length = length\n\n    // Data types may be found in either LWO2 OR LWO3 spec\n    switch (blockID) {\n      case 'FORM': // form blocks may consist of sub -chunks or sub-forms\n        this.IFF.parseForm(length)\n        break\n\n      // SKIPPED CHUNKS\n      // if break; is called directly, the position in the lwoTree is not created\n      // any sub chunks and forms are added to the parent form instead\n      // MISC skipped\n      case 'ICON': // Thumbnail Icon Image\n      case 'VMPA': // Vertex Map Parameter\n      case 'BBOX': // bounding box\n      // case 'VMMD':\n      // case 'VTYP':\n\n      // normal maps can be specified, normally on models imported from other applications. Currently ignored\n      case 'NORM':\n\n      // ENVL FORM skipped\n      case 'PRE ':\n      case 'POST':\n      case 'KEY ':\n      case 'SPAN':\n\n      // CLIP FORM skipped\n      case 'TIME':\n      case 'CLRS':\n      case 'CLRA':\n      case 'FILT':\n      case 'DITH':\n      case 'CONT':\n      case 'BRIT':\n      case 'SATR':\n      case 'HUE ':\n      case 'GAMM':\n      case 'NEGA':\n      case 'IFLT':\n      case 'PFLT':\n\n      // Image Map Layer skipped\n      case 'PROJ':\n      case 'AXIS':\n      case 'AAST':\n      case 'PIXB':\n      case 'AUVO':\n      case 'STCK':\n\n      // Procedural Textures skipped\n      case 'PROC':\n      case 'VALU':\n      case 'FUNC':\n\n      // Gradient Textures skipped\n      case 'PNAM':\n      case 'INAM':\n      case 'GRST':\n      case 'GREN':\n      case 'GRPT':\n      case 'FKEY':\n      case 'IKEY':\n\n      // Texture Mapping Form skipped\n      case 'CSYS':\n\n      // Surface CHUNKs skipped\n      case 'OPAQ': // top level 'opacity' checkbox\n      case 'CMAP': // clip map\n\n      // Surface node CHUNKS skipped\n      // These mainly specify the node editor setup in LW\n      case 'NLOC':\n      case 'NZOM':\n      case 'NVER':\n      case 'NSRV':\n      case 'NVSK': // unknown\n      case 'NCRD':\n      case 'WRPW': // image wrap w ( for cylindrical and spherical projections)\n      case 'WRPH': // image wrap h\n      case 'NMOD':\n      case 'NSEL':\n      case 'NPRW':\n      case 'NPLA':\n      case 'NODS':\n      case 'VERS':\n      case 'ENUM':\n      case 'TAG ':\n      case 'OPAC':\n\n      // Car Material CHUNKS\n      case 'CGMD':\n      case 'CGTY':\n      case 'CGST':\n      case 'CGEN':\n      case 'CGTS':\n      case 'CGTE':\n      case 'OSMP':\n      case 'OMDE':\n      case 'OUTR':\n      case 'FLAG':\n\n      case 'TRNL':\n      case 'GLOW':\n      case 'GVAL': // glow intensity\n      case 'SHRP':\n      case 'RFOP':\n      case 'RSAN':\n      case 'TROP':\n      case 'RBLR':\n      case 'TBLR':\n      case 'CLRH':\n      case 'CLRF':\n      case 'ADTR':\n      case 'LINE':\n      case 'ALPH':\n      case 'VCOL':\n      case 'ENAB':\n        this.IFF.debugger.skipped = true\n        this.IFF.reader.skip(length)\n        break\n\n      case 'SURF':\n        this.IFF.parseSurfaceLwo2(length)\n        break\n\n      case 'CLIP':\n        this.IFF.parseClipLwo2(length)\n        break\n\n      // Texture node chunks (not in spec)\n      case 'IPIX': // usePixelBlending\n      case 'IMIP': // useMipMaps\n      case 'IMOD': // imageBlendingMode\n      case 'AMOD': // unknown\n      case 'IINV': // imageInvertAlpha\n      case 'INCR': // imageInvertColor\n      case 'IAXS': // imageAxis ( for non-UV maps)\n      case 'IFOT': // imageFallofType\n      case 'ITIM': // timing for animated textures\n      case 'IWRL':\n      case 'IUTI':\n      case 'IINX':\n      case 'IINY':\n      case 'IINZ':\n      case 'IREF': // possibly a VX for reused texture nodes\n        if (length === 4) this.IFF.currentNode[blockID] = this.IFF.reader.getInt32()\n        else this.IFF.reader.skip(length)\n        break\n\n      case 'OTAG':\n        this.IFF.parseObjectTag()\n        break\n\n      case 'LAYR':\n        this.IFF.parseLayer(length)\n        break\n\n      case 'PNTS':\n        this.IFF.parsePoints(length)\n        break\n\n      case 'VMAP':\n        this.IFF.parseVertexMapping(length)\n        break\n\n      case 'AUVU':\n      case 'AUVN':\n        this.IFF.reader.skip(length - 1)\n        this.IFF.reader.getVariableLengthIndex() // VX\n        break\n\n      case 'POLS':\n        this.IFF.parsePolygonList(length)\n        break\n\n      case 'TAGS':\n        this.IFF.parseTagStrings(length)\n        break\n\n      case 'PTAG':\n        this.IFF.parsePolygonTagMapping(length)\n        break\n\n      case 'VMAD':\n        this.IFF.parseVertexMapping(length, true)\n        break\n\n      // Misc CHUNKS\n      case 'DESC': // Description Line\n        this.IFF.currentForm.description = this.IFF.reader.getString()\n        break\n\n      case 'TEXT':\n      case 'CMNT':\n      case 'NCOM':\n        this.IFF.currentForm.comment = this.IFF.reader.getString()\n        break\n\n      // Envelope Form\n      case 'NAME':\n        this.IFF.currentForm.channelName = this.IFF.reader.getString()\n        break\n\n      // Image Map Layer\n      case 'WRAP':\n        this.IFF.currentForm.wrap = { w: this.IFF.reader.getUint16(), h: this.IFF.reader.getUint16() }\n        break\n\n      case 'IMAG':\n        const index = this.IFF.reader.getVariableLengthIndex()\n        this.IFF.currentForm.imageIndex = index\n        break\n\n      // Texture Mapping Form\n      case 'OREF':\n        this.IFF.currentForm.referenceObject = this.IFF.reader.getString()\n        break\n\n      case 'ROID':\n        this.IFF.currentForm.referenceObjectID = this.IFF.reader.getUint32()\n        break\n\n      // Surface Blocks\n      case 'SSHN':\n        this.IFF.currentSurface.surfaceShaderName = this.IFF.reader.getString()\n        break\n\n      case 'AOVN':\n        this.IFF.currentSurface.surfaceCustomAOVName = this.IFF.reader.getString()\n        break\n\n      // Nodal Blocks\n      case 'NSTA':\n        this.IFF.currentForm.disabled = this.IFF.reader.getUint16()\n        break\n\n      case 'NRNM':\n        this.IFF.currentForm.realName = this.IFF.reader.getString()\n        break\n\n      case 'NNME':\n        this.IFF.currentForm.refName = this.IFF.reader.getString()\n        this.IFF.currentSurface.nodes[this.IFF.currentForm.refName] = this.IFF.currentForm\n        break\n\n      // Nodal Blocks : connections\n      case 'INME':\n        if (!this.IFF.currentForm.nodeName) this.IFF.currentForm.nodeName = []\n        this.IFF.currentForm.nodeName.push(this.IFF.reader.getString())\n        break\n\n      case 'IINN':\n        if (!this.IFF.currentForm.inputNodeName) this.IFF.currentForm.inputNodeName = []\n        this.IFF.currentForm.inputNodeName.push(this.IFF.reader.getString())\n        break\n\n      case 'IINM':\n        if (!this.IFF.currentForm.inputName) this.IFF.currentForm.inputName = []\n        this.IFF.currentForm.inputName.push(this.IFF.reader.getString())\n        break\n\n      case 'IONM':\n        if (!this.IFF.currentForm.inputOutputName) this.IFF.currentForm.inputOutputName = []\n        this.IFF.currentForm.inputOutputName.push(this.IFF.reader.getString())\n        break\n\n      case 'FNAM':\n        this.IFF.currentForm.fileName = this.IFF.reader.getString()\n        break\n\n      case 'CHAN': // NOTE: ENVL Forms may also have CHAN chunk, however ENVL is currently ignored\n        if (length === 4) this.IFF.currentForm.textureChannel = this.IFF.reader.getIDTag()\n        else this.IFF.reader.skip(length)\n        break\n\n      // LWO2 Spec chunks: these are needed since the SURF FORMs are often in LWO2 format\n      case 'SMAN':\n        const maxSmoothingAngle = this.IFF.reader.getFloat32()\n        this.IFF.currentSurface.attributes.smooth = maxSmoothingAngle < 0 ? false : true\n        break\n\n      // LWO2: Basic Surface Parameters\n      case 'COLR':\n        this.IFF.currentSurface.attributes.Color = { value: this.IFF.reader.getFloat32Array(3) }\n        this.IFF.reader.skip(2) // VX: envelope\n        break\n\n      case 'LUMI':\n        this.IFF.currentSurface.attributes.Luminosity = { value: this.IFF.reader.getFloat32() }\n        this.IFF.reader.skip(2)\n        break\n\n      case 'SPEC':\n        this.IFF.currentSurface.attributes.Specular = { value: this.IFF.reader.getFloat32() }\n        this.IFF.reader.skip(2)\n        break\n\n      case 'DIFF':\n        this.IFF.currentSurface.attributes.Diffuse = { value: this.IFF.reader.getFloat32() }\n        this.IFF.reader.skip(2)\n        break\n\n      case 'REFL':\n        this.IFF.currentSurface.attributes.Reflection = { value: this.IFF.reader.getFloat32() }\n        this.IFF.reader.skip(2)\n        break\n\n      case 'GLOS':\n        this.IFF.currentSurface.attributes.Glossiness = { value: this.IFF.reader.getFloat32() }\n        this.IFF.reader.skip(2)\n        break\n\n      case 'TRAN':\n        this.IFF.currentSurface.attributes.opacity = this.IFF.reader.getFloat32()\n        this.IFF.reader.skip(2)\n        break\n\n      case 'BUMP':\n        this.IFF.currentSurface.attributes.bumpStrength = this.IFF.reader.getFloat32()\n        this.IFF.reader.skip(2)\n        break\n\n      case 'SIDE':\n        this.IFF.currentSurface.attributes.side = this.IFF.reader.getUint16()\n        break\n\n      case 'RIMG':\n        this.IFF.currentSurface.attributes.reflectionMap = this.IFF.reader.getVariableLengthIndex()\n        break\n\n      case 'RIND':\n        this.IFF.currentSurface.attributes.refractiveIndex = this.IFF.reader.getFloat32()\n        this.IFF.reader.skip(2)\n        break\n\n      case 'TIMG':\n        this.IFF.currentSurface.attributes.refractionMap = this.IFF.reader.getVariableLengthIndex()\n        break\n\n      case 'IMAP':\n        this.IFF.reader.skip(2)\n        break\n\n      case 'TMAP':\n        this.IFF.debugger.skipped = true\n        this.IFF.reader.skip(length) // needs implementing\n        break\n\n      case 'IUVI': // uv channel name\n        this.IFF.currentNode.UVChannel = this.IFF.reader.getString(length)\n        break\n\n      case 'IUTL': // widthWrappingMode: 0 = Reset, 1 = Repeat, 2 = Mirror, 3 = Edge\n        this.IFF.currentNode.widthWrappingMode = this.IFF.reader.getUint32()\n        break\n      case 'IVTL': // heightWrappingMode\n        this.IFF.currentNode.heightWrappingMode = this.IFF.reader.getUint32()\n        break\n\n      // LWO2 USE\n      case 'BLOK':\n        // skip\n        break\n\n      default:\n        this.IFF.parseUnknownCHUNK(blockID, length)\n    }\n\n    if (blockID != 'FORM') {\n      this.IFF.debugger.node = 1\n      this.IFF.debugger.nodeID = blockID\n      this.IFF.debugger.log()\n    }\n\n    if (this.IFF.reader.offset >= this.IFF.currentFormEnd) {\n      this.IFF.currentForm = this.IFF.parentForm\n    }\n  }\n}\n\nexport { LWO2Parser }\n"], "mappings": "AAAA,MAAMA,UAAA,CAAW;EACfC,YAAYC,SAAA,EAAW;IACrB,KAAKC,GAAA,GAAMD,SAAA;EACZ;EAEDE,WAAA,EAAa;IACX,KAAKD,GAAA,CAAIE,QAAA,CAASC,MAAA,GAAS,KAAKH,GAAA,CAAII,MAAA,CAAOD,MAAA;IAC3C,KAAKH,GAAA,CAAIE,QAAA,CAASG,UAAA,CAAY;IAE9B,MAAMC,OAAA,GAAU,KAAKN,GAAA,CAAII,MAAA,CAAOG,QAAA,CAAU;IAC1C,IAAIC,MAAA,GAAS,KAAKR,GAAA,CAAII,MAAA,CAAOK,SAAA,CAAW;IACxC,IAAID,MAAA,GAAS,KAAKR,GAAA,CAAII,MAAA,CAAOM,EAAA,CAAGC,UAAA,GAAa,KAAKX,GAAA,CAAII,MAAA,CAAOD,MAAA,EAAQ;MACnE,KAAKH,GAAA,CAAII,MAAA,CAAOD,MAAA,IAAU;MAC1BK,MAAA,GAAS,KAAKR,GAAA,CAAII,MAAA,CAAOQ,SAAA,CAAW;IACrC;IAED,KAAKZ,GAAA,CAAIE,QAAA,CAASW,UAAA,GAAa,KAAKb,GAAA,CAAII,MAAA,CAAOD,MAAA;IAC/C,KAAKH,GAAA,CAAIE,QAAA,CAASM,MAAA,GAASA,MAAA;IAG3B,QAAQF,OAAA;MACN,KAAK;QACH,KAAKN,GAAA,CAAIc,SAAA,CAAUN,MAAM;QACzB;MAMF,KAAK;MACL,KAAK;MACL,KAAK;MAKL,KAAK;MAGL,KAAK;MACL,KAAK;MACL,KAAK;MACL,KAAK;MAGL,KAAK;MACL,KAAK;MACL,KAAK;MACL,KAAK;MACL,KAAK;MACL,KAAK;MACL,KAAK;MACL,KAAK;MACL,KAAK;MACL,KAAK;MACL,KAAK;MACL,KAAK;MACL,KAAK;MAGL,KAAK;MACL,KAAK;MACL,KAAK;MACL,KAAK;MACL,KAAK;MACL,KAAK;MAGL,KAAK;MACL,KAAK;MACL,KAAK;MAGL,KAAK;MACL,KAAK;MACL,KAAK;MACL,KAAK;MACL,KAAK;MACL,KAAK;MACL,KAAK;MAGL,KAAK;MAGL,KAAK;MACL,KAAK;MAIL,KAAK;MACL,KAAK;MACL,KAAK;MACL,KAAK;MACL,KAAK;MACL,KAAK;MACL,KAAK;MACL,KAAK;MACL,KAAK;MACL,KAAK;MACL,KAAK;MACL,KAAK;MACL,KAAK;MACL,KAAK;MACL,KAAK;MACL,KAAK;MACL,KAAK;MAGL,KAAK;MACL,KAAK;MACL,KAAK;MACL,KAAK;MACL,KAAK;MACL,KAAK;MACL,KAAK;MACL,KAAK;MACL,KAAK;MACL,KAAK;MAEL,KAAK;MACL,KAAK;MACL,KAAK;MACL,KAAK;MACL,KAAK;MACL,KAAK;MACL,KAAK;MACL,KAAK;MACL,KAAK;MACL,KAAK;MACL,KAAK;MACL,KAAK;MACL,KAAK;MACL,KAAK;MACL,KAAK;MACL,KAAK;QACH,KAAKR,GAAA,CAAIE,QAAA,CAASa,OAAA,GAAU;QAC5B,KAAKf,GAAA,CAAII,MAAA,CAAOY,IAAA,CAAKR,MAAM;QAC3B;MAEF,KAAK;QACH,KAAKR,GAAA,CAAIiB,gBAAA,CAAiBT,MAAM;QAChC;MAEF,KAAK;QACH,KAAKR,GAAA,CAAIkB,aAAA,CAAcV,MAAM;QAC7B;MAGF,KAAK;MACL,KAAK;MACL,KAAK;MACL,KAAK;MACL,KAAK;MACL,KAAK;MACL,KAAK;MACL,KAAK;MACL,KAAK;MACL,KAAK;MACL,KAAK;MACL,KAAK;MACL,KAAK;MACL,KAAK;MACL,KAAK;QACH,IAAIA,MAAA,KAAW,GAAG,KAAKR,GAAA,CAAImB,WAAA,CAAYb,OAAO,IAAI,KAAKN,GAAA,CAAII,MAAA,CAAOgB,QAAA,CAAU,OACvE,KAAKpB,GAAA,CAAII,MAAA,CAAOY,IAAA,CAAKR,MAAM;QAChC;MAEF,KAAK;QACH,KAAKR,GAAA,CAAIqB,cAAA,CAAgB;QACzB;MAEF,KAAK;QACH,KAAKrB,GAAA,CAAIsB,UAAA,CAAWd,MAAM;QAC1B;MAEF,KAAK;QACH,KAAKR,GAAA,CAAIuB,WAAA,CAAYf,MAAM;QAC3B;MAEF,KAAK;QACH,KAAKR,GAAA,CAAIwB,kBAAA,CAAmBhB,MAAM;QAClC;MAEF,KAAK;MACL,KAAK;QACH,KAAKR,GAAA,CAAII,MAAA,CAAOY,IAAA,CAAKR,MAAA,GAAS,CAAC;QAC/B,KAAKR,GAAA,CAAII,MAAA,CAAOqB,sBAAA,CAAwB;QACxC;MAEF,KAAK;QACH,KAAKzB,GAAA,CAAI0B,gBAAA,CAAiBlB,MAAM;QAChC;MAEF,KAAK;QACH,KAAKR,GAAA,CAAI2B,eAAA,CAAgBnB,MAAM;QAC/B;MAEF,KAAK;QACH,KAAKR,GAAA,CAAI4B,sBAAA,CAAuBpB,MAAM;QACtC;MAEF,KAAK;QACH,KAAKR,GAAA,CAAIwB,kBAAA,CAAmBhB,MAAA,EAAQ,IAAI;QACxC;MAGF,KAAK;QACH,KAAKR,GAAA,CAAI6B,WAAA,CAAYC,WAAA,GAAc,KAAK9B,GAAA,CAAII,MAAA,CAAO2B,SAAA,CAAW;QAC9D;MAEF,KAAK;MACL,KAAK;MACL,KAAK;QACH,KAAK/B,GAAA,CAAI6B,WAAA,CAAYG,OAAA,GAAU,KAAKhC,GAAA,CAAII,MAAA,CAAO2B,SAAA,CAAW;QAC1D;MAGF,KAAK;QACH,KAAK/B,GAAA,CAAI6B,WAAA,CAAYI,WAAA,GAAc,KAAKjC,GAAA,CAAII,MAAA,CAAO2B,SAAA,CAAW;QAC9D;MAGF,KAAK;QACH,KAAK/B,GAAA,CAAI6B,WAAA,CAAYK,IAAA,GAAO;UAAEC,CAAA,EAAG,KAAKnC,GAAA,CAAII,MAAA,CAAOQ,SAAA,CAAS;UAAIwB,CAAA,EAAG,KAAKpC,GAAA,CAAII,MAAA,CAAOQ,SAAA;QAAa;QAC9F;MAEF,KAAK;QACH,MAAMyB,KAAA,GAAQ,KAAKrC,GAAA,CAAII,MAAA,CAAOqB,sBAAA,CAAwB;QACtD,KAAKzB,GAAA,CAAI6B,WAAA,CAAYS,UAAA,GAAaD,KAAA;QAClC;MAGF,KAAK;QACH,KAAKrC,GAAA,CAAI6B,WAAA,CAAYU,eAAA,GAAkB,KAAKvC,GAAA,CAAII,MAAA,CAAO2B,SAAA,CAAW;QAClE;MAEF,KAAK;QACH,KAAK/B,GAAA,CAAI6B,WAAA,CAAYW,iBAAA,GAAoB,KAAKxC,GAAA,CAAII,MAAA,CAAOK,SAAA,CAAW;QACpE;MAGF,KAAK;QACH,KAAKT,GAAA,CAAIyC,cAAA,CAAeC,iBAAA,GAAoB,KAAK1C,GAAA,CAAII,MAAA,CAAO2B,SAAA,CAAW;QACvE;MAEF,KAAK;QACH,KAAK/B,GAAA,CAAIyC,cAAA,CAAeE,oBAAA,GAAuB,KAAK3C,GAAA,CAAII,MAAA,CAAO2B,SAAA,CAAW;QAC1E;MAGF,KAAK;QACH,KAAK/B,GAAA,CAAI6B,WAAA,CAAYe,QAAA,GAAW,KAAK5C,GAAA,CAAII,MAAA,CAAOQ,SAAA,CAAW;QAC3D;MAEF,KAAK;QACH,KAAKZ,GAAA,CAAI6B,WAAA,CAAYgB,QAAA,GAAW,KAAK7C,GAAA,CAAII,MAAA,CAAO2B,SAAA,CAAW;QAC3D;MAEF,KAAK;QACH,KAAK/B,GAAA,CAAI6B,WAAA,CAAYiB,OAAA,GAAU,KAAK9C,GAAA,CAAII,MAAA,CAAO2B,SAAA,CAAW;QAC1D,KAAK/B,GAAA,CAAIyC,cAAA,CAAeM,KAAA,CAAM,KAAK/C,GAAA,CAAI6B,WAAA,CAAYiB,OAAO,IAAI,KAAK9C,GAAA,CAAI6B,WAAA;QACvE;MAGF,KAAK;QACH,IAAI,CAAC,KAAK7B,GAAA,CAAI6B,WAAA,CAAYmB,QAAA,EAAU,KAAKhD,GAAA,CAAI6B,WAAA,CAAYmB,QAAA,GAAW,EAAE;QACtE,KAAKhD,GAAA,CAAI6B,WAAA,CAAYmB,QAAA,CAASC,IAAA,CAAK,KAAKjD,GAAA,CAAII,MAAA,CAAO2B,SAAA,EAAW;QAC9D;MAEF,KAAK;QACH,IAAI,CAAC,KAAK/B,GAAA,CAAI6B,WAAA,CAAYqB,aAAA,EAAe,KAAKlD,GAAA,CAAI6B,WAAA,CAAYqB,aAAA,GAAgB,EAAE;QAChF,KAAKlD,GAAA,CAAI6B,WAAA,CAAYqB,aAAA,CAAcD,IAAA,CAAK,KAAKjD,GAAA,CAAII,MAAA,CAAO2B,SAAA,EAAW;QACnE;MAEF,KAAK;QACH,IAAI,CAAC,KAAK/B,GAAA,CAAI6B,WAAA,CAAYsB,SAAA,EAAW,KAAKnD,GAAA,CAAI6B,WAAA,CAAYsB,SAAA,GAAY,EAAE;QACxE,KAAKnD,GAAA,CAAI6B,WAAA,CAAYsB,SAAA,CAAUF,IAAA,CAAK,KAAKjD,GAAA,CAAII,MAAA,CAAO2B,SAAA,EAAW;QAC/D;MAEF,KAAK;QACH,IAAI,CAAC,KAAK/B,GAAA,CAAI6B,WAAA,CAAYuB,eAAA,EAAiB,KAAKpD,GAAA,CAAI6B,WAAA,CAAYuB,eAAA,GAAkB,EAAE;QACpF,KAAKpD,GAAA,CAAI6B,WAAA,CAAYuB,eAAA,CAAgBH,IAAA,CAAK,KAAKjD,GAAA,CAAII,MAAA,CAAO2B,SAAA,EAAW;QACrE;MAEF,KAAK;QACH,KAAK/B,GAAA,CAAI6B,WAAA,CAAYwB,QAAA,GAAW,KAAKrD,GAAA,CAAII,MAAA,CAAO2B,SAAA,CAAW;QAC3D;MAEF,KAAK;QACH,IAAIvB,MAAA,KAAW,GAAG,KAAKR,GAAA,CAAI6B,WAAA,CAAYyB,cAAA,GAAiB,KAAKtD,GAAA,CAAII,MAAA,CAAOG,QAAA,CAAU,OAC7E,KAAKP,GAAA,CAAII,MAAA,CAAOY,IAAA,CAAKR,MAAM;QAChC;MAGF,KAAK;QACH,MAAM+C,iBAAA,GAAoB,KAAKvD,GAAA,CAAII,MAAA,CAAOoD,UAAA,CAAY;QACtD,KAAKxD,GAAA,CAAIyC,cAAA,CAAegB,UAAA,CAAWC,MAAA,GAASH,iBAAA,GAAoB,IAAI,QAAQ;QAC5E;MAGF,KAAK;QACH,KAAKvD,GAAA,CAAIyC,cAAA,CAAegB,UAAA,CAAWE,KAAA,GAAQ;UAAEC,KAAA,EAAO,KAAK5D,GAAA,CAAII,MAAA,CAAOyD,eAAA,CAAgB,CAAC;QAAG;QACxF,KAAK7D,GAAA,CAAII,MAAA,CAAOY,IAAA,CAAK,CAAC;QACtB;MAEF,KAAK;QACH,KAAKhB,GAAA,CAAIyC,cAAA,CAAegB,UAAA,CAAWK,UAAA,GAAa;UAAEF,KAAA,EAAO,KAAK5D,GAAA,CAAII,MAAA,CAAOoD,UAAA,CAAU;QAAI;QACvF,KAAKxD,GAAA,CAAII,MAAA,CAAOY,IAAA,CAAK,CAAC;QACtB;MAEF,KAAK;QACH,KAAKhB,GAAA,CAAIyC,cAAA,CAAegB,UAAA,CAAWM,QAAA,GAAW;UAAEH,KAAA,EAAO,KAAK5D,GAAA,CAAII,MAAA,CAAOoD,UAAA,CAAU;QAAI;QACrF,KAAKxD,GAAA,CAAII,MAAA,CAAOY,IAAA,CAAK,CAAC;QACtB;MAEF,KAAK;QACH,KAAKhB,GAAA,CAAIyC,cAAA,CAAegB,UAAA,CAAWO,OAAA,GAAU;UAAEJ,KAAA,EAAO,KAAK5D,GAAA,CAAII,MAAA,CAAOoD,UAAA,CAAU;QAAI;QACpF,KAAKxD,GAAA,CAAII,MAAA,CAAOY,IAAA,CAAK,CAAC;QACtB;MAEF,KAAK;QACH,KAAKhB,GAAA,CAAIyC,cAAA,CAAegB,UAAA,CAAWQ,UAAA,GAAa;UAAEL,KAAA,EAAO,KAAK5D,GAAA,CAAII,MAAA,CAAOoD,UAAA,CAAU;QAAI;QACvF,KAAKxD,GAAA,CAAII,MAAA,CAAOY,IAAA,CAAK,CAAC;QACtB;MAEF,KAAK;QACH,KAAKhB,GAAA,CAAIyC,cAAA,CAAegB,UAAA,CAAWS,UAAA,GAAa;UAAEN,KAAA,EAAO,KAAK5D,GAAA,CAAII,MAAA,CAAOoD,UAAA,CAAU;QAAI;QACvF,KAAKxD,GAAA,CAAII,MAAA,CAAOY,IAAA,CAAK,CAAC;QACtB;MAEF,KAAK;QACH,KAAKhB,GAAA,CAAIyC,cAAA,CAAegB,UAAA,CAAWU,OAAA,GAAU,KAAKnE,GAAA,CAAII,MAAA,CAAOoD,UAAA,CAAY;QACzE,KAAKxD,GAAA,CAAII,MAAA,CAAOY,IAAA,CAAK,CAAC;QACtB;MAEF,KAAK;QACH,KAAKhB,GAAA,CAAIyC,cAAA,CAAegB,UAAA,CAAWW,YAAA,GAAe,KAAKpE,GAAA,CAAII,MAAA,CAAOoD,UAAA,CAAY;QAC9E,KAAKxD,GAAA,CAAII,MAAA,CAAOY,IAAA,CAAK,CAAC;QACtB;MAEF,KAAK;QACH,KAAKhB,GAAA,CAAIyC,cAAA,CAAegB,UAAA,CAAWY,IAAA,GAAO,KAAKrE,GAAA,CAAII,MAAA,CAAOQ,SAAA,CAAW;QACrE;MAEF,KAAK;QACH,KAAKZ,GAAA,CAAIyC,cAAA,CAAegB,UAAA,CAAWa,aAAA,GAAgB,KAAKtE,GAAA,CAAII,MAAA,CAAOqB,sBAAA,CAAwB;QAC3F;MAEF,KAAK;QACH,KAAKzB,GAAA,CAAIyC,cAAA,CAAegB,UAAA,CAAWc,eAAA,GAAkB,KAAKvE,GAAA,CAAII,MAAA,CAAOoD,UAAA,CAAY;QACjF,KAAKxD,GAAA,CAAII,MAAA,CAAOY,IAAA,CAAK,CAAC;QACtB;MAEF,KAAK;QACH,KAAKhB,GAAA,CAAIyC,cAAA,CAAegB,UAAA,CAAWe,aAAA,GAAgB,KAAKxE,GAAA,CAAII,MAAA,CAAOqB,sBAAA,CAAwB;QAC3F;MAEF,KAAK;QACH,KAAKzB,GAAA,CAAII,MAAA,CAAOY,IAAA,CAAK,CAAC;QACtB;MAEF,KAAK;QACH,KAAKhB,GAAA,CAAIE,QAAA,CAASa,OAAA,GAAU;QAC5B,KAAKf,GAAA,CAAII,MAAA,CAAOY,IAAA,CAAKR,MAAM;QAC3B;MAEF,KAAK;QACH,KAAKR,GAAA,CAAImB,WAAA,CAAYsD,SAAA,GAAY,KAAKzE,GAAA,CAAII,MAAA,CAAO2B,SAAA,CAAUvB,MAAM;QACjE;MAEF,KAAK;QACH,KAAKR,GAAA,CAAImB,WAAA,CAAYuD,iBAAA,GAAoB,KAAK1E,GAAA,CAAII,MAAA,CAAOK,SAAA,CAAW;QACpE;MACF,KAAK;QACH,KAAKT,GAAA,CAAImB,WAAA,CAAYwD,kBAAA,GAAqB,KAAK3E,GAAA,CAAII,MAAA,CAAOK,SAAA,CAAW;QACrE;MAGF,KAAK;QAEH;MAEF;QACE,KAAKT,GAAA,CAAI4E,iBAAA,CAAkBtE,OAAA,EAASE,MAAM;IAC7C;IAED,IAAIF,OAAA,IAAW,QAAQ;MACrB,KAAKN,GAAA,CAAIE,QAAA,CAAS2E,IAAA,GAAO;MACzB,KAAK7E,GAAA,CAAIE,QAAA,CAAS4E,MAAA,GAASxE,OAAA;MAC3B,KAAKN,GAAA,CAAIE,QAAA,CAAS6E,GAAA,CAAK;IACxB;IAED,IAAI,KAAK/E,GAAA,CAAII,MAAA,CAAOD,MAAA,IAAU,KAAKH,GAAA,CAAIgF,cAAA,EAAgB;MACrD,KAAKhF,GAAA,CAAI6B,WAAA,GAAc,KAAK7B,GAAA,CAAIiF,UAAA;IACjC;EACF;AACH", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}