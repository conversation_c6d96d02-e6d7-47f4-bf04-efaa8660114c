{"ast": null, "code": "import { Vector3, Vector4 } from 'three';\n\n/**\n * @module NURBSUtils\n * @three_import import * as NURBSUtils from 'three/addons/curves/NURBSUtils.js';\n */\n\n/**\n * Finds knot vector span.\n *\n * @param {number} p - The degree.\n * @param {number} u - The parametric value.\n * @param {Array<number>} U - The knot vector.\n * @return {number} The span.\n */\nfunction findSpan(p, u, U) {\n  const n = U.length - p - 1;\n  if (u >= U[n]) {\n    return n - 1;\n  }\n  if (u <= U[p]) {\n    return p;\n  }\n  let low = p;\n  let high = n;\n  let mid = Math.floor((low + high) / 2);\n  while (u < U[mid] || u >= U[mid + 1]) {\n    if (u < U[mid]) {\n      high = mid;\n    } else {\n      low = mid;\n    }\n    mid = Math.floor((low + high) / 2);\n  }\n  return mid;\n}\n\n/**\n * Calculates basis functions. See The NURBS Book, page 70, algorithm A2.2.\n *\n * @param {number} span - The span in which `u` lies.\n * @param {number} u - The parametric value.\n * @param {number} p - The degree.\n * @param {Array<number>} U - The knot vector.\n * @return {Array<number>} Array[p+1] with basis functions values.\n */\nfunction calcBasisFunctions(span, u, p, U) {\n  const N = [];\n  const left = [];\n  const right = [];\n  N[0] = 1.0;\n  for (let j = 1; j <= p; ++j) {\n    left[j] = u - U[span + 1 - j];\n    right[j] = U[span + j] - u;\n    let saved = 0.0;\n    for (let r = 0; r < j; ++r) {\n      const rv = right[r + 1];\n      const lv = left[j - r];\n      const temp = N[r] / (rv + lv);\n      N[r] = saved + rv * temp;\n      saved = lv * temp;\n    }\n    N[j] = saved;\n  }\n  return N;\n}\n\n/**\n * Calculates B-Spline curve points. See The NURBS Book, page 82, algorithm A3.1.\n *\n * @param {number} p - The degree of the B-Spline.\n * @param {Array<number>} U - The knot vector.\n * @param {Array<Vector4>} P - The control points\n * @param {number} u - The parametric point.\n * @return {Vector4} The point for given `u`.\n */\nfunction calcBSplinePoint(p, U, P, u) {\n  const span = findSpan(p, u, U);\n  const N = calcBasisFunctions(span, u, p, U);\n  const C = new Vector4(0, 0, 0, 0);\n  for (let j = 0; j <= p; ++j) {\n    const point = P[span - p + j];\n    const Nj = N[j];\n    const wNj = point.w * Nj;\n    C.x += point.x * wNj;\n    C.y += point.y * wNj;\n    C.z += point.z * wNj;\n    C.w += point.w * Nj;\n  }\n  return C;\n}\n\n/**\n * Calculates basis functions derivatives. See The NURBS Book, page 72, algorithm A2.3.\n *\n * @param {number} span - The span in which `u` lies.\n * @param {number} u - The parametric point.\n * @param {number} p - The degree.\n * @param {number} n - number of derivatives to calculate\n * @param {Array<number>} U - The knot vector.\n * @return {Array<Array<number>>} An array[n+1][p+1] with basis functions derivatives.\n */\nfunction calcBasisFunctionDerivatives(span, u, p, n, U) {\n  const zeroArr = [];\n  for (let i = 0; i <= p; ++i) zeroArr[i] = 0.0;\n  const ders = [];\n  for (let i = 0; i <= n; ++i) ders[i] = zeroArr.slice(0);\n  const ndu = [];\n  for (let i = 0; i <= p; ++i) ndu[i] = zeroArr.slice(0);\n  ndu[0][0] = 1.0;\n  const left = zeroArr.slice(0);\n  const right = zeroArr.slice(0);\n  for (let j = 1; j <= p; ++j) {\n    left[j] = u - U[span + 1 - j];\n    right[j] = U[span + j] - u;\n    let saved = 0.0;\n    for (let r = 0; r < j; ++r) {\n      const rv = right[r + 1];\n      const lv = left[j - r];\n      ndu[j][r] = rv + lv;\n      const temp = ndu[r][j - 1] / ndu[j][r];\n      ndu[r][j] = saved + rv * temp;\n      saved = lv * temp;\n    }\n    ndu[j][j] = saved;\n  }\n  for (let j = 0; j <= p; ++j) {\n    ders[0][j] = ndu[j][p];\n  }\n  for (let r = 0; r <= p; ++r) {\n    let s1 = 0;\n    let s2 = 1;\n    const a = [];\n    for (let i = 0; i <= p; ++i) {\n      a[i] = zeroArr.slice(0);\n    }\n    a[0][0] = 1.0;\n    for (let k = 1; k <= n; ++k) {\n      let d = 0.0;\n      const rk = r - k;\n      const pk = p - k;\n      if (r >= k) {\n        a[s2][0] = a[s1][0] / ndu[pk + 1][rk];\n        d = a[s2][0] * ndu[rk][pk];\n      }\n      const j1 = rk >= -1 ? 1 : -rk;\n      const j2 = r - 1 <= pk ? k - 1 : p - r;\n      for (let j = j1; j <= j2; ++j) {\n        a[s2][j] = (a[s1][j] - a[s1][j - 1]) / ndu[pk + 1][rk + j];\n        d += a[s2][j] * ndu[rk + j][pk];\n      }\n      if (r <= pk) {\n        a[s2][k] = -a[s1][k - 1] / ndu[pk + 1][r];\n        d += a[s2][k] * ndu[r][pk];\n      }\n      ders[k][r] = d;\n      const j = s1;\n      s1 = s2;\n      s2 = j;\n    }\n  }\n  let r = p;\n  for (let k = 1; k <= n; ++k) {\n    for (let j = 0; j <= p; ++j) {\n      ders[k][j] *= r;\n    }\n    r *= p - k;\n  }\n  return ders;\n}\n\n/**\n * Calculates derivatives of a B-Spline. See The NURBS Book, page 93, algorithm A3.2.\n *\n * @param {number} p - The degree.\n * @param {Array<number>} U - The knot vector.\n * @param {Array<Vector4>} P - The control points\n * @param {number} u - The parametric point.\n * @param {number} nd - The number of derivatives.\n * @return {Array<Vector4>} An array[d+1] with derivatives.\n */\nfunction calcBSplineDerivatives(p, U, P, u, nd) {\n  const du = nd < p ? nd : p;\n  const CK = [];\n  const span = findSpan(p, u, U);\n  const nders = calcBasisFunctionDerivatives(span, u, p, du, U);\n  const Pw = [];\n  for (let i = 0; i < P.length; ++i) {\n    const point = P[i].clone();\n    const w = point.w;\n    point.x *= w;\n    point.y *= w;\n    point.z *= w;\n    Pw[i] = point;\n  }\n  for (let k = 0; k <= du; ++k) {\n    const point = Pw[span - p].clone().multiplyScalar(nders[k][0]);\n    for (let j = 1; j <= p; ++j) {\n      point.add(Pw[span - p + j].clone().multiplyScalar(nders[k][j]));\n    }\n    CK[k] = point;\n  }\n  for (let k = du + 1; k <= nd + 1; ++k) {\n    CK[k] = new Vector4(0, 0, 0);\n  }\n  return CK;\n}\n\n/**\n * Calculates \"K over I\".\n *\n * @param {number} k - The K value.\n * @param {number} i - The I value.\n * @return {number} k!/(i!(k-i)!)\n */\nfunction calcKoverI(k, i) {\n  let nom = 1;\n  for (let j = 2; j <= k; ++j) {\n    nom *= j;\n  }\n  let denom = 1;\n  for (let j = 2; j <= i; ++j) {\n    denom *= j;\n  }\n  for (let j = 2; j <= k - i; ++j) {\n    denom *= j;\n  }\n  return nom / denom;\n}\n\n/**\n * Calculates derivatives (0-nd) of rational curve. See The NURBS Book, page 127, algorithm A4.2.\n *\n * @param {Array<Vector4>} Pders - Array with derivatives.\n * @return {Array<Vector3>} An array with derivatives for rational curve.\n */\nfunction calcRationalCurveDerivatives(Pders) {\n  const nd = Pders.length;\n  const Aders = [];\n  const wders = [];\n  for (let i = 0; i < nd; ++i) {\n    const point = Pders[i];\n    Aders[i] = new Vector3(point.x, point.y, point.z);\n    wders[i] = point.w;\n  }\n  const CK = [];\n  for (let k = 0; k < nd; ++k) {\n    const v = Aders[k].clone();\n    for (let i = 1; i <= k; ++i) {\n      v.sub(CK[k - i].clone().multiplyScalar(calcKoverI(k, i) * wders[i]));\n    }\n    CK[k] = v.divideScalar(wders[0]);\n  }\n  return CK;\n}\n\n/**\n * Calculates NURBS curve derivatives. See The NURBS Book, page 127, algorithm A4.2.\n *\n * @param {number} p - The degree.\n * @param {Array<number>} U - The knot vector.\n * @param {Array<Vector4>} P - The control points in homogeneous space.\n * @param {number} u - The parametric point.\n * @param {number} nd - The number of derivatives.\n * @return {Array<Vector3>} array with derivatives for rational curve.\n */\nfunction calcNURBSDerivatives(p, U, P, u, nd) {\n  const Pders = calcBSplineDerivatives(p, U, P, u, nd);\n  return calcRationalCurveDerivatives(Pders);\n}\n\n/**\n * Calculates a rational B-Spline surface point. See The NURBS Book, page 134, algorithm A4.3.\n *\n * @param {number} p - The first degree of B-Spline surface.\n * @param {number} q - The second degree of B-Spline surface.\n * @param {Array<number>} U - The first knot vector.\n * @param {Array<number>} V - The second knot vector.\n * @param {Array<Array<Vector4>>} P - The control points in homogeneous space.\n * @param {number} u - The first parametric point.\n * @param {number} v - The second parametric point.\n * @param {Vector3} target - The target vector.\n */\nfunction calcSurfacePoint(p, q, U, V, P, u, v, target) {\n  const uspan = findSpan(p, u, U);\n  const vspan = findSpan(q, v, V);\n  const Nu = calcBasisFunctions(uspan, u, p, U);\n  const Nv = calcBasisFunctions(vspan, v, q, V);\n  const temp = [];\n  for (let l = 0; l <= q; ++l) {\n    temp[l] = new Vector4(0, 0, 0, 0);\n    for (let k = 0; k <= p; ++k) {\n      const point = P[uspan - p + k][vspan - q + l].clone();\n      const w = point.w;\n      point.x *= w;\n      point.y *= w;\n      point.z *= w;\n      temp[l].add(point.multiplyScalar(Nu[k]));\n    }\n  }\n  const Sw = new Vector4(0, 0, 0, 0);\n  for (let l = 0; l <= q; ++l) {\n    Sw.add(temp[l].multiplyScalar(Nv[l]));\n  }\n  Sw.divideScalar(Sw.w);\n  target.set(Sw.x, Sw.y, Sw.z);\n}\n\n/**\n * Calculates a rational B-Spline volume point. See The NURBS Book, page 134, algorithm A4.3.\n *\n * @param {number} p - The first degree of B-Spline surface.\n * @param {number} q - The second degree of B-Spline surface.\n * @param {number} r - The third degree of B-Spline surface.\n * @param {Array<number>} U - The first knot vector.\n * @param {Array<number>} V - The second knot vector.\n * @param {Array<number>} W - The third knot vector.\n * @param {Array<Array<Array<Vector4>>>} P - The control points in homogeneous space.\n * @param {number} u - The first parametric point.\n * @param {number} v - The second parametric point.\n * @param {number} w - The third parametric point.\n * @param {Vector3} target - The target vector.\n */\nfunction calcVolumePoint(p, q, r, U, V, W, P, u, v, w, target) {\n  const uspan = findSpan(p, u, U);\n  const vspan = findSpan(q, v, V);\n  const wspan = findSpan(r, w, W);\n  const Nu = calcBasisFunctions(uspan, u, p, U);\n  const Nv = calcBasisFunctions(vspan, v, q, V);\n  const Nw = calcBasisFunctions(wspan, w, r, W);\n  const temp = [];\n  for (let m = 0; m <= r; ++m) {\n    temp[m] = [];\n    for (let l = 0; l <= q; ++l) {\n      temp[m][l] = new Vector4(0, 0, 0, 0);\n      for (let k = 0; k <= p; ++k) {\n        const point = P[uspan - p + k][vspan - q + l][wspan - r + m].clone();\n        const w = point.w;\n        point.x *= w;\n        point.y *= w;\n        point.z *= w;\n        temp[m][l].add(point.multiplyScalar(Nu[k]));\n      }\n    }\n  }\n  const Sw = new Vector4(0, 0, 0, 0);\n  for (let m = 0; m <= r; ++m) {\n    for (let l = 0; l <= q; ++l) {\n      Sw.add(temp[m][l].multiplyScalar(Nw[m]).multiplyScalar(Nv[l]));\n    }\n  }\n  Sw.divideScalar(Sw.w);\n  target.set(Sw.x, Sw.y, Sw.z);\n}\nexport { findSpan, calcBasisFunctions, calcBSplinePoint, calcBasisFunctionDerivatives, calcBSplineDerivatives, calcKoverI, calcRationalCurveDerivatives, calcNURBSDerivatives, calcSurfacePoint, calcVolumePoint };", "map": {"version": 3, "names": ["Vector3", "Vector4", "findSpan", "p", "u", "U", "n", "length", "low", "high", "mid", "Math", "floor", "calcBasisFunctions", "span", "N", "left", "right", "j", "saved", "r", "rv", "lv", "temp", "calcBSplinePoint", "P", "C", "point", "Nj", "wNj", "w", "x", "y", "z", "calcBasisFunctionDerivatives", "zeroArr", "i", "ders", "slice", "ndu", "s1", "s2", "a", "k", "d", "rk", "pk", "j1", "j2", "calcBSplineDerivatives", "nd", "du", "CK", "nders", "Pw", "clone", "multiplyScalar", "add", "calcKoverI", "nom", "denom", "calcRationalCurveDerivatives", "Pders", "<PERSON><PERSON>", "wders", "v", "sub", "divideScalar", "calcNURBSDerivatives", "calcSurfacePoint", "q", "V", "target", "uspan", "vspan", "<PERSON>u", "Nv", "l", "Sw", "set", "calcVolumePoint", "W", "wspan", "Nw", "m"], "sources": ["C:/Users/<USER>/Documents/Apps_Creations/SiahMan_Racing4Speed_WebGame/siah-man-racing/node_modules/three/examples/jsm/curves/NURBSUtils.js"], "sourcesContent": ["import {\n\tVector3,\n\tVector4\n} from 'three';\n\n/**\n * @module NURBSUtils\n * @three_import import * as NURBSUtils from 'three/addons/curves/NURBSUtils.js';\n */\n\n/**\n * Finds knot vector span.\n *\n * @param {number} p - The degree.\n * @param {number} u - The parametric value.\n * @param {Array<number>} U - The knot vector.\n * @return {number} The span.\n */\nfunction findSpan( p, u, U ) {\n\n\tconst n = U.length - p - 1;\n\n\tif ( u >= U[ n ] ) {\n\n\t\treturn n - 1;\n\n\t}\n\n\tif ( u <= U[ p ] ) {\n\n\t\treturn p;\n\n\t}\n\n\tlet low = p;\n\tlet high = n;\n\tlet mid = Math.floor( ( low + high ) / 2 );\n\n\twhile ( u < U[ mid ] || u >= U[ mid + 1 ] ) {\n\n\t\tif ( u < U[ mid ] ) {\n\n\t\t\thigh = mid;\n\n\t\t} else {\n\n\t\t\tlow = mid;\n\n\t\t}\n\n\t\tmid = Math.floor( ( low + high ) / 2 );\n\n\t}\n\n\treturn mid;\n\n}\n\n/**\n * Calculates basis functions. See The NURBS Book, page 70, algorithm A2.2.\n *\n * @param {number} span - The span in which `u` lies.\n * @param {number} u - The parametric value.\n * @param {number} p - The degree.\n * @param {Array<number>} U - The knot vector.\n * @return {Array<number>} Array[p+1] with basis functions values.\n */\nfunction calcBasisFunctions( span, u, p, U ) {\n\n\tconst N = [];\n\tconst left = [];\n\tconst right = [];\n\tN[ 0 ] = 1.0;\n\n\tfor ( let j = 1; j <= p; ++ j ) {\n\n\t\tleft[ j ] = u - U[ span + 1 - j ];\n\t\tright[ j ] = U[ span + j ] - u;\n\n\t\tlet saved = 0.0;\n\n\t\tfor ( let r = 0; r < j; ++ r ) {\n\n\t\t\tconst rv = right[ r + 1 ];\n\t\t\tconst lv = left[ j - r ];\n\t\t\tconst temp = N[ r ] / ( rv + lv );\n\t\t\tN[ r ] = saved + rv * temp;\n\t\t\tsaved = lv * temp;\n\n\t\t}\n\n\t\tN[ j ] = saved;\n\n\t}\n\n\treturn N;\n\n}\n\n/**\n * Calculates B-Spline curve points. See The NURBS Book, page 82, algorithm A3.1.\n *\n * @param {number} p - The degree of the B-Spline.\n * @param {Array<number>} U - The knot vector.\n * @param {Array<Vector4>} P - The control points\n * @param {number} u - The parametric point.\n * @return {Vector4} The point for given `u`.\n */\nfunction calcBSplinePoint( p, U, P, u ) {\n\n\tconst span = findSpan( p, u, U );\n\tconst N = calcBasisFunctions( span, u, p, U );\n\tconst C = new Vector4( 0, 0, 0, 0 );\n\n\tfor ( let j = 0; j <= p; ++ j ) {\n\n\t\tconst point = P[ span - p + j ];\n\t\tconst Nj = N[ j ];\n\t\tconst wNj = point.w * Nj;\n\t\tC.x += point.x * wNj;\n\t\tC.y += point.y * wNj;\n\t\tC.z += point.z * wNj;\n\t\tC.w += point.w * Nj;\n\n\t}\n\n\treturn C;\n\n}\n\n/**\n * Calculates basis functions derivatives. See The NURBS Book, page 72, algorithm A2.3.\n *\n * @param {number} span - The span in which `u` lies.\n * @param {number} u - The parametric point.\n * @param {number} p - The degree.\n * @param {number} n - number of derivatives to calculate\n * @param {Array<number>} U - The knot vector.\n * @return {Array<Array<number>>} An array[n+1][p+1] with basis functions derivatives.\n */\nfunction calcBasisFunctionDerivatives( span, u, p, n, U ) {\n\n\tconst zeroArr = [];\n\tfor ( let i = 0; i <= p; ++ i )\n\t\tzeroArr[ i ] = 0.0;\n\n\tconst ders = [];\n\n\tfor ( let i = 0; i <= n; ++ i )\n\t\tders[ i ] = zeroArr.slice( 0 );\n\n\tconst ndu = [];\n\n\tfor ( let i = 0; i <= p; ++ i )\n\t\tndu[ i ] = zeroArr.slice( 0 );\n\n\tndu[ 0 ][ 0 ] = 1.0;\n\n\tconst left = zeroArr.slice( 0 );\n\tconst right = zeroArr.slice( 0 );\n\n\tfor ( let j = 1; j <= p; ++ j ) {\n\n\t\tleft[ j ] = u - U[ span + 1 - j ];\n\t\tright[ j ] = U[ span + j ] - u;\n\n\t\tlet saved = 0.0;\n\n\t\tfor ( let r = 0; r < j; ++ r ) {\n\n\t\t\tconst rv = right[ r + 1 ];\n\t\t\tconst lv = left[ j - r ];\n\t\t\tndu[ j ][ r ] = rv + lv;\n\n\t\t\tconst temp = ndu[ r ][ j - 1 ] / ndu[ j ][ r ];\n\t\t\tndu[ r ][ j ] = saved + rv * temp;\n\t\t\tsaved = lv * temp;\n\n\t\t}\n\n\t\tndu[ j ][ j ] = saved;\n\n\t}\n\n\tfor ( let j = 0; j <= p; ++ j ) {\n\n\t\tders[ 0 ][ j ] = ndu[ j ][ p ];\n\n\t}\n\n\tfor ( let r = 0; r <= p; ++ r ) {\n\n\t\tlet s1 = 0;\n\t\tlet s2 = 1;\n\n\t\tconst a = [];\n\t\tfor ( let i = 0; i <= p; ++ i ) {\n\n\t\t\ta[ i ] = zeroArr.slice( 0 );\n\n\t\t}\n\n\t\ta[ 0 ][ 0 ] = 1.0;\n\n\t\tfor ( let k = 1; k <= n; ++ k ) {\n\n\t\t\tlet d = 0.0;\n\t\t\tconst rk = r - k;\n\t\t\tconst pk = p - k;\n\n\t\t\tif ( r >= k ) {\n\n\t\t\t\ta[ s2 ][ 0 ] = a[ s1 ][ 0 ] / ndu[ pk + 1 ][ rk ];\n\t\t\t\td = a[ s2 ][ 0 ] * ndu[ rk ][ pk ];\n\n\t\t\t}\n\n\t\t\tconst j1 = ( rk >= - 1 ) ? 1 : - rk;\n\t\t\tconst j2 = ( r - 1 <= pk ) ? k - 1 : p - r;\n\n\t\t\tfor ( let j = j1; j <= j2; ++ j ) {\n\n\t\t\t\ta[ s2 ][ j ] = ( a[ s1 ][ j ] - a[ s1 ][ j - 1 ] ) / ndu[ pk + 1 ][ rk + j ];\n\t\t\t\td += a[ s2 ][ j ] * ndu[ rk + j ][ pk ];\n\n\t\t\t}\n\n\t\t\tif ( r <= pk ) {\n\n\t\t\t\ta[ s2 ][ k ] = - a[ s1 ][ k - 1 ] / ndu[ pk + 1 ][ r ];\n\t\t\t\td += a[ s2 ][ k ] * ndu[ r ][ pk ];\n\n\t\t\t}\n\n\t\t\tders[ k ][ r ] = d;\n\n\t\t\tconst j = s1;\n\t\t\ts1 = s2;\n\t\t\ts2 = j;\n\n\t\t}\n\n\t}\n\n\tlet r = p;\n\n\tfor ( let k = 1; k <= n; ++ k ) {\n\n\t\tfor ( let j = 0; j <= p; ++ j ) {\n\n\t\t\tders[ k ][ j ] *= r;\n\n\t\t}\n\n\t\tr *= p - k;\n\n\t}\n\n\treturn ders;\n\n}\n\n/**\n * Calculates derivatives of a B-Spline. See The NURBS Book, page 93, algorithm A3.2.\n *\n * @param {number} p - The degree.\n * @param {Array<number>} U - The knot vector.\n * @param {Array<Vector4>} P - The control points\n * @param {number} u - The parametric point.\n * @param {number} nd - The number of derivatives.\n * @return {Array<Vector4>} An array[d+1] with derivatives.\n */\nfunction calcBSplineDerivatives( p, U, P, u, nd ) {\n\n\tconst du = nd < p ? nd : p;\n\tconst CK = [];\n\tconst span = findSpan( p, u, U );\n\tconst nders = calcBasisFunctionDerivatives( span, u, p, du, U );\n\tconst Pw = [];\n\n\tfor ( let i = 0; i < P.length; ++ i ) {\n\n\t\tconst point = P[ i ].clone();\n\t\tconst w = point.w;\n\n\t\tpoint.x *= w;\n\t\tpoint.y *= w;\n\t\tpoint.z *= w;\n\n\t\tPw[ i ] = point;\n\n\t}\n\n\tfor ( let k = 0; k <= du; ++ k ) {\n\n\t\tconst point = Pw[ span - p ].clone().multiplyScalar( nders[ k ][ 0 ] );\n\n\t\tfor ( let j = 1; j <= p; ++ j ) {\n\n\t\t\tpoint.add( Pw[ span - p + j ].clone().multiplyScalar( nders[ k ][ j ] ) );\n\n\t\t}\n\n\t\tCK[ k ] = point;\n\n\t}\n\n\tfor ( let k = du + 1; k <= nd + 1; ++ k ) {\n\n\t\tCK[ k ] = new Vector4( 0, 0, 0 );\n\n\t}\n\n\treturn CK;\n\n}\n\n/**\n * Calculates \"K over I\".\n *\n * @param {number} k - The K value.\n * @param {number} i - The I value.\n * @return {number} k!/(i!(k-i)!)\n */\nfunction calcKoverI( k, i ) {\n\n\tlet nom = 1;\n\n\tfor ( let j = 2; j <= k; ++ j ) {\n\n\t\tnom *= j;\n\n\t}\n\n\tlet denom = 1;\n\n\tfor ( let j = 2; j <= i; ++ j ) {\n\n\t\tdenom *= j;\n\n\t}\n\n\tfor ( let j = 2; j <= k - i; ++ j ) {\n\n\t\tdenom *= j;\n\n\t}\n\n\treturn nom / denom;\n\n}\n\n/**\n * Calculates derivatives (0-nd) of rational curve. See The NURBS Book, page 127, algorithm A4.2.\n *\n * @param {Array<Vector4>} Pders - Array with derivatives.\n * @return {Array<Vector3>} An array with derivatives for rational curve.\n */\nfunction calcRationalCurveDerivatives( Pders ) {\n\n\tconst nd = Pders.length;\n\tconst Aders = [];\n\tconst wders = [];\n\n\tfor ( let i = 0; i < nd; ++ i ) {\n\n\t\tconst point = Pders[ i ];\n\t\tAders[ i ] = new Vector3( point.x, point.y, point.z );\n\t\twders[ i ] = point.w;\n\n\t}\n\n\tconst CK = [];\n\n\tfor ( let k = 0; k < nd; ++ k ) {\n\n\t\tconst v = Aders[ k ].clone();\n\n\t\tfor ( let i = 1; i <= k; ++ i ) {\n\n\t\t\tv.sub( CK[ k - i ].clone().multiplyScalar( calcKoverI( k, i ) * wders[ i ] ) );\n\n\t\t}\n\n\t\tCK[ k ] = v.divideScalar( wders[ 0 ] );\n\n\t}\n\n\treturn CK;\n\n}\n\n/**\n * Calculates NURBS curve derivatives. See The NURBS Book, page 127, algorithm A4.2.\n *\n * @param {number} p - The degree.\n * @param {Array<number>} U - The knot vector.\n * @param {Array<Vector4>} P - The control points in homogeneous space.\n * @param {number} u - The parametric point.\n * @param {number} nd - The number of derivatives.\n * @return {Array<Vector3>} array with derivatives for rational curve.\n */\nfunction calcNURBSDerivatives( p, U, P, u, nd ) {\n\n\tconst Pders = calcBSplineDerivatives( p, U, P, u, nd );\n\treturn calcRationalCurveDerivatives( Pders );\n\n}\n\n/**\n * Calculates a rational B-Spline surface point. See The NURBS Book, page 134, algorithm A4.3.\n *\n * @param {number} p - The first degree of B-Spline surface.\n * @param {number} q - The second degree of B-Spline surface.\n * @param {Array<number>} U - The first knot vector.\n * @param {Array<number>} V - The second knot vector.\n * @param {Array<Array<Vector4>>} P - The control points in homogeneous space.\n * @param {number} u - The first parametric point.\n * @param {number} v - The second parametric point.\n * @param {Vector3} target - The target vector.\n */\nfunction calcSurfacePoint( p, q, U, V, P, u, v, target ) {\n\n\tconst uspan = findSpan( p, u, U );\n\tconst vspan = findSpan( q, v, V );\n\tconst Nu = calcBasisFunctions( uspan, u, p, U );\n\tconst Nv = calcBasisFunctions( vspan, v, q, V );\n\tconst temp = [];\n\n\tfor ( let l = 0; l <= q; ++ l ) {\n\n\t\ttemp[ l ] = new Vector4( 0, 0, 0, 0 );\n\t\tfor ( let k = 0; k <= p; ++ k ) {\n\n\t\t\tconst point = P[ uspan - p + k ][ vspan - q + l ].clone();\n\t\t\tconst w = point.w;\n\t\t\tpoint.x *= w;\n\t\t\tpoint.y *= w;\n\t\t\tpoint.z *= w;\n\t\t\ttemp[ l ].add( point.multiplyScalar( Nu[ k ] ) );\n\n\t\t}\n\n\t}\n\n\tconst Sw = new Vector4( 0, 0, 0, 0 );\n\tfor ( let l = 0; l <= q; ++ l ) {\n\n\t\tSw.add( temp[ l ].multiplyScalar( Nv[ l ] ) );\n\n\t}\n\n\tSw.divideScalar( Sw.w );\n\ttarget.set( Sw.x, Sw.y, Sw.z );\n\n}\n\n/**\n * Calculates a rational B-Spline volume point. See The NURBS Book, page 134, algorithm A4.3.\n *\n * @param {number} p - The first degree of B-Spline surface.\n * @param {number} q - The second degree of B-Spline surface.\n * @param {number} r - The third degree of B-Spline surface.\n * @param {Array<number>} U - The first knot vector.\n * @param {Array<number>} V - The second knot vector.\n * @param {Array<number>} W - The third knot vector.\n * @param {Array<Array<Array<Vector4>>>} P - The control points in homogeneous space.\n * @param {number} u - The first parametric point.\n * @param {number} v - The second parametric point.\n * @param {number} w - The third parametric point.\n * @param {Vector3} target - The target vector.\n */\nfunction calcVolumePoint( p, q, r, U, V, W, P, u, v, w, target ) {\n\n\tconst uspan = findSpan( p, u, U );\n\tconst vspan = findSpan( q, v, V );\n\tconst wspan = findSpan( r, w, W );\n\tconst Nu = calcBasisFunctions( uspan, u, p, U );\n\tconst Nv = calcBasisFunctions( vspan, v, q, V );\n\tconst Nw = calcBasisFunctions( wspan, w, r, W );\n\tconst temp = [];\n\n\tfor ( let m = 0; m <= r; ++ m ) {\n\n\t\ttemp[ m ] = [];\n\n\t\tfor ( let l = 0; l <= q; ++ l ) {\n\n\t\t\ttemp[ m ][ l ] = new Vector4( 0, 0, 0, 0 );\n\t\t\tfor ( let k = 0; k <= p; ++ k ) {\n\n\t\t\t\tconst point = P[ uspan - p + k ][ vspan - q + l ][ wspan - r + m ].clone();\n\t\t\t\tconst w = point.w;\n\t\t\t\tpoint.x *= w;\n\t\t\t\tpoint.y *= w;\n\t\t\t\tpoint.z *= w;\n\t\t\t\ttemp[ m ][ l ].add( point.multiplyScalar( Nu[ k ] ) );\n\n\t\t\t}\n\n\t\t}\n\n\t}\n\n\tconst Sw = new Vector4( 0, 0, 0, 0 );\n\tfor ( let m = 0; m <= r; ++ m ) {\n\n\t\tfor ( let l = 0; l <= q; ++ l ) {\n\n\t\t\tSw.add( temp[ m ][ l ].multiplyScalar( Nw[ m ] ).multiplyScalar( Nv[ l ] ) );\n\n\t\t}\n\n\t}\n\n\tSw.divideScalar( Sw.w );\n\ttarget.set( Sw.x, Sw.y, Sw.z );\n\n}\n\nexport {\n\tfindSpan,\n\tcalcBasisFunctions,\n\tcalcBSplinePoint,\n\tcalcBasisFunctionDerivatives,\n\tcalcBSplineDerivatives,\n\tcalcKoverI,\n\tcalcRationalCurveDerivatives,\n\tcalcNURBSDerivatives,\n\tcalcSurfacePoint,\n\tcalcVolumePoint,\n};\n"], "mappings": "AAAA,SACCA,OAAO,EACPC,OAAO,QACD,OAAO;;AAEd;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASC,QAAQA,CAAEC,CAAC,EAAEC,CAAC,EAAEC,CAAC,EAAG;EAE5B,MAAMC,CAAC,GAAGD,CAAC,CAACE,MAAM,GAAGJ,CAAC,GAAG,CAAC;EAE1B,IAAKC,CAAC,IAAIC,CAAC,CAAEC,CAAC,CAAE,EAAG;IAElB,OAAOA,CAAC,GAAG,CAAC;EAEb;EAEA,IAAKF,CAAC,IAAIC,CAAC,CAAEF,CAAC,CAAE,EAAG;IAElB,OAAOA,CAAC;EAET;EAEA,IAAIK,GAAG,GAAGL,CAAC;EACX,IAAIM,IAAI,GAAGH,CAAC;EACZ,IAAII,GAAG,GAAGC,IAAI,CAACC,KAAK,CAAE,CAAEJ,GAAG,GAAGC,IAAI,IAAK,CAAE,CAAC;EAE1C,OAAQL,CAAC,GAAGC,CAAC,CAAEK,GAAG,CAAE,IAAIN,CAAC,IAAIC,CAAC,CAAEK,GAAG,GAAG,CAAC,CAAE,EAAG;IAE3C,IAAKN,CAAC,GAAGC,CAAC,CAAEK,GAAG,CAAE,EAAG;MAEnBD,IAAI,GAAGC,GAAG;IAEX,CAAC,MAAM;MAENF,GAAG,GAAGE,GAAG;IAEV;IAEAA,GAAG,GAAGC,IAAI,CAACC,KAAK,CAAE,CAAEJ,GAAG,GAAGC,IAAI,IAAK,CAAE,CAAC;EAEvC;EAEA,OAAOC,GAAG;AAEX;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASG,kBAAkBA,CAAEC,IAAI,EAAEV,CAAC,EAAED,CAAC,EAAEE,CAAC,EAAG;EAE5C,MAAMU,CAAC,GAAG,EAAE;EACZ,MAAMC,IAAI,GAAG,EAAE;EACf,MAAMC,KAAK,GAAG,EAAE;EAChBF,CAAC,CAAE,CAAC,CAAE,GAAG,GAAG;EAEZ,KAAM,IAAIG,CAAC,GAAG,CAAC,EAAEA,CAAC,IAAIf,CAAC,EAAE,EAAGe,CAAC,EAAG;IAE/BF,IAAI,CAAEE,CAAC,CAAE,GAAGd,CAAC,GAAGC,CAAC,CAAES,IAAI,GAAG,CAAC,GAAGI,CAAC,CAAE;IACjCD,KAAK,CAAEC,CAAC,CAAE,GAAGb,CAAC,CAAES,IAAI,GAAGI,CAAC,CAAE,GAAGd,CAAC;IAE9B,IAAIe,KAAK,GAAG,GAAG;IAEf,KAAM,IAAIC,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGF,CAAC,EAAE,EAAGE,CAAC,EAAG;MAE9B,MAAMC,EAAE,GAAGJ,KAAK,CAAEG,CAAC,GAAG,CAAC,CAAE;MACzB,MAAME,EAAE,GAAGN,IAAI,CAAEE,CAAC,GAAGE,CAAC,CAAE;MACxB,MAAMG,IAAI,GAAGR,CAAC,CAAEK,CAAC,CAAE,IAAKC,EAAE,GAAGC,EAAE,CAAE;MACjCP,CAAC,CAAEK,CAAC,CAAE,GAAGD,KAAK,GAAGE,EAAE,GAAGE,IAAI;MAC1BJ,KAAK,GAAGG,EAAE,GAAGC,IAAI;IAElB;IAEAR,CAAC,CAAEG,CAAC,CAAE,GAAGC,KAAK;EAEf;EAEA,OAAOJ,CAAC;AAET;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASS,gBAAgBA,CAAErB,CAAC,EAAEE,CAAC,EAAEoB,CAAC,EAAErB,CAAC,EAAG;EAEvC,MAAMU,IAAI,GAAGZ,QAAQ,CAAEC,CAAC,EAAEC,CAAC,EAAEC,CAAE,CAAC;EAChC,MAAMU,CAAC,GAAGF,kBAAkB,CAAEC,IAAI,EAAEV,CAAC,EAAED,CAAC,EAAEE,CAAE,CAAC;EAC7C,MAAMqB,CAAC,GAAG,IAAIzB,OAAO,CAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAE,CAAC;EAEnC,KAAM,IAAIiB,CAAC,GAAG,CAAC,EAAEA,CAAC,IAAIf,CAAC,EAAE,EAAGe,CAAC,EAAG;IAE/B,MAAMS,KAAK,GAAGF,CAAC,CAAEX,IAAI,GAAGX,CAAC,GAAGe,CAAC,CAAE;IAC/B,MAAMU,EAAE,GAAGb,CAAC,CAAEG,CAAC,CAAE;IACjB,MAAMW,GAAG,GAAGF,KAAK,CAACG,CAAC,GAAGF,EAAE;IACxBF,CAAC,CAACK,CAAC,IAAIJ,KAAK,CAACI,CAAC,GAAGF,GAAG;IACpBH,CAAC,CAACM,CAAC,IAAIL,KAAK,CAACK,CAAC,GAAGH,GAAG;IACpBH,CAAC,CAACO,CAAC,IAAIN,KAAK,CAACM,CAAC,GAAGJ,GAAG;IACpBH,CAAC,CAACI,CAAC,IAAIH,KAAK,CAACG,CAAC,GAAGF,EAAE;EAEpB;EAEA,OAAOF,CAAC;AAET;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASQ,4BAA4BA,CAAEpB,IAAI,EAAEV,CAAC,EAAED,CAAC,EAAEG,CAAC,EAAED,CAAC,EAAG;EAEzD,MAAM8B,OAAO,GAAG,EAAE;EAClB,KAAM,IAAIC,CAAC,GAAG,CAAC,EAAEA,CAAC,IAAIjC,CAAC,EAAE,EAAGiC,CAAC,EAC5BD,OAAO,CAAEC,CAAC,CAAE,GAAG,GAAG;EAEnB,MAAMC,IAAI,GAAG,EAAE;EAEf,KAAM,IAAID,CAAC,GAAG,CAAC,EAAEA,CAAC,IAAI9B,CAAC,EAAE,EAAG8B,CAAC,EAC5BC,IAAI,CAAED,CAAC,CAAE,GAAGD,OAAO,CAACG,KAAK,CAAE,CAAE,CAAC;EAE/B,MAAMC,GAAG,GAAG,EAAE;EAEd,KAAM,IAAIH,CAAC,GAAG,CAAC,EAAEA,CAAC,IAAIjC,CAAC,EAAE,EAAGiC,CAAC,EAC5BG,GAAG,CAAEH,CAAC,CAAE,GAAGD,OAAO,CAACG,KAAK,CAAE,CAAE,CAAC;EAE9BC,GAAG,CAAE,CAAC,CAAE,CAAE,CAAC,CAAE,GAAG,GAAG;EAEnB,MAAMvB,IAAI,GAAGmB,OAAO,CAACG,KAAK,CAAE,CAAE,CAAC;EAC/B,MAAMrB,KAAK,GAAGkB,OAAO,CAACG,KAAK,CAAE,CAAE,CAAC;EAEhC,KAAM,IAAIpB,CAAC,GAAG,CAAC,EAAEA,CAAC,IAAIf,CAAC,EAAE,EAAGe,CAAC,EAAG;IAE/BF,IAAI,CAAEE,CAAC,CAAE,GAAGd,CAAC,GAAGC,CAAC,CAAES,IAAI,GAAG,CAAC,GAAGI,CAAC,CAAE;IACjCD,KAAK,CAAEC,CAAC,CAAE,GAAGb,CAAC,CAAES,IAAI,GAAGI,CAAC,CAAE,GAAGd,CAAC;IAE9B,IAAIe,KAAK,GAAG,GAAG;IAEf,KAAM,IAAIC,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGF,CAAC,EAAE,EAAGE,CAAC,EAAG;MAE9B,MAAMC,EAAE,GAAGJ,KAAK,CAAEG,CAAC,GAAG,CAAC,CAAE;MACzB,MAAME,EAAE,GAAGN,IAAI,CAAEE,CAAC,GAAGE,CAAC,CAAE;MACxBmB,GAAG,CAAErB,CAAC,CAAE,CAAEE,CAAC,CAAE,GAAGC,EAAE,GAAGC,EAAE;MAEvB,MAAMC,IAAI,GAAGgB,GAAG,CAAEnB,CAAC,CAAE,CAAEF,CAAC,GAAG,CAAC,CAAE,GAAGqB,GAAG,CAAErB,CAAC,CAAE,CAAEE,CAAC,CAAE;MAC9CmB,GAAG,CAAEnB,CAAC,CAAE,CAAEF,CAAC,CAAE,GAAGC,KAAK,GAAGE,EAAE,GAAGE,IAAI;MACjCJ,KAAK,GAAGG,EAAE,GAAGC,IAAI;IAElB;IAEAgB,GAAG,CAAErB,CAAC,CAAE,CAAEA,CAAC,CAAE,GAAGC,KAAK;EAEtB;EAEA,KAAM,IAAID,CAAC,GAAG,CAAC,EAAEA,CAAC,IAAIf,CAAC,EAAE,EAAGe,CAAC,EAAG;IAE/BmB,IAAI,CAAE,CAAC,CAAE,CAAEnB,CAAC,CAAE,GAAGqB,GAAG,CAAErB,CAAC,CAAE,CAAEf,CAAC,CAAE;EAE/B;EAEA,KAAM,IAAIiB,CAAC,GAAG,CAAC,EAAEA,CAAC,IAAIjB,CAAC,EAAE,EAAGiB,CAAC,EAAG;IAE/B,IAAIoB,EAAE,GAAG,CAAC;IACV,IAAIC,EAAE,GAAG,CAAC;IAEV,MAAMC,CAAC,GAAG,EAAE;IACZ,KAAM,IAAIN,CAAC,GAAG,CAAC,EAAEA,CAAC,IAAIjC,CAAC,EAAE,EAAGiC,CAAC,EAAG;MAE/BM,CAAC,CAAEN,CAAC,CAAE,GAAGD,OAAO,CAACG,KAAK,CAAE,CAAE,CAAC;IAE5B;IAEAI,CAAC,CAAE,CAAC,CAAE,CAAE,CAAC,CAAE,GAAG,GAAG;IAEjB,KAAM,IAAIC,CAAC,GAAG,CAAC,EAAEA,CAAC,IAAIrC,CAAC,EAAE,EAAGqC,CAAC,EAAG;MAE/B,IAAIC,CAAC,GAAG,GAAG;MACX,MAAMC,EAAE,GAAGzB,CAAC,GAAGuB,CAAC;MAChB,MAAMG,EAAE,GAAG3C,CAAC,GAAGwC,CAAC;MAEhB,IAAKvB,CAAC,IAAIuB,CAAC,EAAG;QAEbD,CAAC,CAAED,EAAE,CAAE,CAAE,CAAC,CAAE,GAAGC,CAAC,CAAEF,EAAE,CAAE,CAAE,CAAC,CAAE,GAAGD,GAAG,CAAEO,EAAE,GAAG,CAAC,CAAE,CAAED,EAAE,CAAE;QACjDD,CAAC,GAAGF,CAAC,CAAED,EAAE,CAAE,CAAE,CAAC,CAAE,GAAGF,GAAG,CAAEM,EAAE,CAAE,CAAEC,EAAE,CAAE;MAEnC;MAEA,MAAMC,EAAE,GAAKF,EAAE,IAAI,CAAE,CAAC,GAAK,CAAC,GAAG,CAAEA,EAAE;MACnC,MAAMG,EAAE,GAAK5B,CAAC,GAAG,CAAC,IAAI0B,EAAE,GAAKH,CAAC,GAAG,CAAC,GAAGxC,CAAC,GAAGiB,CAAC;MAE1C,KAAM,IAAIF,CAAC,GAAG6B,EAAE,EAAE7B,CAAC,IAAI8B,EAAE,EAAE,EAAG9B,CAAC,EAAG;QAEjCwB,CAAC,CAAED,EAAE,CAAE,CAAEvB,CAAC,CAAE,GAAG,CAAEwB,CAAC,CAAEF,EAAE,CAAE,CAAEtB,CAAC,CAAE,GAAGwB,CAAC,CAAEF,EAAE,CAAE,CAAEtB,CAAC,GAAG,CAAC,CAAE,IAAKqB,GAAG,CAAEO,EAAE,GAAG,CAAC,CAAE,CAAED,EAAE,GAAG3B,CAAC,CAAE;QAC5E0B,CAAC,IAAIF,CAAC,CAAED,EAAE,CAAE,CAAEvB,CAAC,CAAE,GAAGqB,GAAG,CAAEM,EAAE,GAAG3B,CAAC,CAAE,CAAE4B,EAAE,CAAE;MAExC;MAEA,IAAK1B,CAAC,IAAI0B,EAAE,EAAG;QAEdJ,CAAC,CAAED,EAAE,CAAE,CAAEE,CAAC,CAAE,GAAG,CAAED,CAAC,CAAEF,EAAE,CAAE,CAAEG,CAAC,GAAG,CAAC,CAAE,GAAGJ,GAAG,CAAEO,EAAE,GAAG,CAAC,CAAE,CAAE1B,CAAC,CAAE;QACtDwB,CAAC,IAAIF,CAAC,CAAED,EAAE,CAAE,CAAEE,CAAC,CAAE,GAAGJ,GAAG,CAAEnB,CAAC,CAAE,CAAE0B,EAAE,CAAE;MAEnC;MAEAT,IAAI,CAAEM,CAAC,CAAE,CAAEvB,CAAC,CAAE,GAAGwB,CAAC;MAElB,MAAM1B,CAAC,GAAGsB,EAAE;MACZA,EAAE,GAAGC,EAAE;MACPA,EAAE,GAAGvB,CAAC;IAEP;EAED;EAEA,IAAIE,CAAC,GAAGjB,CAAC;EAET,KAAM,IAAIwC,CAAC,GAAG,CAAC,EAAEA,CAAC,IAAIrC,CAAC,EAAE,EAAGqC,CAAC,EAAG;IAE/B,KAAM,IAAIzB,CAAC,GAAG,CAAC,EAAEA,CAAC,IAAIf,CAAC,EAAE,EAAGe,CAAC,EAAG;MAE/BmB,IAAI,CAAEM,CAAC,CAAE,CAAEzB,CAAC,CAAE,IAAIE,CAAC;IAEpB;IAEAA,CAAC,IAAIjB,CAAC,GAAGwC,CAAC;EAEX;EAEA,OAAON,IAAI;AAEZ;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASY,sBAAsBA,CAAE9C,CAAC,EAAEE,CAAC,EAAEoB,CAAC,EAAErB,CAAC,EAAE8C,EAAE,EAAG;EAEjD,MAAMC,EAAE,GAAGD,EAAE,GAAG/C,CAAC,GAAG+C,EAAE,GAAG/C,CAAC;EAC1B,MAAMiD,EAAE,GAAG,EAAE;EACb,MAAMtC,IAAI,GAAGZ,QAAQ,CAAEC,CAAC,EAAEC,CAAC,EAAEC,CAAE,CAAC;EAChC,MAAMgD,KAAK,GAAGnB,4BAA4B,CAAEpB,IAAI,EAAEV,CAAC,EAAED,CAAC,EAAEgD,EAAE,EAAE9C,CAAE,CAAC;EAC/D,MAAMiD,EAAE,GAAG,EAAE;EAEb,KAAM,IAAIlB,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGX,CAAC,CAAClB,MAAM,EAAE,EAAG6B,CAAC,EAAG;IAErC,MAAMT,KAAK,GAAGF,CAAC,CAAEW,CAAC,CAAE,CAACmB,KAAK,CAAC,CAAC;IAC5B,MAAMzB,CAAC,GAAGH,KAAK,CAACG,CAAC;IAEjBH,KAAK,CAACI,CAAC,IAAID,CAAC;IACZH,KAAK,CAACK,CAAC,IAAIF,CAAC;IACZH,KAAK,CAACM,CAAC,IAAIH,CAAC;IAEZwB,EAAE,CAAElB,CAAC,CAAE,GAAGT,KAAK;EAEhB;EAEA,KAAM,IAAIgB,CAAC,GAAG,CAAC,EAAEA,CAAC,IAAIQ,EAAE,EAAE,EAAGR,CAAC,EAAG;IAEhC,MAAMhB,KAAK,GAAG2B,EAAE,CAAExC,IAAI,GAAGX,CAAC,CAAE,CAACoD,KAAK,CAAC,CAAC,CAACC,cAAc,CAAEH,KAAK,CAAEV,CAAC,CAAE,CAAE,CAAC,CAAG,CAAC;IAEtE,KAAM,IAAIzB,CAAC,GAAG,CAAC,EAAEA,CAAC,IAAIf,CAAC,EAAE,EAAGe,CAAC,EAAG;MAE/BS,KAAK,CAAC8B,GAAG,CAAEH,EAAE,CAAExC,IAAI,GAAGX,CAAC,GAAGe,CAAC,CAAE,CAACqC,KAAK,CAAC,CAAC,CAACC,cAAc,CAAEH,KAAK,CAAEV,CAAC,CAAE,CAAEzB,CAAC,CAAG,CAAE,CAAC;IAE1E;IAEAkC,EAAE,CAAET,CAAC,CAAE,GAAGhB,KAAK;EAEhB;EAEA,KAAM,IAAIgB,CAAC,GAAGQ,EAAE,GAAG,CAAC,EAAER,CAAC,IAAIO,EAAE,GAAG,CAAC,EAAE,EAAGP,CAAC,EAAG;IAEzCS,EAAE,CAAET,CAAC,CAAE,GAAG,IAAI1C,OAAO,CAAE,CAAC,EAAE,CAAC,EAAE,CAAE,CAAC;EAEjC;EAEA,OAAOmD,EAAE;AAEV;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASM,UAAUA,CAAEf,CAAC,EAAEP,CAAC,EAAG;EAE3B,IAAIuB,GAAG,GAAG,CAAC;EAEX,KAAM,IAAIzC,CAAC,GAAG,CAAC,EAAEA,CAAC,IAAIyB,CAAC,EAAE,EAAGzB,CAAC,EAAG;IAE/ByC,GAAG,IAAIzC,CAAC;EAET;EAEA,IAAI0C,KAAK,GAAG,CAAC;EAEb,KAAM,IAAI1C,CAAC,GAAG,CAAC,EAAEA,CAAC,IAAIkB,CAAC,EAAE,EAAGlB,CAAC,EAAG;IAE/B0C,KAAK,IAAI1C,CAAC;EAEX;EAEA,KAAM,IAAIA,CAAC,GAAG,CAAC,EAAEA,CAAC,IAAIyB,CAAC,GAAGP,CAAC,EAAE,EAAGlB,CAAC,EAAG;IAEnC0C,KAAK,IAAI1C,CAAC;EAEX;EAEA,OAAOyC,GAAG,GAAGC,KAAK;AAEnB;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA,SAASC,4BAA4BA,CAAEC,KAAK,EAAG;EAE9C,MAAMZ,EAAE,GAAGY,KAAK,CAACvD,MAAM;EACvB,MAAMwD,KAAK,GAAG,EAAE;EAChB,MAAMC,KAAK,GAAG,EAAE;EAEhB,KAAM,IAAI5B,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGc,EAAE,EAAE,EAAGd,CAAC,EAAG;IAE/B,MAAMT,KAAK,GAAGmC,KAAK,CAAE1B,CAAC,CAAE;IACxB2B,KAAK,CAAE3B,CAAC,CAAE,GAAG,IAAIpC,OAAO,CAAE2B,KAAK,CAACI,CAAC,EAAEJ,KAAK,CAACK,CAAC,EAAEL,KAAK,CAACM,CAAE,CAAC;IACrD+B,KAAK,CAAE5B,CAAC,CAAE,GAAGT,KAAK,CAACG,CAAC;EAErB;EAEA,MAAMsB,EAAE,GAAG,EAAE;EAEb,KAAM,IAAIT,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGO,EAAE,EAAE,EAAGP,CAAC,EAAG;IAE/B,MAAMsB,CAAC,GAAGF,KAAK,CAAEpB,CAAC,CAAE,CAACY,KAAK,CAAC,CAAC;IAE5B,KAAM,IAAInB,CAAC,GAAG,CAAC,EAAEA,CAAC,IAAIO,CAAC,EAAE,EAAGP,CAAC,EAAG;MAE/B6B,CAAC,CAACC,GAAG,CAAEd,EAAE,CAAET,CAAC,GAAGP,CAAC,CAAE,CAACmB,KAAK,CAAC,CAAC,CAACC,cAAc,CAAEE,UAAU,CAAEf,CAAC,EAAEP,CAAE,CAAC,GAAG4B,KAAK,CAAE5B,CAAC,CAAG,CAAE,CAAC;IAE/E;IAEAgB,EAAE,CAAET,CAAC,CAAE,GAAGsB,CAAC,CAACE,YAAY,CAAEH,KAAK,CAAE,CAAC,CAAG,CAAC;EAEvC;EAEA,OAAOZ,EAAE;AAEV;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASgB,oBAAoBA,CAAEjE,CAAC,EAAEE,CAAC,EAAEoB,CAAC,EAAErB,CAAC,EAAE8C,EAAE,EAAG;EAE/C,MAAMY,KAAK,GAAGb,sBAAsB,CAAE9C,CAAC,EAAEE,CAAC,EAAEoB,CAAC,EAAErB,CAAC,EAAE8C,EAAG,CAAC;EACtD,OAAOW,4BAA4B,CAAEC,KAAM,CAAC;AAE7C;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASO,gBAAgBA,CAAElE,CAAC,EAAEmE,CAAC,EAAEjE,CAAC,EAAEkE,CAAC,EAAE9C,CAAC,EAAErB,CAAC,EAAE6D,CAAC,EAAEO,MAAM,EAAG;EAExD,MAAMC,KAAK,GAAGvE,QAAQ,CAAEC,CAAC,EAAEC,CAAC,EAAEC,CAAE,CAAC;EACjC,MAAMqE,KAAK,GAAGxE,QAAQ,CAAEoE,CAAC,EAAEL,CAAC,EAAEM,CAAE,CAAC;EACjC,MAAMI,EAAE,GAAG9D,kBAAkB,CAAE4D,KAAK,EAAErE,CAAC,EAAED,CAAC,EAAEE,CAAE,CAAC;EAC/C,MAAMuE,EAAE,GAAG/D,kBAAkB,CAAE6D,KAAK,EAAET,CAAC,EAAEK,CAAC,EAAEC,CAAE,CAAC;EAC/C,MAAMhD,IAAI,GAAG,EAAE;EAEf,KAAM,IAAIsD,CAAC,GAAG,CAAC,EAAEA,CAAC,IAAIP,CAAC,EAAE,EAAGO,CAAC,EAAG;IAE/BtD,IAAI,CAAEsD,CAAC,CAAE,GAAG,IAAI5E,OAAO,CAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAE,CAAC;IACrC,KAAM,IAAI0C,CAAC,GAAG,CAAC,EAAEA,CAAC,IAAIxC,CAAC,EAAE,EAAGwC,CAAC,EAAG;MAE/B,MAAMhB,KAAK,GAAGF,CAAC,CAAEgD,KAAK,GAAGtE,CAAC,GAAGwC,CAAC,CAAE,CAAE+B,KAAK,GAAGJ,CAAC,GAAGO,CAAC,CAAE,CAACtB,KAAK,CAAC,CAAC;MACzD,MAAMzB,CAAC,GAAGH,KAAK,CAACG,CAAC;MACjBH,KAAK,CAACI,CAAC,IAAID,CAAC;MACZH,KAAK,CAACK,CAAC,IAAIF,CAAC;MACZH,KAAK,CAACM,CAAC,IAAIH,CAAC;MACZP,IAAI,CAAEsD,CAAC,CAAE,CAACpB,GAAG,CAAE9B,KAAK,CAAC6B,cAAc,CAAEmB,EAAE,CAAEhC,CAAC,CAAG,CAAE,CAAC;IAEjD;EAED;EAEA,MAAMmC,EAAE,GAAG,IAAI7E,OAAO,CAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAE,CAAC;EACpC,KAAM,IAAI4E,CAAC,GAAG,CAAC,EAAEA,CAAC,IAAIP,CAAC,EAAE,EAAGO,CAAC,EAAG;IAE/BC,EAAE,CAACrB,GAAG,CAAElC,IAAI,CAAEsD,CAAC,CAAE,CAACrB,cAAc,CAAEoB,EAAE,CAAEC,CAAC,CAAG,CAAE,CAAC;EAE9C;EAEAC,EAAE,CAACX,YAAY,CAAEW,EAAE,CAAChD,CAAE,CAAC;EACvB0C,MAAM,CAACO,GAAG,CAAED,EAAE,CAAC/C,CAAC,EAAE+C,EAAE,CAAC9C,CAAC,EAAE8C,EAAE,CAAC7C,CAAE,CAAC;AAE/B;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAAS+C,eAAeA,CAAE7E,CAAC,EAAEmE,CAAC,EAAElD,CAAC,EAAEf,CAAC,EAAEkE,CAAC,EAAEU,CAAC,EAAExD,CAAC,EAAErB,CAAC,EAAE6D,CAAC,EAAEnC,CAAC,EAAE0C,MAAM,EAAG;EAEhE,MAAMC,KAAK,GAAGvE,QAAQ,CAAEC,CAAC,EAAEC,CAAC,EAAEC,CAAE,CAAC;EACjC,MAAMqE,KAAK,GAAGxE,QAAQ,CAAEoE,CAAC,EAAEL,CAAC,EAAEM,CAAE,CAAC;EACjC,MAAMW,KAAK,GAAGhF,QAAQ,CAAEkB,CAAC,EAAEU,CAAC,EAAEmD,CAAE,CAAC;EACjC,MAAMN,EAAE,GAAG9D,kBAAkB,CAAE4D,KAAK,EAAErE,CAAC,EAAED,CAAC,EAAEE,CAAE,CAAC;EAC/C,MAAMuE,EAAE,GAAG/D,kBAAkB,CAAE6D,KAAK,EAAET,CAAC,EAAEK,CAAC,EAAEC,CAAE,CAAC;EAC/C,MAAMY,EAAE,GAAGtE,kBAAkB,CAAEqE,KAAK,EAAEpD,CAAC,EAAEV,CAAC,EAAE6D,CAAE,CAAC;EAC/C,MAAM1D,IAAI,GAAG,EAAE;EAEf,KAAM,IAAI6D,CAAC,GAAG,CAAC,EAAEA,CAAC,IAAIhE,CAAC,EAAE,EAAGgE,CAAC,EAAG;IAE/B7D,IAAI,CAAE6D,CAAC,CAAE,GAAG,EAAE;IAEd,KAAM,IAAIP,CAAC,GAAG,CAAC,EAAEA,CAAC,IAAIP,CAAC,EAAE,EAAGO,CAAC,EAAG;MAE/BtD,IAAI,CAAE6D,CAAC,CAAE,CAAEP,CAAC,CAAE,GAAG,IAAI5E,OAAO,CAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAE,CAAC;MAC1C,KAAM,IAAI0C,CAAC,GAAG,CAAC,EAAEA,CAAC,IAAIxC,CAAC,EAAE,EAAGwC,CAAC,EAAG;QAE/B,MAAMhB,KAAK,GAAGF,CAAC,CAAEgD,KAAK,GAAGtE,CAAC,GAAGwC,CAAC,CAAE,CAAE+B,KAAK,GAAGJ,CAAC,GAAGO,CAAC,CAAE,CAAEK,KAAK,GAAG9D,CAAC,GAAGgE,CAAC,CAAE,CAAC7B,KAAK,CAAC,CAAC;QAC1E,MAAMzB,CAAC,GAAGH,KAAK,CAACG,CAAC;QACjBH,KAAK,CAACI,CAAC,IAAID,CAAC;QACZH,KAAK,CAACK,CAAC,IAAIF,CAAC;QACZH,KAAK,CAACM,CAAC,IAAIH,CAAC;QACZP,IAAI,CAAE6D,CAAC,CAAE,CAAEP,CAAC,CAAE,CAACpB,GAAG,CAAE9B,KAAK,CAAC6B,cAAc,CAAEmB,EAAE,CAAEhC,CAAC,CAAG,CAAE,CAAC;MAEtD;IAED;EAED;EAEA,MAAMmC,EAAE,GAAG,IAAI7E,OAAO,CAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAE,CAAC;EACpC,KAAM,IAAImF,CAAC,GAAG,CAAC,EAAEA,CAAC,IAAIhE,CAAC,EAAE,EAAGgE,CAAC,EAAG;IAE/B,KAAM,IAAIP,CAAC,GAAG,CAAC,EAAEA,CAAC,IAAIP,CAAC,EAAE,EAAGO,CAAC,EAAG;MAE/BC,EAAE,CAACrB,GAAG,CAAElC,IAAI,CAAE6D,CAAC,CAAE,CAAEP,CAAC,CAAE,CAACrB,cAAc,CAAE2B,EAAE,CAAEC,CAAC,CAAG,CAAC,CAAC5B,cAAc,CAAEoB,EAAE,CAAEC,CAAC,CAAG,CAAE,CAAC;IAE7E;EAED;EAEAC,EAAE,CAACX,YAAY,CAAEW,EAAE,CAAChD,CAAE,CAAC;EACvB0C,MAAM,CAACO,GAAG,CAAED,EAAE,CAAC/C,CAAC,EAAE+C,EAAE,CAAC9C,CAAC,EAAE8C,EAAE,CAAC7C,CAAE,CAAC;AAE/B;AAEA,SACC/B,QAAQ,EACRW,kBAAkB,EAClBW,gBAAgB,EAChBU,4BAA4B,EAC5Be,sBAAsB,EACtBS,UAAU,EACVG,4BAA4B,EAC5BO,oBAAoB,EACpBC,gBAAgB,EAChBW,eAAe", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}