{"ast": null, "code": "import _extends from '@babel/runtime/helpers/esm/extends';\nimport * as React from 'react';\nimport { useFrame } from '@react-three/fiber';\nconst Detailed = /* @__PURE__ */React.forwardRef(({\n  children,\n  hysteresis = 0,\n  distances,\n  ...props\n}, ref) => {\n  const lodRef = React.useRef(null);\n  React.useImperativeHandle(ref, () => lodRef.current, []);\n  React.useLayoutEffect(() => {\n    const {\n      current: lod\n    } = lodRef;\n    lod.levels.length = 0;\n    lod.children.forEach((object, index) => lod.levels.push({\n      object,\n      hysteresis,\n      distance: distances[index]\n    }));\n  });\n  useFrame(state => {\n    var _lodRef$current;\n    return (_lodRef$current = lodRef.current) == null ? void 0 : _lodRef$current.update(state.camera);\n  });\n  return /*#__PURE__*/React.createElement(\"lOD\", _extends({\n    ref: lodRef\n  }, props), children);\n});\nexport { Detailed };", "map": {"version": 3, "names": ["_extends", "React", "useFrame", "Detailed", "forwardRef", "children", "hysteresis", "distances", "props", "ref", "lodRef", "useRef", "useImperativeHandle", "current", "useLayoutEffect", "lod", "levels", "length", "for<PERSON>ach", "object", "index", "push", "distance", "state", "_lodRef$current", "update", "camera", "createElement"], "sources": ["C:/Users/<USER>/Documents/Apps_Creations/SiahMan_Racing4Speed_WebGame/siah-man-racing/node_modules/@react-three/drei/core/Detailed.js"], "sourcesContent": ["import _extends from '@babel/runtime/helpers/esm/extends';\nimport * as React from 'react';\nimport { useFrame } from '@react-three/fiber';\n\nconst Detailed = /* @__PURE__ */React.forwardRef(({\n  children,\n  hysteresis = 0,\n  distances,\n  ...props\n}, ref) => {\n  const lodRef = React.useRef(null);\n  React.useImperativeHandle(ref, () => lodRef.current, []);\n  React.useLayoutEffect(() => {\n    const {\n      current: lod\n    } = lodRef;\n    lod.levels.length = 0;\n    lod.children.forEach((object, index) => lod.levels.push({\n      object,\n      hysteresis,\n      distance: distances[index]\n    }));\n  });\n  useFrame(state => {\n    var _lodRef$current;\n    return (_lodRef$current = lodRef.current) == null ? void 0 : _lodRef$current.update(state.camera);\n  });\n  return /*#__PURE__*/React.createElement(\"lOD\", _extends({\n    ref: lodRef\n  }, props), children);\n});\n\nexport { Detailed };\n"], "mappings": "AAAA,OAAOA,QAAQ,MAAM,oCAAoC;AACzD,OAAO,KAAKC,KAAK,MAAM,OAAO;AAC9B,SAASC,QAAQ,QAAQ,oBAAoB;AAE7C,MAAMC,QAAQ,GAAG,eAAeF,KAAK,CAACG,UAAU,CAAC,CAAC;EAChDC,QAAQ;EACRC,UAAU,GAAG,CAAC;EACdC,SAAS;EACT,GAAGC;AACL,CAAC,EAAEC,GAAG,KAAK;EACT,MAAMC,MAAM,GAAGT,KAAK,CAACU,MAAM,CAAC,IAAI,CAAC;EACjCV,KAAK,CAACW,mBAAmB,CAACH,GAAG,EAAE,MAAMC,MAAM,CAACG,OAAO,EAAE,EAAE,CAAC;EACxDZ,KAAK,CAACa,eAAe,CAAC,MAAM;IAC1B,MAAM;MACJD,OAAO,EAAEE;IACX,CAAC,GAAGL,MAAM;IACVK,GAAG,CAACC,MAAM,CAACC,MAAM,GAAG,CAAC;IACrBF,GAAG,CAACV,QAAQ,CAACa,OAAO,CAAC,CAACC,MAAM,EAAEC,KAAK,KAAKL,GAAG,CAACC,MAAM,CAACK,IAAI,CAAC;MACtDF,MAAM;MACNb,UAAU;MACVgB,QAAQ,EAAEf,SAAS,CAACa,KAAK;IAC3B,CAAC,CAAC,CAAC;EACL,CAAC,CAAC;EACFlB,QAAQ,CAACqB,KAAK,IAAI;IAChB,IAAIC,eAAe;IACnB,OAAO,CAACA,eAAe,GAAGd,MAAM,CAACG,OAAO,KAAK,IAAI,GAAG,KAAK,CAAC,GAAGW,eAAe,CAACC,MAAM,CAACF,KAAK,CAACG,MAAM,CAAC;EACnG,CAAC,CAAC;EACF,OAAO,aAAazB,KAAK,CAAC0B,aAAa,CAAC,KAAK,EAAE3B,QAAQ,CAAC;IACtDS,GAAG,EAAEC;EACP,CAAC,EAAEF,KAAK,CAAC,EAAEH,QAAQ,CAAC;AACtB,CAAC,CAAC;AAEF,SAASF,QAAQ", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}