{"ast": null, "code": "var __defProp = Object.defineProperty;\nvar __defNormalProp = (obj, key, value) => key in obj ? __defProp(obj, key, {\n  enumerable: true,\n  configurable: true,\n  writable: true,\n  value\n}) : obj[key] = value;\nvar __publicField = (obj, key, value) => {\n  __defNormalProp(obj, typeof key !== \"symbol\" ? key + \"\" : key, value);\n  return value;\n};\nimport { GLTFLoader } from \"../loaders/GLTFLoader.js\";\nconst DEFAULT_HAND_PROFILE_PATH = \"https://cdn.jsdelivr.net/npm/@webxr-input-profiles/assets@1.0/dist/profiles/generic-hand/\";\nclass XRHandMeshModel {\n  constructor(handModel, controller, path = DEFAULT_HAND_PROFILE_PATH, handedness, customModelPath) {\n    __publicField(this, \"controller\");\n    __publicField(this, \"handModel\");\n    __publicField(this, \"bones\");\n    this.controller = controller;\n    this.handModel = handModel;\n    this.bones = [];\n    const loader = new GLTFLoader();\n    if (!customModelPath) loader.setPath(path);\n    loader.load(customModelPath != null ? customModelPath : `${handedness}.glb`, gltf => {\n      const object = gltf.scene.children[0];\n      this.handModel.add(object);\n      const mesh = object.getObjectByProperty(\"type\", \"SkinnedMesh\");\n      mesh.frustumCulled = false;\n      mesh.castShadow = true;\n      mesh.receiveShadow = true;\n      const joints = [\"wrist\", \"thumb-metacarpal\", \"thumb-phalanx-proximal\", \"thumb-phalanx-distal\", \"thumb-tip\", \"index-finger-metacarpal\", \"index-finger-phalanx-proximal\", \"index-finger-phalanx-intermediate\", \"index-finger-phalanx-distal\", \"index-finger-tip\", \"middle-finger-metacarpal\", \"middle-finger-phalanx-proximal\", \"middle-finger-phalanx-intermediate\", \"middle-finger-phalanx-distal\", \"middle-finger-tip\", \"ring-finger-metacarpal\", \"ring-finger-phalanx-proximal\", \"ring-finger-phalanx-intermediate\", \"ring-finger-phalanx-distal\", \"ring-finger-tip\", \"pinky-finger-metacarpal\", \"pinky-finger-phalanx-proximal\", \"pinky-finger-phalanx-intermediate\", \"pinky-finger-phalanx-distal\", \"pinky-finger-tip\"];\n      joints.forEach(jointName => {\n        const bone = object.getObjectByName(jointName);\n        if (bone !== void 0) {\n          bone.jointName = jointName;\n        } else {\n          console.warn(`Couldn't find ${jointName} in ${handedness} hand mesh`);\n        }\n        this.bones.push(bone);\n      });\n    });\n  }\n  updateMesh() {\n    const XRJoints = this.controller.joints;\n    for (let i = 0; i < this.bones.length; i++) {\n      const bone = this.bones[i];\n      if (bone) {\n        const XRJoint = XRJoints[bone.jointName];\n        if (XRJoint.visible) {\n          const position = XRJoint.position;\n          bone.position.copy(position);\n          bone.quaternion.copy(XRJoint.quaternion);\n        }\n      }\n    }\n  }\n}\nexport { XRHandMeshModel };", "map": {"version": 3, "names": ["DEFAULT_HAND_PROFILE_PATH", "XRHandMeshModel", "constructor", "handModel", "controller", "path", "handedness", "customModelPath", "__publicField", "bones", "loader", "GLTFLoader", "set<PERSON>ath", "load", "gltf", "object", "scene", "children", "add", "mesh", "getObjectByProperty", "frustumCulled", "<PERSON><PERSON><PERSON><PERSON>", "receiveShadow", "joints", "for<PERSON>ach", "jointName", "bone", "getObjectByName", "console", "warn", "push", "updateMesh", "XRJoints", "i", "length", "XRJoint", "visible", "position", "copy", "quaternion"], "sources": ["C:\\Users\\<USER>\\Documents\\Apps_Creations\\SiahMan_Racing4Speed_WebGame\\siah-man-racing\\node_modules\\src\\webxr\\XRHandMeshModel.ts"], "sourcesContent": ["import { Object3D } from 'three'\nimport { GLTFLoader } from '../loaders/GLTFLoader'\n\nconst DEFAULT_HAND_PROFILE_PATH =\n  'https://cdn.jsdelivr.net/npm/@webxr-input-profiles/assets@1.0/dist/profiles/generic-hand/'\n\nclass XRHandMeshModel {\n  controller: Object3D\n  handModel: Object3D\n  bones: Object3D[]\n\n  constructor(\n    handModel: Object3D,\n    controller: Object3D,\n    path: string = DEFAULT_HAND_PROFILE_PATH,\n    handedness: string,\n    customModelPath?: string,\n  ) {\n    this.controller = controller\n    this.handModel = handModel\n\n    this.bones = []\n\n    const loader = new GLTFLoader()\n    if (!customModelPath) loader.setPath(path)\n    loader.load(customModelPath ?? `${handedness}.glb`, (gltf: { scene: Object3D }) => {\n      const object = gltf.scene.children[0]\n      this.handModel.add(object)\n\n      const mesh = object.getObjectByProperty('type', 'SkinnedMesh')!\n      mesh.frustumCulled = false\n      mesh.castShadow = true\n      mesh.receiveShadow = true\n\n      const joints = [\n        'wrist',\n        'thumb-metacarpal',\n        'thumb-phalanx-proximal',\n        'thumb-phalanx-distal',\n        'thumb-tip',\n        'index-finger-metacarpal',\n        'index-finger-phalanx-proximal',\n        'index-finger-phalanx-intermediate',\n        'index-finger-phalanx-distal',\n        'index-finger-tip',\n        'middle-finger-metacarpal',\n        'middle-finger-phalanx-proximal',\n        'middle-finger-phalanx-intermediate',\n        'middle-finger-phalanx-distal',\n        'middle-finger-tip',\n        'ring-finger-metacarpal',\n        'ring-finger-phalanx-proximal',\n        'ring-finger-phalanx-intermediate',\n        'ring-finger-phalanx-distal',\n        'ring-finger-tip',\n        'pinky-finger-metacarpal',\n        'pinky-finger-phalanx-proximal',\n        'pinky-finger-phalanx-intermediate',\n        'pinky-finger-phalanx-distal',\n        'pinky-finger-tip',\n      ]\n\n      joints.forEach((jointName) => {\n        const bone = object.getObjectByName(jointName) as any\n\n        if (bone !== undefined) {\n          bone.jointName = jointName\n        } else {\n          console.warn(`Couldn't find ${jointName} in ${handedness} hand mesh`)\n        }\n\n        this.bones.push(bone)\n      })\n    })\n  }\n\n  updateMesh(): void {\n    // XR Joints\n    const XRJoints = (this.controller as any).joints\n\n    for (let i = 0; i < this.bones.length; i++) {\n      const bone = this.bones[i]\n\n      if (bone) {\n        const XRJoint = XRJoints[(bone as any).jointName]\n\n        if (XRJoint.visible) {\n          const position = XRJoint.position\n\n          bone.position.copy(position)\n          bone.quaternion.copy(XRJoint.quaternion)\n          // bone.scale.setScalar( XRJoint.jointRadius || defaultRadius );\n        }\n      }\n    }\n  }\n}\n\nexport { XRHandMeshModel }\n"], "mappings": ";;;;;;;;;;;;AAGA,MAAMA,yBAAA,GACJ;AAEF,MAAMC,eAAA,CAAgB;EAKpBC,YACEC,SAAA,EACAC,UAAA,EACAC,IAAA,GAAeL,yBAAA,EACfM,UAAA,EACAC,eAAA,EACA;IAVFC,aAAA;IACAA,aAAA;IACAA,aAAA;IASE,KAAKJ,UAAA,GAAaA,UAAA;IAClB,KAAKD,SAAA,GAAYA,SAAA;IAEjB,KAAKM,KAAA,GAAQ;IAEP,MAAAC,MAAA,GAAS,IAAIC,UAAA;IACnB,IAAI,CAACJ,eAAA,EAAiBG,MAAA,CAAOE,OAAA,CAAQP,IAAI;IACzCK,MAAA,CAAOG,IAAA,CAAKN,eAAA,WAAAA,eAAA,GAAmB,GAAGD,UAAA,QAAmBQ,IAAA,IAA8B;MACjF,MAAMC,MAAA,GAASD,IAAA,CAAKE,KAAA,CAAMC,QAAA,CAAS,CAAC;MAC/B,KAAAd,SAAA,CAAUe,GAAA,CAAIH,MAAM;MAEzB,MAAMI,IAAA,GAAOJ,MAAA,CAAOK,mBAAA,CAAoB,QAAQ,aAAa;MAC7DD,IAAA,CAAKE,aAAA,GAAgB;MACrBF,IAAA,CAAKG,UAAA,GAAa;MAClBH,IAAA,CAAKI,aAAA,GAAgB;MAErB,MAAMC,MAAA,GAAS,CACb,SACA,oBACA,0BACA,wBACA,aACA,2BACA,iCACA,qCACA,+BACA,oBACA,4BACA,kCACA,sCACA,gCACA,qBACA,0BACA,gCACA,oCACA,8BACA,mBACA,2BACA,iCACA,qCACA,+BACA;MAGKA,MAAA,CAAAC,OAAA,CAASC,SAAA,IAAc;QACtB,MAAAC,IAAA,GAAOZ,MAAA,CAAOa,eAAA,CAAgBF,SAAS;QAE7C,IAAIC,IAAA,KAAS,QAAW;UACtBA,IAAA,CAAKD,SAAA,GAAYA,SAAA;QAAA,OACZ;UACGG,OAAA,CAAAC,IAAA,CAAK,iBAAiBJ,SAAA,OAAgBpB,UAAA,YAAsB;QACtE;QAEK,KAAAG,KAAA,CAAMsB,IAAA,CAAKJ,IAAI;MAAA,CACrB;IAAA,CACF;EACH;EAEAK,WAAA,EAAmB;IAEX,MAAAC,QAAA,GAAY,KAAK7B,UAAA,CAAmBoB,MAAA;IAE1C,SAASU,CAAA,GAAI,GAAGA,CAAA,GAAI,KAAKzB,KAAA,CAAM0B,MAAA,EAAQD,CAAA,IAAK;MACpC,MAAAP,IAAA,GAAO,KAAKlB,KAAA,CAAMyB,CAAC;MAEzB,IAAIP,IAAA,EAAM;QACF,MAAAS,OAAA,GAAUH,QAAA,CAAUN,IAAA,CAAaD,SAAS;QAEhD,IAAIU,OAAA,CAAQC,OAAA,EAAS;UACnB,MAAMC,QAAA,GAAWF,OAAA,CAAQE,QAAA;UAEpBX,IAAA,CAAAW,QAAA,CAASC,IAAA,CAAKD,QAAQ;UACtBX,IAAA,CAAAa,UAAA,CAAWD,IAAA,CAAKH,OAAA,CAAQI,UAAU;QAEzC;MACF;IACF;EACF;AACF", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}