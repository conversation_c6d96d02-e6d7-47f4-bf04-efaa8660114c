{"ast": null, "code": "import { V, c as computeRubberband } from './maths-0ab39ae9.esm.js';\nfunction _toPrimitive(input, hint) {\n  if (typeof input !== \"object\" || input === null) return input;\n  var prim = input[Symbol.toPrimitive];\n  if (prim !== undefined) {\n    var res = prim.call(input, hint || \"default\");\n    if (typeof res !== \"object\") return res;\n    throw new TypeError(\"@@toPrimitive must return a primitive value.\");\n  }\n  return (hint === \"string\" ? String : Number)(input);\n}\nfunction _toPropertyKey(arg) {\n  var key = _toPrimitive(arg, \"string\");\n  return typeof key === \"symbol\" ? key : String(key);\n}\nfunction _defineProperty(obj, key, value) {\n  key = _toPropertyKey(key);\n  if (key in obj) {\n    Object.defineProperty(obj, key, {\n      value: value,\n      enumerable: true,\n      configurable: true,\n      writable: true\n    });\n  } else {\n    obj[key] = value;\n  }\n  return obj;\n}\nfunction ownKeys(e, r) {\n  var t = Object.keys(e);\n  if (Object.getOwnPropertySymbols) {\n    var o = Object.getOwnPropertySymbols(e);\n    r && (o = o.filter(function (r) {\n      return Object.getOwnPropertyDescriptor(e, r).enumerable;\n    })), t.push.apply(t, o);\n  }\n  return t;\n}\nfunction _objectSpread2(e) {\n  for (var r = 1; r < arguments.length; r++) {\n    var t = null != arguments[r] ? arguments[r] : {};\n    r % 2 ? ownKeys(Object(t), !0).forEach(function (r) {\n      _defineProperty(e, r, t[r]);\n    }) : Object.getOwnPropertyDescriptors ? Object.defineProperties(e, Object.getOwnPropertyDescriptors(t)) : ownKeys(Object(t)).forEach(function (r) {\n      Object.defineProperty(e, r, Object.getOwnPropertyDescriptor(t, r));\n    });\n  }\n  return e;\n}\nconst EVENT_TYPE_MAP = {\n  pointer: {\n    start: 'down',\n    change: 'move',\n    end: 'up'\n  },\n  mouse: {\n    start: 'down',\n    change: 'move',\n    end: 'up'\n  },\n  touch: {\n    start: 'start',\n    change: 'move',\n    end: 'end'\n  },\n  gesture: {\n    start: 'start',\n    change: 'change',\n    end: 'end'\n  }\n};\nfunction capitalize(string) {\n  if (!string) return '';\n  return string[0].toUpperCase() + string.slice(1);\n}\nconst actionsWithoutCaptureSupported = ['enter', 'leave'];\nfunction hasCapture(capture = false, actionKey) {\n  return capture && !actionsWithoutCaptureSupported.includes(actionKey);\n}\nfunction toHandlerProp(device, action = '', capture = false) {\n  const deviceProps = EVENT_TYPE_MAP[device];\n  const actionKey = deviceProps ? deviceProps[action] || action : action;\n  return 'on' + capitalize(device) + capitalize(actionKey) + (hasCapture(capture, actionKey) ? 'Capture' : '');\n}\nconst pointerCaptureEvents = ['gotpointercapture', 'lostpointercapture'];\nfunction parseProp(prop) {\n  let eventKey = prop.substring(2).toLowerCase();\n  const passive = !!~eventKey.indexOf('passive');\n  if (passive) eventKey = eventKey.replace('passive', '');\n  const captureKey = pointerCaptureEvents.includes(eventKey) ? 'capturecapture' : 'capture';\n  const capture = !!~eventKey.indexOf(captureKey);\n  if (capture) eventKey = eventKey.replace('capture', '');\n  return {\n    device: eventKey,\n    capture,\n    passive\n  };\n}\nfunction toDomEventType(device, action = '') {\n  const deviceProps = EVENT_TYPE_MAP[device];\n  const actionKey = deviceProps ? deviceProps[action] || action : action;\n  return device + actionKey;\n}\nfunction isTouch(event) {\n  return 'touches' in event;\n}\nfunction getPointerType(event) {\n  if (isTouch(event)) return 'touch';\n  if ('pointerType' in event) return event.pointerType;\n  return 'mouse';\n}\nfunction getCurrentTargetTouchList(event) {\n  return Array.from(event.touches).filter(e => {\n    var _event$currentTarget, _event$currentTarget$;\n    return e.target === event.currentTarget || ((_event$currentTarget = event.currentTarget) === null || _event$currentTarget === void 0 || (_event$currentTarget$ = _event$currentTarget.contains) === null || _event$currentTarget$ === void 0 ? void 0 : _event$currentTarget$.call(_event$currentTarget, e.target));\n  });\n}\nfunction getTouchList(event) {\n  return event.type === 'touchend' || event.type === 'touchcancel' ? event.changedTouches : event.targetTouches;\n}\nfunction getValueEvent(event) {\n  return isTouch(event) ? getTouchList(event)[0] : event;\n}\nfunction distanceAngle(P1, P2) {\n  try {\n    const dx = P2.clientX - P1.clientX;\n    const dy = P2.clientY - P1.clientY;\n    const cx = (P2.clientX + P1.clientX) / 2;\n    const cy = (P2.clientY + P1.clientY) / 2;\n    const distance = Math.hypot(dx, dy);\n    const angle = -(Math.atan2(dx, dy) * 180) / Math.PI;\n    const origin = [cx, cy];\n    return {\n      angle,\n      distance,\n      origin\n    };\n  } catch (_unused) {}\n  return null;\n}\nfunction touchIds(event) {\n  return getCurrentTargetTouchList(event).map(touch => touch.identifier);\n}\nfunction touchDistanceAngle(event, ids) {\n  const [P1, P2] = Array.from(event.touches).filter(touch => ids.includes(touch.identifier));\n  return distanceAngle(P1, P2);\n}\nfunction pointerId(event) {\n  const valueEvent = getValueEvent(event);\n  return isTouch(event) ? valueEvent.identifier : valueEvent.pointerId;\n}\nfunction pointerValues(event) {\n  const valueEvent = getValueEvent(event);\n  return [valueEvent.clientX, valueEvent.clientY];\n}\nconst LINE_HEIGHT = 40;\nconst PAGE_HEIGHT = 800;\nfunction wheelValues(event) {\n  let {\n    deltaX,\n    deltaY,\n    deltaMode\n  } = event;\n  if (deltaMode === 1) {\n    deltaX *= LINE_HEIGHT;\n    deltaY *= LINE_HEIGHT;\n  } else if (deltaMode === 2) {\n    deltaX *= PAGE_HEIGHT;\n    deltaY *= PAGE_HEIGHT;\n  }\n  return [deltaX, deltaY];\n}\nfunction scrollValues(event) {\n  var _ref, _ref2;\n  const {\n    scrollX,\n    scrollY,\n    scrollLeft,\n    scrollTop\n  } = event.currentTarget;\n  return [(_ref = scrollX !== null && scrollX !== void 0 ? scrollX : scrollLeft) !== null && _ref !== void 0 ? _ref : 0, (_ref2 = scrollY !== null && scrollY !== void 0 ? scrollY : scrollTop) !== null && _ref2 !== void 0 ? _ref2 : 0];\n}\nfunction getEventDetails(event) {\n  const payload = {};\n  if ('buttons' in event) payload.buttons = event.buttons;\n  if ('shiftKey' in event) {\n    const {\n      shiftKey,\n      altKey,\n      metaKey,\n      ctrlKey\n    } = event;\n    Object.assign(payload, {\n      shiftKey,\n      altKey,\n      metaKey,\n      ctrlKey\n    });\n  }\n  return payload;\n}\nfunction call(v, ...args) {\n  if (typeof v === 'function') {\n    return v(...args);\n  } else {\n    return v;\n  }\n}\nfunction noop() {}\nfunction chain(...fns) {\n  if (fns.length === 0) return noop;\n  if (fns.length === 1) return fns[0];\n  return function () {\n    let result;\n    for (const fn of fns) {\n      result = fn.apply(this, arguments) || result;\n    }\n    return result;\n  };\n}\nfunction assignDefault(value, fallback) {\n  return Object.assign({}, fallback, value || {});\n}\nconst BEFORE_LAST_KINEMATICS_DELAY = 32;\nclass Engine {\n  constructor(ctrl, args, key) {\n    this.ctrl = ctrl;\n    this.args = args;\n    this.key = key;\n    if (!this.state) {\n      this.state = {};\n      this.computeValues([0, 0]);\n      this.computeInitial();\n      if (this.init) this.init();\n      this.reset();\n    }\n  }\n  get state() {\n    return this.ctrl.state[this.key];\n  }\n  set state(state) {\n    this.ctrl.state[this.key] = state;\n  }\n  get shared() {\n    return this.ctrl.state.shared;\n  }\n  get eventStore() {\n    return this.ctrl.gestureEventStores[this.key];\n  }\n  get timeoutStore() {\n    return this.ctrl.gestureTimeoutStores[this.key];\n  }\n  get config() {\n    return this.ctrl.config[this.key];\n  }\n  get sharedConfig() {\n    return this.ctrl.config.shared;\n  }\n  get handler() {\n    return this.ctrl.handlers[this.key];\n  }\n  reset() {\n    const {\n      state,\n      shared,\n      ingKey,\n      args\n    } = this;\n    shared[ingKey] = state._active = state.active = state._blocked = state._force = false;\n    state._step = [false, false];\n    state.intentional = false;\n    state._movement = [0, 0];\n    state._distance = [0, 0];\n    state._direction = [0, 0];\n    state._delta = [0, 0];\n    state._bounds = [[-Infinity, Infinity], [-Infinity, Infinity]];\n    state.args = args;\n    state.axis = undefined;\n    state.memo = undefined;\n    state.elapsedTime = state.timeDelta = 0;\n    state.direction = [0, 0];\n    state.distance = [0, 0];\n    state.overflow = [0, 0];\n    state._movementBound = [false, false];\n    state.velocity = [0, 0];\n    state.movement = [0, 0];\n    state.delta = [0, 0];\n    state.timeStamp = 0;\n  }\n  start(event) {\n    const state = this.state;\n    const config = this.config;\n    if (!state._active) {\n      this.reset();\n      this.computeInitial();\n      state._active = true;\n      state.target = event.target;\n      state.currentTarget = event.currentTarget;\n      state.lastOffset = config.from ? call(config.from, state) : state.offset;\n      state.offset = state.lastOffset;\n      state.startTime = state.timeStamp = event.timeStamp;\n    }\n  }\n  computeValues(values) {\n    const state = this.state;\n    state._values = values;\n    state.values = this.config.transform(values);\n  }\n  computeInitial() {\n    const state = this.state;\n    state._initial = state._values;\n    state.initial = state.values;\n  }\n  compute(event) {\n    const {\n      state,\n      config,\n      shared\n    } = this;\n    state.args = this.args;\n    let dt = 0;\n    if (event) {\n      state.event = event;\n      if (config.preventDefault && event.cancelable) state.event.preventDefault();\n      state.type = event.type;\n      shared.touches = this.ctrl.pointerIds.size || this.ctrl.touchIds.size;\n      shared.locked = !!document.pointerLockElement;\n      Object.assign(shared, getEventDetails(event));\n      shared.down = shared.pressed = shared.buttons % 2 === 1 || shared.touches > 0;\n      dt = event.timeStamp - state.timeStamp;\n      state.timeStamp = event.timeStamp;\n      state.elapsedTime = state.timeStamp - state.startTime;\n    }\n    if (state._active) {\n      const _absoluteDelta = state._delta.map(Math.abs);\n      V.addTo(state._distance, _absoluteDelta);\n    }\n    if (this.axisIntent) this.axisIntent(event);\n    const [_m0, _m1] = state._movement;\n    const [t0, t1] = config.threshold;\n    const {\n      _step,\n      values\n    } = state;\n    if (config.hasCustomTransform) {\n      if (_step[0] === false) _step[0] = Math.abs(_m0) >= t0 && values[0];\n      if (_step[1] === false) _step[1] = Math.abs(_m1) >= t1 && values[1];\n    } else {\n      if (_step[0] === false) _step[0] = Math.abs(_m0) >= t0 && Math.sign(_m0) * t0;\n      if (_step[1] === false) _step[1] = Math.abs(_m1) >= t1 && Math.sign(_m1) * t1;\n    }\n    state.intentional = _step[0] !== false || _step[1] !== false;\n    if (!state.intentional) return;\n    const movement = [0, 0];\n    if (config.hasCustomTransform) {\n      const [v0, v1] = values;\n      movement[0] = _step[0] !== false ? v0 - _step[0] : 0;\n      movement[1] = _step[1] !== false ? v1 - _step[1] : 0;\n    } else {\n      movement[0] = _step[0] !== false ? _m0 - _step[0] : 0;\n      movement[1] = _step[1] !== false ? _m1 - _step[1] : 0;\n    }\n    if (this.restrictToAxis && !state._blocked) this.restrictToAxis(movement);\n    const previousOffset = state.offset;\n    const gestureIsActive = state._active && !state._blocked || state.active;\n    if (gestureIsActive) {\n      state.first = state._active && !state.active;\n      state.last = !state._active && state.active;\n      state.active = shared[this.ingKey] = state._active;\n      if (event) {\n        if (state.first) {\n          if ('bounds' in config) state._bounds = call(config.bounds, state);\n          if (this.setup) this.setup();\n        }\n        state.movement = movement;\n        this.computeOffset();\n      }\n    }\n    const [ox, oy] = state.offset;\n    const [[x0, x1], [y0, y1]] = state._bounds;\n    state.overflow = [ox < x0 ? -1 : ox > x1 ? 1 : 0, oy < y0 ? -1 : oy > y1 ? 1 : 0];\n    state._movementBound[0] = state.overflow[0] ? state._movementBound[0] === false ? state._movement[0] : state._movementBound[0] : false;\n    state._movementBound[1] = state.overflow[1] ? state._movementBound[1] === false ? state._movement[1] : state._movementBound[1] : false;\n    const rubberband = state._active ? config.rubberband || [0, 0] : [0, 0];\n    state.offset = computeRubberband(state._bounds, state.offset, rubberband);\n    state.delta = V.sub(state.offset, previousOffset);\n    this.computeMovement();\n    if (gestureIsActive && (!state.last || dt > BEFORE_LAST_KINEMATICS_DELAY)) {\n      state.delta = V.sub(state.offset, previousOffset);\n      const absoluteDelta = state.delta.map(Math.abs);\n      V.addTo(state.distance, absoluteDelta);\n      state.direction = state.delta.map(Math.sign);\n      state._direction = state._delta.map(Math.sign);\n      if (!state.first && dt > 0) {\n        state.velocity = [absoluteDelta[0] / dt, absoluteDelta[1] / dt];\n        state.timeDelta = dt;\n      }\n    }\n  }\n  emit() {\n    const state = this.state;\n    const shared = this.shared;\n    const config = this.config;\n    if (!state._active) this.clean();\n    if ((state._blocked || !state.intentional) && !state._force && !config.triggerAllEvents) return;\n    const memo = this.handler(_objectSpread2(_objectSpread2(_objectSpread2({}, shared), state), {}, {\n      [this.aliasKey]: state.values\n    }));\n    if (memo !== undefined) state.memo = memo;\n  }\n  clean() {\n    this.eventStore.clean();\n    this.timeoutStore.clean();\n  }\n}\nfunction selectAxis([dx, dy], threshold) {\n  const absDx = Math.abs(dx);\n  const absDy = Math.abs(dy);\n  if (absDx > absDy && absDx > threshold) {\n    return 'x';\n  }\n  if (absDy > absDx && absDy > threshold) {\n    return 'y';\n  }\n  return undefined;\n}\nclass CoordinatesEngine extends Engine {\n  constructor(...args) {\n    super(...args);\n    _defineProperty(this, \"aliasKey\", 'xy');\n  }\n  reset() {\n    super.reset();\n    this.state.axis = undefined;\n  }\n  init() {\n    this.state.offset = [0, 0];\n    this.state.lastOffset = [0, 0];\n  }\n  computeOffset() {\n    this.state.offset = V.add(this.state.lastOffset, this.state.movement);\n  }\n  computeMovement() {\n    this.state.movement = V.sub(this.state.offset, this.state.lastOffset);\n  }\n  axisIntent(event) {\n    const state = this.state;\n    const config = this.config;\n    if (!state.axis && event) {\n      const threshold = typeof config.axisThreshold === 'object' ? config.axisThreshold[getPointerType(event)] : config.axisThreshold;\n      state.axis = selectAxis(state._movement, threshold);\n    }\n    state._blocked = (config.lockDirection || !!config.axis) && !state.axis || !!config.axis && config.axis !== state.axis;\n  }\n  restrictToAxis(v) {\n    if (this.config.axis || this.config.lockDirection) {\n      switch (this.state.axis) {\n        case 'x':\n          v[1] = 0;\n          break;\n        case 'y':\n          v[0] = 0;\n          break;\n      }\n    }\n  }\n}\nconst identity = v => v;\nconst DEFAULT_RUBBERBAND = 0.15;\nconst commonConfigResolver = {\n  enabled(value = true) {\n    return value;\n  },\n  eventOptions(value, _k, config) {\n    return _objectSpread2(_objectSpread2({}, config.shared.eventOptions), value);\n  },\n  preventDefault(value = false) {\n    return value;\n  },\n  triggerAllEvents(value = false) {\n    return value;\n  },\n  rubberband(value = 0) {\n    switch (value) {\n      case true:\n        return [DEFAULT_RUBBERBAND, DEFAULT_RUBBERBAND];\n      case false:\n        return [0, 0];\n      default:\n        return V.toVector(value);\n    }\n  },\n  from(value) {\n    if (typeof value === 'function') return value;\n    if (value != null) return V.toVector(value);\n  },\n  transform(value, _k, config) {\n    const transform = value || config.shared.transform;\n    this.hasCustomTransform = !!transform;\n    if (process.env.NODE_ENV === 'development') {\n      const originalTransform = transform || identity;\n      return v => {\n        const r = originalTransform(v);\n        if (!isFinite(r[0]) || !isFinite(r[1])) {\n          console.warn(`[@use-gesture]: config.transform() must produce a valid result, but it was: [${r[0]},${[1]}]`);\n        }\n        return r;\n      };\n    }\n    return transform || identity;\n  },\n  threshold(value) {\n    return V.toVector(value, 0);\n  }\n};\nif (process.env.NODE_ENV === 'development') {\n  Object.assign(commonConfigResolver, {\n    domTarget(value) {\n      if (value !== undefined) {\n        throw Error(`[@use-gesture]: \\`domTarget\\` option has been renamed to \\`target\\`.`);\n      }\n      return NaN;\n    },\n    lockDirection(value) {\n      if (value !== undefined) {\n        throw Error(`[@use-gesture]: \\`lockDirection\\` option has been merged with \\`axis\\`. Use it as in \\`{ axis: 'lock' }\\``);\n      }\n      return NaN;\n    },\n    initial(value) {\n      if (value !== undefined) {\n        throw Error(`[@use-gesture]: \\`initial\\` option has been renamed to \\`from\\`.`);\n      }\n      return NaN;\n    }\n  });\n}\nconst DEFAULT_AXIS_THRESHOLD = 0;\nconst coordinatesConfigResolver = _objectSpread2(_objectSpread2({}, commonConfigResolver), {}, {\n  axis(_v, _k, {\n    axis\n  }) {\n    this.lockDirection = axis === 'lock';\n    if (!this.lockDirection) return axis;\n  },\n  axisThreshold(value = DEFAULT_AXIS_THRESHOLD) {\n    return value;\n  },\n  bounds(value = {}) {\n    if (typeof value === 'function') {\n      return state => coordinatesConfigResolver.bounds(value(state));\n    }\n    if ('current' in value) {\n      return () => value.current;\n    }\n    if (typeof HTMLElement === 'function' && value instanceof HTMLElement) {\n      return value;\n    }\n    const {\n      left = -Infinity,\n      right = Infinity,\n      top = -Infinity,\n      bottom = Infinity\n    } = value;\n    return [[left, right], [top, bottom]];\n  }\n});\nconst KEYS_DELTA_MAP = {\n  ArrowRight: (displacement, factor = 1) => [displacement * factor, 0],\n  ArrowLeft: (displacement, factor = 1) => [-1 * displacement * factor, 0],\n  ArrowUp: (displacement, factor = 1) => [0, -1 * displacement * factor],\n  ArrowDown: (displacement, factor = 1) => [0, displacement * factor]\n};\nclass DragEngine extends CoordinatesEngine {\n  constructor(...args) {\n    super(...args);\n    _defineProperty(this, \"ingKey\", 'dragging');\n  }\n  reset() {\n    super.reset();\n    const state = this.state;\n    state._pointerId = undefined;\n    state._pointerActive = false;\n    state._keyboardActive = false;\n    state._preventScroll = false;\n    state._delayed = false;\n    state.swipe = [0, 0];\n    state.tap = false;\n    state.canceled = false;\n    state.cancel = this.cancel.bind(this);\n  }\n  setup() {\n    const state = this.state;\n    if (state._bounds instanceof HTMLElement) {\n      const boundRect = state._bounds.getBoundingClientRect();\n      const targetRect = state.currentTarget.getBoundingClientRect();\n      const _bounds = {\n        left: boundRect.left - targetRect.left + state.offset[0],\n        right: boundRect.right - targetRect.right + state.offset[0],\n        top: boundRect.top - targetRect.top + state.offset[1],\n        bottom: boundRect.bottom - targetRect.bottom + state.offset[1]\n      };\n      state._bounds = coordinatesConfigResolver.bounds(_bounds);\n    }\n  }\n  cancel() {\n    const state = this.state;\n    if (state.canceled) return;\n    state.canceled = true;\n    state._active = false;\n    setTimeout(() => {\n      this.compute();\n      this.emit();\n    }, 0);\n  }\n  setActive() {\n    this.state._active = this.state._pointerActive || this.state._keyboardActive;\n  }\n  clean() {\n    this.pointerClean();\n    this.state._pointerActive = false;\n    this.state._keyboardActive = false;\n    super.clean();\n  }\n  pointerDown(event) {\n    const config = this.config;\n    const state = this.state;\n    if (event.buttons != null && (Array.isArray(config.pointerButtons) ? !config.pointerButtons.includes(event.buttons) : config.pointerButtons !== -1 && config.pointerButtons !== event.buttons)) return;\n    const ctrlIds = this.ctrl.setEventIds(event);\n    if (config.pointerCapture) {\n      event.target.setPointerCapture(event.pointerId);\n    }\n    if (ctrlIds && ctrlIds.size > 1 && state._pointerActive) return;\n    this.start(event);\n    this.setupPointer(event);\n    state._pointerId = pointerId(event);\n    state._pointerActive = true;\n    this.computeValues(pointerValues(event));\n    this.computeInitial();\n    if (config.preventScrollAxis && getPointerType(event) !== 'mouse') {\n      state._active = false;\n      this.setupScrollPrevention(event);\n    } else if (config.delay > 0) {\n      this.setupDelayTrigger(event);\n      if (config.triggerAllEvents) {\n        this.compute(event);\n        this.emit();\n      }\n    } else {\n      this.startPointerDrag(event);\n    }\n  }\n  startPointerDrag(event) {\n    const state = this.state;\n    state._active = true;\n    state._preventScroll = true;\n    state._delayed = false;\n    this.compute(event);\n    this.emit();\n  }\n  pointerMove(event) {\n    const state = this.state;\n    const config = this.config;\n    if (!state._pointerActive) return;\n    const id = pointerId(event);\n    if (state._pointerId !== undefined && id !== state._pointerId) return;\n    const _values = pointerValues(event);\n    if (document.pointerLockElement === event.target) {\n      state._delta = [event.movementX, event.movementY];\n    } else {\n      state._delta = V.sub(_values, state._values);\n      this.computeValues(_values);\n    }\n    V.addTo(state._movement, state._delta);\n    this.compute(event);\n    if (state._delayed && state.intentional) {\n      this.timeoutStore.remove('dragDelay');\n      state.active = false;\n      this.startPointerDrag(event);\n      return;\n    }\n    if (config.preventScrollAxis && !state._preventScroll) {\n      if (state.axis) {\n        if (state.axis === config.preventScrollAxis || config.preventScrollAxis === 'xy') {\n          state._active = false;\n          this.clean();\n          return;\n        } else {\n          this.timeoutStore.remove('startPointerDrag');\n          this.startPointerDrag(event);\n          return;\n        }\n      } else {\n        return;\n      }\n    }\n    this.emit();\n  }\n  pointerUp(event) {\n    this.ctrl.setEventIds(event);\n    try {\n      if (this.config.pointerCapture && event.target.hasPointerCapture(event.pointerId)) {\n        ;\n        event.target.releasePointerCapture(event.pointerId);\n      }\n    } catch (_unused) {\n      if (process.env.NODE_ENV === 'development') {\n        console.warn(`[@use-gesture]: If you see this message, it's likely that you're using an outdated version of \\`@react-three/fiber\\`. \\n\\nPlease upgrade to the latest version.`);\n      }\n    }\n    const state = this.state;\n    const config = this.config;\n    if (!state._active || !state._pointerActive) return;\n    const id = pointerId(event);\n    if (state._pointerId !== undefined && id !== state._pointerId) return;\n    this.state._pointerActive = false;\n    this.setActive();\n    this.compute(event);\n    const [dx, dy] = state._distance;\n    state.tap = dx <= config.tapsThreshold && dy <= config.tapsThreshold;\n    if (state.tap && config.filterTaps) {\n      state._force = true;\n    } else {\n      const [_dx, _dy] = state._delta;\n      const [_mx, _my] = state._movement;\n      const [svx, svy] = config.swipe.velocity;\n      const [sx, sy] = config.swipe.distance;\n      const sdt = config.swipe.duration;\n      if (state.elapsedTime < sdt) {\n        const _vx = Math.abs(_dx / state.timeDelta);\n        const _vy = Math.abs(_dy / state.timeDelta);\n        if (_vx > svx && Math.abs(_mx) > sx) state.swipe[0] = Math.sign(_dx);\n        if (_vy > svy && Math.abs(_my) > sy) state.swipe[1] = Math.sign(_dy);\n      }\n    }\n    this.emit();\n  }\n  pointerClick(event) {\n    if (!this.state.tap && event.detail > 0) {\n      event.preventDefault();\n      event.stopPropagation();\n    }\n  }\n  setupPointer(event) {\n    const config = this.config;\n    const device = config.device;\n    if (process.env.NODE_ENV === 'development') {\n      try {\n        if (device === 'pointer' && config.preventScrollDelay === undefined) {\n          const currentTarget = 'uv' in event ? event.sourceEvent.currentTarget : event.currentTarget;\n          const style = window.getComputedStyle(currentTarget);\n          if (style.touchAction === 'auto') {\n            console.warn(`[@use-gesture]: The drag target has its \\`touch-action\\` style property set to \\`auto\\`. It is recommended to add \\`touch-action: 'none'\\` so that the drag gesture behaves correctly on touch-enabled devices. For more information read this: https://use-gesture.netlify.app/docs/extras/#touch-action.\\n\\nThis message will only show in development mode. It won't appear in production. If this is intended, you can ignore it.`, currentTarget);\n          }\n        }\n      } catch (_unused2) {}\n    }\n    if (config.pointerLock) {\n      event.currentTarget.requestPointerLock();\n    }\n    if (!config.pointerCapture) {\n      this.eventStore.add(this.sharedConfig.window, device, 'change', this.pointerMove.bind(this));\n      this.eventStore.add(this.sharedConfig.window, device, 'end', this.pointerUp.bind(this));\n      this.eventStore.add(this.sharedConfig.window, device, 'cancel', this.pointerUp.bind(this));\n    }\n  }\n  pointerClean() {\n    if (this.config.pointerLock && document.pointerLockElement === this.state.currentTarget) {\n      document.exitPointerLock();\n    }\n  }\n  preventScroll(event) {\n    if (this.state._preventScroll && event.cancelable) {\n      event.preventDefault();\n    }\n  }\n  setupScrollPrevention(event) {\n    this.state._preventScroll = false;\n    persistEvent(event);\n    const remove = this.eventStore.add(this.sharedConfig.window, 'touch', 'change', this.preventScroll.bind(this), {\n      passive: false\n    });\n    this.eventStore.add(this.sharedConfig.window, 'touch', 'end', remove);\n    this.eventStore.add(this.sharedConfig.window, 'touch', 'cancel', remove);\n    this.timeoutStore.add('startPointerDrag', this.startPointerDrag.bind(this), this.config.preventScrollDelay, event);\n  }\n  setupDelayTrigger(event) {\n    this.state._delayed = true;\n    this.timeoutStore.add('dragDelay', () => {\n      this.state._step = [0, 0];\n      this.startPointerDrag(event);\n    }, this.config.delay);\n  }\n  keyDown(event) {\n    const deltaFn = KEYS_DELTA_MAP[event.key];\n    if (deltaFn) {\n      const state = this.state;\n      const factor = event.shiftKey ? 10 : event.altKey ? 0.1 : 1;\n      this.start(event);\n      state._delta = deltaFn(this.config.keyboardDisplacement, factor);\n      state._keyboardActive = true;\n      V.addTo(state._movement, state._delta);\n      this.compute(event);\n      this.emit();\n    }\n  }\n  keyUp(event) {\n    if (!(event.key in KEYS_DELTA_MAP)) return;\n    this.state._keyboardActive = false;\n    this.setActive();\n    this.compute(event);\n    this.emit();\n  }\n  bind(bindFunction) {\n    const device = this.config.device;\n    bindFunction(device, 'start', this.pointerDown.bind(this));\n    if (this.config.pointerCapture) {\n      bindFunction(device, 'change', this.pointerMove.bind(this));\n      bindFunction(device, 'end', this.pointerUp.bind(this));\n      bindFunction(device, 'cancel', this.pointerUp.bind(this));\n      bindFunction('lostPointerCapture', '', this.pointerUp.bind(this));\n    }\n    if (this.config.keys) {\n      bindFunction('key', 'down', this.keyDown.bind(this));\n      bindFunction('key', 'up', this.keyUp.bind(this));\n    }\n    if (this.config.filterTaps) {\n      bindFunction('click', '', this.pointerClick.bind(this), {\n        capture: true,\n        passive: false\n      });\n    }\n  }\n}\nfunction persistEvent(event) {\n  'persist' in event && typeof event.persist === 'function' && event.persist();\n}\nconst isBrowser = typeof window !== 'undefined' && window.document && window.document.createElement;\nfunction supportsTouchEvents() {\n  return isBrowser && 'ontouchstart' in window;\n}\nfunction isTouchScreen() {\n  return supportsTouchEvents() || isBrowser && window.navigator.maxTouchPoints > 1;\n}\nfunction supportsPointerEvents() {\n  return isBrowser && 'onpointerdown' in window;\n}\nfunction supportsPointerLock() {\n  return isBrowser && 'exitPointerLock' in window.document;\n}\nfunction supportsGestureEvents() {\n  try {\n    return 'constructor' in GestureEvent;\n  } catch (e) {\n    return false;\n  }\n}\nconst SUPPORT = {\n  isBrowser,\n  gesture: supportsGestureEvents(),\n  touch: supportsTouchEvents(),\n  touchscreen: isTouchScreen(),\n  pointer: supportsPointerEvents(),\n  pointerLock: supportsPointerLock()\n};\nconst DEFAULT_PREVENT_SCROLL_DELAY = 250;\nconst DEFAULT_DRAG_DELAY = 180;\nconst DEFAULT_SWIPE_VELOCITY = 0.5;\nconst DEFAULT_SWIPE_DISTANCE = 50;\nconst DEFAULT_SWIPE_DURATION = 250;\nconst DEFAULT_KEYBOARD_DISPLACEMENT = 10;\nconst DEFAULT_DRAG_AXIS_THRESHOLD = {\n  mouse: 0,\n  touch: 0,\n  pen: 8\n};\nconst dragConfigResolver = _objectSpread2(_objectSpread2({}, coordinatesConfigResolver), {}, {\n  device(_v, _k, {\n    pointer: {\n      touch = false,\n      lock = false,\n      mouse = false\n    } = {}\n  }) {\n    this.pointerLock = lock && SUPPORT.pointerLock;\n    if (SUPPORT.touch && touch) return 'touch';\n    if (this.pointerLock) return 'mouse';\n    if (SUPPORT.pointer && !mouse) return 'pointer';\n    if (SUPPORT.touch) return 'touch';\n    return 'mouse';\n  },\n  preventScrollAxis(value, _k, {\n    preventScroll\n  }) {\n    this.preventScrollDelay = typeof preventScroll === 'number' ? preventScroll : preventScroll || preventScroll === undefined && value ? DEFAULT_PREVENT_SCROLL_DELAY : undefined;\n    if (!SUPPORT.touchscreen || preventScroll === false) return undefined;\n    return value ? value : preventScroll !== undefined ? 'y' : undefined;\n  },\n  pointerCapture(_v, _k, {\n    pointer: {\n      capture = true,\n      buttons = 1,\n      keys = true\n    } = {}\n  }) {\n    this.pointerButtons = buttons;\n    this.keys = keys;\n    return !this.pointerLock && this.device === 'pointer' && capture;\n  },\n  threshold(value, _k, {\n    filterTaps = false,\n    tapsThreshold = 3,\n    axis = undefined\n  }) {\n    const threshold = V.toVector(value, filterTaps ? tapsThreshold : axis ? 1 : 0);\n    this.filterTaps = filterTaps;\n    this.tapsThreshold = tapsThreshold;\n    return threshold;\n  },\n  swipe({\n    velocity = DEFAULT_SWIPE_VELOCITY,\n    distance = DEFAULT_SWIPE_DISTANCE,\n    duration = DEFAULT_SWIPE_DURATION\n  } = {}) {\n    return {\n      velocity: this.transform(V.toVector(velocity)),\n      distance: this.transform(V.toVector(distance)),\n      duration\n    };\n  },\n  delay(value = 0) {\n    switch (value) {\n      case true:\n        return DEFAULT_DRAG_DELAY;\n      case false:\n        return 0;\n      default:\n        return value;\n    }\n  },\n  axisThreshold(value) {\n    if (!value) return DEFAULT_DRAG_AXIS_THRESHOLD;\n    return _objectSpread2(_objectSpread2({}, DEFAULT_DRAG_AXIS_THRESHOLD), value);\n  },\n  keyboardDisplacement(value = DEFAULT_KEYBOARD_DISPLACEMENT) {\n    return value;\n  }\n});\nif (process.env.NODE_ENV === 'development') {\n  Object.assign(dragConfigResolver, {\n    useTouch(value) {\n      if (value !== undefined) {\n        throw Error(`[@use-gesture]: \\`useTouch\\` option has been renamed to \\`pointer.touch\\`. Use it as in \\`{ pointer: { touch: true } }\\`.`);\n      }\n      return NaN;\n    },\n    experimental_preventWindowScrollY(value) {\n      if (value !== undefined) {\n        throw Error(`[@use-gesture]: \\`experimental_preventWindowScrollY\\` option has been renamed to \\`preventScroll\\`.`);\n      }\n      return NaN;\n    },\n    swipeVelocity(value) {\n      if (value !== undefined) {\n        throw Error(`[@use-gesture]: \\`swipeVelocity\\` option has been renamed to \\`swipe.velocity\\`. Use it as in \\`{ swipe: { velocity: 0.5 } }\\`.`);\n      }\n      return NaN;\n    },\n    swipeDistance(value) {\n      if (value !== undefined) {\n        throw Error(`[@use-gesture]: \\`swipeDistance\\` option has been renamed to \\`swipe.distance\\`. Use it as in \\`{ swipe: { distance: 50 } }\\`.`);\n      }\n      return NaN;\n    },\n    swipeDuration(value) {\n      if (value !== undefined) {\n        throw Error(`[@use-gesture]: \\`swipeDuration\\` option has been renamed to \\`swipe.duration\\`. Use it as in \\`{ swipe: { duration: 250 } }\\`.`);\n      }\n      return NaN;\n    }\n  });\n}\nfunction clampStateInternalMovementToBounds(state) {\n  const [ox, oy] = state.overflow;\n  const [dx, dy] = state._delta;\n  const [dirx, diry] = state._direction;\n  if (ox < 0 && dx > 0 && dirx < 0 || ox > 0 && dx < 0 && dirx > 0) {\n    state._movement[0] = state._movementBound[0];\n  }\n  if (oy < 0 && dy > 0 && diry < 0 || oy > 0 && dy < 0 && diry > 0) {\n    state._movement[1] = state._movementBound[1];\n  }\n}\nconst SCALE_ANGLE_RATIO_INTENT_DEG = 30;\nconst PINCH_WHEEL_RATIO = 100;\nclass PinchEngine extends Engine {\n  constructor(...args) {\n    super(...args);\n    _defineProperty(this, \"ingKey\", 'pinching');\n    _defineProperty(this, \"aliasKey\", 'da');\n  }\n  init() {\n    this.state.offset = [1, 0];\n    this.state.lastOffset = [1, 0];\n    this.state._pointerEvents = new Map();\n  }\n  reset() {\n    super.reset();\n    const state = this.state;\n    state._touchIds = [];\n    state.canceled = false;\n    state.cancel = this.cancel.bind(this);\n    state.turns = 0;\n  }\n  computeOffset() {\n    const {\n      type,\n      movement,\n      lastOffset\n    } = this.state;\n    if (type === 'wheel') {\n      this.state.offset = V.add(movement, lastOffset);\n    } else {\n      this.state.offset = [(1 + movement[0]) * lastOffset[0], movement[1] + lastOffset[1]];\n    }\n  }\n  computeMovement() {\n    const {\n      offset,\n      lastOffset\n    } = this.state;\n    this.state.movement = [offset[0] / lastOffset[0], offset[1] - lastOffset[1]];\n  }\n  axisIntent() {\n    const state = this.state;\n    const [_m0, _m1] = state._movement;\n    if (!state.axis) {\n      const axisMovementDifference = Math.abs(_m0) * SCALE_ANGLE_RATIO_INTENT_DEG - Math.abs(_m1);\n      if (axisMovementDifference < 0) state.axis = 'angle';else if (axisMovementDifference > 0) state.axis = 'scale';\n    }\n  }\n  restrictToAxis(v) {\n    if (this.config.lockDirection) {\n      if (this.state.axis === 'scale') v[1] = 0;else if (this.state.axis === 'angle') v[0] = 0;\n    }\n  }\n  cancel() {\n    const state = this.state;\n    if (state.canceled) return;\n    setTimeout(() => {\n      state.canceled = true;\n      state._active = false;\n      this.compute();\n      this.emit();\n    }, 0);\n  }\n  touchStart(event) {\n    this.ctrl.setEventIds(event);\n    const state = this.state;\n    const ctrlTouchIds = this.ctrl.touchIds;\n    if (state._active) {\n      if (state._touchIds.every(id => ctrlTouchIds.has(id))) return;\n    }\n    if (ctrlTouchIds.size < 2) return;\n    this.start(event);\n    state._touchIds = Array.from(ctrlTouchIds).slice(0, 2);\n    const payload = touchDistanceAngle(event, state._touchIds);\n    if (!payload) return;\n    this.pinchStart(event, payload);\n  }\n  pointerStart(event) {\n    if (event.buttons != null && event.buttons % 2 !== 1) return;\n    this.ctrl.setEventIds(event);\n    event.target.setPointerCapture(event.pointerId);\n    const state = this.state;\n    const _pointerEvents = state._pointerEvents;\n    const ctrlPointerIds = this.ctrl.pointerIds;\n    if (state._active) {\n      if (Array.from(_pointerEvents.keys()).every(id => ctrlPointerIds.has(id))) return;\n    }\n    if (_pointerEvents.size < 2) {\n      _pointerEvents.set(event.pointerId, event);\n    }\n    if (state._pointerEvents.size < 2) return;\n    this.start(event);\n    const payload = distanceAngle(...Array.from(_pointerEvents.values()));\n    if (!payload) return;\n    this.pinchStart(event, payload);\n  }\n  pinchStart(event, payload) {\n    const state = this.state;\n    state.origin = payload.origin;\n    this.computeValues([payload.distance, payload.angle]);\n    this.computeInitial();\n    this.compute(event);\n    this.emit();\n  }\n  touchMove(event) {\n    if (!this.state._active) return;\n    const payload = touchDistanceAngle(event, this.state._touchIds);\n    if (!payload) return;\n    this.pinchMove(event, payload);\n  }\n  pointerMove(event) {\n    const _pointerEvents = this.state._pointerEvents;\n    if (_pointerEvents.has(event.pointerId)) {\n      _pointerEvents.set(event.pointerId, event);\n    }\n    if (!this.state._active) return;\n    const payload = distanceAngle(...Array.from(_pointerEvents.values()));\n    if (!payload) return;\n    this.pinchMove(event, payload);\n  }\n  pinchMove(event, payload) {\n    const state = this.state;\n    const prev_a = state._values[1];\n    const delta_a = payload.angle - prev_a;\n    let delta_turns = 0;\n    if (Math.abs(delta_a) > 270) delta_turns += Math.sign(delta_a);\n    this.computeValues([payload.distance, payload.angle - 360 * delta_turns]);\n    state.origin = payload.origin;\n    state.turns = delta_turns;\n    state._movement = [state._values[0] / state._initial[0] - 1, state._values[1] - state._initial[1]];\n    this.compute(event);\n    this.emit();\n  }\n  touchEnd(event) {\n    this.ctrl.setEventIds(event);\n    if (!this.state._active) return;\n    if (this.state._touchIds.some(id => !this.ctrl.touchIds.has(id))) {\n      this.state._active = false;\n      this.compute(event);\n      this.emit();\n    }\n  }\n  pointerEnd(event) {\n    const state = this.state;\n    this.ctrl.setEventIds(event);\n    try {\n      event.target.releasePointerCapture(event.pointerId);\n    } catch (_unused) {}\n    if (state._pointerEvents.has(event.pointerId)) {\n      state._pointerEvents.delete(event.pointerId);\n    }\n    if (!state._active) return;\n    if (state._pointerEvents.size < 2) {\n      state._active = false;\n      this.compute(event);\n      this.emit();\n    }\n  }\n  gestureStart(event) {\n    if (event.cancelable) event.preventDefault();\n    const state = this.state;\n    if (state._active) return;\n    this.start(event);\n    this.computeValues([event.scale, event.rotation]);\n    state.origin = [event.clientX, event.clientY];\n    this.compute(event);\n    this.emit();\n  }\n  gestureMove(event) {\n    if (event.cancelable) event.preventDefault();\n    if (!this.state._active) return;\n    const state = this.state;\n    this.computeValues([event.scale, event.rotation]);\n    state.origin = [event.clientX, event.clientY];\n    const _previousMovement = state._movement;\n    state._movement = [event.scale - 1, event.rotation];\n    state._delta = V.sub(state._movement, _previousMovement);\n    this.compute(event);\n    this.emit();\n  }\n  gestureEnd(event) {\n    if (!this.state._active) return;\n    this.state._active = false;\n    this.compute(event);\n    this.emit();\n  }\n  wheel(event) {\n    const modifierKey = this.config.modifierKey;\n    if (modifierKey && (Array.isArray(modifierKey) ? !modifierKey.find(k => event[k]) : !event[modifierKey])) return;\n    if (!this.state._active) this.wheelStart(event);else this.wheelChange(event);\n    this.timeoutStore.add('wheelEnd', this.wheelEnd.bind(this));\n  }\n  wheelStart(event) {\n    this.start(event);\n    this.wheelChange(event);\n  }\n  wheelChange(event) {\n    const isR3f = 'uv' in event;\n    if (!isR3f) {\n      if (event.cancelable) {\n        event.preventDefault();\n      }\n      if (process.env.NODE_ENV === 'development' && !event.defaultPrevented) {\n        console.warn(`[@use-gesture]: To properly support zoom on trackpads, try using the \\`target\\` option.\\n\\nThis message will only appear in development mode.`);\n      }\n    }\n    const state = this.state;\n    state._delta = [-wheelValues(event)[1] / PINCH_WHEEL_RATIO * state.offset[0], 0];\n    V.addTo(state._movement, state._delta);\n    clampStateInternalMovementToBounds(state);\n    this.state.origin = [event.clientX, event.clientY];\n    this.compute(event);\n    this.emit();\n  }\n  wheelEnd() {\n    if (!this.state._active) return;\n    this.state._active = false;\n    this.compute();\n    this.emit();\n  }\n  bind(bindFunction) {\n    const device = this.config.device;\n    if (!!device) {\n      bindFunction(device, 'start', this[device + 'Start'].bind(this));\n      bindFunction(device, 'change', this[device + 'Move'].bind(this));\n      bindFunction(device, 'end', this[device + 'End'].bind(this));\n      bindFunction(device, 'cancel', this[device + 'End'].bind(this));\n      bindFunction('lostPointerCapture', '', this[device + 'End'].bind(this));\n    }\n    if (this.config.pinchOnWheel) {\n      bindFunction('wheel', '', this.wheel.bind(this), {\n        passive: false\n      });\n    }\n  }\n}\nconst pinchConfigResolver = _objectSpread2(_objectSpread2({}, commonConfigResolver), {}, {\n  device(_v, _k, {\n    shared,\n    pointer: {\n      touch = false\n    } = {}\n  }) {\n    const sharedConfig = shared;\n    if (sharedConfig.target && !SUPPORT.touch && SUPPORT.gesture) return 'gesture';\n    if (SUPPORT.touch && touch) return 'touch';\n    if (SUPPORT.touchscreen) {\n      if (SUPPORT.pointer) return 'pointer';\n      if (SUPPORT.touch) return 'touch';\n    }\n  },\n  bounds(_v, _k, {\n    scaleBounds = {},\n    angleBounds = {}\n  }) {\n    const _scaleBounds = state => {\n      const D = assignDefault(call(scaleBounds, state), {\n        min: -Infinity,\n        max: Infinity\n      });\n      return [D.min, D.max];\n    };\n    const _angleBounds = state => {\n      const A = assignDefault(call(angleBounds, state), {\n        min: -Infinity,\n        max: Infinity\n      });\n      return [A.min, A.max];\n    };\n    if (typeof scaleBounds !== 'function' && typeof angleBounds !== 'function') return [_scaleBounds(), _angleBounds()];\n    return state => [_scaleBounds(state), _angleBounds(state)];\n  },\n  threshold(value, _k, config) {\n    this.lockDirection = config.axis === 'lock';\n    const threshold = V.toVector(value, this.lockDirection ? [0.1, 3] : 0);\n    return threshold;\n  },\n  modifierKey(value) {\n    if (value === undefined) return 'ctrlKey';\n    return value;\n  },\n  pinchOnWheel(value = true) {\n    return value;\n  }\n});\nclass MoveEngine extends CoordinatesEngine {\n  constructor(...args) {\n    super(...args);\n    _defineProperty(this, \"ingKey\", 'moving');\n  }\n  move(event) {\n    if (this.config.mouseOnly && event.pointerType !== 'mouse') return;\n    if (!this.state._active) this.moveStart(event);else this.moveChange(event);\n    this.timeoutStore.add('moveEnd', this.moveEnd.bind(this));\n  }\n  moveStart(event) {\n    this.start(event);\n    this.computeValues(pointerValues(event));\n    this.compute(event);\n    this.computeInitial();\n    this.emit();\n  }\n  moveChange(event) {\n    if (!this.state._active) return;\n    const values = pointerValues(event);\n    const state = this.state;\n    state._delta = V.sub(values, state._values);\n    V.addTo(state._movement, state._delta);\n    this.computeValues(values);\n    this.compute(event);\n    this.emit();\n  }\n  moveEnd(event) {\n    if (!this.state._active) return;\n    this.state._active = false;\n    this.compute(event);\n    this.emit();\n  }\n  bind(bindFunction) {\n    bindFunction('pointer', 'change', this.move.bind(this));\n    bindFunction('pointer', 'leave', this.moveEnd.bind(this));\n  }\n}\nconst moveConfigResolver = _objectSpread2(_objectSpread2({}, coordinatesConfigResolver), {}, {\n  mouseOnly: (value = true) => value\n});\nclass ScrollEngine extends CoordinatesEngine {\n  constructor(...args) {\n    super(...args);\n    _defineProperty(this, \"ingKey\", 'scrolling');\n  }\n  scroll(event) {\n    if (!this.state._active) this.start(event);\n    this.scrollChange(event);\n    this.timeoutStore.add('scrollEnd', this.scrollEnd.bind(this));\n  }\n  scrollChange(event) {\n    if (event.cancelable) event.preventDefault();\n    const state = this.state;\n    const values = scrollValues(event);\n    state._delta = V.sub(values, state._values);\n    V.addTo(state._movement, state._delta);\n    this.computeValues(values);\n    this.compute(event);\n    this.emit();\n  }\n  scrollEnd() {\n    if (!this.state._active) return;\n    this.state._active = false;\n    this.compute();\n    this.emit();\n  }\n  bind(bindFunction) {\n    bindFunction('scroll', '', this.scroll.bind(this));\n  }\n}\nconst scrollConfigResolver = coordinatesConfigResolver;\nclass WheelEngine extends CoordinatesEngine {\n  constructor(...args) {\n    super(...args);\n    _defineProperty(this, \"ingKey\", 'wheeling');\n  }\n  wheel(event) {\n    if (!this.state._active) this.start(event);\n    this.wheelChange(event);\n    this.timeoutStore.add('wheelEnd', this.wheelEnd.bind(this));\n  }\n  wheelChange(event) {\n    const state = this.state;\n    state._delta = wheelValues(event);\n    V.addTo(state._movement, state._delta);\n    clampStateInternalMovementToBounds(state);\n    this.compute(event);\n    this.emit();\n  }\n  wheelEnd() {\n    if (!this.state._active) return;\n    this.state._active = false;\n    this.compute();\n    this.emit();\n  }\n  bind(bindFunction) {\n    bindFunction('wheel', '', this.wheel.bind(this));\n  }\n}\nconst wheelConfigResolver = coordinatesConfigResolver;\nclass HoverEngine extends CoordinatesEngine {\n  constructor(...args) {\n    super(...args);\n    _defineProperty(this, \"ingKey\", 'hovering');\n  }\n  enter(event) {\n    if (this.config.mouseOnly && event.pointerType !== 'mouse') return;\n    this.start(event);\n    this.computeValues(pointerValues(event));\n    this.compute(event);\n    this.emit();\n  }\n  leave(event) {\n    if (this.config.mouseOnly && event.pointerType !== 'mouse') return;\n    const state = this.state;\n    if (!state._active) return;\n    state._active = false;\n    const values = pointerValues(event);\n    state._movement = state._delta = V.sub(values, state._values);\n    this.computeValues(values);\n    this.compute(event);\n    state.delta = state.movement;\n    this.emit();\n  }\n  bind(bindFunction) {\n    bindFunction('pointer', 'enter', this.enter.bind(this));\n    bindFunction('pointer', 'leave', this.leave.bind(this));\n  }\n}\nconst hoverConfigResolver = _objectSpread2(_objectSpread2({}, coordinatesConfigResolver), {}, {\n  mouseOnly: (value = true) => value\n});\nconst EngineMap = new Map();\nconst ConfigResolverMap = new Map();\nfunction registerAction(action) {\n  EngineMap.set(action.key, action.engine);\n  ConfigResolverMap.set(action.key, action.resolver);\n}\nconst dragAction = {\n  key: 'drag',\n  engine: DragEngine,\n  resolver: dragConfigResolver\n};\nconst hoverAction = {\n  key: 'hover',\n  engine: HoverEngine,\n  resolver: hoverConfigResolver\n};\nconst moveAction = {\n  key: 'move',\n  engine: MoveEngine,\n  resolver: moveConfigResolver\n};\nconst pinchAction = {\n  key: 'pinch',\n  engine: PinchEngine,\n  resolver: pinchConfigResolver\n};\nconst scrollAction = {\n  key: 'scroll',\n  engine: ScrollEngine,\n  resolver: scrollConfigResolver\n};\nconst wheelAction = {\n  key: 'wheel',\n  engine: WheelEngine,\n  resolver: wheelConfigResolver\n};\nexport { ConfigResolverMap as C, EngineMap as E, SUPPORT as S, _objectSpread2 as _, _defineProperty as a, touchIds as b, chain as c, toHandlerProp as d, dragAction as e, pinchAction as f, hoverAction as h, isTouch as i, moveAction as m, parseProp as p, registerAction as r, scrollAction as s, toDomEventType as t, wheelAction as w };", "map": {"version": 3, "names": ["V", "c", "computeRubberband", "_toPrimitive", "input", "hint", "prim", "Symbol", "toPrimitive", "undefined", "res", "call", "TypeError", "String", "Number", "_to<PERSON><PERSON><PERSON><PERSON><PERSON>", "arg", "key", "_defineProperty", "obj", "value", "Object", "defineProperty", "enumerable", "configurable", "writable", "ownKeys", "e", "r", "t", "keys", "getOwnPropertySymbols", "o", "filter", "getOwnPropertyDescriptor", "push", "apply", "_objectSpread2", "arguments", "length", "for<PERSON>ach", "getOwnPropertyDescriptors", "defineProperties", "EVENT_TYPE_MAP", "pointer", "start", "change", "end", "mouse", "touch", "gesture", "capitalize", "string", "toUpperCase", "slice", "actionsWithoutCaptureSupported", "hasCapture", "capture", "action<PERSON>ey", "includes", "toHandlerProp", "device", "action", "deviceProps", "pointerCaptureEvents", "parseProp", "prop", "eventKey", "substring", "toLowerCase", "passive", "indexOf", "replace", "<PERSON><PERSON><PERSON>", "toDomEventType", "is<PERSON><PERSON>ch", "event", "getPointerType", "pointerType", "getCurrentTargetTouchList", "Array", "from", "touches", "_event$currentTarget", "_event$currentTarget$", "target", "currentTarget", "contains", "getTouchList", "type", "changedTouches", "targetTouches", "getValueEvent", "distanceAngle", "P1", "P2", "dx", "clientX", "dy", "clientY", "cx", "cy", "distance", "Math", "hypot", "angle", "atan2", "PI", "origin", "_unused", "touchIds", "map", "identifier", "touchDistanceAngle", "ids", "pointerId", "valueEvent", "pointerValues", "LINE_HEIGHT", "PAGE_HEIGHT", "wheelValues", "deltaX", "deltaY", "deltaMode", "scrollValues", "_ref", "_ref2", "scrollX", "scrollY", "scrollLeft", "scrollTop", "getEventDetails", "payload", "buttons", "shift<PERSON>ey", "altKey", "metaKey", "ctrl<PERSON>ey", "assign", "v", "args", "noop", "chain", "fns", "result", "fn", "assignDefault", "fallback", "BEFORE_LAST_KINEMATICS_DELAY", "Engine", "constructor", "ctrl", "state", "computeValues", "computeInitial", "init", "reset", "shared", "eventStore", "gestureEventStores", "timeoutStore", "gestureTimeoutStores", "config", "sharedConfig", "handler", "handlers", "<PERSON><PERSON><PERSON>", "_active", "active", "_blocked", "_force", "_step", "intentional", "_movement", "_distance", "_direction", "_delta", "_bounds", "Infinity", "axis", "memo", "elapsedTime", "<PERSON><PERSON><PERSON><PERSON>", "direction", "overflow", "_movementBound", "velocity", "movement", "delta", "timeStamp", "lastOffset", "offset", "startTime", "values", "_values", "transform", "_initial", "initial", "compute", "dt", "preventDefault", "cancelable", "pointerIds", "size", "locked", "document", "pointerLockElement", "down", "pressed", "_absoluteD<PERSON><PERSON>", "abs", "addTo", "axisIntent", "_m0", "_m1", "t0", "t1", "threshold", "hasCustomTransform", "sign", "v0", "v1", "restrictToAxis", "previousOffset", "gestureIsActive", "first", "last", "bounds", "setup", "computeOffset", "ox", "oy", "x0", "x1", "y0", "y1", "rubberband", "sub", "computeMovement", "absoluteDelta", "emit", "clean", "triggerAllEvents", "<PERSON><PERSON><PERSON>", "selectAxis", "absDx", "absDy", "CoordinatesEngine", "add", "axisThreshold", "lockDirection", "identity", "DEFAULT_RUBBERBAND", "commonConfigResolver", "enabled", "eventOptions", "_k", "toVector", "process", "env", "NODE_ENV", "originalTransform", "isFinite", "console", "warn", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Error", "NaN", "DEFAULT_AXIS_THRESHOLD", "coordinatesConfigResolver", "_v", "current", "HTMLElement", "left", "right", "top", "bottom", "KEYS_DELTA_MAP", "ArrowRight", "displacement", "factor", "ArrowLeft", "ArrowUp", "ArrowDown", "DragEngine", "_pointerId", "_pointerActive", "_keyboardActive", "_preventScroll", "_delayed", "swipe", "tap", "canceled", "cancel", "bind", "boundRect", "getBoundingClientRect", "targetRect", "setTimeout", "setActive", "pointer<PERSON><PERSON>", "pointerDown", "isArray", "pointerButtons", "ctrlIds", "setEventIds", "pointerCapture", "setPointerCapture", "setupPointer", "preventScrollAxis", "setupScrollPrevention", "delay", "setup<PERSON>elayTrigger", "startPointerDrag", "pointer<PERSON><PERSON>", "id", "movementX", "movementY", "remove", "pointerUp", "hasPointerCapture", "releasePointerCapture", "tapsThreshold", "filterTaps", "_dx", "_dy", "_mx", "_my", "svx", "svy", "sx", "sy", "sdt", "duration", "_vx", "_vy", "pointer<PERSON>lick", "detail", "stopPropagation", "preventScrollDelay", "sourceEvent", "style", "window", "getComputedStyle", "touchAction", "_unused2", "pointerLock", "requestPointerLock", "exitPointerLock", "preventScroll", "persistEvent", "keyDown", "deltaFn", "keyboardDisplacement", "keyUp", "bindFunction", "persist", "<PERSON><PERSON><PERSON><PERSON>", "createElement", "supportsTouchEvents", "isTouchScreen", "navigator", "maxTouchPoints", "supportsPointerEvents", "supportsPointerLock", "supportsGestureEvents", "GestureEvent", "SUPPORT", "touchscreen", "DEFAULT_PREVENT_SCROLL_DELAY", "DEFAULT_DRAG_DELAY", "DEFAULT_SWIPE_VELOCITY", "DEFAULT_SWIPE_DISTANCE", "DEFAULT_SWIPE_DURATION", "DEFAULT_KEYBOARD_DISPLACEMENT", "DEFAULT_DRAG_AXIS_THRESHOLD", "pen", "dragConfigResolver", "lock", "useTouch", "experimental_preventWindowScrollY", "swipeVelocity", "swipeDistance", "swipeDuration", "clampStateInternalMovementToBounds", "dirx", "diry", "SCALE_ANGLE_RATIO_INTENT_DEG", "PINCH_WHEEL_RATIO", "PinchEngine", "_pointerEvents", "Map", "_touchIds", "turns", "axisMovementDifference", "touchStart", "ctrlTouchIds", "every", "has", "pinchStart", "pointerStart", "ctrlPointerIds", "set", "touchMove", "pinchMove", "prev_a", "delta_a", "delta_turns", "touchEnd", "some", "pointerEnd", "delete", "gestureStart", "scale", "rotation", "gestureMove", "_previousMovement", "gestureEnd", "wheel", "modifierKey", "find", "k", "wheelStart", "wheelChange", "wheelEnd", "isR3f", "defaultPrevented", "pinchOnWheel", "pinchConfigResolver", "scaleBounds", "angleBounds", "_scaleBounds", "D", "min", "max", "_angleBounds", "A", "MoveEngine", "move", "mouseOnly", "moveStart", "moveChange", "moveEnd", "moveConfigResolver", "ScrollEngine", "scroll", "scrollChange", "scrollEnd", "scrollConfigResolver", "WheelEngine", "wheelConfigResolver", "HoverEngine", "enter", "leave", "hoverConfigResolver", "EngineMap", "ConfigResolverMap", "registerAction", "engine", "resolver", "dragAction", "hoverAction", "moveAction", "pinchAction", "scrollAction", "wheelAction", "C", "E", "S", "_", "a", "b", "d", "f", "h", "i", "m", "p", "s", "w"], "sources": ["C:/Users/<USER>/Documents/Apps_Creations/SiahMan_Racing4Speed_WebGame/siah-man-racing/node_modules/@use-gesture/core/dist/actions-fe213e88.esm.js"], "sourcesContent": ["import { V, c as computeRubberband } from './maths-0ab39ae9.esm.js';\n\nfunction _toPrimitive(input, hint) {\n  if (typeof input !== \"object\" || input === null) return input;\n  var prim = input[Symbol.toPrimitive];\n  if (prim !== undefined) {\n    var res = prim.call(input, hint || \"default\");\n    if (typeof res !== \"object\") return res;\n    throw new TypeError(\"@@toPrimitive must return a primitive value.\");\n  }\n  return (hint === \"string\" ? String : Number)(input);\n}\n\nfunction _toPropertyKey(arg) {\n  var key = _toPrimitive(arg, \"string\");\n  return typeof key === \"symbol\" ? key : String(key);\n}\n\nfunction _defineProperty(obj, key, value) {\n  key = _toPropertyKey(key);\n  if (key in obj) {\n    Object.defineProperty(obj, key, {\n      value: value,\n      enumerable: true,\n      configurable: true,\n      writable: true\n    });\n  } else {\n    obj[key] = value;\n  }\n  return obj;\n}\n\nfunction ownKeys(e, r) {\n  var t = Object.keys(e);\n  if (Object.getOwnPropertySymbols) {\n    var o = Object.getOwnPropertySymbols(e);\n    r && (o = o.filter(function (r) {\n      return Object.getOwnPropertyDescriptor(e, r).enumerable;\n    })), t.push.apply(t, o);\n  }\n  return t;\n}\nfunction _objectSpread2(e) {\n  for (var r = 1; r < arguments.length; r++) {\n    var t = null != arguments[r] ? arguments[r] : {};\n    r % 2 ? ownKeys(Object(t), !0).forEach(function (r) {\n      _defineProperty(e, r, t[r]);\n    }) : Object.getOwnPropertyDescriptors ? Object.defineProperties(e, Object.getOwnPropertyDescriptors(t)) : ownKeys(Object(t)).forEach(function (r) {\n      Object.defineProperty(e, r, Object.getOwnPropertyDescriptor(t, r));\n    });\n  }\n  return e;\n}\n\nconst EVENT_TYPE_MAP = {\n  pointer: {\n    start: 'down',\n    change: 'move',\n    end: 'up'\n  },\n  mouse: {\n    start: 'down',\n    change: 'move',\n    end: 'up'\n  },\n  touch: {\n    start: 'start',\n    change: 'move',\n    end: 'end'\n  },\n  gesture: {\n    start: 'start',\n    change: 'change',\n    end: 'end'\n  }\n};\nfunction capitalize(string) {\n  if (!string) return '';\n  return string[0].toUpperCase() + string.slice(1);\n}\nconst actionsWithoutCaptureSupported = ['enter', 'leave'];\nfunction hasCapture(capture = false, actionKey) {\n  return capture && !actionsWithoutCaptureSupported.includes(actionKey);\n}\nfunction toHandlerProp(device, action = '', capture = false) {\n  const deviceProps = EVENT_TYPE_MAP[device];\n  const actionKey = deviceProps ? deviceProps[action] || action : action;\n  return 'on' + capitalize(device) + capitalize(actionKey) + (hasCapture(capture, actionKey) ? 'Capture' : '');\n}\nconst pointerCaptureEvents = ['gotpointercapture', 'lostpointercapture'];\nfunction parseProp(prop) {\n  let eventKey = prop.substring(2).toLowerCase();\n  const passive = !!~eventKey.indexOf('passive');\n  if (passive) eventKey = eventKey.replace('passive', '');\n  const captureKey = pointerCaptureEvents.includes(eventKey) ? 'capturecapture' : 'capture';\n  const capture = !!~eventKey.indexOf(captureKey);\n  if (capture) eventKey = eventKey.replace('capture', '');\n  return {\n    device: eventKey,\n    capture,\n    passive\n  };\n}\nfunction toDomEventType(device, action = '') {\n  const deviceProps = EVENT_TYPE_MAP[device];\n  const actionKey = deviceProps ? deviceProps[action] || action : action;\n  return device + actionKey;\n}\nfunction isTouch(event) {\n  return 'touches' in event;\n}\nfunction getPointerType(event) {\n  if (isTouch(event)) return 'touch';\n  if ('pointerType' in event) return event.pointerType;\n  return 'mouse';\n}\nfunction getCurrentTargetTouchList(event) {\n  return Array.from(event.touches).filter(e => {\n    var _event$currentTarget, _event$currentTarget$;\n    return e.target === event.currentTarget || ((_event$currentTarget = event.currentTarget) === null || _event$currentTarget === void 0 || (_event$currentTarget$ = _event$currentTarget.contains) === null || _event$currentTarget$ === void 0 ? void 0 : _event$currentTarget$.call(_event$currentTarget, e.target));\n  });\n}\nfunction getTouchList(event) {\n  return event.type === 'touchend' || event.type === 'touchcancel' ? event.changedTouches : event.targetTouches;\n}\nfunction getValueEvent(event) {\n  return isTouch(event) ? getTouchList(event)[0] : event;\n}\nfunction distanceAngle(P1, P2) {\n  try {\n    const dx = P2.clientX - P1.clientX;\n    const dy = P2.clientY - P1.clientY;\n    const cx = (P2.clientX + P1.clientX) / 2;\n    const cy = (P2.clientY + P1.clientY) / 2;\n    const distance = Math.hypot(dx, dy);\n    const angle = -(Math.atan2(dx, dy) * 180) / Math.PI;\n    const origin = [cx, cy];\n    return {\n      angle,\n      distance,\n      origin\n    };\n  } catch (_unused) {}\n  return null;\n}\nfunction touchIds(event) {\n  return getCurrentTargetTouchList(event).map(touch => touch.identifier);\n}\nfunction touchDistanceAngle(event, ids) {\n  const [P1, P2] = Array.from(event.touches).filter(touch => ids.includes(touch.identifier));\n  return distanceAngle(P1, P2);\n}\nfunction pointerId(event) {\n  const valueEvent = getValueEvent(event);\n  return isTouch(event) ? valueEvent.identifier : valueEvent.pointerId;\n}\nfunction pointerValues(event) {\n  const valueEvent = getValueEvent(event);\n  return [valueEvent.clientX, valueEvent.clientY];\n}\nconst LINE_HEIGHT = 40;\nconst PAGE_HEIGHT = 800;\nfunction wheelValues(event) {\n  let {\n    deltaX,\n    deltaY,\n    deltaMode\n  } = event;\n  if (deltaMode === 1) {\n    deltaX *= LINE_HEIGHT;\n    deltaY *= LINE_HEIGHT;\n  } else if (deltaMode === 2) {\n    deltaX *= PAGE_HEIGHT;\n    deltaY *= PAGE_HEIGHT;\n  }\n  return [deltaX, deltaY];\n}\nfunction scrollValues(event) {\n  var _ref, _ref2;\n  const {\n    scrollX,\n    scrollY,\n    scrollLeft,\n    scrollTop\n  } = event.currentTarget;\n  return [(_ref = scrollX !== null && scrollX !== void 0 ? scrollX : scrollLeft) !== null && _ref !== void 0 ? _ref : 0, (_ref2 = scrollY !== null && scrollY !== void 0 ? scrollY : scrollTop) !== null && _ref2 !== void 0 ? _ref2 : 0];\n}\nfunction getEventDetails(event) {\n  const payload = {};\n  if ('buttons' in event) payload.buttons = event.buttons;\n  if ('shiftKey' in event) {\n    const {\n      shiftKey,\n      altKey,\n      metaKey,\n      ctrlKey\n    } = event;\n    Object.assign(payload, {\n      shiftKey,\n      altKey,\n      metaKey,\n      ctrlKey\n    });\n  }\n  return payload;\n}\n\nfunction call(v, ...args) {\n  if (typeof v === 'function') {\n    return v(...args);\n  } else {\n    return v;\n  }\n}\nfunction noop() {}\nfunction chain(...fns) {\n  if (fns.length === 0) return noop;\n  if (fns.length === 1) return fns[0];\n  return function () {\n    let result;\n    for (const fn of fns) {\n      result = fn.apply(this, arguments) || result;\n    }\n    return result;\n  };\n}\nfunction assignDefault(value, fallback) {\n  return Object.assign({}, fallback, value || {});\n}\n\nconst BEFORE_LAST_KINEMATICS_DELAY = 32;\nclass Engine {\n  constructor(ctrl, args, key) {\n    this.ctrl = ctrl;\n    this.args = args;\n    this.key = key;\n    if (!this.state) {\n      this.state = {};\n      this.computeValues([0, 0]);\n      this.computeInitial();\n      if (this.init) this.init();\n      this.reset();\n    }\n  }\n  get state() {\n    return this.ctrl.state[this.key];\n  }\n  set state(state) {\n    this.ctrl.state[this.key] = state;\n  }\n  get shared() {\n    return this.ctrl.state.shared;\n  }\n  get eventStore() {\n    return this.ctrl.gestureEventStores[this.key];\n  }\n  get timeoutStore() {\n    return this.ctrl.gestureTimeoutStores[this.key];\n  }\n  get config() {\n    return this.ctrl.config[this.key];\n  }\n  get sharedConfig() {\n    return this.ctrl.config.shared;\n  }\n  get handler() {\n    return this.ctrl.handlers[this.key];\n  }\n  reset() {\n    const {\n      state,\n      shared,\n      ingKey,\n      args\n    } = this;\n    shared[ingKey] = state._active = state.active = state._blocked = state._force = false;\n    state._step = [false, false];\n    state.intentional = false;\n    state._movement = [0, 0];\n    state._distance = [0, 0];\n    state._direction = [0, 0];\n    state._delta = [0, 0];\n    state._bounds = [[-Infinity, Infinity], [-Infinity, Infinity]];\n    state.args = args;\n    state.axis = undefined;\n    state.memo = undefined;\n    state.elapsedTime = state.timeDelta = 0;\n    state.direction = [0, 0];\n    state.distance = [0, 0];\n    state.overflow = [0, 0];\n    state._movementBound = [false, false];\n    state.velocity = [0, 0];\n    state.movement = [0, 0];\n    state.delta = [0, 0];\n    state.timeStamp = 0;\n  }\n  start(event) {\n    const state = this.state;\n    const config = this.config;\n    if (!state._active) {\n      this.reset();\n      this.computeInitial();\n      state._active = true;\n      state.target = event.target;\n      state.currentTarget = event.currentTarget;\n      state.lastOffset = config.from ? call(config.from, state) : state.offset;\n      state.offset = state.lastOffset;\n      state.startTime = state.timeStamp = event.timeStamp;\n    }\n  }\n  computeValues(values) {\n    const state = this.state;\n    state._values = values;\n    state.values = this.config.transform(values);\n  }\n  computeInitial() {\n    const state = this.state;\n    state._initial = state._values;\n    state.initial = state.values;\n  }\n  compute(event) {\n    const {\n      state,\n      config,\n      shared\n    } = this;\n    state.args = this.args;\n    let dt = 0;\n    if (event) {\n      state.event = event;\n      if (config.preventDefault && event.cancelable) state.event.preventDefault();\n      state.type = event.type;\n      shared.touches = this.ctrl.pointerIds.size || this.ctrl.touchIds.size;\n      shared.locked = !!document.pointerLockElement;\n      Object.assign(shared, getEventDetails(event));\n      shared.down = shared.pressed = shared.buttons % 2 === 1 || shared.touches > 0;\n      dt = event.timeStamp - state.timeStamp;\n      state.timeStamp = event.timeStamp;\n      state.elapsedTime = state.timeStamp - state.startTime;\n    }\n    if (state._active) {\n      const _absoluteDelta = state._delta.map(Math.abs);\n      V.addTo(state._distance, _absoluteDelta);\n    }\n    if (this.axisIntent) this.axisIntent(event);\n    const [_m0, _m1] = state._movement;\n    const [t0, t1] = config.threshold;\n    const {\n      _step,\n      values\n    } = state;\n    if (config.hasCustomTransform) {\n      if (_step[0] === false) _step[0] = Math.abs(_m0) >= t0 && values[0];\n      if (_step[1] === false) _step[1] = Math.abs(_m1) >= t1 && values[1];\n    } else {\n      if (_step[0] === false) _step[0] = Math.abs(_m0) >= t0 && Math.sign(_m0) * t0;\n      if (_step[1] === false) _step[1] = Math.abs(_m1) >= t1 && Math.sign(_m1) * t1;\n    }\n    state.intentional = _step[0] !== false || _step[1] !== false;\n    if (!state.intentional) return;\n    const movement = [0, 0];\n    if (config.hasCustomTransform) {\n      const [v0, v1] = values;\n      movement[0] = _step[0] !== false ? v0 - _step[0] : 0;\n      movement[1] = _step[1] !== false ? v1 - _step[1] : 0;\n    } else {\n      movement[0] = _step[0] !== false ? _m0 - _step[0] : 0;\n      movement[1] = _step[1] !== false ? _m1 - _step[1] : 0;\n    }\n    if (this.restrictToAxis && !state._blocked) this.restrictToAxis(movement);\n    const previousOffset = state.offset;\n    const gestureIsActive = state._active && !state._blocked || state.active;\n    if (gestureIsActive) {\n      state.first = state._active && !state.active;\n      state.last = !state._active && state.active;\n      state.active = shared[this.ingKey] = state._active;\n      if (event) {\n        if (state.first) {\n          if ('bounds' in config) state._bounds = call(config.bounds, state);\n          if (this.setup) this.setup();\n        }\n        state.movement = movement;\n        this.computeOffset();\n      }\n    }\n    const [ox, oy] = state.offset;\n    const [[x0, x1], [y0, y1]] = state._bounds;\n    state.overflow = [ox < x0 ? -1 : ox > x1 ? 1 : 0, oy < y0 ? -1 : oy > y1 ? 1 : 0];\n    state._movementBound[0] = state.overflow[0] ? state._movementBound[0] === false ? state._movement[0] : state._movementBound[0] : false;\n    state._movementBound[1] = state.overflow[1] ? state._movementBound[1] === false ? state._movement[1] : state._movementBound[1] : false;\n    const rubberband = state._active ? config.rubberband || [0, 0] : [0, 0];\n    state.offset = computeRubberband(state._bounds, state.offset, rubberband);\n    state.delta = V.sub(state.offset, previousOffset);\n    this.computeMovement();\n    if (gestureIsActive && (!state.last || dt > BEFORE_LAST_KINEMATICS_DELAY)) {\n      state.delta = V.sub(state.offset, previousOffset);\n      const absoluteDelta = state.delta.map(Math.abs);\n      V.addTo(state.distance, absoluteDelta);\n      state.direction = state.delta.map(Math.sign);\n      state._direction = state._delta.map(Math.sign);\n      if (!state.first && dt > 0) {\n        state.velocity = [absoluteDelta[0] / dt, absoluteDelta[1] / dt];\n        state.timeDelta = dt;\n      }\n    }\n  }\n  emit() {\n    const state = this.state;\n    const shared = this.shared;\n    const config = this.config;\n    if (!state._active) this.clean();\n    if ((state._blocked || !state.intentional) && !state._force && !config.triggerAllEvents) return;\n    const memo = this.handler(_objectSpread2(_objectSpread2(_objectSpread2({}, shared), state), {}, {\n      [this.aliasKey]: state.values\n    }));\n    if (memo !== undefined) state.memo = memo;\n  }\n  clean() {\n    this.eventStore.clean();\n    this.timeoutStore.clean();\n  }\n}\n\nfunction selectAxis([dx, dy], threshold) {\n  const absDx = Math.abs(dx);\n  const absDy = Math.abs(dy);\n  if (absDx > absDy && absDx > threshold) {\n    return 'x';\n  }\n  if (absDy > absDx && absDy > threshold) {\n    return 'y';\n  }\n  return undefined;\n}\nclass CoordinatesEngine extends Engine {\n  constructor(...args) {\n    super(...args);\n    _defineProperty(this, \"aliasKey\", 'xy');\n  }\n  reset() {\n    super.reset();\n    this.state.axis = undefined;\n  }\n  init() {\n    this.state.offset = [0, 0];\n    this.state.lastOffset = [0, 0];\n  }\n  computeOffset() {\n    this.state.offset = V.add(this.state.lastOffset, this.state.movement);\n  }\n  computeMovement() {\n    this.state.movement = V.sub(this.state.offset, this.state.lastOffset);\n  }\n  axisIntent(event) {\n    const state = this.state;\n    const config = this.config;\n    if (!state.axis && event) {\n      const threshold = typeof config.axisThreshold === 'object' ? config.axisThreshold[getPointerType(event)] : config.axisThreshold;\n      state.axis = selectAxis(state._movement, threshold);\n    }\n    state._blocked = (config.lockDirection || !!config.axis) && !state.axis || !!config.axis && config.axis !== state.axis;\n  }\n  restrictToAxis(v) {\n    if (this.config.axis || this.config.lockDirection) {\n      switch (this.state.axis) {\n        case 'x':\n          v[1] = 0;\n          break;\n        case 'y':\n          v[0] = 0;\n          break;\n      }\n    }\n  }\n}\n\nconst identity = v => v;\nconst DEFAULT_RUBBERBAND = 0.15;\nconst commonConfigResolver = {\n  enabled(value = true) {\n    return value;\n  },\n  eventOptions(value, _k, config) {\n    return _objectSpread2(_objectSpread2({}, config.shared.eventOptions), value);\n  },\n  preventDefault(value = false) {\n    return value;\n  },\n  triggerAllEvents(value = false) {\n    return value;\n  },\n  rubberband(value = 0) {\n    switch (value) {\n      case true:\n        return [DEFAULT_RUBBERBAND, DEFAULT_RUBBERBAND];\n      case false:\n        return [0, 0];\n      default:\n        return V.toVector(value);\n    }\n  },\n  from(value) {\n    if (typeof value === 'function') return value;\n    if (value != null) return V.toVector(value);\n  },\n  transform(value, _k, config) {\n    const transform = value || config.shared.transform;\n    this.hasCustomTransform = !!transform;\n    if (process.env.NODE_ENV === 'development') {\n      const originalTransform = transform || identity;\n      return v => {\n        const r = originalTransform(v);\n        if (!isFinite(r[0]) || !isFinite(r[1])) {\n          console.warn(`[@use-gesture]: config.transform() must produce a valid result, but it was: [${r[0]},${[1]}]`);\n        }\n        return r;\n      };\n    }\n    return transform || identity;\n  },\n  threshold(value) {\n    return V.toVector(value, 0);\n  }\n};\nif (process.env.NODE_ENV === 'development') {\n  Object.assign(commonConfigResolver, {\n    domTarget(value) {\n      if (value !== undefined) {\n        throw Error(`[@use-gesture]: \\`domTarget\\` option has been renamed to \\`target\\`.`);\n      }\n      return NaN;\n    },\n    lockDirection(value) {\n      if (value !== undefined) {\n        throw Error(`[@use-gesture]: \\`lockDirection\\` option has been merged with \\`axis\\`. Use it as in \\`{ axis: 'lock' }\\``);\n      }\n      return NaN;\n    },\n    initial(value) {\n      if (value !== undefined) {\n        throw Error(`[@use-gesture]: \\`initial\\` option has been renamed to \\`from\\`.`);\n      }\n      return NaN;\n    }\n  });\n}\n\nconst DEFAULT_AXIS_THRESHOLD = 0;\nconst coordinatesConfigResolver = _objectSpread2(_objectSpread2({}, commonConfigResolver), {}, {\n  axis(_v, _k, {\n    axis\n  }) {\n    this.lockDirection = axis === 'lock';\n    if (!this.lockDirection) return axis;\n  },\n  axisThreshold(value = DEFAULT_AXIS_THRESHOLD) {\n    return value;\n  },\n  bounds(value = {}) {\n    if (typeof value === 'function') {\n      return state => coordinatesConfigResolver.bounds(value(state));\n    }\n    if ('current' in value) {\n      return () => value.current;\n    }\n    if (typeof HTMLElement === 'function' && value instanceof HTMLElement) {\n      return value;\n    }\n    const {\n      left = -Infinity,\n      right = Infinity,\n      top = -Infinity,\n      bottom = Infinity\n    } = value;\n    return [[left, right], [top, bottom]];\n  }\n});\n\nconst KEYS_DELTA_MAP = {\n  ArrowRight: (displacement, factor = 1) => [displacement * factor, 0],\n  ArrowLeft: (displacement, factor = 1) => [-1 * displacement * factor, 0],\n  ArrowUp: (displacement, factor = 1) => [0, -1 * displacement * factor],\n  ArrowDown: (displacement, factor = 1) => [0, displacement * factor]\n};\nclass DragEngine extends CoordinatesEngine {\n  constructor(...args) {\n    super(...args);\n    _defineProperty(this, \"ingKey\", 'dragging');\n  }\n  reset() {\n    super.reset();\n    const state = this.state;\n    state._pointerId = undefined;\n    state._pointerActive = false;\n    state._keyboardActive = false;\n    state._preventScroll = false;\n    state._delayed = false;\n    state.swipe = [0, 0];\n    state.tap = false;\n    state.canceled = false;\n    state.cancel = this.cancel.bind(this);\n  }\n  setup() {\n    const state = this.state;\n    if (state._bounds instanceof HTMLElement) {\n      const boundRect = state._bounds.getBoundingClientRect();\n      const targetRect = state.currentTarget.getBoundingClientRect();\n      const _bounds = {\n        left: boundRect.left - targetRect.left + state.offset[0],\n        right: boundRect.right - targetRect.right + state.offset[0],\n        top: boundRect.top - targetRect.top + state.offset[1],\n        bottom: boundRect.bottom - targetRect.bottom + state.offset[1]\n      };\n      state._bounds = coordinatesConfigResolver.bounds(_bounds);\n    }\n  }\n  cancel() {\n    const state = this.state;\n    if (state.canceled) return;\n    state.canceled = true;\n    state._active = false;\n    setTimeout(() => {\n      this.compute();\n      this.emit();\n    }, 0);\n  }\n  setActive() {\n    this.state._active = this.state._pointerActive || this.state._keyboardActive;\n  }\n  clean() {\n    this.pointerClean();\n    this.state._pointerActive = false;\n    this.state._keyboardActive = false;\n    super.clean();\n  }\n  pointerDown(event) {\n    const config = this.config;\n    const state = this.state;\n    if (event.buttons != null && (Array.isArray(config.pointerButtons) ? !config.pointerButtons.includes(event.buttons) : config.pointerButtons !== -1 && config.pointerButtons !== event.buttons)) return;\n    const ctrlIds = this.ctrl.setEventIds(event);\n    if (config.pointerCapture) {\n      event.target.setPointerCapture(event.pointerId);\n    }\n    if (ctrlIds && ctrlIds.size > 1 && state._pointerActive) return;\n    this.start(event);\n    this.setupPointer(event);\n    state._pointerId = pointerId(event);\n    state._pointerActive = true;\n    this.computeValues(pointerValues(event));\n    this.computeInitial();\n    if (config.preventScrollAxis && getPointerType(event) !== 'mouse') {\n      state._active = false;\n      this.setupScrollPrevention(event);\n    } else if (config.delay > 0) {\n      this.setupDelayTrigger(event);\n      if (config.triggerAllEvents) {\n        this.compute(event);\n        this.emit();\n      }\n    } else {\n      this.startPointerDrag(event);\n    }\n  }\n  startPointerDrag(event) {\n    const state = this.state;\n    state._active = true;\n    state._preventScroll = true;\n    state._delayed = false;\n    this.compute(event);\n    this.emit();\n  }\n  pointerMove(event) {\n    const state = this.state;\n    const config = this.config;\n    if (!state._pointerActive) return;\n    const id = pointerId(event);\n    if (state._pointerId !== undefined && id !== state._pointerId) return;\n    const _values = pointerValues(event);\n    if (document.pointerLockElement === event.target) {\n      state._delta = [event.movementX, event.movementY];\n    } else {\n      state._delta = V.sub(_values, state._values);\n      this.computeValues(_values);\n    }\n    V.addTo(state._movement, state._delta);\n    this.compute(event);\n    if (state._delayed && state.intentional) {\n      this.timeoutStore.remove('dragDelay');\n      state.active = false;\n      this.startPointerDrag(event);\n      return;\n    }\n    if (config.preventScrollAxis && !state._preventScroll) {\n      if (state.axis) {\n        if (state.axis === config.preventScrollAxis || config.preventScrollAxis === 'xy') {\n          state._active = false;\n          this.clean();\n          return;\n        } else {\n          this.timeoutStore.remove('startPointerDrag');\n          this.startPointerDrag(event);\n          return;\n        }\n      } else {\n        return;\n      }\n    }\n    this.emit();\n  }\n  pointerUp(event) {\n    this.ctrl.setEventIds(event);\n    try {\n      if (this.config.pointerCapture && event.target.hasPointerCapture(event.pointerId)) {\n        ;\n        event.target.releasePointerCapture(event.pointerId);\n      }\n    } catch (_unused) {\n      if (process.env.NODE_ENV === 'development') {\n        console.warn(`[@use-gesture]: If you see this message, it's likely that you're using an outdated version of \\`@react-three/fiber\\`. \\n\\nPlease upgrade to the latest version.`);\n      }\n    }\n    const state = this.state;\n    const config = this.config;\n    if (!state._active || !state._pointerActive) return;\n    const id = pointerId(event);\n    if (state._pointerId !== undefined && id !== state._pointerId) return;\n    this.state._pointerActive = false;\n    this.setActive();\n    this.compute(event);\n    const [dx, dy] = state._distance;\n    state.tap = dx <= config.tapsThreshold && dy <= config.tapsThreshold;\n    if (state.tap && config.filterTaps) {\n      state._force = true;\n    } else {\n      const [_dx, _dy] = state._delta;\n      const [_mx, _my] = state._movement;\n      const [svx, svy] = config.swipe.velocity;\n      const [sx, sy] = config.swipe.distance;\n      const sdt = config.swipe.duration;\n      if (state.elapsedTime < sdt) {\n        const _vx = Math.abs(_dx / state.timeDelta);\n        const _vy = Math.abs(_dy / state.timeDelta);\n        if (_vx > svx && Math.abs(_mx) > sx) state.swipe[0] = Math.sign(_dx);\n        if (_vy > svy && Math.abs(_my) > sy) state.swipe[1] = Math.sign(_dy);\n      }\n    }\n    this.emit();\n  }\n  pointerClick(event) {\n    if (!this.state.tap && event.detail > 0) {\n      event.preventDefault();\n      event.stopPropagation();\n    }\n  }\n  setupPointer(event) {\n    const config = this.config;\n    const device = config.device;\n    if (process.env.NODE_ENV === 'development') {\n      try {\n        if (device === 'pointer' && config.preventScrollDelay === undefined) {\n          const currentTarget = 'uv' in event ? event.sourceEvent.currentTarget : event.currentTarget;\n          const style = window.getComputedStyle(currentTarget);\n          if (style.touchAction === 'auto') {\n            console.warn(`[@use-gesture]: The drag target has its \\`touch-action\\` style property set to \\`auto\\`. It is recommended to add \\`touch-action: 'none'\\` so that the drag gesture behaves correctly on touch-enabled devices. For more information read this: https://use-gesture.netlify.app/docs/extras/#touch-action.\\n\\nThis message will only show in development mode. It won't appear in production. If this is intended, you can ignore it.`, currentTarget);\n          }\n        }\n      } catch (_unused2) {}\n    }\n    if (config.pointerLock) {\n      event.currentTarget.requestPointerLock();\n    }\n    if (!config.pointerCapture) {\n      this.eventStore.add(this.sharedConfig.window, device, 'change', this.pointerMove.bind(this));\n      this.eventStore.add(this.sharedConfig.window, device, 'end', this.pointerUp.bind(this));\n      this.eventStore.add(this.sharedConfig.window, device, 'cancel', this.pointerUp.bind(this));\n    }\n  }\n  pointerClean() {\n    if (this.config.pointerLock && document.pointerLockElement === this.state.currentTarget) {\n      document.exitPointerLock();\n    }\n  }\n  preventScroll(event) {\n    if (this.state._preventScroll && event.cancelable) {\n      event.preventDefault();\n    }\n  }\n  setupScrollPrevention(event) {\n    this.state._preventScroll = false;\n    persistEvent(event);\n    const remove = this.eventStore.add(this.sharedConfig.window, 'touch', 'change', this.preventScroll.bind(this), {\n      passive: false\n    });\n    this.eventStore.add(this.sharedConfig.window, 'touch', 'end', remove);\n    this.eventStore.add(this.sharedConfig.window, 'touch', 'cancel', remove);\n    this.timeoutStore.add('startPointerDrag', this.startPointerDrag.bind(this), this.config.preventScrollDelay, event);\n  }\n  setupDelayTrigger(event) {\n    this.state._delayed = true;\n    this.timeoutStore.add('dragDelay', () => {\n      this.state._step = [0, 0];\n      this.startPointerDrag(event);\n    }, this.config.delay);\n  }\n  keyDown(event) {\n    const deltaFn = KEYS_DELTA_MAP[event.key];\n    if (deltaFn) {\n      const state = this.state;\n      const factor = event.shiftKey ? 10 : event.altKey ? 0.1 : 1;\n      this.start(event);\n      state._delta = deltaFn(this.config.keyboardDisplacement, factor);\n      state._keyboardActive = true;\n      V.addTo(state._movement, state._delta);\n      this.compute(event);\n      this.emit();\n    }\n  }\n  keyUp(event) {\n    if (!(event.key in KEYS_DELTA_MAP)) return;\n    this.state._keyboardActive = false;\n    this.setActive();\n    this.compute(event);\n    this.emit();\n  }\n  bind(bindFunction) {\n    const device = this.config.device;\n    bindFunction(device, 'start', this.pointerDown.bind(this));\n    if (this.config.pointerCapture) {\n      bindFunction(device, 'change', this.pointerMove.bind(this));\n      bindFunction(device, 'end', this.pointerUp.bind(this));\n      bindFunction(device, 'cancel', this.pointerUp.bind(this));\n      bindFunction('lostPointerCapture', '', this.pointerUp.bind(this));\n    }\n    if (this.config.keys) {\n      bindFunction('key', 'down', this.keyDown.bind(this));\n      bindFunction('key', 'up', this.keyUp.bind(this));\n    }\n    if (this.config.filterTaps) {\n      bindFunction('click', '', this.pointerClick.bind(this), {\n        capture: true,\n        passive: false\n      });\n    }\n  }\n}\nfunction persistEvent(event) {\n  'persist' in event && typeof event.persist === 'function' && event.persist();\n}\n\nconst isBrowser = typeof window !== 'undefined' && window.document && window.document.createElement;\nfunction supportsTouchEvents() {\n  return isBrowser && 'ontouchstart' in window;\n}\nfunction isTouchScreen() {\n  return supportsTouchEvents() || isBrowser && window.navigator.maxTouchPoints > 1;\n}\nfunction supportsPointerEvents() {\n  return isBrowser && 'onpointerdown' in window;\n}\nfunction supportsPointerLock() {\n  return isBrowser && 'exitPointerLock' in window.document;\n}\nfunction supportsGestureEvents() {\n  try {\n    return 'constructor' in GestureEvent;\n  } catch (e) {\n    return false;\n  }\n}\nconst SUPPORT = {\n  isBrowser,\n  gesture: supportsGestureEvents(),\n  touch: supportsTouchEvents(),\n  touchscreen: isTouchScreen(),\n  pointer: supportsPointerEvents(),\n  pointerLock: supportsPointerLock()\n};\n\nconst DEFAULT_PREVENT_SCROLL_DELAY = 250;\nconst DEFAULT_DRAG_DELAY = 180;\nconst DEFAULT_SWIPE_VELOCITY = 0.5;\nconst DEFAULT_SWIPE_DISTANCE = 50;\nconst DEFAULT_SWIPE_DURATION = 250;\nconst DEFAULT_KEYBOARD_DISPLACEMENT = 10;\nconst DEFAULT_DRAG_AXIS_THRESHOLD = {\n  mouse: 0,\n  touch: 0,\n  pen: 8\n};\nconst dragConfigResolver = _objectSpread2(_objectSpread2({}, coordinatesConfigResolver), {}, {\n  device(_v, _k, {\n    pointer: {\n      touch = false,\n      lock = false,\n      mouse = false\n    } = {}\n  }) {\n    this.pointerLock = lock && SUPPORT.pointerLock;\n    if (SUPPORT.touch && touch) return 'touch';\n    if (this.pointerLock) return 'mouse';\n    if (SUPPORT.pointer && !mouse) return 'pointer';\n    if (SUPPORT.touch) return 'touch';\n    return 'mouse';\n  },\n  preventScrollAxis(value, _k, {\n    preventScroll\n  }) {\n    this.preventScrollDelay = typeof preventScroll === 'number' ? preventScroll : preventScroll || preventScroll === undefined && value ? DEFAULT_PREVENT_SCROLL_DELAY : undefined;\n    if (!SUPPORT.touchscreen || preventScroll === false) return undefined;\n    return value ? value : preventScroll !== undefined ? 'y' : undefined;\n  },\n  pointerCapture(_v, _k, {\n    pointer: {\n      capture = true,\n      buttons = 1,\n      keys = true\n    } = {}\n  }) {\n    this.pointerButtons = buttons;\n    this.keys = keys;\n    return !this.pointerLock && this.device === 'pointer' && capture;\n  },\n  threshold(value, _k, {\n    filterTaps = false,\n    tapsThreshold = 3,\n    axis = undefined\n  }) {\n    const threshold = V.toVector(value, filterTaps ? tapsThreshold : axis ? 1 : 0);\n    this.filterTaps = filterTaps;\n    this.tapsThreshold = tapsThreshold;\n    return threshold;\n  },\n  swipe({\n    velocity = DEFAULT_SWIPE_VELOCITY,\n    distance = DEFAULT_SWIPE_DISTANCE,\n    duration = DEFAULT_SWIPE_DURATION\n  } = {}) {\n    return {\n      velocity: this.transform(V.toVector(velocity)),\n      distance: this.transform(V.toVector(distance)),\n      duration\n    };\n  },\n  delay(value = 0) {\n    switch (value) {\n      case true:\n        return DEFAULT_DRAG_DELAY;\n      case false:\n        return 0;\n      default:\n        return value;\n    }\n  },\n  axisThreshold(value) {\n    if (!value) return DEFAULT_DRAG_AXIS_THRESHOLD;\n    return _objectSpread2(_objectSpread2({}, DEFAULT_DRAG_AXIS_THRESHOLD), value);\n  },\n  keyboardDisplacement(value = DEFAULT_KEYBOARD_DISPLACEMENT) {\n    return value;\n  }\n});\nif (process.env.NODE_ENV === 'development') {\n  Object.assign(dragConfigResolver, {\n    useTouch(value) {\n      if (value !== undefined) {\n        throw Error(`[@use-gesture]: \\`useTouch\\` option has been renamed to \\`pointer.touch\\`. Use it as in \\`{ pointer: { touch: true } }\\`.`);\n      }\n      return NaN;\n    },\n    experimental_preventWindowScrollY(value) {\n      if (value !== undefined) {\n        throw Error(`[@use-gesture]: \\`experimental_preventWindowScrollY\\` option has been renamed to \\`preventScroll\\`.`);\n      }\n      return NaN;\n    },\n    swipeVelocity(value) {\n      if (value !== undefined) {\n        throw Error(`[@use-gesture]: \\`swipeVelocity\\` option has been renamed to \\`swipe.velocity\\`. Use it as in \\`{ swipe: { velocity: 0.5 } }\\`.`);\n      }\n      return NaN;\n    },\n    swipeDistance(value) {\n      if (value !== undefined) {\n        throw Error(`[@use-gesture]: \\`swipeDistance\\` option has been renamed to \\`swipe.distance\\`. Use it as in \\`{ swipe: { distance: 50 } }\\`.`);\n      }\n      return NaN;\n    },\n    swipeDuration(value) {\n      if (value !== undefined) {\n        throw Error(`[@use-gesture]: \\`swipeDuration\\` option has been renamed to \\`swipe.duration\\`. Use it as in \\`{ swipe: { duration: 250 } }\\`.`);\n      }\n      return NaN;\n    }\n  });\n}\n\nfunction clampStateInternalMovementToBounds(state) {\n  const [ox, oy] = state.overflow;\n  const [dx, dy] = state._delta;\n  const [dirx, diry] = state._direction;\n  if (ox < 0 && dx > 0 && dirx < 0 || ox > 0 && dx < 0 && dirx > 0) {\n    state._movement[0] = state._movementBound[0];\n  }\n  if (oy < 0 && dy > 0 && diry < 0 || oy > 0 && dy < 0 && diry > 0) {\n    state._movement[1] = state._movementBound[1];\n  }\n}\n\nconst SCALE_ANGLE_RATIO_INTENT_DEG = 30;\nconst PINCH_WHEEL_RATIO = 100;\nclass PinchEngine extends Engine {\n  constructor(...args) {\n    super(...args);\n    _defineProperty(this, \"ingKey\", 'pinching');\n    _defineProperty(this, \"aliasKey\", 'da');\n  }\n  init() {\n    this.state.offset = [1, 0];\n    this.state.lastOffset = [1, 0];\n    this.state._pointerEvents = new Map();\n  }\n  reset() {\n    super.reset();\n    const state = this.state;\n    state._touchIds = [];\n    state.canceled = false;\n    state.cancel = this.cancel.bind(this);\n    state.turns = 0;\n  }\n  computeOffset() {\n    const {\n      type,\n      movement,\n      lastOffset\n    } = this.state;\n    if (type === 'wheel') {\n      this.state.offset = V.add(movement, lastOffset);\n    } else {\n      this.state.offset = [(1 + movement[0]) * lastOffset[0], movement[1] + lastOffset[1]];\n    }\n  }\n  computeMovement() {\n    const {\n      offset,\n      lastOffset\n    } = this.state;\n    this.state.movement = [offset[0] / lastOffset[0], offset[1] - lastOffset[1]];\n  }\n  axisIntent() {\n    const state = this.state;\n    const [_m0, _m1] = state._movement;\n    if (!state.axis) {\n      const axisMovementDifference = Math.abs(_m0) * SCALE_ANGLE_RATIO_INTENT_DEG - Math.abs(_m1);\n      if (axisMovementDifference < 0) state.axis = 'angle';else if (axisMovementDifference > 0) state.axis = 'scale';\n    }\n  }\n  restrictToAxis(v) {\n    if (this.config.lockDirection) {\n      if (this.state.axis === 'scale') v[1] = 0;else if (this.state.axis === 'angle') v[0] = 0;\n    }\n  }\n  cancel() {\n    const state = this.state;\n    if (state.canceled) return;\n    setTimeout(() => {\n      state.canceled = true;\n      state._active = false;\n      this.compute();\n      this.emit();\n    }, 0);\n  }\n  touchStart(event) {\n    this.ctrl.setEventIds(event);\n    const state = this.state;\n    const ctrlTouchIds = this.ctrl.touchIds;\n    if (state._active) {\n      if (state._touchIds.every(id => ctrlTouchIds.has(id))) return;\n    }\n    if (ctrlTouchIds.size < 2) return;\n    this.start(event);\n    state._touchIds = Array.from(ctrlTouchIds).slice(0, 2);\n    const payload = touchDistanceAngle(event, state._touchIds);\n    if (!payload) return;\n    this.pinchStart(event, payload);\n  }\n  pointerStart(event) {\n    if (event.buttons != null && event.buttons % 2 !== 1) return;\n    this.ctrl.setEventIds(event);\n    event.target.setPointerCapture(event.pointerId);\n    const state = this.state;\n    const _pointerEvents = state._pointerEvents;\n    const ctrlPointerIds = this.ctrl.pointerIds;\n    if (state._active) {\n      if (Array.from(_pointerEvents.keys()).every(id => ctrlPointerIds.has(id))) return;\n    }\n    if (_pointerEvents.size < 2) {\n      _pointerEvents.set(event.pointerId, event);\n    }\n    if (state._pointerEvents.size < 2) return;\n    this.start(event);\n    const payload = distanceAngle(...Array.from(_pointerEvents.values()));\n    if (!payload) return;\n    this.pinchStart(event, payload);\n  }\n  pinchStart(event, payload) {\n    const state = this.state;\n    state.origin = payload.origin;\n    this.computeValues([payload.distance, payload.angle]);\n    this.computeInitial();\n    this.compute(event);\n    this.emit();\n  }\n  touchMove(event) {\n    if (!this.state._active) return;\n    const payload = touchDistanceAngle(event, this.state._touchIds);\n    if (!payload) return;\n    this.pinchMove(event, payload);\n  }\n  pointerMove(event) {\n    const _pointerEvents = this.state._pointerEvents;\n    if (_pointerEvents.has(event.pointerId)) {\n      _pointerEvents.set(event.pointerId, event);\n    }\n    if (!this.state._active) return;\n    const payload = distanceAngle(...Array.from(_pointerEvents.values()));\n    if (!payload) return;\n    this.pinchMove(event, payload);\n  }\n  pinchMove(event, payload) {\n    const state = this.state;\n    const prev_a = state._values[1];\n    const delta_a = payload.angle - prev_a;\n    let delta_turns = 0;\n    if (Math.abs(delta_a) > 270) delta_turns += Math.sign(delta_a);\n    this.computeValues([payload.distance, payload.angle - 360 * delta_turns]);\n    state.origin = payload.origin;\n    state.turns = delta_turns;\n    state._movement = [state._values[0] / state._initial[0] - 1, state._values[1] - state._initial[1]];\n    this.compute(event);\n    this.emit();\n  }\n  touchEnd(event) {\n    this.ctrl.setEventIds(event);\n    if (!this.state._active) return;\n    if (this.state._touchIds.some(id => !this.ctrl.touchIds.has(id))) {\n      this.state._active = false;\n      this.compute(event);\n      this.emit();\n    }\n  }\n  pointerEnd(event) {\n    const state = this.state;\n    this.ctrl.setEventIds(event);\n    try {\n      event.target.releasePointerCapture(event.pointerId);\n    } catch (_unused) {}\n    if (state._pointerEvents.has(event.pointerId)) {\n      state._pointerEvents.delete(event.pointerId);\n    }\n    if (!state._active) return;\n    if (state._pointerEvents.size < 2) {\n      state._active = false;\n      this.compute(event);\n      this.emit();\n    }\n  }\n  gestureStart(event) {\n    if (event.cancelable) event.preventDefault();\n    const state = this.state;\n    if (state._active) return;\n    this.start(event);\n    this.computeValues([event.scale, event.rotation]);\n    state.origin = [event.clientX, event.clientY];\n    this.compute(event);\n    this.emit();\n  }\n  gestureMove(event) {\n    if (event.cancelable) event.preventDefault();\n    if (!this.state._active) return;\n    const state = this.state;\n    this.computeValues([event.scale, event.rotation]);\n    state.origin = [event.clientX, event.clientY];\n    const _previousMovement = state._movement;\n    state._movement = [event.scale - 1, event.rotation];\n    state._delta = V.sub(state._movement, _previousMovement);\n    this.compute(event);\n    this.emit();\n  }\n  gestureEnd(event) {\n    if (!this.state._active) return;\n    this.state._active = false;\n    this.compute(event);\n    this.emit();\n  }\n  wheel(event) {\n    const modifierKey = this.config.modifierKey;\n    if (modifierKey && (Array.isArray(modifierKey) ? !modifierKey.find(k => event[k]) : !event[modifierKey])) return;\n    if (!this.state._active) this.wheelStart(event);else this.wheelChange(event);\n    this.timeoutStore.add('wheelEnd', this.wheelEnd.bind(this));\n  }\n  wheelStart(event) {\n    this.start(event);\n    this.wheelChange(event);\n  }\n  wheelChange(event) {\n    const isR3f = ('uv' in event);\n    if (!isR3f) {\n      if (event.cancelable) {\n        event.preventDefault();\n      }\n      if (process.env.NODE_ENV === 'development' && !event.defaultPrevented) {\n        console.warn(`[@use-gesture]: To properly support zoom on trackpads, try using the \\`target\\` option.\\n\\nThis message will only appear in development mode.`);\n      }\n    }\n    const state = this.state;\n    state._delta = [-wheelValues(event)[1] / PINCH_WHEEL_RATIO * state.offset[0], 0];\n    V.addTo(state._movement, state._delta);\n    clampStateInternalMovementToBounds(state);\n    this.state.origin = [event.clientX, event.clientY];\n    this.compute(event);\n    this.emit();\n  }\n  wheelEnd() {\n    if (!this.state._active) return;\n    this.state._active = false;\n    this.compute();\n    this.emit();\n  }\n  bind(bindFunction) {\n    const device = this.config.device;\n    if (!!device) {\n      bindFunction(device, 'start', this[device + 'Start'].bind(this));\n      bindFunction(device, 'change', this[device + 'Move'].bind(this));\n      bindFunction(device, 'end', this[device + 'End'].bind(this));\n      bindFunction(device, 'cancel', this[device + 'End'].bind(this));\n      bindFunction('lostPointerCapture', '', this[device + 'End'].bind(this));\n    }\n    if (this.config.pinchOnWheel) {\n      bindFunction('wheel', '', this.wheel.bind(this), {\n        passive: false\n      });\n    }\n  }\n}\n\nconst pinchConfigResolver = _objectSpread2(_objectSpread2({}, commonConfigResolver), {}, {\n  device(_v, _k, {\n    shared,\n    pointer: {\n      touch = false\n    } = {}\n  }) {\n    const sharedConfig = shared;\n    if (sharedConfig.target && !SUPPORT.touch && SUPPORT.gesture) return 'gesture';\n    if (SUPPORT.touch && touch) return 'touch';\n    if (SUPPORT.touchscreen) {\n      if (SUPPORT.pointer) return 'pointer';\n      if (SUPPORT.touch) return 'touch';\n    }\n  },\n  bounds(_v, _k, {\n    scaleBounds = {},\n    angleBounds = {}\n  }) {\n    const _scaleBounds = state => {\n      const D = assignDefault(call(scaleBounds, state), {\n        min: -Infinity,\n        max: Infinity\n      });\n      return [D.min, D.max];\n    };\n    const _angleBounds = state => {\n      const A = assignDefault(call(angleBounds, state), {\n        min: -Infinity,\n        max: Infinity\n      });\n      return [A.min, A.max];\n    };\n    if (typeof scaleBounds !== 'function' && typeof angleBounds !== 'function') return [_scaleBounds(), _angleBounds()];\n    return state => [_scaleBounds(state), _angleBounds(state)];\n  },\n  threshold(value, _k, config) {\n    this.lockDirection = config.axis === 'lock';\n    const threshold = V.toVector(value, this.lockDirection ? [0.1, 3] : 0);\n    return threshold;\n  },\n  modifierKey(value) {\n    if (value === undefined) return 'ctrlKey';\n    return value;\n  },\n  pinchOnWheel(value = true) {\n    return value;\n  }\n});\n\nclass MoveEngine extends CoordinatesEngine {\n  constructor(...args) {\n    super(...args);\n    _defineProperty(this, \"ingKey\", 'moving');\n  }\n  move(event) {\n    if (this.config.mouseOnly && event.pointerType !== 'mouse') return;\n    if (!this.state._active) this.moveStart(event);else this.moveChange(event);\n    this.timeoutStore.add('moveEnd', this.moveEnd.bind(this));\n  }\n  moveStart(event) {\n    this.start(event);\n    this.computeValues(pointerValues(event));\n    this.compute(event);\n    this.computeInitial();\n    this.emit();\n  }\n  moveChange(event) {\n    if (!this.state._active) return;\n    const values = pointerValues(event);\n    const state = this.state;\n    state._delta = V.sub(values, state._values);\n    V.addTo(state._movement, state._delta);\n    this.computeValues(values);\n    this.compute(event);\n    this.emit();\n  }\n  moveEnd(event) {\n    if (!this.state._active) return;\n    this.state._active = false;\n    this.compute(event);\n    this.emit();\n  }\n  bind(bindFunction) {\n    bindFunction('pointer', 'change', this.move.bind(this));\n    bindFunction('pointer', 'leave', this.moveEnd.bind(this));\n  }\n}\n\nconst moveConfigResolver = _objectSpread2(_objectSpread2({}, coordinatesConfigResolver), {}, {\n  mouseOnly: (value = true) => value\n});\n\nclass ScrollEngine extends CoordinatesEngine {\n  constructor(...args) {\n    super(...args);\n    _defineProperty(this, \"ingKey\", 'scrolling');\n  }\n  scroll(event) {\n    if (!this.state._active) this.start(event);\n    this.scrollChange(event);\n    this.timeoutStore.add('scrollEnd', this.scrollEnd.bind(this));\n  }\n  scrollChange(event) {\n    if (event.cancelable) event.preventDefault();\n    const state = this.state;\n    const values = scrollValues(event);\n    state._delta = V.sub(values, state._values);\n    V.addTo(state._movement, state._delta);\n    this.computeValues(values);\n    this.compute(event);\n    this.emit();\n  }\n  scrollEnd() {\n    if (!this.state._active) return;\n    this.state._active = false;\n    this.compute();\n    this.emit();\n  }\n  bind(bindFunction) {\n    bindFunction('scroll', '', this.scroll.bind(this));\n  }\n}\n\nconst scrollConfigResolver = coordinatesConfigResolver;\n\nclass WheelEngine extends CoordinatesEngine {\n  constructor(...args) {\n    super(...args);\n    _defineProperty(this, \"ingKey\", 'wheeling');\n  }\n  wheel(event) {\n    if (!this.state._active) this.start(event);\n    this.wheelChange(event);\n    this.timeoutStore.add('wheelEnd', this.wheelEnd.bind(this));\n  }\n  wheelChange(event) {\n    const state = this.state;\n    state._delta = wheelValues(event);\n    V.addTo(state._movement, state._delta);\n    clampStateInternalMovementToBounds(state);\n    this.compute(event);\n    this.emit();\n  }\n  wheelEnd() {\n    if (!this.state._active) return;\n    this.state._active = false;\n    this.compute();\n    this.emit();\n  }\n  bind(bindFunction) {\n    bindFunction('wheel', '', this.wheel.bind(this));\n  }\n}\n\nconst wheelConfigResolver = coordinatesConfigResolver;\n\nclass HoverEngine extends CoordinatesEngine {\n  constructor(...args) {\n    super(...args);\n    _defineProperty(this, \"ingKey\", 'hovering');\n  }\n  enter(event) {\n    if (this.config.mouseOnly && event.pointerType !== 'mouse') return;\n    this.start(event);\n    this.computeValues(pointerValues(event));\n    this.compute(event);\n    this.emit();\n  }\n  leave(event) {\n    if (this.config.mouseOnly && event.pointerType !== 'mouse') return;\n    const state = this.state;\n    if (!state._active) return;\n    state._active = false;\n    const values = pointerValues(event);\n    state._movement = state._delta = V.sub(values, state._values);\n    this.computeValues(values);\n    this.compute(event);\n    state.delta = state.movement;\n    this.emit();\n  }\n  bind(bindFunction) {\n    bindFunction('pointer', 'enter', this.enter.bind(this));\n    bindFunction('pointer', 'leave', this.leave.bind(this));\n  }\n}\n\nconst hoverConfigResolver = _objectSpread2(_objectSpread2({}, coordinatesConfigResolver), {}, {\n  mouseOnly: (value = true) => value\n});\n\nconst EngineMap = new Map();\nconst ConfigResolverMap = new Map();\nfunction registerAction(action) {\n  EngineMap.set(action.key, action.engine);\n  ConfigResolverMap.set(action.key, action.resolver);\n}\nconst dragAction = {\n  key: 'drag',\n  engine: DragEngine,\n  resolver: dragConfigResolver\n};\nconst hoverAction = {\n  key: 'hover',\n  engine: HoverEngine,\n  resolver: hoverConfigResolver\n};\nconst moveAction = {\n  key: 'move',\n  engine: MoveEngine,\n  resolver: moveConfigResolver\n};\nconst pinchAction = {\n  key: 'pinch',\n  engine: PinchEngine,\n  resolver: pinchConfigResolver\n};\nconst scrollAction = {\n  key: 'scroll',\n  engine: ScrollEngine,\n  resolver: scrollConfigResolver\n};\nconst wheelAction = {\n  key: 'wheel',\n  engine: WheelEngine,\n  resolver: wheelConfigResolver\n};\n\nexport { ConfigResolverMap as C, EngineMap as E, SUPPORT as S, _objectSpread2 as _, _defineProperty as a, touchIds as b, chain as c, toHandlerProp as d, dragAction as e, pinchAction as f, hoverAction as h, isTouch as i, moveAction as m, parseProp as p, registerAction as r, scrollAction as s, toDomEventType as t, wheelAction as w };\n"], "mappings": "AAAA,SAASA,CAAC,EAAEC,CAAC,IAAIC,iBAAiB,QAAQ,yBAAyB;AAEnE,SAASC,YAAYA,CAACC,KAAK,EAAEC,IAAI,EAAE;EACjC,IAAI,OAAOD,KAAK,KAAK,QAAQ,IAAIA,KAAK,KAAK,IAAI,EAAE,OAAOA,KAAK;EAC7D,IAAIE,IAAI,GAAGF,KAAK,CAACG,MAAM,CAACC,WAAW,CAAC;EACpC,IAAIF,IAAI,KAAKG,SAAS,EAAE;IACtB,IAAIC,GAAG,GAAGJ,IAAI,CAACK,IAAI,CAACP,KAAK,EAAEC,IAAI,IAAI,SAAS,CAAC;IAC7C,IAAI,OAAOK,GAAG,KAAK,QAAQ,EAAE,OAAOA,GAAG;IACvC,MAAM,IAAIE,SAAS,CAAC,8CAA8C,CAAC;EACrE;EACA,OAAO,CAACP,IAAI,KAAK,QAAQ,GAAGQ,MAAM,GAAGC,MAAM,EAAEV,KAAK,CAAC;AACrD;AAEA,SAASW,cAAcA,CAACC,GAAG,EAAE;EAC3B,IAAIC,GAAG,GAAGd,YAAY,CAACa,GAAG,EAAE,QAAQ,CAAC;EACrC,OAAO,OAAOC,GAAG,KAAK,QAAQ,GAAGA,GAAG,GAAGJ,MAAM,CAACI,GAAG,CAAC;AACpD;AAEA,SAASC,eAAeA,CAACC,GAAG,EAAEF,GAAG,EAAEG,KAAK,EAAE;EACxCH,GAAG,GAAGF,cAAc,CAACE,GAAG,CAAC;EACzB,IAAIA,GAAG,IAAIE,GAAG,EAAE;IACdE,MAAM,CAACC,cAAc,CAACH,GAAG,EAAEF,GAAG,EAAE;MAC9BG,KAAK,EAAEA,KAAK;MACZG,UAAU,EAAE,IAAI;MAChBC,YAAY,EAAE,IAAI;MAClBC,QAAQ,EAAE;IACZ,CAAC,CAAC;EACJ,CAAC,MAAM;IACLN,GAAG,CAACF,GAAG,CAAC,GAAGG,KAAK;EAClB;EACA,OAAOD,GAAG;AACZ;AAEA,SAASO,OAAOA,CAACC,CAAC,EAAEC,CAAC,EAAE;EACrB,IAAIC,CAAC,GAAGR,MAAM,CAACS,IAAI,CAACH,CAAC,CAAC;EACtB,IAAIN,MAAM,CAACU,qBAAqB,EAAE;IAChC,IAAIC,CAAC,GAAGX,MAAM,CAACU,qBAAqB,CAACJ,CAAC,CAAC;IACvCC,CAAC,KAAKI,CAAC,GAAGA,CAAC,CAACC,MAAM,CAAC,UAAUL,CAAC,EAAE;MAC9B,OAAOP,MAAM,CAACa,wBAAwB,CAACP,CAAC,EAAEC,CAAC,CAAC,CAACL,UAAU;IACzD,CAAC,CAAC,CAAC,EAAEM,CAAC,CAACM,IAAI,CAACC,KAAK,CAACP,CAAC,EAAEG,CAAC,CAAC;EACzB;EACA,OAAOH,CAAC;AACV;AACA,SAASQ,cAAcA,CAACV,CAAC,EAAE;EACzB,KAAK,IAAIC,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGU,SAAS,CAACC,MAAM,EAAEX,CAAC,EAAE,EAAE;IACzC,IAAIC,CAAC,GAAG,IAAI,IAAIS,SAAS,CAACV,CAAC,CAAC,GAAGU,SAAS,CAACV,CAAC,CAAC,GAAG,CAAC,CAAC;IAChDA,CAAC,GAAG,CAAC,GAAGF,OAAO,CAACL,MAAM,CAACQ,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAACW,OAAO,CAAC,UAAUZ,CAAC,EAAE;MAClDV,eAAe,CAACS,CAAC,EAAEC,CAAC,EAAEC,CAAC,CAACD,CAAC,CAAC,CAAC;IAC7B,CAAC,CAAC,GAAGP,MAAM,CAACoB,yBAAyB,GAAGpB,MAAM,CAACqB,gBAAgB,CAACf,CAAC,EAAEN,MAAM,CAACoB,yBAAyB,CAACZ,CAAC,CAAC,CAAC,GAAGH,OAAO,CAACL,MAAM,CAACQ,CAAC,CAAC,CAAC,CAACW,OAAO,CAAC,UAAUZ,CAAC,EAAE;MAChJP,MAAM,CAACC,cAAc,CAACK,CAAC,EAAEC,CAAC,EAAEP,MAAM,CAACa,wBAAwB,CAACL,CAAC,EAAED,CAAC,CAAC,CAAC;IACpE,CAAC,CAAC;EACJ;EACA,OAAOD,CAAC;AACV;AAEA,MAAMgB,cAAc,GAAG;EACrBC,OAAO,EAAE;IACPC,KAAK,EAAE,MAAM;IACbC,MAAM,EAAE,MAAM;IACdC,GAAG,EAAE;EACP,CAAC;EACDC,KAAK,EAAE;IACLH,KAAK,EAAE,MAAM;IACbC,MAAM,EAAE,MAAM;IACdC,GAAG,EAAE;EACP,CAAC;EACDE,KAAK,EAAE;IACLJ,KAAK,EAAE,OAAO;IACdC,MAAM,EAAE,MAAM;IACdC,GAAG,EAAE;EACP,CAAC;EACDG,OAAO,EAAE;IACPL,KAAK,EAAE,OAAO;IACdC,MAAM,EAAE,QAAQ;IAChBC,GAAG,EAAE;EACP;AACF,CAAC;AACD,SAASI,UAAUA,CAACC,MAAM,EAAE;EAC1B,IAAI,CAACA,MAAM,EAAE,OAAO,EAAE;EACtB,OAAOA,MAAM,CAAC,CAAC,CAAC,CAACC,WAAW,CAAC,CAAC,GAAGD,MAAM,CAACE,KAAK,CAAC,CAAC,CAAC;AAClD;AACA,MAAMC,8BAA8B,GAAG,CAAC,OAAO,EAAE,OAAO,CAAC;AACzD,SAASC,UAAUA,CAACC,OAAO,GAAG,KAAK,EAAEC,SAAS,EAAE;EAC9C,OAAOD,OAAO,IAAI,CAACF,8BAA8B,CAACI,QAAQ,CAACD,SAAS,CAAC;AACvE;AACA,SAASE,aAAaA,CAACC,MAAM,EAAEC,MAAM,GAAG,EAAE,EAAEL,OAAO,GAAG,KAAK,EAAE;EAC3D,MAAMM,WAAW,GAAGpB,cAAc,CAACkB,MAAM,CAAC;EAC1C,MAAMH,SAAS,GAAGK,WAAW,GAAGA,WAAW,CAACD,MAAM,CAAC,IAAIA,MAAM,GAAGA,MAAM;EACtE,OAAO,IAAI,GAAGX,UAAU,CAACU,MAAM,CAAC,GAAGV,UAAU,CAACO,SAAS,CAAC,IAAIF,UAAU,CAACC,OAAO,EAAEC,SAAS,CAAC,GAAG,SAAS,GAAG,EAAE,CAAC;AAC9G;AACA,MAAMM,oBAAoB,GAAG,CAAC,mBAAmB,EAAE,oBAAoB,CAAC;AACxE,SAASC,SAASA,CAACC,IAAI,EAAE;EACvB,IAAIC,QAAQ,GAAGD,IAAI,CAACE,SAAS,CAAC,CAAC,CAAC,CAACC,WAAW,CAAC,CAAC;EAC9C,MAAMC,OAAO,GAAG,CAAC,CAAC,CAACH,QAAQ,CAACI,OAAO,CAAC,SAAS,CAAC;EAC9C,IAAID,OAAO,EAAEH,QAAQ,GAAGA,QAAQ,CAACK,OAAO,CAAC,SAAS,EAAE,EAAE,CAAC;EACvD,MAAMC,UAAU,GAAGT,oBAAoB,CAACL,QAAQ,CAACQ,QAAQ,CAAC,GAAG,gBAAgB,GAAG,SAAS;EACzF,MAAMV,OAAO,GAAG,CAAC,CAAC,CAACU,QAAQ,CAACI,OAAO,CAACE,UAAU,CAAC;EAC/C,IAAIhB,OAAO,EAAEU,QAAQ,GAAGA,QAAQ,CAACK,OAAO,CAAC,SAAS,EAAE,EAAE,CAAC;EACvD,OAAO;IACLX,MAAM,EAAEM,QAAQ;IAChBV,OAAO;IACPa;EACF,CAAC;AACH;AACA,SAASI,cAAcA,CAACb,MAAM,EAAEC,MAAM,GAAG,EAAE,EAAE;EAC3C,MAAMC,WAAW,GAAGpB,cAAc,CAACkB,MAAM,CAAC;EAC1C,MAAMH,SAAS,GAAGK,WAAW,GAAGA,WAAW,CAACD,MAAM,CAAC,IAAIA,MAAM,GAAGA,MAAM;EACtE,OAAOD,MAAM,GAAGH,SAAS;AAC3B;AACA,SAASiB,OAAOA,CAACC,KAAK,EAAE;EACtB,OAAO,SAAS,IAAIA,KAAK;AAC3B;AACA,SAASC,cAAcA,CAACD,KAAK,EAAE;EAC7B,IAAID,OAAO,CAACC,KAAK,CAAC,EAAE,OAAO,OAAO;EAClC,IAAI,aAAa,IAAIA,KAAK,EAAE,OAAOA,KAAK,CAACE,WAAW;EACpD,OAAO,OAAO;AAChB;AACA,SAASC,yBAAyBA,CAACH,KAAK,EAAE;EACxC,OAAOI,KAAK,CAACC,IAAI,CAACL,KAAK,CAACM,OAAO,CAAC,CAACjD,MAAM,CAACN,CAAC,IAAI;IAC3C,IAAIwD,oBAAoB,EAAEC,qBAAqB;IAC/C,OAAOzD,CAAC,CAAC0D,MAAM,KAAKT,KAAK,CAACU,aAAa,KAAK,CAACH,oBAAoB,GAAGP,KAAK,CAACU,aAAa,MAAM,IAAI,IAAIH,oBAAoB,KAAK,KAAK,CAAC,IAAI,CAACC,qBAAqB,GAAGD,oBAAoB,CAACI,QAAQ,MAAM,IAAI,IAAIH,qBAAqB,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,qBAAqB,CAACzE,IAAI,CAACwE,oBAAoB,EAAExD,CAAC,CAAC0D,MAAM,CAAC,CAAC;EACrT,CAAC,CAAC;AACJ;AACA,SAASG,YAAYA,CAACZ,KAAK,EAAE;EAC3B,OAAOA,KAAK,CAACa,IAAI,KAAK,UAAU,IAAIb,KAAK,CAACa,IAAI,KAAK,aAAa,GAAGb,KAAK,CAACc,cAAc,GAAGd,KAAK,CAACe,aAAa;AAC/G;AACA,SAASC,aAAaA,CAAChB,KAAK,EAAE;EAC5B,OAAOD,OAAO,CAACC,KAAK,CAAC,GAAGY,YAAY,CAACZ,KAAK,CAAC,CAAC,CAAC,CAAC,GAAGA,KAAK;AACxD;AACA,SAASiB,aAAaA,CAACC,EAAE,EAAEC,EAAE,EAAE;EAC7B,IAAI;IACF,MAAMC,EAAE,GAAGD,EAAE,CAACE,OAAO,GAAGH,EAAE,CAACG,OAAO;IAClC,MAAMC,EAAE,GAAGH,EAAE,CAACI,OAAO,GAAGL,EAAE,CAACK,OAAO;IAClC,MAAMC,EAAE,GAAG,CAACL,EAAE,CAACE,OAAO,GAAGH,EAAE,CAACG,OAAO,IAAI,CAAC;IACxC,MAAMI,EAAE,GAAG,CAACN,EAAE,CAACI,OAAO,GAAGL,EAAE,CAACK,OAAO,IAAI,CAAC;IACxC,MAAMG,QAAQ,GAAGC,IAAI,CAACC,KAAK,CAACR,EAAE,EAAEE,EAAE,CAAC;IACnC,MAAMO,KAAK,GAAG,EAAEF,IAAI,CAACG,KAAK,CAACV,EAAE,EAAEE,EAAE,CAAC,GAAG,GAAG,CAAC,GAAGK,IAAI,CAACI,EAAE;IACnD,MAAMC,MAAM,GAAG,CAACR,EAAE,EAAEC,EAAE,CAAC;IACvB,OAAO;MACLI,KAAK;MACLH,QAAQ;MACRM;IACF,CAAC;EACH,CAAC,CAAC,OAAOC,OAAO,EAAE,CAAC;EACnB,OAAO,IAAI;AACb;AACA,SAASC,QAAQA,CAAClC,KAAK,EAAE;EACvB,OAAOG,yBAAyB,CAACH,KAAK,CAAC,CAACmC,GAAG,CAAC9D,KAAK,IAAIA,KAAK,CAAC+D,UAAU,CAAC;AACxE;AACA,SAASC,kBAAkBA,CAACrC,KAAK,EAAEsC,GAAG,EAAE;EACtC,MAAM,CAACpB,EAAE,EAAEC,EAAE,CAAC,GAAGf,KAAK,CAACC,IAAI,CAACL,KAAK,CAACM,OAAO,CAAC,CAACjD,MAAM,CAACgB,KAAK,IAAIiE,GAAG,CAACvD,QAAQ,CAACV,KAAK,CAAC+D,UAAU,CAAC,CAAC;EAC1F,OAAOnB,aAAa,CAACC,EAAE,EAAEC,EAAE,CAAC;AAC9B;AACA,SAASoB,SAASA,CAACvC,KAAK,EAAE;EACxB,MAAMwC,UAAU,GAAGxB,aAAa,CAAChB,KAAK,CAAC;EACvC,OAAOD,OAAO,CAACC,KAAK,CAAC,GAAGwC,UAAU,CAACJ,UAAU,GAAGI,UAAU,CAACD,SAAS;AACtE;AACA,SAASE,aAAaA,CAACzC,KAAK,EAAE;EAC5B,MAAMwC,UAAU,GAAGxB,aAAa,CAAChB,KAAK,CAAC;EACvC,OAAO,CAACwC,UAAU,CAACnB,OAAO,EAAEmB,UAAU,CAACjB,OAAO,CAAC;AACjD;AACA,MAAMmB,WAAW,GAAG,EAAE;AACtB,MAAMC,WAAW,GAAG,GAAG;AACvB,SAASC,WAAWA,CAAC5C,KAAK,EAAE;EAC1B,IAAI;IACF6C,MAAM;IACNC,MAAM;IACNC;EACF,CAAC,GAAG/C,KAAK;EACT,IAAI+C,SAAS,KAAK,CAAC,EAAE;IACnBF,MAAM,IAAIH,WAAW;IACrBI,MAAM,IAAIJ,WAAW;EACvB,CAAC,MAAM,IAAIK,SAAS,KAAK,CAAC,EAAE;IAC1BF,MAAM,IAAIF,WAAW;IACrBG,MAAM,IAAIH,WAAW;EACvB;EACA,OAAO,CAACE,MAAM,EAAEC,MAAM,CAAC;AACzB;AACA,SAASE,YAAYA,CAAChD,KAAK,EAAE;EAC3B,IAAIiD,IAAI,EAAEC,KAAK;EACf,MAAM;IACJC,OAAO;IACPC,OAAO;IACPC,UAAU;IACVC;EACF,CAAC,GAAGtD,KAAK,CAACU,aAAa;EACvB,OAAO,CAAC,CAACuC,IAAI,GAAGE,OAAO,KAAK,IAAI,IAAIA,OAAO,KAAK,KAAK,CAAC,GAAGA,OAAO,GAAGE,UAAU,MAAM,IAAI,IAAIJ,IAAI,KAAK,KAAK,CAAC,GAAGA,IAAI,GAAG,CAAC,EAAE,CAACC,KAAK,GAAGE,OAAO,KAAK,IAAI,IAAIA,OAAO,KAAK,KAAK,CAAC,GAAGA,OAAO,GAAGE,SAAS,MAAM,IAAI,IAAIJ,KAAK,KAAK,KAAK,CAAC,GAAGA,KAAK,GAAG,CAAC,CAAC;AACzO;AACA,SAASK,eAAeA,CAACvD,KAAK,EAAE;EAC9B,MAAMwD,OAAO,GAAG,CAAC,CAAC;EAClB,IAAI,SAAS,IAAIxD,KAAK,EAAEwD,OAAO,CAACC,OAAO,GAAGzD,KAAK,CAACyD,OAAO;EACvD,IAAI,UAAU,IAAIzD,KAAK,EAAE;IACvB,MAAM;MACJ0D,QAAQ;MACRC,MAAM;MACNC,OAAO;MACPC;IACF,CAAC,GAAG7D,KAAK;IACTvD,MAAM,CAACqH,MAAM,CAACN,OAAO,EAAE;MACrBE,QAAQ;MACRC,MAAM;MACNC,OAAO;MACPC;IACF,CAAC,CAAC;EACJ;EACA,OAAOL,OAAO;AAChB;AAEA,SAASzH,IAAIA,CAACgI,CAAC,EAAE,GAAGC,IAAI,EAAE;EACxB,IAAI,OAAOD,CAAC,KAAK,UAAU,EAAE;IAC3B,OAAOA,CAAC,CAAC,GAAGC,IAAI,CAAC;EACnB,CAAC,MAAM;IACL,OAAOD,CAAC;EACV;AACF;AACA,SAASE,IAAIA,CAAA,EAAG,CAAC;AACjB,SAASC,KAAKA,CAAC,GAAGC,GAAG,EAAE;EACrB,IAAIA,GAAG,CAACxG,MAAM,KAAK,CAAC,EAAE,OAAOsG,IAAI;EACjC,IAAIE,GAAG,CAACxG,MAAM,KAAK,CAAC,EAAE,OAAOwG,GAAG,CAAC,CAAC,CAAC;EACnC,OAAO,YAAY;IACjB,IAAIC,MAAM;IACV,KAAK,MAAMC,EAAE,IAAIF,GAAG,EAAE;MACpBC,MAAM,GAAGC,EAAE,CAAC7G,KAAK,CAAC,IAAI,EAAEE,SAAS,CAAC,IAAI0G,MAAM;IAC9C;IACA,OAAOA,MAAM;EACf,CAAC;AACH;AACA,SAASE,aAAaA,CAAC9H,KAAK,EAAE+H,QAAQ,EAAE;EACtC,OAAO9H,MAAM,CAACqH,MAAM,CAAC,CAAC,CAAC,EAAES,QAAQ,EAAE/H,KAAK,IAAI,CAAC,CAAC,CAAC;AACjD;AAEA,MAAMgI,4BAA4B,GAAG,EAAE;AACvC,MAAMC,MAAM,CAAC;EACXC,WAAWA,CAACC,IAAI,EAAEX,IAAI,EAAE3H,GAAG,EAAE;IAC3B,IAAI,CAACsI,IAAI,GAAGA,IAAI;IAChB,IAAI,CAACX,IAAI,GAAGA,IAAI;IAChB,IAAI,CAAC3H,GAAG,GAAGA,GAAG;IACd,IAAI,CAAC,IAAI,CAACuI,KAAK,EAAE;MACf,IAAI,CAACA,KAAK,GAAG,CAAC,CAAC;MACf,IAAI,CAACC,aAAa,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;MAC1B,IAAI,CAACC,cAAc,CAAC,CAAC;MACrB,IAAI,IAAI,CAACC,IAAI,EAAE,IAAI,CAACA,IAAI,CAAC,CAAC;MAC1B,IAAI,CAACC,KAAK,CAAC,CAAC;IACd;EACF;EACA,IAAIJ,KAAKA,CAAA,EAAG;IACV,OAAO,IAAI,CAACD,IAAI,CAACC,KAAK,CAAC,IAAI,CAACvI,GAAG,CAAC;EAClC;EACA,IAAIuI,KAAKA,CAACA,KAAK,EAAE;IACf,IAAI,CAACD,IAAI,CAACC,KAAK,CAAC,IAAI,CAACvI,GAAG,CAAC,GAAGuI,KAAK;EACnC;EACA,IAAIK,MAAMA,CAAA,EAAG;IACX,OAAO,IAAI,CAACN,IAAI,CAACC,KAAK,CAACK,MAAM;EAC/B;EACA,IAAIC,UAAUA,CAAA,EAAG;IACf,OAAO,IAAI,CAACP,IAAI,CAACQ,kBAAkB,CAAC,IAAI,CAAC9I,GAAG,CAAC;EAC/C;EACA,IAAI+I,YAAYA,CAAA,EAAG;IACjB,OAAO,IAAI,CAACT,IAAI,CAACU,oBAAoB,CAAC,IAAI,CAAChJ,GAAG,CAAC;EACjD;EACA,IAAIiJ,MAAMA,CAAA,EAAG;IACX,OAAO,IAAI,CAACX,IAAI,CAACW,MAAM,CAAC,IAAI,CAACjJ,GAAG,CAAC;EACnC;EACA,IAAIkJ,YAAYA,CAAA,EAAG;IACjB,OAAO,IAAI,CAACZ,IAAI,CAACW,MAAM,CAACL,MAAM;EAChC;EACA,IAAIO,OAAOA,CAAA,EAAG;IACZ,OAAO,IAAI,CAACb,IAAI,CAACc,QAAQ,CAAC,IAAI,CAACpJ,GAAG,CAAC;EACrC;EACA2I,KAAKA,CAAA,EAAG;IACN,MAAM;MACJJ,KAAK;MACLK,MAAM;MACNS,MAAM;MACN1B;IACF,CAAC,GAAG,IAAI;IACRiB,MAAM,CAACS,MAAM,CAAC,GAAGd,KAAK,CAACe,OAAO,GAAGf,KAAK,CAACgB,MAAM,GAAGhB,KAAK,CAACiB,QAAQ,GAAGjB,KAAK,CAACkB,MAAM,GAAG,KAAK;IACrFlB,KAAK,CAACmB,KAAK,GAAG,CAAC,KAAK,EAAE,KAAK,CAAC;IAC5BnB,KAAK,CAACoB,WAAW,GAAG,KAAK;IACzBpB,KAAK,CAACqB,SAAS,GAAG,CAAC,CAAC,EAAE,CAAC,CAAC;IACxBrB,KAAK,CAACsB,SAAS,GAAG,CAAC,CAAC,EAAE,CAAC,CAAC;IACxBtB,KAAK,CAACuB,UAAU,GAAG,CAAC,CAAC,EAAE,CAAC,CAAC;IACzBvB,KAAK,CAACwB,MAAM,GAAG,CAAC,CAAC,EAAE,CAAC,CAAC;IACrBxB,KAAK,CAACyB,OAAO,GAAG,CAAC,CAAC,CAACC,QAAQ,EAAEA,QAAQ,CAAC,EAAE,CAAC,CAACA,QAAQ,EAAEA,QAAQ,CAAC,CAAC;IAC9D1B,KAAK,CAACZ,IAAI,GAAGA,IAAI;IACjBY,KAAK,CAAC2B,IAAI,GAAG1K,SAAS;IACtB+I,KAAK,CAAC4B,IAAI,GAAG3K,SAAS;IACtB+I,KAAK,CAAC6B,WAAW,GAAG7B,KAAK,CAAC8B,SAAS,GAAG,CAAC;IACvC9B,KAAK,CAAC+B,SAAS,GAAG,CAAC,CAAC,EAAE,CAAC,CAAC;IACxB/B,KAAK,CAAClD,QAAQ,GAAG,CAAC,CAAC,EAAE,CAAC,CAAC;IACvBkD,KAAK,CAACgC,QAAQ,GAAG,CAAC,CAAC,EAAE,CAAC,CAAC;IACvBhC,KAAK,CAACiC,cAAc,GAAG,CAAC,KAAK,EAAE,KAAK,CAAC;IACrCjC,KAAK,CAACkC,QAAQ,GAAG,CAAC,CAAC,EAAE,CAAC,CAAC;IACvBlC,KAAK,CAACmC,QAAQ,GAAG,CAAC,CAAC,EAAE,CAAC,CAAC;IACvBnC,KAAK,CAACoC,KAAK,GAAG,CAAC,CAAC,EAAE,CAAC,CAAC;IACpBpC,KAAK,CAACqC,SAAS,GAAG,CAAC;EACrB;EACAhJ,KAAKA,CAAC+B,KAAK,EAAE;IACX,MAAM4E,KAAK,GAAG,IAAI,CAACA,KAAK;IACxB,MAAMU,MAAM,GAAG,IAAI,CAACA,MAAM;IAC1B,IAAI,CAACV,KAAK,CAACe,OAAO,EAAE;MAClB,IAAI,CAACX,KAAK,CAAC,CAAC;MACZ,IAAI,CAACF,cAAc,CAAC,CAAC;MACrBF,KAAK,CAACe,OAAO,GAAG,IAAI;MACpBf,KAAK,CAACnE,MAAM,GAAGT,KAAK,CAACS,MAAM;MAC3BmE,KAAK,CAAClE,aAAa,GAAGV,KAAK,CAACU,aAAa;MACzCkE,KAAK,CAACsC,UAAU,GAAG5B,MAAM,CAACjF,IAAI,GAAGtE,IAAI,CAACuJ,MAAM,CAACjF,IAAI,EAAEuE,KAAK,CAAC,GAAGA,KAAK,CAACuC,MAAM;MACxEvC,KAAK,CAACuC,MAAM,GAAGvC,KAAK,CAACsC,UAAU;MAC/BtC,KAAK,CAACwC,SAAS,GAAGxC,KAAK,CAACqC,SAAS,GAAGjH,KAAK,CAACiH,SAAS;IACrD;EACF;EACApC,aAAaA,CAACwC,MAAM,EAAE;IACpB,MAAMzC,KAAK,GAAG,IAAI,CAACA,KAAK;IACxBA,KAAK,CAAC0C,OAAO,GAAGD,MAAM;IACtBzC,KAAK,CAACyC,MAAM,GAAG,IAAI,CAAC/B,MAAM,CAACiC,SAAS,CAACF,MAAM,CAAC;EAC9C;EACAvC,cAAcA,CAAA,EAAG;IACf,MAAMF,KAAK,GAAG,IAAI,CAACA,KAAK;IACxBA,KAAK,CAAC4C,QAAQ,GAAG5C,KAAK,CAAC0C,OAAO;IAC9B1C,KAAK,CAAC6C,OAAO,GAAG7C,KAAK,CAACyC,MAAM;EAC9B;EACAK,OAAOA,CAAC1H,KAAK,EAAE;IACb,MAAM;MACJ4E,KAAK;MACLU,MAAM;MACNL;IACF,CAAC,GAAG,IAAI;IACRL,KAAK,CAACZ,IAAI,GAAG,IAAI,CAACA,IAAI;IACtB,IAAI2D,EAAE,GAAG,CAAC;IACV,IAAI3H,KAAK,EAAE;MACT4E,KAAK,CAAC5E,KAAK,GAAGA,KAAK;MACnB,IAAIsF,MAAM,CAACsC,cAAc,IAAI5H,KAAK,CAAC6H,UAAU,EAAEjD,KAAK,CAAC5E,KAAK,CAAC4H,cAAc,CAAC,CAAC;MAC3EhD,KAAK,CAAC/D,IAAI,GAAGb,KAAK,CAACa,IAAI;MACvBoE,MAAM,CAAC3E,OAAO,GAAG,IAAI,CAACqE,IAAI,CAACmD,UAAU,CAACC,IAAI,IAAI,IAAI,CAACpD,IAAI,CAACzC,QAAQ,CAAC6F,IAAI;MACrE9C,MAAM,CAAC+C,MAAM,GAAG,CAAC,CAACC,QAAQ,CAACC,kBAAkB;MAC7CzL,MAAM,CAACqH,MAAM,CAACmB,MAAM,EAAE1B,eAAe,CAACvD,KAAK,CAAC,CAAC;MAC7CiF,MAAM,CAACkD,IAAI,GAAGlD,MAAM,CAACmD,OAAO,GAAGnD,MAAM,CAACxB,OAAO,GAAG,CAAC,KAAK,CAAC,IAAIwB,MAAM,CAAC3E,OAAO,GAAG,CAAC;MAC7EqH,EAAE,GAAG3H,KAAK,CAACiH,SAAS,GAAGrC,KAAK,CAACqC,SAAS;MACtCrC,KAAK,CAACqC,SAAS,GAAGjH,KAAK,CAACiH,SAAS;MACjCrC,KAAK,CAAC6B,WAAW,GAAG7B,KAAK,CAACqC,SAAS,GAAGrC,KAAK,CAACwC,SAAS;IACvD;IACA,IAAIxC,KAAK,CAACe,OAAO,EAAE;MACjB,MAAM0C,cAAc,GAAGzD,KAAK,CAACwB,MAAM,CAACjE,GAAG,CAACR,IAAI,CAAC2G,GAAG,CAAC;MACjDlN,CAAC,CAACmN,KAAK,CAAC3D,KAAK,CAACsB,SAAS,EAAEmC,cAAc,CAAC;IAC1C;IACA,IAAI,IAAI,CAACG,UAAU,EAAE,IAAI,CAACA,UAAU,CAACxI,KAAK,CAAC;IAC3C,MAAM,CAACyI,GAAG,EAAEC,GAAG,CAAC,GAAG9D,KAAK,CAACqB,SAAS;IAClC,MAAM,CAAC0C,EAAE,EAAEC,EAAE,CAAC,GAAGtD,MAAM,CAACuD,SAAS;IACjC,MAAM;MACJ9C,KAAK;MACLsB;IACF,CAAC,GAAGzC,KAAK;IACT,IAAIU,MAAM,CAACwD,kBAAkB,EAAE;MAC7B,IAAI/C,KAAK,CAAC,CAAC,CAAC,KAAK,KAAK,EAAEA,KAAK,CAAC,CAAC,CAAC,GAAGpE,IAAI,CAAC2G,GAAG,CAACG,GAAG,CAAC,IAAIE,EAAE,IAAItB,MAAM,CAAC,CAAC,CAAC;MACnE,IAAItB,KAAK,CAAC,CAAC,CAAC,KAAK,KAAK,EAAEA,KAAK,CAAC,CAAC,CAAC,GAAGpE,IAAI,CAAC2G,GAAG,CAACI,GAAG,CAAC,IAAIE,EAAE,IAAIvB,MAAM,CAAC,CAAC,CAAC;IACrE,CAAC,MAAM;MACL,IAAItB,KAAK,CAAC,CAAC,CAAC,KAAK,KAAK,EAAEA,KAAK,CAAC,CAAC,CAAC,GAAGpE,IAAI,CAAC2G,GAAG,CAACG,GAAG,CAAC,IAAIE,EAAE,IAAIhH,IAAI,CAACoH,IAAI,CAACN,GAAG,CAAC,GAAGE,EAAE;MAC7E,IAAI5C,KAAK,CAAC,CAAC,CAAC,KAAK,KAAK,EAAEA,KAAK,CAAC,CAAC,CAAC,GAAGpE,IAAI,CAAC2G,GAAG,CAACI,GAAG,CAAC,IAAIE,EAAE,IAAIjH,IAAI,CAACoH,IAAI,CAACL,GAAG,CAAC,GAAGE,EAAE;IAC/E;IACAhE,KAAK,CAACoB,WAAW,GAAGD,KAAK,CAAC,CAAC,CAAC,KAAK,KAAK,IAAIA,KAAK,CAAC,CAAC,CAAC,KAAK,KAAK;IAC5D,IAAI,CAACnB,KAAK,CAACoB,WAAW,EAAE;IACxB,MAAMe,QAAQ,GAAG,CAAC,CAAC,EAAE,CAAC,CAAC;IACvB,IAAIzB,MAAM,CAACwD,kBAAkB,EAAE;MAC7B,MAAM,CAACE,EAAE,EAAEC,EAAE,CAAC,GAAG5B,MAAM;MACvBN,QAAQ,CAAC,CAAC,CAAC,GAAGhB,KAAK,CAAC,CAAC,CAAC,KAAK,KAAK,GAAGiD,EAAE,GAAGjD,KAAK,CAAC,CAAC,CAAC,GAAG,CAAC;MACpDgB,QAAQ,CAAC,CAAC,CAAC,GAAGhB,KAAK,CAAC,CAAC,CAAC,KAAK,KAAK,GAAGkD,EAAE,GAAGlD,KAAK,CAAC,CAAC,CAAC,GAAG,CAAC;IACtD,CAAC,MAAM;MACLgB,QAAQ,CAAC,CAAC,CAAC,GAAGhB,KAAK,CAAC,CAAC,CAAC,KAAK,KAAK,GAAG0C,GAAG,GAAG1C,KAAK,CAAC,CAAC,CAAC,GAAG,CAAC;MACrDgB,QAAQ,CAAC,CAAC,CAAC,GAAGhB,KAAK,CAAC,CAAC,CAAC,KAAK,KAAK,GAAG2C,GAAG,GAAG3C,KAAK,CAAC,CAAC,CAAC,GAAG,CAAC;IACvD;IACA,IAAI,IAAI,CAACmD,cAAc,IAAI,CAACtE,KAAK,CAACiB,QAAQ,EAAE,IAAI,CAACqD,cAAc,CAACnC,QAAQ,CAAC;IACzE,MAAMoC,cAAc,GAAGvE,KAAK,CAACuC,MAAM;IACnC,MAAMiC,eAAe,GAAGxE,KAAK,CAACe,OAAO,IAAI,CAACf,KAAK,CAACiB,QAAQ,IAAIjB,KAAK,CAACgB,MAAM;IACxE,IAAIwD,eAAe,EAAE;MACnBxE,KAAK,CAACyE,KAAK,GAAGzE,KAAK,CAACe,OAAO,IAAI,CAACf,KAAK,CAACgB,MAAM;MAC5ChB,KAAK,CAAC0E,IAAI,GAAG,CAAC1E,KAAK,CAACe,OAAO,IAAIf,KAAK,CAACgB,MAAM;MAC3ChB,KAAK,CAACgB,MAAM,GAAGX,MAAM,CAAC,IAAI,CAACS,MAAM,CAAC,GAAGd,KAAK,CAACe,OAAO;MAClD,IAAI3F,KAAK,EAAE;QACT,IAAI4E,KAAK,CAACyE,KAAK,EAAE;UACf,IAAI,QAAQ,IAAI/D,MAAM,EAAEV,KAAK,CAACyB,OAAO,GAAGtK,IAAI,CAACuJ,MAAM,CAACiE,MAAM,EAAE3E,KAAK,CAAC;UAClE,IAAI,IAAI,CAAC4E,KAAK,EAAE,IAAI,CAACA,KAAK,CAAC,CAAC;QAC9B;QACA5E,KAAK,CAACmC,QAAQ,GAAGA,QAAQ;QACzB,IAAI,CAAC0C,aAAa,CAAC,CAAC;MACtB;IACF;IACA,MAAM,CAACC,EAAE,EAAEC,EAAE,CAAC,GAAG/E,KAAK,CAACuC,MAAM;IAC7B,MAAM,CAAC,CAACyC,EAAE,EAAEC,EAAE,CAAC,EAAE,CAACC,EAAE,EAAEC,EAAE,CAAC,CAAC,GAAGnF,KAAK,CAACyB,OAAO;IAC1CzB,KAAK,CAACgC,QAAQ,GAAG,CAAC8C,EAAE,GAAGE,EAAE,GAAG,CAAC,CAAC,GAAGF,EAAE,GAAGG,EAAE,GAAG,CAAC,GAAG,CAAC,EAAEF,EAAE,GAAGG,EAAE,GAAG,CAAC,CAAC,GAAGH,EAAE,GAAGI,EAAE,GAAG,CAAC,GAAG,CAAC,CAAC;IACjFnF,KAAK,CAACiC,cAAc,CAAC,CAAC,CAAC,GAAGjC,KAAK,CAACgC,QAAQ,CAAC,CAAC,CAAC,GAAGhC,KAAK,CAACiC,cAAc,CAAC,CAAC,CAAC,KAAK,KAAK,GAAGjC,KAAK,CAACqB,SAAS,CAAC,CAAC,CAAC,GAAGrB,KAAK,CAACiC,cAAc,CAAC,CAAC,CAAC,GAAG,KAAK;IACtIjC,KAAK,CAACiC,cAAc,CAAC,CAAC,CAAC,GAAGjC,KAAK,CAACgC,QAAQ,CAAC,CAAC,CAAC,GAAGhC,KAAK,CAACiC,cAAc,CAAC,CAAC,CAAC,KAAK,KAAK,GAAGjC,KAAK,CAACqB,SAAS,CAAC,CAAC,CAAC,GAAGrB,KAAK,CAACiC,cAAc,CAAC,CAAC,CAAC,GAAG,KAAK;IACtI,MAAMmD,UAAU,GAAGpF,KAAK,CAACe,OAAO,GAAGL,MAAM,CAAC0E,UAAU,IAAI,CAAC,CAAC,EAAE,CAAC,CAAC,GAAG,CAAC,CAAC,EAAE,CAAC,CAAC;IACvEpF,KAAK,CAACuC,MAAM,GAAG7L,iBAAiB,CAACsJ,KAAK,CAACyB,OAAO,EAAEzB,KAAK,CAACuC,MAAM,EAAE6C,UAAU,CAAC;IACzEpF,KAAK,CAACoC,KAAK,GAAG5L,CAAC,CAAC6O,GAAG,CAACrF,KAAK,CAACuC,MAAM,EAAEgC,cAAc,CAAC;IACjD,IAAI,CAACe,eAAe,CAAC,CAAC;IACtB,IAAId,eAAe,KAAK,CAACxE,KAAK,CAAC0E,IAAI,IAAI3B,EAAE,GAAGnD,4BAA4B,CAAC,EAAE;MACzEI,KAAK,CAACoC,KAAK,GAAG5L,CAAC,CAAC6O,GAAG,CAACrF,KAAK,CAACuC,MAAM,EAAEgC,cAAc,CAAC;MACjD,MAAMgB,aAAa,GAAGvF,KAAK,CAACoC,KAAK,CAAC7E,GAAG,CAACR,IAAI,CAAC2G,GAAG,CAAC;MAC/ClN,CAAC,CAACmN,KAAK,CAAC3D,KAAK,CAAClD,QAAQ,EAAEyI,aAAa,CAAC;MACtCvF,KAAK,CAAC+B,SAAS,GAAG/B,KAAK,CAACoC,KAAK,CAAC7E,GAAG,CAACR,IAAI,CAACoH,IAAI,CAAC;MAC5CnE,KAAK,CAACuB,UAAU,GAAGvB,KAAK,CAACwB,MAAM,CAACjE,GAAG,CAACR,IAAI,CAACoH,IAAI,CAAC;MAC9C,IAAI,CAACnE,KAAK,CAACyE,KAAK,IAAI1B,EAAE,GAAG,CAAC,EAAE;QAC1B/C,KAAK,CAACkC,QAAQ,GAAG,CAACqD,aAAa,CAAC,CAAC,CAAC,GAAGxC,EAAE,EAAEwC,aAAa,CAAC,CAAC,CAAC,GAAGxC,EAAE,CAAC;QAC/D/C,KAAK,CAAC8B,SAAS,GAAGiB,EAAE;MACtB;IACF;EACF;EACAyC,IAAIA,CAAA,EAAG;IACL,MAAMxF,KAAK,GAAG,IAAI,CAACA,KAAK;IACxB,MAAMK,MAAM,GAAG,IAAI,CAACA,MAAM;IAC1B,MAAMK,MAAM,GAAG,IAAI,CAACA,MAAM;IAC1B,IAAI,CAACV,KAAK,CAACe,OAAO,EAAE,IAAI,CAAC0E,KAAK,CAAC,CAAC;IAChC,IAAI,CAACzF,KAAK,CAACiB,QAAQ,IAAI,CAACjB,KAAK,CAACoB,WAAW,KAAK,CAACpB,KAAK,CAACkB,MAAM,IAAI,CAACR,MAAM,CAACgF,gBAAgB,EAAE;IACzF,MAAM9D,IAAI,GAAG,IAAI,CAAChB,OAAO,CAAC/H,cAAc,CAACA,cAAc,CAACA,cAAc,CAAC,CAAC,CAAC,EAAEwH,MAAM,CAAC,EAAEL,KAAK,CAAC,EAAE,CAAC,CAAC,EAAE;MAC9F,CAAC,IAAI,CAAC2F,QAAQ,GAAG3F,KAAK,CAACyC;IACzB,CAAC,CAAC,CAAC;IACH,IAAIb,IAAI,KAAK3K,SAAS,EAAE+I,KAAK,CAAC4B,IAAI,GAAGA,IAAI;EAC3C;EACA6D,KAAKA,CAAA,EAAG;IACN,IAAI,CAACnF,UAAU,CAACmF,KAAK,CAAC,CAAC;IACvB,IAAI,CAACjF,YAAY,CAACiF,KAAK,CAAC,CAAC;EAC3B;AACF;AAEA,SAASG,UAAUA,CAAC,CAACpJ,EAAE,EAAEE,EAAE,CAAC,EAAEuH,SAAS,EAAE;EACvC,MAAM4B,KAAK,GAAG9I,IAAI,CAAC2G,GAAG,CAAClH,EAAE,CAAC;EAC1B,MAAMsJ,KAAK,GAAG/I,IAAI,CAAC2G,GAAG,CAAChH,EAAE,CAAC;EAC1B,IAAImJ,KAAK,GAAGC,KAAK,IAAID,KAAK,GAAG5B,SAAS,EAAE;IACtC,OAAO,GAAG;EACZ;EACA,IAAI6B,KAAK,GAAGD,KAAK,IAAIC,KAAK,GAAG7B,SAAS,EAAE;IACtC,OAAO,GAAG;EACZ;EACA,OAAOhN,SAAS;AAClB;AACA,MAAM8O,iBAAiB,SAASlG,MAAM,CAAC;EACrCC,WAAWA,CAAC,GAAGV,IAAI,EAAE;IACnB,KAAK,CAAC,GAAGA,IAAI,CAAC;IACd1H,eAAe,CAAC,IAAI,EAAE,UAAU,EAAE,IAAI,CAAC;EACzC;EACA0I,KAAKA,CAAA,EAAG;IACN,KAAK,CAACA,KAAK,CAAC,CAAC;IACb,IAAI,CAACJ,KAAK,CAAC2B,IAAI,GAAG1K,SAAS;EAC7B;EACAkJ,IAAIA,CAAA,EAAG;IACL,IAAI,CAACH,KAAK,CAACuC,MAAM,GAAG,CAAC,CAAC,EAAE,CAAC,CAAC;IAC1B,IAAI,CAACvC,KAAK,CAACsC,UAAU,GAAG,CAAC,CAAC,EAAE,CAAC,CAAC;EAChC;EACAuC,aAAaA,CAAA,EAAG;IACd,IAAI,CAAC7E,KAAK,CAACuC,MAAM,GAAG/L,CAAC,CAACwP,GAAG,CAAC,IAAI,CAAChG,KAAK,CAACsC,UAAU,EAAE,IAAI,CAACtC,KAAK,CAACmC,QAAQ,CAAC;EACvE;EACAmD,eAAeA,CAAA,EAAG;IAChB,IAAI,CAACtF,KAAK,CAACmC,QAAQ,GAAG3L,CAAC,CAAC6O,GAAG,CAAC,IAAI,CAACrF,KAAK,CAACuC,MAAM,EAAE,IAAI,CAACvC,KAAK,CAACsC,UAAU,CAAC;EACvE;EACAsB,UAAUA,CAACxI,KAAK,EAAE;IAChB,MAAM4E,KAAK,GAAG,IAAI,CAACA,KAAK;IACxB,MAAMU,MAAM,GAAG,IAAI,CAACA,MAAM;IAC1B,IAAI,CAACV,KAAK,CAAC2B,IAAI,IAAIvG,KAAK,EAAE;MACxB,MAAM6I,SAAS,GAAG,OAAOvD,MAAM,CAACuF,aAAa,KAAK,QAAQ,GAAGvF,MAAM,CAACuF,aAAa,CAAC5K,cAAc,CAACD,KAAK,CAAC,CAAC,GAAGsF,MAAM,CAACuF,aAAa;MAC/HjG,KAAK,CAAC2B,IAAI,GAAGiE,UAAU,CAAC5F,KAAK,CAACqB,SAAS,EAAE4C,SAAS,CAAC;IACrD;IACAjE,KAAK,CAACiB,QAAQ,GAAG,CAACP,MAAM,CAACwF,aAAa,IAAI,CAAC,CAACxF,MAAM,CAACiB,IAAI,KAAK,CAAC3B,KAAK,CAAC2B,IAAI,IAAI,CAAC,CAACjB,MAAM,CAACiB,IAAI,IAAIjB,MAAM,CAACiB,IAAI,KAAK3B,KAAK,CAAC2B,IAAI;EACxH;EACA2C,cAAcA,CAACnF,CAAC,EAAE;IAChB,IAAI,IAAI,CAACuB,MAAM,CAACiB,IAAI,IAAI,IAAI,CAACjB,MAAM,CAACwF,aAAa,EAAE;MACjD,QAAQ,IAAI,CAAClG,KAAK,CAAC2B,IAAI;QACrB,KAAK,GAAG;UACNxC,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC;UACR;QACF,KAAK,GAAG;UACNA,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC;UACR;MACJ;IACF;EACF;AACF;AAEA,MAAMgH,QAAQ,GAAGhH,CAAC,IAAIA,CAAC;AACvB,MAAMiH,kBAAkB,GAAG,IAAI;AAC/B,MAAMC,oBAAoB,GAAG;EAC3BC,OAAOA,CAAC1O,KAAK,GAAG,IAAI,EAAE;IACpB,OAAOA,KAAK;EACd,CAAC;EACD2O,YAAYA,CAAC3O,KAAK,EAAE4O,EAAE,EAAE9F,MAAM,EAAE;IAC9B,OAAO7H,cAAc,CAACA,cAAc,CAAC,CAAC,CAAC,EAAE6H,MAAM,CAACL,MAAM,CAACkG,YAAY,CAAC,EAAE3O,KAAK,CAAC;EAC9E,CAAC;EACDoL,cAAcA,CAACpL,KAAK,GAAG,KAAK,EAAE;IAC5B,OAAOA,KAAK;EACd,CAAC;EACD8N,gBAAgBA,CAAC9N,KAAK,GAAG,KAAK,EAAE;IAC9B,OAAOA,KAAK;EACd,CAAC;EACDwN,UAAUA,CAACxN,KAAK,GAAG,CAAC,EAAE;IACpB,QAAQA,KAAK;MACX,KAAK,IAAI;QACP,OAAO,CAACwO,kBAAkB,EAAEA,kBAAkB,CAAC;MACjD,KAAK,KAAK;QACR,OAAO,CAAC,CAAC,EAAE,CAAC,CAAC;MACf;QACE,OAAO5P,CAAC,CAACiQ,QAAQ,CAAC7O,KAAK,CAAC;IAC5B;EACF,CAAC;EACD6D,IAAIA,CAAC7D,KAAK,EAAE;IACV,IAAI,OAAOA,KAAK,KAAK,UAAU,EAAE,OAAOA,KAAK;IAC7C,IAAIA,KAAK,IAAI,IAAI,EAAE,OAAOpB,CAAC,CAACiQ,QAAQ,CAAC7O,KAAK,CAAC;EAC7C,CAAC;EACD+K,SAASA,CAAC/K,KAAK,EAAE4O,EAAE,EAAE9F,MAAM,EAAE;IAC3B,MAAMiC,SAAS,GAAG/K,KAAK,IAAI8I,MAAM,CAACL,MAAM,CAACsC,SAAS;IAClD,IAAI,CAACuB,kBAAkB,GAAG,CAAC,CAACvB,SAAS;IACrC,IAAI+D,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,aAAa,EAAE;MAC1C,MAAMC,iBAAiB,GAAGlE,SAAS,IAAIwD,QAAQ;MAC/C,OAAOhH,CAAC,IAAI;QACV,MAAM/G,CAAC,GAAGyO,iBAAiB,CAAC1H,CAAC,CAAC;QAC9B,IAAI,CAAC2H,QAAQ,CAAC1O,CAAC,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC0O,QAAQ,CAAC1O,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;UACtC2O,OAAO,CAACC,IAAI,CAAC,gFAAgF5O,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,GAAG,CAAC;QAC9G;QACA,OAAOA,CAAC;MACV,CAAC;IACH;IACA,OAAOuK,SAAS,IAAIwD,QAAQ;EAC9B,CAAC;EACDlC,SAASA,CAACrM,KAAK,EAAE;IACf,OAAOpB,CAAC,CAACiQ,QAAQ,CAAC7O,KAAK,EAAE,CAAC,CAAC;EAC7B;AACF,CAAC;AACD,IAAI8O,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,aAAa,EAAE;EAC1C/O,MAAM,CAACqH,MAAM,CAACmH,oBAAoB,EAAE;IAClCY,SAASA,CAACrP,KAAK,EAAE;MACf,IAAIA,KAAK,KAAKX,SAAS,EAAE;QACvB,MAAMiQ,KAAK,CAAC,sEAAsE,CAAC;MACrF;MACA,OAAOC,GAAG;IACZ,CAAC;IACDjB,aAAaA,CAACtO,KAAK,EAAE;MACnB,IAAIA,KAAK,KAAKX,SAAS,EAAE;QACvB,MAAMiQ,KAAK,CAAC,2GAA2G,CAAC;MAC1H;MACA,OAAOC,GAAG;IACZ,CAAC;IACDtE,OAAOA,CAACjL,KAAK,EAAE;MACb,IAAIA,KAAK,KAAKX,SAAS,EAAE;QACvB,MAAMiQ,KAAK,CAAC,kEAAkE,CAAC;MACjF;MACA,OAAOC,GAAG;IACZ;EACF,CAAC,CAAC;AACJ;AAEA,MAAMC,sBAAsB,GAAG,CAAC;AAChC,MAAMC,yBAAyB,GAAGxO,cAAc,CAACA,cAAc,CAAC,CAAC,CAAC,EAAEwN,oBAAoB,CAAC,EAAE,CAAC,CAAC,EAAE;EAC7F1E,IAAIA,CAAC2F,EAAE,EAAEd,EAAE,EAAE;IACX7E;EACF,CAAC,EAAE;IACD,IAAI,CAACuE,aAAa,GAAGvE,IAAI,KAAK,MAAM;IACpC,IAAI,CAAC,IAAI,CAACuE,aAAa,EAAE,OAAOvE,IAAI;EACtC,CAAC;EACDsE,aAAaA,CAACrO,KAAK,GAAGwP,sBAAsB,EAAE;IAC5C,OAAOxP,KAAK;EACd,CAAC;EACD+M,MAAMA,CAAC/M,KAAK,GAAG,CAAC,CAAC,EAAE;IACjB,IAAI,OAAOA,KAAK,KAAK,UAAU,EAAE;MAC/B,OAAOoI,KAAK,IAAIqH,yBAAyB,CAAC1C,MAAM,CAAC/M,KAAK,CAACoI,KAAK,CAAC,CAAC;IAChE;IACA,IAAI,SAAS,IAAIpI,KAAK,EAAE;MACtB,OAAO,MAAMA,KAAK,CAAC2P,OAAO;IAC5B;IACA,IAAI,OAAOC,WAAW,KAAK,UAAU,IAAI5P,KAAK,YAAY4P,WAAW,EAAE;MACrE,OAAO5P,KAAK;IACd;IACA,MAAM;MACJ6P,IAAI,GAAG,CAAC/F,QAAQ;MAChBgG,KAAK,GAAGhG,QAAQ;MAChBiG,GAAG,GAAG,CAACjG,QAAQ;MACfkG,MAAM,GAAGlG;IACX,CAAC,GAAG9J,KAAK;IACT,OAAO,CAAC,CAAC6P,IAAI,EAAEC,KAAK,CAAC,EAAE,CAACC,GAAG,EAAEC,MAAM,CAAC,CAAC;EACvC;AACF,CAAC,CAAC;AAEF,MAAMC,cAAc,GAAG;EACrBC,UAAU,EAAEA,CAACC,YAAY,EAAEC,MAAM,GAAG,CAAC,KAAK,CAACD,YAAY,GAAGC,MAAM,EAAE,CAAC,CAAC;EACpEC,SAAS,EAAEA,CAACF,YAAY,EAAEC,MAAM,GAAG,CAAC,KAAK,CAAC,CAAC,CAAC,GAAGD,YAAY,GAAGC,MAAM,EAAE,CAAC,CAAC;EACxEE,OAAO,EAAEA,CAACH,YAAY,EAAEC,MAAM,GAAG,CAAC,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC,GAAGD,YAAY,GAAGC,MAAM,CAAC;EACtEG,SAAS,EAAEA,CAACJ,YAAY,EAAEC,MAAM,GAAG,CAAC,KAAK,CAAC,CAAC,EAAED,YAAY,GAAGC,MAAM;AACpE,CAAC;AACD,MAAMI,UAAU,SAASrC,iBAAiB,CAAC;EACzCjG,WAAWA,CAAC,GAAGV,IAAI,EAAE;IACnB,KAAK,CAAC,GAAGA,IAAI,CAAC;IACd1H,eAAe,CAAC,IAAI,EAAE,QAAQ,EAAE,UAAU,CAAC;EAC7C;EACA0I,KAAKA,CAAA,EAAG;IACN,KAAK,CAACA,KAAK,CAAC,CAAC;IACb,MAAMJ,KAAK,GAAG,IAAI,CAACA,KAAK;IACxBA,KAAK,CAACqI,UAAU,GAAGpR,SAAS;IAC5B+I,KAAK,CAACsI,cAAc,GAAG,KAAK;IAC5BtI,KAAK,CAACuI,eAAe,GAAG,KAAK;IAC7BvI,KAAK,CAACwI,cAAc,GAAG,KAAK;IAC5BxI,KAAK,CAACyI,QAAQ,GAAG,KAAK;IACtBzI,KAAK,CAAC0I,KAAK,GAAG,CAAC,CAAC,EAAE,CAAC,CAAC;IACpB1I,KAAK,CAAC2I,GAAG,GAAG,KAAK;IACjB3I,KAAK,CAAC4I,QAAQ,GAAG,KAAK;IACtB5I,KAAK,CAAC6I,MAAM,GAAG,IAAI,CAACA,MAAM,CAACC,IAAI,CAAC,IAAI,CAAC;EACvC;EACAlE,KAAKA,CAAA,EAAG;IACN,MAAM5E,KAAK,GAAG,IAAI,CAACA,KAAK;IACxB,IAAIA,KAAK,CAACyB,OAAO,YAAY+F,WAAW,EAAE;MACxC,MAAMuB,SAAS,GAAG/I,KAAK,CAACyB,OAAO,CAACuH,qBAAqB,CAAC,CAAC;MACvD,MAAMC,UAAU,GAAGjJ,KAAK,CAAClE,aAAa,CAACkN,qBAAqB,CAAC,CAAC;MAC9D,MAAMvH,OAAO,GAAG;QACdgG,IAAI,EAAEsB,SAAS,CAACtB,IAAI,GAAGwB,UAAU,CAACxB,IAAI,GAAGzH,KAAK,CAACuC,MAAM,CAAC,CAAC,CAAC;QACxDmF,KAAK,EAAEqB,SAAS,CAACrB,KAAK,GAAGuB,UAAU,CAACvB,KAAK,GAAG1H,KAAK,CAACuC,MAAM,CAAC,CAAC,CAAC;QAC3DoF,GAAG,EAAEoB,SAAS,CAACpB,GAAG,GAAGsB,UAAU,CAACtB,GAAG,GAAG3H,KAAK,CAACuC,MAAM,CAAC,CAAC,CAAC;QACrDqF,MAAM,EAAEmB,SAAS,CAACnB,MAAM,GAAGqB,UAAU,CAACrB,MAAM,GAAG5H,KAAK,CAACuC,MAAM,CAAC,CAAC;MAC/D,CAAC;MACDvC,KAAK,CAACyB,OAAO,GAAG4F,yBAAyB,CAAC1C,MAAM,CAAClD,OAAO,CAAC;IAC3D;EACF;EACAoH,MAAMA,CAAA,EAAG;IACP,MAAM7I,KAAK,GAAG,IAAI,CAACA,KAAK;IACxB,IAAIA,KAAK,CAAC4I,QAAQ,EAAE;IACpB5I,KAAK,CAAC4I,QAAQ,GAAG,IAAI;IACrB5I,KAAK,CAACe,OAAO,GAAG,KAAK;IACrBmI,UAAU,CAAC,MAAM;MACf,IAAI,CAACpG,OAAO,CAAC,CAAC;MACd,IAAI,CAAC0C,IAAI,CAAC,CAAC;IACb,CAAC,EAAE,CAAC,CAAC;EACP;EACA2D,SAASA,CAAA,EAAG;IACV,IAAI,CAACnJ,KAAK,CAACe,OAAO,GAAG,IAAI,CAACf,KAAK,CAACsI,cAAc,IAAI,IAAI,CAACtI,KAAK,CAACuI,eAAe;EAC9E;EACA9C,KAAKA,CAAA,EAAG;IACN,IAAI,CAAC2D,YAAY,CAAC,CAAC;IACnB,IAAI,CAACpJ,KAAK,CAACsI,cAAc,GAAG,KAAK;IACjC,IAAI,CAACtI,KAAK,CAACuI,eAAe,GAAG,KAAK;IAClC,KAAK,CAAC9C,KAAK,CAAC,CAAC;EACf;EACA4D,WAAWA,CAACjO,KAAK,EAAE;IACjB,MAAMsF,MAAM,GAAG,IAAI,CAACA,MAAM;IAC1B,MAAMV,KAAK,GAAG,IAAI,CAACA,KAAK;IACxB,IAAI5E,KAAK,CAACyD,OAAO,IAAI,IAAI,KAAKrD,KAAK,CAAC8N,OAAO,CAAC5I,MAAM,CAAC6I,cAAc,CAAC,GAAG,CAAC7I,MAAM,CAAC6I,cAAc,CAACpP,QAAQ,CAACiB,KAAK,CAACyD,OAAO,CAAC,GAAG6B,MAAM,CAAC6I,cAAc,KAAK,CAAC,CAAC,IAAI7I,MAAM,CAAC6I,cAAc,KAAKnO,KAAK,CAACyD,OAAO,CAAC,EAAE;IAChM,MAAM2K,OAAO,GAAG,IAAI,CAACzJ,IAAI,CAAC0J,WAAW,CAACrO,KAAK,CAAC;IAC5C,IAAIsF,MAAM,CAACgJ,cAAc,EAAE;MACzBtO,KAAK,CAACS,MAAM,CAAC8N,iBAAiB,CAACvO,KAAK,CAACuC,SAAS,CAAC;IACjD;IACA,IAAI6L,OAAO,IAAIA,OAAO,CAACrG,IAAI,GAAG,CAAC,IAAInD,KAAK,CAACsI,cAAc,EAAE;IACzD,IAAI,CAACjP,KAAK,CAAC+B,KAAK,CAAC;IACjB,IAAI,CAACwO,YAAY,CAACxO,KAAK,CAAC;IACxB4E,KAAK,CAACqI,UAAU,GAAG1K,SAAS,CAACvC,KAAK,CAAC;IACnC4E,KAAK,CAACsI,cAAc,GAAG,IAAI;IAC3B,IAAI,CAACrI,aAAa,CAACpC,aAAa,CAACzC,KAAK,CAAC,CAAC;IACxC,IAAI,CAAC8E,cAAc,CAAC,CAAC;IACrB,IAAIQ,MAAM,CAACmJ,iBAAiB,IAAIxO,cAAc,CAACD,KAAK,CAAC,KAAK,OAAO,EAAE;MACjE4E,KAAK,CAACe,OAAO,GAAG,KAAK;MACrB,IAAI,CAAC+I,qBAAqB,CAAC1O,KAAK,CAAC;IACnC,CAAC,MAAM,IAAIsF,MAAM,CAACqJ,KAAK,GAAG,CAAC,EAAE;MAC3B,IAAI,CAACC,iBAAiB,CAAC5O,KAAK,CAAC;MAC7B,IAAIsF,MAAM,CAACgF,gBAAgB,EAAE;QAC3B,IAAI,CAAC5C,OAAO,CAAC1H,KAAK,CAAC;QACnB,IAAI,CAACoK,IAAI,CAAC,CAAC;MACb;IACF,CAAC,MAAM;MACL,IAAI,CAACyE,gBAAgB,CAAC7O,KAAK,CAAC;IAC9B;EACF;EACA6O,gBAAgBA,CAAC7O,KAAK,EAAE;IACtB,MAAM4E,KAAK,GAAG,IAAI,CAACA,KAAK;IACxBA,KAAK,CAACe,OAAO,GAAG,IAAI;IACpBf,KAAK,CAACwI,cAAc,GAAG,IAAI;IAC3BxI,KAAK,CAACyI,QAAQ,GAAG,KAAK;IACtB,IAAI,CAAC3F,OAAO,CAAC1H,KAAK,CAAC;IACnB,IAAI,CAACoK,IAAI,CAAC,CAAC;EACb;EACA0E,WAAWA,CAAC9O,KAAK,EAAE;IACjB,MAAM4E,KAAK,GAAG,IAAI,CAACA,KAAK;IACxB,MAAMU,MAAM,GAAG,IAAI,CAACA,MAAM;IAC1B,IAAI,CAACV,KAAK,CAACsI,cAAc,EAAE;IAC3B,MAAM6B,EAAE,GAAGxM,SAAS,CAACvC,KAAK,CAAC;IAC3B,IAAI4E,KAAK,CAACqI,UAAU,KAAKpR,SAAS,IAAIkT,EAAE,KAAKnK,KAAK,CAACqI,UAAU,EAAE;IAC/D,MAAM3F,OAAO,GAAG7E,aAAa,CAACzC,KAAK,CAAC;IACpC,IAAIiI,QAAQ,CAACC,kBAAkB,KAAKlI,KAAK,CAACS,MAAM,EAAE;MAChDmE,KAAK,CAACwB,MAAM,GAAG,CAACpG,KAAK,CAACgP,SAAS,EAAEhP,KAAK,CAACiP,SAAS,CAAC;IACnD,CAAC,MAAM;MACLrK,KAAK,CAACwB,MAAM,GAAGhL,CAAC,CAAC6O,GAAG,CAAC3C,OAAO,EAAE1C,KAAK,CAAC0C,OAAO,CAAC;MAC5C,IAAI,CAACzC,aAAa,CAACyC,OAAO,CAAC;IAC7B;IACAlM,CAAC,CAACmN,KAAK,CAAC3D,KAAK,CAACqB,SAAS,EAAErB,KAAK,CAACwB,MAAM,CAAC;IACtC,IAAI,CAACsB,OAAO,CAAC1H,KAAK,CAAC;IACnB,IAAI4E,KAAK,CAACyI,QAAQ,IAAIzI,KAAK,CAACoB,WAAW,EAAE;MACvC,IAAI,CAACZ,YAAY,CAAC8J,MAAM,CAAC,WAAW,CAAC;MACrCtK,KAAK,CAACgB,MAAM,GAAG,KAAK;MACpB,IAAI,CAACiJ,gBAAgB,CAAC7O,KAAK,CAAC;MAC5B;IACF;IACA,IAAIsF,MAAM,CAACmJ,iBAAiB,IAAI,CAAC7J,KAAK,CAACwI,cAAc,EAAE;MACrD,IAAIxI,KAAK,CAAC2B,IAAI,EAAE;QACd,IAAI3B,KAAK,CAAC2B,IAAI,KAAKjB,MAAM,CAACmJ,iBAAiB,IAAInJ,MAAM,CAACmJ,iBAAiB,KAAK,IAAI,EAAE;UAChF7J,KAAK,CAACe,OAAO,GAAG,KAAK;UACrB,IAAI,CAAC0E,KAAK,CAAC,CAAC;UACZ;QACF,CAAC,MAAM;UACL,IAAI,CAACjF,YAAY,CAAC8J,MAAM,CAAC,kBAAkB,CAAC;UAC5C,IAAI,CAACL,gBAAgB,CAAC7O,KAAK,CAAC;UAC5B;QACF;MACF,CAAC,MAAM;QACL;MACF;IACF;IACA,IAAI,CAACoK,IAAI,CAAC,CAAC;EACb;EACA+E,SAASA,CAACnP,KAAK,EAAE;IACf,IAAI,CAAC2E,IAAI,CAAC0J,WAAW,CAACrO,KAAK,CAAC;IAC5B,IAAI;MACF,IAAI,IAAI,CAACsF,MAAM,CAACgJ,cAAc,IAAItO,KAAK,CAACS,MAAM,CAAC2O,iBAAiB,CAACpP,KAAK,CAACuC,SAAS,CAAC,EAAE;QACjF;QACAvC,KAAK,CAACS,MAAM,CAAC4O,qBAAqB,CAACrP,KAAK,CAACuC,SAAS,CAAC;MACrD;IACF,CAAC,CAAC,OAAON,OAAO,EAAE;MAChB,IAAIqJ,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,aAAa,EAAE;QAC1CG,OAAO,CAACC,IAAI,CAAC,iKAAiK,CAAC;MACjL;IACF;IACA,MAAMhH,KAAK,GAAG,IAAI,CAACA,KAAK;IACxB,MAAMU,MAAM,GAAG,IAAI,CAACA,MAAM;IAC1B,IAAI,CAACV,KAAK,CAACe,OAAO,IAAI,CAACf,KAAK,CAACsI,cAAc,EAAE;IAC7C,MAAM6B,EAAE,GAAGxM,SAAS,CAACvC,KAAK,CAAC;IAC3B,IAAI4E,KAAK,CAACqI,UAAU,KAAKpR,SAAS,IAAIkT,EAAE,KAAKnK,KAAK,CAACqI,UAAU,EAAE;IAC/D,IAAI,CAACrI,KAAK,CAACsI,cAAc,GAAG,KAAK;IACjC,IAAI,CAACa,SAAS,CAAC,CAAC;IAChB,IAAI,CAACrG,OAAO,CAAC1H,KAAK,CAAC;IACnB,MAAM,CAACoB,EAAE,EAAEE,EAAE,CAAC,GAAGsD,KAAK,CAACsB,SAAS;IAChCtB,KAAK,CAAC2I,GAAG,GAAGnM,EAAE,IAAIkE,MAAM,CAACgK,aAAa,IAAIhO,EAAE,IAAIgE,MAAM,CAACgK,aAAa;IACpE,IAAI1K,KAAK,CAAC2I,GAAG,IAAIjI,MAAM,CAACiK,UAAU,EAAE;MAClC3K,KAAK,CAACkB,MAAM,GAAG,IAAI;IACrB,CAAC,MAAM;MACL,MAAM,CAAC0J,GAAG,EAAEC,GAAG,CAAC,GAAG7K,KAAK,CAACwB,MAAM;MAC/B,MAAM,CAACsJ,GAAG,EAAEC,GAAG,CAAC,GAAG/K,KAAK,CAACqB,SAAS;MAClC,MAAM,CAAC2J,GAAG,EAAEC,GAAG,CAAC,GAAGvK,MAAM,CAACgI,KAAK,CAACxG,QAAQ;MACxC,MAAM,CAACgJ,EAAE,EAAEC,EAAE,CAAC,GAAGzK,MAAM,CAACgI,KAAK,CAAC5L,QAAQ;MACtC,MAAMsO,GAAG,GAAG1K,MAAM,CAACgI,KAAK,CAAC2C,QAAQ;MACjC,IAAIrL,KAAK,CAAC6B,WAAW,GAAGuJ,GAAG,EAAE;QAC3B,MAAME,GAAG,GAAGvO,IAAI,CAAC2G,GAAG,CAACkH,GAAG,GAAG5K,KAAK,CAAC8B,SAAS,CAAC;QAC3C,MAAMyJ,GAAG,GAAGxO,IAAI,CAAC2G,GAAG,CAACmH,GAAG,GAAG7K,KAAK,CAAC8B,SAAS,CAAC;QAC3C,IAAIwJ,GAAG,GAAGN,GAAG,IAAIjO,IAAI,CAAC2G,GAAG,CAACoH,GAAG,CAAC,GAAGI,EAAE,EAAElL,KAAK,CAAC0I,KAAK,CAAC,CAAC,CAAC,GAAG3L,IAAI,CAACoH,IAAI,CAACyG,GAAG,CAAC;QACpE,IAAIW,GAAG,GAAGN,GAAG,IAAIlO,IAAI,CAAC2G,GAAG,CAACqH,GAAG,CAAC,GAAGI,EAAE,EAAEnL,KAAK,CAAC0I,KAAK,CAAC,CAAC,CAAC,GAAG3L,IAAI,CAACoH,IAAI,CAAC0G,GAAG,CAAC;MACtE;IACF;IACA,IAAI,CAACrF,IAAI,CAAC,CAAC;EACb;EACAgG,YAAYA,CAACpQ,KAAK,EAAE;IAClB,IAAI,CAAC,IAAI,CAAC4E,KAAK,CAAC2I,GAAG,IAAIvN,KAAK,CAACqQ,MAAM,GAAG,CAAC,EAAE;MACvCrQ,KAAK,CAAC4H,cAAc,CAAC,CAAC;MACtB5H,KAAK,CAACsQ,eAAe,CAAC,CAAC;IACzB;EACF;EACA9B,YAAYA,CAACxO,KAAK,EAAE;IAClB,MAAMsF,MAAM,GAAG,IAAI,CAACA,MAAM;IAC1B,MAAMrG,MAAM,GAAGqG,MAAM,CAACrG,MAAM;IAC5B,IAAIqM,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,aAAa,EAAE;MAC1C,IAAI;QACF,IAAIvM,MAAM,KAAK,SAAS,IAAIqG,MAAM,CAACiL,kBAAkB,KAAK1U,SAAS,EAAE;UACnE,MAAM6E,aAAa,GAAG,IAAI,IAAIV,KAAK,GAAGA,KAAK,CAACwQ,WAAW,CAAC9P,aAAa,GAAGV,KAAK,CAACU,aAAa;UAC3F,MAAM+P,KAAK,GAAGC,MAAM,CAACC,gBAAgB,CAACjQ,aAAa,CAAC;UACpD,IAAI+P,KAAK,CAACG,WAAW,KAAK,MAAM,EAAE;YAChCjF,OAAO,CAACC,IAAI,CAAC,uaAAua,EAAElL,aAAa,CAAC;UACtc;QACF;MACF,CAAC,CAAC,OAAOmQ,QAAQ,EAAE,CAAC;IACtB;IACA,IAAIvL,MAAM,CAACwL,WAAW,EAAE;MACtB9Q,KAAK,CAACU,aAAa,CAACqQ,kBAAkB,CAAC,CAAC;IAC1C;IACA,IAAI,CAACzL,MAAM,CAACgJ,cAAc,EAAE;MAC1B,IAAI,CAACpJ,UAAU,CAAC0F,GAAG,CAAC,IAAI,CAACrF,YAAY,CAACmL,MAAM,EAAEzR,MAAM,EAAE,QAAQ,EAAE,IAAI,CAAC6P,WAAW,CAACpB,IAAI,CAAC,IAAI,CAAC,CAAC;MAC5F,IAAI,CAACxI,UAAU,CAAC0F,GAAG,CAAC,IAAI,CAACrF,YAAY,CAACmL,MAAM,EAAEzR,MAAM,EAAE,KAAK,EAAE,IAAI,CAACkQ,SAAS,CAACzB,IAAI,CAAC,IAAI,CAAC,CAAC;MACvF,IAAI,CAACxI,UAAU,CAAC0F,GAAG,CAAC,IAAI,CAACrF,YAAY,CAACmL,MAAM,EAAEzR,MAAM,EAAE,QAAQ,EAAE,IAAI,CAACkQ,SAAS,CAACzB,IAAI,CAAC,IAAI,CAAC,CAAC;IAC5F;EACF;EACAM,YAAYA,CAAA,EAAG;IACb,IAAI,IAAI,CAAC1I,MAAM,CAACwL,WAAW,IAAI7I,QAAQ,CAACC,kBAAkB,KAAK,IAAI,CAACtD,KAAK,CAAClE,aAAa,EAAE;MACvFuH,QAAQ,CAAC+I,eAAe,CAAC,CAAC;IAC5B;EACF;EACAC,aAAaA,CAACjR,KAAK,EAAE;IACnB,IAAI,IAAI,CAAC4E,KAAK,CAACwI,cAAc,IAAIpN,KAAK,CAAC6H,UAAU,EAAE;MACjD7H,KAAK,CAAC4H,cAAc,CAAC,CAAC;IACxB;EACF;EACA8G,qBAAqBA,CAAC1O,KAAK,EAAE;IAC3B,IAAI,CAAC4E,KAAK,CAACwI,cAAc,GAAG,KAAK;IACjC8D,YAAY,CAAClR,KAAK,CAAC;IACnB,MAAMkP,MAAM,GAAG,IAAI,CAAChK,UAAU,CAAC0F,GAAG,CAAC,IAAI,CAACrF,YAAY,CAACmL,MAAM,EAAE,OAAO,EAAE,QAAQ,EAAE,IAAI,CAACO,aAAa,CAACvD,IAAI,CAAC,IAAI,CAAC,EAAE;MAC7GhO,OAAO,EAAE;IACX,CAAC,CAAC;IACF,IAAI,CAACwF,UAAU,CAAC0F,GAAG,CAAC,IAAI,CAACrF,YAAY,CAACmL,MAAM,EAAE,OAAO,EAAE,KAAK,EAAExB,MAAM,CAAC;IACrE,IAAI,CAAChK,UAAU,CAAC0F,GAAG,CAAC,IAAI,CAACrF,YAAY,CAACmL,MAAM,EAAE,OAAO,EAAE,QAAQ,EAAExB,MAAM,CAAC;IACxE,IAAI,CAAC9J,YAAY,CAACwF,GAAG,CAAC,kBAAkB,EAAE,IAAI,CAACiE,gBAAgB,CAACnB,IAAI,CAAC,IAAI,CAAC,EAAE,IAAI,CAACpI,MAAM,CAACiL,kBAAkB,EAAEvQ,KAAK,CAAC;EACpH;EACA4O,iBAAiBA,CAAC5O,KAAK,EAAE;IACvB,IAAI,CAAC4E,KAAK,CAACyI,QAAQ,GAAG,IAAI;IAC1B,IAAI,CAACjI,YAAY,CAACwF,GAAG,CAAC,WAAW,EAAE,MAAM;MACvC,IAAI,CAAChG,KAAK,CAACmB,KAAK,GAAG,CAAC,CAAC,EAAE,CAAC,CAAC;MACzB,IAAI,CAAC8I,gBAAgB,CAAC7O,KAAK,CAAC;IAC9B,CAAC,EAAE,IAAI,CAACsF,MAAM,CAACqJ,KAAK,CAAC;EACvB;EACAwC,OAAOA,CAACnR,KAAK,EAAE;IACb,MAAMoR,OAAO,GAAG3E,cAAc,CAACzM,KAAK,CAAC3D,GAAG,CAAC;IACzC,IAAI+U,OAAO,EAAE;MACX,MAAMxM,KAAK,GAAG,IAAI,CAACA,KAAK;MACxB,MAAMgI,MAAM,GAAG5M,KAAK,CAAC0D,QAAQ,GAAG,EAAE,GAAG1D,KAAK,CAAC2D,MAAM,GAAG,GAAG,GAAG,CAAC;MAC3D,IAAI,CAAC1F,KAAK,CAAC+B,KAAK,CAAC;MACjB4E,KAAK,CAACwB,MAAM,GAAGgL,OAAO,CAAC,IAAI,CAAC9L,MAAM,CAAC+L,oBAAoB,EAAEzE,MAAM,CAAC;MAChEhI,KAAK,CAACuI,eAAe,GAAG,IAAI;MAC5B/R,CAAC,CAACmN,KAAK,CAAC3D,KAAK,CAACqB,SAAS,EAAErB,KAAK,CAACwB,MAAM,CAAC;MACtC,IAAI,CAACsB,OAAO,CAAC1H,KAAK,CAAC;MACnB,IAAI,CAACoK,IAAI,CAAC,CAAC;IACb;EACF;EACAkH,KAAKA,CAACtR,KAAK,EAAE;IACX,IAAI,EAAEA,KAAK,CAAC3D,GAAG,IAAIoQ,cAAc,CAAC,EAAE;IACpC,IAAI,CAAC7H,KAAK,CAACuI,eAAe,GAAG,KAAK;IAClC,IAAI,CAACY,SAAS,CAAC,CAAC;IAChB,IAAI,CAACrG,OAAO,CAAC1H,KAAK,CAAC;IACnB,IAAI,CAACoK,IAAI,CAAC,CAAC;EACb;EACAsD,IAAIA,CAAC6D,YAAY,EAAE;IACjB,MAAMtS,MAAM,GAAG,IAAI,CAACqG,MAAM,CAACrG,MAAM;IACjCsS,YAAY,CAACtS,MAAM,EAAE,OAAO,EAAE,IAAI,CAACgP,WAAW,CAACP,IAAI,CAAC,IAAI,CAAC,CAAC;IAC1D,IAAI,IAAI,CAACpI,MAAM,CAACgJ,cAAc,EAAE;MAC9BiD,YAAY,CAACtS,MAAM,EAAE,QAAQ,EAAE,IAAI,CAAC6P,WAAW,CAACpB,IAAI,CAAC,IAAI,CAAC,CAAC;MAC3D6D,YAAY,CAACtS,MAAM,EAAE,KAAK,EAAE,IAAI,CAACkQ,SAAS,CAACzB,IAAI,CAAC,IAAI,CAAC,CAAC;MACtD6D,YAAY,CAACtS,MAAM,EAAE,QAAQ,EAAE,IAAI,CAACkQ,SAAS,CAACzB,IAAI,CAAC,IAAI,CAAC,CAAC;MACzD6D,YAAY,CAAC,oBAAoB,EAAE,EAAE,EAAE,IAAI,CAACpC,SAAS,CAACzB,IAAI,CAAC,IAAI,CAAC,CAAC;IACnE;IACA,IAAI,IAAI,CAACpI,MAAM,CAACpI,IAAI,EAAE;MACpBqU,YAAY,CAAC,KAAK,EAAE,MAAM,EAAE,IAAI,CAACJ,OAAO,CAACzD,IAAI,CAAC,IAAI,CAAC,CAAC;MACpD6D,YAAY,CAAC,KAAK,EAAE,IAAI,EAAE,IAAI,CAACD,KAAK,CAAC5D,IAAI,CAAC,IAAI,CAAC,CAAC;IAClD;IACA,IAAI,IAAI,CAACpI,MAAM,CAACiK,UAAU,EAAE;MAC1BgC,YAAY,CAAC,OAAO,EAAE,EAAE,EAAE,IAAI,CAACnB,YAAY,CAAC1C,IAAI,CAAC,IAAI,CAAC,EAAE;QACtD7O,OAAO,EAAE,IAAI;QACba,OAAO,EAAE;MACX,CAAC,CAAC;IACJ;EACF;AACF;AACA,SAASwR,YAAYA,CAAClR,KAAK,EAAE;EAC3B,SAAS,IAAIA,KAAK,IAAI,OAAOA,KAAK,CAACwR,OAAO,KAAK,UAAU,IAAIxR,KAAK,CAACwR,OAAO,CAAC,CAAC;AAC9E;AAEA,MAAMC,SAAS,GAAG,OAAOf,MAAM,KAAK,WAAW,IAAIA,MAAM,CAACzI,QAAQ,IAAIyI,MAAM,CAACzI,QAAQ,CAACyJ,aAAa;AACnG,SAASC,mBAAmBA,CAAA,EAAG;EAC7B,OAAOF,SAAS,IAAI,cAAc,IAAIf,MAAM;AAC9C;AACA,SAASkB,aAAaA,CAAA,EAAG;EACvB,OAAOD,mBAAmB,CAAC,CAAC,IAAIF,SAAS,IAAIf,MAAM,CAACmB,SAAS,CAACC,cAAc,GAAG,CAAC;AAClF;AACA,SAASC,qBAAqBA,CAAA,EAAG;EAC/B,OAAON,SAAS,IAAI,eAAe,IAAIf,MAAM;AAC/C;AACA,SAASsB,mBAAmBA,CAAA,EAAG;EAC7B,OAAOP,SAAS,IAAI,iBAAiB,IAAIf,MAAM,CAACzI,QAAQ;AAC1D;AACA,SAASgK,qBAAqBA,CAAA,EAAG;EAC/B,IAAI;IACF,OAAO,aAAa,IAAIC,YAAY;EACtC,CAAC,CAAC,OAAOnV,CAAC,EAAE;IACV,OAAO,KAAK;EACd;AACF;AACA,MAAMoV,OAAO,GAAG;EACdV,SAAS;EACTnT,OAAO,EAAE2T,qBAAqB,CAAC,CAAC;EAChC5T,KAAK,EAAEsT,mBAAmB,CAAC,CAAC;EAC5BS,WAAW,EAAER,aAAa,CAAC,CAAC;EAC5B5T,OAAO,EAAE+T,qBAAqB,CAAC,CAAC;EAChCjB,WAAW,EAAEkB,mBAAmB,CAAC;AACnC,CAAC;AAED,MAAMK,4BAA4B,GAAG,GAAG;AACxC,MAAMC,kBAAkB,GAAG,GAAG;AAC9B,MAAMC,sBAAsB,GAAG,GAAG;AAClC,MAAMC,sBAAsB,GAAG,EAAE;AACjC,MAAMC,sBAAsB,GAAG,GAAG;AAClC,MAAMC,6BAA6B,GAAG,EAAE;AACxC,MAAMC,2BAA2B,GAAG;EAClCvU,KAAK,EAAE,CAAC;EACRC,KAAK,EAAE,CAAC;EACRuU,GAAG,EAAE;AACP,CAAC;AACD,MAAMC,kBAAkB,GAAGpV,cAAc,CAACA,cAAc,CAAC,CAAC,CAAC,EAAEwO,yBAAyB,CAAC,EAAE,CAAC,CAAC,EAAE;EAC3FhN,MAAMA,CAACiN,EAAE,EAAEd,EAAE,EAAE;IACbpN,OAAO,EAAE;MACPK,KAAK,GAAG,KAAK;MACbyU,IAAI,GAAG,KAAK;MACZ1U,KAAK,GAAG;IACV,CAAC,GAAG,CAAC;EACP,CAAC,EAAE;IACD,IAAI,CAAC0S,WAAW,GAAGgC,IAAI,IAAIX,OAAO,CAACrB,WAAW;IAC9C,IAAIqB,OAAO,CAAC9T,KAAK,IAAIA,KAAK,EAAE,OAAO,OAAO;IAC1C,IAAI,IAAI,CAACyS,WAAW,EAAE,OAAO,OAAO;IACpC,IAAIqB,OAAO,CAACnU,OAAO,IAAI,CAACI,KAAK,EAAE,OAAO,SAAS;IAC/C,IAAI+T,OAAO,CAAC9T,KAAK,EAAE,OAAO,OAAO;IACjC,OAAO,OAAO;EAChB,CAAC;EACDoQ,iBAAiBA,CAACjS,KAAK,EAAE4O,EAAE,EAAE;IAC3B6F;EACF,CAAC,EAAE;IACD,IAAI,CAACV,kBAAkB,GAAG,OAAOU,aAAa,KAAK,QAAQ,GAAGA,aAAa,GAAGA,aAAa,IAAIA,aAAa,KAAKpV,SAAS,IAAIW,KAAK,GAAG6V,4BAA4B,GAAGxW,SAAS;IAC9K,IAAI,CAACsW,OAAO,CAACC,WAAW,IAAInB,aAAa,KAAK,KAAK,EAAE,OAAOpV,SAAS;IACrE,OAAOW,KAAK,GAAGA,KAAK,GAAGyU,aAAa,KAAKpV,SAAS,GAAG,GAAG,GAAGA,SAAS;EACtE,CAAC;EACDyS,cAAcA,CAACpC,EAAE,EAAEd,EAAE,EAAE;IACrBpN,OAAO,EAAE;MACPa,OAAO,GAAG,IAAI;MACd4E,OAAO,GAAG,CAAC;MACXvG,IAAI,GAAG;IACT,CAAC,GAAG,CAAC;EACP,CAAC,EAAE;IACD,IAAI,CAACiR,cAAc,GAAG1K,OAAO;IAC7B,IAAI,CAACvG,IAAI,GAAGA,IAAI;IAChB,OAAO,CAAC,IAAI,CAAC4T,WAAW,IAAI,IAAI,CAAC7R,MAAM,KAAK,SAAS,IAAIJ,OAAO;EAClE,CAAC;EACDgK,SAASA,CAACrM,KAAK,EAAE4O,EAAE,EAAE;IACnBmE,UAAU,GAAG,KAAK;IAClBD,aAAa,GAAG,CAAC;IACjB/I,IAAI,GAAG1K;EACT,CAAC,EAAE;IACD,MAAMgN,SAAS,GAAGzN,CAAC,CAACiQ,QAAQ,CAAC7O,KAAK,EAAE+S,UAAU,GAAGD,aAAa,GAAG/I,IAAI,GAAG,CAAC,GAAG,CAAC,CAAC;IAC9E,IAAI,CAACgJ,UAAU,GAAGA,UAAU;IAC5B,IAAI,CAACD,aAAa,GAAGA,aAAa;IAClC,OAAOzG,SAAS;EAClB,CAAC;EACDyE,KAAKA,CAAC;IACJxG,QAAQ,GAAGyL,sBAAsB;IACjC7Q,QAAQ,GAAG8Q,sBAAsB;IACjCvC,QAAQ,GAAGwC;EACb,CAAC,GAAG,CAAC,CAAC,EAAE;IACN,OAAO;MACL3L,QAAQ,EAAE,IAAI,CAACS,SAAS,CAACnM,CAAC,CAACiQ,QAAQ,CAACvE,QAAQ,CAAC,CAAC;MAC9CpF,QAAQ,EAAE,IAAI,CAAC6F,SAAS,CAACnM,CAAC,CAACiQ,QAAQ,CAAC3J,QAAQ,CAAC,CAAC;MAC9CuO;IACF,CAAC;EACH,CAAC;EACDtB,KAAKA,CAACnS,KAAK,GAAG,CAAC,EAAE;IACf,QAAQA,KAAK;MACX,KAAK,IAAI;QACP,OAAO8V,kBAAkB;MAC3B,KAAK,KAAK;QACR,OAAO,CAAC;MACV;QACE,OAAO9V,KAAK;IAChB;EACF,CAAC;EACDqO,aAAaA,CAACrO,KAAK,EAAE;IACnB,IAAI,CAACA,KAAK,EAAE,OAAOmW,2BAA2B;IAC9C,OAAOlV,cAAc,CAACA,cAAc,CAAC,CAAC,CAAC,EAAEkV,2BAA2B,CAAC,EAAEnW,KAAK,CAAC;EAC/E,CAAC;EACD6U,oBAAoBA,CAAC7U,KAAK,GAAGkW,6BAA6B,EAAE;IAC1D,OAAOlW,KAAK;EACd;AACF,CAAC,CAAC;AACF,IAAI8O,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,aAAa,EAAE;EAC1C/O,MAAM,CAACqH,MAAM,CAAC+O,kBAAkB,EAAE;IAChCE,QAAQA,CAACvW,KAAK,EAAE;MACd,IAAIA,KAAK,KAAKX,SAAS,EAAE;QACvB,MAAMiQ,KAAK,CAAC,2HAA2H,CAAC;MAC1I;MACA,OAAOC,GAAG;IACZ,CAAC;IACDiH,iCAAiCA,CAACxW,KAAK,EAAE;MACvC,IAAIA,KAAK,KAAKX,SAAS,EAAE;QACvB,MAAMiQ,KAAK,CAAC,qGAAqG,CAAC;MACpH;MACA,OAAOC,GAAG;IACZ,CAAC;IACDkH,aAAaA,CAACzW,KAAK,EAAE;MACnB,IAAIA,KAAK,KAAKX,SAAS,EAAE;QACvB,MAAMiQ,KAAK,CAAC,iIAAiI,CAAC;MAChJ;MACA,OAAOC,GAAG;IACZ,CAAC;IACDmH,aAAaA,CAAC1W,KAAK,EAAE;MACnB,IAAIA,KAAK,KAAKX,SAAS,EAAE;QACvB,MAAMiQ,KAAK,CAAC,gIAAgI,CAAC;MAC/I;MACA,OAAOC,GAAG;IACZ,CAAC;IACDoH,aAAaA,CAAC3W,KAAK,EAAE;MACnB,IAAIA,KAAK,KAAKX,SAAS,EAAE;QACvB,MAAMiQ,KAAK,CAAC,iIAAiI,CAAC;MAChJ;MACA,OAAOC,GAAG;IACZ;EACF,CAAC,CAAC;AACJ;AAEA,SAASqH,kCAAkCA,CAACxO,KAAK,EAAE;EACjD,MAAM,CAAC8E,EAAE,EAAEC,EAAE,CAAC,GAAG/E,KAAK,CAACgC,QAAQ;EAC/B,MAAM,CAACxF,EAAE,EAAEE,EAAE,CAAC,GAAGsD,KAAK,CAACwB,MAAM;EAC7B,MAAM,CAACiN,IAAI,EAAEC,IAAI,CAAC,GAAG1O,KAAK,CAACuB,UAAU;EACrC,IAAIuD,EAAE,GAAG,CAAC,IAAItI,EAAE,GAAG,CAAC,IAAIiS,IAAI,GAAG,CAAC,IAAI3J,EAAE,GAAG,CAAC,IAAItI,EAAE,GAAG,CAAC,IAAIiS,IAAI,GAAG,CAAC,EAAE;IAChEzO,KAAK,CAACqB,SAAS,CAAC,CAAC,CAAC,GAAGrB,KAAK,CAACiC,cAAc,CAAC,CAAC,CAAC;EAC9C;EACA,IAAI8C,EAAE,GAAG,CAAC,IAAIrI,EAAE,GAAG,CAAC,IAAIgS,IAAI,GAAG,CAAC,IAAI3J,EAAE,GAAG,CAAC,IAAIrI,EAAE,GAAG,CAAC,IAAIgS,IAAI,GAAG,CAAC,EAAE;IAChE1O,KAAK,CAACqB,SAAS,CAAC,CAAC,CAAC,GAAGrB,KAAK,CAACiC,cAAc,CAAC,CAAC,CAAC;EAC9C;AACF;AAEA,MAAM0M,4BAA4B,GAAG,EAAE;AACvC,MAAMC,iBAAiB,GAAG,GAAG;AAC7B,MAAMC,WAAW,SAAShP,MAAM,CAAC;EAC/BC,WAAWA,CAAC,GAAGV,IAAI,EAAE;IACnB,KAAK,CAAC,GAAGA,IAAI,CAAC;IACd1H,eAAe,CAAC,IAAI,EAAE,QAAQ,EAAE,UAAU,CAAC;IAC3CA,eAAe,CAAC,IAAI,EAAE,UAAU,EAAE,IAAI,CAAC;EACzC;EACAyI,IAAIA,CAAA,EAAG;IACL,IAAI,CAACH,KAAK,CAACuC,MAAM,GAAG,CAAC,CAAC,EAAE,CAAC,CAAC;IAC1B,IAAI,CAACvC,KAAK,CAACsC,UAAU,GAAG,CAAC,CAAC,EAAE,CAAC,CAAC;IAC9B,IAAI,CAACtC,KAAK,CAAC8O,cAAc,GAAG,IAAIC,GAAG,CAAC,CAAC;EACvC;EACA3O,KAAKA,CAAA,EAAG;IACN,KAAK,CAACA,KAAK,CAAC,CAAC;IACb,MAAMJ,KAAK,GAAG,IAAI,CAACA,KAAK;IACxBA,KAAK,CAACgP,SAAS,GAAG,EAAE;IACpBhP,KAAK,CAAC4I,QAAQ,GAAG,KAAK;IACtB5I,KAAK,CAAC6I,MAAM,GAAG,IAAI,CAACA,MAAM,CAACC,IAAI,CAAC,IAAI,CAAC;IACrC9I,KAAK,CAACiP,KAAK,GAAG,CAAC;EACjB;EACApK,aAAaA,CAAA,EAAG;IACd,MAAM;MACJ5I,IAAI;MACJkG,QAAQ;MACRG;IACF,CAAC,GAAG,IAAI,CAACtC,KAAK;IACd,IAAI/D,IAAI,KAAK,OAAO,EAAE;MACpB,IAAI,CAAC+D,KAAK,CAACuC,MAAM,GAAG/L,CAAC,CAACwP,GAAG,CAAC7D,QAAQ,EAAEG,UAAU,CAAC;IACjD,CAAC,MAAM;MACL,IAAI,CAACtC,KAAK,CAACuC,MAAM,GAAG,CAAC,CAAC,CAAC,GAAGJ,QAAQ,CAAC,CAAC,CAAC,IAAIG,UAAU,CAAC,CAAC,CAAC,EAAEH,QAAQ,CAAC,CAAC,CAAC,GAAGG,UAAU,CAAC,CAAC,CAAC,CAAC;IACtF;EACF;EACAgD,eAAeA,CAAA,EAAG;IAChB,MAAM;MACJ/C,MAAM;MACND;IACF,CAAC,GAAG,IAAI,CAACtC,KAAK;IACd,IAAI,CAACA,KAAK,CAACmC,QAAQ,GAAG,CAACI,MAAM,CAAC,CAAC,CAAC,GAAGD,UAAU,CAAC,CAAC,CAAC,EAAEC,MAAM,CAAC,CAAC,CAAC,GAAGD,UAAU,CAAC,CAAC,CAAC,CAAC;EAC9E;EACAsB,UAAUA,CAAA,EAAG;IACX,MAAM5D,KAAK,GAAG,IAAI,CAACA,KAAK;IACxB,MAAM,CAAC6D,GAAG,EAAEC,GAAG,CAAC,GAAG9D,KAAK,CAACqB,SAAS;IAClC,IAAI,CAACrB,KAAK,CAAC2B,IAAI,EAAE;MACf,MAAMuN,sBAAsB,GAAGnS,IAAI,CAAC2G,GAAG,CAACG,GAAG,CAAC,GAAG8K,4BAA4B,GAAG5R,IAAI,CAAC2G,GAAG,CAACI,GAAG,CAAC;MAC3F,IAAIoL,sBAAsB,GAAG,CAAC,EAAElP,KAAK,CAAC2B,IAAI,GAAG,OAAO,CAAC,KAAK,IAAIuN,sBAAsB,GAAG,CAAC,EAAElP,KAAK,CAAC2B,IAAI,GAAG,OAAO;IAChH;EACF;EACA2C,cAAcA,CAACnF,CAAC,EAAE;IAChB,IAAI,IAAI,CAACuB,MAAM,CAACwF,aAAa,EAAE;MAC7B,IAAI,IAAI,CAAClG,KAAK,CAAC2B,IAAI,KAAK,OAAO,EAAExC,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,KAAK,IAAI,IAAI,CAACa,KAAK,CAAC2B,IAAI,KAAK,OAAO,EAAExC,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC;IAC1F;EACF;EACA0J,MAAMA,CAAA,EAAG;IACP,MAAM7I,KAAK,GAAG,IAAI,CAACA,KAAK;IACxB,IAAIA,KAAK,CAAC4I,QAAQ,EAAE;IACpBM,UAAU,CAAC,MAAM;MACflJ,KAAK,CAAC4I,QAAQ,GAAG,IAAI;MACrB5I,KAAK,CAACe,OAAO,GAAG,KAAK;MACrB,IAAI,CAAC+B,OAAO,CAAC,CAAC;MACd,IAAI,CAAC0C,IAAI,CAAC,CAAC;IACb,CAAC,EAAE,CAAC,CAAC;EACP;EACA2J,UAAUA,CAAC/T,KAAK,EAAE;IAChB,IAAI,CAAC2E,IAAI,CAAC0J,WAAW,CAACrO,KAAK,CAAC;IAC5B,MAAM4E,KAAK,GAAG,IAAI,CAACA,KAAK;IACxB,MAAMoP,YAAY,GAAG,IAAI,CAACrP,IAAI,CAACzC,QAAQ;IACvC,IAAI0C,KAAK,CAACe,OAAO,EAAE;MACjB,IAAIf,KAAK,CAACgP,SAAS,CAACK,KAAK,CAAClF,EAAE,IAAIiF,YAAY,CAACE,GAAG,CAACnF,EAAE,CAAC,CAAC,EAAE;IACzD;IACA,IAAIiF,YAAY,CAACjM,IAAI,GAAG,CAAC,EAAE;IAC3B,IAAI,CAAC9J,KAAK,CAAC+B,KAAK,CAAC;IACjB4E,KAAK,CAACgP,SAAS,GAAGxT,KAAK,CAACC,IAAI,CAAC2T,YAAY,CAAC,CAACtV,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC;IACtD,MAAM8E,OAAO,GAAGnB,kBAAkB,CAACrC,KAAK,EAAE4E,KAAK,CAACgP,SAAS,CAAC;IAC1D,IAAI,CAACpQ,OAAO,EAAE;IACd,IAAI,CAAC2Q,UAAU,CAACnU,KAAK,EAAEwD,OAAO,CAAC;EACjC;EACA4Q,YAAYA,CAACpU,KAAK,EAAE;IAClB,IAAIA,KAAK,CAACyD,OAAO,IAAI,IAAI,IAAIzD,KAAK,CAACyD,OAAO,GAAG,CAAC,KAAK,CAAC,EAAE;IACtD,IAAI,CAACkB,IAAI,CAAC0J,WAAW,CAACrO,KAAK,CAAC;IAC5BA,KAAK,CAACS,MAAM,CAAC8N,iBAAiB,CAACvO,KAAK,CAACuC,SAAS,CAAC;IAC/C,MAAMqC,KAAK,GAAG,IAAI,CAACA,KAAK;IACxB,MAAM8O,cAAc,GAAG9O,KAAK,CAAC8O,cAAc;IAC3C,MAAMW,cAAc,GAAG,IAAI,CAAC1P,IAAI,CAACmD,UAAU;IAC3C,IAAIlD,KAAK,CAACe,OAAO,EAAE;MACjB,IAAIvF,KAAK,CAACC,IAAI,CAACqT,cAAc,CAACxW,IAAI,CAAC,CAAC,CAAC,CAAC+W,KAAK,CAAClF,EAAE,IAAIsF,cAAc,CAACH,GAAG,CAACnF,EAAE,CAAC,CAAC,EAAE;IAC7E;IACA,IAAI2E,cAAc,CAAC3L,IAAI,GAAG,CAAC,EAAE;MAC3B2L,cAAc,CAACY,GAAG,CAACtU,KAAK,CAACuC,SAAS,EAAEvC,KAAK,CAAC;IAC5C;IACA,IAAI4E,KAAK,CAAC8O,cAAc,CAAC3L,IAAI,GAAG,CAAC,EAAE;IACnC,IAAI,CAAC9J,KAAK,CAAC+B,KAAK,CAAC;IACjB,MAAMwD,OAAO,GAAGvC,aAAa,CAAC,GAAGb,KAAK,CAACC,IAAI,CAACqT,cAAc,CAACrM,MAAM,CAAC,CAAC,CAAC,CAAC;IACrE,IAAI,CAAC7D,OAAO,EAAE;IACd,IAAI,CAAC2Q,UAAU,CAACnU,KAAK,EAAEwD,OAAO,CAAC;EACjC;EACA2Q,UAAUA,CAACnU,KAAK,EAAEwD,OAAO,EAAE;IACzB,MAAMoB,KAAK,GAAG,IAAI,CAACA,KAAK;IACxBA,KAAK,CAAC5C,MAAM,GAAGwB,OAAO,CAACxB,MAAM;IAC7B,IAAI,CAAC6C,aAAa,CAAC,CAACrB,OAAO,CAAC9B,QAAQ,EAAE8B,OAAO,CAAC3B,KAAK,CAAC,CAAC;IACrD,IAAI,CAACiD,cAAc,CAAC,CAAC;IACrB,IAAI,CAAC4C,OAAO,CAAC1H,KAAK,CAAC;IACnB,IAAI,CAACoK,IAAI,CAAC,CAAC;EACb;EACAmK,SAASA,CAACvU,KAAK,EAAE;IACf,IAAI,CAAC,IAAI,CAAC4E,KAAK,CAACe,OAAO,EAAE;IACzB,MAAMnC,OAAO,GAAGnB,kBAAkB,CAACrC,KAAK,EAAE,IAAI,CAAC4E,KAAK,CAACgP,SAAS,CAAC;IAC/D,IAAI,CAACpQ,OAAO,EAAE;IACd,IAAI,CAACgR,SAAS,CAACxU,KAAK,EAAEwD,OAAO,CAAC;EAChC;EACAsL,WAAWA,CAAC9O,KAAK,EAAE;IACjB,MAAM0T,cAAc,GAAG,IAAI,CAAC9O,KAAK,CAAC8O,cAAc;IAChD,IAAIA,cAAc,CAACQ,GAAG,CAAClU,KAAK,CAACuC,SAAS,CAAC,EAAE;MACvCmR,cAAc,CAACY,GAAG,CAACtU,KAAK,CAACuC,SAAS,EAAEvC,KAAK,CAAC;IAC5C;IACA,IAAI,CAAC,IAAI,CAAC4E,KAAK,CAACe,OAAO,EAAE;IACzB,MAAMnC,OAAO,GAAGvC,aAAa,CAAC,GAAGb,KAAK,CAACC,IAAI,CAACqT,cAAc,CAACrM,MAAM,CAAC,CAAC,CAAC,CAAC;IACrE,IAAI,CAAC7D,OAAO,EAAE;IACd,IAAI,CAACgR,SAAS,CAACxU,KAAK,EAAEwD,OAAO,CAAC;EAChC;EACAgR,SAASA,CAACxU,KAAK,EAAEwD,OAAO,EAAE;IACxB,MAAMoB,KAAK,GAAG,IAAI,CAACA,KAAK;IACxB,MAAM6P,MAAM,GAAG7P,KAAK,CAAC0C,OAAO,CAAC,CAAC,CAAC;IAC/B,MAAMoN,OAAO,GAAGlR,OAAO,CAAC3B,KAAK,GAAG4S,MAAM;IACtC,IAAIE,WAAW,GAAG,CAAC;IACnB,IAAIhT,IAAI,CAAC2G,GAAG,CAACoM,OAAO,CAAC,GAAG,GAAG,EAAEC,WAAW,IAAIhT,IAAI,CAACoH,IAAI,CAAC2L,OAAO,CAAC;IAC9D,IAAI,CAAC7P,aAAa,CAAC,CAACrB,OAAO,CAAC9B,QAAQ,EAAE8B,OAAO,CAAC3B,KAAK,GAAG,GAAG,GAAG8S,WAAW,CAAC,CAAC;IACzE/P,KAAK,CAAC5C,MAAM,GAAGwB,OAAO,CAACxB,MAAM;IAC7B4C,KAAK,CAACiP,KAAK,GAAGc,WAAW;IACzB/P,KAAK,CAACqB,SAAS,GAAG,CAACrB,KAAK,CAAC0C,OAAO,CAAC,CAAC,CAAC,GAAG1C,KAAK,CAAC4C,QAAQ,CAAC,CAAC,CAAC,GAAG,CAAC,EAAE5C,KAAK,CAAC0C,OAAO,CAAC,CAAC,CAAC,GAAG1C,KAAK,CAAC4C,QAAQ,CAAC,CAAC,CAAC,CAAC;IAClG,IAAI,CAACE,OAAO,CAAC1H,KAAK,CAAC;IACnB,IAAI,CAACoK,IAAI,CAAC,CAAC;EACb;EACAwK,QAAQA,CAAC5U,KAAK,EAAE;IACd,IAAI,CAAC2E,IAAI,CAAC0J,WAAW,CAACrO,KAAK,CAAC;IAC5B,IAAI,CAAC,IAAI,CAAC4E,KAAK,CAACe,OAAO,EAAE;IACzB,IAAI,IAAI,CAACf,KAAK,CAACgP,SAAS,CAACiB,IAAI,CAAC9F,EAAE,IAAI,CAAC,IAAI,CAACpK,IAAI,CAACzC,QAAQ,CAACgS,GAAG,CAACnF,EAAE,CAAC,CAAC,EAAE;MAChE,IAAI,CAACnK,KAAK,CAACe,OAAO,GAAG,KAAK;MAC1B,IAAI,CAAC+B,OAAO,CAAC1H,KAAK,CAAC;MACnB,IAAI,CAACoK,IAAI,CAAC,CAAC;IACb;EACF;EACA0K,UAAUA,CAAC9U,KAAK,EAAE;IAChB,MAAM4E,KAAK,GAAG,IAAI,CAACA,KAAK;IACxB,IAAI,CAACD,IAAI,CAAC0J,WAAW,CAACrO,KAAK,CAAC;IAC5B,IAAI;MACFA,KAAK,CAACS,MAAM,CAAC4O,qBAAqB,CAACrP,KAAK,CAACuC,SAAS,CAAC;IACrD,CAAC,CAAC,OAAON,OAAO,EAAE,CAAC;IACnB,IAAI2C,KAAK,CAAC8O,cAAc,CAACQ,GAAG,CAAClU,KAAK,CAACuC,SAAS,CAAC,EAAE;MAC7CqC,KAAK,CAAC8O,cAAc,CAACqB,MAAM,CAAC/U,KAAK,CAACuC,SAAS,CAAC;IAC9C;IACA,IAAI,CAACqC,KAAK,CAACe,OAAO,EAAE;IACpB,IAAIf,KAAK,CAAC8O,cAAc,CAAC3L,IAAI,GAAG,CAAC,EAAE;MACjCnD,KAAK,CAACe,OAAO,GAAG,KAAK;MACrB,IAAI,CAAC+B,OAAO,CAAC1H,KAAK,CAAC;MACnB,IAAI,CAACoK,IAAI,CAAC,CAAC;IACb;EACF;EACA4K,YAAYA,CAAChV,KAAK,EAAE;IAClB,IAAIA,KAAK,CAAC6H,UAAU,EAAE7H,KAAK,CAAC4H,cAAc,CAAC,CAAC;IAC5C,MAAMhD,KAAK,GAAG,IAAI,CAACA,KAAK;IACxB,IAAIA,KAAK,CAACe,OAAO,EAAE;IACnB,IAAI,CAAC1H,KAAK,CAAC+B,KAAK,CAAC;IACjB,IAAI,CAAC6E,aAAa,CAAC,CAAC7E,KAAK,CAACiV,KAAK,EAAEjV,KAAK,CAACkV,QAAQ,CAAC,CAAC;IACjDtQ,KAAK,CAAC5C,MAAM,GAAG,CAAChC,KAAK,CAACqB,OAAO,EAAErB,KAAK,CAACuB,OAAO,CAAC;IAC7C,IAAI,CAACmG,OAAO,CAAC1H,KAAK,CAAC;IACnB,IAAI,CAACoK,IAAI,CAAC,CAAC;EACb;EACA+K,WAAWA,CAACnV,KAAK,EAAE;IACjB,IAAIA,KAAK,CAAC6H,UAAU,EAAE7H,KAAK,CAAC4H,cAAc,CAAC,CAAC;IAC5C,IAAI,CAAC,IAAI,CAAChD,KAAK,CAACe,OAAO,EAAE;IACzB,MAAMf,KAAK,GAAG,IAAI,CAACA,KAAK;IACxB,IAAI,CAACC,aAAa,CAAC,CAAC7E,KAAK,CAACiV,KAAK,EAAEjV,KAAK,CAACkV,QAAQ,CAAC,CAAC;IACjDtQ,KAAK,CAAC5C,MAAM,GAAG,CAAChC,KAAK,CAACqB,OAAO,EAAErB,KAAK,CAACuB,OAAO,CAAC;IAC7C,MAAM6T,iBAAiB,GAAGxQ,KAAK,CAACqB,SAAS;IACzCrB,KAAK,CAACqB,SAAS,GAAG,CAACjG,KAAK,CAACiV,KAAK,GAAG,CAAC,EAAEjV,KAAK,CAACkV,QAAQ,CAAC;IACnDtQ,KAAK,CAACwB,MAAM,GAAGhL,CAAC,CAAC6O,GAAG,CAACrF,KAAK,CAACqB,SAAS,EAAEmP,iBAAiB,CAAC;IACxD,IAAI,CAAC1N,OAAO,CAAC1H,KAAK,CAAC;IACnB,IAAI,CAACoK,IAAI,CAAC,CAAC;EACb;EACAiL,UAAUA,CAACrV,KAAK,EAAE;IAChB,IAAI,CAAC,IAAI,CAAC4E,KAAK,CAACe,OAAO,EAAE;IACzB,IAAI,CAACf,KAAK,CAACe,OAAO,GAAG,KAAK;IAC1B,IAAI,CAAC+B,OAAO,CAAC1H,KAAK,CAAC;IACnB,IAAI,CAACoK,IAAI,CAAC,CAAC;EACb;EACAkL,KAAKA,CAACtV,KAAK,EAAE;IACX,MAAMuV,WAAW,GAAG,IAAI,CAACjQ,MAAM,CAACiQ,WAAW;IAC3C,IAAIA,WAAW,KAAKnV,KAAK,CAAC8N,OAAO,CAACqH,WAAW,CAAC,GAAG,CAACA,WAAW,CAACC,IAAI,CAACC,CAAC,IAAIzV,KAAK,CAACyV,CAAC,CAAC,CAAC,GAAG,CAACzV,KAAK,CAACuV,WAAW,CAAC,CAAC,EAAE;IAC1G,IAAI,CAAC,IAAI,CAAC3Q,KAAK,CAACe,OAAO,EAAE,IAAI,CAAC+P,UAAU,CAAC1V,KAAK,CAAC,CAAC,KAAK,IAAI,CAAC2V,WAAW,CAAC3V,KAAK,CAAC;IAC5E,IAAI,CAACoF,YAAY,CAACwF,GAAG,CAAC,UAAU,EAAE,IAAI,CAACgL,QAAQ,CAAClI,IAAI,CAAC,IAAI,CAAC,CAAC;EAC7D;EACAgI,UAAUA,CAAC1V,KAAK,EAAE;IAChB,IAAI,CAAC/B,KAAK,CAAC+B,KAAK,CAAC;IACjB,IAAI,CAAC2V,WAAW,CAAC3V,KAAK,CAAC;EACzB;EACA2V,WAAWA,CAAC3V,KAAK,EAAE;IACjB,MAAM6V,KAAK,GAAI,IAAI,IAAI7V,KAAM;IAC7B,IAAI,CAAC6V,KAAK,EAAE;MACV,IAAI7V,KAAK,CAAC6H,UAAU,EAAE;QACpB7H,KAAK,CAAC4H,cAAc,CAAC,CAAC;MACxB;MACA,IAAI0D,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,aAAa,IAAI,CAACxL,KAAK,CAAC8V,gBAAgB,EAAE;QACrEnK,OAAO,CAACC,IAAI,CAAC,+IAA+I,CAAC;MAC/J;IACF;IACA,MAAMhH,KAAK,GAAG,IAAI,CAACA,KAAK;IACxBA,KAAK,CAACwB,MAAM,GAAG,CAAC,CAACxD,WAAW,CAAC5C,KAAK,CAAC,CAAC,CAAC,CAAC,GAAGwT,iBAAiB,GAAG5O,KAAK,CAACuC,MAAM,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC;IAChF/L,CAAC,CAACmN,KAAK,CAAC3D,KAAK,CAACqB,SAAS,EAAErB,KAAK,CAACwB,MAAM,CAAC;IACtCgN,kCAAkC,CAACxO,KAAK,CAAC;IACzC,IAAI,CAACA,KAAK,CAAC5C,MAAM,GAAG,CAAChC,KAAK,CAACqB,OAAO,EAAErB,KAAK,CAACuB,OAAO,CAAC;IAClD,IAAI,CAACmG,OAAO,CAAC1H,KAAK,CAAC;IACnB,IAAI,CAACoK,IAAI,CAAC,CAAC;EACb;EACAwL,QAAQA,CAAA,EAAG;IACT,IAAI,CAAC,IAAI,CAAChR,KAAK,CAACe,OAAO,EAAE;IACzB,IAAI,CAACf,KAAK,CAACe,OAAO,GAAG,KAAK;IAC1B,IAAI,CAAC+B,OAAO,CAAC,CAAC;IACd,IAAI,CAAC0C,IAAI,CAAC,CAAC;EACb;EACAsD,IAAIA,CAAC6D,YAAY,EAAE;IACjB,MAAMtS,MAAM,GAAG,IAAI,CAACqG,MAAM,CAACrG,MAAM;IACjC,IAAI,CAAC,CAACA,MAAM,EAAE;MACZsS,YAAY,CAACtS,MAAM,EAAE,OAAO,EAAE,IAAI,CAACA,MAAM,GAAG,OAAO,CAAC,CAACyO,IAAI,CAAC,IAAI,CAAC,CAAC;MAChE6D,YAAY,CAACtS,MAAM,EAAE,QAAQ,EAAE,IAAI,CAACA,MAAM,GAAG,MAAM,CAAC,CAACyO,IAAI,CAAC,IAAI,CAAC,CAAC;MAChE6D,YAAY,CAACtS,MAAM,EAAE,KAAK,EAAE,IAAI,CAACA,MAAM,GAAG,KAAK,CAAC,CAACyO,IAAI,CAAC,IAAI,CAAC,CAAC;MAC5D6D,YAAY,CAACtS,MAAM,EAAE,QAAQ,EAAE,IAAI,CAACA,MAAM,GAAG,KAAK,CAAC,CAACyO,IAAI,CAAC,IAAI,CAAC,CAAC;MAC/D6D,YAAY,CAAC,oBAAoB,EAAE,EAAE,EAAE,IAAI,CAACtS,MAAM,GAAG,KAAK,CAAC,CAACyO,IAAI,CAAC,IAAI,CAAC,CAAC;IACzE;IACA,IAAI,IAAI,CAACpI,MAAM,CAACyQ,YAAY,EAAE;MAC5BxE,YAAY,CAAC,OAAO,EAAE,EAAE,EAAE,IAAI,CAAC+D,KAAK,CAAC5H,IAAI,CAAC,IAAI,CAAC,EAAE;QAC/ChO,OAAO,EAAE;MACX,CAAC,CAAC;IACJ;EACF;AACF;AAEA,MAAMsW,mBAAmB,GAAGvY,cAAc,CAACA,cAAc,CAAC,CAAC,CAAC,EAAEwN,oBAAoB,CAAC,EAAE,CAAC,CAAC,EAAE;EACvFhM,MAAMA,CAACiN,EAAE,EAAEd,EAAE,EAAE;IACbnG,MAAM;IACNjH,OAAO,EAAE;MACPK,KAAK,GAAG;IACV,CAAC,GAAG,CAAC;EACP,CAAC,EAAE;IACD,MAAMkH,YAAY,GAAGN,MAAM;IAC3B,IAAIM,YAAY,CAAC9E,MAAM,IAAI,CAAC0R,OAAO,CAAC9T,KAAK,IAAI8T,OAAO,CAAC7T,OAAO,EAAE,OAAO,SAAS;IAC9E,IAAI6T,OAAO,CAAC9T,KAAK,IAAIA,KAAK,EAAE,OAAO,OAAO;IAC1C,IAAI8T,OAAO,CAACC,WAAW,EAAE;MACvB,IAAID,OAAO,CAACnU,OAAO,EAAE,OAAO,SAAS;MACrC,IAAImU,OAAO,CAAC9T,KAAK,EAAE,OAAO,OAAO;IACnC;EACF,CAAC;EACDkL,MAAMA,CAAC2C,EAAE,EAAEd,EAAE,EAAE;IACb6K,WAAW,GAAG,CAAC,CAAC;IAChBC,WAAW,GAAG,CAAC;EACjB,CAAC,EAAE;IACD,MAAMC,YAAY,GAAGvR,KAAK,IAAI;MAC5B,MAAMwR,CAAC,GAAG9R,aAAa,CAACvI,IAAI,CAACka,WAAW,EAAErR,KAAK,CAAC,EAAE;QAChDyR,GAAG,EAAE,CAAC/P,QAAQ;QACdgQ,GAAG,EAAEhQ;MACP,CAAC,CAAC;MACF,OAAO,CAAC8P,CAAC,CAACC,GAAG,EAAED,CAAC,CAACE,GAAG,CAAC;IACvB,CAAC;IACD,MAAMC,YAAY,GAAG3R,KAAK,IAAI;MAC5B,MAAM4R,CAAC,GAAGlS,aAAa,CAACvI,IAAI,CAACma,WAAW,EAAEtR,KAAK,CAAC,EAAE;QAChDyR,GAAG,EAAE,CAAC/P,QAAQ;QACdgQ,GAAG,EAAEhQ;MACP,CAAC,CAAC;MACF,OAAO,CAACkQ,CAAC,CAACH,GAAG,EAAEG,CAAC,CAACF,GAAG,CAAC;IACvB,CAAC;IACD,IAAI,OAAOL,WAAW,KAAK,UAAU,IAAI,OAAOC,WAAW,KAAK,UAAU,EAAE,OAAO,CAACC,YAAY,CAAC,CAAC,EAAEI,YAAY,CAAC,CAAC,CAAC;IACnH,OAAO3R,KAAK,IAAI,CAACuR,YAAY,CAACvR,KAAK,CAAC,EAAE2R,YAAY,CAAC3R,KAAK,CAAC,CAAC;EAC5D,CAAC;EACDiE,SAASA,CAACrM,KAAK,EAAE4O,EAAE,EAAE9F,MAAM,EAAE;IAC3B,IAAI,CAACwF,aAAa,GAAGxF,MAAM,CAACiB,IAAI,KAAK,MAAM;IAC3C,MAAMsC,SAAS,GAAGzN,CAAC,CAACiQ,QAAQ,CAAC7O,KAAK,EAAE,IAAI,CAACsO,aAAa,GAAG,CAAC,GAAG,EAAE,CAAC,CAAC,GAAG,CAAC,CAAC;IACtE,OAAOjC,SAAS;EAClB,CAAC;EACD0M,WAAWA,CAAC/Y,KAAK,EAAE;IACjB,IAAIA,KAAK,KAAKX,SAAS,EAAE,OAAO,SAAS;IACzC,OAAOW,KAAK;EACd,CAAC;EACDuZ,YAAYA,CAACvZ,KAAK,GAAG,IAAI,EAAE;IACzB,OAAOA,KAAK;EACd;AACF,CAAC,CAAC;AAEF,MAAMia,UAAU,SAAS9L,iBAAiB,CAAC;EACzCjG,WAAWA,CAAC,GAAGV,IAAI,EAAE;IACnB,KAAK,CAAC,GAAGA,IAAI,CAAC;IACd1H,eAAe,CAAC,IAAI,EAAE,QAAQ,EAAE,QAAQ,CAAC;EAC3C;EACAoa,IAAIA,CAAC1W,KAAK,EAAE;IACV,IAAI,IAAI,CAACsF,MAAM,CAACqR,SAAS,IAAI3W,KAAK,CAACE,WAAW,KAAK,OAAO,EAAE;IAC5D,IAAI,CAAC,IAAI,CAAC0E,KAAK,CAACe,OAAO,EAAE,IAAI,CAACiR,SAAS,CAAC5W,KAAK,CAAC,CAAC,KAAK,IAAI,CAAC6W,UAAU,CAAC7W,KAAK,CAAC;IAC1E,IAAI,CAACoF,YAAY,CAACwF,GAAG,CAAC,SAAS,EAAE,IAAI,CAACkM,OAAO,CAACpJ,IAAI,CAAC,IAAI,CAAC,CAAC;EAC3D;EACAkJ,SAASA,CAAC5W,KAAK,EAAE;IACf,IAAI,CAAC/B,KAAK,CAAC+B,KAAK,CAAC;IACjB,IAAI,CAAC6E,aAAa,CAACpC,aAAa,CAACzC,KAAK,CAAC,CAAC;IACxC,IAAI,CAAC0H,OAAO,CAAC1H,KAAK,CAAC;IACnB,IAAI,CAAC8E,cAAc,CAAC,CAAC;IACrB,IAAI,CAACsF,IAAI,CAAC,CAAC;EACb;EACAyM,UAAUA,CAAC7W,KAAK,EAAE;IAChB,IAAI,CAAC,IAAI,CAAC4E,KAAK,CAACe,OAAO,EAAE;IACzB,MAAM0B,MAAM,GAAG5E,aAAa,CAACzC,KAAK,CAAC;IACnC,MAAM4E,KAAK,GAAG,IAAI,CAACA,KAAK;IACxBA,KAAK,CAACwB,MAAM,GAAGhL,CAAC,CAAC6O,GAAG,CAAC5C,MAAM,EAAEzC,KAAK,CAAC0C,OAAO,CAAC;IAC3ClM,CAAC,CAACmN,KAAK,CAAC3D,KAAK,CAACqB,SAAS,EAAErB,KAAK,CAACwB,MAAM,CAAC;IACtC,IAAI,CAACvB,aAAa,CAACwC,MAAM,CAAC;IAC1B,IAAI,CAACK,OAAO,CAAC1H,KAAK,CAAC;IACnB,IAAI,CAACoK,IAAI,CAAC,CAAC;EACb;EACA0M,OAAOA,CAAC9W,KAAK,EAAE;IACb,IAAI,CAAC,IAAI,CAAC4E,KAAK,CAACe,OAAO,EAAE;IACzB,IAAI,CAACf,KAAK,CAACe,OAAO,GAAG,KAAK;IAC1B,IAAI,CAAC+B,OAAO,CAAC1H,KAAK,CAAC;IACnB,IAAI,CAACoK,IAAI,CAAC,CAAC;EACb;EACAsD,IAAIA,CAAC6D,YAAY,EAAE;IACjBA,YAAY,CAAC,SAAS,EAAE,QAAQ,EAAE,IAAI,CAACmF,IAAI,CAAChJ,IAAI,CAAC,IAAI,CAAC,CAAC;IACvD6D,YAAY,CAAC,SAAS,EAAE,OAAO,EAAE,IAAI,CAACuF,OAAO,CAACpJ,IAAI,CAAC,IAAI,CAAC,CAAC;EAC3D;AACF;AAEA,MAAMqJ,kBAAkB,GAAGtZ,cAAc,CAACA,cAAc,CAAC,CAAC,CAAC,EAAEwO,yBAAyB,CAAC,EAAE,CAAC,CAAC,EAAE;EAC3F0K,SAAS,EAAEA,CAACna,KAAK,GAAG,IAAI,KAAKA;AAC/B,CAAC,CAAC;AAEF,MAAMwa,YAAY,SAASrM,iBAAiB,CAAC;EAC3CjG,WAAWA,CAAC,GAAGV,IAAI,EAAE;IACnB,KAAK,CAAC,GAAGA,IAAI,CAAC;IACd1H,eAAe,CAAC,IAAI,EAAE,QAAQ,EAAE,WAAW,CAAC;EAC9C;EACA2a,MAAMA,CAACjX,KAAK,EAAE;IACZ,IAAI,CAAC,IAAI,CAAC4E,KAAK,CAACe,OAAO,EAAE,IAAI,CAAC1H,KAAK,CAAC+B,KAAK,CAAC;IAC1C,IAAI,CAACkX,YAAY,CAAClX,KAAK,CAAC;IACxB,IAAI,CAACoF,YAAY,CAACwF,GAAG,CAAC,WAAW,EAAE,IAAI,CAACuM,SAAS,CAACzJ,IAAI,CAAC,IAAI,CAAC,CAAC;EAC/D;EACAwJ,YAAYA,CAAClX,KAAK,EAAE;IAClB,IAAIA,KAAK,CAAC6H,UAAU,EAAE7H,KAAK,CAAC4H,cAAc,CAAC,CAAC;IAC5C,MAAMhD,KAAK,GAAG,IAAI,CAACA,KAAK;IACxB,MAAMyC,MAAM,GAAGrE,YAAY,CAAChD,KAAK,CAAC;IAClC4E,KAAK,CAACwB,MAAM,GAAGhL,CAAC,CAAC6O,GAAG,CAAC5C,MAAM,EAAEzC,KAAK,CAAC0C,OAAO,CAAC;IAC3ClM,CAAC,CAACmN,KAAK,CAAC3D,KAAK,CAACqB,SAAS,EAAErB,KAAK,CAACwB,MAAM,CAAC;IACtC,IAAI,CAACvB,aAAa,CAACwC,MAAM,CAAC;IAC1B,IAAI,CAACK,OAAO,CAAC1H,KAAK,CAAC;IACnB,IAAI,CAACoK,IAAI,CAAC,CAAC;EACb;EACA+M,SAASA,CAAA,EAAG;IACV,IAAI,CAAC,IAAI,CAACvS,KAAK,CAACe,OAAO,EAAE;IACzB,IAAI,CAACf,KAAK,CAACe,OAAO,GAAG,KAAK;IAC1B,IAAI,CAAC+B,OAAO,CAAC,CAAC;IACd,IAAI,CAAC0C,IAAI,CAAC,CAAC;EACb;EACAsD,IAAIA,CAAC6D,YAAY,EAAE;IACjBA,YAAY,CAAC,QAAQ,EAAE,EAAE,EAAE,IAAI,CAAC0F,MAAM,CAACvJ,IAAI,CAAC,IAAI,CAAC,CAAC;EACpD;AACF;AAEA,MAAM0J,oBAAoB,GAAGnL,yBAAyB;AAEtD,MAAMoL,WAAW,SAAS1M,iBAAiB,CAAC;EAC1CjG,WAAWA,CAAC,GAAGV,IAAI,EAAE;IACnB,KAAK,CAAC,GAAGA,IAAI,CAAC;IACd1H,eAAe,CAAC,IAAI,EAAE,QAAQ,EAAE,UAAU,CAAC;EAC7C;EACAgZ,KAAKA,CAACtV,KAAK,EAAE;IACX,IAAI,CAAC,IAAI,CAAC4E,KAAK,CAACe,OAAO,EAAE,IAAI,CAAC1H,KAAK,CAAC+B,KAAK,CAAC;IAC1C,IAAI,CAAC2V,WAAW,CAAC3V,KAAK,CAAC;IACvB,IAAI,CAACoF,YAAY,CAACwF,GAAG,CAAC,UAAU,EAAE,IAAI,CAACgL,QAAQ,CAAClI,IAAI,CAAC,IAAI,CAAC,CAAC;EAC7D;EACAiI,WAAWA,CAAC3V,KAAK,EAAE;IACjB,MAAM4E,KAAK,GAAG,IAAI,CAACA,KAAK;IACxBA,KAAK,CAACwB,MAAM,GAAGxD,WAAW,CAAC5C,KAAK,CAAC;IACjC5E,CAAC,CAACmN,KAAK,CAAC3D,KAAK,CAACqB,SAAS,EAAErB,KAAK,CAACwB,MAAM,CAAC;IACtCgN,kCAAkC,CAACxO,KAAK,CAAC;IACzC,IAAI,CAAC8C,OAAO,CAAC1H,KAAK,CAAC;IACnB,IAAI,CAACoK,IAAI,CAAC,CAAC;EACb;EACAwL,QAAQA,CAAA,EAAG;IACT,IAAI,CAAC,IAAI,CAAChR,KAAK,CAACe,OAAO,EAAE;IACzB,IAAI,CAACf,KAAK,CAACe,OAAO,GAAG,KAAK;IAC1B,IAAI,CAAC+B,OAAO,CAAC,CAAC;IACd,IAAI,CAAC0C,IAAI,CAAC,CAAC;EACb;EACAsD,IAAIA,CAAC6D,YAAY,EAAE;IACjBA,YAAY,CAAC,OAAO,EAAE,EAAE,EAAE,IAAI,CAAC+D,KAAK,CAAC5H,IAAI,CAAC,IAAI,CAAC,CAAC;EAClD;AACF;AAEA,MAAM4J,mBAAmB,GAAGrL,yBAAyB;AAErD,MAAMsL,WAAW,SAAS5M,iBAAiB,CAAC;EAC1CjG,WAAWA,CAAC,GAAGV,IAAI,EAAE;IACnB,KAAK,CAAC,GAAGA,IAAI,CAAC;IACd1H,eAAe,CAAC,IAAI,EAAE,QAAQ,EAAE,UAAU,CAAC;EAC7C;EACAkb,KAAKA,CAACxX,KAAK,EAAE;IACX,IAAI,IAAI,CAACsF,MAAM,CAACqR,SAAS,IAAI3W,KAAK,CAACE,WAAW,KAAK,OAAO,EAAE;IAC5D,IAAI,CAACjC,KAAK,CAAC+B,KAAK,CAAC;IACjB,IAAI,CAAC6E,aAAa,CAACpC,aAAa,CAACzC,KAAK,CAAC,CAAC;IACxC,IAAI,CAAC0H,OAAO,CAAC1H,KAAK,CAAC;IACnB,IAAI,CAACoK,IAAI,CAAC,CAAC;EACb;EACAqN,KAAKA,CAACzX,KAAK,EAAE;IACX,IAAI,IAAI,CAACsF,MAAM,CAACqR,SAAS,IAAI3W,KAAK,CAACE,WAAW,KAAK,OAAO,EAAE;IAC5D,MAAM0E,KAAK,GAAG,IAAI,CAACA,KAAK;IACxB,IAAI,CAACA,KAAK,CAACe,OAAO,EAAE;IACpBf,KAAK,CAACe,OAAO,GAAG,KAAK;IACrB,MAAM0B,MAAM,GAAG5E,aAAa,CAACzC,KAAK,CAAC;IACnC4E,KAAK,CAACqB,SAAS,GAAGrB,KAAK,CAACwB,MAAM,GAAGhL,CAAC,CAAC6O,GAAG,CAAC5C,MAAM,EAAEzC,KAAK,CAAC0C,OAAO,CAAC;IAC7D,IAAI,CAACzC,aAAa,CAACwC,MAAM,CAAC;IAC1B,IAAI,CAACK,OAAO,CAAC1H,KAAK,CAAC;IACnB4E,KAAK,CAACoC,KAAK,GAAGpC,KAAK,CAACmC,QAAQ;IAC5B,IAAI,CAACqD,IAAI,CAAC,CAAC;EACb;EACAsD,IAAIA,CAAC6D,YAAY,EAAE;IACjBA,YAAY,CAAC,SAAS,EAAE,OAAO,EAAE,IAAI,CAACiG,KAAK,CAAC9J,IAAI,CAAC,IAAI,CAAC,CAAC;IACvD6D,YAAY,CAAC,SAAS,EAAE,OAAO,EAAE,IAAI,CAACkG,KAAK,CAAC/J,IAAI,CAAC,IAAI,CAAC,CAAC;EACzD;AACF;AAEA,MAAMgK,mBAAmB,GAAGja,cAAc,CAACA,cAAc,CAAC,CAAC,CAAC,EAAEwO,yBAAyB,CAAC,EAAE,CAAC,CAAC,EAAE;EAC5F0K,SAAS,EAAEA,CAACna,KAAK,GAAG,IAAI,KAAKA;AAC/B,CAAC,CAAC;AAEF,MAAMmb,SAAS,GAAG,IAAIhE,GAAG,CAAC,CAAC;AAC3B,MAAMiE,iBAAiB,GAAG,IAAIjE,GAAG,CAAC,CAAC;AACnC,SAASkE,cAAcA,CAAC3Y,MAAM,EAAE;EAC9ByY,SAAS,CAACrD,GAAG,CAACpV,MAAM,CAAC7C,GAAG,EAAE6C,MAAM,CAAC4Y,MAAM,CAAC;EACxCF,iBAAiB,CAACtD,GAAG,CAACpV,MAAM,CAAC7C,GAAG,EAAE6C,MAAM,CAAC6Y,QAAQ,CAAC;AACpD;AACA,MAAMC,UAAU,GAAG;EACjB3b,GAAG,EAAE,MAAM;EACXyb,MAAM,EAAE9K,UAAU;EAClB+K,QAAQ,EAAElF;AACZ,CAAC;AACD,MAAMoF,WAAW,GAAG;EAClB5b,GAAG,EAAE,OAAO;EACZyb,MAAM,EAAEP,WAAW;EACnBQ,QAAQ,EAAEL;AACZ,CAAC;AACD,MAAMQ,UAAU,GAAG;EACjB7b,GAAG,EAAE,MAAM;EACXyb,MAAM,EAAErB,UAAU;EAClBsB,QAAQ,EAAEhB;AACZ,CAAC;AACD,MAAMoB,WAAW,GAAG;EAClB9b,GAAG,EAAE,OAAO;EACZyb,MAAM,EAAErE,WAAW;EACnBsE,QAAQ,EAAE/B;AACZ,CAAC;AACD,MAAMoC,YAAY,GAAG;EACnB/b,GAAG,EAAE,QAAQ;EACbyb,MAAM,EAAEd,YAAY;EACpBe,QAAQ,EAAEX;AACZ,CAAC;AACD,MAAMiB,WAAW,GAAG;EAClBhc,GAAG,EAAE,OAAO;EACZyb,MAAM,EAAET,WAAW;EACnBU,QAAQ,EAAET;AACZ,CAAC;AAED,SAASM,iBAAiB,IAAIU,CAAC,EAAEX,SAAS,IAAIY,CAAC,EAAEpG,OAAO,IAAIqG,CAAC,EAAE/a,cAAc,IAAIgb,CAAC,EAAEnc,eAAe,IAAIoc,CAAC,EAAExW,QAAQ,IAAIyW,CAAC,EAAEzU,KAAK,IAAI7I,CAAC,EAAE2D,aAAa,IAAI4Z,CAAC,EAAEZ,UAAU,IAAIjb,CAAC,EAAEob,WAAW,IAAIU,CAAC,EAAEZ,WAAW,IAAIa,CAAC,EAAE/Y,OAAO,IAAIgZ,CAAC,EAAEb,UAAU,IAAIc,CAAC,EAAE3Z,SAAS,IAAI4Z,CAAC,EAAEpB,cAAc,IAAI7a,CAAC,EAAEob,YAAY,IAAIc,CAAC,EAAEpZ,cAAc,IAAI7C,CAAC,EAAEob,WAAW,IAAIc,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}