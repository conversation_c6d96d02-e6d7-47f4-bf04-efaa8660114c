import React, { useEffect, useRef, useState } from 'react';
import { useNavigate } from 'react-router-dom';
import { useDispatch, useSelector } from 'react-redux';
import styled, { keyframes } from 'styled-components';
import { 
  setCurrentScreen, 
  startRace, 
  addExperience, 
  addCurrency, 
  unlockAbility, 
  updateRaceState 
} from '../store/gameSlice';
import { unlockAchievement } from '../store/userSlice';
import { RootState } from '../store';
import { Game, Track, Vehicle } from '../game/GameEngine';
import { Vector2D, TrackGenerator } from '../game/utils';
import { TTSSystem } from '../game/TTSSystem';
import { CarWhispererSystem, AbilityType } from '../game/CarWhispererSystem';
import RacingEnvironment from '../components/RacingEnvironment';
import RaceUI from '../components/RaceUI';
import GamePhysics from '../components/GamePhysics';
import GameStateManager from '../components/GameStateManager';
import ArcadeHUD from '../components/ArcadeHUD';
import { AudioSystem } from '../game/AudioSystem';

const spaceEffect = keyframes`
  0% { background-position: 0% 0%; }
  25% { background-position: 50% 50%; }
  50% { background-position: 100% 100%; }
  75% { background-position: 50% 50%; }
  100% { background-position: 0% 0%; }
`;

const RacingContainer = styled.div`
  width: 100%;
  height: 100vh;
  background: ${props => props.theme.backgroundGradient};
  background-size: 400% 400%;
  animation: ${spaceEffect} 30s ease infinite;
  color: white;
  display: flex;
  flex-direction: column;
  overflow: hidden;
  position: relative;
`;

const CanvasContainer = styled.div`
  flex: 1;
  position: relative;
`;

const GameCanvas = styled.canvas`
  width: 100%;
  height: 100%;
  display: block;
`;

const TrackSelector = styled.div`
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.9);
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 20px;
  z-index: 10;
  backdrop-filter: blur(5px);
`;

const TrackGrid = styled.div`
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 20px;
  width: 100%;
  max-width: 800px;
`;

const TrackCard = styled.div<{ $unlocked: boolean, $selected: boolean }>`
  background: ${props => props.$selected ? props.theme.button.primary.background : 'linear-gradient(145deg, rgba(40, 40, 60, 0.7), rgba(20, 20, 40, 0.7))'};
  border-radius: 15px;
  padding: 20px;
  cursor: ${props => props.$unlocked ? 'pointer' : 'not-allowed'};
  opacity: ${props => props.$unlocked ? 1 : 0.6};
  transition: all 0.3s ease;
  border: ${props => props.$selected ? `3px solid ${props.theme.accentColor}` : '3px solid transparent'}; 
  box-shadow: ${props => props.$selected ? `0 0 20px ${props.theme.accentColor}` : '0 5px 15px rgba(0,0,0,0.3)'};

  &:hover {
    transform: ${props => props.$unlocked ? 'scale(1.05) translateY(-5px)' : 'none'};
    box-shadow: ${props => props.$unlocked ? (props.$selected ? `0 0 25px ${props.theme.accentColor}` : '0 8px 20px rgba(0,0,0,0.4)') : '0 5px 15px rgba(0,0,0,0.3)'};
  }
`;

const TrackName = styled.h3`
  margin: 0 0 10px 0;
  font-size: 1.2rem;
`;

const TrackInfo = styled.div`
  display: flex;
  justify-content: space-between;
  font-size: 0.9rem;
  margin-bottom: 10px;
`;

const TrackDifficulty = styled.div<{ $difficulty: 'easy' | 'medium' | 'hard' }>`
  color: ${props => {
    switch(props.$difficulty) {
      case 'easy': return '#33cc66';
      case 'medium': return '#ffcc33';
      case 'hard': return '#ff3366';
      default: return '#ffffff';
    }
  }};
`;

const TrackBestTime = styled.div`
  color: #aaaaaa;
`;

const ButtonsContainer = styled.div`
  display: flex;
  justify-content: center;
  margin-top: 30px;
  gap: 20px;
`;

const Button = styled.button<{ $primary?: boolean }>`
  background: ${props => props.$primary ? props.theme.button.primary.background : props.theme.button.secondary.background};
  color: ${props => props.$primary ? props.theme.button.primary.color : props.theme.button.secondary.color};
  border: none;
  border-radius: 8px;
  padding: 14px 28px;
  font-size: 1.2rem;
  font-weight: bold;
  cursor: pointer;
  transition: all 0.3s ease;
  box-shadow: 0 4px 10px rgba(0,0,0,0.2);
  text-shadow: 0 1px 2px rgba(0,0,0,0.3);

  &:hover {
    background: ${props => props.$primary ? props.theme.button.primary.hover : props.theme.button.secondary.hover};
    transform: translateY(-2px);
    box-shadow: ${props => props.$primary ? '0 6px 15px rgba(0, 170, 255, 0.4)' : '0 6px 15px rgba(0,0,0,0.3)'};
  }

  &:active {
    transform: translateY(0);
    box-shadow: 0 2px 5px rgba(0,0,0,0.2);
  }

  &:disabled {
    background: linear-gradient(145deg, #333, #111);
    cursor: not-allowed;
    opacity: 0.6;
    box-shadow: none;
  }
`;

const GameOverlay = styled.div`
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  pointer-events: none;
  z-index: 2;
`;

const RaceResults = styled.div`
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  background: rgba(0, 0, 0, 0.8);
  border-radius: 10px;
  padding: 30px;
  text-align: center;
  width: 80%;
  max-width: 500px;
  pointer-events: auto;
`;

const ResultsTitle = styled.h2`
  margin: 0 0 20px 0;
  font-size: 2rem;
  color: #fdbb2d;
`;

const ResultsInfo = styled.div`
  margin-bottom: 30px;
  font-size: 1.2rem;
  line-height: 1.5;
`;

const ResultsRewards = styled.div`
  display: flex;
  justify-content: space-around;
  margin-bottom: 30px;
`;

const RewardItem = styled.div`
  display: flex;
  flex-direction: column;
  align-items: center;
`;

const RewardValue = styled.div`
  font-size: 1.5rem;
  font-weight: bold;
  color: #fdbb2d;
  margin-bottom: 5px;
`;

const RewardLabel = styled.div`
  font-size: 0.9rem;
  color: #aaaaaa;
`;

const IntegratedRacing: React.FC = () => {
  const navigate = useNavigate();
  const dispatch = useDispatch();
  const canvasRef = useRef<HTMLCanvasElement>(null);
  const gameInstance = useRef<Game | null>(null);
  const audioSystem = useRef<AudioSystem>(AudioSystem.getInstance());
  
  const currentTrackId = useSelector((state: RootState) => state.game.currentTrackId);
  const { availableTracks, activeRace, isPlaying, selectedWeather, selectedTimeOfDay, raceState } = useSelector((state: RootState) => state.game);
  const currentVehicleId = useSelector((state: RootState) => state.vehicle.currentVehicleId);
  const vehicles = useSelector((state: RootState) => state.vehicle.vehicles);
  const equippedParts = useSelector((state: RootState) => 
    state.vehicle.equippedParts[currentVehicleId] || {
      body: null,
      wheels: null,
      spoiler: null,
      engine: null,
      suspension: null,
      decals: [],
    }
  );
  const selectedColor = useSelector((state: RootState) => 
    state.vehicle.selectedColors[currentVehicleId]?.colorId || state.vehicle.availableColors[0].id
  );
  const availableColors = useSelector((state: RootState) => state.vehicle.availableColors);
  const controlScheme = useSelector((state: RootState) => state.user.settings.controlScheme);
  const unlockedAbilities = useSelector((state: RootState) => state.game.unlockedAbilities);
  
  const [showTrackSelector, setShowTrackSelector] = useState(true);
  const [selectedTrackId, setSelectedTrackId] = useState<string | null>(null);
  const [currentTimeOfDay, setCurrentTimeOfDay] = useState<'day' | 'sunset' | 'night' | 'dawn'>('day');
  const [currentWeather, setCurrentWeather] = useState<'clear' | 'rain' | 'snow' | 'fog' | 'storm'>('clear');
  const [raceFinished, setRaceFinished] = useState(false);
  
  // Get the color object for the selected color
  const color = availableColors.find(c => c.id === selectedColor);
  
  // Get the vehicle object
  const vehicle = vehicles && Object.values(vehicles).find((v: any) => v.id === currentVehicleId);

  useEffect(() => {
    dispatch(setCurrentScreen('race'));
    
    if (currentTrackId) {
      setSelectedTrackId(currentTrackId);
    } else if (availableTracks && availableTracks.length > 0) {
      const firstUnlockedTrack = availableTracks.find((track: any) => track.unlocked);
      if (firstUnlockedTrack) {
        setSelectedTrackId(firstUnlockedTrack.id);
      }
    }
    
    // Set time of day and weather based on selected options or defaults
    setCurrentTimeOfDay(selectedTimeOfDay || 'day');
    setCurrentWeather(selectedWeather || 'clear');
    
    // Setup initial race state
    dispatch(updateRaceState({
      isRaceStarted: false,
      currentLap: 0,
      position: 0,
      raceTime: '00:00.000',
      bestLapTime: null,
      currentLapTime: '00:00.000'
    }));
    
    // Clean up on unmount
    return () => {
      audioSystem.current.stopAllSounds();
      audioSystem.current.stopMusic();
      
      if (gameInstance.current) {
        // Clean up game instance
        gameInstance.current.dispose();
        gameInstance.current = null;
      }
    };
  }, [dispatch, currentTrackId, availableTracks, selectedTimeOfDay, selectedWeather]);
  
  const handleTrackSelect = (trackId: string) => {
    setSelectedTrackId(trackId);
    audioSystem.current.playSoundEffect('ui_select');
  };
  
  const handleStartRace = () => {
    if (!selectedTrackId) return;
    
    setShowTrackSelector(false);
    audioSystem.current.playSoundEffect('race_start_countdown');
    
    // Initialize the game with the selected track
    dispatch(startRace({
      trackId: selectedTrackId,
      vehicleId: currentVehicleId,
      timeOfDay: currentTimeOfDay,
      weather: currentWeather
    }));
    
    // Wait for game state initialization to complete
    setTimeout(() => {
      // Play starting sound
      audioSystem.current.playSoundEffect('race_start');
      
      // Switch to race music
      audioSystem.current.fadeOutCurrentMusic(1000);
      setTimeout(() => {
        audioSystem.current.playMusic('race_music');
      }, 1000);
    }, 3500); // Wait for countdown to finish
  };
  
  const handleBackToMenu = () => {
    navigate('/main-menu');
  };
  
  const handleContinue = () => {
    setRaceFinished(false);
    setShowTrackSelector(true);
  };
  
  // Handle race completion
  useEffect(() => {
    if (raceState.isRaceComplete) {
      setRaceFinished(true);
      audioSystem.current.playSoundEffect('race_complete');
      audioSystem.current.fadeOutCurrentMusic(2000);
      setTimeout(() => {
        audioSystem.current.playMusic('victory_music');
      }, 2000);
    }
  }, [raceState.isRaceComplete]);

  return (
    <RacingContainer>
      {/* Game Physics handler - no visual representation */}
      {!showTrackSelector && <GamePhysics />}
      
      {/* Game State Manager - no visual representation */}
      {!showTrackSelector && <GameStateManager />}
      
      <CanvasContainer>
        <GameCanvas ref={canvasRef} />
        
        {/* 3D Racing Environment */}
        {!showTrackSelector && selectedTrackId && (
          <RacingEnvironment 
            trackId={selectedTrackId}
            timeOfDay={currentTimeOfDay}
            weather={currentWeather}
            gameMode="standard"
            showControls={false}
          />
        )}
        
        {/* Race UI Overlay */}
        {!showTrackSelector && (
          <RaceUI
            currentLap={raceState.currentLap}
            totalLaps={3}
            position={raceState.position}
            totalRacers={8}
            speed={Math.round(raceState.speed * 30)} // Convert internal speed to KPH
            raceTime={raceState.raceTime}
            currentLapTime={raceState.currentLapTime}
            bestLapTime={raceState.bestLapTime}
          />
        )}

        {/* Enhanced Arcade HUD for fun gameplay! */}
        {!showTrackSelector && (
          <ArcadeHUD visible={true} />
        )}
        
        <GameOverlay>
          {raceFinished && (
            <RaceResults>
              <ResultsTitle>Race Complete!</ResultsTitle>
              <ResultsInfo>
                <div>Position: {raceState.position} of 8</div>
                {raceState.bestLapTime && (
                  <div>Best Lap: {raceState.bestLapTime}</div>
                )}
              </ResultsInfo>
              
              <ResultsRewards>
                <RewardItem>
                  <RewardValue>+{raceState.experienceReward}</RewardValue>
                  <RewardLabel>Experience</RewardLabel>
                </RewardItem>
                <RewardItem>
                  <RewardValue>+{raceState.currencyReward}</RewardValue>
                  <RewardLabel>Credits</RewardLabel>
                </RewardItem>
              </ResultsRewards>
              
              <ButtonsContainer>
                <Button onClick={handleContinue} $primary>
                  Race Again
                </Button>
                <Button onClick={handleBackToMenu}>
                  Back to Menu
                </Button>
              </ButtonsContainer>
            </RaceResults>
          )}
        </GameOverlay>
      </CanvasContainer>
      
      {showTrackSelector && (
        <TrackSelector>
          <h2>Select Track</h2>
          
          <TrackGrid>
            {availableTracks?.map((track: any) => (
              <TrackCard 
                key={track.id}
                $unlocked={track.unlocked}
                $selected={selectedTrackId === track.id}
                onClick={() => track.unlocked && handleTrackSelect(track.id)}
              >
                <TrackName>{track.name}</TrackName>
                <TrackInfo>
                  <TrackDifficulty $difficulty={track.difficulty}>
                    {track.difficulty.charAt(0).toUpperCase() + track.difficulty.slice(1)}
                  </TrackDifficulty>
                  {track.bestTime && (
                    <TrackBestTime>
                      Best: {track.bestTime}
                    </TrackBestTime>
                  )}
                </TrackInfo>
                {!track.unlocked && (
                  <div style={{ fontSize: '0.9rem', color: '#aaaaaa' }}>
                    Locked - Complete previous tracks to unlock
                  </div>
                )}
              </TrackCard>
            ))}
          </TrackGrid>
          
          <ButtonsContainer>
            <Button 
              onClick={handleStartRace} 
              $primary
              disabled={!selectedTrackId}
            >
              Start Race
            </Button>
            <Button onClick={handleBackToMenu}>
              Back to Menu
            </Button>
          </ButtonsContainer>
        </TrackSelector>
      )}
    </RacingContainer>
  );
};

export default IntegratedRacing;
