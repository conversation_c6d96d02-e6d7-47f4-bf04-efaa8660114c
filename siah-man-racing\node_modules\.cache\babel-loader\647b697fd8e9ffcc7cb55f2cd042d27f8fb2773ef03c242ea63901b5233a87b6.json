{"ast": null, "code": "import { a as _defineProperty, _ as _objectSpread2 } from './objectSpread2-284232a6.esm.js';\nimport { _ as _classCallCheck } from './classCallCheck-9098b006.esm.js';\nimport { l as lerp, f as fade } from './misc-19a3ec46.esm.js';\nfunction _defineProperties(target, props) {\n  for (var i = 0; i < props.length; i++) {\n    var descriptor = props[i];\n    descriptor.enumerable = descriptor.enumerable || false;\n    descriptor.configurable = true;\n    if (\"value\" in descriptor) descriptor.writable = true;\n    Object.defineProperty(target, descriptor.key, descriptor);\n  }\n}\nfunction _createClass(Constructor, protoProps, staticProps) {\n  if (protoProps) _defineProperties(Constructor.prototype, protoProps);\n  if (staticProps) _defineProperties(Constructor, staticProps);\n  return Constructor;\n}\n\n/*\n * A speed-improved perlin and simplex noise algorithms for 2D.\n *\n * Based on example code by <PERSON> (<EMAIL>).\n * Optimisations by <PERSON> (p<PERSON><PERSON>@drizzle.stanford.edu).\n * Better rank ordering method by <PERSON> in 2012.\n * Converted to Javascript by <PERSON>tle.\n *\n * Version 2012-03-09\n *\n * This code was placed in the public domain by its original author,\n * Stefan Gustavson. You may use it as you see fit, but\n * attribution is appreciated.\n *\n */\n\nvar Grad = function Grad(x, y, z) {\n  var _this = this;\n  _classCallCheck(this, Grad);\n  _defineProperty(this, \"dot2\", function (x, y) {\n    return _this.x * x + _this.y * y;\n  });\n  _defineProperty(this, \"dot3\", function (x, y, z) {\n    return _this.x * x + _this.y * y + _this.z * z;\n  });\n  this.x = x;\n  this.y = y;\n  this.z = z;\n};\nvar grad3 = [new Grad(1, 1, 0), new Grad(-1, 1, 0), new Grad(1, -1, 0), new Grad(-1, -1, 0), new Grad(1, 0, 1), new Grad(-1, 0, 1), new Grad(1, 0, -1), new Grad(-1, 0, -1), new Grad(0, 1, 1), new Grad(0, -1, 1), new Grad(0, 1, -1), new Grad(0, -1, -1)];\nvar p = [151, 160, 137, 91, 90, 15, 131, 13, 201, 95, 96, 53, 194, 233, 7, 225, 140, 36, 103, 30, 69, 142, 8, 99, 37, 240, 21, 10, 23, 190, 6, 148, 247, 120, 234, 75, 0, 26, 197, 62, 94, 252, 219, 203, 117, 35, 11, 32, 57, 177, 33, 88, 237, 149, 56, 87, 174, 20, 125, 136, 171, 168, 68, 175, 74, 165, 71, 134, 139, 48, 27, 166, 77, 146, 158, 231, 83, 111, 229, 122, 60, 211, 133, 230, 220, 105, 92, 41, 55, 46, 245, 40, 244, 102, 143, 54, 65, 25, 63, 161, 1, 216, 80, 73, 209, 76, 132, 187, 208, 89, 18, 169, 200, 196, 135, 130, 116, 188, 159, 86, 164, 100, 109, 198, 173, 186, 3, 64, 52, 217, 226, 250, 124, 123, 5, 202, 38, 147, 118, 126, 255, 82, 85, 212, 207, 206, 59, 227, 47, 16, 58, 17, 182, 189, 28, 42, 223, 183, 170, 213, 119, 248, 152, 2, 44, 154, 163, 70, 221, 153, 101, 155, 167, 43, 172, 9, 129, 22, 39, 253, 19, 98, 108, 110, 79, 113, 224, 232, 178, 185, 112, 104, 218, 246, 97, 228, 251, 34, 242, 193, 238, 210, 144, 12, 191, 179, 162, 241, 81, 51, 145, 235, 249, 14, 239, 107, 49, 192, 214, 31, 181, 199, 106, 157, 184, 84, 204, 176, 115, 121, 50, 45, 127, 4, 150, 254, 138, 236, 205, 93, 222, 114, 67, 29, 24, 72, 243, 141, 128, 195, 78, 66, 215, 61, 156, 180]; // To remove the need for index wrapping, double the permutation table length\n\nvar perm = new Array(512);\nvar gradP = new Array(512); // This isn't a very good seeding function, but it works ok. It supports 2^16\n// different seed values. Write something better if you need more seeds.\n\nvar seed = function seed(_seed) {\n  if (_seed > 0 && _seed < 1) {\n    // Scale the seed out\n    _seed *= 65536;\n  }\n  _seed = Math.floor(_seed);\n  if (_seed < 256) {\n    _seed |= _seed << 8;\n  }\n  for (var i = 0; i < 256; i++) {\n    var v;\n    if (i & 1) {\n      v = p[i] ^ _seed & 255;\n    } else {\n      v = p[i] ^ _seed >> 8 & 255;\n    }\n    perm[i] = perm[i + 256] = v;\n    gradP[i] = gradP[i + 256] = grad3[v % 12];\n  }\n};\nseed(0);\n/*\n  for(var i=0; i<256; i++) {\n    perm[i] = perm[i + 256] = p[i];\n    gradP[i] = gradP[i + 256] = grad3[perm[i] % 12];\n  }*/\n// Skewing and unskewing factors for 2, 3, and 4 dimensions\n\nvar F2 = 0.5 * (Math.sqrt(3) - 1);\nvar G2 = (3 - Math.sqrt(3)) / 6;\nvar F3 = 1 / 3;\nvar G3 = 1 / 6; // 2D simplex noise\n\nvar simplex2 = function simplex2(xin, yin) {\n  var n0, n1, n2; // Noise contributions from the three corners\n  // Skew the input space to determine which simplex cell we're in\n\n  var s = (xin + yin) * F2; // Hairy factor for 2D\n\n  var i = Math.floor(xin + s);\n  var j = Math.floor(yin + s);\n  var t = (i + j) * G2;\n  var x0 = xin - i + t; // The x,y distances from the cell origin, unskewed.\n\n  var y0 = yin - j + t; // For the 2D case, the simplex shape is an equilateral triangle.\n  // Determine which simplex we are in.\n\n  var i1, j1; // Offsets for second (middle) corner of simplex in (i,j) coords\n\n  if (x0 > y0) {\n    // lower triangle, XY order: (0,0)->(1,0)->(1,1)\n    i1 = 1;\n    j1 = 0;\n  } else {\n    // upper triangle, YX order: (0,0)->(0,1)->(1,1)\n    i1 = 0;\n    j1 = 1;\n  } // A step of (1,0) in (i,j) means a step of (1-c,-c) in (x,y), and\n  // a step of (0,1) in (i,j) means a step of (-c,1-c) in (x,y), where\n  // c = (3-sqrt(3))/6\n\n  var x1 = x0 - i1 + G2; // Offsets for middle corner in (x,y) unskewed coords\n\n  var y1 = y0 - j1 + G2;\n  var x2 = x0 - 1 + 2 * G2; // Offsets for last corner in (x,y) unskewed coords\n\n  var y2 = y0 - 1 + 2 * G2; // Work out the hashed gradient indices of the three simplex corners\n\n  i &= 255;\n  j &= 255;\n  var gi0 = gradP[i + perm[j]];\n  var gi1 = gradP[i + i1 + perm[j + j1]];\n  var gi2 = gradP[i + 1 + perm[j + 1]]; // Calculate the contribution from the three corners\n\n  var t0 = 0.5 - x0 * x0 - y0 * y0;\n  if (t0 < 0) {\n    n0 = 0;\n  } else {\n    t0 *= t0;\n    n0 = t0 * t0 * gi0.dot2(x0, y0); // (x,y) of grad3 used for 2D gradient\n  }\n  var t1 = 0.5 - x1 * x1 - y1 * y1;\n  if (t1 < 0) {\n    n1 = 0;\n  } else {\n    t1 *= t1;\n    n1 = t1 * t1 * gi1.dot2(x1, y1);\n  }\n  var t2 = 0.5 - x2 * x2 - y2 * y2;\n  if (t2 < 0) {\n    n2 = 0;\n  } else {\n    t2 *= t2;\n    n2 = t2 * t2 * gi2.dot2(x2, y2);\n  } // Add contributions from each corner to get the final noise value.\n  // The result is scaled to return values in the interval [-1,1].\n\n  return 70 * (n0 + n1 + n2);\n}; // 3D simplex noise\n\nvar simplex3 = function simplex3(xin, yin, zin) {\n  var n0, n1, n2, n3; // Noise contributions from the four corners\n  // Skew the input space to determine which simplex cell we're in\n\n  var s = (xin + yin + zin) * F3; // Hairy factor for 2D\n\n  var i = Math.floor(xin + s);\n  var j = Math.floor(yin + s);\n  var k = Math.floor(zin + s);\n  var t = (i + j + k) * G3;\n  var x0 = xin - i + t; // The x,y distances from the cell origin, unskewed.\n\n  var y0 = yin - j + t;\n  var z0 = zin - k + t; // For the 3D case, the simplex shape is a slightly irregular tetrahedron.\n  // Determine which simplex we are in.\n\n  var i1, j1, k1; // Offsets for second corner of simplex in (i,j,k) coords\n\n  var i2, j2, k2; // Offsets for third corner of simplex in (i,j,k) coords\n\n  if (x0 >= y0) {\n    if (y0 >= z0) {\n      i1 = 1;\n      j1 = 0;\n      k1 = 0;\n      i2 = 1;\n      j2 = 1;\n      k2 = 0;\n    } else if (x0 >= z0) {\n      i1 = 1;\n      j1 = 0;\n      k1 = 0;\n      i2 = 1;\n      j2 = 0;\n      k2 = 1;\n    } else {\n      i1 = 0;\n      j1 = 0;\n      k1 = 1;\n      i2 = 1;\n      j2 = 0;\n      k2 = 1;\n    }\n  } else {\n    if (y0 < z0) {\n      i1 = 0;\n      j1 = 0;\n      k1 = 1;\n      i2 = 0;\n      j2 = 1;\n      k2 = 1;\n    } else if (x0 < z0) {\n      i1 = 0;\n      j1 = 1;\n      k1 = 0;\n      i2 = 0;\n      j2 = 1;\n      k2 = 1;\n    } else {\n      i1 = 0;\n      j1 = 1;\n      k1 = 0;\n      i2 = 1;\n      j2 = 1;\n      k2 = 0;\n    }\n  } // A step of (1,0,0) in (i,j,k) means a step of (1-c,-c,-c) in (x,y,z),\n  // a step of (0,1,0) in (i,j,k) means a step of (-c,1-c,-c) in (x,y,z), and\n  // a step of (0,0,1) in (i,j,k) means a step of (-c,-c,1-c) in (x,y,z), where\n  // c = 1/6.\n\n  var x1 = x0 - i1 + G3; // Offsets for second corner\n\n  var y1 = y0 - j1 + G3;\n  var z1 = z0 - k1 + G3;\n  var x2 = x0 - i2 + 2 * G3; // Offsets for third corner\n\n  var y2 = y0 - j2 + 2 * G3;\n  var z2 = z0 - k2 + 2 * G3;\n  var x3 = x0 - 1 + 3 * G3; // Offsets for fourth corner\n\n  var y3 = y0 - 1 + 3 * G3;\n  var z3 = z0 - 1 + 3 * G3; // Work out the hashed gradient indices of the four simplex corners\n\n  i &= 255;\n  j &= 255;\n  k &= 255;\n  var gi0 = gradP[i + perm[j + perm[k]]];\n  var gi1 = gradP[i + i1 + perm[j + j1 + perm[k + k1]]];\n  var gi2 = gradP[i + i2 + perm[j + j2 + perm[k + k2]]];\n  var gi3 = gradP[i + 1 + perm[j + 1 + perm[k + 1]]]; // Calculate the contribution from the four corners\n\n  var t0 = 0.6 - x0 * x0 - y0 * y0 - z0 * z0;\n  if (t0 < 0) {\n    n0 = 0;\n  } else {\n    t0 *= t0;\n    n0 = t0 * t0 * gi0.dot3(x0, y0, z0); // (x,y) of grad3 used for 2D gradient\n  }\n  var t1 = 0.6 - x1 * x1 - y1 * y1 - z1 * z1;\n  if (t1 < 0) {\n    n1 = 0;\n  } else {\n    t1 *= t1;\n    n1 = t1 * t1 * gi1.dot3(x1, y1, z1);\n  }\n  var t2 = 0.6 - x2 * x2 - y2 * y2 - z2 * z2;\n  if (t2 < 0) {\n    n2 = 0;\n  } else {\n    t2 *= t2;\n    n2 = t2 * t2 * gi2.dot3(x2, y2, z2);\n  }\n  var t3 = 0.6 - x3 * x3 - y3 * y3 - z3 * z3;\n  if (t3 < 0) {\n    n3 = 0;\n  } else {\n    t3 *= t3;\n    n3 = t3 * t3 * gi3.dot3(x3, y3, z3);\n  } // Add contributions from each corner to get the final noise value.\n  // The result is scaled to return values in the interval [-1,1].\n\n  return 32 * (n0 + n1 + n2 + n3);\n}; // ##### Perlin noise stuff\n// 2D Perlin Noise\n\nvar perlin2 = function perlin2(x, y) {\n  // Find unit grid cell containing point\n  var X = Math.floor(x),\n    Y = Math.floor(y); // Get relative xy coordinates of point within that cell\n\n  x = x - X;\n  y = y - Y; // Wrap the integer cells at 255 (smaller integer period can be introduced here)\n\n  X = X & 255;\n  Y = Y & 255; // Calculate noise contributions from each of the four corners\n\n  var n00 = gradP[X + perm[Y]].dot2(x, y);\n  var n01 = gradP[X + perm[Y + 1]].dot2(x, y - 1);\n  var n10 = gradP[X + 1 + perm[Y]].dot2(x - 1, y);\n  var n11 = gradP[X + 1 + perm[Y + 1]].dot2(x - 1, y - 1); // Compute the fade curve value for x\n\n  var u = fade(x); // Interpolate the four results\n\n  return lerp(lerp(n00, n10, u), lerp(n01, n11, u), fade(y));\n}; // 3D Perlin Noise\n\nvar perlin3 = function perlin3(x, y, z) {\n  // Find unit grid cell containing point\n  var X = Math.floor(x),\n    Y = Math.floor(y),\n    Z = Math.floor(z); // Get relative xyz coordinates of point within that cell\n\n  x = x - X;\n  y = y - Y;\n  z = z - Z; // Wrap the integer cells at 255 (smaller integer period can be introduced here)\n\n  X = X & 255;\n  Y = Y & 255;\n  Z = Z & 255; // Calculate noise contributions from each of the eight corners\n\n  var n000 = gradP[X + perm[Y + perm[Z]]].dot3(x, y, z);\n  var n001 = gradP[X + perm[Y + perm[Z + 1]]].dot3(x, y, z - 1);\n  var n010 = gradP[X + perm[Y + 1 + perm[Z]]].dot3(x, y - 1, z);\n  var n011 = gradP[X + perm[Y + 1 + perm[Z + 1]]].dot3(x, y - 1, z - 1);\n  var n100 = gradP[X + 1 + perm[Y + perm[Z]]].dot3(x - 1, y, z);\n  var n101 = gradP[X + 1 + perm[Y + perm[Z + 1]]].dot3(x - 1, y, z - 1);\n  var n110 = gradP[X + 1 + perm[Y + 1 + perm[Z]]].dot3(x - 1, y - 1, z);\n  var n111 = gradP[X + 1 + perm[Y + 1 + perm[Z + 1]]].dot3(x - 1, y - 1, z - 1); // Compute the fade curve value for x, y, z\n\n  var u = fade(x);\n  var v = fade(y);\n  var w = fade(z); // Interpolate\n\n  return lerp(lerp(lerp(n000, n100, u), lerp(n001, n101, u), w), lerp(lerp(n010, n110, u), lerp(n011, n111, u), w), v);\n};\nvar noise = /*#__PURE__*/Object.freeze({\n  __proto__: null,\n  seed: seed,\n  simplex2: simplex2,\n  simplex3: simplex3,\n  perlin2: perlin2,\n  perlin3: perlin3\n});\nvar TAU = Math.PI * 2;\nvar FlashGen = /*#__PURE__*/function () {\n  function FlashGen(props) {\n    _classCallCheck(this, FlashGen);\n    _defineProperty(this, \"nextBurstTime\", 0);\n    _defineProperty(this, \"nextFlashEndTime\", 0);\n    _defineProperty(this, \"flashesDone\", 0);\n    _defineProperty(this, \"isFlashing\", false);\n    _defineProperty(this, \"currentCount\", 0);\n    _defineProperty(this, \"flashIntensity\", 0);\n    _defineProperty(this, \"isDecaying\", false);\n    _defineProperty(this, \"autoBurst\", true);\n    _defineProperty(this, \"decaySpeed\", 40);\n    _defineProperty(this, \"minInterval\", 5000);\n    _defineProperty(this, \"maxInterval\", 10000);\n    _defineProperty(this, \"minDuration\", 50);\n    _defineProperty(this, \"maxDuration\", 300);\n    _defineProperty(this, \"count\", 5);\n    Object.assign(this, props);\n  }\n  _createClass(FlashGen, [{\n    key: \"scheduleNextBurst\",\n    value: function scheduleNextBurst(currentTime) {\n      var burstInterval = Math.random() * (this.maxInterval - this.minInterval) + this.minInterval;\n      this.nextBurstTime = currentTime + burstInterval / 1000;\n      this.flashesDone = 0;\n      this.isFlashing = false;\n    }\n  }, {\n    key: \"burst\",\n    value: function burst() {\n      this.nextBurstTime = 0;\n      this.flashesDone = 0;\n      this.isFlashing = false;\n    }\n  }, {\n    key: \"update\",\n    value: function update(currentTime, delta) {\n      if (currentTime > this.nextBurstTime && this.currentCount === 0) {\n        this.currentCount = Math.floor(Math.random() * this.count) + 1;\n      }\n      if (this.flashesDone < this.currentCount && currentTime > this.nextBurstTime) {\n        if (!this.isFlashing) {\n          this.isFlashing = true;\n          this.flashIntensity = 1;\n          var flashDuration = Math.random() * (this.maxDuration - this.minDuration) + this.minDuration;\n          this.nextFlashEndTime = currentTime + flashDuration / 1000;\n        } else if (this.isFlashing && currentTime > this.nextFlashEndTime) {\n          this.isFlashing = false;\n          this.isDecaying = true;\n          this.flashesDone++;\n          if (this.flashesDone >= this.currentCount) {\n            this.currentCount = 0;\n            if (this.autoBurst) this.scheduleNextBurst(currentTime);\n          }\n        }\n      }\n      if (this.isDecaying) {\n        this.flashIntensity -= delta * this.decaySpeed;\n        this.flashIntensity = Math.max(0, Math.min(1, this.flashIntensity));\n        if (this.flashIntensity <= 0) {\n          this.isDecaying = false;\n          this.flashIntensity = 0;\n        }\n      }\n      return this.flashIntensity;\n    }\n  }]);\n  return FlashGen;\n}(); // Credits @kchapelier https://github.com/kchapelier/wavefunctioncollapse/blob/master/example/lcg.js#L22-L30\n\nfunction normalizeSeed(seed) {\n  if (typeof seed === \"number\") {\n    seed = Math.abs(seed);\n  } else if (typeof seed === \"string\") {\n    var string = seed;\n    seed = 0;\n    for (var i = 0; i < string.length; i++) {\n      seed = (seed + (i + 1) * (string.charCodeAt(i) % 96)) % 2147483647;\n    }\n  }\n  if (seed === 0) {\n    seed = 311;\n  }\n  return seed;\n}\nfunction lcgRandom(seed) {\n  var state = normalizeSeed(seed);\n  return function () {\n    var result = state * 48271 % 2147483647;\n    state = result;\n    return result / 2147483647;\n  };\n}\nvar Generator = function Generator(_seed) {\n  var _this = this;\n  _classCallCheck(this, Generator);\n  _defineProperty(this, \"seed\", 0);\n  _defineProperty(this, \"init\", function (seed) {\n    _this.seed = seed;\n    _this.value = lcgRandom(seed);\n  });\n  _defineProperty(this, \"value\", lcgRandom(this.seed));\n  this.init(_seed);\n};\nvar defaultGen = new Generator(Math.random());\n/***\n * [3D] Sphere\n */\n\nvar defaultSphere = {\n  radius: 1,\n  center: [0, 0, 0]\n}; // random on surface of sphere\n// - https://twitter.com/fermatslibrary/status/1430932503578226688\n// - https://mathworld.wolfram.com/SpherePointPicking.html\n\nfunction onSphere(buffer, sphere) {\n  var rng = arguments.length > 2 && arguments[2] !== undefined ? arguments[2] : defaultGen;\n  var _defaultSphere$sphere = _objectSpread2(_objectSpread2({}, defaultSphere), sphere),\n    radius = _defaultSphere$sphere.radius,\n    center = _defaultSphere$sphere.center;\n  for (var i = 0; i < buffer.length; i += 3) {\n    var u = rng.value();\n    var v = rng.value();\n    var theta = Math.acos(2 * v - 1);\n    var phi = TAU * u;\n    buffer[i] = Math.sin(theta) * Math.cos(phi) * radius + center[0];\n    buffer[i + 1] = Math.sin(theta) * Math.sin(phi) * radius + center[1];\n    buffer[i + 2] = Math.cos(theta) * radius + center[2];\n  }\n  return buffer;\n} // from \"Another Method\" https://datagenetics.com/blog/january32020/index.html\n\nfunction inSphere(buffer, sphere) {\n  var rng = arguments.length > 2 && arguments[2] !== undefined ? arguments[2] : defaultGen;\n  var _defaultSphere$sphere2 = _objectSpread2(_objectSpread2({}, defaultSphere), sphere),\n    radius = _defaultSphere$sphere2.radius,\n    center = _defaultSphere$sphere2.center;\n  for (var i = 0; i < buffer.length; i += 3) {\n    var u = Math.pow(rng.value(), 1 / 3);\n    var x = rng.value() * 2 - 1;\n    var y = rng.value() * 2 - 1;\n    var z = rng.value() * 2 - 1;\n    var mag = Math.sqrt(x * x + y * y + z * z);\n    x = u * x / mag;\n    y = u * y / mag;\n    z = u * z / mag;\n    buffer[i] = x * radius + center[0];\n    buffer[i + 1] = y * radius + center[1];\n    buffer[i + 2] = z * radius + center[2];\n  }\n  return buffer;\n}\n/***\n * [2D] Circle\n */\n\nvar defaultCircle = {\n  radius: 1,\n  center: [0, 0]\n}; // random circle https://stackoverflow.com/a/50746409\n\nfunction inCircle(buffer, circle) {\n  var rng = arguments.length > 2 && arguments[2] !== undefined ? arguments[2] : defaultGen;\n  var _defaultCircle$circle = _objectSpread2(_objectSpread2({}, defaultCircle), circle),\n    radius = _defaultCircle$circle.radius,\n    center = _defaultCircle$circle.center;\n  for (var i = 0; i < buffer.length; i += 2) {\n    var r = radius * Math.sqrt(rng.value());\n    var theta = rng.value() * TAU;\n    buffer[i] = Math.sin(theta) * r + center[0];\n    buffer[i + 1] = Math.cos(theta) * r + center[1];\n  }\n  return buffer;\n}\nfunction onCircle(buffer, circle) {\n  var rng = arguments.length > 2 && arguments[2] !== undefined ? arguments[2] : defaultGen;\n  var _defaultCircle$circle2 = _objectSpread2(_objectSpread2({}, defaultCircle), circle),\n    radius = _defaultCircle$circle2.radius,\n    center = _defaultCircle$circle2.center;\n  for (var i = 0; i < buffer.length; i += 2) {\n    var theta = rng.value() * TAU;\n    buffer[i] = Math.sin(theta) * radius + center[0];\n    buffer[i + 1] = Math.cos(theta) * radius + center[1];\n  }\n  return buffer;\n}\n/**\n * [2D] Plane\n */\n\nvar defaultRect = {\n  sides: 1,\n  center: [0, 0]\n};\nfunction inRect(buffer, rect) {\n  var rng = arguments.length > 2 && arguments[2] !== undefined ? arguments[2] : defaultGen;\n  var _defaultRect$rect = _objectSpread2(_objectSpread2({}, defaultRect), rect),\n    sides = _defaultRect$rect.sides,\n    center = _defaultRect$rect.center;\n  var sideX = typeof sides === \"number\" ? sides : sides[0];\n  var sideY = typeof sides === \"number\" ? sides : sides[1];\n  for (var i = 0; i < buffer.length; i += 2) {\n    buffer[i] = (rng.value() - 0.5) * sideX + center[0];\n    buffer[i + 1] = (rng.value() - 0.5) * sideY + center[1];\n  }\n  return buffer;\n}\nfunction onRect(buffer, rect) {\n  return buffer;\n}\n/***\n * [3D] Box\n */\n\nfunction inBox(buffer, box) {\n  var rng = arguments.length > 2 && arguments[2] !== undefined ? arguments[2] : defaultGen;\n  var _defaultBox$box = _objectSpread2(_objectSpread2({}, defaultBox), box),\n    sides = _defaultBox$box.sides,\n    center = _defaultBox$box.center;\n  var sideX = typeof sides === \"number\" ? sides : sides[0];\n  var sideY = typeof sides === \"number\" ? sides : sides[1];\n  var sideZ = typeof sides === \"number\" ? sides : sides[2];\n  for (var i = 0; i < buffer.length; i += 3) {\n    buffer[i] = (rng.value() - 0.5) * sideX + center[0];\n    buffer[i + 1] = (rng.value() - 0.5) * sideY + center[1];\n    buffer[i + 2] = (rng.value() - 0.5) * sideZ + center[2];\n  }\n  return buffer;\n}\nvar defaultBox = {\n  sides: 1,\n  center: [0, 0, 0]\n};\nfunction onBox(buffer, box) {\n  var rng = arguments.length > 2 && arguments[2] !== undefined ? arguments[2] : defaultGen;\n  var _defaultBox$box2 = _objectSpread2(_objectSpread2({}, defaultBox), box),\n    sides = _defaultBox$box2.sides,\n    center = _defaultBox$box2.center;\n  var sideX = typeof sides === \"number\" ? sides : sides[0];\n  var sideY = typeof sides === \"number\" ? sides : sides[1];\n  var sideZ = typeof sides === \"number\" ? sides : sides[2];\n  for (var i = 0; i < buffer.length; i += 3) {\n    buffer[i] = (rng.value() - 0.5) * sideX + center[0];\n    buffer[i + 1] = (rng.value() - 0.5) * sideY + center[1];\n    buffer[i + 2] = (rng.value() - 0.5) * sideZ + center[2];\n  }\n  return buffer;\n}\nvar index = /*#__PURE__*/Object.freeze({\n  __proto__: null,\n  FlashGen: FlashGen,\n  Generator: Generator,\n  onSphere: onSphere,\n  inSphere: inSphere,\n  inCircle: inCircle,\n  onCircle: onCircle,\n  inRect: inRect,\n  onRect: onRect,\n  inBox: inBox,\n  onBox: onBox,\n  noise: noise\n});\nexport { FlashGen as F, Generator as G, inSphere as a, inCircle as b, onCircle as c, inRect as d, onRect as e, inBox as f, onBox as g, index as i, noise as n, onSphere as o };", "map": {"version": 3, "names": ["a", "_defineProperty", "_", "_objectSpread2", "_classCallCheck", "l", "lerp", "f", "fade", "_defineProperties", "target", "props", "i", "length", "descriptor", "enumerable", "configurable", "writable", "Object", "defineProperty", "key", "_createClass", "<PERSON><PERSON><PERSON><PERSON>", "protoProps", "staticProps", "prototype", "Grad", "x", "y", "z", "_this", "grad3", "p", "perm", "Array", "gradP", "seed", "_seed", "Math", "floor", "v", "F2", "sqrt", "G2", "F3", "G3", "simplex2", "xin", "yin", "n0", "n1", "n2", "s", "j", "t", "x0", "y0", "i1", "j1", "x1", "y1", "x2", "y2", "gi0", "gi1", "gi2", "t0", "dot2", "t1", "t2", "simplex3", "zin", "n3", "k", "z0", "k1", "i2", "j2", "k2", "z1", "z2", "x3", "y3", "z3", "gi3", "dot3", "t3", "perlin2", "X", "Y", "n00", "n01", "n10", "n11", "u", "perlin3", "Z", "n000", "n001", "n010", "n011", "n100", "n101", "n110", "n111", "w", "noise", "freeze", "__proto__", "TAU", "PI", "FlashGen", "assign", "value", "scheduleNextBurst", "currentTime", "burstInterval", "random", "maxInterval", "minInterval", "nextBurstTime", "flashesDone", "isFlashing", "burst", "update", "delta", "currentCount", "count", "flashIntensity", "flashDuration", "maxDuration", "minDuration", "nextFlashEndTime", "isDecaying", "autoBurst", "decaySpeed", "max", "min", "normalizeSeed", "abs", "string", "charCodeAt", "lcgRandom", "state", "result", "Generator", "init", "defaultGen", "defaultSphere", "radius", "center", "onSphere", "buffer", "sphere", "rng", "arguments", "undefined", "_defaultSphere$sphere", "theta", "acos", "phi", "sin", "cos", "inSphere", "_defaultSphere$sphere2", "pow", "mag", "defaultCircle", "inCircle", "circle", "_defaultCircle$circle", "r", "onCircle", "_defaultCircle$circle2", "defaultRect", "sides", "inRect", "rect", "_defaultRect$rect", "sideX", "sideY", "onRect", "inBox", "box", "_defaultBox$box", "defaultBox", "sideZ", "onBox", "_defaultBox$box2", "index", "F", "G", "b", "c", "d", "e", "g", "n", "o"], "sources": ["C:/Users/<USER>/Documents/Apps_Creations/SiahMan_Racing4Speed_WebGame/siah-man-racing/node_modules/maath/dist/index-0332b2ed.esm.js"], "sourcesContent": ["import { a as _defineProperty, _ as _objectSpread2 } from './objectSpread2-284232a6.esm.js';\nimport { _ as _classCallCheck } from './classCallCheck-9098b006.esm.js';\nimport { l as lerp, f as fade } from './misc-19a3ec46.esm.js';\n\nfunction _defineProperties(target, props) {\n  for (var i = 0; i < props.length; i++) {\n    var descriptor = props[i];\n    descriptor.enumerable = descriptor.enumerable || false;\n    descriptor.configurable = true;\n    if (\"value\" in descriptor) descriptor.writable = true;\n    Object.defineProperty(target, descriptor.key, descriptor);\n  }\n}\n\nfunction _createClass(Constructor, protoProps, staticProps) {\n  if (protoProps) _defineProperties(Constructor.prototype, protoProps);\n  if (staticProps) _defineProperties(Constructor, staticProps);\n  return Constructor;\n}\n\n/*\n * A speed-improved perlin and simplex noise algorithms for 2D.\n *\n * Based on example code by <PERSON> (<EMAIL>).\n * Optimisations by <PERSON> (p<PERSON><PERSON>@drizzle.stanford.edu).\n * Better rank ordering method by <PERSON> in 2012.\n * Converted to Javascript by <PERSON>tle.\n *\n * Version 2012-03-09\n *\n * This code was placed in the public domain by its original author,\n * Stefan Gustavson. You may use it as you see fit, but\n * attribution is appreciated.\n *\n */\n\nvar Grad = function Grad(x, y, z) {\n  var _this = this;\n\n  _classCallCheck(this, Grad);\n\n  _defineProperty(this, \"dot2\", function (x, y) {\n    return _this.x * x + _this.y * y;\n  });\n\n  _defineProperty(this, \"dot3\", function (x, y, z) {\n    return _this.x * x + _this.y * y + _this.z * z;\n  });\n\n  this.x = x;\n  this.y = y;\n  this.z = z;\n};\n\nvar grad3 = [new Grad(1, 1, 0), new Grad(-1, 1, 0), new Grad(1, -1, 0), new Grad(-1, -1, 0), new Grad(1, 0, 1), new Grad(-1, 0, 1), new Grad(1, 0, -1), new Grad(-1, 0, -1), new Grad(0, 1, 1), new Grad(0, -1, 1), new Grad(0, 1, -1), new Grad(0, -1, -1)];\nvar p = [151, 160, 137, 91, 90, 15, 131, 13, 201, 95, 96, 53, 194, 233, 7, 225, 140, 36, 103, 30, 69, 142, 8, 99, 37, 240, 21, 10, 23, 190, 6, 148, 247, 120, 234, 75, 0, 26, 197, 62, 94, 252, 219, 203, 117, 35, 11, 32, 57, 177, 33, 88, 237, 149, 56, 87, 174, 20, 125, 136, 171, 168, 68, 175, 74, 165, 71, 134, 139, 48, 27, 166, 77, 146, 158, 231, 83, 111, 229, 122, 60, 211, 133, 230, 220, 105, 92, 41, 55, 46, 245, 40, 244, 102, 143, 54, 65, 25, 63, 161, 1, 216, 80, 73, 209, 76, 132, 187, 208, 89, 18, 169, 200, 196, 135, 130, 116, 188, 159, 86, 164, 100, 109, 198, 173, 186, 3, 64, 52, 217, 226, 250, 124, 123, 5, 202, 38, 147, 118, 126, 255, 82, 85, 212, 207, 206, 59, 227, 47, 16, 58, 17, 182, 189, 28, 42, 223, 183, 170, 213, 119, 248, 152, 2, 44, 154, 163, 70, 221, 153, 101, 155, 167, 43, 172, 9, 129, 22, 39, 253, 19, 98, 108, 110, 79, 113, 224, 232, 178, 185, 112, 104, 218, 246, 97, 228, 251, 34, 242, 193, 238, 210, 144, 12, 191, 179, 162, 241, 81, 51, 145, 235, 249, 14, 239, 107, 49, 192, 214, 31, 181, 199, 106, 157, 184, 84, 204, 176, 115, 121, 50, 45, 127, 4, 150, 254, 138, 236, 205, 93, 222, 114, 67, 29, 24, 72, 243, 141, 128, 195, 78, 66, 215, 61, 156, 180]; // To remove the need for index wrapping, double the permutation table length\n\nvar perm = new Array(512);\nvar gradP = new Array(512); // This isn't a very good seeding function, but it works ok. It supports 2^16\n// different seed values. Write something better if you need more seeds.\n\nvar seed = function seed(_seed) {\n  if (_seed > 0 && _seed < 1) {\n    // Scale the seed out\n    _seed *= 65536;\n  }\n\n  _seed = Math.floor(_seed);\n\n  if (_seed < 256) {\n    _seed |= _seed << 8;\n  }\n\n  for (var i = 0; i < 256; i++) {\n    var v;\n\n    if (i & 1) {\n      v = p[i] ^ _seed & 255;\n    } else {\n      v = p[i] ^ _seed >> 8 & 255;\n    }\n\n    perm[i] = perm[i + 256] = v;\n    gradP[i] = gradP[i + 256] = grad3[v % 12];\n  }\n};\nseed(0);\n/*\n  for(var i=0; i<256; i++) {\n    perm[i] = perm[i + 256] = p[i];\n    gradP[i] = gradP[i + 256] = grad3[perm[i] % 12];\n  }*/\n// Skewing and unskewing factors for 2, 3, and 4 dimensions\n\nvar F2 = 0.5 * (Math.sqrt(3) - 1);\nvar G2 = (3 - Math.sqrt(3)) / 6;\nvar F3 = 1 / 3;\nvar G3 = 1 / 6; // 2D simplex noise\n\nvar simplex2 = function simplex2(xin, yin) {\n  var n0, n1, n2; // Noise contributions from the three corners\n  // Skew the input space to determine which simplex cell we're in\n\n  var s = (xin + yin) * F2; // Hairy factor for 2D\n\n  var i = Math.floor(xin + s);\n  var j = Math.floor(yin + s);\n  var t = (i + j) * G2;\n  var x0 = xin - i + t; // The x,y distances from the cell origin, unskewed.\n\n  var y0 = yin - j + t; // For the 2D case, the simplex shape is an equilateral triangle.\n  // Determine which simplex we are in.\n\n  var i1, j1; // Offsets for second (middle) corner of simplex in (i,j) coords\n\n  if (x0 > y0) {\n    // lower triangle, XY order: (0,0)->(1,0)->(1,1)\n    i1 = 1;\n    j1 = 0;\n  } else {\n    // upper triangle, YX order: (0,0)->(0,1)->(1,1)\n    i1 = 0;\n    j1 = 1;\n  } // A step of (1,0) in (i,j) means a step of (1-c,-c) in (x,y), and\n  // a step of (0,1) in (i,j) means a step of (-c,1-c) in (x,y), where\n  // c = (3-sqrt(3))/6\n\n\n  var x1 = x0 - i1 + G2; // Offsets for middle corner in (x,y) unskewed coords\n\n  var y1 = y0 - j1 + G2;\n  var x2 = x0 - 1 + 2 * G2; // Offsets for last corner in (x,y) unskewed coords\n\n  var y2 = y0 - 1 + 2 * G2; // Work out the hashed gradient indices of the three simplex corners\n\n  i &= 255;\n  j &= 255;\n  var gi0 = gradP[i + perm[j]];\n  var gi1 = gradP[i + i1 + perm[j + j1]];\n  var gi2 = gradP[i + 1 + perm[j + 1]]; // Calculate the contribution from the three corners\n\n  var t0 = 0.5 - x0 * x0 - y0 * y0;\n\n  if (t0 < 0) {\n    n0 = 0;\n  } else {\n    t0 *= t0;\n    n0 = t0 * t0 * gi0.dot2(x0, y0); // (x,y) of grad3 used for 2D gradient\n  }\n\n  var t1 = 0.5 - x1 * x1 - y1 * y1;\n\n  if (t1 < 0) {\n    n1 = 0;\n  } else {\n    t1 *= t1;\n    n1 = t1 * t1 * gi1.dot2(x1, y1);\n  }\n\n  var t2 = 0.5 - x2 * x2 - y2 * y2;\n\n  if (t2 < 0) {\n    n2 = 0;\n  } else {\n    t2 *= t2;\n    n2 = t2 * t2 * gi2.dot2(x2, y2);\n  } // Add contributions from each corner to get the final noise value.\n  // The result is scaled to return values in the interval [-1,1].\n\n\n  return 70 * (n0 + n1 + n2);\n}; // 3D simplex noise\n\nvar simplex3 = function simplex3(xin, yin, zin) {\n  var n0, n1, n2, n3; // Noise contributions from the four corners\n  // Skew the input space to determine which simplex cell we're in\n\n  var s = (xin + yin + zin) * F3; // Hairy factor for 2D\n\n  var i = Math.floor(xin + s);\n  var j = Math.floor(yin + s);\n  var k = Math.floor(zin + s);\n  var t = (i + j + k) * G3;\n  var x0 = xin - i + t; // The x,y distances from the cell origin, unskewed.\n\n  var y0 = yin - j + t;\n  var z0 = zin - k + t; // For the 3D case, the simplex shape is a slightly irregular tetrahedron.\n  // Determine which simplex we are in.\n\n  var i1, j1, k1; // Offsets for second corner of simplex in (i,j,k) coords\n\n  var i2, j2, k2; // Offsets for third corner of simplex in (i,j,k) coords\n\n  if (x0 >= y0) {\n    if (y0 >= z0) {\n      i1 = 1;\n      j1 = 0;\n      k1 = 0;\n      i2 = 1;\n      j2 = 1;\n      k2 = 0;\n    } else if (x0 >= z0) {\n      i1 = 1;\n      j1 = 0;\n      k1 = 0;\n      i2 = 1;\n      j2 = 0;\n      k2 = 1;\n    } else {\n      i1 = 0;\n      j1 = 0;\n      k1 = 1;\n      i2 = 1;\n      j2 = 0;\n      k2 = 1;\n    }\n  } else {\n    if (y0 < z0) {\n      i1 = 0;\n      j1 = 0;\n      k1 = 1;\n      i2 = 0;\n      j2 = 1;\n      k2 = 1;\n    } else if (x0 < z0) {\n      i1 = 0;\n      j1 = 1;\n      k1 = 0;\n      i2 = 0;\n      j2 = 1;\n      k2 = 1;\n    } else {\n      i1 = 0;\n      j1 = 1;\n      k1 = 0;\n      i2 = 1;\n      j2 = 1;\n      k2 = 0;\n    }\n  } // A step of (1,0,0) in (i,j,k) means a step of (1-c,-c,-c) in (x,y,z),\n  // a step of (0,1,0) in (i,j,k) means a step of (-c,1-c,-c) in (x,y,z), and\n  // a step of (0,0,1) in (i,j,k) means a step of (-c,-c,1-c) in (x,y,z), where\n  // c = 1/6.\n\n\n  var x1 = x0 - i1 + G3; // Offsets for second corner\n\n  var y1 = y0 - j1 + G3;\n  var z1 = z0 - k1 + G3;\n  var x2 = x0 - i2 + 2 * G3; // Offsets for third corner\n\n  var y2 = y0 - j2 + 2 * G3;\n  var z2 = z0 - k2 + 2 * G3;\n  var x3 = x0 - 1 + 3 * G3; // Offsets for fourth corner\n\n  var y3 = y0 - 1 + 3 * G3;\n  var z3 = z0 - 1 + 3 * G3; // Work out the hashed gradient indices of the four simplex corners\n\n  i &= 255;\n  j &= 255;\n  k &= 255;\n  var gi0 = gradP[i + perm[j + perm[k]]];\n  var gi1 = gradP[i + i1 + perm[j + j1 + perm[k + k1]]];\n  var gi2 = gradP[i + i2 + perm[j + j2 + perm[k + k2]]];\n  var gi3 = gradP[i + 1 + perm[j + 1 + perm[k + 1]]]; // Calculate the contribution from the four corners\n\n  var t0 = 0.6 - x0 * x0 - y0 * y0 - z0 * z0;\n\n  if (t0 < 0) {\n    n0 = 0;\n  } else {\n    t0 *= t0;\n    n0 = t0 * t0 * gi0.dot3(x0, y0, z0); // (x,y) of grad3 used for 2D gradient\n  }\n\n  var t1 = 0.6 - x1 * x1 - y1 * y1 - z1 * z1;\n\n  if (t1 < 0) {\n    n1 = 0;\n  } else {\n    t1 *= t1;\n    n1 = t1 * t1 * gi1.dot3(x1, y1, z1);\n  }\n\n  var t2 = 0.6 - x2 * x2 - y2 * y2 - z2 * z2;\n\n  if (t2 < 0) {\n    n2 = 0;\n  } else {\n    t2 *= t2;\n    n2 = t2 * t2 * gi2.dot3(x2, y2, z2);\n  }\n\n  var t3 = 0.6 - x3 * x3 - y3 * y3 - z3 * z3;\n\n  if (t3 < 0) {\n    n3 = 0;\n  } else {\n    t3 *= t3;\n    n3 = t3 * t3 * gi3.dot3(x3, y3, z3);\n  } // Add contributions from each corner to get the final noise value.\n  // The result is scaled to return values in the interval [-1,1].\n\n\n  return 32 * (n0 + n1 + n2 + n3);\n}; // ##### Perlin noise stuff\n// 2D Perlin Noise\n\nvar perlin2 = function perlin2(x, y) {\n  // Find unit grid cell containing point\n  var X = Math.floor(x),\n      Y = Math.floor(y); // Get relative xy coordinates of point within that cell\n\n  x = x - X;\n  y = y - Y; // Wrap the integer cells at 255 (smaller integer period can be introduced here)\n\n  X = X & 255;\n  Y = Y & 255; // Calculate noise contributions from each of the four corners\n\n  var n00 = gradP[X + perm[Y]].dot2(x, y);\n  var n01 = gradP[X + perm[Y + 1]].dot2(x, y - 1);\n  var n10 = gradP[X + 1 + perm[Y]].dot2(x - 1, y);\n  var n11 = gradP[X + 1 + perm[Y + 1]].dot2(x - 1, y - 1); // Compute the fade curve value for x\n\n  var u = fade(x); // Interpolate the four results\n\n  return lerp(lerp(n00, n10, u), lerp(n01, n11, u), fade(y));\n}; // 3D Perlin Noise\n\nvar perlin3 = function perlin3(x, y, z) {\n  // Find unit grid cell containing point\n  var X = Math.floor(x),\n      Y = Math.floor(y),\n      Z = Math.floor(z); // Get relative xyz coordinates of point within that cell\n\n  x = x - X;\n  y = y - Y;\n  z = z - Z; // Wrap the integer cells at 255 (smaller integer period can be introduced here)\n\n  X = X & 255;\n  Y = Y & 255;\n  Z = Z & 255; // Calculate noise contributions from each of the eight corners\n\n  var n000 = gradP[X + perm[Y + perm[Z]]].dot3(x, y, z);\n  var n001 = gradP[X + perm[Y + perm[Z + 1]]].dot3(x, y, z - 1);\n  var n010 = gradP[X + perm[Y + 1 + perm[Z]]].dot3(x, y - 1, z);\n  var n011 = gradP[X + perm[Y + 1 + perm[Z + 1]]].dot3(x, y - 1, z - 1);\n  var n100 = gradP[X + 1 + perm[Y + perm[Z]]].dot3(x - 1, y, z);\n  var n101 = gradP[X + 1 + perm[Y + perm[Z + 1]]].dot3(x - 1, y, z - 1);\n  var n110 = gradP[X + 1 + perm[Y + 1 + perm[Z]]].dot3(x - 1, y - 1, z);\n  var n111 = gradP[X + 1 + perm[Y + 1 + perm[Z + 1]]].dot3(x - 1, y - 1, z - 1); // Compute the fade curve value for x, y, z\n\n  var u = fade(x);\n  var v = fade(y);\n  var w = fade(z); // Interpolate\n\n  return lerp(lerp(lerp(n000, n100, u), lerp(n001, n101, u), w), lerp(lerp(n010, n110, u), lerp(n011, n111, u), w), v);\n};\n\nvar noise = /*#__PURE__*/Object.freeze({\n  __proto__: null,\n  seed: seed,\n  simplex2: simplex2,\n  simplex3: simplex3,\n  perlin2: perlin2,\n  perlin3: perlin3\n});\n\nvar TAU = Math.PI * 2;\nvar FlashGen = /*#__PURE__*/function () {\n  function FlashGen(props) {\n    _classCallCheck(this, FlashGen);\n\n    _defineProperty(this, \"nextBurstTime\", 0);\n\n    _defineProperty(this, \"nextFlashEndTime\", 0);\n\n    _defineProperty(this, \"flashesDone\", 0);\n\n    _defineProperty(this, \"isFlashing\", false);\n\n    _defineProperty(this, \"currentCount\", 0);\n\n    _defineProperty(this, \"flashIntensity\", 0);\n\n    _defineProperty(this, \"isDecaying\", false);\n\n    _defineProperty(this, \"autoBurst\", true);\n\n    _defineProperty(this, \"decaySpeed\", 40);\n\n    _defineProperty(this, \"minInterval\", 5000);\n\n    _defineProperty(this, \"maxInterval\", 10000);\n\n    _defineProperty(this, \"minDuration\", 50);\n\n    _defineProperty(this, \"maxDuration\", 300);\n\n    _defineProperty(this, \"count\", 5);\n\n    Object.assign(this, props);\n  }\n\n  _createClass(FlashGen, [{\n    key: \"scheduleNextBurst\",\n    value: function scheduleNextBurst(currentTime) {\n      var burstInterval = Math.random() * (this.maxInterval - this.minInterval) + this.minInterval;\n      this.nextBurstTime = currentTime + burstInterval / 1000;\n      this.flashesDone = 0;\n      this.isFlashing = false;\n    }\n  }, {\n    key: \"burst\",\n    value: function burst() {\n      this.nextBurstTime = 0;\n      this.flashesDone = 0;\n      this.isFlashing = false;\n    }\n  }, {\n    key: \"update\",\n    value: function update(currentTime, delta) {\n      if (currentTime > this.nextBurstTime && this.currentCount === 0) {\n        this.currentCount = Math.floor(Math.random() * this.count) + 1;\n      }\n\n      if (this.flashesDone < this.currentCount && currentTime > this.nextBurstTime) {\n        if (!this.isFlashing) {\n          this.isFlashing = true;\n          this.flashIntensity = 1;\n          var flashDuration = Math.random() * (this.maxDuration - this.minDuration) + this.minDuration;\n          this.nextFlashEndTime = currentTime + flashDuration / 1000;\n        } else if (this.isFlashing && currentTime > this.nextFlashEndTime) {\n          this.isFlashing = false;\n          this.isDecaying = true;\n          this.flashesDone++;\n\n          if (this.flashesDone >= this.currentCount) {\n            this.currentCount = 0;\n            if (this.autoBurst) this.scheduleNextBurst(currentTime);\n          }\n        }\n      }\n\n      if (this.isDecaying) {\n        this.flashIntensity -= delta * this.decaySpeed;\n        this.flashIntensity = Math.max(0, Math.min(1, this.flashIntensity));\n\n        if (this.flashIntensity <= 0) {\n          this.isDecaying = false;\n          this.flashIntensity = 0;\n        }\n      }\n\n      return this.flashIntensity;\n    }\n  }]);\n\n  return FlashGen;\n}(); // Credits @kchapelier https://github.com/kchapelier/wavefunctioncollapse/blob/master/example/lcg.js#L22-L30\n\nfunction normalizeSeed(seed) {\n  if (typeof seed === \"number\") {\n    seed = Math.abs(seed);\n  } else if (typeof seed === \"string\") {\n    var string = seed;\n    seed = 0;\n\n    for (var i = 0; i < string.length; i++) {\n      seed = (seed + (i + 1) * (string.charCodeAt(i) % 96)) % 2147483647;\n    }\n  }\n\n  if (seed === 0) {\n    seed = 311;\n  }\n\n  return seed;\n}\n\nfunction lcgRandom(seed) {\n  var state = normalizeSeed(seed);\n  return function () {\n    var result = state * 48271 % 2147483647;\n    state = result;\n    return result / 2147483647;\n  };\n}\n\nvar Generator = function Generator(_seed) {\n  var _this = this;\n\n  _classCallCheck(this, Generator);\n\n  _defineProperty(this, \"seed\", 0);\n\n  _defineProperty(this, \"init\", function (seed) {\n    _this.seed = seed;\n    _this.value = lcgRandom(seed);\n  });\n\n  _defineProperty(this, \"value\", lcgRandom(this.seed));\n\n  this.init(_seed);\n};\nvar defaultGen = new Generator(Math.random());\n/***\n * [3D] Sphere\n */\n\nvar defaultSphere = {\n  radius: 1,\n  center: [0, 0, 0]\n}; // random on surface of sphere\n// - https://twitter.com/fermatslibrary/status/1430932503578226688\n// - https://mathworld.wolfram.com/SpherePointPicking.html\n\nfunction onSphere(buffer, sphere) {\n  var rng = arguments.length > 2 && arguments[2] !== undefined ? arguments[2] : defaultGen;\n\n  var _defaultSphere$sphere = _objectSpread2(_objectSpread2({}, defaultSphere), sphere),\n      radius = _defaultSphere$sphere.radius,\n      center = _defaultSphere$sphere.center;\n\n  for (var i = 0; i < buffer.length; i += 3) {\n    var u = rng.value();\n    var v = rng.value();\n    var theta = Math.acos(2 * v - 1);\n    var phi = TAU * u;\n    buffer[i] = Math.sin(theta) * Math.cos(phi) * radius + center[0];\n    buffer[i + 1] = Math.sin(theta) * Math.sin(phi) * radius + center[1];\n    buffer[i + 2] = Math.cos(theta) * radius + center[2];\n  }\n\n  return buffer;\n} // from \"Another Method\" https://datagenetics.com/blog/january32020/index.html\n\nfunction inSphere(buffer, sphere) {\n  var rng = arguments.length > 2 && arguments[2] !== undefined ? arguments[2] : defaultGen;\n\n  var _defaultSphere$sphere2 = _objectSpread2(_objectSpread2({}, defaultSphere), sphere),\n      radius = _defaultSphere$sphere2.radius,\n      center = _defaultSphere$sphere2.center;\n\n  for (var i = 0; i < buffer.length; i += 3) {\n    var u = Math.pow(rng.value(), 1 / 3);\n    var x = rng.value() * 2 - 1;\n    var y = rng.value() * 2 - 1;\n    var z = rng.value() * 2 - 1;\n    var mag = Math.sqrt(x * x + y * y + z * z);\n    x = u * x / mag;\n    y = u * y / mag;\n    z = u * z / mag;\n    buffer[i] = x * radius + center[0];\n    buffer[i + 1] = y * radius + center[1];\n    buffer[i + 2] = z * radius + center[2];\n  }\n\n  return buffer;\n}\n/***\n * [2D] Circle\n */\n\nvar defaultCircle = {\n  radius: 1,\n  center: [0, 0]\n}; // random circle https://stackoverflow.com/a/50746409\n\nfunction inCircle(buffer, circle) {\n  var rng = arguments.length > 2 && arguments[2] !== undefined ? arguments[2] : defaultGen;\n\n  var _defaultCircle$circle = _objectSpread2(_objectSpread2({}, defaultCircle), circle),\n      radius = _defaultCircle$circle.radius,\n      center = _defaultCircle$circle.center;\n\n  for (var i = 0; i < buffer.length; i += 2) {\n    var r = radius * Math.sqrt(rng.value());\n    var theta = rng.value() * TAU;\n    buffer[i] = Math.sin(theta) * r + center[0];\n    buffer[i + 1] = Math.cos(theta) * r + center[1];\n  }\n\n  return buffer;\n}\nfunction onCircle(buffer, circle) {\n  var rng = arguments.length > 2 && arguments[2] !== undefined ? arguments[2] : defaultGen;\n\n  var _defaultCircle$circle2 = _objectSpread2(_objectSpread2({}, defaultCircle), circle),\n      radius = _defaultCircle$circle2.radius,\n      center = _defaultCircle$circle2.center;\n\n  for (var i = 0; i < buffer.length; i += 2) {\n    var theta = rng.value() * TAU;\n    buffer[i] = Math.sin(theta) * radius + center[0];\n    buffer[i + 1] = Math.cos(theta) * radius + center[1];\n  }\n\n  return buffer;\n}\n/**\n * [2D] Plane\n */\n\nvar defaultRect = {\n  sides: 1,\n  center: [0, 0]\n};\nfunction inRect(buffer, rect) {\n  var rng = arguments.length > 2 && arguments[2] !== undefined ? arguments[2] : defaultGen;\n\n  var _defaultRect$rect = _objectSpread2(_objectSpread2({}, defaultRect), rect),\n      sides = _defaultRect$rect.sides,\n      center = _defaultRect$rect.center;\n\n  var sideX = typeof sides === \"number\" ? sides : sides[0];\n  var sideY = typeof sides === \"number\" ? sides : sides[1];\n\n  for (var i = 0; i < buffer.length; i += 2) {\n    buffer[i] = (rng.value() - 0.5) * sideX + center[0];\n    buffer[i + 1] = (rng.value() - 0.5) * sideY + center[1];\n  }\n\n  return buffer;\n}\nfunction onRect(buffer, rect) {\n  return buffer;\n}\n/***\n * [3D] Box\n */\n\nfunction inBox(buffer, box) {\n  var rng = arguments.length > 2 && arguments[2] !== undefined ? arguments[2] : defaultGen;\n\n  var _defaultBox$box = _objectSpread2(_objectSpread2({}, defaultBox), box),\n      sides = _defaultBox$box.sides,\n      center = _defaultBox$box.center;\n\n  var sideX = typeof sides === \"number\" ? sides : sides[0];\n  var sideY = typeof sides === \"number\" ? sides : sides[1];\n  var sideZ = typeof sides === \"number\" ? sides : sides[2];\n\n  for (var i = 0; i < buffer.length; i += 3) {\n    buffer[i] = (rng.value() - 0.5) * sideX + center[0];\n    buffer[i + 1] = (rng.value() - 0.5) * sideY + center[1];\n    buffer[i + 2] = (rng.value() - 0.5) * sideZ + center[2];\n  }\n\n  return buffer;\n}\nvar defaultBox = {\n  sides: 1,\n  center: [0, 0, 0]\n};\nfunction onBox(buffer, box) {\n  var rng = arguments.length > 2 && arguments[2] !== undefined ? arguments[2] : defaultGen;\n\n  var _defaultBox$box2 = _objectSpread2(_objectSpread2({}, defaultBox), box),\n      sides = _defaultBox$box2.sides,\n      center = _defaultBox$box2.center;\n\n  var sideX = typeof sides === \"number\" ? sides : sides[0];\n  var sideY = typeof sides === \"number\" ? sides : sides[1];\n  var sideZ = typeof sides === \"number\" ? sides : sides[2];\n\n  for (var i = 0; i < buffer.length; i += 3) {\n    buffer[i] = (rng.value() - 0.5) * sideX + center[0];\n    buffer[i + 1] = (rng.value() - 0.5) * sideY + center[1];\n    buffer[i + 2] = (rng.value() - 0.5) * sideZ + center[2];\n  }\n\n  return buffer;\n}\n\nvar index = /*#__PURE__*/Object.freeze({\n  __proto__: null,\n  FlashGen: FlashGen,\n  Generator: Generator,\n  onSphere: onSphere,\n  inSphere: inSphere,\n  inCircle: inCircle,\n  onCircle: onCircle,\n  inRect: inRect,\n  onRect: onRect,\n  inBox: inBox,\n  onBox: onBox,\n  noise: noise\n});\n\nexport { FlashGen as F, Generator as G, inSphere as a, inCircle as b, onCircle as c, inRect as d, onRect as e, inBox as f, onBox as g, index as i, noise as n, onSphere as o };\n"], "mappings": "AAAA,SAASA,CAAC,IAAIC,eAAe,EAAEC,CAAC,IAAIC,cAAc,QAAQ,iCAAiC;AAC3F,SAASD,CAAC,IAAIE,eAAe,QAAQ,kCAAkC;AACvE,SAASC,CAAC,IAAIC,IAAI,EAAEC,CAAC,IAAIC,IAAI,QAAQ,wBAAwB;AAE7D,SAASC,iBAAiBA,CAACC,MAAM,EAAEC,KAAK,EAAE;EACxC,KAAK,IAAIC,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGD,KAAK,CAACE,MAAM,EAAED,CAAC,EAAE,EAAE;IACrC,IAAIE,UAAU,GAAGH,KAAK,CAACC,CAAC,CAAC;IACzBE,UAAU,CAACC,UAAU,GAAGD,UAAU,CAACC,UAAU,IAAI,KAAK;IACtDD,UAAU,CAACE,YAAY,GAAG,IAAI;IAC9B,IAAI,OAAO,IAAIF,UAAU,EAAEA,UAAU,CAACG,QAAQ,GAAG,IAAI;IACrDC,MAAM,CAACC,cAAc,CAACT,MAAM,EAAEI,UAAU,CAACM,GAAG,EAAEN,UAAU,CAAC;EAC3D;AACF;AAEA,SAASO,YAAYA,CAACC,WAAW,EAAEC,UAAU,EAAEC,WAAW,EAAE;EAC1D,IAAID,UAAU,EAAEd,iBAAiB,CAACa,WAAW,CAACG,SAAS,EAAEF,UAAU,CAAC;EACpE,IAAIC,WAAW,EAAEf,iBAAiB,CAACa,WAAW,EAAEE,WAAW,CAAC;EAC5D,OAAOF,WAAW;AACpB;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA,IAAII,IAAI,GAAG,SAASA,IAAIA,CAACC,CAAC,EAAEC,CAAC,EAAEC,CAAC,EAAE;EAChC,IAAIC,KAAK,GAAG,IAAI;EAEhB1B,eAAe,CAAC,IAAI,EAAEsB,IAAI,CAAC;EAE3BzB,eAAe,CAAC,IAAI,EAAE,MAAM,EAAE,UAAU0B,CAAC,EAAEC,CAAC,EAAE;IAC5C,OAAOE,KAAK,CAACH,CAAC,GAAGA,CAAC,GAAGG,KAAK,CAACF,CAAC,GAAGA,CAAC;EAClC,CAAC,CAAC;EAEF3B,eAAe,CAAC,IAAI,EAAE,MAAM,EAAE,UAAU0B,CAAC,EAAEC,CAAC,EAAEC,CAAC,EAAE;IAC/C,OAAOC,KAAK,CAACH,CAAC,GAAGA,CAAC,GAAGG,KAAK,CAACF,CAAC,GAAGA,CAAC,GAAGE,KAAK,CAACD,CAAC,GAAGA,CAAC;EAChD,CAAC,CAAC;EAEF,IAAI,CAACF,CAAC,GAAGA,CAAC;EACV,IAAI,CAACC,CAAC,GAAGA,CAAC;EACV,IAAI,CAACC,CAAC,GAAGA,CAAC;AACZ,CAAC;AAED,IAAIE,KAAK,GAAG,CAAC,IAAIL,IAAI,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,EAAE,IAAIA,IAAI,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,EAAE,IAAIA,IAAI,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,IAAIA,IAAI,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,IAAIA,IAAI,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,EAAE,IAAIA,IAAI,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,EAAE,IAAIA,IAAI,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,IAAIA,IAAI,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,IAAIA,IAAI,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,EAAE,IAAIA,IAAI,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,IAAIA,IAAI,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,IAAIA,IAAI,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;AAC5P,IAAIM,CAAC,GAAG,CAAC,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,GAAG,EAAE,EAAE,EAAE,GAAG,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,GAAG,EAAE,GAAG,EAAE,CAAC,EAAE,GAAG,EAAE,GAAG,EAAE,EAAE,EAAE,GAAG,EAAE,EAAE,EAAE,EAAE,EAAE,GAAG,EAAE,CAAC,EAAE,EAAE,EAAE,EAAE,EAAE,GAAG,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,GAAG,EAAE,CAAC,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,EAAE,EAAE,CAAC,EAAE,EAAE,EAAE,GAAG,EAAE,EAAE,EAAE,EAAE,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,GAAG,EAAE,EAAE,EAAE,EAAE,EAAE,GAAG,EAAE,GAAG,EAAE,EAAE,EAAE,EAAE,EAAE,GAAG,EAAE,EAAE,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,EAAE,EAAE,GAAG,EAAE,EAAE,EAAE,GAAG,EAAE,EAAE,EAAE,GAAG,EAAE,GAAG,EAAE,EAAE,EAAE,EAAE,EAAE,GAAG,EAAE,EAAE,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,EAAE,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,EAAE,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,GAAG,EAAE,EAAE,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,GAAG,EAAE,CAAC,EAAE,GAAG,EAAE,EAAE,EAAE,EAAE,EAAE,GAAG,EAAE,EAAE,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,EAAE,EAAE,EAAE,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,EAAE,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,CAAC,EAAE,EAAE,EAAE,EAAE,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,CAAC,EAAE,GAAG,EAAE,EAAE,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,EAAE,EAAE,EAAE,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,EAAE,EAAE,GAAG,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,GAAG,EAAE,GAAG,EAAE,EAAE,EAAE,EAAE,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,CAAC,EAAE,EAAE,EAAE,GAAG,EAAE,GAAG,EAAE,EAAE,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,EAAE,EAAE,GAAG,EAAE,CAAC,EAAE,GAAG,EAAE,EAAE,EAAE,EAAE,EAAE,GAAG,EAAE,EAAE,EAAE,EAAE,EAAE,GAAG,EAAE,GAAG,EAAE,EAAE,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,EAAE,EAAE,GAAG,EAAE,GAAG,EAAE,EAAE,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,EAAE,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,EAAE,EAAE,EAAE,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,EAAE,EAAE,GAAG,EAAE,GAAG,EAAE,EAAE,EAAE,GAAG,EAAE,GAAG,EAAE,EAAE,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,EAAE,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,EAAE,EAAE,EAAE,EAAE,GAAG,EAAE,CAAC,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,EAAE,EAAE,GAAG,EAAE,GAAG,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,EAAE,EAAE,EAAE,EAAE,GAAG,EAAE,EAAE,EAAE,GAAG,EAAE,GAAG,CAAC,CAAC,CAAC;;AAE5pC,IAAIC,IAAI,GAAG,IAAIC,KAAK,CAAC,GAAG,CAAC;AACzB,IAAIC,KAAK,GAAG,IAAID,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC;AAC5B;;AAEA,IAAIE,IAAI,GAAG,SAASA,IAAIA,CAACC,KAAK,EAAE;EAC9B,IAAIA,KAAK,GAAG,CAAC,IAAIA,KAAK,GAAG,CAAC,EAAE;IAC1B;IACAA,KAAK,IAAI,KAAK;EAChB;EAEAA,KAAK,GAAGC,IAAI,CAACC,KAAK,CAACF,KAAK,CAAC;EAEzB,IAAIA,KAAK,GAAG,GAAG,EAAE;IACfA,KAAK,IAAIA,KAAK,IAAI,CAAC;EACrB;EAEA,KAAK,IAAIzB,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAG,GAAG,EAAEA,CAAC,EAAE,EAAE;IAC5B,IAAI4B,CAAC;IAEL,IAAI5B,CAAC,GAAG,CAAC,EAAE;MACT4B,CAAC,GAAGR,CAAC,CAACpB,CAAC,CAAC,GAAGyB,KAAK,GAAG,GAAG;IACxB,CAAC,MAAM;MACLG,CAAC,GAAGR,CAAC,CAACpB,CAAC,CAAC,GAAGyB,KAAK,IAAI,CAAC,GAAG,GAAG;IAC7B;IAEAJ,IAAI,CAACrB,CAAC,CAAC,GAAGqB,IAAI,CAACrB,CAAC,GAAG,GAAG,CAAC,GAAG4B,CAAC;IAC3BL,KAAK,CAACvB,CAAC,CAAC,GAAGuB,KAAK,CAACvB,CAAC,GAAG,GAAG,CAAC,GAAGmB,KAAK,CAACS,CAAC,GAAG,EAAE,CAAC;EAC3C;AACF,CAAC;AACDJ,IAAI,CAAC,CAAC,CAAC;AACP;AACA;AACA;AACA;AACA;AACA;;AAEA,IAAIK,EAAE,GAAG,GAAG,IAAIH,IAAI,CAACI,IAAI,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC;AACjC,IAAIC,EAAE,GAAG,CAAC,CAAC,GAAGL,IAAI,CAACI,IAAI,CAAC,CAAC,CAAC,IAAI,CAAC;AAC/B,IAAIE,EAAE,GAAG,CAAC,GAAG,CAAC;AACd,IAAIC,EAAE,GAAG,CAAC,GAAG,CAAC,CAAC,CAAC;;AAEhB,IAAIC,QAAQ,GAAG,SAASA,QAAQA,CAACC,GAAG,EAAEC,GAAG,EAAE;EACzC,IAAIC,EAAE,EAAEC,EAAE,EAAEC,EAAE,CAAC,CAAC;EAChB;;EAEA,IAAIC,CAAC,GAAG,CAACL,GAAG,GAAGC,GAAG,IAAIP,EAAE,CAAC,CAAC;;EAE1B,IAAI7B,CAAC,GAAG0B,IAAI,CAACC,KAAK,CAACQ,GAAG,GAAGK,CAAC,CAAC;EAC3B,IAAIC,CAAC,GAAGf,IAAI,CAACC,KAAK,CAACS,GAAG,GAAGI,CAAC,CAAC;EAC3B,IAAIE,CAAC,GAAG,CAAC1C,CAAC,GAAGyC,CAAC,IAAIV,EAAE;EACpB,IAAIY,EAAE,GAAGR,GAAG,GAAGnC,CAAC,GAAG0C,CAAC,CAAC,CAAC;;EAEtB,IAAIE,EAAE,GAAGR,GAAG,GAAGK,CAAC,GAAGC,CAAC,CAAC,CAAC;EACtB;;EAEA,IAAIG,EAAE,EAAEC,EAAE,CAAC,CAAC;;EAEZ,IAAIH,EAAE,GAAGC,EAAE,EAAE;IACX;IACAC,EAAE,GAAG,CAAC;IACNC,EAAE,GAAG,CAAC;EACR,CAAC,MAAM;IACL;IACAD,EAAE,GAAG,CAAC;IACNC,EAAE,GAAG,CAAC;EACR,CAAC,CAAC;EACF;EACA;;EAGA,IAAIC,EAAE,GAAGJ,EAAE,GAAGE,EAAE,GAAGd,EAAE,CAAC,CAAC;;EAEvB,IAAIiB,EAAE,GAAGJ,EAAE,GAAGE,EAAE,GAAGf,EAAE;EACrB,IAAIkB,EAAE,GAAGN,EAAE,GAAG,CAAC,GAAG,CAAC,GAAGZ,EAAE,CAAC,CAAC;;EAE1B,IAAImB,EAAE,GAAGN,EAAE,GAAG,CAAC,GAAG,CAAC,GAAGb,EAAE,CAAC,CAAC;;EAE1B/B,CAAC,IAAI,GAAG;EACRyC,CAAC,IAAI,GAAG;EACR,IAAIU,GAAG,GAAG5B,KAAK,CAACvB,CAAC,GAAGqB,IAAI,CAACoB,CAAC,CAAC,CAAC;EAC5B,IAAIW,GAAG,GAAG7B,KAAK,CAACvB,CAAC,GAAG6C,EAAE,GAAGxB,IAAI,CAACoB,CAAC,GAAGK,EAAE,CAAC,CAAC;EACtC,IAAIO,GAAG,GAAG9B,KAAK,CAACvB,CAAC,GAAG,CAAC,GAAGqB,IAAI,CAACoB,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC;;EAEtC,IAAIa,EAAE,GAAG,GAAG,GAAGX,EAAE,GAAGA,EAAE,GAAGC,EAAE,GAAGA,EAAE;EAEhC,IAAIU,EAAE,GAAG,CAAC,EAAE;IACVjB,EAAE,GAAG,CAAC;EACR,CAAC,MAAM;IACLiB,EAAE,IAAIA,EAAE;IACRjB,EAAE,GAAGiB,EAAE,GAAGA,EAAE,GAAGH,GAAG,CAACI,IAAI,CAACZ,EAAE,EAAEC,EAAE,CAAC,CAAC,CAAC;EACnC;EAEA,IAAIY,EAAE,GAAG,GAAG,GAAGT,EAAE,GAAGA,EAAE,GAAGC,EAAE,GAAGA,EAAE;EAEhC,IAAIQ,EAAE,GAAG,CAAC,EAAE;IACVlB,EAAE,GAAG,CAAC;EACR,CAAC,MAAM;IACLkB,EAAE,IAAIA,EAAE;IACRlB,EAAE,GAAGkB,EAAE,GAAGA,EAAE,GAAGJ,GAAG,CAACG,IAAI,CAACR,EAAE,EAAEC,EAAE,CAAC;EACjC;EAEA,IAAIS,EAAE,GAAG,GAAG,GAAGR,EAAE,GAAGA,EAAE,GAAGC,EAAE,GAAGA,EAAE;EAEhC,IAAIO,EAAE,GAAG,CAAC,EAAE;IACVlB,EAAE,GAAG,CAAC;EACR,CAAC,MAAM;IACLkB,EAAE,IAAIA,EAAE;IACRlB,EAAE,GAAGkB,EAAE,GAAGA,EAAE,GAAGJ,GAAG,CAACE,IAAI,CAACN,EAAE,EAAEC,EAAE,CAAC;EACjC,CAAC,CAAC;EACF;;EAGA,OAAO,EAAE,IAAIb,EAAE,GAAGC,EAAE,GAAGC,EAAE,CAAC;AAC5B,CAAC,CAAC,CAAC;;AAEH,IAAImB,QAAQ,GAAG,SAASA,QAAQA,CAACvB,GAAG,EAAEC,GAAG,EAAEuB,GAAG,EAAE;EAC9C,IAAItB,EAAE,EAAEC,EAAE,EAAEC,EAAE,EAAEqB,EAAE,CAAC,CAAC;EACpB;;EAEA,IAAIpB,CAAC,GAAG,CAACL,GAAG,GAAGC,GAAG,GAAGuB,GAAG,IAAI3B,EAAE,CAAC,CAAC;;EAEhC,IAAIhC,CAAC,GAAG0B,IAAI,CAACC,KAAK,CAACQ,GAAG,GAAGK,CAAC,CAAC;EAC3B,IAAIC,CAAC,GAAGf,IAAI,CAACC,KAAK,CAACS,GAAG,GAAGI,CAAC,CAAC;EAC3B,IAAIqB,CAAC,GAAGnC,IAAI,CAACC,KAAK,CAACgC,GAAG,GAAGnB,CAAC,CAAC;EAC3B,IAAIE,CAAC,GAAG,CAAC1C,CAAC,GAAGyC,CAAC,GAAGoB,CAAC,IAAI5B,EAAE;EACxB,IAAIU,EAAE,GAAGR,GAAG,GAAGnC,CAAC,GAAG0C,CAAC,CAAC,CAAC;;EAEtB,IAAIE,EAAE,GAAGR,GAAG,GAAGK,CAAC,GAAGC,CAAC;EACpB,IAAIoB,EAAE,GAAGH,GAAG,GAAGE,CAAC,GAAGnB,CAAC,CAAC,CAAC;EACtB;;EAEA,IAAIG,EAAE,EAAEC,EAAE,EAAEiB,EAAE,CAAC,CAAC;;EAEhB,IAAIC,EAAE,EAAEC,EAAE,EAAEC,EAAE,CAAC,CAAC;;EAEhB,IAAIvB,EAAE,IAAIC,EAAE,EAAE;IACZ,IAAIA,EAAE,IAAIkB,EAAE,EAAE;MACZjB,EAAE,GAAG,CAAC;MACNC,EAAE,GAAG,CAAC;MACNiB,EAAE,GAAG,CAAC;MACNC,EAAE,GAAG,CAAC;MACNC,EAAE,GAAG,CAAC;MACNC,EAAE,GAAG,CAAC;IACR,CAAC,MAAM,IAAIvB,EAAE,IAAImB,EAAE,EAAE;MACnBjB,EAAE,GAAG,CAAC;MACNC,EAAE,GAAG,CAAC;MACNiB,EAAE,GAAG,CAAC;MACNC,EAAE,GAAG,CAAC;MACNC,EAAE,GAAG,CAAC;MACNC,EAAE,GAAG,CAAC;IACR,CAAC,MAAM;MACLrB,EAAE,GAAG,CAAC;MACNC,EAAE,GAAG,CAAC;MACNiB,EAAE,GAAG,CAAC;MACNC,EAAE,GAAG,CAAC;MACNC,EAAE,GAAG,CAAC;MACNC,EAAE,GAAG,CAAC;IACR;EACF,CAAC,MAAM;IACL,IAAItB,EAAE,GAAGkB,EAAE,EAAE;MACXjB,EAAE,GAAG,CAAC;MACNC,EAAE,GAAG,CAAC;MACNiB,EAAE,GAAG,CAAC;MACNC,EAAE,GAAG,CAAC;MACNC,EAAE,GAAG,CAAC;MACNC,EAAE,GAAG,CAAC;IACR,CAAC,MAAM,IAAIvB,EAAE,GAAGmB,EAAE,EAAE;MAClBjB,EAAE,GAAG,CAAC;MACNC,EAAE,GAAG,CAAC;MACNiB,EAAE,GAAG,CAAC;MACNC,EAAE,GAAG,CAAC;MACNC,EAAE,GAAG,CAAC;MACNC,EAAE,GAAG,CAAC;IACR,CAAC,MAAM;MACLrB,EAAE,GAAG,CAAC;MACNC,EAAE,GAAG,CAAC;MACNiB,EAAE,GAAG,CAAC;MACNC,EAAE,GAAG,CAAC;MACNC,EAAE,GAAG,CAAC;MACNC,EAAE,GAAG,CAAC;IACR;EACF,CAAC,CAAC;EACF;EACA;EACA;;EAGA,IAAInB,EAAE,GAAGJ,EAAE,GAAGE,EAAE,GAAGZ,EAAE,CAAC,CAAC;;EAEvB,IAAIe,EAAE,GAAGJ,EAAE,GAAGE,EAAE,GAAGb,EAAE;EACrB,IAAIkC,EAAE,GAAGL,EAAE,GAAGC,EAAE,GAAG9B,EAAE;EACrB,IAAIgB,EAAE,GAAGN,EAAE,GAAGqB,EAAE,GAAG,CAAC,GAAG/B,EAAE,CAAC,CAAC;;EAE3B,IAAIiB,EAAE,GAAGN,EAAE,GAAGqB,EAAE,GAAG,CAAC,GAAGhC,EAAE;EACzB,IAAImC,EAAE,GAAGN,EAAE,GAAGI,EAAE,GAAG,CAAC,GAAGjC,EAAE;EACzB,IAAIoC,EAAE,GAAG1B,EAAE,GAAG,CAAC,GAAG,CAAC,GAAGV,EAAE,CAAC,CAAC;;EAE1B,IAAIqC,EAAE,GAAG1B,EAAE,GAAG,CAAC,GAAG,CAAC,GAAGX,EAAE;EACxB,IAAIsC,EAAE,GAAGT,EAAE,GAAG,CAAC,GAAG,CAAC,GAAG7B,EAAE,CAAC,CAAC;;EAE1BjC,CAAC,IAAI,GAAG;EACRyC,CAAC,IAAI,GAAG;EACRoB,CAAC,IAAI,GAAG;EACR,IAAIV,GAAG,GAAG5B,KAAK,CAACvB,CAAC,GAAGqB,IAAI,CAACoB,CAAC,GAAGpB,IAAI,CAACwC,CAAC,CAAC,CAAC,CAAC;EACtC,IAAIT,GAAG,GAAG7B,KAAK,CAACvB,CAAC,GAAG6C,EAAE,GAAGxB,IAAI,CAACoB,CAAC,GAAGK,EAAE,GAAGzB,IAAI,CAACwC,CAAC,GAAGE,EAAE,CAAC,CAAC,CAAC;EACrD,IAAIV,GAAG,GAAG9B,KAAK,CAACvB,CAAC,GAAGgE,EAAE,GAAG3C,IAAI,CAACoB,CAAC,GAAGwB,EAAE,GAAG5C,IAAI,CAACwC,CAAC,GAAGK,EAAE,CAAC,CAAC,CAAC;EACrD,IAAIM,GAAG,GAAGjD,KAAK,CAACvB,CAAC,GAAG,CAAC,GAAGqB,IAAI,CAACoB,CAAC,GAAG,CAAC,GAAGpB,IAAI,CAACwC,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;;EAEpD,IAAIP,EAAE,GAAG,GAAG,GAAGX,EAAE,GAAGA,EAAE,GAAGC,EAAE,GAAGA,EAAE,GAAGkB,EAAE,GAAGA,EAAE;EAE1C,IAAIR,EAAE,GAAG,CAAC,EAAE;IACVjB,EAAE,GAAG,CAAC;EACR,CAAC,MAAM;IACLiB,EAAE,IAAIA,EAAE;IACRjB,EAAE,GAAGiB,EAAE,GAAGA,EAAE,GAAGH,GAAG,CAACsB,IAAI,CAAC9B,EAAE,EAAEC,EAAE,EAAEkB,EAAE,CAAC,CAAC,CAAC;EACvC;EAEA,IAAIN,EAAE,GAAG,GAAG,GAAGT,EAAE,GAAGA,EAAE,GAAGC,EAAE,GAAGA,EAAE,GAAGmB,EAAE,GAAGA,EAAE;EAE1C,IAAIX,EAAE,GAAG,CAAC,EAAE;IACVlB,EAAE,GAAG,CAAC;EACR,CAAC,MAAM;IACLkB,EAAE,IAAIA,EAAE;IACRlB,EAAE,GAAGkB,EAAE,GAAGA,EAAE,GAAGJ,GAAG,CAACqB,IAAI,CAAC1B,EAAE,EAAEC,EAAE,EAAEmB,EAAE,CAAC;EACrC;EAEA,IAAIV,EAAE,GAAG,GAAG,GAAGR,EAAE,GAAGA,EAAE,GAAGC,EAAE,GAAGA,EAAE,GAAGkB,EAAE,GAAGA,EAAE;EAE1C,IAAIX,EAAE,GAAG,CAAC,EAAE;IACVlB,EAAE,GAAG,CAAC;EACR,CAAC,MAAM;IACLkB,EAAE,IAAIA,EAAE;IACRlB,EAAE,GAAGkB,EAAE,GAAGA,EAAE,GAAGJ,GAAG,CAACoB,IAAI,CAACxB,EAAE,EAAEC,EAAE,EAAEkB,EAAE,CAAC;EACrC;EAEA,IAAIM,EAAE,GAAG,GAAG,GAAGL,EAAE,GAAGA,EAAE,GAAGC,EAAE,GAAGA,EAAE,GAAGC,EAAE,GAAGA,EAAE;EAE1C,IAAIG,EAAE,GAAG,CAAC,EAAE;IACVd,EAAE,GAAG,CAAC;EACR,CAAC,MAAM;IACLc,EAAE,IAAIA,EAAE;IACRd,EAAE,GAAGc,EAAE,GAAGA,EAAE,GAAGF,GAAG,CAACC,IAAI,CAACJ,EAAE,EAAEC,EAAE,EAAEC,EAAE,CAAC;EACrC,CAAC,CAAC;EACF;;EAGA,OAAO,EAAE,IAAIlC,EAAE,GAAGC,EAAE,GAAGC,EAAE,GAAGqB,EAAE,CAAC;AACjC,CAAC,CAAC,CAAC;AACH;;AAEA,IAAIe,OAAO,GAAG,SAASA,OAAOA,CAAC5D,CAAC,EAAEC,CAAC,EAAE;EACnC;EACA,IAAI4D,CAAC,GAAGlD,IAAI,CAACC,KAAK,CAACZ,CAAC,CAAC;IACjB8D,CAAC,GAAGnD,IAAI,CAACC,KAAK,CAACX,CAAC,CAAC,CAAC,CAAC;;EAEvBD,CAAC,GAAGA,CAAC,GAAG6D,CAAC;EACT5D,CAAC,GAAGA,CAAC,GAAG6D,CAAC,CAAC,CAAC;;EAEXD,CAAC,GAAGA,CAAC,GAAG,GAAG;EACXC,CAAC,GAAGA,CAAC,GAAG,GAAG,CAAC,CAAC;;EAEb,IAAIC,GAAG,GAAGvD,KAAK,CAACqD,CAAC,GAAGvD,IAAI,CAACwD,CAAC,CAAC,CAAC,CAACtB,IAAI,CAACxC,CAAC,EAAEC,CAAC,CAAC;EACvC,IAAI+D,GAAG,GAAGxD,KAAK,CAACqD,CAAC,GAAGvD,IAAI,CAACwD,CAAC,GAAG,CAAC,CAAC,CAAC,CAACtB,IAAI,CAACxC,CAAC,EAAEC,CAAC,GAAG,CAAC,CAAC;EAC/C,IAAIgE,GAAG,GAAGzD,KAAK,CAACqD,CAAC,GAAG,CAAC,GAAGvD,IAAI,CAACwD,CAAC,CAAC,CAAC,CAACtB,IAAI,CAACxC,CAAC,GAAG,CAAC,EAAEC,CAAC,CAAC;EAC/C,IAAIiE,GAAG,GAAG1D,KAAK,CAACqD,CAAC,GAAG,CAAC,GAAGvD,IAAI,CAACwD,CAAC,GAAG,CAAC,CAAC,CAAC,CAACtB,IAAI,CAACxC,CAAC,GAAG,CAAC,EAAEC,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC;;EAEzD,IAAIkE,CAAC,GAAGtF,IAAI,CAACmB,CAAC,CAAC,CAAC,CAAC;;EAEjB,OAAOrB,IAAI,CAACA,IAAI,CAACoF,GAAG,EAAEE,GAAG,EAAEE,CAAC,CAAC,EAAExF,IAAI,CAACqF,GAAG,EAAEE,GAAG,EAAEC,CAAC,CAAC,EAAEtF,IAAI,CAACoB,CAAC,CAAC,CAAC;AAC5D,CAAC,CAAC,CAAC;;AAEH,IAAImE,OAAO,GAAG,SAASA,OAAOA,CAACpE,CAAC,EAAEC,CAAC,EAAEC,CAAC,EAAE;EACtC;EACA,IAAI2D,CAAC,GAAGlD,IAAI,CAACC,KAAK,CAACZ,CAAC,CAAC;IACjB8D,CAAC,GAAGnD,IAAI,CAACC,KAAK,CAACX,CAAC,CAAC;IACjBoE,CAAC,GAAG1D,IAAI,CAACC,KAAK,CAACV,CAAC,CAAC,CAAC,CAAC;;EAEvBF,CAAC,GAAGA,CAAC,GAAG6D,CAAC;EACT5D,CAAC,GAAGA,CAAC,GAAG6D,CAAC;EACT5D,CAAC,GAAGA,CAAC,GAAGmE,CAAC,CAAC,CAAC;;EAEXR,CAAC,GAAGA,CAAC,GAAG,GAAG;EACXC,CAAC,GAAGA,CAAC,GAAG,GAAG;EACXO,CAAC,GAAGA,CAAC,GAAG,GAAG,CAAC,CAAC;;EAEb,IAAIC,IAAI,GAAG9D,KAAK,CAACqD,CAAC,GAAGvD,IAAI,CAACwD,CAAC,GAAGxD,IAAI,CAAC+D,CAAC,CAAC,CAAC,CAAC,CAACX,IAAI,CAAC1D,CAAC,EAAEC,CAAC,EAAEC,CAAC,CAAC;EACrD,IAAIqE,IAAI,GAAG/D,KAAK,CAACqD,CAAC,GAAGvD,IAAI,CAACwD,CAAC,GAAGxD,IAAI,CAAC+D,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAACX,IAAI,CAAC1D,CAAC,EAAEC,CAAC,EAAEC,CAAC,GAAG,CAAC,CAAC;EAC7D,IAAIsE,IAAI,GAAGhE,KAAK,CAACqD,CAAC,GAAGvD,IAAI,CAACwD,CAAC,GAAG,CAAC,GAAGxD,IAAI,CAAC+D,CAAC,CAAC,CAAC,CAAC,CAACX,IAAI,CAAC1D,CAAC,EAAEC,CAAC,GAAG,CAAC,EAAEC,CAAC,CAAC;EAC7D,IAAIuE,IAAI,GAAGjE,KAAK,CAACqD,CAAC,GAAGvD,IAAI,CAACwD,CAAC,GAAG,CAAC,GAAGxD,IAAI,CAAC+D,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAACX,IAAI,CAAC1D,CAAC,EAAEC,CAAC,GAAG,CAAC,EAAEC,CAAC,GAAG,CAAC,CAAC;EACrE,IAAIwE,IAAI,GAAGlE,KAAK,CAACqD,CAAC,GAAG,CAAC,GAAGvD,IAAI,CAACwD,CAAC,GAAGxD,IAAI,CAAC+D,CAAC,CAAC,CAAC,CAAC,CAACX,IAAI,CAAC1D,CAAC,GAAG,CAAC,EAAEC,CAAC,EAAEC,CAAC,CAAC;EAC7D,IAAIyE,IAAI,GAAGnE,KAAK,CAACqD,CAAC,GAAG,CAAC,GAAGvD,IAAI,CAACwD,CAAC,GAAGxD,IAAI,CAAC+D,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAACX,IAAI,CAAC1D,CAAC,GAAG,CAAC,EAAEC,CAAC,EAAEC,CAAC,GAAG,CAAC,CAAC;EACrE,IAAI0E,IAAI,GAAGpE,KAAK,CAACqD,CAAC,GAAG,CAAC,GAAGvD,IAAI,CAACwD,CAAC,GAAG,CAAC,GAAGxD,IAAI,CAAC+D,CAAC,CAAC,CAAC,CAAC,CAACX,IAAI,CAAC1D,CAAC,GAAG,CAAC,EAAEC,CAAC,GAAG,CAAC,EAAEC,CAAC,CAAC;EACrE,IAAI2E,IAAI,GAAGrE,KAAK,CAACqD,CAAC,GAAG,CAAC,GAAGvD,IAAI,CAACwD,CAAC,GAAG,CAAC,GAAGxD,IAAI,CAAC+D,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAACX,IAAI,CAAC1D,CAAC,GAAG,CAAC,EAAEC,CAAC,GAAG,CAAC,EAAEC,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC;;EAE/E,IAAIiE,CAAC,GAAGtF,IAAI,CAACmB,CAAC,CAAC;EACf,IAAIa,CAAC,GAAGhC,IAAI,CAACoB,CAAC,CAAC;EACf,IAAI6E,CAAC,GAAGjG,IAAI,CAACqB,CAAC,CAAC,CAAC,CAAC;;EAEjB,OAAOvB,IAAI,CAACA,IAAI,CAACA,IAAI,CAAC2F,IAAI,EAAEI,IAAI,EAAEP,CAAC,CAAC,EAAExF,IAAI,CAAC4F,IAAI,EAAEI,IAAI,EAAER,CAAC,CAAC,EAAEW,CAAC,CAAC,EAAEnG,IAAI,CAACA,IAAI,CAAC6F,IAAI,EAAEI,IAAI,EAAET,CAAC,CAAC,EAAExF,IAAI,CAAC8F,IAAI,EAAEI,IAAI,EAAEV,CAAC,CAAC,EAAEW,CAAC,CAAC,EAAEjE,CAAC,CAAC;AACtH,CAAC;AAED,IAAIkE,KAAK,GAAG,aAAaxF,MAAM,CAACyF,MAAM,CAAC;EACrCC,SAAS,EAAE,IAAI;EACfxE,IAAI,EAAEA,IAAI;EACVU,QAAQ,EAAEA,QAAQ;EAClBwB,QAAQ,EAAEA,QAAQ;EAClBiB,OAAO,EAAEA,OAAO;EAChBQ,OAAO,EAAEA;AACX,CAAC,CAAC;AAEF,IAAIc,GAAG,GAAGvE,IAAI,CAACwE,EAAE,GAAG,CAAC;AACrB,IAAIC,QAAQ,GAAG,aAAa,YAAY;EACtC,SAASA,QAAQA,CAACpG,KAAK,EAAE;IACvBP,eAAe,CAAC,IAAI,EAAE2G,QAAQ,CAAC;IAE/B9G,eAAe,CAAC,IAAI,EAAE,eAAe,EAAE,CAAC,CAAC;IAEzCA,eAAe,CAAC,IAAI,EAAE,kBAAkB,EAAE,CAAC,CAAC;IAE5CA,eAAe,CAAC,IAAI,EAAE,aAAa,EAAE,CAAC,CAAC;IAEvCA,eAAe,CAAC,IAAI,EAAE,YAAY,EAAE,KAAK,CAAC;IAE1CA,eAAe,CAAC,IAAI,EAAE,cAAc,EAAE,CAAC,CAAC;IAExCA,eAAe,CAAC,IAAI,EAAE,gBAAgB,EAAE,CAAC,CAAC;IAE1CA,eAAe,CAAC,IAAI,EAAE,YAAY,EAAE,KAAK,CAAC;IAE1CA,eAAe,CAAC,IAAI,EAAE,WAAW,EAAE,IAAI,CAAC;IAExCA,eAAe,CAAC,IAAI,EAAE,YAAY,EAAE,EAAE,CAAC;IAEvCA,eAAe,CAAC,IAAI,EAAE,aAAa,EAAE,IAAI,CAAC;IAE1CA,eAAe,CAAC,IAAI,EAAE,aAAa,EAAE,KAAK,CAAC;IAE3CA,eAAe,CAAC,IAAI,EAAE,aAAa,EAAE,EAAE,CAAC;IAExCA,eAAe,CAAC,IAAI,EAAE,aAAa,EAAE,GAAG,CAAC;IAEzCA,eAAe,CAAC,IAAI,EAAE,OAAO,EAAE,CAAC,CAAC;IAEjCiB,MAAM,CAAC8F,MAAM,CAAC,IAAI,EAAErG,KAAK,CAAC;EAC5B;EAEAU,YAAY,CAAC0F,QAAQ,EAAE,CAAC;IACtB3F,GAAG,EAAE,mBAAmB;IACxB6F,KAAK,EAAE,SAASC,iBAAiBA,CAACC,WAAW,EAAE;MAC7C,IAAIC,aAAa,GAAG9E,IAAI,CAAC+E,MAAM,CAAC,CAAC,IAAI,IAAI,CAACC,WAAW,GAAG,IAAI,CAACC,WAAW,CAAC,GAAG,IAAI,CAACA,WAAW;MAC5F,IAAI,CAACC,aAAa,GAAGL,WAAW,GAAGC,aAAa,GAAG,IAAI;MACvD,IAAI,CAACK,WAAW,GAAG,CAAC;MACpB,IAAI,CAACC,UAAU,GAAG,KAAK;IACzB;EACF,CAAC,EAAE;IACDtG,GAAG,EAAE,OAAO;IACZ6F,KAAK,EAAE,SAASU,KAAKA,CAAA,EAAG;MACtB,IAAI,CAACH,aAAa,GAAG,CAAC;MACtB,IAAI,CAACC,WAAW,GAAG,CAAC;MACpB,IAAI,CAACC,UAAU,GAAG,KAAK;IACzB;EACF,CAAC,EAAE;IACDtG,GAAG,EAAE,QAAQ;IACb6F,KAAK,EAAE,SAASW,MAAMA,CAACT,WAAW,EAAEU,KAAK,EAAE;MACzC,IAAIV,WAAW,GAAG,IAAI,CAACK,aAAa,IAAI,IAAI,CAACM,YAAY,KAAK,CAAC,EAAE;QAC/D,IAAI,CAACA,YAAY,GAAGxF,IAAI,CAACC,KAAK,CAACD,IAAI,CAAC+E,MAAM,CAAC,CAAC,GAAG,IAAI,CAACU,KAAK,CAAC,GAAG,CAAC;MAChE;MAEA,IAAI,IAAI,CAACN,WAAW,GAAG,IAAI,CAACK,YAAY,IAAIX,WAAW,GAAG,IAAI,CAACK,aAAa,EAAE;QAC5E,IAAI,CAAC,IAAI,CAACE,UAAU,EAAE;UACpB,IAAI,CAACA,UAAU,GAAG,IAAI;UACtB,IAAI,CAACM,cAAc,GAAG,CAAC;UACvB,IAAIC,aAAa,GAAG3F,IAAI,CAAC+E,MAAM,CAAC,CAAC,IAAI,IAAI,CAACa,WAAW,GAAG,IAAI,CAACC,WAAW,CAAC,GAAG,IAAI,CAACA,WAAW;UAC5F,IAAI,CAACC,gBAAgB,GAAGjB,WAAW,GAAGc,aAAa,GAAG,IAAI;QAC5D,CAAC,MAAM,IAAI,IAAI,CAACP,UAAU,IAAIP,WAAW,GAAG,IAAI,CAACiB,gBAAgB,EAAE;UACjE,IAAI,CAACV,UAAU,GAAG,KAAK;UACvB,IAAI,CAACW,UAAU,GAAG,IAAI;UACtB,IAAI,CAACZ,WAAW,EAAE;UAElB,IAAI,IAAI,CAACA,WAAW,IAAI,IAAI,CAACK,YAAY,EAAE;YACzC,IAAI,CAACA,YAAY,GAAG,CAAC;YACrB,IAAI,IAAI,CAACQ,SAAS,EAAE,IAAI,CAACpB,iBAAiB,CAACC,WAAW,CAAC;UACzD;QACF;MACF;MAEA,IAAI,IAAI,CAACkB,UAAU,EAAE;QACnB,IAAI,CAACL,cAAc,IAAIH,KAAK,GAAG,IAAI,CAACU,UAAU;QAC9C,IAAI,CAACP,cAAc,GAAG1F,IAAI,CAACkG,GAAG,CAAC,CAAC,EAAElG,IAAI,CAACmG,GAAG,CAAC,CAAC,EAAE,IAAI,CAACT,cAAc,CAAC,CAAC;QAEnE,IAAI,IAAI,CAACA,cAAc,IAAI,CAAC,EAAE;UAC5B,IAAI,CAACK,UAAU,GAAG,KAAK;UACvB,IAAI,CAACL,cAAc,GAAG,CAAC;QACzB;MACF;MAEA,OAAO,IAAI,CAACA,cAAc;IAC5B;EACF,CAAC,CAAC,CAAC;EAEH,OAAOjB,QAAQ;AACjB,CAAC,CAAC,CAAC,CAAC,CAAC;;AAEL,SAAS2B,aAAaA,CAACtG,IAAI,EAAE;EAC3B,IAAI,OAAOA,IAAI,KAAK,QAAQ,EAAE;IAC5BA,IAAI,GAAGE,IAAI,CAACqG,GAAG,CAACvG,IAAI,CAAC;EACvB,CAAC,MAAM,IAAI,OAAOA,IAAI,KAAK,QAAQ,EAAE;IACnC,IAAIwG,MAAM,GAAGxG,IAAI;IACjBA,IAAI,GAAG,CAAC;IAER,KAAK,IAAIxB,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGgI,MAAM,CAAC/H,MAAM,EAAED,CAAC,EAAE,EAAE;MACtCwB,IAAI,GAAG,CAACA,IAAI,GAAG,CAACxB,CAAC,GAAG,CAAC,KAAKgI,MAAM,CAACC,UAAU,CAACjI,CAAC,CAAC,GAAG,EAAE,CAAC,IAAI,UAAU;IACpE;EACF;EAEA,IAAIwB,IAAI,KAAK,CAAC,EAAE;IACdA,IAAI,GAAG,GAAG;EACZ;EAEA,OAAOA,IAAI;AACb;AAEA,SAAS0G,SAASA,CAAC1G,IAAI,EAAE;EACvB,IAAI2G,KAAK,GAAGL,aAAa,CAACtG,IAAI,CAAC;EAC/B,OAAO,YAAY;IACjB,IAAI4G,MAAM,GAAGD,KAAK,GAAG,KAAK,GAAG,UAAU;IACvCA,KAAK,GAAGC,MAAM;IACd,OAAOA,MAAM,GAAG,UAAU;EAC5B,CAAC;AACH;AAEA,IAAIC,SAAS,GAAG,SAASA,SAASA,CAAC5G,KAAK,EAAE;EACxC,IAAIP,KAAK,GAAG,IAAI;EAEhB1B,eAAe,CAAC,IAAI,EAAE6I,SAAS,CAAC;EAEhChJ,eAAe,CAAC,IAAI,EAAE,MAAM,EAAE,CAAC,CAAC;EAEhCA,eAAe,CAAC,IAAI,EAAE,MAAM,EAAE,UAAUmC,IAAI,EAAE;IAC5CN,KAAK,CAACM,IAAI,GAAGA,IAAI;IACjBN,KAAK,CAACmF,KAAK,GAAG6B,SAAS,CAAC1G,IAAI,CAAC;EAC/B,CAAC,CAAC;EAEFnC,eAAe,CAAC,IAAI,EAAE,OAAO,EAAE6I,SAAS,CAAC,IAAI,CAAC1G,IAAI,CAAC,CAAC;EAEpD,IAAI,CAAC8G,IAAI,CAAC7G,KAAK,CAAC;AAClB,CAAC;AACD,IAAI8G,UAAU,GAAG,IAAIF,SAAS,CAAC3G,IAAI,CAAC+E,MAAM,CAAC,CAAC,CAAC;AAC7C;AACA;AACA;;AAEA,IAAI+B,aAAa,GAAG;EAClBC,MAAM,EAAE,CAAC;EACTC,MAAM,EAAE,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC;AAClB,CAAC,CAAC,CAAC;AACH;AACA;;AAEA,SAASC,QAAQA,CAACC,MAAM,EAAEC,MAAM,EAAE;EAChC,IAAIC,GAAG,GAAGC,SAAS,CAAC9I,MAAM,GAAG,CAAC,IAAI8I,SAAS,CAAC,CAAC,CAAC,KAAKC,SAAS,GAAGD,SAAS,CAAC,CAAC,CAAC,GAAGR,UAAU;EAExF,IAAIU,qBAAqB,GAAG1J,cAAc,CAACA,cAAc,CAAC,CAAC,CAAC,EAAEiJ,aAAa,CAAC,EAAEK,MAAM,CAAC;IACjFJ,MAAM,GAAGQ,qBAAqB,CAACR,MAAM;IACrCC,MAAM,GAAGO,qBAAqB,CAACP,MAAM;EAEzC,KAAK,IAAI1I,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAG4I,MAAM,CAAC3I,MAAM,EAAED,CAAC,IAAI,CAAC,EAAE;IACzC,IAAIkF,CAAC,GAAG4D,GAAG,CAACzC,KAAK,CAAC,CAAC;IACnB,IAAIzE,CAAC,GAAGkH,GAAG,CAACzC,KAAK,CAAC,CAAC;IACnB,IAAI6C,KAAK,GAAGxH,IAAI,CAACyH,IAAI,CAAC,CAAC,GAAGvH,CAAC,GAAG,CAAC,CAAC;IAChC,IAAIwH,GAAG,GAAGnD,GAAG,GAAGf,CAAC;IACjB0D,MAAM,CAAC5I,CAAC,CAAC,GAAG0B,IAAI,CAAC2H,GAAG,CAACH,KAAK,CAAC,GAAGxH,IAAI,CAAC4H,GAAG,CAACF,GAAG,CAAC,GAAGX,MAAM,GAAGC,MAAM,CAAC,CAAC,CAAC;IAChEE,MAAM,CAAC5I,CAAC,GAAG,CAAC,CAAC,GAAG0B,IAAI,CAAC2H,GAAG,CAACH,KAAK,CAAC,GAAGxH,IAAI,CAAC2H,GAAG,CAACD,GAAG,CAAC,GAAGX,MAAM,GAAGC,MAAM,CAAC,CAAC,CAAC;IACpEE,MAAM,CAAC5I,CAAC,GAAG,CAAC,CAAC,GAAG0B,IAAI,CAAC4H,GAAG,CAACJ,KAAK,CAAC,GAAGT,MAAM,GAAGC,MAAM,CAAC,CAAC,CAAC;EACtD;EAEA,OAAOE,MAAM;AACf,CAAC,CAAC;;AAEF,SAASW,QAAQA,CAACX,MAAM,EAAEC,MAAM,EAAE;EAChC,IAAIC,GAAG,GAAGC,SAAS,CAAC9I,MAAM,GAAG,CAAC,IAAI8I,SAAS,CAAC,CAAC,CAAC,KAAKC,SAAS,GAAGD,SAAS,CAAC,CAAC,CAAC,GAAGR,UAAU;EAExF,IAAIiB,sBAAsB,GAAGjK,cAAc,CAACA,cAAc,CAAC,CAAC,CAAC,EAAEiJ,aAAa,CAAC,EAAEK,MAAM,CAAC;IAClFJ,MAAM,GAAGe,sBAAsB,CAACf,MAAM;IACtCC,MAAM,GAAGc,sBAAsB,CAACd,MAAM;EAE1C,KAAK,IAAI1I,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAG4I,MAAM,CAAC3I,MAAM,EAAED,CAAC,IAAI,CAAC,EAAE;IACzC,IAAIkF,CAAC,GAAGxD,IAAI,CAAC+H,GAAG,CAACX,GAAG,CAACzC,KAAK,CAAC,CAAC,EAAE,CAAC,GAAG,CAAC,CAAC;IACpC,IAAItF,CAAC,GAAG+H,GAAG,CAACzC,KAAK,CAAC,CAAC,GAAG,CAAC,GAAG,CAAC;IAC3B,IAAIrF,CAAC,GAAG8H,GAAG,CAACzC,KAAK,CAAC,CAAC,GAAG,CAAC,GAAG,CAAC;IAC3B,IAAIpF,CAAC,GAAG6H,GAAG,CAACzC,KAAK,CAAC,CAAC,GAAG,CAAC,GAAG,CAAC;IAC3B,IAAIqD,GAAG,GAAGhI,IAAI,CAACI,IAAI,CAACf,CAAC,GAAGA,CAAC,GAAGC,CAAC,GAAGA,CAAC,GAAGC,CAAC,GAAGA,CAAC,CAAC;IAC1CF,CAAC,GAAGmE,CAAC,GAAGnE,CAAC,GAAG2I,GAAG;IACf1I,CAAC,GAAGkE,CAAC,GAAGlE,CAAC,GAAG0I,GAAG;IACfzI,CAAC,GAAGiE,CAAC,GAAGjE,CAAC,GAAGyI,GAAG;IACfd,MAAM,CAAC5I,CAAC,CAAC,GAAGe,CAAC,GAAG0H,MAAM,GAAGC,MAAM,CAAC,CAAC,CAAC;IAClCE,MAAM,CAAC5I,CAAC,GAAG,CAAC,CAAC,GAAGgB,CAAC,GAAGyH,MAAM,GAAGC,MAAM,CAAC,CAAC,CAAC;IACtCE,MAAM,CAAC5I,CAAC,GAAG,CAAC,CAAC,GAAGiB,CAAC,GAAGwH,MAAM,GAAGC,MAAM,CAAC,CAAC,CAAC;EACxC;EAEA,OAAOE,MAAM;AACf;AACA;AACA;AACA;;AAEA,IAAIe,aAAa,GAAG;EAClBlB,MAAM,EAAE,CAAC;EACTC,MAAM,EAAE,CAAC,CAAC,EAAE,CAAC;AACf,CAAC,CAAC,CAAC;;AAEH,SAASkB,QAAQA,CAAChB,MAAM,EAAEiB,MAAM,EAAE;EAChC,IAAIf,GAAG,GAAGC,SAAS,CAAC9I,MAAM,GAAG,CAAC,IAAI8I,SAAS,CAAC,CAAC,CAAC,KAAKC,SAAS,GAAGD,SAAS,CAAC,CAAC,CAAC,GAAGR,UAAU;EAExF,IAAIuB,qBAAqB,GAAGvK,cAAc,CAACA,cAAc,CAAC,CAAC,CAAC,EAAEoK,aAAa,CAAC,EAAEE,MAAM,CAAC;IACjFpB,MAAM,GAAGqB,qBAAqB,CAACrB,MAAM;IACrCC,MAAM,GAAGoB,qBAAqB,CAACpB,MAAM;EAEzC,KAAK,IAAI1I,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAG4I,MAAM,CAAC3I,MAAM,EAAED,CAAC,IAAI,CAAC,EAAE;IACzC,IAAI+J,CAAC,GAAGtB,MAAM,GAAG/G,IAAI,CAACI,IAAI,CAACgH,GAAG,CAACzC,KAAK,CAAC,CAAC,CAAC;IACvC,IAAI6C,KAAK,GAAGJ,GAAG,CAACzC,KAAK,CAAC,CAAC,GAAGJ,GAAG;IAC7B2C,MAAM,CAAC5I,CAAC,CAAC,GAAG0B,IAAI,CAAC2H,GAAG,CAACH,KAAK,CAAC,GAAGa,CAAC,GAAGrB,MAAM,CAAC,CAAC,CAAC;IAC3CE,MAAM,CAAC5I,CAAC,GAAG,CAAC,CAAC,GAAG0B,IAAI,CAAC4H,GAAG,CAACJ,KAAK,CAAC,GAAGa,CAAC,GAAGrB,MAAM,CAAC,CAAC,CAAC;EACjD;EAEA,OAAOE,MAAM;AACf;AACA,SAASoB,QAAQA,CAACpB,MAAM,EAAEiB,MAAM,EAAE;EAChC,IAAIf,GAAG,GAAGC,SAAS,CAAC9I,MAAM,GAAG,CAAC,IAAI8I,SAAS,CAAC,CAAC,CAAC,KAAKC,SAAS,GAAGD,SAAS,CAAC,CAAC,CAAC,GAAGR,UAAU;EAExF,IAAI0B,sBAAsB,GAAG1K,cAAc,CAACA,cAAc,CAAC,CAAC,CAAC,EAAEoK,aAAa,CAAC,EAAEE,MAAM,CAAC;IAClFpB,MAAM,GAAGwB,sBAAsB,CAACxB,MAAM;IACtCC,MAAM,GAAGuB,sBAAsB,CAACvB,MAAM;EAE1C,KAAK,IAAI1I,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAG4I,MAAM,CAAC3I,MAAM,EAAED,CAAC,IAAI,CAAC,EAAE;IACzC,IAAIkJ,KAAK,GAAGJ,GAAG,CAACzC,KAAK,CAAC,CAAC,GAAGJ,GAAG;IAC7B2C,MAAM,CAAC5I,CAAC,CAAC,GAAG0B,IAAI,CAAC2H,GAAG,CAACH,KAAK,CAAC,GAAGT,MAAM,GAAGC,MAAM,CAAC,CAAC,CAAC;IAChDE,MAAM,CAAC5I,CAAC,GAAG,CAAC,CAAC,GAAG0B,IAAI,CAAC4H,GAAG,CAACJ,KAAK,CAAC,GAAGT,MAAM,GAAGC,MAAM,CAAC,CAAC,CAAC;EACtD;EAEA,OAAOE,MAAM;AACf;AACA;AACA;AACA;;AAEA,IAAIsB,WAAW,GAAG;EAChBC,KAAK,EAAE,CAAC;EACRzB,MAAM,EAAE,CAAC,CAAC,EAAE,CAAC;AACf,CAAC;AACD,SAAS0B,MAAMA,CAACxB,MAAM,EAAEyB,IAAI,EAAE;EAC5B,IAAIvB,GAAG,GAAGC,SAAS,CAAC9I,MAAM,GAAG,CAAC,IAAI8I,SAAS,CAAC,CAAC,CAAC,KAAKC,SAAS,GAAGD,SAAS,CAAC,CAAC,CAAC,GAAGR,UAAU;EAExF,IAAI+B,iBAAiB,GAAG/K,cAAc,CAACA,cAAc,CAAC,CAAC,CAAC,EAAE2K,WAAW,CAAC,EAAEG,IAAI,CAAC;IACzEF,KAAK,GAAGG,iBAAiB,CAACH,KAAK;IAC/BzB,MAAM,GAAG4B,iBAAiB,CAAC5B,MAAM;EAErC,IAAI6B,KAAK,GAAG,OAAOJ,KAAK,KAAK,QAAQ,GAAGA,KAAK,GAAGA,KAAK,CAAC,CAAC,CAAC;EACxD,IAAIK,KAAK,GAAG,OAAOL,KAAK,KAAK,QAAQ,GAAGA,KAAK,GAAGA,KAAK,CAAC,CAAC,CAAC;EAExD,KAAK,IAAInK,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAG4I,MAAM,CAAC3I,MAAM,EAAED,CAAC,IAAI,CAAC,EAAE;IACzC4I,MAAM,CAAC5I,CAAC,CAAC,GAAG,CAAC8I,GAAG,CAACzC,KAAK,CAAC,CAAC,GAAG,GAAG,IAAIkE,KAAK,GAAG7B,MAAM,CAAC,CAAC,CAAC;IACnDE,MAAM,CAAC5I,CAAC,GAAG,CAAC,CAAC,GAAG,CAAC8I,GAAG,CAACzC,KAAK,CAAC,CAAC,GAAG,GAAG,IAAImE,KAAK,GAAG9B,MAAM,CAAC,CAAC,CAAC;EACzD;EAEA,OAAOE,MAAM;AACf;AACA,SAAS6B,MAAMA,CAAC7B,MAAM,EAAEyB,IAAI,EAAE;EAC5B,OAAOzB,MAAM;AACf;AACA;AACA;AACA;;AAEA,SAAS8B,KAAKA,CAAC9B,MAAM,EAAE+B,GAAG,EAAE;EAC1B,IAAI7B,GAAG,GAAGC,SAAS,CAAC9I,MAAM,GAAG,CAAC,IAAI8I,SAAS,CAAC,CAAC,CAAC,KAAKC,SAAS,GAAGD,SAAS,CAAC,CAAC,CAAC,GAAGR,UAAU;EAExF,IAAIqC,eAAe,GAAGrL,cAAc,CAACA,cAAc,CAAC,CAAC,CAAC,EAAEsL,UAAU,CAAC,EAAEF,GAAG,CAAC;IACrER,KAAK,GAAGS,eAAe,CAACT,KAAK;IAC7BzB,MAAM,GAAGkC,eAAe,CAAClC,MAAM;EAEnC,IAAI6B,KAAK,GAAG,OAAOJ,KAAK,KAAK,QAAQ,GAAGA,KAAK,GAAGA,KAAK,CAAC,CAAC,CAAC;EACxD,IAAIK,KAAK,GAAG,OAAOL,KAAK,KAAK,QAAQ,GAAGA,KAAK,GAAGA,KAAK,CAAC,CAAC,CAAC;EACxD,IAAIW,KAAK,GAAG,OAAOX,KAAK,KAAK,QAAQ,GAAGA,KAAK,GAAGA,KAAK,CAAC,CAAC,CAAC;EAExD,KAAK,IAAInK,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAG4I,MAAM,CAAC3I,MAAM,EAAED,CAAC,IAAI,CAAC,EAAE;IACzC4I,MAAM,CAAC5I,CAAC,CAAC,GAAG,CAAC8I,GAAG,CAACzC,KAAK,CAAC,CAAC,GAAG,GAAG,IAAIkE,KAAK,GAAG7B,MAAM,CAAC,CAAC,CAAC;IACnDE,MAAM,CAAC5I,CAAC,GAAG,CAAC,CAAC,GAAG,CAAC8I,GAAG,CAACzC,KAAK,CAAC,CAAC,GAAG,GAAG,IAAImE,KAAK,GAAG9B,MAAM,CAAC,CAAC,CAAC;IACvDE,MAAM,CAAC5I,CAAC,GAAG,CAAC,CAAC,GAAG,CAAC8I,GAAG,CAACzC,KAAK,CAAC,CAAC,GAAG,GAAG,IAAIyE,KAAK,GAAGpC,MAAM,CAAC,CAAC,CAAC;EACzD;EAEA,OAAOE,MAAM;AACf;AACA,IAAIiC,UAAU,GAAG;EACfV,KAAK,EAAE,CAAC;EACRzB,MAAM,EAAE,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC;AAClB,CAAC;AACD,SAASqC,KAAKA,CAACnC,MAAM,EAAE+B,GAAG,EAAE;EAC1B,IAAI7B,GAAG,GAAGC,SAAS,CAAC9I,MAAM,GAAG,CAAC,IAAI8I,SAAS,CAAC,CAAC,CAAC,KAAKC,SAAS,GAAGD,SAAS,CAAC,CAAC,CAAC,GAAGR,UAAU;EAExF,IAAIyC,gBAAgB,GAAGzL,cAAc,CAACA,cAAc,CAAC,CAAC,CAAC,EAAEsL,UAAU,CAAC,EAAEF,GAAG,CAAC;IACtER,KAAK,GAAGa,gBAAgB,CAACb,KAAK;IAC9BzB,MAAM,GAAGsC,gBAAgB,CAACtC,MAAM;EAEpC,IAAI6B,KAAK,GAAG,OAAOJ,KAAK,KAAK,QAAQ,GAAGA,KAAK,GAAGA,KAAK,CAAC,CAAC,CAAC;EACxD,IAAIK,KAAK,GAAG,OAAOL,KAAK,KAAK,QAAQ,GAAGA,KAAK,GAAGA,KAAK,CAAC,CAAC,CAAC;EACxD,IAAIW,KAAK,GAAG,OAAOX,KAAK,KAAK,QAAQ,GAAGA,KAAK,GAAGA,KAAK,CAAC,CAAC,CAAC;EAExD,KAAK,IAAInK,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAG4I,MAAM,CAAC3I,MAAM,EAAED,CAAC,IAAI,CAAC,EAAE;IACzC4I,MAAM,CAAC5I,CAAC,CAAC,GAAG,CAAC8I,GAAG,CAACzC,KAAK,CAAC,CAAC,GAAG,GAAG,IAAIkE,KAAK,GAAG7B,MAAM,CAAC,CAAC,CAAC;IACnDE,MAAM,CAAC5I,CAAC,GAAG,CAAC,CAAC,GAAG,CAAC8I,GAAG,CAACzC,KAAK,CAAC,CAAC,GAAG,GAAG,IAAImE,KAAK,GAAG9B,MAAM,CAAC,CAAC,CAAC;IACvDE,MAAM,CAAC5I,CAAC,GAAG,CAAC,CAAC,GAAG,CAAC8I,GAAG,CAACzC,KAAK,CAAC,CAAC,GAAG,GAAG,IAAIyE,KAAK,GAAGpC,MAAM,CAAC,CAAC,CAAC;EACzD;EAEA,OAAOE,MAAM;AACf;AAEA,IAAIqC,KAAK,GAAG,aAAa3K,MAAM,CAACyF,MAAM,CAAC;EACrCC,SAAS,EAAE,IAAI;EACfG,QAAQ,EAAEA,QAAQ;EAClBkC,SAAS,EAAEA,SAAS;EACpBM,QAAQ,EAAEA,QAAQ;EAClBY,QAAQ,EAAEA,QAAQ;EAClBK,QAAQ,EAAEA,QAAQ;EAClBI,QAAQ,EAAEA,QAAQ;EAClBI,MAAM,EAAEA,MAAM;EACdK,MAAM,EAAEA,MAAM;EACdC,KAAK,EAAEA,KAAK;EACZK,KAAK,EAAEA,KAAK;EACZjF,KAAK,EAAEA;AACT,CAAC,CAAC;AAEF,SAASK,QAAQ,IAAI+E,CAAC,EAAE7C,SAAS,IAAI8C,CAAC,EAAE5B,QAAQ,IAAInK,CAAC,EAAEwK,QAAQ,IAAIwB,CAAC,EAAEpB,QAAQ,IAAIqB,CAAC,EAAEjB,MAAM,IAAIkB,CAAC,EAAEb,MAAM,IAAIc,CAAC,EAAEb,KAAK,IAAI/K,CAAC,EAAEoL,KAAK,IAAIS,CAAC,EAAEP,KAAK,IAAIjL,CAAC,EAAE8F,KAAK,IAAI2F,CAAC,EAAE9C,QAAQ,IAAI+C,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}