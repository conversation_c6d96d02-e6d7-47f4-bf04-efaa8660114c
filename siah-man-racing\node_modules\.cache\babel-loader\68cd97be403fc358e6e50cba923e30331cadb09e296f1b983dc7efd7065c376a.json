{"ast": null, "code": "import _extends from '@babel/runtime/helpers/esm/extends';\nimport * as React from 'react';\nimport { REVISION, Quaternion, Vector3, DynamicDrawUsage, MeshLambertMaterial, Matrix4, Color } from 'three';\nimport { extend, useFrame, applyProps } from '@react-three/fiber';\nimport { useTexture } from './Texture.js';\nimport { setUpdateRange } from '../helpers/deprecated.js';\nconst CLOUD_URL = 'https://rawcdn.githack.com/pmndrs/drei-assets/9225a9f1fbd449d9411125c2f419b843d0308c9f/cloud.png';\nconst parentMatrix = /* @__PURE__ */new Matrix4();\nconst translation = /* @__PURE__ */new Vector3();\nconst rotation = /* @__PURE__ */new Quaternion();\nconst cpos = /* @__PURE__ */new Vector3();\nconst cquat = /* @__PURE__ */new Quaternion();\nconst scale = /* @__PURE__ */new Vector3();\nconst context = /* @__PURE__ */React.createContext(null);\nconst Clouds = /* @__PURE__ */React.forwardRef(({\n  children,\n  material = MeshLambertMaterial,\n  texture = CLOUD_URL,\n  range,\n  limit = 200,\n  frustumCulled,\n  ...props\n}, fref) => {\n  var _image$width, _image$height;\n  const CloudMaterial = React.useMemo(() => {\n    return class extends material {\n      constructor() {\n        super();\n        const opaque_fragment = parseInt(REVISION.replace(/\\D+/g, '')) >= 154 ? 'opaque_fragment' : 'output_fragment';\n        this.onBeforeCompile = shader => {\n          shader.vertexShader = `attribute float cloudOpacity;\n               varying float vOpacity;\n              ` + shader.vertexShader.replace('#include <fog_vertex>', `#include <fog_vertex>\n                 vOpacity = cloudOpacity;\n                `);\n          shader.fragmentShader = `varying float vOpacity;\n              ` + shader.fragmentShader.replace(`#include <${opaque_fragment}>`, `#include <${opaque_fragment}>\n                 gl_FragColor = vec4(outgoingLight, diffuseColor.a * vOpacity);\n                `);\n        };\n      }\n    };\n  }, [material]);\n  extend({\n    CloudMaterial\n  });\n  const instance = React.useRef(null);\n  const clouds = React.useRef([]);\n  const opacities = React.useMemo(() => new Float32Array(Array.from({\n    length: limit\n  }, () => 1)), [limit]);\n  const colors = React.useMemo(() => new Float32Array(Array.from({\n    length: limit\n  }, () => [1, 1, 1]).flat()), [limit]);\n  const cloudTexture = useTexture(texture);\n  let t = 0;\n  let index = 0;\n  let config;\n  const qat = new Quaternion();\n  const dir = new Vector3(0, 0, 1);\n  const pos = new Vector3();\n  useFrame((state, delta) => {\n    t = state.clock.elapsedTime;\n    parentMatrix.copy(instance.current.matrixWorld).invert();\n    state.camera.matrixWorld.decompose(cpos, cquat, scale);\n    for (index = 0; index < clouds.current.length; index++) {\n      config = clouds.current[index];\n      config.ref.current.matrixWorld.decompose(translation, rotation, scale);\n      translation.add(pos.copy(config.position).applyQuaternion(rotation).multiply(scale));\n      rotation.copy(cquat).multiply(qat.setFromAxisAngle(dir, config.rotation += delta * config.rotationFactor));\n      scale.multiplyScalar(config.volume + (1 + Math.sin(t * config.density * config.speed)) / 2 * config.growth);\n      config.matrix.compose(translation, rotation, scale).premultiply(parentMatrix);\n      config.dist = translation.distanceTo(cpos);\n    }\n\n    // Depth-sort. Instances have no specific draw order, w/o sorting z would be random\n    clouds.current.sort((a, b) => b.dist - a.dist);\n    for (index = 0; index < clouds.current.length; index++) {\n      config = clouds.current[index];\n      opacities[index] = config.opacity * (config.dist < config.fade - 1 ? config.dist / config.fade : 1);\n      instance.current.setMatrixAt(index, config.matrix);\n      instance.current.setColorAt(index, config.color);\n    }\n\n    // Update instance\n    instance.current.geometry.attributes.cloudOpacity.needsUpdate = true;\n    instance.current.instanceMatrix.needsUpdate = true;\n    if (instance.current.instanceColor) instance.current.instanceColor.needsUpdate = true;\n  });\n  React.useLayoutEffect(() => {\n    const count = Math.min(limit, range !== undefined ? range : limit, clouds.current.length);\n    instance.current.count = count;\n    setUpdateRange(instance.current.instanceMatrix, {\n      start: 0,\n      count: count * 16\n    });\n    if (instance.current.instanceColor) {\n      setUpdateRange(instance.current.instanceColor, {\n        start: 0,\n        count: count * 3\n      });\n    }\n    setUpdateRange(instance.current.geometry.attributes.cloudOpacity, {\n      start: 0,\n      count: count\n    });\n  });\n  let imageBounds = [(_image$width = cloudTexture.image.width) !== null && _image$width !== void 0 ? _image$width : 1, (_image$height = cloudTexture.image.height) !== null && _image$height !== void 0 ? _image$height : 1];\n  const max = Math.max(imageBounds[0], imageBounds[1]);\n  imageBounds = [imageBounds[0] / max, imageBounds[1] / max];\n  return /*#__PURE__*/React.createElement(\"group\", _extends({\n    ref: fref\n  }, props), /*#__PURE__*/React.createElement(context.Provider, {\n    value: clouds\n  }, children, /*#__PURE__*/React.createElement(\"instancedMesh\", {\n    matrixAutoUpdate: false,\n    ref: instance,\n    args: [null, null, limit],\n    frustumCulled: frustumCulled\n  }, /*#__PURE__*/React.createElement(\"instancedBufferAttribute\", {\n    usage: DynamicDrawUsage,\n    attach: \"instanceColor\",\n    args: [colors, 3]\n  }), /*#__PURE__*/React.createElement(\"planeGeometry\", {\n    args: [...imageBounds]\n  }, /*#__PURE__*/React.createElement(\"instancedBufferAttribute\", {\n    usage: DynamicDrawUsage,\n    attach: \"attributes-cloudOpacity\",\n    args: [opacities, 1]\n  })), /*#__PURE__*/React.createElement(\"cloudMaterial\", {\n    key: material.name,\n    map: cloudTexture,\n    transparent: true,\n    depthWrite: false\n  }))));\n});\nconst CloudInstance = /* @__PURE__ */React.forwardRef(({\n  opacity = 1,\n  speed = 0,\n  bounds = [5, 1, 1],\n  segments = 20,\n  color = '#ffffff',\n  fade = 10,\n  volume = 6,\n  smallestVolume = 0.25,\n  distribute = null,\n  growth = 4,\n  concentrate = 'inside',\n  seed = Math.random(),\n  ...props\n}, fref) => {\n  function random() {\n    const x = Math.sin(seed++) * 10000;\n    return x - Math.floor(x);\n  }\n  const parent = React.useContext(context);\n  const ref = React.useRef(null);\n  const uuid = React.useId();\n  const clouds = React.useMemo(() => {\n    return [...new Array(segments)].map((_, index) => ({\n      segments,\n      bounds: new Vector3(1, 1, 1),\n      position: new Vector3(),\n      uuid,\n      index,\n      ref,\n      dist: 0,\n      matrix: new Matrix4(),\n      color: new Color(),\n      rotation: index * (Math.PI / segments)\n    }));\n  }, [segments, uuid]);\n  React.useLayoutEffect(() => {\n    clouds.forEach((cloud, index) => {\n      applyProps(cloud, {\n        volume,\n        color,\n        speed,\n        growth,\n        opacity,\n        fade,\n        bounds,\n        density: Math.max(0.5, random()),\n        rotationFactor: Math.max(0.2, 0.5 * random()) * speed\n      });\n      // Only distribute randomly if there are multiple segments\n\n      const distributed = distribute == null ? void 0 : distribute(cloud, index);\n      if (distributed || segments > 1) {\n        var _distributed$point;\n        cloud.position.copy(cloud.bounds).multiply((_distributed$point = distributed == null ? void 0 : distributed.point) !== null && _distributed$point !== void 0 ? _distributed$point : {\n          x: random() * 2 - 1,\n          y: random() * 2 - 1,\n          z: random() * 2 - 1\n        });\n      }\n      const xDiff = Math.abs(cloud.position.x);\n      const yDiff = Math.abs(cloud.position.y);\n      const zDiff = Math.abs(cloud.position.z);\n      const max = Math.max(xDiff, yDiff, zDiff);\n      cloud.length = 1;\n      if (xDiff === max) cloud.length -= xDiff / cloud.bounds.x;\n      if (yDiff === max) cloud.length -= yDiff / cloud.bounds.y;\n      if (zDiff === max) cloud.length -= zDiff / cloud.bounds.z;\n      cloud.volume = ((distributed == null ? void 0 : distributed.volume) !== undefined ? distributed.volume : Math.max(Math.max(0, smallestVolume), concentrate === 'random' ? random() : concentrate === 'inside' ? cloud.length : 1 - cloud.length)) * volume;\n    });\n  }, [concentrate, bounds, fade, color, opacity, growth, volume, seed, segments, speed]);\n  React.useLayoutEffect(() => {\n    const temp = clouds;\n    parent.current = [...parent.current, ...temp];\n    return () => {\n      parent.current = parent.current.filter(item => item.uuid !== uuid);\n    };\n  }, [clouds]);\n  React.useImperativeHandle(fref, () => ref.current, []);\n  return /*#__PURE__*/React.createElement(\"group\", _extends({\n    ref: ref\n  }, props));\n});\nconst Cloud = /* @__PURE__ */React.forwardRef((props, fref) => {\n  const parent = React.useContext(context);\n  if (parent) return /*#__PURE__*/React.createElement(CloudInstance, _extends({\n    ref: fref\n  }, props));\n  return /*#__PURE__*/React.createElement(Clouds, null, /*#__PURE__*/React.createElement(CloudInstance, _extends({\n    ref: fref\n  }, props)));\n});\nexport { Cloud, CloudInstance, Clouds };", "map": {"version": 3, "names": ["_extends", "React", "REVISION", "Quaternion", "Vector3", "DynamicDrawUsage", "MeshLambertMaterial", "Matrix4", "Color", "extend", "useFrame", "applyProps", "useTexture", "setUpdateRange", "CLOUD_URL", "parentMatrix", "translation", "rotation", "cpos", "cquat", "scale", "context", "createContext", "Clouds", "forwardRef", "children", "material", "texture", "range", "limit", "frustumCulled", "props", "fref", "_image$width", "_image$height", "CloudMaterial", "useMemo", "constructor", "opaque_fragment", "parseInt", "replace", "onBeforeCompile", "shader", "vertexShader", "fragmentShader", "instance", "useRef", "clouds", "opacities", "Float32Array", "Array", "from", "length", "colors", "flat", "cloudTexture", "t", "index", "config", "qat", "dir", "pos", "state", "delta", "clock", "elapsedTime", "copy", "current", "matrixWorld", "invert", "camera", "decompose", "ref", "add", "position", "applyQuaternion", "multiply", "setFromAxisAngle", "rotationFactor", "multiplyScalar", "volume", "Math", "sin", "density", "speed", "growth", "matrix", "compose", "premultiply", "dist", "distanceTo", "sort", "a", "b", "opacity", "fade", "setMatrixAt", "setColorAt", "color", "geometry", "attributes", "cloudOpacity", "needsUpdate", "instanceMatrix", "instanceColor", "useLayoutEffect", "count", "min", "undefined", "start", "imageBounds", "image", "width", "height", "max", "createElement", "Provider", "value", "matrixAutoUpdate", "args", "usage", "attach", "key", "name", "map", "transparent", "depthWrite", "CloudInstance", "bounds", "segments", "smallestVolume", "distribute", "concentrate", "seed", "random", "x", "floor", "parent", "useContext", "uuid", "useId", "_", "PI", "for<PERSON>ach", "cloud", "distributed", "_distributed$point", "point", "y", "z", "xDiff", "abs", "yDiff", "zDiff", "temp", "filter", "item", "useImperativeHandle", "Cloud"], "sources": ["C:/Users/<USER>/Documents/Apps_Creations/SiahMan_Racing4Speed_WebGame/siah-man-racing/node_modules/@react-three/drei/core/Cloud.js"], "sourcesContent": ["import _extends from '@babel/runtime/helpers/esm/extends';\nimport * as React from 'react';\nimport { REVISION, Quaternion, Vector3, DynamicDrawUsage, MeshLambertMaterial, Matrix4, Color } from 'three';\nimport { extend, useFrame, applyProps } from '@react-three/fiber';\nimport { useTexture } from './Texture.js';\nimport { setUpdateRange } from '../helpers/deprecated.js';\n\nconst CLOUD_URL = 'https://rawcdn.githack.com/pmndrs/drei-assets/9225a9f1fbd449d9411125c2f419b843d0308c9f/cloud.png';\nconst parentMatrix = /* @__PURE__ */new Matrix4();\nconst translation = /* @__PURE__ */new Vector3();\nconst rotation = /* @__PURE__ */new Quaternion();\nconst cpos = /* @__PURE__ */new Vector3();\nconst cquat = /* @__PURE__ */new Quaternion();\nconst scale = /* @__PURE__ */new Vector3();\nconst context = /* @__PURE__ */React.createContext(null);\nconst Clouds = /* @__PURE__ */React.forwardRef(({\n  children,\n  material = MeshLambertMaterial,\n  texture = CLOUD_URL,\n  range,\n  limit = 200,\n  frustumCulled,\n  ...props\n}, fref) => {\n  var _image$width, _image$height;\n  const CloudMaterial = React.useMemo(() => {\n    return class extends material {\n      constructor() {\n        super();\n        const opaque_fragment = parseInt(REVISION.replace(/\\D+/g, '')) >= 154 ? 'opaque_fragment' : 'output_fragment';\n        this.onBeforeCompile = shader => {\n          shader.vertexShader = `attribute float cloudOpacity;\n               varying float vOpacity;\n              ` + shader.vertexShader.replace('#include <fog_vertex>', `#include <fog_vertex>\n                 vOpacity = cloudOpacity;\n                `);\n          shader.fragmentShader = `varying float vOpacity;\n              ` + shader.fragmentShader.replace(`#include <${opaque_fragment}>`, `#include <${opaque_fragment}>\n                 gl_FragColor = vec4(outgoingLight, diffuseColor.a * vOpacity);\n                `);\n        };\n      }\n    };\n  }, [material]);\n  extend({\n    CloudMaterial\n  });\n  const instance = React.useRef(null);\n  const clouds = React.useRef([]);\n  const opacities = React.useMemo(() => new Float32Array(Array.from({\n    length: limit\n  }, () => 1)), [limit]);\n  const colors = React.useMemo(() => new Float32Array(Array.from({\n    length: limit\n  }, () => [1, 1, 1]).flat()), [limit]);\n  const cloudTexture = useTexture(texture);\n  let t = 0;\n  let index = 0;\n  let config;\n  const qat = new Quaternion();\n  const dir = new Vector3(0, 0, 1);\n  const pos = new Vector3();\n  useFrame((state, delta) => {\n    t = state.clock.elapsedTime;\n    parentMatrix.copy(instance.current.matrixWorld).invert();\n    state.camera.matrixWorld.decompose(cpos, cquat, scale);\n    for (index = 0; index < clouds.current.length; index++) {\n      config = clouds.current[index];\n      config.ref.current.matrixWorld.decompose(translation, rotation, scale);\n      translation.add(pos.copy(config.position).applyQuaternion(rotation).multiply(scale));\n      rotation.copy(cquat).multiply(qat.setFromAxisAngle(dir, config.rotation += delta * config.rotationFactor));\n      scale.multiplyScalar(config.volume + (1 + Math.sin(t * config.density * config.speed)) / 2 * config.growth);\n      config.matrix.compose(translation, rotation, scale).premultiply(parentMatrix);\n      config.dist = translation.distanceTo(cpos);\n    }\n\n    // Depth-sort. Instances have no specific draw order, w/o sorting z would be random\n    clouds.current.sort((a, b) => b.dist - a.dist);\n    for (index = 0; index < clouds.current.length; index++) {\n      config = clouds.current[index];\n      opacities[index] = config.opacity * (config.dist < config.fade - 1 ? config.dist / config.fade : 1);\n      instance.current.setMatrixAt(index, config.matrix);\n      instance.current.setColorAt(index, config.color);\n    }\n\n    // Update instance\n    instance.current.geometry.attributes.cloudOpacity.needsUpdate = true;\n    instance.current.instanceMatrix.needsUpdate = true;\n    if (instance.current.instanceColor) instance.current.instanceColor.needsUpdate = true;\n  });\n  React.useLayoutEffect(() => {\n    const count = Math.min(limit, range !== undefined ? range : limit, clouds.current.length);\n    instance.current.count = count;\n    setUpdateRange(instance.current.instanceMatrix, {\n      start: 0,\n      count: count * 16\n    });\n    if (instance.current.instanceColor) {\n      setUpdateRange(instance.current.instanceColor, {\n        start: 0,\n        count: count * 3\n      });\n    }\n    setUpdateRange(instance.current.geometry.attributes.cloudOpacity, {\n      start: 0,\n      count: count\n    });\n  });\n  let imageBounds = [(_image$width = cloudTexture.image.width) !== null && _image$width !== void 0 ? _image$width : 1, (_image$height = cloudTexture.image.height) !== null && _image$height !== void 0 ? _image$height : 1];\n  const max = Math.max(imageBounds[0], imageBounds[1]);\n  imageBounds = [imageBounds[0] / max, imageBounds[1] / max];\n  return /*#__PURE__*/React.createElement(\"group\", _extends({\n    ref: fref\n  }, props), /*#__PURE__*/React.createElement(context.Provider, {\n    value: clouds\n  }, children, /*#__PURE__*/React.createElement(\"instancedMesh\", {\n    matrixAutoUpdate: false,\n    ref: instance,\n    args: [null, null, limit],\n    frustumCulled: frustumCulled\n  }, /*#__PURE__*/React.createElement(\"instancedBufferAttribute\", {\n    usage: DynamicDrawUsage,\n    attach: \"instanceColor\",\n    args: [colors, 3]\n  }), /*#__PURE__*/React.createElement(\"planeGeometry\", {\n    args: [...imageBounds]\n  }, /*#__PURE__*/React.createElement(\"instancedBufferAttribute\", {\n    usage: DynamicDrawUsage,\n    attach: \"attributes-cloudOpacity\",\n    args: [opacities, 1]\n  })), /*#__PURE__*/React.createElement(\"cloudMaterial\", {\n    key: material.name,\n    map: cloudTexture,\n    transparent: true,\n    depthWrite: false\n  }))));\n});\nconst CloudInstance = /* @__PURE__ */React.forwardRef(({\n  opacity = 1,\n  speed = 0,\n  bounds = [5, 1, 1],\n  segments = 20,\n  color = '#ffffff',\n  fade = 10,\n  volume = 6,\n  smallestVolume = 0.25,\n  distribute = null,\n  growth = 4,\n  concentrate = 'inside',\n  seed = Math.random(),\n  ...props\n}, fref) => {\n  function random() {\n    const x = Math.sin(seed++) * 10000;\n    return x - Math.floor(x);\n  }\n  const parent = React.useContext(context);\n  const ref = React.useRef(null);\n  const uuid = React.useId();\n  const clouds = React.useMemo(() => {\n    return [...new Array(segments)].map((_, index) => ({\n      segments,\n      bounds: new Vector3(1, 1, 1),\n      position: new Vector3(),\n      uuid,\n      index,\n      ref,\n      dist: 0,\n      matrix: new Matrix4(),\n      color: new Color(),\n      rotation: index * (Math.PI / segments)\n    }));\n  }, [segments, uuid]);\n  React.useLayoutEffect(() => {\n    clouds.forEach((cloud, index) => {\n      applyProps(cloud, {\n        volume,\n        color,\n        speed,\n        growth,\n        opacity,\n        fade,\n        bounds,\n        density: Math.max(0.5, random()),\n        rotationFactor: Math.max(0.2, 0.5 * random()) * speed\n      });\n      // Only distribute randomly if there are multiple segments\n\n      const distributed = distribute == null ? void 0 : distribute(cloud, index);\n      if (distributed || segments > 1) {\n        var _distributed$point;\n        cloud.position.copy(cloud.bounds).multiply((_distributed$point = distributed == null ? void 0 : distributed.point) !== null && _distributed$point !== void 0 ? _distributed$point : {\n          x: random() * 2 - 1,\n          y: random() * 2 - 1,\n          z: random() * 2 - 1\n        });\n      }\n      const xDiff = Math.abs(cloud.position.x);\n      const yDiff = Math.abs(cloud.position.y);\n      const zDiff = Math.abs(cloud.position.z);\n      const max = Math.max(xDiff, yDiff, zDiff);\n      cloud.length = 1;\n      if (xDiff === max) cloud.length -= xDiff / cloud.bounds.x;\n      if (yDiff === max) cloud.length -= yDiff / cloud.bounds.y;\n      if (zDiff === max) cloud.length -= zDiff / cloud.bounds.z;\n      cloud.volume = ((distributed == null ? void 0 : distributed.volume) !== undefined ? distributed.volume : Math.max(Math.max(0, smallestVolume), concentrate === 'random' ? random() : concentrate === 'inside' ? cloud.length : 1 - cloud.length)) * volume;\n    });\n  }, [concentrate, bounds, fade, color, opacity, growth, volume, seed, segments, speed]);\n  React.useLayoutEffect(() => {\n    const temp = clouds;\n    parent.current = [...parent.current, ...temp];\n    return () => {\n      parent.current = parent.current.filter(item => item.uuid !== uuid);\n    };\n  }, [clouds]);\n  React.useImperativeHandle(fref, () => ref.current, []);\n  return /*#__PURE__*/React.createElement(\"group\", _extends({\n    ref: ref\n  }, props));\n});\nconst Cloud = /* @__PURE__ */React.forwardRef((props, fref) => {\n  const parent = React.useContext(context);\n  if (parent) return /*#__PURE__*/React.createElement(CloudInstance, _extends({\n    ref: fref\n  }, props));\n  return /*#__PURE__*/React.createElement(Clouds, null, /*#__PURE__*/React.createElement(CloudInstance, _extends({\n    ref: fref\n  }, props)));\n});\n\nexport { Cloud, CloudInstance, Clouds };\n"], "mappings": "AAAA,OAAOA,QAAQ,MAAM,oCAAoC;AACzD,OAAO,KAAKC,KAAK,MAAM,OAAO;AAC9B,SAASC,QAAQ,EAAEC,UAAU,EAAEC,OAAO,EAAEC,gBAAgB,EAAEC,mBAAmB,EAAEC,OAAO,EAAEC,KAAK,QAAQ,OAAO;AAC5G,SAASC,MAAM,EAAEC,QAAQ,EAAEC,UAAU,QAAQ,oBAAoB;AACjE,SAASC,UAAU,QAAQ,cAAc;AACzC,SAASC,cAAc,QAAQ,0BAA0B;AAEzD,MAAMC,SAAS,GAAG,kGAAkG;AACpH,MAAMC,YAAY,GAAG,eAAe,IAAIR,OAAO,CAAC,CAAC;AACjD,MAAMS,WAAW,GAAG,eAAe,IAAIZ,OAAO,CAAC,CAAC;AAChD,MAAMa,QAAQ,GAAG,eAAe,IAAId,UAAU,CAAC,CAAC;AAChD,MAAMe,IAAI,GAAG,eAAe,IAAId,OAAO,CAAC,CAAC;AACzC,MAAMe,KAAK,GAAG,eAAe,IAAIhB,UAAU,CAAC,CAAC;AAC7C,MAAMiB,KAAK,GAAG,eAAe,IAAIhB,OAAO,CAAC,CAAC;AAC1C,MAAMiB,OAAO,GAAG,eAAepB,KAAK,CAACqB,aAAa,CAAC,IAAI,CAAC;AACxD,MAAMC,MAAM,GAAG,eAAetB,KAAK,CAACuB,UAAU,CAAC,CAAC;EAC9CC,QAAQ;EACRC,QAAQ,GAAGpB,mBAAmB;EAC9BqB,OAAO,GAAGb,SAAS;EACnBc,KAAK;EACLC,KAAK,GAAG,GAAG;EACXC,aAAa;EACb,GAAGC;AACL,CAAC,EAAEC,IAAI,KAAK;EACV,IAAIC,YAAY,EAAEC,aAAa;EAC/B,MAAMC,aAAa,GAAGlC,KAAK,CAACmC,OAAO,CAAC,MAAM;IACxC,OAAO,cAAcV,QAAQ,CAAC;MAC5BW,WAAWA,CAAA,EAAG;QACZ,KAAK,CAAC,CAAC;QACP,MAAMC,eAAe,GAAGC,QAAQ,CAACrC,QAAQ,CAACsC,OAAO,CAAC,MAAM,EAAE,EAAE,CAAC,CAAC,IAAI,GAAG,GAAG,iBAAiB,GAAG,iBAAiB;QAC7G,IAAI,CAACC,eAAe,GAAGC,MAAM,IAAI;UAC/BA,MAAM,CAACC,YAAY,GAAG;AAChC;AACA,eAAe,GAAGD,MAAM,CAACC,YAAY,CAACH,OAAO,CAAC,uBAAuB,EAAE;AACvE;AACA,iBAAiB,CAAC;UACRE,MAAM,CAACE,cAAc,GAAG;AAClC,eAAe,GAAGF,MAAM,CAACE,cAAc,CAACJ,OAAO,CAAC,aAAaF,eAAe,GAAG,EAAE,aAAaA,eAAe;AAC7G;AACA,iBAAiB,CAAC;QACV,CAAC;MACH;IACF,CAAC;EACH,CAAC,EAAE,CAACZ,QAAQ,CAAC,CAAC;EACdjB,MAAM,CAAC;IACL0B;EACF,CAAC,CAAC;EACF,MAAMU,QAAQ,GAAG5C,KAAK,CAAC6C,MAAM,CAAC,IAAI,CAAC;EACnC,MAAMC,MAAM,GAAG9C,KAAK,CAAC6C,MAAM,CAAC,EAAE,CAAC;EAC/B,MAAME,SAAS,GAAG/C,KAAK,CAACmC,OAAO,CAAC,MAAM,IAAIa,YAAY,CAACC,KAAK,CAACC,IAAI,CAAC;IAChEC,MAAM,EAAEvB;EACV,CAAC,EAAE,MAAM,CAAC,CAAC,CAAC,EAAE,CAACA,KAAK,CAAC,CAAC;EACtB,MAAMwB,MAAM,GAAGpD,KAAK,CAACmC,OAAO,CAAC,MAAM,IAAIa,YAAY,CAACC,KAAK,CAACC,IAAI,CAAC;IAC7DC,MAAM,EAAEvB;EACV,CAAC,EAAE,MAAM,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAACyB,IAAI,CAAC,CAAC,CAAC,EAAE,CAACzB,KAAK,CAAC,CAAC;EACrC,MAAM0B,YAAY,GAAG3C,UAAU,CAACe,OAAO,CAAC;EACxC,IAAI6B,CAAC,GAAG,CAAC;EACT,IAAIC,KAAK,GAAG,CAAC;EACb,IAAIC,MAAM;EACV,MAAMC,GAAG,GAAG,IAAIxD,UAAU,CAAC,CAAC;EAC5B,MAAMyD,GAAG,GAAG,IAAIxD,OAAO,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC;EAChC,MAAMyD,GAAG,GAAG,IAAIzD,OAAO,CAAC,CAAC;EACzBM,QAAQ,CAAC,CAACoD,KAAK,EAAEC,KAAK,KAAK;IACzBP,CAAC,GAAGM,KAAK,CAACE,KAAK,CAACC,WAAW;IAC3BlD,YAAY,CAACmD,IAAI,CAACrB,QAAQ,CAACsB,OAAO,CAACC,WAAW,CAAC,CAACC,MAAM,CAAC,CAAC;IACxDP,KAAK,CAACQ,MAAM,CAACF,WAAW,CAACG,SAAS,CAACrD,IAAI,EAAEC,KAAK,EAAEC,KAAK,CAAC;IACtD,KAAKqC,KAAK,GAAG,CAAC,EAAEA,KAAK,GAAGV,MAAM,CAACoB,OAAO,CAACf,MAAM,EAAEK,KAAK,EAAE,EAAE;MACtDC,MAAM,GAAGX,MAAM,CAACoB,OAAO,CAACV,KAAK,CAAC;MAC9BC,MAAM,CAACc,GAAG,CAACL,OAAO,CAACC,WAAW,CAACG,SAAS,CAACvD,WAAW,EAAEC,QAAQ,EAAEG,KAAK,CAAC;MACtEJ,WAAW,CAACyD,GAAG,CAACZ,GAAG,CAACK,IAAI,CAACR,MAAM,CAACgB,QAAQ,CAAC,CAACC,eAAe,CAAC1D,QAAQ,CAAC,CAAC2D,QAAQ,CAACxD,KAAK,CAAC,CAAC;MACpFH,QAAQ,CAACiD,IAAI,CAAC/C,KAAK,CAAC,CAACyD,QAAQ,CAACjB,GAAG,CAACkB,gBAAgB,CAACjB,GAAG,EAAEF,MAAM,CAACzC,QAAQ,IAAI8C,KAAK,GAAGL,MAAM,CAACoB,cAAc,CAAC,CAAC;MAC1G1D,KAAK,CAAC2D,cAAc,CAACrB,MAAM,CAACsB,MAAM,GAAG,CAAC,CAAC,GAAGC,IAAI,CAACC,GAAG,CAAC1B,CAAC,GAAGE,MAAM,CAACyB,OAAO,GAAGzB,MAAM,CAAC0B,KAAK,CAAC,IAAI,CAAC,GAAG1B,MAAM,CAAC2B,MAAM,CAAC;MAC3G3B,MAAM,CAAC4B,MAAM,CAACC,OAAO,CAACvE,WAAW,EAAEC,QAAQ,EAAEG,KAAK,CAAC,CAACoE,WAAW,CAACzE,YAAY,CAAC;MAC7E2C,MAAM,CAAC+B,IAAI,GAAGzE,WAAW,CAAC0E,UAAU,CAACxE,IAAI,CAAC;IAC5C;;IAEA;IACA6B,MAAM,CAACoB,OAAO,CAACwB,IAAI,CAAC,CAACC,CAAC,EAAEC,CAAC,KAAKA,CAAC,CAACJ,IAAI,GAAGG,CAAC,CAACH,IAAI,CAAC;IAC9C,KAAKhC,KAAK,GAAG,CAAC,EAAEA,KAAK,GAAGV,MAAM,CAACoB,OAAO,CAACf,MAAM,EAAEK,KAAK,EAAE,EAAE;MACtDC,MAAM,GAAGX,MAAM,CAACoB,OAAO,CAACV,KAAK,CAAC;MAC9BT,SAAS,CAACS,KAAK,CAAC,GAAGC,MAAM,CAACoC,OAAO,IAAIpC,MAAM,CAAC+B,IAAI,GAAG/B,MAAM,CAACqC,IAAI,GAAG,CAAC,GAAGrC,MAAM,CAAC+B,IAAI,GAAG/B,MAAM,CAACqC,IAAI,GAAG,CAAC,CAAC;MACnGlD,QAAQ,CAACsB,OAAO,CAAC6B,WAAW,CAACvC,KAAK,EAAEC,MAAM,CAAC4B,MAAM,CAAC;MAClDzC,QAAQ,CAACsB,OAAO,CAAC8B,UAAU,CAACxC,KAAK,EAAEC,MAAM,CAACwC,KAAK,CAAC;IAClD;;IAEA;IACArD,QAAQ,CAACsB,OAAO,CAACgC,QAAQ,CAACC,UAAU,CAACC,YAAY,CAACC,WAAW,GAAG,IAAI;IACpEzD,QAAQ,CAACsB,OAAO,CAACoC,cAAc,CAACD,WAAW,GAAG,IAAI;IAClD,IAAIzD,QAAQ,CAACsB,OAAO,CAACqC,aAAa,EAAE3D,QAAQ,CAACsB,OAAO,CAACqC,aAAa,CAACF,WAAW,GAAG,IAAI;EACvF,CAAC,CAAC;EACFrG,KAAK,CAACwG,eAAe,CAAC,MAAM;IAC1B,MAAMC,KAAK,GAAGzB,IAAI,CAAC0B,GAAG,CAAC9E,KAAK,EAAED,KAAK,KAAKgF,SAAS,GAAGhF,KAAK,GAAGC,KAAK,EAAEkB,MAAM,CAACoB,OAAO,CAACf,MAAM,CAAC;IACzFP,QAAQ,CAACsB,OAAO,CAACuC,KAAK,GAAGA,KAAK;IAC9B7F,cAAc,CAACgC,QAAQ,CAACsB,OAAO,CAACoC,cAAc,EAAE;MAC9CM,KAAK,EAAE,CAAC;MACRH,KAAK,EAAEA,KAAK,GAAG;IACjB,CAAC,CAAC;IACF,IAAI7D,QAAQ,CAACsB,OAAO,CAACqC,aAAa,EAAE;MAClC3F,cAAc,CAACgC,QAAQ,CAACsB,OAAO,CAACqC,aAAa,EAAE;QAC7CK,KAAK,EAAE,CAAC;QACRH,KAAK,EAAEA,KAAK,GAAG;MACjB,CAAC,CAAC;IACJ;IACA7F,cAAc,CAACgC,QAAQ,CAACsB,OAAO,CAACgC,QAAQ,CAACC,UAAU,CAACC,YAAY,EAAE;MAChEQ,KAAK,EAAE,CAAC;MACRH,KAAK,EAAEA;IACT,CAAC,CAAC;EACJ,CAAC,CAAC;EACF,IAAII,WAAW,GAAG,CAAC,CAAC7E,YAAY,GAAGsB,YAAY,CAACwD,KAAK,CAACC,KAAK,MAAM,IAAI,IAAI/E,YAAY,KAAK,KAAK,CAAC,GAAGA,YAAY,GAAG,CAAC,EAAE,CAACC,aAAa,GAAGqB,YAAY,CAACwD,KAAK,CAACE,MAAM,MAAM,IAAI,IAAI/E,aAAa,KAAK,KAAK,CAAC,GAAGA,aAAa,GAAG,CAAC,CAAC;EAC1N,MAAMgF,GAAG,GAAGjC,IAAI,CAACiC,GAAG,CAACJ,WAAW,CAAC,CAAC,CAAC,EAAEA,WAAW,CAAC,CAAC,CAAC,CAAC;EACpDA,WAAW,GAAG,CAACA,WAAW,CAAC,CAAC,CAAC,GAAGI,GAAG,EAAEJ,WAAW,CAAC,CAAC,CAAC,GAAGI,GAAG,CAAC;EAC1D,OAAO,aAAajH,KAAK,CAACkH,aAAa,CAAC,OAAO,EAAEnH,QAAQ,CAAC;IACxDwE,GAAG,EAAExC;EACP,CAAC,EAAED,KAAK,CAAC,EAAE,aAAa9B,KAAK,CAACkH,aAAa,CAAC9F,OAAO,CAAC+F,QAAQ,EAAE;IAC5DC,KAAK,EAAEtE;EACT,CAAC,EAAEtB,QAAQ,EAAE,aAAaxB,KAAK,CAACkH,aAAa,CAAC,eAAe,EAAE;IAC7DG,gBAAgB,EAAE,KAAK;IACvB9C,GAAG,EAAE3B,QAAQ;IACb0E,IAAI,EAAE,CAAC,IAAI,EAAE,IAAI,EAAE1F,KAAK,CAAC;IACzBC,aAAa,EAAEA;EACjB,CAAC,EAAE,aAAa7B,KAAK,CAACkH,aAAa,CAAC,0BAA0B,EAAE;IAC9DK,KAAK,EAAEnH,gBAAgB;IACvBoH,MAAM,EAAE,eAAe;IACvBF,IAAI,EAAE,CAAClE,MAAM,EAAE,CAAC;EAClB,CAAC,CAAC,EAAE,aAAapD,KAAK,CAACkH,aAAa,CAAC,eAAe,EAAE;IACpDI,IAAI,EAAE,CAAC,GAAGT,WAAW;EACvB,CAAC,EAAE,aAAa7G,KAAK,CAACkH,aAAa,CAAC,0BAA0B,EAAE;IAC9DK,KAAK,EAAEnH,gBAAgB;IACvBoH,MAAM,EAAE,yBAAyB;IACjCF,IAAI,EAAE,CAACvE,SAAS,EAAE,CAAC;EACrB,CAAC,CAAC,CAAC,EAAE,aAAa/C,KAAK,CAACkH,aAAa,CAAC,eAAe,EAAE;IACrDO,GAAG,EAAEhG,QAAQ,CAACiG,IAAI;IAClBC,GAAG,EAAErE,YAAY;IACjBsE,WAAW,EAAE,IAAI;IACjBC,UAAU,EAAE;EACd,CAAC,CAAC,CAAC,CAAC,CAAC;AACP,CAAC,CAAC;AACF,MAAMC,aAAa,GAAG,eAAe9H,KAAK,CAACuB,UAAU,CAAC,CAAC;EACrDsE,OAAO,GAAG,CAAC;EACXV,KAAK,GAAG,CAAC;EACT4C,MAAM,GAAG,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC;EAClBC,QAAQ,GAAG,EAAE;EACb/B,KAAK,GAAG,SAAS;EACjBH,IAAI,GAAG,EAAE;EACTf,MAAM,GAAG,CAAC;EACVkD,cAAc,GAAG,IAAI;EACrBC,UAAU,GAAG,IAAI;EACjB9C,MAAM,GAAG,CAAC;EACV+C,WAAW,GAAG,QAAQ;EACtBC,IAAI,GAAGpD,IAAI,CAACqD,MAAM,CAAC,CAAC;EACpB,GAAGvG;AACL,CAAC,EAAEC,IAAI,KAAK;EACV,SAASsG,MAAMA,CAAA,EAAG;IAChB,MAAMC,CAAC,GAAGtD,IAAI,CAACC,GAAG,CAACmD,IAAI,EAAE,CAAC,GAAG,KAAK;IAClC,OAAOE,CAAC,GAAGtD,IAAI,CAACuD,KAAK,CAACD,CAAC,CAAC;EAC1B;EACA,MAAME,MAAM,GAAGxI,KAAK,CAACyI,UAAU,CAACrH,OAAO,CAAC;EACxC,MAAMmD,GAAG,GAAGvE,KAAK,CAAC6C,MAAM,CAAC,IAAI,CAAC;EAC9B,MAAM6F,IAAI,GAAG1I,KAAK,CAAC2I,KAAK,CAAC,CAAC;EAC1B,MAAM7F,MAAM,GAAG9C,KAAK,CAACmC,OAAO,CAAC,MAAM;IACjC,OAAO,CAAC,GAAG,IAAIc,KAAK,CAAC+E,QAAQ,CAAC,CAAC,CAACL,GAAG,CAAC,CAACiB,CAAC,EAAEpF,KAAK,MAAM;MACjDwE,QAAQ;MACRD,MAAM,EAAE,IAAI5H,OAAO,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC;MAC5BsE,QAAQ,EAAE,IAAItE,OAAO,CAAC,CAAC;MACvBuI,IAAI;MACJlF,KAAK;MACLe,GAAG;MACHiB,IAAI,EAAE,CAAC;MACPH,MAAM,EAAE,IAAI/E,OAAO,CAAC,CAAC;MACrB2F,KAAK,EAAE,IAAI1F,KAAK,CAAC,CAAC;MAClBS,QAAQ,EAAEwC,KAAK,IAAIwB,IAAI,CAAC6D,EAAE,GAAGb,QAAQ;IACvC,CAAC,CAAC,CAAC;EACL,CAAC,EAAE,CAACA,QAAQ,EAAEU,IAAI,CAAC,CAAC;EACpB1I,KAAK,CAACwG,eAAe,CAAC,MAAM;IAC1B1D,MAAM,CAACgG,OAAO,CAAC,CAACC,KAAK,EAAEvF,KAAK,KAAK;MAC/B9C,UAAU,CAACqI,KAAK,EAAE;QAChBhE,MAAM;QACNkB,KAAK;QACLd,KAAK;QACLC,MAAM;QACNS,OAAO;QACPC,IAAI;QACJiC,MAAM;QACN7C,OAAO,EAAEF,IAAI,CAACiC,GAAG,CAAC,GAAG,EAAEoB,MAAM,CAAC,CAAC,CAAC;QAChCxD,cAAc,EAAEG,IAAI,CAACiC,GAAG,CAAC,GAAG,EAAE,GAAG,GAAGoB,MAAM,CAAC,CAAC,CAAC,GAAGlD;MAClD,CAAC,CAAC;MACF;;MAEA,MAAM6D,WAAW,GAAGd,UAAU,IAAI,IAAI,GAAG,KAAK,CAAC,GAAGA,UAAU,CAACa,KAAK,EAAEvF,KAAK,CAAC;MAC1E,IAAIwF,WAAW,IAAIhB,QAAQ,GAAG,CAAC,EAAE;QAC/B,IAAIiB,kBAAkB;QACtBF,KAAK,CAACtE,QAAQ,CAACR,IAAI,CAAC8E,KAAK,CAAChB,MAAM,CAAC,CAACpD,QAAQ,CAAC,CAACsE,kBAAkB,GAAGD,WAAW,IAAI,IAAI,GAAG,KAAK,CAAC,GAAGA,WAAW,CAACE,KAAK,MAAM,IAAI,IAAID,kBAAkB,KAAK,KAAK,CAAC,GAAGA,kBAAkB,GAAG;UAClLX,CAAC,EAAED,MAAM,CAAC,CAAC,GAAG,CAAC,GAAG,CAAC;UACnBc,CAAC,EAAEd,MAAM,CAAC,CAAC,GAAG,CAAC,GAAG,CAAC;UACnBe,CAAC,EAAEf,MAAM,CAAC,CAAC,GAAG,CAAC,GAAG;QACpB,CAAC,CAAC;MACJ;MACA,MAAMgB,KAAK,GAAGrE,IAAI,CAACsE,GAAG,CAACP,KAAK,CAACtE,QAAQ,CAAC6D,CAAC,CAAC;MACxC,MAAMiB,KAAK,GAAGvE,IAAI,CAACsE,GAAG,CAACP,KAAK,CAACtE,QAAQ,CAAC0E,CAAC,CAAC;MACxC,MAAMK,KAAK,GAAGxE,IAAI,CAACsE,GAAG,CAACP,KAAK,CAACtE,QAAQ,CAAC2E,CAAC,CAAC;MACxC,MAAMnC,GAAG,GAAGjC,IAAI,CAACiC,GAAG,CAACoC,KAAK,EAAEE,KAAK,EAAEC,KAAK,CAAC;MACzCT,KAAK,CAAC5F,MAAM,GAAG,CAAC;MAChB,IAAIkG,KAAK,KAAKpC,GAAG,EAAE8B,KAAK,CAAC5F,MAAM,IAAIkG,KAAK,GAAGN,KAAK,CAAChB,MAAM,CAACO,CAAC;MACzD,IAAIiB,KAAK,KAAKtC,GAAG,EAAE8B,KAAK,CAAC5F,MAAM,IAAIoG,KAAK,GAAGR,KAAK,CAAChB,MAAM,CAACoB,CAAC;MACzD,IAAIK,KAAK,KAAKvC,GAAG,EAAE8B,KAAK,CAAC5F,MAAM,IAAIqG,KAAK,GAAGT,KAAK,CAAChB,MAAM,CAACqB,CAAC;MACzDL,KAAK,CAAChE,MAAM,GAAG,CAAC,CAACiE,WAAW,IAAI,IAAI,GAAG,KAAK,CAAC,GAAGA,WAAW,CAACjE,MAAM,MAAM4B,SAAS,GAAGqC,WAAW,CAACjE,MAAM,GAAGC,IAAI,CAACiC,GAAG,CAACjC,IAAI,CAACiC,GAAG,CAAC,CAAC,EAAEgB,cAAc,CAAC,EAAEE,WAAW,KAAK,QAAQ,GAAGE,MAAM,CAAC,CAAC,GAAGF,WAAW,KAAK,QAAQ,GAAGY,KAAK,CAAC5F,MAAM,GAAG,CAAC,GAAG4F,KAAK,CAAC5F,MAAM,CAAC,IAAI4B,MAAM;IAC5P,CAAC,CAAC;EACJ,CAAC,EAAE,CAACoD,WAAW,EAAEJ,MAAM,EAAEjC,IAAI,EAAEG,KAAK,EAAEJ,OAAO,EAAET,MAAM,EAAEL,MAAM,EAAEqD,IAAI,EAAEJ,QAAQ,EAAE7C,KAAK,CAAC,CAAC;EACtFnF,KAAK,CAACwG,eAAe,CAAC,MAAM;IAC1B,MAAMiD,IAAI,GAAG3G,MAAM;IACnB0F,MAAM,CAACtE,OAAO,GAAG,CAAC,GAAGsE,MAAM,CAACtE,OAAO,EAAE,GAAGuF,IAAI,CAAC;IAC7C,OAAO,MAAM;MACXjB,MAAM,CAACtE,OAAO,GAAGsE,MAAM,CAACtE,OAAO,CAACwF,MAAM,CAACC,IAAI,IAAIA,IAAI,CAACjB,IAAI,KAAKA,IAAI,CAAC;IACpE,CAAC;EACH,CAAC,EAAE,CAAC5F,MAAM,CAAC,CAAC;EACZ9C,KAAK,CAAC4J,mBAAmB,CAAC7H,IAAI,EAAE,MAAMwC,GAAG,CAACL,OAAO,EAAE,EAAE,CAAC;EACtD,OAAO,aAAalE,KAAK,CAACkH,aAAa,CAAC,OAAO,EAAEnH,QAAQ,CAAC;IACxDwE,GAAG,EAAEA;EACP,CAAC,EAAEzC,KAAK,CAAC,CAAC;AACZ,CAAC,CAAC;AACF,MAAM+H,KAAK,GAAG,eAAe7J,KAAK,CAACuB,UAAU,CAAC,CAACO,KAAK,EAAEC,IAAI,KAAK;EAC7D,MAAMyG,MAAM,GAAGxI,KAAK,CAACyI,UAAU,CAACrH,OAAO,CAAC;EACxC,IAAIoH,MAAM,EAAE,OAAO,aAAaxI,KAAK,CAACkH,aAAa,CAACY,aAAa,EAAE/H,QAAQ,CAAC;IAC1EwE,GAAG,EAAExC;EACP,CAAC,EAAED,KAAK,CAAC,CAAC;EACV,OAAO,aAAa9B,KAAK,CAACkH,aAAa,CAAC5F,MAAM,EAAE,IAAI,EAAE,aAAatB,KAAK,CAACkH,aAAa,CAACY,aAAa,EAAE/H,QAAQ,CAAC;IAC7GwE,GAAG,EAAExC;EACP,CAAC,EAAED,KAAK,CAAC,CAAC,CAAC;AACb,CAAC,CAAC;AAEF,SAAS+H,KAAK,EAAE/B,aAAa,EAAExG,MAAM", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}