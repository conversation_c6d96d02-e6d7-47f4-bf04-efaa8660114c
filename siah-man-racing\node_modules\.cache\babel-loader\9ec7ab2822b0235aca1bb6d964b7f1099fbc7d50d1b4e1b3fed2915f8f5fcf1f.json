{"ast": null, "code": "import { Mesh, <PERSON>ufferGeometry, BufferAttribute, DynamicDrawUsage, Sphere, Vector3, Color } from \"three\";\nclass MarchingCubes extends Mesh {\n  constructor(resolution, material, enableUvs = false, enableColors = false, maxPolyCount = 1e4) {\n    const geometry = new BufferGeometry();\n    super(geometry, material);\n    this.isMarchingCubes = true;\n    const scope = this;\n    const vlist = new Float32Array(12 * 3);\n    const nlist = new Float32Array(12 * 3);\n    const clist = new Float32Array(12 * 3);\n    this.enableUvs = enableUvs;\n    this.enableColors = enableColors;\n    this.init = function (resolution2) {\n      this.resolution = resolution2;\n      this.isolation = 80;\n      this.size = resolution2;\n      this.size2 = this.size * this.size;\n      this.size3 = this.size2 * this.size;\n      this.halfsize = this.size / 2;\n      this.delta = 2 / this.size;\n      this.yd = this.size;\n      this.zd = this.size2;\n      this.field = new Float32Array(this.size3);\n      this.normal_cache = new Float32Array(this.size3 * 3);\n      this.palette = new Float32Array(this.size3 * 3);\n      this.count = 0;\n      const maxVertexCount = maxPolyCount * 3;\n      this.positionArray = new Float32Array(maxVertexCount * 3);\n      const positionAttribute = new BufferAttribute(this.positionArray, 3);\n      positionAttribute.setUsage(DynamicDrawUsage);\n      geometry.setAttribute(\"position\", positionAttribute);\n      this.normalArray = new Float32Array(maxVertexCount * 3);\n      const normalAttribute = new BufferAttribute(this.normalArray, 3);\n      normalAttribute.setUsage(DynamicDrawUsage);\n      geometry.setAttribute(\"normal\", normalAttribute);\n      if (this.enableUvs) {\n        this.uvArray = new Float32Array(maxVertexCount * 2);\n        const uvAttribute = new BufferAttribute(this.uvArray, 2);\n        uvAttribute.setUsage(DynamicDrawUsage);\n        geometry.setAttribute(\"uv\", uvAttribute);\n      }\n      if (this.enableColors) {\n        this.colorArray = new Float32Array(maxVertexCount * 3);\n        const colorAttribute = new BufferAttribute(this.colorArray, 3);\n        colorAttribute.setUsage(DynamicDrawUsage);\n        geometry.setAttribute(\"color\", colorAttribute);\n      }\n      geometry.boundingSphere = new Sphere(new Vector3(), 1);\n    };\n    function lerp(a, b, t) {\n      return a + (b - a) * t;\n    }\n    function VIntX(q, offset, isol, x, y, z, valp1, valp2, c_offset1, c_offset2) {\n      const mu = (isol - valp1) / (valp2 - valp1),\n        nc = scope.normal_cache;\n      vlist[offset + 0] = x + mu * scope.delta;\n      vlist[offset + 1] = y;\n      vlist[offset + 2] = z;\n      nlist[offset + 0] = lerp(nc[q + 0], nc[q + 3], mu);\n      nlist[offset + 1] = lerp(nc[q + 1], nc[q + 4], mu);\n      nlist[offset + 2] = lerp(nc[q + 2], nc[q + 5], mu);\n      clist[offset + 0] = lerp(scope.palette[c_offset1 * 3 + 0], scope.palette[c_offset2 * 3 + 0], mu);\n      clist[offset + 1] = lerp(scope.palette[c_offset1 * 3 + 1], scope.palette[c_offset2 * 3 + 1], mu);\n      clist[offset + 2] = lerp(scope.palette[c_offset1 * 3 + 2], scope.palette[c_offset2 * 3 + 2], mu);\n    }\n    function VIntY(q, offset, isol, x, y, z, valp1, valp2, c_offset1, c_offset2) {\n      const mu = (isol - valp1) / (valp2 - valp1),\n        nc = scope.normal_cache;\n      vlist[offset + 0] = x;\n      vlist[offset + 1] = y + mu * scope.delta;\n      vlist[offset + 2] = z;\n      const q2 = q + scope.yd * 3;\n      nlist[offset + 0] = lerp(nc[q + 0], nc[q2 + 0], mu);\n      nlist[offset + 1] = lerp(nc[q + 1], nc[q2 + 1], mu);\n      nlist[offset + 2] = lerp(nc[q + 2], nc[q2 + 2], mu);\n      clist[offset + 0] = lerp(scope.palette[c_offset1 * 3 + 0], scope.palette[c_offset2 * 3 + 0], mu);\n      clist[offset + 1] = lerp(scope.palette[c_offset1 * 3 + 1], scope.palette[c_offset2 * 3 + 1], mu);\n      clist[offset + 2] = lerp(scope.palette[c_offset1 * 3 + 2], scope.palette[c_offset2 * 3 + 2], mu);\n    }\n    function VIntZ(q, offset, isol, x, y, z, valp1, valp2, c_offset1, c_offset2) {\n      const mu = (isol - valp1) / (valp2 - valp1),\n        nc = scope.normal_cache;\n      vlist[offset + 0] = x;\n      vlist[offset + 1] = y;\n      vlist[offset + 2] = z + mu * scope.delta;\n      const q2 = q + scope.zd * 3;\n      nlist[offset + 0] = lerp(nc[q + 0], nc[q2 + 0], mu);\n      nlist[offset + 1] = lerp(nc[q + 1], nc[q2 + 1], mu);\n      nlist[offset + 2] = lerp(nc[q + 2], nc[q2 + 2], mu);\n      clist[offset + 0] = lerp(scope.palette[c_offset1 * 3 + 0], scope.palette[c_offset2 * 3 + 0], mu);\n      clist[offset + 1] = lerp(scope.palette[c_offset1 * 3 + 1], scope.palette[c_offset2 * 3 + 1], mu);\n      clist[offset + 2] = lerp(scope.palette[c_offset1 * 3 + 2], scope.palette[c_offset2 * 3 + 2], mu);\n    }\n    function compNorm(q) {\n      const q3 = q * 3;\n      if (scope.normal_cache[q3] === 0) {\n        scope.normal_cache[q3 + 0] = scope.field[q - 1] - scope.field[q + 1];\n        scope.normal_cache[q3 + 1] = scope.field[q - scope.yd] - scope.field[q + scope.yd];\n        scope.normal_cache[q3 + 2] = scope.field[q - scope.zd] - scope.field[q + scope.zd];\n      }\n    }\n    function polygonize(fx, fy, fz, q, isol) {\n      const q1 = q + 1,\n        qy = q + scope.yd,\n        qz = q + scope.zd,\n        q1y = q1 + scope.yd,\n        q1z = q1 + scope.zd,\n        qyz = q + scope.yd + scope.zd,\n        q1yz = q1 + scope.yd + scope.zd;\n      let cubeindex = 0;\n      const field0 = scope.field[q],\n        field1 = scope.field[q1],\n        field2 = scope.field[qy],\n        field3 = scope.field[q1y],\n        field4 = scope.field[qz],\n        field5 = scope.field[q1z],\n        field6 = scope.field[qyz],\n        field7 = scope.field[q1yz];\n      if (field0 < isol) cubeindex |= 1;\n      if (field1 < isol) cubeindex |= 2;\n      if (field2 < isol) cubeindex |= 8;\n      if (field3 < isol) cubeindex |= 4;\n      if (field4 < isol) cubeindex |= 16;\n      if (field5 < isol) cubeindex |= 32;\n      if (field6 < isol) cubeindex |= 128;\n      if (field7 < isol) cubeindex |= 64;\n      const bits = edgeTable[cubeindex];\n      if (bits === 0) return 0;\n      const d = scope.delta,\n        fx2 = fx + d,\n        fy2 = fy + d,\n        fz2 = fz + d;\n      if (bits & 1) {\n        compNorm(q);\n        compNorm(q1);\n        VIntX(q * 3, 0, isol, fx, fy, fz, field0, field1, q, q1);\n      }\n      if (bits & 2) {\n        compNorm(q1);\n        compNorm(q1y);\n        VIntY(q1 * 3, 3, isol, fx2, fy, fz, field1, field3, q1, q1y);\n      }\n      if (bits & 4) {\n        compNorm(qy);\n        compNorm(q1y);\n        VIntX(qy * 3, 6, isol, fx, fy2, fz, field2, field3, qy, q1y);\n      }\n      if (bits & 8) {\n        compNorm(q);\n        compNorm(qy);\n        VIntY(q * 3, 9, isol, fx, fy, fz, field0, field2, q, qy);\n      }\n      if (bits & 16) {\n        compNorm(qz);\n        compNorm(q1z);\n        VIntX(qz * 3, 12, isol, fx, fy, fz2, field4, field5, qz, q1z);\n      }\n      if (bits & 32) {\n        compNorm(q1z);\n        compNorm(q1yz);\n        VIntY(q1z * 3, 15, isol, fx2, fy, fz2, field5, field7, q1z, q1yz);\n      }\n      if (bits & 64) {\n        compNorm(qyz);\n        compNorm(q1yz);\n        VIntX(qyz * 3, 18, isol, fx, fy2, fz2, field6, field7, qyz, q1yz);\n      }\n      if (bits & 128) {\n        compNorm(qz);\n        compNorm(qyz);\n        VIntY(qz * 3, 21, isol, fx, fy, fz2, field4, field6, qz, qyz);\n      }\n      if (bits & 256) {\n        compNorm(q);\n        compNorm(qz);\n        VIntZ(q * 3, 24, isol, fx, fy, fz, field0, field4, q, qz);\n      }\n      if (bits & 512) {\n        compNorm(q1);\n        compNorm(q1z);\n        VIntZ(q1 * 3, 27, isol, fx2, fy, fz, field1, field5, q1, q1z);\n      }\n      if (bits & 1024) {\n        compNorm(q1y);\n        compNorm(q1yz);\n        VIntZ(q1y * 3, 30, isol, fx2, fy2, fz, field3, field7, q1y, q1yz);\n      }\n      if (bits & 2048) {\n        compNorm(qy);\n        compNorm(qyz);\n        VIntZ(qy * 3, 33, isol, fx, fy2, fz, field2, field6, qy, qyz);\n      }\n      cubeindex <<= 4;\n      let o1,\n        o2,\n        o3,\n        numtris = 0,\n        i = 0;\n      while (triTable[cubeindex + i] != -1) {\n        o1 = cubeindex + i;\n        o2 = o1 + 1;\n        o3 = o1 + 2;\n        posnormtriv(vlist, nlist, clist, 3 * triTable[o1], 3 * triTable[o2], 3 * triTable[o3]);\n        i += 3;\n        numtris++;\n      }\n      return numtris;\n    }\n    function posnormtriv(pos, norm, colors, o1, o2, o3) {\n      const c = scope.count * 3;\n      scope.positionArray[c + 0] = pos[o1];\n      scope.positionArray[c + 1] = pos[o1 + 1];\n      scope.positionArray[c + 2] = pos[o1 + 2];\n      scope.positionArray[c + 3] = pos[o2];\n      scope.positionArray[c + 4] = pos[o2 + 1];\n      scope.positionArray[c + 5] = pos[o2 + 2];\n      scope.positionArray[c + 6] = pos[o3];\n      scope.positionArray[c + 7] = pos[o3 + 1];\n      scope.positionArray[c + 8] = pos[o3 + 2];\n      if (scope.material.flatShading === true) {\n        const nx = (norm[o1 + 0] + norm[o2 + 0] + norm[o3 + 0]) / 3;\n        const ny = (norm[o1 + 1] + norm[o2 + 1] + norm[o3 + 1]) / 3;\n        const nz = (norm[o1 + 2] + norm[o2 + 2] + norm[o3 + 2]) / 3;\n        scope.normalArray[c + 0] = nx;\n        scope.normalArray[c + 1] = ny;\n        scope.normalArray[c + 2] = nz;\n        scope.normalArray[c + 3] = nx;\n        scope.normalArray[c + 4] = ny;\n        scope.normalArray[c + 5] = nz;\n        scope.normalArray[c + 6] = nx;\n        scope.normalArray[c + 7] = ny;\n        scope.normalArray[c + 8] = nz;\n      } else {\n        scope.normalArray[c + 0] = norm[o1 + 0];\n        scope.normalArray[c + 1] = norm[o1 + 1];\n        scope.normalArray[c + 2] = norm[o1 + 2];\n        scope.normalArray[c + 3] = norm[o2 + 0];\n        scope.normalArray[c + 4] = norm[o2 + 1];\n        scope.normalArray[c + 5] = norm[o2 + 2];\n        scope.normalArray[c + 6] = norm[o3 + 0];\n        scope.normalArray[c + 7] = norm[o3 + 1];\n        scope.normalArray[c + 8] = norm[o3 + 2];\n      }\n      if (scope.enableUvs) {\n        const d = scope.count * 2;\n        scope.uvArray[d + 0] = pos[o1 + 0];\n        scope.uvArray[d + 1] = pos[o1 + 2];\n        scope.uvArray[d + 2] = pos[o2 + 0];\n        scope.uvArray[d + 3] = pos[o2 + 2];\n        scope.uvArray[d + 4] = pos[o3 + 0];\n        scope.uvArray[d + 5] = pos[o3 + 2];\n      }\n      if (scope.enableColors) {\n        scope.colorArray[c + 0] = colors[o1 + 0];\n        scope.colorArray[c + 1] = colors[o1 + 1];\n        scope.colorArray[c + 2] = colors[o1 + 2];\n        scope.colorArray[c + 3] = colors[o2 + 0];\n        scope.colorArray[c + 4] = colors[o2 + 1];\n        scope.colorArray[c + 5] = colors[o2 + 2];\n        scope.colorArray[c + 6] = colors[o3 + 0];\n        scope.colorArray[c + 7] = colors[o3 + 1];\n        scope.colorArray[c + 8] = colors[o3 + 2];\n      }\n      scope.count += 3;\n    }\n    this.addBall = function (ballx, bally, ballz, strength, subtract, colors) {\n      const sign = Math.sign(strength);\n      strength = Math.abs(strength);\n      const userDefineColor = !(colors === void 0 || colors === null);\n      let ballColor = new Color(ballx, bally, ballz);\n      if (userDefineColor) {\n        try {\n          ballColor = colors instanceof Color ? colors : Array.isArray(colors) ? new Color(Math.min(Math.abs(colors[0]), 1), Math.min(Math.abs(colors[1]), 1), Math.min(Math.abs(colors[2]), 1)) : new Color(colors);\n        } catch (err) {\n          ballColor = new Color(ballx, bally, ballz);\n        }\n      }\n      const radius = this.size * Math.sqrt(strength / subtract),\n        zs = ballz * this.size,\n        ys = bally * this.size,\n        xs = ballx * this.size;\n      let min_z = Math.floor(zs - radius);\n      if (min_z < 1) min_z = 1;\n      let max_z = Math.floor(zs + radius);\n      if (max_z > this.size - 1) max_z = this.size - 1;\n      let min_y = Math.floor(ys - radius);\n      if (min_y < 1) min_y = 1;\n      let max_y = Math.floor(ys + radius);\n      if (max_y > this.size - 1) max_y = this.size - 1;\n      let min_x = Math.floor(xs - radius);\n      if (min_x < 1) min_x = 1;\n      let max_x = Math.floor(xs + radius);\n      if (max_x > this.size - 1) max_x = this.size - 1;\n      let x, y, z, y_offset, z_offset, fx, fy, fz, fz2, fy2, val;\n      for (z = min_z; z < max_z; z++) {\n        z_offset = this.size2 * z;\n        fz = z / this.size - ballz;\n        fz2 = fz * fz;\n        for (y = min_y; y < max_y; y++) {\n          y_offset = z_offset + this.size * y;\n          fy = y / this.size - bally;\n          fy2 = fy * fy;\n          for (x = min_x; x < max_x; x++) {\n            fx = x / this.size - ballx;\n            val = strength / (1e-6 + fx * fx + fy2 + fz2) - subtract;\n            if (val > 0) {\n              this.field[y_offset + x] += val * sign;\n              const ratio = Math.sqrt((x - xs) * (x - xs) + (y - ys) * (y - ys) + (z - zs) * (z - zs)) / radius;\n              const contrib = 1 - ratio * ratio * ratio * (ratio * (ratio * 6 - 15) + 10);\n              this.palette[(y_offset + x) * 3 + 0] += ballColor.r * contrib;\n              this.palette[(y_offset + x) * 3 + 1] += ballColor.g * contrib;\n              this.palette[(y_offset + x) * 3 + 2] += ballColor.b * contrib;\n            }\n          }\n        }\n      }\n    };\n    this.addPlaneX = function (strength, subtract) {\n      const size = this.size,\n        yd = this.yd,\n        zd = this.zd,\n        field = this.field;\n      let x,\n        y,\n        z,\n        xx,\n        val,\n        xdiv,\n        cxy,\n        dist = size * Math.sqrt(strength / subtract);\n      if (dist > size) dist = size;\n      for (x = 0; x < dist; x++) {\n        xdiv = x / size;\n        xx = xdiv * xdiv;\n        val = strength / (1e-4 + xx) - subtract;\n        if (val > 0) {\n          for (y = 0; y < size; y++) {\n            cxy = x + y * yd;\n            for (z = 0; z < size; z++) {\n              field[zd * z + cxy] += val;\n            }\n          }\n        }\n      }\n    };\n    this.addPlaneY = function (strength, subtract) {\n      const size = this.size,\n        yd = this.yd,\n        zd = this.zd,\n        field = this.field;\n      let x,\n        y,\n        z,\n        yy,\n        val,\n        ydiv,\n        cy,\n        cxy,\n        dist = size * Math.sqrt(strength / subtract);\n      if (dist > size) dist = size;\n      for (y = 0; y < dist; y++) {\n        ydiv = y / size;\n        yy = ydiv * ydiv;\n        val = strength / (1e-4 + yy) - subtract;\n        if (val > 0) {\n          cy = y * yd;\n          for (x = 0; x < size; x++) {\n            cxy = cy + x;\n            for (z = 0; z < size; z++) field[zd * z + cxy] += val;\n          }\n        }\n      }\n    };\n    this.addPlaneZ = function (strength, subtract) {\n      const size = this.size,\n        yd = this.yd,\n        zd = this.zd,\n        field = this.field;\n      let x,\n        y,\n        z,\n        zz,\n        val,\n        zdiv,\n        cz,\n        cyz,\n        dist = size * Math.sqrt(strength / subtract);\n      if (dist > size) dist = size;\n      for (z = 0; z < dist; z++) {\n        zdiv = z / size;\n        zz = zdiv * zdiv;\n        val = strength / (1e-4 + zz) - subtract;\n        if (val > 0) {\n          cz = zd * z;\n          for (y = 0; y < size; y++) {\n            cyz = cz + y * yd;\n            for (x = 0; x < size; x++) field[cyz + x] += val;\n          }\n        }\n      }\n    };\n    this.setCell = function (x, y, z, value) {\n      const index = this.size2 * z + this.size * y + x;\n      this.field[index] = value;\n    };\n    this.getCell = function (x, y, z) {\n      const index = this.size2 * z + this.size * y + x;\n      return this.field[index];\n    };\n    this.blur = function (intensity = 1) {\n      const field = this.field;\n      const fieldCopy = field.slice();\n      const size = this.size;\n      const size2 = this.size2;\n      for (let x = 0; x < size; x++) {\n        for (let y = 0; y < size; y++) {\n          for (let z = 0; z < size; z++) {\n            const index = size2 * z + size * y + x;\n            let val = fieldCopy[index];\n            let count = 1;\n            for (let x2 = -1; x2 <= 1; x2 += 2) {\n              const x3 = x2 + x;\n              if (x3 < 0 || x3 >= size) continue;\n              for (let y2 = -1; y2 <= 1; y2 += 2) {\n                const y3 = y2 + y;\n                if (y3 < 0 || y3 >= size) continue;\n                for (let z2 = -1; z2 <= 1; z2 += 2) {\n                  const z3 = z2 + z;\n                  if (z3 < 0 || z3 >= size) continue;\n                  const index2 = size2 * z3 + size * y3 + x3;\n                  const val2 = fieldCopy[index2];\n                  count++;\n                  val += intensity * (val2 - val) / count;\n                }\n              }\n            }\n            field[index] = val;\n          }\n        }\n      }\n    };\n    this.reset = function () {\n      for (let i = 0; i < this.size3; i++) {\n        this.normal_cache[i * 3] = 0;\n        this.field[i] = 0;\n        this.palette[i * 3] = this.palette[i * 3 + 1] = this.palette[i * 3 + 2] = 0;\n      }\n    };\n    this.update = function () {\n      this.count = 0;\n      const smin2 = this.size - 2;\n      for (let z = 1; z < smin2; z++) {\n        const z_offset = this.size2 * z;\n        const fz = (z - this.halfsize) / this.halfsize;\n        for (let y = 1; y < smin2; y++) {\n          const y_offset = z_offset + this.size * y;\n          const fy = (y - this.halfsize) / this.halfsize;\n          for (let x = 1; x < smin2; x++) {\n            const fx = (x - this.halfsize) / this.halfsize;\n            const q = y_offset + x;\n            polygonize(fx, fy, fz, q, this.isolation);\n          }\n        }\n      }\n      this.geometry.setDrawRange(0, this.count);\n      geometry.getAttribute(\"position\").needsUpdate = true;\n      geometry.getAttribute(\"normal\").needsUpdate = true;\n      if (this.enableUvs) geometry.getAttribute(\"uv\").needsUpdate = true;\n      if (this.enableColors) geometry.getAttribute(\"color\").needsUpdate = true;\n      if (this.count / 3 > maxPolyCount) console.warn(\"THREE.MarchingCubes: Geometry buffers too small for rendering. Please create an instance with a higher poly count.\");\n    };\n    this.init(resolution);\n  }\n}\nconst edgeTable = new Int32Array([0, 265, 515, 778, 1030, 1295, 1541, 1804, 2060, 2309, 2575, 2822, 3082, 3331, 3593, 3840, 400, 153, 915, 666, 1430, 1183, 1941, 1692, 2460, 2197, 2975, 2710, 3482, 3219, 3993, 3728, 560, 825, 51, 314, 1590, 1855, 1077, 1340, 2620, 2869, 2111, 2358, 3642, 3891, 3129, 3376, 928, 681, 419, 170, 1958, 1711, 1445, 1196, 2988, 2725, 2479, 2214, 4010, 3747, 3497, 3232, 1120, 1385, 1635, 1898, 102, 367, 613, 876, 3180, 3429, 3695, 3942, 2154, 2403, 2665, 2912, 1520, 1273, 2035, 1786, 502, 255, 1013, 764, 3580, 3317, 4095, 3830, 2554, 2291, 3065, 2800, 1616, 1881, 1107, 1370, 598, 863, 85, 348, 3676, 3925, 3167, 3414, 2650, 2899, 2137, 2384, 1984, 1737, 1475, 1226, 966, 719, 453, 204, 4044, 3781, 3535, 3270, 3018, 2755, 2505, 2240, 2240, 2505, 2755, 3018, 3270, 3535, 3781, 4044, 204, 453, 719, 966, 1226, 1475, 1737, 1984, 2384, 2137, 2899, 2650, 3414, 3167, 3925, 3676, 348, 85, 863, 598, 1370, 1107, 1881, 1616, 2800, 3065, 2291, 2554, 3830, 4095, 3317, 3580, 764, 1013, 255, 502, 1786, 2035, 1273, 1520, 2912, 2665, 2403, 2154, 3942, 3695, 3429, 3180, 876, 613, 367, 102, 1898, 1635, 1385, 1120, 3232, 3497, 3747, 4010, 2214, 2479, 2725, 2988, 1196, 1445, 1711, 1958, 170, 419, 681, 928, 3376, 3129, 3891, 3642, 2358, 2111, 2869, 2620, 1340, 1077, 1855, 1590, 314, 51, 825, 560, 3728, 3993, 3219, 3482, 2710, 2975, 2197, 2460, 1692, 1941, 1183, 1430, 666, 915, 153, 400, 3840, 3593, 3331, 3082, 2822, 2575, 2309, 2060, 1804, 1541, 1295, 1030, 778, 515, 265, 0]);\nconst triTable = new Int32Array([-1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 0, 8, 3, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 0, 1, 9, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 1, 8, 3, 9, 8, 1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 1, 2, 10, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 0, 8, 3, 1, 2, 10, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 9, 2, 10, 0, 2, 9, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 2, 8, 3, 2, 10, 8, 10, 9, 8, -1, -1, -1, -1, -1, -1, -1, 3, 11, 2, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 0, 11, 2, 8, 11, 0, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 1, 9, 0, 2, 3, 11, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 1, 11, 2, 1, 9, 11, 9, 8, 11, -1, -1, -1, -1, -1, -1, -1, 3, 10, 1, 11, 10, 3, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 0, 10, 1, 0, 8, 10, 8, 11, 10, -1, -1, -1, -1, -1, -1, -1, 3, 9, 0, 3, 11, 9, 11, 10, 9, -1, -1, -1, -1, -1, -1, -1, 9, 8, 10, 10, 8, 11, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 4, 7, 8, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 4, 3, 0, 7, 3, 4, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 0, 1, 9, 8, 4, 7, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 4, 1, 9, 4, 7, 1, 7, 3, 1, -1, -1, -1, -1, -1, -1, -1, 1, 2, 10, 8, 4, 7, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 3, 4, 7, 3, 0, 4, 1, 2, 10, -1, -1, -1, -1, -1, -1, -1, 9, 2, 10, 9, 0, 2, 8, 4, 7, -1, -1, -1, -1, -1, -1, -1, 2, 10, 9, 2, 9, 7, 2, 7, 3, 7, 9, 4, -1, -1, -1, -1, 8, 4, 7, 3, 11, 2, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 11, 4, 7, 11, 2, 4, 2, 0, 4, -1, -1, -1, -1, -1, -1, -1, 9, 0, 1, 8, 4, 7, 2, 3, 11, -1, -1, -1, -1, -1, -1, -1, 4, 7, 11, 9, 4, 11, 9, 11, 2, 9, 2, 1, -1, -1, -1, -1, 3, 10, 1, 3, 11, 10, 7, 8, 4, -1, -1, -1, -1, -1, -1, -1, 1, 11, 10, 1, 4, 11, 1, 0, 4, 7, 11, 4, -1, -1, -1, -1, 4, 7, 8, 9, 0, 11, 9, 11, 10, 11, 0, 3, -1, -1, -1, -1, 4, 7, 11, 4, 11, 9, 9, 11, 10, -1, -1, -1, -1, -1, -1, -1, 9, 5, 4, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 9, 5, 4, 0, 8, 3, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 0, 5, 4, 1, 5, 0, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 8, 5, 4, 8, 3, 5, 3, 1, 5, -1, -1, -1, -1, -1, -1, -1, 1, 2, 10, 9, 5, 4, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 3, 0, 8, 1, 2, 10, 4, 9, 5, -1, -1, -1, -1, -1, -1, -1, 5, 2, 10, 5, 4, 2, 4, 0, 2, -1, -1, -1, -1, -1, -1, -1, 2, 10, 5, 3, 2, 5, 3, 5, 4, 3, 4, 8, -1, -1, -1, -1, 9, 5, 4, 2, 3, 11, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 0, 11, 2, 0, 8, 11, 4, 9, 5, -1, -1, -1, -1, -1, -1, -1, 0, 5, 4, 0, 1, 5, 2, 3, 11, -1, -1, -1, -1, -1, -1, -1, 2, 1, 5, 2, 5, 8, 2, 8, 11, 4, 8, 5, -1, -1, -1, -1, 10, 3, 11, 10, 1, 3, 9, 5, 4, -1, -1, -1, -1, -1, -1, -1, 4, 9, 5, 0, 8, 1, 8, 10, 1, 8, 11, 10, -1, -1, -1, -1, 5, 4, 0, 5, 0, 11, 5, 11, 10, 11, 0, 3, -1, -1, -1, -1, 5, 4, 8, 5, 8, 10, 10, 8, 11, -1, -1, -1, -1, -1, -1, -1, 9, 7, 8, 5, 7, 9, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 9, 3, 0, 9, 5, 3, 5, 7, 3, -1, -1, -1, -1, -1, -1, -1, 0, 7, 8, 0, 1, 7, 1, 5, 7, -1, -1, -1, -1, -1, -1, -1, 1, 5, 3, 3, 5, 7, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 9, 7, 8, 9, 5, 7, 10, 1, 2, -1, -1, -1, -1, -1, -1, -1, 10, 1, 2, 9, 5, 0, 5, 3, 0, 5, 7, 3, -1, -1, -1, -1, 8, 0, 2, 8, 2, 5, 8, 5, 7, 10, 5, 2, -1, -1, -1, -1, 2, 10, 5, 2, 5, 3, 3, 5, 7, -1, -1, -1, -1, -1, -1, -1, 7, 9, 5, 7, 8, 9, 3, 11, 2, -1, -1, -1, -1, -1, -1, -1, 9, 5, 7, 9, 7, 2, 9, 2, 0, 2, 7, 11, -1, -1, -1, -1, 2, 3, 11, 0, 1, 8, 1, 7, 8, 1, 5, 7, -1, -1, -1, -1, 11, 2, 1, 11, 1, 7, 7, 1, 5, -1, -1, -1, -1, -1, -1, -1, 9, 5, 8, 8, 5, 7, 10, 1, 3, 10, 3, 11, -1, -1, -1, -1, 5, 7, 0, 5, 0, 9, 7, 11, 0, 1, 0, 10, 11, 10, 0, -1, 11, 10, 0, 11, 0, 3, 10, 5, 0, 8, 0, 7, 5, 7, 0, -1, 11, 10, 5, 7, 11, 5, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 10, 6, 5, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 0, 8, 3, 5, 10, 6, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 9, 0, 1, 5, 10, 6, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 1, 8, 3, 1, 9, 8, 5, 10, 6, -1, -1, -1, -1, -1, -1, -1, 1, 6, 5, 2, 6, 1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 1, 6, 5, 1, 2, 6, 3, 0, 8, -1, -1, -1, -1, -1, -1, -1, 9, 6, 5, 9, 0, 6, 0, 2, 6, -1, -1, -1, -1, -1, -1, -1, 5, 9, 8, 5, 8, 2, 5, 2, 6, 3, 2, 8, -1, -1, -1, -1, 2, 3, 11, 10, 6, 5, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 11, 0, 8, 11, 2, 0, 10, 6, 5, -1, -1, -1, -1, -1, -1, -1, 0, 1, 9, 2, 3, 11, 5, 10, 6, -1, -1, -1, -1, -1, -1, -1, 5, 10, 6, 1, 9, 2, 9, 11, 2, 9, 8, 11, -1, -1, -1, -1, 6, 3, 11, 6, 5, 3, 5, 1, 3, -1, -1, -1, -1, -1, -1, -1, 0, 8, 11, 0, 11, 5, 0, 5, 1, 5, 11, 6, -1, -1, -1, -1, 3, 11, 6, 0, 3, 6, 0, 6, 5, 0, 5, 9, -1, -1, -1, -1, 6, 5, 9, 6, 9, 11, 11, 9, 8, -1, -1, -1, -1, -1, -1, -1, 5, 10, 6, 4, 7, 8, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 4, 3, 0, 4, 7, 3, 6, 5, 10, -1, -1, -1, -1, -1, -1, -1, 1, 9, 0, 5, 10, 6, 8, 4, 7, -1, -1, -1, -1, -1, -1, -1, 10, 6, 5, 1, 9, 7, 1, 7, 3, 7, 9, 4, -1, -1, -1, -1, 6, 1, 2, 6, 5, 1, 4, 7, 8, -1, -1, -1, -1, -1, -1, -1, 1, 2, 5, 5, 2, 6, 3, 0, 4, 3, 4, 7, -1, -1, -1, -1, 8, 4, 7, 9, 0, 5, 0, 6, 5, 0, 2, 6, -1, -1, -1, -1, 7, 3, 9, 7, 9, 4, 3, 2, 9, 5, 9, 6, 2, 6, 9, -1, 3, 11, 2, 7, 8, 4, 10, 6, 5, -1, -1, -1, -1, -1, -1, -1, 5, 10, 6, 4, 7, 2, 4, 2, 0, 2, 7, 11, -1, -1, -1, -1, 0, 1, 9, 4, 7, 8, 2, 3, 11, 5, 10, 6, -1, -1, -1, -1, 9, 2, 1, 9, 11, 2, 9, 4, 11, 7, 11, 4, 5, 10, 6, -1, 8, 4, 7, 3, 11, 5, 3, 5, 1, 5, 11, 6, -1, -1, -1, -1, 5, 1, 11, 5, 11, 6, 1, 0, 11, 7, 11, 4, 0, 4, 11, -1, 0, 5, 9, 0, 6, 5, 0, 3, 6, 11, 6, 3, 8, 4, 7, -1, 6, 5, 9, 6, 9, 11, 4, 7, 9, 7, 11, 9, -1, -1, -1, -1, 10, 4, 9, 6, 4, 10, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 4, 10, 6, 4, 9, 10, 0, 8, 3, -1, -1, -1, -1, -1, -1, -1, 10, 0, 1, 10, 6, 0, 6, 4, 0, -1, -1, -1, -1, -1, -1, -1, 8, 3, 1, 8, 1, 6, 8, 6, 4, 6, 1, 10, -1, -1, -1, -1, 1, 4, 9, 1, 2, 4, 2, 6, 4, -1, -1, -1, -1, -1, -1, -1, 3, 0, 8, 1, 2, 9, 2, 4, 9, 2, 6, 4, -1, -1, -1, -1, 0, 2, 4, 4, 2, 6, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 8, 3, 2, 8, 2, 4, 4, 2, 6, -1, -1, -1, -1, -1, -1, -1, 10, 4, 9, 10, 6, 4, 11, 2, 3, -1, -1, -1, -1, -1, -1, -1, 0, 8, 2, 2, 8, 11, 4, 9, 10, 4, 10, 6, -1, -1, -1, -1, 3, 11, 2, 0, 1, 6, 0, 6, 4, 6, 1, 10, -1, -1, -1, -1, 6, 4, 1, 6, 1, 10, 4, 8, 1, 2, 1, 11, 8, 11, 1, -1, 9, 6, 4, 9, 3, 6, 9, 1, 3, 11, 6, 3, -1, -1, -1, -1, 8, 11, 1, 8, 1, 0, 11, 6, 1, 9, 1, 4, 6, 4, 1, -1, 3, 11, 6, 3, 6, 0, 0, 6, 4, -1, -1, -1, -1, -1, -1, -1, 6, 4, 8, 11, 6, 8, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 7, 10, 6, 7, 8, 10, 8, 9, 10, -1, -1, -1, -1, -1, -1, -1, 0, 7, 3, 0, 10, 7, 0, 9, 10, 6, 7, 10, -1, -1, -1, -1, 10, 6, 7, 1, 10, 7, 1, 7, 8, 1, 8, 0, -1, -1, -1, -1, 10, 6, 7, 10, 7, 1, 1, 7, 3, -1, -1, -1, -1, -1, -1, -1, 1, 2, 6, 1, 6, 8, 1, 8, 9, 8, 6, 7, -1, -1, -1, -1, 2, 6, 9, 2, 9, 1, 6, 7, 9, 0, 9, 3, 7, 3, 9, -1, 7, 8, 0, 7, 0, 6, 6, 0, 2, -1, -1, -1, -1, -1, -1, -1, 7, 3, 2, 6, 7, 2, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 2, 3, 11, 10, 6, 8, 10, 8, 9, 8, 6, 7, -1, -1, -1, -1, 2, 0, 7, 2, 7, 11, 0, 9, 7, 6, 7, 10, 9, 10, 7, -1, 1, 8, 0, 1, 7, 8, 1, 10, 7, 6, 7, 10, 2, 3, 11, -1, 11, 2, 1, 11, 1, 7, 10, 6, 1, 6, 7, 1, -1, -1, -1, -1, 8, 9, 6, 8, 6, 7, 9, 1, 6, 11, 6, 3, 1, 3, 6, -1, 0, 9, 1, 11, 6, 7, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 7, 8, 0, 7, 0, 6, 3, 11, 0, 11, 6, 0, -1, -1, -1, -1, 7, 11, 6, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 7, 6, 11, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 3, 0, 8, 11, 7, 6, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 0, 1, 9, 11, 7, 6, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 8, 1, 9, 8, 3, 1, 11, 7, 6, -1, -1, -1, -1, -1, -1, -1, 10, 1, 2, 6, 11, 7, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 1, 2, 10, 3, 0, 8, 6, 11, 7, -1, -1, -1, -1, -1, -1, -1, 2, 9, 0, 2, 10, 9, 6, 11, 7, -1, -1, -1, -1, -1, -1, -1, 6, 11, 7, 2, 10, 3, 10, 8, 3, 10, 9, 8, -1, -1, -1, -1, 7, 2, 3, 6, 2, 7, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 7, 0, 8, 7, 6, 0, 6, 2, 0, -1, -1, -1, -1, -1, -1, -1, 2, 7, 6, 2, 3, 7, 0, 1, 9, -1, -1, -1, -1, -1, -1, -1, 1, 6, 2, 1, 8, 6, 1, 9, 8, 8, 7, 6, -1, -1, -1, -1, 10, 7, 6, 10, 1, 7, 1, 3, 7, -1, -1, -1, -1, -1, -1, -1, 10, 7, 6, 1, 7, 10, 1, 8, 7, 1, 0, 8, -1, -1, -1, -1, 0, 3, 7, 0, 7, 10, 0, 10, 9, 6, 10, 7, -1, -1, -1, -1, 7, 6, 10, 7, 10, 8, 8, 10, 9, -1, -1, -1, -1, -1, -1, -1, 6, 8, 4, 11, 8, 6, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 3, 6, 11, 3, 0, 6, 0, 4, 6, -1, -1, -1, -1, -1, -1, -1, 8, 6, 11, 8, 4, 6, 9, 0, 1, -1, -1, -1, -1, -1, -1, -1, 9, 4, 6, 9, 6, 3, 9, 3, 1, 11, 3, 6, -1, -1, -1, -1, 6, 8, 4, 6, 11, 8, 2, 10, 1, -1, -1, -1, -1, -1, -1, -1, 1, 2, 10, 3, 0, 11, 0, 6, 11, 0, 4, 6, -1, -1, -1, -1, 4, 11, 8, 4, 6, 11, 0, 2, 9, 2, 10, 9, -1, -1, -1, -1, 10, 9, 3, 10, 3, 2, 9, 4, 3, 11, 3, 6, 4, 6, 3, -1, 8, 2, 3, 8, 4, 2, 4, 6, 2, -1, -1, -1, -1, -1, -1, -1, 0, 4, 2, 4, 6, 2, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 1, 9, 0, 2, 3, 4, 2, 4, 6, 4, 3, 8, -1, -1, -1, -1, 1, 9, 4, 1, 4, 2, 2, 4, 6, -1, -1, -1, -1, -1, -1, -1, 8, 1, 3, 8, 6, 1, 8, 4, 6, 6, 10, 1, -1, -1, -1, -1, 10, 1, 0, 10, 0, 6, 6, 0, 4, -1, -1, -1, -1, -1, -1, -1, 4, 6, 3, 4, 3, 8, 6, 10, 3, 0, 3, 9, 10, 9, 3, -1, 10, 9, 4, 6, 10, 4, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 4, 9, 5, 7, 6, 11, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 0, 8, 3, 4, 9, 5, 11, 7, 6, -1, -1, -1, -1, -1, -1, -1, 5, 0, 1, 5, 4, 0, 7, 6, 11, -1, -1, -1, -1, -1, -1, -1, 11, 7, 6, 8, 3, 4, 3, 5, 4, 3, 1, 5, -1, -1, -1, -1, 9, 5, 4, 10, 1, 2, 7, 6, 11, -1, -1, -1, -1, -1, -1, -1, 6, 11, 7, 1, 2, 10, 0, 8, 3, 4, 9, 5, -1, -1, -1, -1, 7, 6, 11, 5, 4, 10, 4, 2, 10, 4, 0, 2, -1, -1, -1, -1, 3, 4, 8, 3, 5, 4, 3, 2, 5, 10, 5, 2, 11, 7, 6, -1, 7, 2, 3, 7, 6, 2, 5, 4, 9, -1, -1, -1, -1, -1, -1, -1, 9, 5, 4, 0, 8, 6, 0, 6, 2, 6, 8, 7, -1, -1, -1, -1, 3, 6, 2, 3, 7, 6, 1, 5, 0, 5, 4, 0, -1, -1, -1, -1, 6, 2, 8, 6, 8, 7, 2, 1, 8, 4, 8, 5, 1, 5, 8, -1, 9, 5, 4, 10, 1, 6, 1, 7, 6, 1, 3, 7, -1, -1, -1, -1, 1, 6, 10, 1, 7, 6, 1, 0, 7, 8, 7, 0, 9, 5, 4, -1, 4, 0, 10, 4, 10, 5, 0, 3, 10, 6, 10, 7, 3, 7, 10, -1, 7, 6, 10, 7, 10, 8, 5, 4, 10, 4, 8, 10, -1, -1, -1, -1, 6, 9, 5, 6, 11, 9, 11, 8, 9, -1, -1, -1, -1, -1, -1, -1, 3, 6, 11, 0, 6, 3, 0, 5, 6, 0, 9, 5, -1, -1, -1, -1, 0, 11, 8, 0, 5, 11, 0, 1, 5, 5, 6, 11, -1, -1, -1, -1, 6, 11, 3, 6, 3, 5, 5, 3, 1, -1, -1, -1, -1, -1, -1, -1, 1, 2, 10, 9, 5, 11, 9, 11, 8, 11, 5, 6, -1, -1, -1, -1, 0, 11, 3, 0, 6, 11, 0, 9, 6, 5, 6, 9, 1, 2, 10, -1, 11, 8, 5, 11, 5, 6, 8, 0, 5, 10, 5, 2, 0, 2, 5, -1, 6, 11, 3, 6, 3, 5, 2, 10, 3, 10, 5, 3, -1, -1, -1, -1, 5, 8, 9, 5, 2, 8, 5, 6, 2, 3, 8, 2, -1, -1, -1, -1, 9, 5, 6, 9, 6, 0, 0, 6, 2, -1, -1, -1, -1, -1, -1, -1, 1, 5, 8, 1, 8, 0, 5, 6, 8, 3, 8, 2, 6, 2, 8, -1, 1, 5, 6, 2, 1, 6, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 1, 3, 6, 1, 6, 10, 3, 8, 6, 5, 6, 9, 8, 9, 6, -1, 10, 1, 0, 10, 0, 6, 9, 5, 0, 5, 6, 0, -1, -1, -1, -1, 0, 3, 8, 5, 6, 10, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 10, 5, 6, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 11, 5, 10, 7, 5, 11, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 11, 5, 10, 11, 7, 5, 8, 3, 0, -1, -1, -1, -1, -1, -1, -1, 5, 11, 7, 5, 10, 11, 1, 9, 0, -1, -1, -1, -1, -1, -1, -1, 10, 7, 5, 10, 11, 7, 9, 8, 1, 8, 3, 1, -1, -1, -1, -1, 11, 1, 2, 11, 7, 1, 7, 5, 1, -1, -1, -1, -1, -1, -1, -1, 0, 8, 3, 1, 2, 7, 1, 7, 5, 7, 2, 11, -1, -1, -1, -1, 9, 7, 5, 9, 2, 7, 9, 0, 2, 2, 11, 7, -1, -1, -1, -1, 7, 5, 2, 7, 2, 11, 5, 9, 2, 3, 2, 8, 9, 8, 2, -1, 2, 5, 10, 2, 3, 5, 3, 7, 5, -1, -1, -1, -1, -1, -1, -1, 8, 2, 0, 8, 5, 2, 8, 7, 5, 10, 2, 5, -1, -1, -1, -1, 9, 0, 1, 5, 10, 3, 5, 3, 7, 3, 10, 2, -1, -1, -1, -1, 9, 8, 2, 9, 2, 1, 8, 7, 2, 10, 2, 5, 7, 5, 2, -1, 1, 3, 5, 3, 7, 5, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 0, 8, 7, 0, 7, 1, 1, 7, 5, -1, -1, -1, -1, -1, -1, -1, 9, 0, 3, 9, 3, 5, 5, 3, 7, -1, -1, -1, -1, -1, -1, -1, 9, 8, 7, 5, 9, 7, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 5, 8, 4, 5, 10, 8, 10, 11, 8, -1, -1, -1, -1, -1, -1, -1, 5, 0, 4, 5, 11, 0, 5, 10, 11, 11, 3, 0, -1, -1, -1, -1, 0, 1, 9, 8, 4, 10, 8, 10, 11, 10, 4, 5, -1, -1, -1, -1, 10, 11, 4, 10, 4, 5, 11, 3, 4, 9, 4, 1, 3, 1, 4, -1, 2, 5, 1, 2, 8, 5, 2, 11, 8, 4, 5, 8, -1, -1, -1, -1, 0, 4, 11, 0, 11, 3, 4, 5, 11, 2, 11, 1, 5, 1, 11, -1, 0, 2, 5, 0, 5, 9, 2, 11, 5, 4, 5, 8, 11, 8, 5, -1, 9, 4, 5, 2, 11, 3, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 2, 5, 10, 3, 5, 2, 3, 4, 5, 3, 8, 4, -1, -1, -1, -1, 5, 10, 2, 5, 2, 4, 4, 2, 0, -1, -1, -1, -1, -1, -1, -1, 3, 10, 2, 3, 5, 10, 3, 8, 5, 4, 5, 8, 0, 1, 9, -1, 5, 10, 2, 5, 2, 4, 1, 9, 2, 9, 4, 2, -1, -1, -1, -1, 8, 4, 5, 8, 5, 3, 3, 5, 1, -1, -1, -1, -1, -1, -1, -1, 0, 4, 5, 1, 0, 5, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 8, 4, 5, 8, 5, 3, 9, 0, 5, 0, 3, 5, -1, -1, -1, -1, 9, 4, 5, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 4, 11, 7, 4, 9, 11, 9, 10, 11, -1, -1, -1, -1, -1, -1, -1, 0, 8, 3, 4, 9, 7, 9, 11, 7, 9, 10, 11, -1, -1, -1, -1, 1, 10, 11, 1, 11, 4, 1, 4, 0, 7, 4, 11, -1, -1, -1, -1, 3, 1, 4, 3, 4, 8, 1, 10, 4, 7, 4, 11, 10, 11, 4, -1, 4, 11, 7, 9, 11, 4, 9, 2, 11, 9, 1, 2, -1, -1, -1, -1, 9, 7, 4, 9, 11, 7, 9, 1, 11, 2, 11, 1, 0, 8, 3, -1, 11, 7, 4, 11, 4, 2, 2, 4, 0, -1, -1, -1, -1, -1, -1, -1, 11, 7, 4, 11, 4, 2, 8, 3, 4, 3, 2, 4, -1, -1, -1, -1, 2, 9, 10, 2, 7, 9, 2, 3, 7, 7, 4, 9, -1, -1, -1, -1, 9, 10, 7, 9, 7, 4, 10, 2, 7, 8, 7, 0, 2, 0, 7, -1, 3, 7, 10, 3, 10, 2, 7, 4, 10, 1, 10, 0, 4, 0, 10, -1, 1, 10, 2, 8, 7, 4, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 4, 9, 1, 4, 1, 7, 7, 1, 3, -1, -1, -1, -1, -1, -1, -1, 4, 9, 1, 4, 1, 7, 0, 8, 1, 8, 7, 1, -1, -1, -1, -1, 4, 0, 3, 7, 4, 3, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 4, 8, 7, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 9, 10, 8, 10, 11, 8, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 3, 0, 9, 3, 9, 11, 11, 9, 10, -1, -1, -1, -1, -1, -1, -1, 0, 1, 10, 0, 10, 8, 8, 10, 11, -1, -1, -1, -1, -1, -1, -1, 3, 1, 10, 11, 3, 10, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 1, 2, 11, 1, 11, 9, 9, 11, 8, -1, -1, -1, -1, -1, -1, -1, 3, 0, 9, 3, 9, 11, 1, 2, 9, 2, 11, 9, -1, -1, -1, -1, 0, 2, 11, 8, 0, 11, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 3, 2, 11, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 2, 3, 8, 2, 8, 10, 10, 8, 9, -1, -1, -1, -1, -1, -1, -1, 9, 10, 2, 0, 9, 2, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 2, 3, 8, 2, 8, 10, 0, 1, 8, 1, 10, 8, -1, -1, -1, -1, 1, 10, 2, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 1, 3, 8, 9, 1, 8, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 0, 9, 1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 0, 3, 8, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1]);\nexport { MarchingCubes, edgeTable, triTable };", "map": {"version": 3, "names": ["MarchingCubes", "<PERSON><PERSON>", "constructor", "resolution", "material", "enableUvs", "enableColors", "maxPolyCount", "geometry", "BufferGeometry", "isMarchingCubes", "scope", "vlist", "Float32Array", "nlist", "clist", "init", "resolution2", "isolation", "size", "size2", "size3", "halfsize", "delta", "yd", "zd", "field", "normal_cache", "palette", "count", "maxVertexCount", "positionArray", "positionAttribute", "BufferAttribute", "setUsage", "DynamicDrawUsage", "setAttribute", "normalArray", "normalAttribute", "uvArray", "uvAttribute", "colorArray", "colorAttribute", "boundingSphere", "Sphere", "Vector3", "lerp", "a", "b", "t", "VIntX", "q", "offset", "isol", "x", "y", "z", "valp1", "valp2", "c_offset1", "c_offset2", "mu", "nc", "VIntY", "q2", "VIntZ", "compNorm", "q3", "polygonize", "fx", "fy", "fz", "q1", "qy", "qz", "q1y", "q1z", "qyz", "q1yz", "cubeindex", "field0", "field1", "field2", "field3", "field4", "field5", "field6", "field7", "bits", "edgeTable", "d", "fx2", "fy2", "fz2", "o1", "o2", "o3", "numtris", "i", "triTable", "posnormtriv", "pos", "norm", "colors", "c", "flatShading", "nx", "ny", "nz", "addBall", "ballx", "bally", "ballz", "strength", "subtract", "sign", "Math", "abs", "userDefineColor", "ballColor", "Color", "Array", "isArray", "min", "err", "radius", "sqrt", "zs", "ys", "xs", "min_z", "floor", "max_z", "min_y", "max_y", "min_x", "max_x", "y_offset", "z_offset", "val", "ratio", "contrib", "r", "g", "addPlaneX", "xx", "xdiv", "cxy", "dist", "addPlaneY", "yy", "ydiv", "cy", "addPlaneZ", "zz", "zdiv", "cz", "cyz", "setCell", "value", "index", "getCell", "blur", "intensity", "fieldCopy", "slice", "x2", "x3", "y2", "y3", "z2", "z3", "index2", "val2", "reset", "update", "smin2", "setDrawRange", "getAttribute", "needsUpdate", "console", "warn", "Int32Array"], "sources": ["C:\\Users\\<USER>\\Documents\\Apps_Creations\\SiahMan_Racing4Speed_WebGame\\siah-man-racing\\node_modules\\src\\objects\\MarchingCubes.js"], "sourcesContent": ["import { BufferAttribute, BufferGeometry, Color, DynamicDrawUsage, Mesh, Sphere, Vector3 } from 'three'\n\n/**\n * Port of http://webglsamples.org/blob/blob.html\n */\n\nclass MarchingCubes extends Mesh {\n  constructor(resolution, material, enableUvs = false, enableColors = false, maxPolyCount = 10000) {\n    const geometry = new BufferGeometry()\n\n    super(geometry, material)\n\n    this.isMarchingCubes = true\n\n    const scope = this\n\n    // temp buffers used in polygonize\n\n    const vlist = new Float32Array(12 * 3)\n    const nlist = new Float32Array(12 * 3)\n    const clist = new Float32Array(12 * 3)\n\n    this.enableUvs = enableUvs\n    this.enableColors = enableColors\n\n    // functions have to be object properties\n    // prototype functions kill performance\n    // (tested and it was 4x slower !!!)\n\n    this.init = function (resolution) {\n      this.resolution = resolution\n\n      // parameters\n\n      this.isolation = 80.0\n\n      // size of field, 32 is pushing it in Javascript :)\n\n      this.size = resolution\n      this.size2 = this.size * this.size\n      this.size3 = this.size2 * this.size\n      this.halfsize = this.size / 2.0\n\n      // deltas\n\n      this.delta = 2.0 / this.size\n      this.yd = this.size\n      this.zd = this.size2\n\n      this.field = new Float32Array(this.size3)\n      this.normal_cache = new Float32Array(this.size3 * 3)\n      this.palette = new Float32Array(this.size3 * 3)\n\n      //\n\n      this.count = 0\n\n      const maxVertexCount = maxPolyCount * 3\n\n      this.positionArray = new Float32Array(maxVertexCount * 3)\n      const positionAttribute = new BufferAttribute(this.positionArray, 3)\n      positionAttribute.setUsage(DynamicDrawUsage)\n      geometry.setAttribute('position', positionAttribute)\n\n      this.normalArray = new Float32Array(maxVertexCount * 3)\n      const normalAttribute = new BufferAttribute(this.normalArray, 3)\n      normalAttribute.setUsage(DynamicDrawUsage)\n      geometry.setAttribute('normal', normalAttribute)\n\n      if (this.enableUvs) {\n        this.uvArray = new Float32Array(maxVertexCount * 2)\n        const uvAttribute = new BufferAttribute(this.uvArray, 2)\n        uvAttribute.setUsage(DynamicDrawUsage)\n        geometry.setAttribute('uv', uvAttribute)\n      }\n\n      if (this.enableColors) {\n        this.colorArray = new Float32Array(maxVertexCount * 3)\n        const colorAttribute = new BufferAttribute(this.colorArray, 3)\n        colorAttribute.setUsage(DynamicDrawUsage)\n        geometry.setAttribute('color', colorAttribute)\n      }\n\n      geometry.boundingSphere = new Sphere(new Vector3(), 1)\n    }\n\n    ///////////////////////\n    // Polygonization\n    ///////////////////////\n\n    function lerp(a, b, t) {\n      return a + (b - a) * t\n    }\n\n    function VIntX(q, offset, isol, x, y, z, valp1, valp2, c_offset1, c_offset2) {\n      const mu = (isol - valp1) / (valp2 - valp1),\n        nc = scope.normal_cache\n\n      vlist[offset + 0] = x + mu * scope.delta\n      vlist[offset + 1] = y\n      vlist[offset + 2] = z\n\n      nlist[offset + 0] = lerp(nc[q + 0], nc[q + 3], mu)\n      nlist[offset + 1] = lerp(nc[q + 1], nc[q + 4], mu)\n      nlist[offset + 2] = lerp(nc[q + 2], nc[q + 5], mu)\n\n      clist[offset + 0] = lerp(scope.palette[c_offset1 * 3 + 0], scope.palette[c_offset2 * 3 + 0], mu)\n      clist[offset + 1] = lerp(scope.palette[c_offset1 * 3 + 1], scope.palette[c_offset2 * 3 + 1], mu)\n      clist[offset + 2] = lerp(scope.palette[c_offset1 * 3 + 2], scope.palette[c_offset2 * 3 + 2], mu)\n    }\n\n    function VIntY(q, offset, isol, x, y, z, valp1, valp2, c_offset1, c_offset2) {\n      const mu = (isol - valp1) / (valp2 - valp1),\n        nc = scope.normal_cache\n\n      vlist[offset + 0] = x\n      vlist[offset + 1] = y + mu * scope.delta\n      vlist[offset + 2] = z\n\n      const q2 = q + scope.yd * 3\n\n      nlist[offset + 0] = lerp(nc[q + 0], nc[q2 + 0], mu)\n      nlist[offset + 1] = lerp(nc[q + 1], nc[q2 + 1], mu)\n      nlist[offset + 2] = lerp(nc[q + 2], nc[q2 + 2], mu)\n\n      clist[offset + 0] = lerp(scope.palette[c_offset1 * 3 + 0], scope.palette[c_offset2 * 3 + 0], mu)\n      clist[offset + 1] = lerp(scope.palette[c_offset1 * 3 + 1], scope.palette[c_offset2 * 3 + 1], mu)\n      clist[offset + 2] = lerp(scope.palette[c_offset1 * 3 + 2], scope.palette[c_offset2 * 3 + 2], mu)\n    }\n\n    function VIntZ(q, offset, isol, x, y, z, valp1, valp2, c_offset1, c_offset2) {\n      const mu = (isol - valp1) / (valp2 - valp1),\n        nc = scope.normal_cache\n\n      vlist[offset + 0] = x\n      vlist[offset + 1] = y\n      vlist[offset + 2] = z + mu * scope.delta\n\n      const q2 = q + scope.zd * 3\n\n      nlist[offset + 0] = lerp(nc[q + 0], nc[q2 + 0], mu)\n      nlist[offset + 1] = lerp(nc[q + 1], nc[q2 + 1], mu)\n      nlist[offset + 2] = lerp(nc[q + 2], nc[q2 + 2], mu)\n\n      clist[offset + 0] = lerp(scope.palette[c_offset1 * 3 + 0], scope.palette[c_offset2 * 3 + 0], mu)\n      clist[offset + 1] = lerp(scope.palette[c_offset1 * 3 + 1], scope.palette[c_offset2 * 3 + 1], mu)\n      clist[offset + 2] = lerp(scope.palette[c_offset1 * 3 + 2], scope.palette[c_offset2 * 3 + 2], mu)\n    }\n\n    function compNorm(q) {\n      const q3 = q * 3\n\n      if (scope.normal_cache[q3] === 0.0) {\n        scope.normal_cache[q3 + 0] = scope.field[q - 1] - scope.field[q + 1]\n        scope.normal_cache[q3 + 1] = scope.field[q - scope.yd] - scope.field[q + scope.yd]\n        scope.normal_cache[q3 + 2] = scope.field[q - scope.zd] - scope.field[q + scope.zd]\n      }\n    }\n\n    // Returns total number of triangles. Fills triangles.\n    // (this is where most of time is spent - it's inner work of O(n3) loop )\n\n    function polygonize(fx, fy, fz, q, isol) {\n      // cache indices\n      const q1 = q + 1,\n        qy = q + scope.yd,\n        qz = q + scope.zd,\n        q1y = q1 + scope.yd,\n        q1z = q1 + scope.zd,\n        qyz = q + scope.yd + scope.zd,\n        q1yz = q1 + scope.yd + scope.zd\n\n      let cubeindex = 0\n      const field0 = scope.field[q],\n        field1 = scope.field[q1],\n        field2 = scope.field[qy],\n        field3 = scope.field[q1y],\n        field4 = scope.field[qz],\n        field5 = scope.field[q1z],\n        field6 = scope.field[qyz],\n        field7 = scope.field[q1yz]\n\n      if (field0 < isol) cubeindex |= 1\n      if (field1 < isol) cubeindex |= 2\n      if (field2 < isol) cubeindex |= 8\n      if (field3 < isol) cubeindex |= 4\n      if (field4 < isol) cubeindex |= 16\n      if (field5 < isol) cubeindex |= 32\n      if (field6 < isol) cubeindex |= 128\n      if (field7 < isol) cubeindex |= 64\n\n      // if cube is entirely in/out of the surface - bail, nothing to draw\n\n      const bits = edgeTable[cubeindex]\n      if (bits === 0) return 0\n\n      const d = scope.delta,\n        fx2 = fx + d,\n        fy2 = fy + d,\n        fz2 = fz + d\n\n      // top of the cube\n\n      if (bits & 1) {\n        compNorm(q)\n        compNorm(q1)\n        VIntX(q * 3, 0, isol, fx, fy, fz, field0, field1, q, q1)\n      }\n\n      if (bits & 2) {\n        compNorm(q1)\n        compNorm(q1y)\n        VIntY(q1 * 3, 3, isol, fx2, fy, fz, field1, field3, q1, q1y)\n      }\n\n      if (bits & 4) {\n        compNorm(qy)\n        compNorm(q1y)\n        VIntX(qy * 3, 6, isol, fx, fy2, fz, field2, field3, qy, q1y)\n      }\n\n      if (bits & 8) {\n        compNorm(q)\n        compNorm(qy)\n        VIntY(q * 3, 9, isol, fx, fy, fz, field0, field2, q, qy)\n      }\n\n      // bottom of the cube\n\n      if (bits & 16) {\n        compNorm(qz)\n        compNorm(q1z)\n        VIntX(qz * 3, 12, isol, fx, fy, fz2, field4, field5, qz, q1z)\n      }\n\n      if (bits & 32) {\n        compNorm(q1z)\n        compNorm(q1yz)\n        VIntY(q1z * 3, 15, isol, fx2, fy, fz2, field5, field7, q1z, q1yz)\n      }\n\n      if (bits & 64) {\n        compNorm(qyz)\n        compNorm(q1yz)\n        VIntX(qyz * 3, 18, isol, fx, fy2, fz2, field6, field7, qyz, q1yz)\n      }\n\n      if (bits & 128) {\n        compNorm(qz)\n        compNorm(qyz)\n        VIntY(qz * 3, 21, isol, fx, fy, fz2, field4, field6, qz, qyz)\n      }\n\n      // vertical lines of the cube\n      if (bits & 256) {\n        compNorm(q)\n        compNorm(qz)\n        VIntZ(q * 3, 24, isol, fx, fy, fz, field0, field4, q, qz)\n      }\n\n      if (bits & 512) {\n        compNorm(q1)\n        compNorm(q1z)\n        VIntZ(q1 * 3, 27, isol, fx2, fy, fz, field1, field5, q1, q1z)\n      }\n\n      if (bits & 1024) {\n        compNorm(q1y)\n        compNorm(q1yz)\n        VIntZ(q1y * 3, 30, isol, fx2, fy2, fz, field3, field7, q1y, q1yz)\n      }\n\n      if (bits & 2048) {\n        compNorm(qy)\n        compNorm(qyz)\n        VIntZ(qy * 3, 33, isol, fx, fy2, fz, field2, field6, qy, qyz)\n      }\n\n      cubeindex <<= 4 // re-purpose cubeindex into an offset into triTable\n\n      let o1,\n        o2,\n        o3,\n        numtris = 0,\n        i = 0\n\n      // here is where triangles are created\n\n      while (triTable[cubeindex + i] != -1) {\n        o1 = cubeindex + i\n        o2 = o1 + 1\n        o3 = o1 + 2\n\n        posnormtriv(vlist, nlist, clist, 3 * triTable[o1], 3 * triTable[o2], 3 * triTable[o3])\n\n        i += 3\n        numtris++\n      }\n\n      return numtris\n    }\n\n    function posnormtriv(pos, norm, colors, o1, o2, o3) {\n      const c = scope.count * 3\n\n      // positions\n\n      scope.positionArray[c + 0] = pos[o1]\n      scope.positionArray[c + 1] = pos[o1 + 1]\n      scope.positionArray[c + 2] = pos[o1 + 2]\n\n      scope.positionArray[c + 3] = pos[o2]\n      scope.positionArray[c + 4] = pos[o2 + 1]\n      scope.positionArray[c + 5] = pos[o2 + 2]\n\n      scope.positionArray[c + 6] = pos[o3]\n      scope.positionArray[c + 7] = pos[o3 + 1]\n      scope.positionArray[c + 8] = pos[o3 + 2]\n\n      // normals\n\n      if (scope.material.flatShading === true) {\n        const nx = (norm[o1 + 0] + norm[o2 + 0] + norm[o3 + 0]) / 3\n        const ny = (norm[o1 + 1] + norm[o2 + 1] + norm[o3 + 1]) / 3\n        const nz = (norm[o1 + 2] + norm[o2 + 2] + norm[o3 + 2]) / 3\n\n        scope.normalArray[c + 0] = nx\n        scope.normalArray[c + 1] = ny\n        scope.normalArray[c + 2] = nz\n\n        scope.normalArray[c + 3] = nx\n        scope.normalArray[c + 4] = ny\n        scope.normalArray[c + 5] = nz\n\n        scope.normalArray[c + 6] = nx\n        scope.normalArray[c + 7] = ny\n        scope.normalArray[c + 8] = nz\n      } else {\n        scope.normalArray[c + 0] = norm[o1 + 0]\n        scope.normalArray[c + 1] = norm[o1 + 1]\n        scope.normalArray[c + 2] = norm[o1 + 2]\n\n        scope.normalArray[c + 3] = norm[o2 + 0]\n        scope.normalArray[c + 4] = norm[o2 + 1]\n        scope.normalArray[c + 5] = norm[o2 + 2]\n\n        scope.normalArray[c + 6] = norm[o3 + 0]\n        scope.normalArray[c + 7] = norm[o3 + 1]\n        scope.normalArray[c + 8] = norm[o3 + 2]\n      }\n\n      // uvs\n\n      if (scope.enableUvs) {\n        const d = scope.count * 2\n\n        scope.uvArray[d + 0] = pos[o1 + 0]\n        scope.uvArray[d + 1] = pos[o1 + 2]\n\n        scope.uvArray[d + 2] = pos[o2 + 0]\n        scope.uvArray[d + 3] = pos[o2 + 2]\n\n        scope.uvArray[d + 4] = pos[o3 + 0]\n        scope.uvArray[d + 5] = pos[o3 + 2]\n      }\n\n      // colors\n\n      if (scope.enableColors) {\n        scope.colorArray[c + 0] = colors[o1 + 0]\n        scope.colorArray[c + 1] = colors[o1 + 1]\n        scope.colorArray[c + 2] = colors[o1 + 2]\n\n        scope.colorArray[c + 3] = colors[o2 + 0]\n        scope.colorArray[c + 4] = colors[o2 + 1]\n        scope.colorArray[c + 5] = colors[o2 + 2]\n\n        scope.colorArray[c + 6] = colors[o3 + 0]\n        scope.colorArray[c + 7] = colors[o3 + 1]\n        scope.colorArray[c + 8] = colors[o3 + 2]\n      }\n\n      scope.count += 3\n    }\n\n    /////////////////////////////////////\n    // Metaballs\n    /////////////////////////////////////\n\n    // Adds a reciprocal ball (nice and blobby) that, to be fast, fades to zero after\n    // a fixed distance, determined by strength and subtract.\n\n    this.addBall = function (ballx, bally, ballz, strength, subtract, colors) {\n      const sign = Math.sign(strength)\n      strength = Math.abs(strength)\n      const userDefineColor = !(colors === undefined || colors === null)\n      let ballColor = new Color(ballx, bally, ballz)\n\n      if (userDefineColor) {\n        try {\n          ballColor =\n            colors instanceof Color\n              ? colors\n              : Array.isArray(colors)\n              ? new Color(\n                  Math.min(Math.abs(colors[0]), 1),\n                  Math.min(Math.abs(colors[1]), 1),\n                  Math.min(Math.abs(colors[2]), 1),\n                )\n              : new Color(colors)\n        } catch (err) {\n          ballColor = new Color(ballx, bally, ballz)\n        }\n      }\n\n      // Let's solve the equation to find the radius:\n      // 1.0 / (0.000001 + radius^2) * strength - subtract = 0\n      // strength / (radius^2) = subtract\n      // strength = subtract * radius^2\n      // radius^2 = strength / subtract\n      // radius = sqrt(strength / subtract)\n\n      const radius = this.size * Math.sqrt(strength / subtract),\n        zs = ballz * this.size,\n        ys = bally * this.size,\n        xs = ballx * this.size\n\n      let min_z = Math.floor(zs - radius)\n      if (min_z < 1) min_z = 1\n      let max_z = Math.floor(zs + radius)\n      if (max_z > this.size - 1) max_z = this.size - 1\n      let min_y = Math.floor(ys - radius)\n      if (min_y < 1) min_y = 1\n      let max_y = Math.floor(ys + radius)\n      if (max_y > this.size - 1) max_y = this.size - 1\n      let min_x = Math.floor(xs - radius)\n      if (min_x < 1) min_x = 1\n      let max_x = Math.floor(xs + radius)\n      if (max_x > this.size - 1) max_x = this.size - 1\n\n      // Don't polygonize in the outer layer because normals aren't\n      // well-defined there.\n\n      let x, y, z, y_offset, z_offset, fx, fy, fz, fz2, fy2, val\n\n      for (z = min_z; z < max_z; z++) {\n        z_offset = this.size2 * z\n        fz = z / this.size - ballz\n        fz2 = fz * fz\n\n        for (y = min_y; y < max_y; y++) {\n          y_offset = z_offset + this.size * y\n          fy = y / this.size - bally\n          fy2 = fy * fy\n\n          for (x = min_x; x < max_x; x++) {\n            fx = x / this.size - ballx\n            val = strength / (0.000001 + fx * fx + fy2 + fz2) - subtract\n            if (val > 0.0) {\n              this.field[y_offset + x] += val * sign\n\n              // optimization\n              // http://www.geisswerks.com/ryan/BLOBS/blobs.html\n              const ratio = Math.sqrt((x - xs) * (x - xs) + (y - ys) * (y - ys) + (z - zs) * (z - zs)) / radius\n              const contrib = 1 - ratio * ratio * ratio * (ratio * (ratio * 6 - 15) + 10)\n              this.palette[(y_offset + x) * 3 + 0] += ballColor.r * contrib\n              this.palette[(y_offset + x) * 3 + 1] += ballColor.g * contrib\n              this.palette[(y_offset + x) * 3 + 2] += ballColor.b * contrib\n            }\n          }\n        }\n      }\n    }\n\n    this.addPlaneX = function (strength, subtract) {\n      // cache attribute lookups\n      const size = this.size,\n        yd = this.yd,\n        zd = this.zd,\n        field = this.field\n\n      let x,\n        y,\n        z,\n        xx,\n        val,\n        xdiv,\n        cxy,\n        dist = size * Math.sqrt(strength / subtract)\n\n      if (dist > size) dist = size\n\n      for (x = 0; x < dist; x++) {\n        xdiv = x / size\n        xx = xdiv * xdiv\n        val = strength / (0.0001 + xx) - subtract\n\n        if (val > 0.0) {\n          for (y = 0; y < size; y++) {\n            cxy = x + y * yd\n\n            for (z = 0; z < size; z++) {\n              field[zd * z + cxy] += val\n            }\n          }\n        }\n      }\n    }\n\n    this.addPlaneY = function (strength, subtract) {\n      // cache attribute lookups\n      const size = this.size,\n        yd = this.yd,\n        zd = this.zd,\n        field = this.field\n\n      let x,\n        y,\n        z,\n        yy,\n        val,\n        ydiv,\n        cy,\n        cxy,\n        dist = size * Math.sqrt(strength / subtract)\n\n      if (dist > size) dist = size\n\n      for (y = 0; y < dist; y++) {\n        ydiv = y / size\n        yy = ydiv * ydiv\n        val = strength / (0.0001 + yy) - subtract\n\n        if (val > 0.0) {\n          cy = y * yd\n\n          for (x = 0; x < size; x++) {\n            cxy = cy + x\n\n            for (z = 0; z < size; z++) field[zd * z + cxy] += val\n          }\n        }\n      }\n    }\n\n    this.addPlaneZ = function (strength, subtract) {\n      // cache attribute lookups\n\n      const size = this.size,\n        yd = this.yd,\n        zd = this.zd,\n        field = this.field\n\n      let x,\n        y,\n        z,\n        zz,\n        val,\n        zdiv,\n        cz,\n        cyz,\n        dist = size * Math.sqrt(strength / subtract)\n\n      if (dist > size) dist = size\n\n      for (z = 0; z < dist; z++) {\n        zdiv = z / size\n        zz = zdiv * zdiv\n        val = strength / (0.0001 + zz) - subtract\n        if (val > 0.0) {\n          cz = zd * z\n\n          for (y = 0; y < size; y++) {\n            cyz = cz + y * yd\n\n            for (x = 0; x < size; x++) field[cyz + x] += val\n          }\n        }\n      }\n    }\n\n    /////////////////////////////////////\n    // Updates\n    /////////////////////////////////////\n\n    this.setCell = function (x, y, z, value) {\n      const index = this.size2 * z + this.size * y + x\n      this.field[index] = value\n    }\n\n    this.getCell = function (x, y, z) {\n      const index = this.size2 * z + this.size * y + x\n      return this.field[index]\n    }\n\n    this.blur = function (intensity = 1) {\n      const field = this.field\n      const fieldCopy = field.slice()\n      const size = this.size\n      const size2 = this.size2\n      for (let x = 0; x < size; x++) {\n        for (let y = 0; y < size; y++) {\n          for (let z = 0; z < size; z++) {\n            const index = size2 * z + size * y + x\n            let val = fieldCopy[index]\n            let count = 1\n\n            for (let x2 = -1; x2 <= 1; x2 += 2) {\n              const x3 = x2 + x\n              if (x3 < 0 || x3 >= size) continue\n\n              for (let y2 = -1; y2 <= 1; y2 += 2) {\n                const y3 = y2 + y\n                if (y3 < 0 || y3 >= size) continue\n\n                for (let z2 = -1; z2 <= 1; z2 += 2) {\n                  const z3 = z2 + z\n                  if (z3 < 0 || z3 >= size) continue\n\n                  const index2 = size2 * z3 + size * y3 + x3\n                  const val2 = fieldCopy[index2]\n\n                  count++\n                  val += (intensity * (val2 - val)) / count\n                }\n              }\n            }\n\n            field[index] = val\n          }\n        }\n      }\n    }\n\n    this.reset = function () {\n      // wipe the normal cache\n\n      for (let i = 0; i < this.size3; i++) {\n        this.normal_cache[i * 3] = 0.0\n        this.field[i] = 0.0\n        this.palette[i * 3] = this.palette[i * 3 + 1] = this.palette[i * 3 + 2] = 0.0\n      }\n    }\n\n    this.update = function () {\n      this.count = 0\n\n      // Triangulate. Yeah, this is slow.\n\n      const smin2 = this.size - 2\n\n      for (let z = 1; z < smin2; z++) {\n        const z_offset = this.size2 * z\n        const fz = (z - this.halfsize) / this.halfsize //+ 1\n\n        for (let y = 1; y < smin2; y++) {\n          const y_offset = z_offset + this.size * y\n          const fy = (y - this.halfsize) / this.halfsize //+ 1\n\n          for (let x = 1; x < smin2; x++) {\n            const fx = (x - this.halfsize) / this.halfsize //+ 1\n            const q = y_offset + x\n\n            polygonize(fx, fy, fz, q, this.isolation)\n          }\n        }\n      }\n\n      // set the draw range to only the processed triangles\n\n      this.geometry.setDrawRange(0, this.count)\n\n      // update geometry data\n\n      geometry.getAttribute('position').needsUpdate = true\n      geometry.getAttribute('normal').needsUpdate = true\n\n      if (this.enableUvs) geometry.getAttribute('uv').needsUpdate = true\n      if (this.enableColors) geometry.getAttribute('color').needsUpdate = true\n\n      // safety check\n\n      if (this.count / 3 > maxPolyCount)\n        console.warn(\n          'THREE.MarchingCubes: Geometry buffers too small for rendering. Please create an instance with a higher poly count.',\n        )\n    }\n\n    this.init(resolution)\n  }\n}\n\n/////////////////////////////////////\n// Marching cubes lookup tables\n/////////////////////////////////////\n\n// These tables are straight from Paul Bourke's page:\n// http://paulbourke.net/geometry/polygonise/\n// who in turn got them from Cory Gene Bloyd.\n\n// prettier-ignore\nconst edgeTable = new Int32Array( [\n\t0x0, 0x109, 0x203, 0x30a, 0x406, 0x50f, 0x605, 0x70c,\n\t0x80c, 0x905, 0xa0f, 0xb06, 0xc0a, 0xd03, 0xe09, 0xf00,\n\t0x190, 0x99, 0x393, 0x29a, 0x596, 0x49f, 0x795, 0x69c,\n\t0x99c, 0x895, 0xb9f, 0xa96, 0xd9a, 0xc93, 0xf99, 0xe90,\n\t0x230, 0x339, 0x33, 0x13a, 0x636, 0x73f, 0x435, 0x53c,\n\t0xa3c, 0xb35, 0x83f, 0x936, 0xe3a, 0xf33, 0xc39, 0xd30,\n\t0x3a0, 0x2a9, 0x1a3, 0xaa, 0x7a6, 0x6af, 0x5a5, 0x4ac,\n\t0xbac, 0xaa5, 0x9af, 0x8a6, 0xfaa, 0xea3, 0xda9, 0xca0,\n\t0x460, 0x569, 0x663, 0x76a, 0x66, 0x16f, 0x265, 0x36c,\n\t0xc6c, 0xd65, 0xe6f, 0xf66, 0x86a, 0x963, 0xa69, 0xb60,\n\t0x5f0, 0x4f9, 0x7f3, 0x6fa, 0x1f6, 0xff, 0x3f5, 0x2fc,\n\t0xdfc, 0xcf5, 0xfff, 0xef6, 0x9fa, 0x8f3, 0xbf9, 0xaf0,\n\t0x650, 0x759, 0x453, 0x55a, 0x256, 0x35f, 0x55, 0x15c,\n\t0xe5c, 0xf55, 0xc5f, 0xd56, 0xa5a, 0xb53, 0x859, 0x950,\n\t0x7c0, 0x6c9, 0x5c3, 0x4ca, 0x3c6, 0x2cf, 0x1c5, 0xcc,\n\t0xfcc, 0xec5, 0xdcf, 0xcc6, 0xbca, 0xac3, 0x9c9, 0x8c0,\n\t0x8c0, 0x9c9, 0xac3, 0xbca, 0xcc6, 0xdcf, 0xec5, 0xfcc,\n\t0xcc, 0x1c5, 0x2cf, 0x3c6, 0x4ca, 0x5c3, 0x6c9, 0x7c0,\n\t0x950, 0x859, 0xb53, 0xa5a, 0xd56, 0xc5f, 0xf55, 0xe5c,\n\t0x15c, 0x55, 0x35f, 0x256, 0x55a, 0x453, 0x759, 0x650,\n\t0xaf0, 0xbf9, 0x8f3, 0x9fa, 0xef6, 0xfff, 0xcf5, 0xdfc,\n\t0x2fc, 0x3f5, 0xff, 0x1f6, 0x6fa, 0x7f3, 0x4f9, 0x5f0,\n\t0xb60, 0xa69, 0x963, 0x86a, 0xf66, 0xe6f, 0xd65, 0xc6c,\n\t0x36c, 0x265, 0x16f, 0x66, 0x76a, 0x663, 0x569, 0x460,\n\t0xca0, 0xda9, 0xea3, 0xfaa, 0x8a6, 0x9af, 0xaa5, 0xbac,\n\t0x4ac, 0x5a5, 0x6af, 0x7a6, 0xaa, 0x1a3, 0x2a9, 0x3a0,\n\t0xd30, 0xc39, 0xf33, 0xe3a, 0x936, 0x83f, 0xb35, 0xa3c,\n\t0x53c, 0x435, 0x73f, 0x636, 0x13a, 0x33, 0x339, 0x230,\n\t0xe90, 0xf99, 0xc93, 0xd9a, 0xa96, 0xb9f, 0x895, 0x99c,\n\t0x69c, 0x795, 0x49f, 0x596, 0x29a, 0x393, 0x99, 0x190,\n\t0xf00, 0xe09, 0xd03, 0xc0a, 0xb06, 0xa0f, 0x905, 0x80c,\n\t0x70c, 0x605, 0x50f, 0x406, 0x30a, 0x203, 0x109, 0x0 ] );\n\n// prettier-ignore\nconst triTable = new Int32Array( [\n\t- 1, - 1, - 1, - 1, - 1, - 1, - 1, - 1, - 1, - 1, - 1, - 1, - 1, - 1, - 1, - 1,\n\t0, 8, 3, - 1, - 1, - 1, - 1, - 1, - 1, - 1, - 1, - 1, - 1, - 1, - 1, - 1,\n\t0, 1, 9, - 1, - 1, - 1, - 1, - 1, - 1, - 1, - 1, - 1, - 1, - 1, - 1, - 1,\n\t1, 8, 3, 9, 8, 1, - 1, - 1, - 1, - 1, - 1, - 1, - 1, - 1, - 1, - 1,\n\t1, 2, 10, - 1, - 1, - 1, - 1, - 1, - 1, - 1, - 1, - 1, - 1, - 1, - 1, - 1,\n\t0, 8, 3, 1, 2, 10, - 1, - 1, - 1, - 1, - 1, - 1, - 1, - 1, - 1, - 1,\n\t9, 2, 10, 0, 2, 9, - 1, - 1, - 1, - 1, - 1, - 1, - 1, - 1, - 1, - 1,\n\t2, 8, 3, 2, 10, 8, 10, 9, 8, - 1, - 1, - 1, - 1, - 1, - 1, - 1,\n\t3, 11, 2, - 1, - 1, - 1, - 1, - 1, - 1, - 1, - 1, - 1, - 1, - 1, - 1, - 1,\n\t0, 11, 2, 8, 11, 0, - 1, - 1, - 1, - 1, - 1, - 1, - 1, - 1, - 1, - 1,\n\t1, 9, 0, 2, 3, 11, - 1, - 1, - 1, - 1, - 1, - 1, - 1, - 1, - 1, - 1,\n\t1, 11, 2, 1, 9, 11, 9, 8, 11, - 1, - 1, - 1, - 1, - 1, - 1, - 1,\n\t3, 10, 1, 11, 10, 3, - 1, - 1, - 1, - 1, - 1, - 1, - 1, - 1, - 1, - 1,\n\t0, 10, 1, 0, 8, 10, 8, 11, 10, - 1, - 1, - 1, - 1, - 1, - 1, - 1,\n\t3, 9, 0, 3, 11, 9, 11, 10, 9, - 1, - 1, - 1, - 1, - 1, - 1, - 1,\n\t9, 8, 10, 10, 8, 11, - 1, - 1, - 1, - 1, - 1, - 1, - 1, - 1, - 1, - 1,\n\t4, 7, 8, - 1, - 1, - 1, - 1, - 1, - 1, - 1, - 1, - 1, - 1, - 1, - 1, - 1,\n\t4, 3, 0, 7, 3, 4, - 1, - 1, - 1, - 1, - 1, - 1, - 1, - 1, - 1, - 1,\n\t0, 1, 9, 8, 4, 7, - 1, - 1, - 1, - 1, - 1, - 1, - 1, - 1, - 1, - 1,\n\t4, 1, 9, 4, 7, 1, 7, 3, 1, - 1, - 1, - 1, - 1, - 1, - 1, - 1,\n\t1, 2, 10, 8, 4, 7, - 1, - 1, - 1, - 1, - 1, - 1, - 1, - 1, - 1, - 1,\n\t3, 4, 7, 3, 0, 4, 1, 2, 10, - 1, - 1, - 1, - 1, - 1, - 1, - 1,\n\t9, 2, 10, 9, 0, 2, 8, 4, 7, - 1, - 1, - 1, - 1, - 1, - 1, - 1,\n\t2, 10, 9, 2, 9, 7, 2, 7, 3, 7, 9, 4, - 1, - 1, - 1, - 1,\n\t8, 4, 7, 3, 11, 2, - 1, - 1, - 1, - 1, - 1, - 1, - 1, - 1, - 1, - 1,\n\t11, 4, 7, 11, 2, 4, 2, 0, 4, - 1, - 1, - 1, - 1, - 1, - 1, - 1,\n\t9, 0, 1, 8, 4, 7, 2, 3, 11, - 1, - 1, - 1, - 1, - 1, - 1, - 1,\n\t4, 7, 11, 9, 4, 11, 9, 11, 2, 9, 2, 1, - 1, - 1, - 1, - 1,\n\t3, 10, 1, 3, 11, 10, 7, 8, 4, - 1, - 1, - 1, - 1, - 1, - 1, - 1,\n\t1, 11, 10, 1, 4, 11, 1, 0, 4, 7, 11, 4, - 1, - 1, - 1, - 1,\n\t4, 7, 8, 9, 0, 11, 9, 11, 10, 11, 0, 3, - 1, - 1, - 1, - 1,\n\t4, 7, 11, 4, 11, 9, 9, 11, 10, - 1, - 1, - 1, - 1, - 1, - 1, - 1,\n\t9, 5, 4, - 1, - 1, - 1, - 1, - 1, - 1, - 1, - 1, - 1, - 1, - 1, - 1, - 1,\n\t9, 5, 4, 0, 8, 3, - 1, - 1, - 1, - 1, - 1, - 1, - 1, - 1, - 1, - 1,\n\t0, 5, 4, 1, 5, 0, - 1, - 1, - 1, - 1, - 1, - 1, - 1, - 1, - 1, - 1,\n\t8, 5, 4, 8, 3, 5, 3, 1, 5, - 1, - 1, - 1, - 1, - 1, - 1, - 1,\n\t1, 2, 10, 9, 5, 4, - 1, - 1, - 1, - 1, - 1, - 1, - 1, - 1, - 1, - 1,\n\t3, 0, 8, 1, 2, 10, 4, 9, 5, - 1, - 1, - 1, - 1, - 1, - 1, - 1,\n\t5, 2, 10, 5, 4, 2, 4, 0, 2, - 1, - 1, - 1, - 1, - 1, - 1, - 1,\n\t2, 10, 5, 3, 2, 5, 3, 5, 4, 3, 4, 8, - 1, - 1, - 1, - 1,\n\t9, 5, 4, 2, 3, 11, - 1, - 1, - 1, - 1, - 1, - 1, - 1, - 1, - 1, - 1,\n\t0, 11, 2, 0, 8, 11, 4, 9, 5, - 1, - 1, - 1, - 1, - 1, - 1, - 1,\n\t0, 5, 4, 0, 1, 5, 2, 3, 11, - 1, - 1, - 1, - 1, - 1, - 1, - 1,\n\t2, 1, 5, 2, 5, 8, 2, 8, 11, 4, 8, 5, - 1, - 1, - 1, - 1,\n\t10, 3, 11, 10, 1, 3, 9, 5, 4, - 1, - 1, - 1, - 1, - 1, - 1, - 1,\n\t4, 9, 5, 0, 8, 1, 8, 10, 1, 8, 11, 10, - 1, - 1, - 1, - 1,\n\t5, 4, 0, 5, 0, 11, 5, 11, 10, 11, 0, 3, - 1, - 1, - 1, - 1,\n\t5, 4, 8, 5, 8, 10, 10, 8, 11, - 1, - 1, - 1, - 1, - 1, - 1, - 1,\n\t9, 7, 8, 5, 7, 9, - 1, - 1, - 1, - 1, - 1, - 1, - 1, - 1, - 1, - 1,\n\t9, 3, 0, 9, 5, 3, 5, 7, 3, - 1, - 1, - 1, - 1, - 1, - 1, - 1,\n\t0, 7, 8, 0, 1, 7, 1, 5, 7, - 1, - 1, - 1, - 1, - 1, - 1, - 1,\n\t1, 5, 3, 3, 5, 7, - 1, - 1, - 1, - 1, - 1, - 1, - 1, - 1, - 1, - 1,\n\t9, 7, 8, 9, 5, 7, 10, 1, 2, - 1, - 1, - 1, - 1, - 1, - 1, - 1,\n\t10, 1, 2, 9, 5, 0, 5, 3, 0, 5, 7, 3, - 1, - 1, - 1, - 1,\n\t8, 0, 2, 8, 2, 5, 8, 5, 7, 10, 5, 2, - 1, - 1, - 1, - 1,\n\t2, 10, 5, 2, 5, 3, 3, 5, 7, - 1, - 1, - 1, - 1, - 1, - 1, - 1,\n\t7, 9, 5, 7, 8, 9, 3, 11, 2, - 1, - 1, - 1, - 1, - 1, - 1, - 1,\n\t9, 5, 7, 9, 7, 2, 9, 2, 0, 2, 7, 11, - 1, - 1, - 1, - 1,\n\t2, 3, 11, 0, 1, 8, 1, 7, 8, 1, 5, 7, - 1, - 1, - 1, - 1,\n\t11, 2, 1, 11, 1, 7, 7, 1, 5, - 1, - 1, - 1, - 1, - 1, - 1, - 1,\n\t9, 5, 8, 8, 5, 7, 10, 1, 3, 10, 3, 11, - 1, - 1, - 1, - 1,\n\t5, 7, 0, 5, 0, 9, 7, 11, 0, 1, 0, 10, 11, 10, 0, - 1,\n\t11, 10, 0, 11, 0, 3, 10, 5, 0, 8, 0, 7, 5, 7, 0, - 1,\n\t11, 10, 5, 7, 11, 5, - 1, - 1, - 1, - 1, - 1, - 1, - 1, - 1, - 1, - 1,\n\t10, 6, 5, - 1, - 1, - 1, - 1, - 1, - 1, - 1, - 1, - 1, - 1, - 1, - 1, - 1,\n\t0, 8, 3, 5, 10, 6, - 1, - 1, - 1, - 1, - 1, - 1, - 1, - 1, - 1, - 1,\n\t9, 0, 1, 5, 10, 6, - 1, - 1, - 1, - 1, - 1, - 1, - 1, - 1, - 1, - 1,\n\t1, 8, 3, 1, 9, 8, 5, 10, 6, - 1, - 1, - 1, - 1, - 1, - 1, - 1,\n\t1, 6, 5, 2, 6, 1, - 1, - 1, - 1, - 1, - 1, - 1, - 1, - 1, - 1, - 1,\n\t1, 6, 5, 1, 2, 6, 3, 0, 8, - 1, - 1, - 1, - 1, - 1, - 1, - 1,\n\t9, 6, 5, 9, 0, 6, 0, 2, 6, - 1, - 1, - 1, - 1, - 1, - 1, - 1,\n\t5, 9, 8, 5, 8, 2, 5, 2, 6, 3, 2, 8, - 1, - 1, - 1, - 1,\n\t2, 3, 11, 10, 6, 5, - 1, - 1, - 1, - 1, - 1, - 1, - 1, - 1, - 1, - 1,\n\t11, 0, 8, 11, 2, 0, 10, 6, 5, - 1, - 1, - 1, - 1, - 1, - 1, - 1,\n\t0, 1, 9, 2, 3, 11, 5, 10, 6, - 1, - 1, - 1, - 1, - 1, - 1, - 1,\n\t5, 10, 6, 1, 9, 2, 9, 11, 2, 9, 8, 11, - 1, - 1, - 1, - 1,\n\t6, 3, 11, 6, 5, 3, 5, 1, 3, - 1, - 1, - 1, - 1, - 1, - 1, - 1,\n\t0, 8, 11, 0, 11, 5, 0, 5, 1, 5, 11, 6, - 1, - 1, - 1, - 1,\n\t3, 11, 6, 0, 3, 6, 0, 6, 5, 0, 5, 9, - 1, - 1, - 1, - 1,\n\t6, 5, 9, 6, 9, 11, 11, 9, 8, - 1, - 1, - 1, - 1, - 1, - 1, - 1,\n\t5, 10, 6, 4, 7, 8, - 1, - 1, - 1, - 1, - 1, - 1, - 1, - 1, - 1, - 1,\n\t4, 3, 0, 4, 7, 3, 6, 5, 10, - 1, - 1, - 1, - 1, - 1, - 1, - 1,\n\t1, 9, 0, 5, 10, 6, 8, 4, 7, - 1, - 1, - 1, - 1, - 1, - 1, - 1,\n\t10, 6, 5, 1, 9, 7, 1, 7, 3, 7, 9, 4, - 1, - 1, - 1, - 1,\n\t6, 1, 2, 6, 5, 1, 4, 7, 8, - 1, - 1, - 1, - 1, - 1, - 1, - 1,\n\t1, 2, 5, 5, 2, 6, 3, 0, 4, 3, 4, 7, - 1, - 1, - 1, - 1,\n\t8, 4, 7, 9, 0, 5, 0, 6, 5, 0, 2, 6, - 1, - 1, - 1, - 1,\n\t7, 3, 9, 7, 9, 4, 3, 2, 9, 5, 9, 6, 2, 6, 9, - 1,\n\t3, 11, 2, 7, 8, 4, 10, 6, 5, - 1, - 1, - 1, - 1, - 1, - 1, - 1,\n\t5, 10, 6, 4, 7, 2, 4, 2, 0, 2, 7, 11, - 1, - 1, - 1, - 1,\n\t0, 1, 9, 4, 7, 8, 2, 3, 11, 5, 10, 6, - 1, - 1, - 1, - 1,\n\t9, 2, 1, 9, 11, 2, 9, 4, 11, 7, 11, 4, 5, 10, 6, - 1,\n\t8, 4, 7, 3, 11, 5, 3, 5, 1, 5, 11, 6, - 1, - 1, - 1, - 1,\n\t5, 1, 11, 5, 11, 6, 1, 0, 11, 7, 11, 4, 0, 4, 11, - 1,\n\t0, 5, 9, 0, 6, 5, 0, 3, 6, 11, 6, 3, 8, 4, 7, - 1,\n\t6, 5, 9, 6, 9, 11, 4, 7, 9, 7, 11, 9, - 1, - 1, - 1, - 1,\n\t10, 4, 9, 6, 4, 10, - 1, - 1, - 1, - 1, - 1, - 1, - 1, - 1, - 1, - 1,\n\t4, 10, 6, 4, 9, 10, 0, 8, 3, - 1, - 1, - 1, - 1, - 1, - 1, - 1,\n\t10, 0, 1, 10, 6, 0, 6, 4, 0, - 1, - 1, - 1, - 1, - 1, - 1, - 1,\n\t8, 3, 1, 8, 1, 6, 8, 6, 4, 6, 1, 10, - 1, - 1, - 1, - 1,\n\t1, 4, 9, 1, 2, 4, 2, 6, 4, - 1, - 1, - 1, - 1, - 1, - 1, - 1,\n\t3, 0, 8, 1, 2, 9, 2, 4, 9, 2, 6, 4, - 1, - 1, - 1, - 1,\n\t0, 2, 4, 4, 2, 6, - 1, - 1, - 1, - 1, - 1, - 1, - 1, - 1, - 1, - 1,\n\t8, 3, 2, 8, 2, 4, 4, 2, 6, - 1, - 1, - 1, - 1, - 1, - 1, - 1,\n\t10, 4, 9, 10, 6, 4, 11, 2, 3, - 1, - 1, - 1, - 1, - 1, - 1, - 1,\n\t0, 8, 2, 2, 8, 11, 4, 9, 10, 4, 10, 6, - 1, - 1, - 1, - 1,\n\t3, 11, 2, 0, 1, 6, 0, 6, 4, 6, 1, 10, - 1, - 1, - 1, - 1,\n\t6, 4, 1, 6, 1, 10, 4, 8, 1, 2, 1, 11, 8, 11, 1, - 1,\n\t9, 6, 4, 9, 3, 6, 9, 1, 3, 11, 6, 3, - 1, - 1, - 1, - 1,\n\t8, 11, 1, 8, 1, 0, 11, 6, 1, 9, 1, 4, 6, 4, 1, - 1,\n\t3, 11, 6, 3, 6, 0, 0, 6, 4, - 1, - 1, - 1, - 1, - 1, - 1, - 1,\n\t6, 4, 8, 11, 6, 8, - 1, - 1, - 1, - 1, - 1, - 1, - 1, - 1, - 1, - 1,\n\t7, 10, 6, 7, 8, 10, 8, 9, 10, - 1, - 1, - 1, - 1, - 1, - 1, - 1,\n\t0, 7, 3, 0, 10, 7, 0, 9, 10, 6, 7, 10, - 1, - 1, - 1, - 1,\n\t10, 6, 7, 1, 10, 7, 1, 7, 8, 1, 8, 0, - 1, - 1, - 1, - 1,\n\t10, 6, 7, 10, 7, 1, 1, 7, 3, - 1, - 1, - 1, - 1, - 1, - 1, - 1,\n\t1, 2, 6, 1, 6, 8, 1, 8, 9, 8, 6, 7, - 1, - 1, - 1, - 1,\n\t2, 6, 9, 2, 9, 1, 6, 7, 9, 0, 9, 3, 7, 3, 9, - 1,\n\t7, 8, 0, 7, 0, 6, 6, 0, 2, - 1, - 1, - 1, - 1, - 1, - 1, - 1,\n\t7, 3, 2, 6, 7, 2, - 1, - 1, - 1, - 1, - 1, - 1, - 1, - 1, - 1, - 1,\n\t2, 3, 11, 10, 6, 8, 10, 8, 9, 8, 6, 7, - 1, - 1, - 1, - 1,\n\t2, 0, 7, 2, 7, 11, 0, 9, 7, 6, 7, 10, 9, 10, 7, - 1,\n\t1, 8, 0, 1, 7, 8, 1, 10, 7, 6, 7, 10, 2, 3, 11, - 1,\n\t11, 2, 1, 11, 1, 7, 10, 6, 1, 6, 7, 1, - 1, - 1, - 1, - 1,\n\t8, 9, 6, 8, 6, 7, 9, 1, 6, 11, 6, 3, 1, 3, 6, - 1,\n\t0, 9, 1, 11, 6, 7, - 1, - 1, - 1, - 1, - 1, - 1, - 1, - 1, - 1, - 1,\n\t7, 8, 0, 7, 0, 6, 3, 11, 0, 11, 6, 0, - 1, - 1, - 1, - 1,\n\t7, 11, 6, - 1, - 1, - 1, - 1, - 1, - 1, - 1, - 1, - 1, - 1, - 1, - 1, - 1,\n\t7, 6, 11, - 1, - 1, - 1, - 1, - 1, - 1, - 1, - 1, - 1, - 1, - 1, - 1, - 1,\n\t3, 0, 8, 11, 7, 6, - 1, - 1, - 1, - 1, - 1, - 1, - 1, - 1, - 1, - 1,\n\t0, 1, 9, 11, 7, 6, - 1, - 1, - 1, - 1, - 1, - 1, - 1, - 1, - 1, - 1,\n\t8, 1, 9, 8, 3, 1, 11, 7, 6, - 1, - 1, - 1, - 1, - 1, - 1, - 1,\n\t10, 1, 2, 6, 11, 7, - 1, - 1, - 1, - 1, - 1, - 1, - 1, - 1, - 1, - 1,\n\t1, 2, 10, 3, 0, 8, 6, 11, 7, - 1, - 1, - 1, - 1, - 1, - 1, - 1,\n\t2, 9, 0, 2, 10, 9, 6, 11, 7, - 1, - 1, - 1, - 1, - 1, - 1, - 1,\n\t6, 11, 7, 2, 10, 3, 10, 8, 3, 10, 9, 8, - 1, - 1, - 1, - 1,\n\t7, 2, 3, 6, 2, 7, - 1, - 1, - 1, - 1, - 1, - 1, - 1, - 1, - 1, - 1,\n\t7, 0, 8, 7, 6, 0, 6, 2, 0, - 1, - 1, - 1, - 1, - 1, - 1, - 1,\n\t2, 7, 6, 2, 3, 7, 0, 1, 9, - 1, - 1, - 1, - 1, - 1, - 1, - 1,\n\t1, 6, 2, 1, 8, 6, 1, 9, 8, 8, 7, 6, - 1, - 1, - 1, - 1,\n\t10, 7, 6, 10, 1, 7, 1, 3, 7, - 1, - 1, - 1, - 1, - 1, - 1, - 1,\n\t10, 7, 6, 1, 7, 10, 1, 8, 7, 1, 0, 8, - 1, - 1, - 1, - 1,\n\t0, 3, 7, 0, 7, 10, 0, 10, 9, 6, 10, 7, - 1, - 1, - 1, - 1,\n\t7, 6, 10, 7, 10, 8, 8, 10, 9, - 1, - 1, - 1, - 1, - 1, - 1, - 1,\n\t6, 8, 4, 11, 8, 6, - 1, - 1, - 1, - 1, - 1, - 1, - 1, - 1, - 1, - 1,\n\t3, 6, 11, 3, 0, 6, 0, 4, 6, - 1, - 1, - 1, - 1, - 1, - 1, - 1,\n\t8, 6, 11, 8, 4, 6, 9, 0, 1, - 1, - 1, - 1, - 1, - 1, - 1, - 1,\n\t9, 4, 6, 9, 6, 3, 9, 3, 1, 11, 3, 6, - 1, - 1, - 1, - 1,\n\t6, 8, 4, 6, 11, 8, 2, 10, 1, - 1, - 1, - 1, - 1, - 1, - 1, - 1,\n\t1, 2, 10, 3, 0, 11, 0, 6, 11, 0, 4, 6, - 1, - 1, - 1, - 1,\n\t4, 11, 8, 4, 6, 11, 0, 2, 9, 2, 10, 9, - 1, - 1, - 1, - 1,\n\t10, 9, 3, 10, 3, 2, 9, 4, 3, 11, 3, 6, 4, 6, 3, - 1,\n\t8, 2, 3, 8, 4, 2, 4, 6, 2, - 1, - 1, - 1, - 1, - 1, - 1, - 1,\n\t0, 4, 2, 4, 6, 2, - 1, - 1, - 1, - 1, - 1, - 1, - 1, - 1, - 1, - 1,\n\t1, 9, 0, 2, 3, 4, 2, 4, 6, 4, 3, 8, - 1, - 1, - 1, - 1,\n\t1, 9, 4, 1, 4, 2, 2, 4, 6, - 1, - 1, - 1, - 1, - 1, - 1, - 1,\n\t8, 1, 3, 8, 6, 1, 8, 4, 6, 6, 10, 1, - 1, - 1, - 1, - 1,\n\t10, 1, 0, 10, 0, 6, 6, 0, 4, - 1, - 1, - 1, - 1, - 1, - 1, - 1,\n\t4, 6, 3, 4, 3, 8, 6, 10, 3, 0, 3, 9, 10, 9, 3, - 1,\n\t10, 9, 4, 6, 10, 4, - 1, - 1, - 1, - 1, - 1, - 1, - 1, - 1, - 1, - 1,\n\t4, 9, 5, 7, 6, 11, - 1, - 1, - 1, - 1, - 1, - 1, - 1, - 1, - 1, - 1,\n\t0, 8, 3, 4, 9, 5, 11, 7, 6, - 1, - 1, - 1, - 1, - 1, - 1, - 1,\n\t5, 0, 1, 5, 4, 0, 7, 6, 11, - 1, - 1, - 1, - 1, - 1, - 1, - 1,\n\t11, 7, 6, 8, 3, 4, 3, 5, 4, 3, 1, 5, - 1, - 1, - 1, - 1,\n\t9, 5, 4, 10, 1, 2, 7, 6, 11, - 1, - 1, - 1, - 1, - 1, - 1, - 1,\n\t6, 11, 7, 1, 2, 10, 0, 8, 3, 4, 9, 5, - 1, - 1, - 1, - 1,\n\t7, 6, 11, 5, 4, 10, 4, 2, 10, 4, 0, 2, - 1, - 1, - 1, - 1,\n\t3, 4, 8, 3, 5, 4, 3, 2, 5, 10, 5, 2, 11, 7, 6, - 1,\n\t7, 2, 3, 7, 6, 2, 5, 4, 9, - 1, - 1, - 1, - 1, - 1, - 1, - 1,\n\t9, 5, 4, 0, 8, 6, 0, 6, 2, 6, 8, 7, - 1, - 1, - 1, - 1,\n\t3, 6, 2, 3, 7, 6, 1, 5, 0, 5, 4, 0, - 1, - 1, - 1, - 1,\n\t6, 2, 8, 6, 8, 7, 2, 1, 8, 4, 8, 5, 1, 5, 8, - 1,\n\t9, 5, 4, 10, 1, 6, 1, 7, 6, 1, 3, 7, - 1, - 1, - 1, - 1,\n\t1, 6, 10, 1, 7, 6, 1, 0, 7, 8, 7, 0, 9, 5, 4, - 1,\n\t4, 0, 10, 4, 10, 5, 0, 3, 10, 6, 10, 7, 3, 7, 10, - 1,\n\t7, 6, 10, 7, 10, 8, 5, 4, 10, 4, 8, 10, - 1, - 1, - 1, - 1,\n\t6, 9, 5, 6, 11, 9, 11, 8, 9, - 1, - 1, - 1, - 1, - 1, - 1, - 1,\n\t3, 6, 11, 0, 6, 3, 0, 5, 6, 0, 9, 5, - 1, - 1, - 1, - 1,\n\t0, 11, 8, 0, 5, 11, 0, 1, 5, 5, 6, 11, - 1, - 1, - 1, - 1,\n\t6, 11, 3, 6, 3, 5, 5, 3, 1, - 1, - 1, - 1, - 1, - 1, - 1, - 1,\n\t1, 2, 10, 9, 5, 11, 9, 11, 8, 11, 5, 6, - 1, - 1, - 1, - 1,\n\t0, 11, 3, 0, 6, 11, 0, 9, 6, 5, 6, 9, 1, 2, 10, - 1,\n\t11, 8, 5, 11, 5, 6, 8, 0, 5, 10, 5, 2, 0, 2, 5, - 1,\n\t6, 11, 3, 6, 3, 5, 2, 10, 3, 10, 5, 3, - 1, - 1, - 1, - 1,\n\t5, 8, 9, 5, 2, 8, 5, 6, 2, 3, 8, 2, - 1, - 1, - 1, - 1,\n\t9, 5, 6, 9, 6, 0, 0, 6, 2, - 1, - 1, - 1, - 1, - 1, - 1, - 1,\n\t1, 5, 8, 1, 8, 0, 5, 6, 8, 3, 8, 2, 6, 2, 8, - 1,\n\t1, 5, 6, 2, 1, 6, - 1, - 1, - 1, - 1, - 1, - 1, - 1, - 1, - 1, - 1,\n\t1, 3, 6, 1, 6, 10, 3, 8, 6, 5, 6, 9, 8, 9, 6, - 1,\n\t10, 1, 0, 10, 0, 6, 9, 5, 0, 5, 6, 0, - 1, - 1, - 1, - 1,\n\t0, 3, 8, 5, 6, 10, - 1, - 1, - 1, - 1, - 1, - 1, - 1, - 1, - 1, - 1,\n\t10, 5, 6, - 1, - 1, - 1, - 1, - 1, - 1, - 1, - 1, - 1, - 1, - 1, - 1, - 1,\n\t11, 5, 10, 7, 5, 11, - 1, - 1, - 1, - 1, - 1, - 1, - 1, - 1, - 1, - 1,\n\t11, 5, 10, 11, 7, 5, 8, 3, 0, - 1, - 1, - 1, - 1, - 1, - 1, - 1,\n\t5, 11, 7, 5, 10, 11, 1, 9, 0, - 1, - 1, - 1, - 1, - 1, - 1, - 1,\n\t10, 7, 5, 10, 11, 7, 9, 8, 1, 8, 3, 1, - 1, - 1, - 1, - 1,\n\t11, 1, 2, 11, 7, 1, 7, 5, 1, - 1, - 1, - 1, - 1, - 1, - 1, - 1,\n\t0, 8, 3, 1, 2, 7, 1, 7, 5, 7, 2, 11, - 1, - 1, - 1, - 1,\n\t9, 7, 5, 9, 2, 7, 9, 0, 2, 2, 11, 7, - 1, - 1, - 1, - 1,\n\t7, 5, 2, 7, 2, 11, 5, 9, 2, 3, 2, 8, 9, 8, 2, - 1,\n\t2, 5, 10, 2, 3, 5, 3, 7, 5, - 1, - 1, - 1, - 1, - 1, - 1, - 1,\n\t8, 2, 0, 8, 5, 2, 8, 7, 5, 10, 2, 5, - 1, - 1, - 1, - 1,\n\t9, 0, 1, 5, 10, 3, 5, 3, 7, 3, 10, 2, - 1, - 1, - 1, - 1,\n\t9, 8, 2, 9, 2, 1, 8, 7, 2, 10, 2, 5, 7, 5, 2, - 1,\n\t1, 3, 5, 3, 7, 5, - 1, - 1, - 1, - 1, - 1, - 1, - 1, - 1, - 1, - 1,\n\t0, 8, 7, 0, 7, 1, 1, 7, 5, - 1, - 1, - 1, - 1, - 1, - 1, - 1,\n\t9, 0, 3, 9, 3, 5, 5, 3, 7, - 1, - 1, - 1, - 1, - 1, - 1, - 1,\n\t9, 8, 7, 5, 9, 7, - 1, - 1, - 1, - 1, - 1, - 1, - 1, - 1, - 1, - 1,\n\t5, 8, 4, 5, 10, 8, 10, 11, 8, - 1, - 1, - 1, - 1, - 1, - 1, - 1,\n\t5, 0, 4, 5, 11, 0, 5, 10, 11, 11, 3, 0, - 1, - 1, - 1, - 1,\n\t0, 1, 9, 8, 4, 10, 8, 10, 11, 10, 4, 5, - 1, - 1, - 1, - 1,\n\t10, 11, 4, 10, 4, 5, 11, 3, 4, 9, 4, 1, 3, 1, 4, - 1,\n\t2, 5, 1, 2, 8, 5, 2, 11, 8, 4, 5, 8, - 1, - 1, - 1, - 1,\n\t0, 4, 11, 0, 11, 3, 4, 5, 11, 2, 11, 1, 5, 1, 11, - 1,\n\t0, 2, 5, 0, 5, 9, 2, 11, 5, 4, 5, 8, 11, 8, 5, - 1,\n\t9, 4, 5, 2, 11, 3, - 1, - 1, - 1, - 1, - 1, - 1, - 1, - 1, - 1, - 1,\n\t2, 5, 10, 3, 5, 2, 3, 4, 5, 3, 8, 4, - 1, - 1, - 1, - 1,\n\t5, 10, 2, 5, 2, 4, 4, 2, 0, - 1, - 1, - 1, - 1, - 1, - 1, - 1,\n\t3, 10, 2, 3, 5, 10, 3, 8, 5, 4, 5, 8, 0, 1, 9, - 1,\n\t5, 10, 2, 5, 2, 4, 1, 9, 2, 9, 4, 2, - 1, - 1, - 1, - 1,\n\t8, 4, 5, 8, 5, 3, 3, 5, 1, - 1, - 1, - 1, - 1, - 1, - 1, - 1,\n\t0, 4, 5, 1, 0, 5, - 1, - 1, - 1, - 1, - 1, - 1, - 1, - 1, - 1, - 1,\n\t8, 4, 5, 8, 5, 3, 9, 0, 5, 0, 3, 5, - 1, - 1, - 1, - 1,\n\t9, 4, 5, - 1, - 1, - 1, - 1, - 1, - 1, - 1, - 1, - 1, - 1, - 1, - 1, - 1,\n\t4, 11, 7, 4, 9, 11, 9, 10, 11, - 1, - 1, - 1, - 1, - 1, - 1, - 1,\n\t0, 8, 3, 4, 9, 7, 9, 11, 7, 9, 10, 11, - 1, - 1, - 1, - 1,\n\t1, 10, 11, 1, 11, 4, 1, 4, 0, 7, 4, 11, - 1, - 1, - 1, - 1,\n\t3, 1, 4, 3, 4, 8, 1, 10, 4, 7, 4, 11, 10, 11, 4, - 1,\n\t4, 11, 7, 9, 11, 4, 9, 2, 11, 9, 1, 2, - 1, - 1, - 1, - 1,\n\t9, 7, 4, 9, 11, 7, 9, 1, 11, 2, 11, 1, 0, 8, 3, - 1,\n\t11, 7, 4, 11, 4, 2, 2, 4, 0, - 1, - 1, - 1, - 1, - 1, - 1, - 1,\n\t11, 7, 4, 11, 4, 2, 8, 3, 4, 3, 2, 4, - 1, - 1, - 1, - 1,\n\t2, 9, 10, 2, 7, 9, 2, 3, 7, 7, 4, 9, - 1, - 1, - 1, - 1,\n\t9, 10, 7, 9, 7, 4, 10, 2, 7, 8, 7, 0, 2, 0, 7, - 1,\n\t3, 7, 10, 3, 10, 2, 7, 4, 10, 1, 10, 0, 4, 0, 10, - 1,\n\t1, 10, 2, 8, 7, 4, - 1, - 1, - 1, - 1, - 1, - 1, - 1, - 1, - 1, - 1,\n\t4, 9, 1, 4, 1, 7, 7, 1, 3, - 1, - 1, - 1, - 1, - 1, - 1, - 1,\n\t4, 9, 1, 4, 1, 7, 0, 8, 1, 8, 7, 1, - 1, - 1, - 1, - 1,\n\t4, 0, 3, 7, 4, 3, - 1, - 1, - 1, - 1, - 1, - 1, - 1, - 1, - 1, - 1,\n\t4, 8, 7, - 1, - 1, - 1, - 1, - 1, - 1, - 1, - 1, - 1, - 1, - 1, - 1, - 1,\n\t9, 10, 8, 10, 11, 8, - 1, - 1, - 1, - 1, - 1, - 1, - 1, - 1, - 1, - 1,\n\t3, 0, 9, 3, 9, 11, 11, 9, 10, - 1, - 1, - 1, - 1, - 1, - 1, - 1,\n\t0, 1, 10, 0, 10, 8, 8, 10, 11, - 1, - 1, - 1, - 1, - 1, - 1, - 1,\n\t3, 1, 10, 11, 3, 10, - 1, - 1, - 1, - 1, - 1, - 1, - 1, - 1, - 1, - 1,\n\t1, 2, 11, 1, 11, 9, 9, 11, 8, - 1, - 1, - 1, - 1, - 1, - 1, - 1,\n\t3, 0, 9, 3, 9, 11, 1, 2, 9, 2, 11, 9, - 1, - 1, - 1, - 1,\n\t0, 2, 11, 8, 0, 11, - 1, - 1, - 1, - 1, - 1, - 1, - 1, - 1, - 1, - 1,\n\t3, 2, 11, - 1, - 1, - 1, - 1, - 1, - 1, - 1, - 1, - 1, - 1, - 1, - 1, - 1,\n\t2, 3, 8, 2, 8, 10, 10, 8, 9, - 1, - 1, - 1, - 1, - 1, - 1, - 1,\n\t9, 10, 2, 0, 9, 2, - 1, - 1, - 1, - 1, - 1, - 1, - 1, - 1, - 1, - 1,\n\t2, 3, 8, 2, 8, 10, 0, 1, 8, 1, 10, 8, - 1, - 1, - 1, - 1,\n\t1, 10, 2, - 1, - 1, - 1, - 1, - 1, - 1, - 1, - 1, - 1, - 1, - 1, - 1, - 1,\n\t1, 3, 8, 9, 1, 8, - 1, - 1, - 1, - 1, - 1, - 1, - 1, - 1, - 1, - 1,\n\t0, 9, 1, - 1, - 1, - 1, - 1, - 1, - 1, - 1, - 1, - 1, - 1, - 1, - 1, - 1,\n\t0, 3, 8, - 1, - 1, - 1, - 1, - 1, - 1, - 1, - 1, - 1, - 1, - 1, - 1, - 1,\n\t- 1, - 1, - 1, - 1, - 1, - 1, - 1, - 1, - 1, - 1, - 1, - 1, - 1, - 1, - 1, - 1 ] );\n\nexport { MarchingCubes, edgeTable, triTable }\n"], "mappings": ";AAMA,MAAMA,aAAA,SAAsBC,IAAA,CAAK;EAC/BC,YAAYC,UAAA,EAAYC,QAAA,EAAUC,SAAA,GAAY,OAAOC,YAAA,GAAe,OAAOC,YAAA,GAAe,KAAO;IAC/F,MAAMC,QAAA,GAAW,IAAIC,cAAA,CAAgB;IAErC,MAAMD,QAAA,EAAUJ,QAAQ;IAExB,KAAKM,eAAA,GAAkB;IAEvB,MAAMC,KAAA,GAAQ;IAId,MAAMC,KAAA,GAAQ,IAAIC,YAAA,CAAa,KAAK,CAAC;IACrC,MAAMC,KAAA,GAAQ,IAAID,YAAA,CAAa,KAAK,CAAC;IACrC,MAAME,KAAA,GAAQ,IAAIF,YAAA,CAAa,KAAK,CAAC;IAErC,KAAKR,SAAA,GAAYA,SAAA;IACjB,KAAKC,YAAA,GAAeA,YAAA;IAMpB,KAAKU,IAAA,GAAO,UAAUC,WAAA,EAAY;MAChC,KAAKd,UAAA,GAAac,WAAA;MAIlB,KAAKC,SAAA,GAAY;MAIjB,KAAKC,IAAA,GAAOF,WAAA;MACZ,KAAKG,KAAA,GAAQ,KAAKD,IAAA,GAAO,KAAKA,IAAA;MAC9B,KAAKE,KAAA,GAAQ,KAAKD,KAAA,GAAQ,KAAKD,IAAA;MAC/B,KAAKG,QAAA,GAAW,KAAKH,IAAA,GAAO;MAI5B,KAAKI,KAAA,GAAQ,IAAM,KAAKJ,IAAA;MACxB,KAAKK,EAAA,GAAK,KAAKL,IAAA;MACf,KAAKM,EAAA,GAAK,KAAKL,KAAA;MAEf,KAAKM,KAAA,GAAQ,IAAIb,YAAA,CAAa,KAAKQ,KAAK;MACxC,KAAKM,YAAA,GAAe,IAAId,YAAA,CAAa,KAAKQ,KAAA,GAAQ,CAAC;MACnD,KAAKO,OAAA,GAAU,IAAIf,YAAA,CAAa,KAAKQ,KAAA,GAAQ,CAAC;MAI9C,KAAKQ,KAAA,GAAQ;MAEb,MAAMC,cAAA,GAAiBvB,YAAA,GAAe;MAEtC,KAAKwB,aAAA,GAAgB,IAAIlB,YAAA,CAAaiB,cAAA,GAAiB,CAAC;MACxD,MAAME,iBAAA,GAAoB,IAAIC,eAAA,CAAgB,KAAKF,aAAA,EAAe,CAAC;MACnEC,iBAAA,CAAkBE,QAAA,CAASC,gBAAgB;MAC3C3B,QAAA,CAAS4B,YAAA,CAAa,YAAYJ,iBAAiB;MAEnD,KAAKK,WAAA,GAAc,IAAIxB,YAAA,CAAaiB,cAAA,GAAiB,CAAC;MACtD,MAAMQ,eAAA,GAAkB,IAAIL,eAAA,CAAgB,KAAKI,WAAA,EAAa,CAAC;MAC/DC,eAAA,CAAgBJ,QAAA,CAASC,gBAAgB;MACzC3B,QAAA,CAAS4B,YAAA,CAAa,UAAUE,eAAe;MAE/C,IAAI,KAAKjC,SAAA,EAAW;QAClB,KAAKkC,OAAA,GAAU,IAAI1B,YAAA,CAAaiB,cAAA,GAAiB,CAAC;QAClD,MAAMU,WAAA,GAAc,IAAIP,eAAA,CAAgB,KAAKM,OAAA,EAAS,CAAC;QACvDC,WAAA,CAAYN,QAAA,CAASC,gBAAgB;QACrC3B,QAAA,CAAS4B,YAAA,CAAa,MAAMI,WAAW;MACxC;MAED,IAAI,KAAKlC,YAAA,EAAc;QACrB,KAAKmC,UAAA,GAAa,IAAI5B,YAAA,CAAaiB,cAAA,GAAiB,CAAC;QACrD,MAAMY,cAAA,GAAiB,IAAIT,eAAA,CAAgB,KAAKQ,UAAA,EAAY,CAAC;QAC7DC,cAAA,CAAeR,QAAA,CAASC,gBAAgB;QACxC3B,QAAA,CAAS4B,YAAA,CAAa,SAASM,cAAc;MAC9C;MAEDlC,QAAA,CAASmC,cAAA,GAAiB,IAAIC,MAAA,CAAO,IAAIC,OAAA,CAAO,GAAI,CAAC;IACtD;IAMD,SAASC,KAAKC,CAAA,EAAGC,CAAA,EAAGC,CAAA,EAAG;MACrB,OAAOF,CAAA,IAAKC,CAAA,GAAID,CAAA,IAAKE,CAAA;IACtB;IAED,SAASC,MAAMC,CAAA,EAAGC,MAAA,EAAQC,IAAA,EAAMC,CAAA,EAAGC,CAAA,EAAGC,CAAA,EAAGC,KAAA,EAAOC,KAAA,EAAOC,SAAA,EAAWC,SAAA,EAAW;MAC3E,MAAMC,EAAA,IAAMR,IAAA,GAAOI,KAAA,KAAUC,KAAA,GAAQD,KAAA;QACnCK,EAAA,GAAKnD,KAAA,CAAMgB,YAAA;MAEbf,KAAA,CAAMwC,MAAA,GAAS,CAAC,IAAIE,CAAA,GAAIO,EAAA,GAAKlD,KAAA,CAAMY,KAAA;MACnCX,KAAA,CAAMwC,MAAA,GAAS,CAAC,IAAIG,CAAA;MACpB3C,KAAA,CAAMwC,MAAA,GAAS,CAAC,IAAII,CAAA;MAEpB1C,KAAA,CAAMsC,MAAA,GAAS,CAAC,IAAIN,IAAA,CAAKgB,EAAA,CAAGX,CAAA,GAAI,CAAC,GAAGW,EAAA,CAAGX,CAAA,GAAI,CAAC,GAAGU,EAAE;MACjD/C,KAAA,CAAMsC,MAAA,GAAS,CAAC,IAAIN,IAAA,CAAKgB,EAAA,CAAGX,CAAA,GAAI,CAAC,GAAGW,EAAA,CAAGX,CAAA,GAAI,CAAC,GAAGU,EAAE;MACjD/C,KAAA,CAAMsC,MAAA,GAAS,CAAC,IAAIN,IAAA,CAAKgB,EAAA,CAAGX,CAAA,GAAI,CAAC,GAAGW,EAAA,CAAGX,CAAA,GAAI,CAAC,GAAGU,EAAE;MAEjD9C,KAAA,CAAMqC,MAAA,GAAS,CAAC,IAAIN,IAAA,CAAKnC,KAAA,CAAMiB,OAAA,CAAQ+B,SAAA,GAAY,IAAI,CAAC,GAAGhD,KAAA,CAAMiB,OAAA,CAAQgC,SAAA,GAAY,IAAI,CAAC,GAAGC,EAAE;MAC/F9C,KAAA,CAAMqC,MAAA,GAAS,CAAC,IAAIN,IAAA,CAAKnC,KAAA,CAAMiB,OAAA,CAAQ+B,SAAA,GAAY,IAAI,CAAC,GAAGhD,KAAA,CAAMiB,OAAA,CAAQgC,SAAA,GAAY,IAAI,CAAC,GAAGC,EAAE;MAC/F9C,KAAA,CAAMqC,MAAA,GAAS,CAAC,IAAIN,IAAA,CAAKnC,KAAA,CAAMiB,OAAA,CAAQ+B,SAAA,GAAY,IAAI,CAAC,GAAGhD,KAAA,CAAMiB,OAAA,CAAQgC,SAAA,GAAY,IAAI,CAAC,GAAGC,EAAE;IAChG;IAED,SAASE,MAAMZ,CAAA,EAAGC,MAAA,EAAQC,IAAA,EAAMC,CAAA,EAAGC,CAAA,EAAGC,CAAA,EAAGC,KAAA,EAAOC,KAAA,EAAOC,SAAA,EAAWC,SAAA,EAAW;MAC3E,MAAMC,EAAA,IAAMR,IAAA,GAAOI,KAAA,KAAUC,KAAA,GAAQD,KAAA;QACnCK,EAAA,GAAKnD,KAAA,CAAMgB,YAAA;MAEbf,KAAA,CAAMwC,MAAA,GAAS,CAAC,IAAIE,CAAA;MACpB1C,KAAA,CAAMwC,MAAA,GAAS,CAAC,IAAIG,CAAA,GAAIM,EAAA,GAAKlD,KAAA,CAAMY,KAAA;MACnCX,KAAA,CAAMwC,MAAA,GAAS,CAAC,IAAII,CAAA;MAEpB,MAAMQ,EAAA,GAAKb,CAAA,GAAIxC,KAAA,CAAMa,EAAA,GAAK;MAE1BV,KAAA,CAAMsC,MAAA,GAAS,CAAC,IAAIN,IAAA,CAAKgB,EAAA,CAAGX,CAAA,GAAI,CAAC,GAAGW,EAAA,CAAGE,EAAA,GAAK,CAAC,GAAGH,EAAE;MAClD/C,KAAA,CAAMsC,MAAA,GAAS,CAAC,IAAIN,IAAA,CAAKgB,EAAA,CAAGX,CAAA,GAAI,CAAC,GAAGW,EAAA,CAAGE,EAAA,GAAK,CAAC,GAAGH,EAAE;MAClD/C,KAAA,CAAMsC,MAAA,GAAS,CAAC,IAAIN,IAAA,CAAKgB,EAAA,CAAGX,CAAA,GAAI,CAAC,GAAGW,EAAA,CAAGE,EAAA,GAAK,CAAC,GAAGH,EAAE;MAElD9C,KAAA,CAAMqC,MAAA,GAAS,CAAC,IAAIN,IAAA,CAAKnC,KAAA,CAAMiB,OAAA,CAAQ+B,SAAA,GAAY,IAAI,CAAC,GAAGhD,KAAA,CAAMiB,OAAA,CAAQgC,SAAA,GAAY,IAAI,CAAC,GAAGC,EAAE;MAC/F9C,KAAA,CAAMqC,MAAA,GAAS,CAAC,IAAIN,IAAA,CAAKnC,KAAA,CAAMiB,OAAA,CAAQ+B,SAAA,GAAY,IAAI,CAAC,GAAGhD,KAAA,CAAMiB,OAAA,CAAQgC,SAAA,GAAY,IAAI,CAAC,GAAGC,EAAE;MAC/F9C,KAAA,CAAMqC,MAAA,GAAS,CAAC,IAAIN,IAAA,CAAKnC,KAAA,CAAMiB,OAAA,CAAQ+B,SAAA,GAAY,IAAI,CAAC,GAAGhD,KAAA,CAAMiB,OAAA,CAAQgC,SAAA,GAAY,IAAI,CAAC,GAAGC,EAAE;IAChG;IAED,SAASI,MAAMd,CAAA,EAAGC,MAAA,EAAQC,IAAA,EAAMC,CAAA,EAAGC,CAAA,EAAGC,CAAA,EAAGC,KAAA,EAAOC,KAAA,EAAOC,SAAA,EAAWC,SAAA,EAAW;MAC3E,MAAMC,EAAA,IAAMR,IAAA,GAAOI,KAAA,KAAUC,KAAA,GAAQD,KAAA;QACnCK,EAAA,GAAKnD,KAAA,CAAMgB,YAAA;MAEbf,KAAA,CAAMwC,MAAA,GAAS,CAAC,IAAIE,CAAA;MACpB1C,KAAA,CAAMwC,MAAA,GAAS,CAAC,IAAIG,CAAA;MACpB3C,KAAA,CAAMwC,MAAA,GAAS,CAAC,IAAII,CAAA,GAAIK,EAAA,GAAKlD,KAAA,CAAMY,KAAA;MAEnC,MAAMyC,EAAA,GAAKb,CAAA,GAAIxC,KAAA,CAAMc,EAAA,GAAK;MAE1BX,KAAA,CAAMsC,MAAA,GAAS,CAAC,IAAIN,IAAA,CAAKgB,EAAA,CAAGX,CAAA,GAAI,CAAC,GAAGW,EAAA,CAAGE,EAAA,GAAK,CAAC,GAAGH,EAAE;MAClD/C,KAAA,CAAMsC,MAAA,GAAS,CAAC,IAAIN,IAAA,CAAKgB,EAAA,CAAGX,CAAA,GAAI,CAAC,GAAGW,EAAA,CAAGE,EAAA,GAAK,CAAC,GAAGH,EAAE;MAClD/C,KAAA,CAAMsC,MAAA,GAAS,CAAC,IAAIN,IAAA,CAAKgB,EAAA,CAAGX,CAAA,GAAI,CAAC,GAAGW,EAAA,CAAGE,EAAA,GAAK,CAAC,GAAGH,EAAE;MAElD9C,KAAA,CAAMqC,MAAA,GAAS,CAAC,IAAIN,IAAA,CAAKnC,KAAA,CAAMiB,OAAA,CAAQ+B,SAAA,GAAY,IAAI,CAAC,GAAGhD,KAAA,CAAMiB,OAAA,CAAQgC,SAAA,GAAY,IAAI,CAAC,GAAGC,EAAE;MAC/F9C,KAAA,CAAMqC,MAAA,GAAS,CAAC,IAAIN,IAAA,CAAKnC,KAAA,CAAMiB,OAAA,CAAQ+B,SAAA,GAAY,IAAI,CAAC,GAAGhD,KAAA,CAAMiB,OAAA,CAAQgC,SAAA,GAAY,IAAI,CAAC,GAAGC,EAAE;MAC/F9C,KAAA,CAAMqC,MAAA,GAAS,CAAC,IAAIN,IAAA,CAAKnC,KAAA,CAAMiB,OAAA,CAAQ+B,SAAA,GAAY,IAAI,CAAC,GAAGhD,KAAA,CAAMiB,OAAA,CAAQgC,SAAA,GAAY,IAAI,CAAC,GAAGC,EAAE;IAChG;IAED,SAASK,SAASf,CAAA,EAAG;MACnB,MAAMgB,EAAA,GAAKhB,CAAA,GAAI;MAEf,IAAIxC,KAAA,CAAMgB,YAAA,CAAawC,EAAE,MAAM,GAAK;QAClCxD,KAAA,CAAMgB,YAAA,CAAawC,EAAA,GAAK,CAAC,IAAIxD,KAAA,CAAMe,KAAA,CAAMyB,CAAA,GAAI,CAAC,IAAIxC,KAAA,CAAMe,KAAA,CAAMyB,CAAA,GAAI,CAAC;QACnExC,KAAA,CAAMgB,YAAA,CAAawC,EAAA,GAAK,CAAC,IAAIxD,KAAA,CAAMe,KAAA,CAAMyB,CAAA,GAAIxC,KAAA,CAAMa,EAAE,IAAIb,KAAA,CAAMe,KAAA,CAAMyB,CAAA,GAAIxC,KAAA,CAAMa,EAAE;QACjFb,KAAA,CAAMgB,YAAA,CAAawC,EAAA,GAAK,CAAC,IAAIxD,KAAA,CAAMe,KAAA,CAAMyB,CAAA,GAAIxC,KAAA,CAAMc,EAAE,IAAId,KAAA,CAAMe,KAAA,CAAMyB,CAAA,GAAIxC,KAAA,CAAMc,EAAE;MAClF;IACF;IAKD,SAAS2C,WAAWC,EAAA,EAAIC,EAAA,EAAIC,EAAA,EAAIpB,CAAA,EAAGE,IAAA,EAAM;MAEvC,MAAMmB,EAAA,GAAKrB,CAAA,GAAI;QACbsB,EAAA,GAAKtB,CAAA,GAAIxC,KAAA,CAAMa,EAAA;QACfkD,EAAA,GAAKvB,CAAA,GAAIxC,KAAA,CAAMc,EAAA;QACfkD,GAAA,GAAMH,EAAA,GAAK7D,KAAA,CAAMa,EAAA;QACjBoD,GAAA,GAAMJ,EAAA,GAAK7D,KAAA,CAAMc,EAAA;QACjBoD,GAAA,GAAM1B,CAAA,GAAIxC,KAAA,CAAMa,EAAA,GAAKb,KAAA,CAAMc,EAAA;QAC3BqD,IAAA,GAAON,EAAA,GAAK7D,KAAA,CAAMa,EAAA,GAAKb,KAAA,CAAMc,EAAA;MAE/B,IAAIsD,SAAA,GAAY;MAChB,MAAMC,MAAA,GAASrE,KAAA,CAAMe,KAAA,CAAMyB,CAAC;QAC1B8B,MAAA,GAAStE,KAAA,CAAMe,KAAA,CAAM8C,EAAE;QACvBU,MAAA,GAASvE,KAAA,CAAMe,KAAA,CAAM+C,EAAE;QACvBU,MAAA,GAASxE,KAAA,CAAMe,KAAA,CAAMiD,GAAG;QACxBS,MAAA,GAASzE,KAAA,CAAMe,KAAA,CAAMgD,EAAE;QACvBW,MAAA,GAAS1E,KAAA,CAAMe,KAAA,CAAMkD,GAAG;QACxBU,MAAA,GAAS3E,KAAA,CAAMe,KAAA,CAAMmD,GAAG;QACxBU,MAAA,GAAS5E,KAAA,CAAMe,KAAA,CAAMoD,IAAI;MAE3B,IAAIE,MAAA,GAAS3B,IAAA,EAAM0B,SAAA,IAAa;MAChC,IAAIE,MAAA,GAAS5B,IAAA,EAAM0B,SAAA,IAAa;MAChC,IAAIG,MAAA,GAAS7B,IAAA,EAAM0B,SAAA,IAAa;MAChC,IAAII,MAAA,GAAS9B,IAAA,EAAM0B,SAAA,IAAa;MAChC,IAAIK,MAAA,GAAS/B,IAAA,EAAM0B,SAAA,IAAa;MAChC,IAAIM,MAAA,GAAShC,IAAA,EAAM0B,SAAA,IAAa;MAChC,IAAIO,MAAA,GAASjC,IAAA,EAAM0B,SAAA,IAAa;MAChC,IAAIQ,MAAA,GAASlC,IAAA,EAAM0B,SAAA,IAAa;MAIhC,MAAMS,IAAA,GAAOC,SAAA,CAAUV,SAAS;MAChC,IAAIS,IAAA,KAAS,GAAG,OAAO;MAEvB,MAAME,CAAA,GAAI/E,KAAA,CAAMY,KAAA;QACdoE,GAAA,GAAMtB,EAAA,GAAKqB,CAAA;QACXE,GAAA,GAAMtB,EAAA,GAAKoB,CAAA;QACXG,GAAA,GAAMtB,EAAA,GAAKmB,CAAA;MAIb,IAAIF,IAAA,GAAO,GAAG;QACZtB,QAAA,CAASf,CAAC;QACVe,QAAA,CAASM,EAAE;QACXtB,KAAA,CAAMC,CAAA,GAAI,GAAG,GAAGE,IAAA,EAAMgB,EAAA,EAAIC,EAAA,EAAIC,EAAA,EAAIS,MAAA,EAAQC,MAAA,EAAQ9B,CAAA,EAAGqB,EAAE;MACxD;MAED,IAAIgB,IAAA,GAAO,GAAG;QACZtB,QAAA,CAASM,EAAE;QACXN,QAAA,CAASS,GAAG;QACZZ,KAAA,CAAMS,EAAA,GAAK,GAAG,GAAGnB,IAAA,EAAMsC,GAAA,EAAKrB,EAAA,EAAIC,EAAA,EAAIU,MAAA,EAAQE,MAAA,EAAQX,EAAA,EAAIG,GAAG;MAC5D;MAED,IAAIa,IAAA,GAAO,GAAG;QACZtB,QAAA,CAASO,EAAE;QACXP,QAAA,CAASS,GAAG;QACZzB,KAAA,CAAMuB,EAAA,GAAK,GAAG,GAAGpB,IAAA,EAAMgB,EAAA,EAAIuB,GAAA,EAAKrB,EAAA,EAAIW,MAAA,EAAQC,MAAA,EAAQV,EAAA,EAAIE,GAAG;MAC5D;MAED,IAAIa,IAAA,GAAO,GAAG;QACZtB,QAAA,CAASf,CAAC;QACVe,QAAA,CAASO,EAAE;QACXV,KAAA,CAAMZ,CAAA,GAAI,GAAG,GAAGE,IAAA,EAAMgB,EAAA,EAAIC,EAAA,EAAIC,EAAA,EAAIS,MAAA,EAAQE,MAAA,EAAQ/B,CAAA,EAAGsB,EAAE;MACxD;MAID,IAAIe,IAAA,GAAO,IAAI;QACbtB,QAAA,CAASQ,EAAE;QACXR,QAAA,CAASU,GAAG;QACZ1B,KAAA,CAAMwB,EAAA,GAAK,GAAG,IAAIrB,IAAA,EAAMgB,EAAA,EAAIC,EAAA,EAAIuB,GAAA,EAAKT,MAAA,EAAQC,MAAA,EAAQX,EAAA,EAAIE,GAAG;MAC7D;MAED,IAAIY,IAAA,GAAO,IAAI;QACbtB,QAAA,CAASU,GAAG;QACZV,QAAA,CAASY,IAAI;QACbf,KAAA,CAAMa,GAAA,GAAM,GAAG,IAAIvB,IAAA,EAAMsC,GAAA,EAAKrB,EAAA,EAAIuB,GAAA,EAAKR,MAAA,EAAQE,MAAA,EAAQX,GAAA,EAAKE,IAAI;MACjE;MAED,IAAIU,IAAA,GAAO,IAAI;QACbtB,QAAA,CAASW,GAAG;QACZX,QAAA,CAASY,IAAI;QACb5B,KAAA,CAAM2B,GAAA,GAAM,GAAG,IAAIxB,IAAA,EAAMgB,EAAA,EAAIuB,GAAA,EAAKC,GAAA,EAAKP,MAAA,EAAQC,MAAA,EAAQV,GAAA,EAAKC,IAAI;MACjE;MAED,IAAIU,IAAA,GAAO,KAAK;QACdtB,QAAA,CAASQ,EAAE;QACXR,QAAA,CAASW,GAAG;QACZd,KAAA,CAAMW,EAAA,GAAK,GAAG,IAAIrB,IAAA,EAAMgB,EAAA,EAAIC,EAAA,EAAIuB,GAAA,EAAKT,MAAA,EAAQE,MAAA,EAAQZ,EAAA,EAAIG,GAAG;MAC7D;MAGD,IAAIW,IAAA,GAAO,KAAK;QACdtB,QAAA,CAASf,CAAC;QACVe,QAAA,CAASQ,EAAE;QACXT,KAAA,CAAMd,CAAA,GAAI,GAAG,IAAIE,IAAA,EAAMgB,EAAA,EAAIC,EAAA,EAAIC,EAAA,EAAIS,MAAA,EAAQI,MAAA,EAAQjC,CAAA,EAAGuB,EAAE;MACzD;MAED,IAAIc,IAAA,GAAO,KAAK;QACdtB,QAAA,CAASM,EAAE;QACXN,QAAA,CAASU,GAAG;QACZX,KAAA,CAAMO,EAAA,GAAK,GAAG,IAAInB,IAAA,EAAMsC,GAAA,EAAKrB,EAAA,EAAIC,EAAA,EAAIU,MAAA,EAAQI,MAAA,EAAQb,EAAA,EAAII,GAAG;MAC7D;MAED,IAAIY,IAAA,GAAO,MAAM;QACftB,QAAA,CAASS,GAAG;QACZT,QAAA,CAASY,IAAI;QACbb,KAAA,CAAMU,GAAA,GAAM,GAAG,IAAItB,IAAA,EAAMsC,GAAA,EAAKC,GAAA,EAAKrB,EAAA,EAAIY,MAAA,EAAQI,MAAA,EAAQZ,GAAA,EAAKG,IAAI;MACjE;MAED,IAAIU,IAAA,GAAO,MAAM;QACftB,QAAA,CAASO,EAAE;QACXP,QAAA,CAASW,GAAG;QACZZ,KAAA,CAAMQ,EAAA,GAAK,GAAG,IAAIpB,IAAA,EAAMgB,EAAA,EAAIuB,GAAA,EAAKrB,EAAA,EAAIW,MAAA,EAAQI,MAAA,EAAQb,EAAA,EAAII,GAAG;MAC7D;MAEDE,SAAA,KAAc;MAEd,IAAIe,EAAA;QACFC,EAAA;QACAC,EAAA;QACAC,OAAA,GAAU;QACVC,CAAA,GAAI;MAIN,OAAOC,QAAA,CAASpB,SAAA,GAAYmB,CAAC,KAAK,IAAI;QACpCJ,EAAA,GAAKf,SAAA,GAAYmB,CAAA;QACjBH,EAAA,GAAKD,EAAA,GAAK;QACVE,EAAA,GAAKF,EAAA,GAAK;QAEVM,WAAA,CAAYxF,KAAA,EAAOE,KAAA,EAAOC,KAAA,EAAO,IAAIoF,QAAA,CAASL,EAAE,GAAG,IAAIK,QAAA,CAASJ,EAAE,GAAG,IAAII,QAAA,CAASH,EAAE,CAAC;QAErFE,CAAA,IAAK;QACLD,OAAA;MACD;MAED,OAAOA,OAAA;IACR;IAED,SAASG,YAAYC,GAAA,EAAKC,IAAA,EAAMC,MAAA,EAAQT,EAAA,EAAIC,EAAA,EAAIC,EAAA,EAAI;MAClD,MAAMQ,CAAA,GAAI7F,KAAA,CAAMkB,KAAA,GAAQ;MAIxBlB,KAAA,CAAMoB,aAAA,CAAcyE,CAAA,GAAI,CAAC,IAAIH,GAAA,CAAIP,EAAE;MACnCnF,KAAA,CAAMoB,aAAA,CAAcyE,CAAA,GAAI,CAAC,IAAIH,GAAA,CAAIP,EAAA,GAAK,CAAC;MACvCnF,KAAA,CAAMoB,aAAA,CAAcyE,CAAA,GAAI,CAAC,IAAIH,GAAA,CAAIP,EAAA,GAAK,CAAC;MAEvCnF,KAAA,CAAMoB,aAAA,CAAcyE,CAAA,GAAI,CAAC,IAAIH,GAAA,CAAIN,EAAE;MACnCpF,KAAA,CAAMoB,aAAA,CAAcyE,CAAA,GAAI,CAAC,IAAIH,GAAA,CAAIN,EAAA,GAAK,CAAC;MACvCpF,KAAA,CAAMoB,aAAA,CAAcyE,CAAA,GAAI,CAAC,IAAIH,GAAA,CAAIN,EAAA,GAAK,CAAC;MAEvCpF,KAAA,CAAMoB,aAAA,CAAcyE,CAAA,GAAI,CAAC,IAAIH,GAAA,CAAIL,EAAE;MACnCrF,KAAA,CAAMoB,aAAA,CAAcyE,CAAA,GAAI,CAAC,IAAIH,GAAA,CAAIL,EAAA,GAAK,CAAC;MACvCrF,KAAA,CAAMoB,aAAA,CAAcyE,CAAA,GAAI,CAAC,IAAIH,GAAA,CAAIL,EAAA,GAAK,CAAC;MAIvC,IAAIrF,KAAA,CAAMP,QAAA,CAASqG,WAAA,KAAgB,MAAM;QACvC,MAAMC,EAAA,IAAMJ,IAAA,CAAKR,EAAA,GAAK,CAAC,IAAIQ,IAAA,CAAKP,EAAA,GAAK,CAAC,IAAIO,IAAA,CAAKN,EAAA,GAAK,CAAC,KAAK;QAC1D,MAAMW,EAAA,IAAML,IAAA,CAAKR,EAAA,GAAK,CAAC,IAAIQ,IAAA,CAAKP,EAAA,GAAK,CAAC,IAAIO,IAAA,CAAKN,EAAA,GAAK,CAAC,KAAK;QAC1D,MAAMY,EAAA,IAAMN,IAAA,CAAKR,EAAA,GAAK,CAAC,IAAIQ,IAAA,CAAKP,EAAA,GAAK,CAAC,IAAIO,IAAA,CAAKN,EAAA,GAAK,CAAC,KAAK;QAE1DrF,KAAA,CAAM0B,WAAA,CAAYmE,CAAA,GAAI,CAAC,IAAIE,EAAA;QAC3B/F,KAAA,CAAM0B,WAAA,CAAYmE,CAAA,GAAI,CAAC,IAAIG,EAAA;QAC3BhG,KAAA,CAAM0B,WAAA,CAAYmE,CAAA,GAAI,CAAC,IAAII,EAAA;QAE3BjG,KAAA,CAAM0B,WAAA,CAAYmE,CAAA,GAAI,CAAC,IAAIE,EAAA;QAC3B/F,KAAA,CAAM0B,WAAA,CAAYmE,CAAA,GAAI,CAAC,IAAIG,EAAA;QAC3BhG,KAAA,CAAM0B,WAAA,CAAYmE,CAAA,GAAI,CAAC,IAAII,EAAA;QAE3BjG,KAAA,CAAM0B,WAAA,CAAYmE,CAAA,GAAI,CAAC,IAAIE,EAAA;QAC3B/F,KAAA,CAAM0B,WAAA,CAAYmE,CAAA,GAAI,CAAC,IAAIG,EAAA;QAC3BhG,KAAA,CAAM0B,WAAA,CAAYmE,CAAA,GAAI,CAAC,IAAII,EAAA;MACnC,OAAa;QACLjG,KAAA,CAAM0B,WAAA,CAAYmE,CAAA,GAAI,CAAC,IAAIF,IAAA,CAAKR,EAAA,GAAK,CAAC;QACtCnF,KAAA,CAAM0B,WAAA,CAAYmE,CAAA,GAAI,CAAC,IAAIF,IAAA,CAAKR,EAAA,GAAK,CAAC;QACtCnF,KAAA,CAAM0B,WAAA,CAAYmE,CAAA,GAAI,CAAC,IAAIF,IAAA,CAAKR,EAAA,GAAK,CAAC;QAEtCnF,KAAA,CAAM0B,WAAA,CAAYmE,CAAA,GAAI,CAAC,IAAIF,IAAA,CAAKP,EAAA,GAAK,CAAC;QACtCpF,KAAA,CAAM0B,WAAA,CAAYmE,CAAA,GAAI,CAAC,IAAIF,IAAA,CAAKP,EAAA,GAAK,CAAC;QACtCpF,KAAA,CAAM0B,WAAA,CAAYmE,CAAA,GAAI,CAAC,IAAIF,IAAA,CAAKP,EAAA,GAAK,CAAC;QAEtCpF,KAAA,CAAM0B,WAAA,CAAYmE,CAAA,GAAI,CAAC,IAAIF,IAAA,CAAKN,EAAA,GAAK,CAAC;QACtCrF,KAAA,CAAM0B,WAAA,CAAYmE,CAAA,GAAI,CAAC,IAAIF,IAAA,CAAKN,EAAA,GAAK,CAAC;QACtCrF,KAAA,CAAM0B,WAAA,CAAYmE,CAAA,GAAI,CAAC,IAAIF,IAAA,CAAKN,EAAA,GAAK,CAAC;MACvC;MAID,IAAIrF,KAAA,CAAMN,SAAA,EAAW;QACnB,MAAMqF,CAAA,GAAI/E,KAAA,CAAMkB,KAAA,GAAQ;QAExBlB,KAAA,CAAM4B,OAAA,CAAQmD,CAAA,GAAI,CAAC,IAAIW,GAAA,CAAIP,EAAA,GAAK,CAAC;QACjCnF,KAAA,CAAM4B,OAAA,CAAQmD,CAAA,GAAI,CAAC,IAAIW,GAAA,CAAIP,EAAA,GAAK,CAAC;QAEjCnF,KAAA,CAAM4B,OAAA,CAAQmD,CAAA,GAAI,CAAC,IAAIW,GAAA,CAAIN,EAAA,GAAK,CAAC;QACjCpF,KAAA,CAAM4B,OAAA,CAAQmD,CAAA,GAAI,CAAC,IAAIW,GAAA,CAAIN,EAAA,GAAK,CAAC;QAEjCpF,KAAA,CAAM4B,OAAA,CAAQmD,CAAA,GAAI,CAAC,IAAIW,GAAA,CAAIL,EAAA,GAAK,CAAC;QACjCrF,KAAA,CAAM4B,OAAA,CAAQmD,CAAA,GAAI,CAAC,IAAIW,GAAA,CAAIL,EAAA,GAAK,CAAC;MAClC;MAID,IAAIrF,KAAA,CAAML,YAAA,EAAc;QACtBK,KAAA,CAAM8B,UAAA,CAAW+D,CAAA,GAAI,CAAC,IAAID,MAAA,CAAOT,EAAA,GAAK,CAAC;QACvCnF,KAAA,CAAM8B,UAAA,CAAW+D,CAAA,GAAI,CAAC,IAAID,MAAA,CAAOT,EAAA,GAAK,CAAC;QACvCnF,KAAA,CAAM8B,UAAA,CAAW+D,CAAA,GAAI,CAAC,IAAID,MAAA,CAAOT,EAAA,GAAK,CAAC;QAEvCnF,KAAA,CAAM8B,UAAA,CAAW+D,CAAA,GAAI,CAAC,IAAID,MAAA,CAAOR,EAAA,GAAK,CAAC;QACvCpF,KAAA,CAAM8B,UAAA,CAAW+D,CAAA,GAAI,CAAC,IAAID,MAAA,CAAOR,EAAA,GAAK,CAAC;QACvCpF,KAAA,CAAM8B,UAAA,CAAW+D,CAAA,GAAI,CAAC,IAAID,MAAA,CAAOR,EAAA,GAAK,CAAC;QAEvCpF,KAAA,CAAM8B,UAAA,CAAW+D,CAAA,GAAI,CAAC,IAAID,MAAA,CAAOP,EAAA,GAAK,CAAC;QACvCrF,KAAA,CAAM8B,UAAA,CAAW+D,CAAA,GAAI,CAAC,IAAID,MAAA,CAAOP,EAAA,GAAK,CAAC;QACvCrF,KAAA,CAAM8B,UAAA,CAAW+D,CAAA,GAAI,CAAC,IAAID,MAAA,CAAOP,EAAA,GAAK,CAAC;MACxC;MAEDrF,KAAA,CAAMkB,KAAA,IAAS;IAChB;IASD,KAAKgF,OAAA,GAAU,UAAUC,KAAA,EAAOC,KAAA,EAAOC,KAAA,EAAOC,QAAA,EAAUC,QAAA,EAAUX,MAAA,EAAQ;MACxE,MAAMY,IAAA,GAAOC,IAAA,CAAKD,IAAA,CAAKF,QAAQ;MAC/BA,QAAA,GAAWG,IAAA,CAAKC,GAAA,CAAIJ,QAAQ;MAC5B,MAAMK,eAAA,GAAkB,EAAEf,MAAA,KAAW,UAAaA,MAAA,KAAW;MAC7D,IAAIgB,SAAA,GAAY,IAAIC,KAAA,CAAMV,KAAA,EAAOC,KAAA,EAAOC,KAAK;MAE7C,IAAIM,eAAA,EAAiB;QACnB,IAAI;UACFC,SAAA,GACEhB,MAAA,YAAkBiB,KAAA,GACdjB,MAAA,GACAkB,KAAA,CAAMC,OAAA,CAAQnB,MAAM,IACpB,IAAIiB,KAAA,CACFJ,IAAA,CAAKO,GAAA,CAAIP,IAAA,CAAKC,GAAA,CAAId,MAAA,CAAO,CAAC,CAAC,GAAG,CAAC,GAC/Ba,IAAA,CAAKO,GAAA,CAAIP,IAAA,CAAKC,GAAA,CAAId,MAAA,CAAO,CAAC,CAAC,GAAG,CAAC,GAC/Ba,IAAA,CAAKO,GAAA,CAAIP,IAAA,CAAKC,GAAA,CAAId,MAAA,CAAO,CAAC,CAAC,GAAG,CAAC,CAChC,IACD,IAAIiB,KAAA,CAAMjB,MAAM;QACvB,SAAQqB,GAAA,EAAP;UACAL,SAAA,GAAY,IAAIC,KAAA,CAAMV,KAAA,EAAOC,KAAA,EAAOC,KAAK;QAC1C;MACF;MASD,MAAMa,MAAA,GAAS,KAAK1G,IAAA,GAAOiG,IAAA,CAAKU,IAAA,CAAKb,QAAA,GAAWC,QAAQ;QACtDa,EAAA,GAAKf,KAAA,GAAQ,KAAK7F,IAAA;QAClB6G,EAAA,GAAKjB,KAAA,GAAQ,KAAK5F,IAAA;QAClB8G,EAAA,GAAKnB,KAAA,GAAQ,KAAK3F,IAAA;MAEpB,IAAI+G,KAAA,GAAQd,IAAA,CAAKe,KAAA,CAAMJ,EAAA,GAAKF,MAAM;MAClC,IAAIK,KAAA,GAAQ,GAAGA,KAAA,GAAQ;MACvB,IAAIE,KAAA,GAAQhB,IAAA,CAAKe,KAAA,CAAMJ,EAAA,GAAKF,MAAM;MAClC,IAAIO,KAAA,GAAQ,KAAKjH,IAAA,GAAO,GAAGiH,KAAA,GAAQ,KAAKjH,IAAA,GAAO;MAC/C,IAAIkH,KAAA,GAAQjB,IAAA,CAAKe,KAAA,CAAMH,EAAA,GAAKH,MAAM;MAClC,IAAIQ,KAAA,GAAQ,GAAGA,KAAA,GAAQ;MACvB,IAAIC,KAAA,GAAQlB,IAAA,CAAKe,KAAA,CAAMH,EAAA,GAAKH,MAAM;MAClC,IAAIS,KAAA,GAAQ,KAAKnH,IAAA,GAAO,GAAGmH,KAAA,GAAQ,KAAKnH,IAAA,GAAO;MAC/C,IAAIoH,KAAA,GAAQnB,IAAA,CAAKe,KAAA,CAAMF,EAAA,GAAKJ,MAAM;MAClC,IAAIU,KAAA,GAAQ,GAAGA,KAAA,GAAQ;MACvB,IAAIC,KAAA,GAAQpB,IAAA,CAAKe,KAAA,CAAMF,EAAA,GAAKJ,MAAM;MAClC,IAAIW,KAAA,GAAQ,KAAKrH,IAAA,GAAO,GAAGqH,KAAA,GAAQ,KAAKrH,IAAA,GAAO;MAK/C,IAAImC,CAAA,EAAGC,CAAA,EAAGC,CAAA,EAAGiF,QAAA,EAAUC,QAAA,EAAUrE,EAAA,EAAIC,EAAA,EAAIC,EAAA,EAAIsB,GAAA,EAAKD,GAAA,EAAK+C,GAAA;MAEvD,KAAKnF,CAAA,GAAI0E,KAAA,EAAO1E,CAAA,GAAI4E,KAAA,EAAO5E,CAAA,IAAK;QAC9BkF,QAAA,GAAW,KAAKtH,KAAA,GAAQoC,CAAA;QACxBe,EAAA,GAAKf,CAAA,GAAI,KAAKrC,IAAA,GAAO6F,KAAA;QACrBnB,GAAA,GAAMtB,EAAA,GAAKA,EAAA;QAEX,KAAKhB,CAAA,GAAI8E,KAAA,EAAO9E,CAAA,GAAI+E,KAAA,EAAO/E,CAAA,IAAK;UAC9BkF,QAAA,GAAWC,QAAA,GAAW,KAAKvH,IAAA,GAAOoC,CAAA;UAClCe,EAAA,GAAKf,CAAA,GAAI,KAAKpC,IAAA,GAAO4F,KAAA;UACrBnB,GAAA,GAAMtB,EAAA,GAAKA,EAAA;UAEX,KAAKhB,CAAA,GAAIiF,KAAA,EAAOjF,CAAA,GAAIkF,KAAA,EAAOlF,CAAA,IAAK;YAC9Be,EAAA,GAAKf,CAAA,GAAI,KAAKnC,IAAA,GAAO2F,KAAA;YACrB6B,GAAA,GAAM1B,QAAA,IAAY,OAAW5C,EAAA,GAAKA,EAAA,GAAKuB,GAAA,GAAMC,GAAA,IAAOqB,QAAA;YACpD,IAAIyB,GAAA,GAAM,GAAK;cACb,KAAKjH,KAAA,CAAM+G,QAAA,GAAWnF,CAAC,KAAKqF,GAAA,GAAMxB,IAAA;cAIlC,MAAMyB,KAAA,GAAQxB,IAAA,CAAKU,IAAA,EAAMxE,CAAA,GAAI2E,EAAA,KAAO3E,CAAA,GAAI2E,EAAA,KAAO1E,CAAA,GAAIyE,EAAA,KAAOzE,CAAA,GAAIyE,EAAA,KAAOxE,CAAA,GAAIuE,EAAA,KAAOvE,CAAA,GAAIuE,EAAA,CAAG,IAAIF,MAAA;cAC3F,MAAMgB,OAAA,GAAU,IAAID,KAAA,GAAQA,KAAA,GAAQA,KAAA,IAASA,KAAA,IAASA,KAAA,GAAQ,IAAI,MAAM;cACxE,KAAKhH,OAAA,EAAS6G,QAAA,GAAWnF,CAAA,IAAK,IAAI,CAAC,KAAKiE,SAAA,CAAUuB,CAAA,GAAID,OAAA;cACtD,KAAKjH,OAAA,EAAS6G,QAAA,GAAWnF,CAAA,IAAK,IAAI,CAAC,KAAKiE,SAAA,CAAUwB,CAAA,GAAIF,OAAA;cACtD,KAAKjH,OAAA,EAAS6G,QAAA,GAAWnF,CAAA,IAAK,IAAI,CAAC,KAAKiE,SAAA,CAAUvE,CAAA,GAAI6F,OAAA;YACvD;UACF;QACF;MACF;IACF;IAED,KAAKG,SAAA,GAAY,UAAU/B,QAAA,EAAUC,QAAA,EAAU;MAE7C,MAAM/F,IAAA,GAAO,KAAKA,IAAA;QAChBK,EAAA,GAAK,KAAKA,EAAA;QACVC,EAAA,GAAK,KAAKA,EAAA;QACVC,KAAA,GAAQ,KAAKA,KAAA;MAEf,IAAI4B,CAAA;QACFC,CAAA;QACAC,CAAA;QACAyF,EAAA;QACAN,GAAA;QACAO,IAAA;QACAC,GAAA;QACAC,IAAA,GAAOjI,IAAA,GAAOiG,IAAA,CAAKU,IAAA,CAAKb,QAAA,GAAWC,QAAQ;MAE7C,IAAIkC,IAAA,GAAOjI,IAAA,EAAMiI,IAAA,GAAOjI,IAAA;MAExB,KAAKmC,CAAA,GAAI,GAAGA,CAAA,GAAI8F,IAAA,EAAM9F,CAAA,IAAK;QACzB4F,IAAA,GAAO5F,CAAA,GAAInC,IAAA;QACX8H,EAAA,GAAKC,IAAA,GAAOA,IAAA;QACZP,GAAA,GAAM1B,QAAA,IAAY,OAASgC,EAAA,IAAM/B,QAAA;QAEjC,IAAIyB,GAAA,GAAM,GAAK;UACb,KAAKpF,CAAA,GAAI,GAAGA,CAAA,GAAIpC,IAAA,EAAMoC,CAAA,IAAK;YACzB4F,GAAA,GAAM7F,CAAA,GAAIC,CAAA,GAAI/B,EAAA;YAEd,KAAKgC,CAAA,GAAI,GAAGA,CAAA,GAAIrC,IAAA,EAAMqC,CAAA,IAAK;cACzB9B,KAAA,CAAMD,EAAA,GAAK+B,CAAA,GAAI2F,GAAG,KAAKR,GAAA;YACxB;UACF;QACF;MACF;IACF;IAED,KAAKU,SAAA,GAAY,UAAUpC,QAAA,EAAUC,QAAA,EAAU;MAE7C,MAAM/F,IAAA,GAAO,KAAKA,IAAA;QAChBK,EAAA,GAAK,KAAKA,EAAA;QACVC,EAAA,GAAK,KAAKA,EAAA;QACVC,KAAA,GAAQ,KAAKA,KAAA;MAEf,IAAI4B,CAAA;QACFC,CAAA;QACAC,CAAA;QACA8F,EAAA;QACAX,GAAA;QACAY,IAAA;QACAC,EAAA;QACAL,GAAA;QACAC,IAAA,GAAOjI,IAAA,GAAOiG,IAAA,CAAKU,IAAA,CAAKb,QAAA,GAAWC,QAAQ;MAE7C,IAAIkC,IAAA,GAAOjI,IAAA,EAAMiI,IAAA,GAAOjI,IAAA;MAExB,KAAKoC,CAAA,GAAI,GAAGA,CAAA,GAAI6F,IAAA,EAAM7F,CAAA,IAAK;QACzBgG,IAAA,GAAOhG,CAAA,GAAIpC,IAAA;QACXmI,EAAA,GAAKC,IAAA,GAAOA,IAAA;QACZZ,GAAA,GAAM1B,QAAA,IAAY,OAASqC,EAAA,IAAMpC,QAAA;QAEjC,IAAIyB,GAAA,GAAM,GAAK;UACba,EAAA,GAAKjG,CAAA,GAAI/B,EAAA;UAET,KAAK8B,CAAA,GAAI,GAAGA,CAAA,GAAInC,IAAA,EAAMmC,CAAA,IAAK;YACzB6F,GAAA,GAAMK,EAAA,GAAKlG,CAAA;YAEX,KAAKE,CAAA,GAAI,GAAGA,CAAA,GAAIrC,IAAA,EAAMqC,CAAA,IAAK9B,KAAA,CAAMD,EAAA,GAAK+B,CAAA,GAAI2F,GAAG,KAAKR,GAAA;UACnD;QACF;MACF;IACF;IAED,KAAKc,SAAA,GAAY,UAAUxC,QAAA,EAAUC,QAAA,EAAU;MAG7C,MAAM/F,IAAA,GAAO,KAAKA,IAAA;QAChBK,EAAA,GAAK,KAAKA,EAAA;QACVC,EAAA,GAAK,KAAKA,EAAA;QACVC,KAAA,GAAQ,KAAKA,KAAA;MAEf,IAAI4B,CAAA;QACFC,CAAA;QACAC,CAAA;QACAkG,EAAA;QACAf,GAAA;QACAgB,IAAA;QACAC,EAAA;QACAC,GAAA;QACAT,IAAA,GAAOjI,IAAA,GAAOiG,IAAA,CAAKU,IAAA,CAAKb,QAAA,GAAWC,QAAQ;MAE7C,IAAIkC,IAAA,GAAOjI,IAAA,EAAMiI,IAAA,GAAOjI,IAAA;MAExB,KAAKqC,CAAA,GAAI,GAAGA,CAAA,GAAI4F,IAAA,EAAM5F,CAAA,IAAK;QACzBmG,IAAA,GAAOnG,CAAA,GAAIrC,IAAA;QACXuI,EAAA,GAAKC,IAAA,GAAOA,IAAA;QACZhB,GAAA,GAAM1B,QAAA,IAAY,OAASyC,EAAA,IAAMxC,QAAA;QACjC,IAAIyB,GAAA,GAAM,GAAK;UACbiB,EAAA,GAAKnI,EAAA,GAAK+B,CAAA;UAEV,KAAKD,CAAA,GAAI,GAAGA,CAAA,GAAIpC,IAAA,EAAMoC,CAAA,IAAK;YACzBsG,GAAA,GAAMD,EAAA,GAAKrG,CAAA,GAAI/B,EAAA;YAEf,KAAK8B,CAAA,GAAI,GAAGA,CAAA,GAAInC,IAAA,EAAMmC,CAAA,IAAK5B,KAAA,CAAMmI,GAAA,GAAMvG,CAAC,KAAKqF,GAAA;UAC9C;QACF;MACF;IACF;IAMD,KAAKmB,OAAA,GAAU,UAAUxG,CAAA,EAAGC,CAAA,EAAGC,CAAA,EAAGuG,KAAA,EAAO;MACvC,MAAMC,KAAA,GAAQ,KAAK5I,KAAA,GAAQoC,CAAA,GAAI,KAAKrC,IAAA,GAAOoC,CAAA,GAAID,CAAA;MAC/C,KAAK5B,KAAA,CAAMsI,KAAK,IAAID,KAAA;IACrB;IAED,KAAKE,OAAA,GAAU,UAAU3G,CAAA,EAAGC,CAAA,EAAGC,CAAA,EAAG;MAChC,MAAMwG,KAAA,GAAQ,KAAK5I,KAAA,GAAQoC,CAAA,GAAI,KAAKrC,IAAA,GAAOoC,CAAA,GAAID,CAAA;MAC/C,OAAO,KAAK5B,KAAA,CAAMsI,KAAK;IACxB;IAED,KAAKE,IAAA,GAAO,UAAUC,SAAA,GAAY,GAAG;MACnC,MAAMzI,KAAA,GAAQ,KAAKA,KAAA;MACnB,MAAM0I,SAAA,GAAY1I,KAAA,CAAM2I,KAAA,CAAO;MAC/B,MAAMlJ,IAAA,GAAO,KAAKA,IAAA;MAClB,MAAMC,KAAA,GAAQ,KAAKA,KAAA;MACnB,SAASkC,CAAA,GAAI,GAAGA,CAAA,GAAInC,IAAA,EAAMmC,CAAA,IAAK;QAC7B,SAASC,CAAA,GAAI,GAAGA,CAAA,GAAIpC,IAAA,EAAMoC,CAAA,IAAK;UAC7B,SAASC,CAAA,GAAI,GAAGA,CAAA,GAAIrC,IAAA,EAAMqC,CAAA,IAAK;YAC7B,MAAMwG,KAAA,GAAQ5I,KAAA,GAAQoC,CAAA,GAAIrC,IAAA,GAAOoC,CAAA,GAAID,CAAA;YACrC,IAAIqF,GAAA,GAAMyB,SAAA,CAAUJ,KAAK;YACzB,IAAInI,KAAA,GAAQ;YAEZ,SAASyI,EAAA,GAAK,IAAIA,EAAA,IAAM,GAAGA,EAAA,IAAM,GAAG;cAClC,MAAMC,EAAA,GAAKD,EAAA,GAAKhH,CAAA;cAChB,IAAIiH,EAAA,GAAK,KAAKA,EAAA,IAAMpJ,IAAA,EAAM;cAE1B,SAASqJ,EAAA,GAAK,IAAIA,EAAA,IAAM,GAAGA,EAAA,IAAM,GAAG;gBAClC,MAAMC,EAAA,GAAKD,EAAA,GAAKjH,CAAA;gBAChB,IAAIkH,EAAA,GAAK,KAAKA,EAAA,IAAMtJ,IAAA,EAAM;gBAE1B,SAASuJ,EAAA,GAAK,IAAIA,EAAA,IAAM,GAAGA,EAAA,IAAM,GAAG;kBAClC,MAAMC,EAAA,GAAKD,EAAA,GAAKlH,CAAA;kBAChB,IAAImH,EAAA,GAAK,KAAKA,EAAA,IAAMxJ,IAAA,EAAM;kBAE1B,MAAMyJ,MAAA,GAASxJ,KAAA,GAAQuJ,EAAA,GAAKxJ,IAAA,GAAOsJ,EAAA,GAAKF,EAAA;kBACxC,MAAMM,IAAA,GAAOT,SAAA,CAAUQ,MAAM;kBAE7B/I,KAAA;kBACA8G,GAAA,IAAQwB,SAAA,IAAaU,IAAA,GAAOlC,GAAA,IAAQ9G,KAAA;gBACrC;cACF;YACF;YAEDH,KAAA,CAAMsI,KAAK,IAAIrB,GAAA;UAChB;QACF;MACF;IACF;IAED,KAAKmC,KAAA,GAAQ,YAAY;MAGvB,SAAS5E,CAAA,GAAI,GAAGA,CAAA,GAAI,KAAK7E,KAAA,EAAO6E,CAAA,IAAK;QACnC,KAAKvE,YAAA,CAAauE,CAAA,GAAI,CAAC,IAAI;QAC3B,KAAKxE,KAAA,CAAMwE,CAAC,IAAI;QAChB,KAAKtE,OAAA,CAAQsE,CAAA,GAAI,CAAC,IAAI,KAAKtE,OAAA,CAAQsE,CAAA,GAAI,IAAI,CAAC,IAAI,KAAKtE,OAAA,CAAQsE,CAAA,GAAI,IAAI,CAAC,IAAI;MAC3E;IACF;IAED,KAAK6E,MAAA,GAAS,YAAY;MACxB,KAAKlJ,KAAA,GAAQ;MAIb,MAAMmJ,KAAA,GAAQ,KAAK7J,IAAA,GAAO;MAE1B,SAASqC,CAAA,GAAI,GAAGA,CAAA,GAAIwH,KAAA,EAAOxH,CAAA,IAAK;QAC9B,MAAMkF,QAAA,GAAW,KAAKtH,KAAA,GAAQoC,CAAA;QAC9B,MAAMe,EAAA,IAAMf,CAAA,GAAI,KAAKlC,QAAA,IAAY,KAAKA,QAAA;QAEtC,SAASiC,CAAA,GAAI,GAAGA,CAAA,GAAIyH,KAAA,EAAOzH,CAAA,IAAK;UAC9B,MAAMkF,QAAA,GAAWC,QAAA,GAAW,KAAKvH,IAAA,GAAOoC,CAAA;UACxC,MAAMe,EAAA,IAAMf,CAAA,GAAI,KAAKjC,QAAA,IAAY,KAAKA,QAAA;UAEtC,SAASgC,CAAA,GAAI,GAAGA,CAAA,GAAI0H,KAAA,EAAO1H,CAAA,IAAK;YAC9B,MAAMe,EAAA,IAAMf,CAAA,GAAI,KAAKhC,QAAA,IAAY,KAAKA,QAAA;YACtC,MAAM6B,CAAA,GAAIsF,QAAA,GAAWnF,CAAA;YAErBc,UAAA,CAAWC,EAAA,EAAIC,EAAA,EAAIC,EAAA,EAAIpB,CAAA,EAAG,KAAKjC,SAAS;UACzC;QACF;MACF;MAID,KAAKV,QAAA,CAASyK,YAAA,CAAa,GAAG,KAAKpJ,KAAK;MAIxCrB,QAAA,CAAS0K,YAAA,CAAa,UAAU,EAAEC,WAAA,GAAc;MAChD3K,QAAA,CAAS0K,YAAA,CAAa,QAAQ,EAAEC,WAAA,GAAc;MAE9C,IAAI,KAAK9K,SAAA,EAAWG,QAAA,CAAS0K,YAAA,CAAa,IAAI,EAAEC,WAAA,GAAc;MAC9D,IAAI,KAAK7K,YAAA,EAAcE,QAAA,CAAS0K,YAAA,CAAa,OAAO,EAAEC,WAAA,GAAc;MAIpE,IAAI,KAAKtJ,KAAA,GAAQ,IAAItB,YAAA,EACnB6K,OAAA,CAAQC,IAAA,CACN,oHACD;IACJ;IAED,KAAKrK,IAAA,CAAKb,UAAU;EACrB;AACH;AAWK,MAACsF,SAAA,GAAY,IAAI6F,UAAA,CAAY,CACjC,GAAK,KAAO,KAAO,KAAO,MAAO,MAAO,MAAO,MAC/C,MAAO,MAAO,MAAO,MAAO,MAAO,MAAO,MAAO,MACjD,KAAO,KAAM,KAAO,KAAO,MAAO,MAAO,MAAO,MAChD,MAAO,MAAO,MAAO,MAAO,MAAO,MAAO,MAAO,MACjD,KAAO,KAAO,IAAM,KAAO,MAAO,MAAO,MAAO,MAChD,MAAO,MAAO,MAAO,MAAO,MAAO,MAAO,MAAO,MACjD,KAAO,KAAO,KAAO,KAAM,MAAO,MAAO,MAAO,MAChD,MAAO,MAAO,MAAO,MAAO,MAAO,MAAO,MAAO,MACjD,MAAO,MAAO,MAAO,MAAO,KAAM,KAAO,KAAO,KAChD,MAAO,MAAO,MAAO,MAAO,MAAO,MAAO,MAAO,MACjD,MAAO,MAAO,MAAO,MAAO,KAAO,KAAM,MAAO,KAChD,MAAO,MAAO,MAAO,MAAO,MAAO,MAAO,MAAO,MACjD,MAAO,MAAO,MAAO,MAAO,KAAO,KAAO,IAAM,KAChD,MAAO,MAAO,MAAO,MAAO,MAAO,MAAO,MAAO,MACjD,MAAO,MAAO,MAAO,MAAO,KAAO,KAAO,KAAO,KACjD,MAAO,MAAO,MAAO,MAAO,MAAO,MAAO,MAAO,MACjD,MAAO,MAAO,MAAO,MAAO,MAAO,MAAO,MAAO,MACjD,KAAM,KAAO,KAAO,KAAO,MAAO,MAAO,MAAO,MAChD,MAAO,MAAO,MAAO,MAAO,MAAO,MAAO,MAAO,MACjD,KAAO,IAAM,KAAO,KAAO,MAAO,MAAO,MAAO,MAChD,MAAO,MAAO,MAAO,MAAO,MAAO,MAAO,MAAO,MACjD,KAAO,MAAO,KAAM,KAAO,MAAO,MAAO,MAAO,MAChD,MAAO,MAAO,MAAO,MAAO,MAAO,MAAO,MAAO,MACjD,KAAO,KAAO,KAAO,KAAM,MAAO,MAAO,MAAO,MAChD,MAAO,MAAO,MAAO,MAAO,MAAO,MAAO,MAAO,MACjD,MAAO,MAAO,MAAO,MAAO,KAAM,KAAO,KAAO,KAChD,MAAO,MAAO,MAAO,MAAO,MAAO,MAAO,MAAO,MACjD,MAAO,MAAO,MAAO,MAAO,KAAO,IAAM,KAAO,KAChD,MAAO,MAAO,MAAO,MAAO,MAAO,MAAO,MAAO,MACjD,MAAO,MAAO,MAAO,MAAO,KAAO,KAAO,KAAM,KAChD,MAAO,MAAO,MAAO,MAAO,MAAO,MAAO,MAAO,MACjD,MAAO,MAAO,MAAO,MAAO,KAAO,KAAO,KAAO,EAAQ;AAGrD,MAACnF,QAAA,GAAW,IAAImF,UAAA,CAAY,CAChC,IAAK,IAAK,IAAK,IAAK,IAAK,IAAK,IAAK,IAAK,IAAK,IAAK,IAAK,IAAK,IAAK,IAAK,IAAK,IAC3E,GAAG,GAAG,GAAG,IAAK,IAAK,IAAK,IAAK,IAAK,IAAK,IAAK,IAAK,IAAK,IAAK,IAAK,IAAK,IACrE,GAAG,GAAG,GAAG,IAAK,IAAK,IAAK,IAAK,IAAK,IAAK,IAAK,IAAK,IAAK,IAAK,IAAK,IAAK,IACrE,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,IAAK,IAAK,IAAK,IAAK,IAAK,IAAK,IAAK,IAAK,IAAK,IAC/D,GAAG,GAAG,IAAI,IAAK,IAAK,IAAK,IAAK,IAAK,IAAK,IAAK,IAAK,IAAK,IAAK,IAAK,IAAK,IACtE,GAAG,GAAG,GAAG,GAAG,GAAG,IAAI,IAAK,IAAK,IAAK,IAAK,IAAK,IAAK,IAAK,IAAK,IAAK,IAChE,GAAG,GAAG,IAAI,GAAG,GAAG,GAAG,IAAK,IAAK,IAAK,IAAK,IAAK,IAAK,IAAK,IAAK,IAAK,IAChE,GAAG,GAAG,GAAG,GAAG,IAAI,GAAG,IAAI,GAAG,GAAG,IAAK,IAAK,IAAK,IAAK,IAAK,IAAK,IAC3D,GAAG,IAAI,GAAG,IAAK,IAAK,IAAK,IAAK,IAAK,IAAK,IAAK,IAAK,IAAK,IAAK,IAAK,IAAK,IACtE,GAAG,IAAI,GAAG,GAAG,IAAI,GAAG,IAAK,IAAK,IAAK,IAAK,IAAK,IAAK,IAAK,IAAK,IAAK,IACjE,GAAG,GAAG,GAAG,GAAG,GAAG,IAAI,IAAK,IAAK,IAAK,IAAK,IAAK,IAAK,IAAK,IAAK,IAAK,IAChE,GAAG,IAAI,GAAG,GAAG,GAAG,IAAI,GAAG,GAAG,IAAI,IAAK,IAAK,IAAK,IAAK,IAAK,IAAK,IAC5D,GAAG,IAAI,GAAG,IAAI,IAAI,GAAG,IAAK,IAAK,IAAK,IAAK,IAAK,IAAK,IAAK,IAAK,IAAK,IAClE,GAAG,IAAI,GAAG,GAAG,GAAG,IAAI,GAAG,IAAI,IAAI,IAAK,IAAK,IAAK,IAAK,IAAK,IAAK,IAC7D,GAAG,GAAG,GAAG,GAAG,IAAI,GAAG,IAAI,IAAI,GAAG,IAAK,IAAK,IAAK,IAAK,IAAK,IAAK,IAC5D,GAAG,GAAG,IAAI,IAAI,GAAG,IAAI,IAAK,IAAK,IAAK,IAAK,IAAK,IAAK,IAAK,IAAK,IAAK,IAClE,GAAG,GAAG,GAAG,IAAK,IAAK,IAAK,IAAK,IAAK,IAAK,IAAK,IAAK,IAAK,IAAK,IAAK,IAAK,IACrE,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,IAAK,IAAK,IAAK,IAAK,IAAK,IAAK,IAAK,IAAK,IAAK,IAC/D,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,IAAK,IAAK,IAAK,IAAK,IAAK,IAAK,IAAK,IAAK,IAAK,IAC/D,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,IAAK,IAAK,IAAK,IAAK,IAAK,IAAK,IACzD,GAAG,GAAG,IAAI,GAAG,GAAG,GAAG,IAAK,IAAK,IAAK,IAAK,IAAK,IAAK,IAAK,IAAK,IAAK,IAChE,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,IAAI,IAAK,IAAK,IAAK,IAAK,IAAK,IAAK,IAC1D,GAAG,GAAG,IAAI,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,IAAK,IAAK,IAAK,IAAK,IAAK,IAAK,IAC1D,GAAG,IAAI,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,IAAK,IAAK,IAAK,IACpD,GAAG,GAAG,GAAG,GAAG,IAAI,GAAG,IAAK,IAAK,IAAK,IAAK,IAAK,IAAK,IAAK,IAAK,IAAK,IAChE,IAAI,GAAG,GAAG,IAAI,GAAG,GAAG,GAAG,GAAG,GAAG,IAAK,IAAK,IAAK,IAAK,IAAK,IAAK,IAC3D,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,IAAI,IAAK,IAAK,IAAK,IAAK,IAAK,IAAK,IAC1D,GAAG,GAAG,IAAI,GAAG,GAAG,IAAI,GAAG,IAAI,GAAG,GAAG,GAAG,GAAG,IAAK,IAAK,IAAK,IACtD,GAAG,IAAI,GAAG,GAAG,IAAI,IAAI,GAAG,GAAG,GAAG,IAAK,IAAK,IAAK,IAAK,IAAK,IAAK,IAC5D,GAAG,IAAI,IAAI,GAAG,GAAG,IAAI,GAAG,GAAG,GAAG,GAAG,IAAI,GAAG,IAAK,IAAK,IAAK,IACvD,GAAG,GAAG,GAAG,GAAG,GAAG,IAAI,GAAG,IAAI,IAAI,IAAI,GAAG,GAAG,IAAK,IAAK,IAAK,IACvD,GAAG,GAAG,IAAI,GAAG,IAAI,GAAG,GAAG,IAAI,IAAI,IAAK,IAAK,IAAK,IAAK,IAAK,IAAK,IAC7D,GAAG,GAAG,GAAG,IAAK,IAAK,IAAK,IAAK,IAAK,IAAK,IAAK,IAAK,IAAK,IAAK,IAAK,IAAK,IACrE,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,IAAK,IAAK,IAAK,IAAK,IAAK,IAAK,IAAK,IAAK,IAAK,IAC/D,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,IAAK,IAAK,IAAK,IAAK,IAAK,IAAK,IAAK,IAAK,IAAK,IAC/D,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,IAAK,IAAK,IAAK,IAAK,IAAK,IAAK,IACzD,GAAG,GAAG,IAAI,GAAG,GAAG,GAAG,IAAK,IAAK,IAAK,IAAK,IAAK,IAAK,IAAK,IAAK,IAAK,IAChE,GAAG,GAAG,GAAG,GAAG,GAAG,IAAI,GAAG,GAAG,GAAG,IAAK,IAAK,IAAK,IAAK,IAAK,IAAK,IAC1D,GAAG,GAAG,IAAI,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,IAAK,IAAK,IAAK,IAAK,IAAK,IAAK,IAC1D,GAAG,IAAI,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,IAAK,IAAK,IAAK,IACpD,GAAG,GAAG,GAAG,GAAG,GAAG,IAAI,IAAK,IAAK,IAAK,IAAK,IAAK,IAAK,IAAK,IAAK,IAAK,IAChE,GAAG,IAAI,GAAG,GAAG,GAAG,IAAI,GAAG,GAAG,GAAG,IAAK,IAAK,IAAK,IAAK,IAAK,IAAK,IAC3D,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,IAAI,IAAK,IAAK,IAAK,IAAK,IAAK,IAAK,IAC1D,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,IAAI,GAAG,GAAG,GAAG,IAAK,IAAK,IAAK,IACpD,IAAI,GAAG,IAAI,IAAI,GAAG,GAAG,GAAG,GAAG,GAAG,IAAK,IAAK,IAAK,IAAK,IAAK,IAAK,IAC5D,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,IAAI,GAAG,GAAG,IAAI,IAAI,IAAK,IAAK,IAAK,IACtD,GAAG,GAAG,GAAG,GAAG,GAAG,IAAI,GAAG,IAAI,IAAI,IAAI,GAAG,GAAG,IAAK,IAAK,IAAK,IACvD,GAAG,GAAG,GAAG,GAAG,GAAG,IAAI,IAAI,GAAG,IAAI,IAAK,IAAK,IAAK,IAAK,IAAK,IAAK,IAC5D,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,IAAK,IAAK,IAAK,IAAK,IAAK,IAAK,IAAK,IAAK,IAAK,IAC/D,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,IAAK,IAAK,IAAK,IAAK,IAAK,IAAK,IACzD,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,IAAK,IAAK,IAAK,IAAK,IAAK,IAAK,IACzD,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,IAAK,IAAK,IAAK,IAAK,IAAK,IAAK,IAAK,IAAK,IAAK,IAC/D,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,IAAI,GAAG,GAAG,IAAK,IAAK,IAAK,IAAK,IAAK,IAAK,IAC1D,IAAI,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,IAAK,IAAK,IAAK,IACpD,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,IAAI,GAAG,GAAG,IAAK,IAAK,IAAK,IACpD,GAAG,IAAI,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,IAAK,IAAK,IAAK,IAAK,IAAK,IAAK,IAC1D,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,IAAI,GAAG,IAAK,IAAK,IAAK,IAAK,IAAK,IAAK,IAC1D,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,IAAI,IAAK,IAAK,IAAK,IACpD,GAAG,GAAG,IAAI,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,IAAK,IAAK,IAAK,IACpD,IAAI,GAAG,GAAG,IAAI,GAAG,GAAG,GAAG,GAAG,GAAG,IAAK,IAAK,IAAK,IAAK,IAAK,IAAK,IAC3D,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,IAAI,GAAG,GAAG,IAAI,GAAG,IAAI,IAAK,IAAK,IAAK,IACtD,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,IAAI,GAAG,GAAG,GAAG,IAAI,IAAI,IAAI,GAAG,IACjD,IAAI,IAAI,GAAG,IAAI,GAAG,GAAG,IAAI,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,IACjD,IAAI,IAAI,GAAG,GAAG,IAAI,GAAG,IAAK,IAAK,IAAK,IAAK,IAAK,IAAK,IAAK,IAAK,IAAK,IAClE,IAAI,GAAG,GAAG,IAAK,IAAK,IAAK,IAAK,IAAK,IAAK,IAAK,IAAK,IAAK,IAAK,IAAK,IAAK,IACtE,GAAG,GAAG,GAAG,GAAG,IAAI,GAAG,IAAK,IAAK,IAAK,IAAK,IAAK,IAAK,IAAK,IAAK,IAAK,IAChE,GAAG,GAAG,GAAG,GAAG,IAAI,GAAG,IAAK,IAAK,IAAK,IAAK,IAAK,IAAK,IAAK,IAAK,IAAK,IAChE,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,IAAI,GAAG,IAAK,IAAK,IAAK,IAAK,IAAK,IAAK,IAC1D,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,IAAK,IAAK,IAAK,IAAK,IAAK,IAAK,IAAK,IAAK,IAAK,IAC/D,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,IAAK,IAAK,IAAK,IAAK,IAAK,IAAK,IACzD,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,IAAK,IAAK,IAAK,IAAK,IAAK,IAAK,IACzD,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,IAAK,IAAK,IAAK,IACnD,GAAG,GAAG,IAAI,IAAI,GAAG,GAAG,IAAK,IAAK,IAAK,IAAK,IAAK,IAAK,IAAK,IAAK,IAAK,IACjE,IAAI,GAAG,GAAG,IAAI,GAAG,GAAG,IAAI,GAAG,GAAG,IAAK,IAAK,IAAK,IAAK,IAAK,IAAK,IAC5D,GAAG,GAAG,GAAG,GAAG,GAAG,IAAI,GAAG,IAAI,GAAG,IAAK,IAAK,IAAK,IAAK,IAAK,IAAK,IAC3D,GAAG,IAAI,GAAG,GAAG,GAAG,GAAG,GAAG,IAAI,GAAG,GAAG,GAAG,IAAI,IAAK,IAAK,IAAK,IACtD,GAAG,GAAG,IAAI,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,IAAK,IAAK,IAAK,IAAK,IAAK,IAAK,IAC1D,GAAG,GAAG,IAAI,GAAG,IAAI,GAAG,GAAG,GAAG,GAAG,GAAG,IAAI,GAAG,IAAK,IAAK,IAAK,IACtD,GAAG,IAAI,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,IAAK,IAAK,IAAK,IACpD,GAAG,GAAG,GAAG,GAAG,GAAG,IAAI,IAAI,GAAG,GAAG,IAAK,IAAK,IAAK,IAAK,IAAK,IAAK,IAC3D,GAAG,IAAI,GAAG,GAAG,GAAG,GAAG,IAAK,IAAK,IAAK,IAAK,IAAK,IAAK,IAAK,IAAK,IAAK,IAChE,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,IAAI,IAAK,IAAK,IAAK,IAAK,IAAK,IAAK,IAC1D,GAAG,GAAG,GAAG,GAAG,IAAI,GAAG,GAAG,GAAG,GAAG,IAAK,IAAK,IAAK,IAAK,IAAK,IAAK,IAC1D,IAAI,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,IAAK,IAAK,IAAK,IACpD,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,IAAK,IAAK,IAAK,IAAK,IAAK,IAAK,IACzD,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,IAAK,IAAK,IAAK,IACnD,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,IAAK,IAAK,IAAK,IACnD,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,IAC7C,GAAG,IAAI,GAAG,GAAG,GAAG,GAAG,IAAI,GAAG,GAAG,IAAK,IAAK,IAAK,IAAK,IAAK,IAAK,IAC3D,GAAG,IAAI,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,IAAI,IAAK,IAAK,IAAK,IACrD,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,IAAI,GAAG,IAAI,GAAG,IAAK,IAAK,IAAK,IACrD,GAAG,GAAG,GAAG,GAAG,IAAI,GAAG,GAAG,GAAG,IAAI,GAAG,IAAI,GAAG,GAAG,IAAI,GAAG,IACjD,GAAG,GAAG,GAAG,GAAG,IAAI,GAAG,GAAG,GAAG,GAAG,GAAG,IAAI,GAAG,IAAK,IAAK,IAAK,IACrD,GAAG,GAAG,IAAI,GAAG,IAAI,GAAG,GAAG,GAAG,IAAI,GAAG,IAAI,GAAG,GAAG,GAAG,IAAI,IAClD,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,IAAI,GAAG,GAAG,GAAG,GAAG,GAAG,IAC9C,GAAG,GAAG,GAAG,GAAG,GAAG,IAAI,GAAG,GAAG,GAAG,GAAG,IAAI,GAAG,IAAK,IAAK,IAAK,IACrD,IAAI,GAAG,GAAG,GAAG,GAAG,IAAI,IAAK,IAAK,IAAK,IAAK,IAAK,IAAK,IAAK,IAAK,IAAK,IACjE,GAAG,IAAI,GAAG,GAAG,GAAG,IAAI,GAAG,GAAG,GAAG,IAAK,IAAK,IAAK,IAAK,IAAK,IAAK,IAC3D,IAAI,GAAG,GAAG,IAAI,GAAG,GAAG,GAAG,GAAG,GAAG,IAAK,IAAK,IAAK,IAAK,IAAK,IAAK,IAC3D,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,IAAI,IAAK,IAAK,IAAK,IACpD,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,IAAK,IAAK,IAAK,IAAK,IAAK,IAAK,IACzD,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,IAAK,IAAK,IAAK,IACnD,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,IAAK,IAAK,IAAK,IAAK,IAAK,IAAK,IAAK,IAAK,IAAK,IAC/D,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,IAAK,IAAK,IAAK,IAAK,IAAK,IAAK,IACzD,IAAI,GAAG,GAAG,IAAI,GAAG,GAAG,IAAI,GAAG,GAAG,IAAK,IAAK,IAAK,IAAK,IAAK,IAAK,IAC5D,GAAG,GAAG,GAAG,GAAG,GAAG,IAAI,GAAG,GAAG,IAAI,GAAG,IAAI,GAAG,IAAK,IAAK,IAAK,IACtD,GAAG,IAAI,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,IAAI,IAAK,IAAK,IAAK,IACrD,GAAG,GAAG,GAAG,GAAG,GAAG,IAAI,GAAG,GAAG,GAAG,GAAG,GAAG,IAAI,GAAG,IAAI,GAAG,IAChD,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,IAAI,GAAG,GAAG,IAAK,IAAK,IAAK,IACpD,GAAG,IAAI,GAAG,GAAG,GAAG,GAAG,IAAI,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,IAC/C,GAAG,IAAI,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,IAAK,IAAK,IAAK,IAAK,IAAK,IAAK,IAC1D,GAAG,GAAG,GAAG,IAAI,GAAG,GAAG,IAAK,IAAK,IAAK,IAAK,IAAK,IAAK,IAAK,IAAK,IAAK,IAChE,GAAG,IAAI,GAAG,GAAG,GAAG,IAAI,GAAG,GAAG,IAAI,IAAK,IAAK,IAAK,IAAK,IAAK,IAAK,IAC5D,GAAG,GAAG,GAAG,GAAG,IAAI,GAAG,GAAG,GAAG,IAAI,GAAG,GAAG,IAAI,IAAK,IAAK,IAAK,IACtD,IAAI,GAAG,GAAG,GAAG,IAAI,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,IAAK,IAAK,IAAK,IACrD,IAAI,GAAG,GAAG,IAAI,GAAG,GAAG,GAAG,GAAG,GAAG,IAAK,IAAK,IAAK,IAAK,IAAK,IAAK,IAC3D,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,IAAK,IAAK,IAAK,IACnD,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,IAC7C,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,IAAK,IAAK,IAAK,IAAK,IAAK,IAAK,IACzD,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,IAAK,IAAK,IAAK,IAAK,IAAK,IAAK,IAAK,IAAK,IAAK,IAC/D,GAAG,GAAG,IAAI,IAAI,GAAG,GAAG,IAAI,GAAG,GAAG,GAAG,GAAG,GAAG,IAAK,IAAK,IAAK,IACtD,GAAG,GAAG,GAAG,GAAG,GAAG,IAAI,GAAG,GAAG,GAAG,GAAG,GAAG,IAAI,GAAG,IAAI,GAAG,IAChD,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,IAAI,GAAG,GAAG,GAAG,IAAI,GAAG,GAAG,IAAI,IAChD,IAAI,GAAG,GAAG,IAAI,GAAG,GAAG,IAAI,GAAG,GAAG,GAAG,GAAG,GAAG,IAAK,IAAK,IAAK,IACtD,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,IAAI,GAAG,GAAG,GAAG,GAAG,GAAG,IAC9C,GAAG,GAAG,GAAG,IAAI,GAAG,GAAG,IAAK,IAAK,IAAK,IAAK,IAAK,IAAK,IAAK,IAAK,IAAK,IAChE,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,IAAI,GAAG,IAAI,GAAG,GAAG,IAAK,IAAK,IAAK,IACrD,GAAG,IAAI,GAAG,IAAK,IAAK,IAAK,IAAK,IAAK,IAAK,IAAK,IAAK,IAAK,IAAK,IAAK,IAAK,IACtE,GAAG,GAAG,IAAI,IAAK,IAAK,IAAK,IAAK,IAAK,IAAK,IAAK,IAAK,IAAK,IAAK,IAAK,IAAK,IACtE,GAAG,GAAG,GAAG,IAAI,GAAG,GAAG,IAAK,IAAK,IAAK,IAAK,IAAK,IAAK,IAAK,IAAK,IAAK,IAChE,GAAG,GAAG,GAAG,IAAI,GAAG,GAAG,IAAK,IAAK,IAAK,IAAK,IAAK,IAAK,IAAK,IAAK,IAAK,IAChE,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,IAAI,GAAG,GAAG,IAAK,IAAK,IAAK,IAAK,IAAK,IAAK,IAC1D,IAAI,GAAG,GAAG,GAAG,IAAI,GAAG,IAAK,IAAK,IAAK,IAAK,IAAK,IAAK,IAAK,IAAK,IAAK,IACjE,GAAG,GAAG,IAAI,GAAG,GAAG,GAAG,GAAG,IAAI,GAAG,IAAK,IAAK,IAAK,IAAK,IAAK,IAAK,IAC3D,GAAG,GAAG,GAAG,GAAG,IAAI,GAAG,GAAG,IAAI,GAAG,IAAK,IAAK,IAAK,IAAK,IAAK,IAAK,IAC3D,GAAG,IAAI,GAAG,GAAG,IAAI,GAAG,IAAI,GAAG,GAAG,IAAI,GAAG,GAAG,IAAK,IAAK,IAAK,IACvD,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,IAAK,IAAK,IAAK,IAAK,IAAK,IAAK,IAAK,IAAK,IAAK,IAC/D,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,IAAK,IAAK,IAAK,IAAK,IAAK,IAAK,IACzD,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,IAAK,IAAK,IAAK,IAAK,IAAK,IAAK,IACzD,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,IAAK,IAAK,IAAK,IACnD,IAAI,GAAG,GAAG,IAAI,GAAG,GAAG,GAAG,GAAG,GAAG,IAAK,IAAK,IAAK,IAAK,IAAK,IAAK,IAC3D,IAAI,GAAG,GAAG,GAAG,GAAG,IAAI,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,IAAK,IAAK,IAAK,IACrD,GAAG,GAAG,GAAG,GAAG,GAAG,IAAI,GAAG,IAAI,GAAG,GAAG,IAAI,GAAG,IAAK,IAAK,IAAK,IACtD,GAAG,GAAG,IAAI,GAAG,IAAI,GAAG,GAAG,IAAI,GAAG,IAAK,IAAK,IAAK,IAAK,IAAK,IAAK,IAC5D,GAAG,GAAG,GAAG,IAAI,GAAG,GAAG,IAAK,IAAK,IAAK,IAAK,IAAK,IAAK,IAAK,IAAK,IAAK,IAChE,GAAG,GAAG,IAAI,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,IAAK,IAAK,IAAK,IAAK,IAAK,IAAK,IAC1D,GAAG,GAAG,IAAI,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,IAAK,IAAK,IAAK,IAAK,IAAK,IAAK,IAC1D,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,IAAI,GAAG,GAAG,IAAK,IAAK,IAAK,IACpD,GAAG,GAAG,GAAG,GAAG,IAAI,GAAG,GAAG,IAAI,GAAG,IAAK,IAAK,IAAK,IAAK,IAAK,IAAK,IAC3D,GAAG,GAAG,IAAI,GAAG,GAAG,IAAI,GAAG,GAAG,IAAI,GAAG,GAAG,GAAG,IAAK,IAAK,IAAK,IACtD,GAAG,IAAI,GAAG,GAAG,GAAG,IAAI,GAAG,GAAG,GAAG,GAAG,IAAI,GAAG,IAAK,IAAK,IAAK,IACtD,IAAI,GAAG,GAAG,IAAI,GAAG,GAAG,GAAG,GAAG,GAAG,IAAI,GAAG,GAAG,GAAG,GAAG,GAAG,IAChD,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,IAAK,IAAK,IAAK,IAAK,IAAK,IAAK,IACzD,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,IAAK,IAAK,IAAK,IAAK,IAAK,IAAK,IAAK,IAAK,IAAK,IAC/D,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,IAAK,IAAK,IAAK,IACnD,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,IAAK,IAAK,IAAK,IAAK,IAAK,IAAK,IACzD,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,IAAI,GAAG,IAAK,IAAK,IAAK,IACpD,IAAI,GAAG,GAAG,IAAI,GAAG,GAAG,GAAG,GAAG,GAAG,IAAK,IAAK,IAAK,IAAK,IAAK,IAAK,IAC3D,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,IAAI,GAAG,GAAG,GAAG,GAAG,IAAI,GAAG,GAAG,IAC/C,IAAI,GAAG,GAAG,GAAG,IAAI,GAAG,IAAK,IAAK,IAAK,IAAK,IAAK,IAAK,IAAK,IAAK,IAAK,IACjE,GAAG,GAAG,GAAG,GAAG,GAAG,IAAI,IAAK,IAAK,IAAK,IAAK,IAAK,IAAK,IAAK,IAAK,IAAK,IAChE,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,IAAI,GAAG,GAAG,IAAK,IAAK,IAAK,IAAK,IAAK,IAAK,IAC1D,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,IAAI,IAAK,IAAK,IAAK,IAAK,IAAK,IAAK,IAC1D,IAAI,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,IAAK,IAAK,IAAK,IACpD,GAAG,GAAG,GAAG,IAAI,GAAG,GAAG,GAAG,GAAG,IAAI,IAAK,IAAK,IAAK,IAAK,IAAK,IAAK,IAC3D,GAAG,IAAI,GAAG,GAAG,GAAG,IAAI,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,IAAK,IAAK,IAAK,IACrD,GAAG,GAAG,IAAI,GAAG,GAAG,IAAI,GAAG,GAAG,IAAI,GAAG,GAAG,GAAG,IAAK,IAAK,IAAK,IACtD,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,IAAI,GAAG,GAAG,IAAI,GAAG,GAAG,IAC/C,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,IAAK,IAAK,IAAK,IAAK,IAAK,IAAK,IACzD,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,IAAK,IAAK,IAAK,IACnD,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,IAAK,IAAK,IAAK,IACnD,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,IAC7C,GAAG,GAAG,GAAG,IAAI,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,IAAK,IAAK,IAAK,IACpD,GAAG,GAAG,IAAI,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,IAC9C,GAAG,GAAG,IAAI,GAAG,IAAI,GAAG,GAAG,GAAG,IAAI,GAAG,IAAI,GAAG,GAAG,GAAG,IAAI,IAClD,GAAG,GAAG,IAAI,GAAG,IAAI,GAAG,GAAG,GAAG,IAAI,GAAG,GAAG,IAAI,IAAK,IAAK,IAAK,IACvD,GAAG,GAAG,GAAG,GAAG,IAAI,GAAG,IAAI,GAAG,GAAG,IAAK,IAAK,IAAK,IAAK,IAAK,IAAK,IAC3D,GAAG,GAAG,IAAI,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,IAAK,IAAK,IAAK,IACpD,GAAG,IAAI,GAAG,GAAG,GAAG,IAAI,GAAG,GAAG,GAAG,GAAG,GAAG,IAAI,IAAK,IAAK,IAAK,IACtD,GAAG,IAAI,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,IAAK,IAAK,IAAK,IAAK,IAAK,IAAK,IAC1D,GAAG,GAAG,IAAI,GAAG,GAAG,IAAI,GAAG,IAAI,GAAG,IAAI,GAAG,GAAG,IAAK,IAAK,IAAK,IACvD,GAAG,IAAI,GAAG,GAAG,GAAG,IAAI,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,IAAI,IAChD,IAAI,GAAG,GAAG,IAAI,GAAG,GAAG,GAAG,GAAG,GAAG,IAAI,GAAG,GAAG,GAAG,GAAG,GAAG,IAChD,GAAG,IAAI,GAAG,GAAG,GAAG,GAAG,GAAG,IAAI,GAAG,IAAI,GAAG,GAAG,IAAK,IAAK,IAAK,IACtD,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,IAAK,IAAK,IAAK,IACnD,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,IAAK,IAAK,IAAK,IAAK,IAAK,IAAK,IACzD,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,IAC7C,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,IAAK,IAAK,IAAK,IAAK,IAAK,IAAK,IAAK,IAAK,IAAK,IAC/D,GAAG,GAAG,GAAG,GAAG,GAAG,IAAI,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,IAC9C,IAAI,GAAG,GAAG,IAAI,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,IAAK,IAAK,IAAK,IACrD,GAAG,GAAG,GAAG,GAAG,GAAG,IAAI,IAAK,IAAK,IAAK,IAAK,IAAK,IAAK,IAAK,IAAK,IAAK,IAChE,IAAI,GAAG,GAAG,IAAK,IAAK,IAAK,IAAK,IAAK,IAAK,IAAK,IAAK,IAAK,IAAK,IAAK,IAAK,IACtE,IAAI,GAAG,IAAI,GAAG,GAAG,IAAI,IAAK,IAAK,IAAK,IAAK,IAAK,IAAK,IAAK,IAAK,IAAK,IAClE,IAAI,GAAG,IAAI,IAAI,GAAG,GAAG,GAAG,GAAG,GAAG,IAAK,IAAK,IAAK,IAAK,IAAK,IAAK,IAC5D,GAAG,IAAI,GAAG,GAAG,IAAI,IAAI,GAAG,GAAG,GAAG,IAAK,IAAK,IAAK,IAAK,IAAK,IAAK,IAC5D,IAAI,GAAG,GAAG,IAAI,IAAI,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,IAAK,IAAK,IAAK,IACtD,IAAI,GAAG,GAAG,IAAI,GAAG,GAAG,GAAG,GAAG,GAAG,IAAK,IAAK,IAAK,IAAK,IAAK,IAAK,IAC3D,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,IAAI,IAAK,IAAK,IAAK,IACpD,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,IAAI,GAAG,IAAK,IAAK,IAAK,IACpD,GAAG,GAAG,GAAG,GAAG,GAAG,IAAI,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,IAC9C,GAAG,GAAG,IAAI,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,IAAK,IAAK,IAAK,IAAK,IAAK,IAAK,IAC1D,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,IAAI,GAAG,GAAG,IAAK,IAAK,IAAK,IACpD,GAAG,GAAG,GAAG,GAAG,IAAI,GAAG,GAAG,GAAG,GAAG,GAAG,IAAI,GAAG,IAAK,IAAK,IAAK,IACrD,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,IAAI,GAAG,GAAG,GAAG,GAAG,GAAG,IAC9C,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,IAAK,IAAK,IAAK,IAAK,IAAK,IAAK,IAAK,IAAK,IAAK,IAC/D,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,IAAK,IAAK,IAAK,IAAK,IAAK,IAAK,IACzD,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,IAAK,IAAK,IAAK,IAAK,IAAK,IAAK,IACzD,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,IAAK,IAAK,IAAK,IAAK,IAAK,IAAK,IAAK,IAAK,IAAK,IAC/D,GAAG,GAAG,GAAG,GAAG,IAAI,GAAG,IAAI,IAAI,GAAG,IAAK,IAAK,IAAK,IAAK,IAAK,IAAK,IAC5D,GAAG,GAAG,GAAG,GAAG,IAAI,GAAG,GAAG,IAAI,IAAI,IAAI,GAAG,GAAG,IAAK,IAAK,IAAK,IACvD,GAAG,GAAG,GAAG,GAAG,GAAG,IAAI,GAAG,IAAI,IAAI,IAAI,GAAG,GAAG,IAAK,IAAK,IAAK,IACvD,IAAI,IAAI,GAAG,IAAI,GAAG,GAAG,IAAI,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,IACjD,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,IAAI,GAAG,GAAG,GAAG,GAAG,IAAK,IAAK,IAAK,IACpD,GAAG,GAAG,IAAI,GAAG,IAAI,GAAG,GAAG,GAAG,IAAI,GAAG,IAAI,GAAG,GAAG,GAAG,IAAI,IAClD,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,IAAI,GAAG,GAAG,GAAG,GAAG,IAAI,GAAG,GAAG,IAC/C,GAAG,GAAG,GAAG,GAAG,IAAI,GAAG,IAAK,IAAK,IAAK,IAAK,IAAK,IAAK,IAAK,IAAK,IAAK,IAChE,GAAG,GAAG,IAAI,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,IAAK,IAAK,IAAK,IACpD,GAAG,IAAI,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,IAAK,IAAK,IAAK,IAAK,IAAK,IAAK,IAC1D,GAAG,IAAI,GAAG,GAAG,GAAG,IAAI,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,IAC/C,GAAG,IAAI,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,IAAK,IAAK,IAAK,IACpD,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,IAAK,IAAK,IAAK,IAAK,IAAK,IAAK,IACzD,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,IAAK,IAAK,IAAK,IAAK,IAAK,IAAK,IAAK,IAAK,IAAK,IAC/D,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,IAAK,IAAK,IAAK,IACnD,GAAG,GAAG,GAAG,IAAK,IAAK,IAAK,IAAK,IAAK,IAAK,IAAK,IAAK,IAAK,IAAK,IAAK,IAAK,IACrE,GAAG,IAAI,GAAG,GAAG,GAAG,IAAI,GAAG,IAAI,IAAI,IAAK,IAAK,IAAK,IAAK,IAAK,IAAK,IAC7D,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,IAAI,GAAG,GAAG,IAAI,IAAI,IAAK,IAAK,IAAK,IACtD,GAAG,IAAI,IAAI,GAAG,IAAI,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,IAAI,IAAK,IAAK,IAAK,IACvD,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,IAAI,GAAG,GAAG,GAAG,IAAI,IAAI,IAAI,GAAG,IACjD,GAAG,IAAI,GAAG,GAAG,IAAI,GAAG,GAAG,GAAG,IAAI,GAAG,GAAG,GAAG,IAAK,IAAK,IAAK,IACtD,GAAG,GAAG,GAAG,GAAG,IAAI,GAAG,GAAG,GAAG,IAAI,GAAG,IAAI,GAAG,GAAG,GAAG,GAAG,IAChD,IAAI,GAAG,GAAG,IAAI,GAAG,GAAG,GAAG,GAAG,GAAG,IAAK,IAAK,IAAK,IAAK,IAAK,IAAK,IAC3D,IAAI,GAAG,GAAG,IAAI,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,IAAK,IAAK,IAAK,IACrD,GAAG,GAAG,IAAI,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,IAAK,IAAK,IAAK,IACpD,GAAG,IAAI,GAAG,GAAG,GAAG,GAAG,IAAI,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,IAC/C,GAAG,GAAG,IAAI,GAAG,IAAI,GAAG,GAAG,GAAG,IAAI,GAAG,IAAI,GAAG,GAAG,GAAG,IAAI,IAClD,GAAG,IAAI,GAAG,GAAG,GAAG,GAAG,IAAK,IAAK,IAAK,IAAK,IAAK,IAAK,IAAK,IAAK,IAAK,IAChE,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,IAAK,IAAK,IAAK,IAAK,IAAK,IAAK,IACzD,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,IAAK,IAAK,IAAK,IACnD,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,IAAK,IAAK,IAAK,IAAK,IAAK,IAAK,IAAK,IAAK,IAAK,IAC/D,GAAG,GAAG,GAAG,IAAK,IAAK,IAAK,IAAK,IAAK,IAAK,IAAK,IAAK,IAAK,IAAK,IAAK,IAAK,IACrE,GAAG,IAAI,GAAG,IAAI,IAAI,GAAG,IAAK,IAAK,IAAK,IAAK,IAAK,IAAK,IAAK,IAAK,IAAK,IAClE,GAAG,GAAG,GAAG,GAAG,GAAG,IAAI,IAAI,GAAG,IAAI,IAAK,IAAK,IAAK,IAAK,IAAK,IAAK,IAC5D,GAAG,GAAG,IAAI,GAAG,IAAI,GAAG,GAAG,IAAI,IAAI,IAAK,IAAK,IAAK,IAAK,IAAK,IAAK,IAC7D,GAAG,GAAG,IAAI,IAAI,GAAG,IAAI,IAAK,IAAK,IAAK,IAAK,IAAK,IAAK,IAAK,IAAK,IAAK,IAClE,GAAG,GAAG,IAAI,GAAG,IAAI,GAAG,GAAG,IAAI,GAAG,IAAK,IAAK,IAAK,IAAK,IAAK,IAAK,IAC5D,GAAG,GAAG,GAAG,GAAG,GAAG,IAAI,GAAG,GAAG,GAAG,GAAG,IAAI,GAAG,IAAK,IAAK,IAAK,IACrD,GAAG,GAAG,IAAI,GAAG,GAAG,IAAI,IAAK,IAAK,IAAK,IAAK,IAAK,IAAK,IAAK,IAAK,IAAK,IACjE,GAAG,GAAG,IAAI,IAAK,IAAK,IAAK,IAAK,IAAK,IAAK,IAAK,IAAK,IAAK,IAAK,IAAK,IAAK,IACtE,GAAG,GAAG,GAAG,GAAG,GAAG,IAAI,IAAI,GAAG,GAAG,IAAK,IAAK,IAAK,IAAK,IAAK,IAAK,IAC3D,GAAG,IAAI,GAAG,GAAG,GAAG,GAAG,IAAK,IAAK,IAAK,IAAK,IAAK,IAAK,IAAK,IAAK,IAAK,IAChE,GAAG,GAAG,GAAG,GAAG,GAAG,IAAI,GAAG,GAAG,GAAG,GAAG,IAAI,GAAG,IAAK,IAAK,IAAK,IACrD,GAAG,IAAI,GAAG,IAAK,IAAK,IAAK,IAAK,IAAK,IAAK,IAAK,IAAK,IAAK,IAAK,IAAK,IAAK,IACtE,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,IAAK,IAAK,IAAK,IAAK,IAAK,IAAK,IAAK,IAAK,IAAK,IAC/D,GAAG,GAAG,GAAG,IAAK,IAAK,IAAK,IAAK,IAAK,IAAK,IAAK,IAAK,IAAK,IAAK,IAAK,IAAK,IACrE,GAAG,GAAG,GAAG,IAAK,IAAK,IAAK,IAAK,IAAK,IAAK,IAAK,IAAK,IAAK,IAAK,IAAK,IAAK,IACrE,IAAK,IAAK,IAAK,IAAK,IAAK,IAAK,IAAK,IAAK,IAAK,IAAK,IAAK,IAAK,IAAK,IAAK,IAAK,GAAK", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}