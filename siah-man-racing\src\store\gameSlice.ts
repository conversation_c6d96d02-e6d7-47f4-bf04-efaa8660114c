import { createSlice, PayloadAction } from '@reduxjs/toolkit';
import { 
  Track, GameMode, TimeOfDay, Weather, 
  DifficultyLevel, RaceState, Checkpoint,
  RaceResult, Challenge, OpponentVehicle, VehicleType
} from '../types/GameTypes';

// Using Track interface from GameTypes.ts

export interface GameState { 
  // Navigation
  currentScreen: 'mainMenu' | 'garage' | 'trackSelect' | 'race' | 'results' | 'shop' | 'challenges' | 'settings';
  
  // Game status
  isPlaying: boolean;
  gameMode: GameMode;
  
  // Track data
  currentTrackId: string | null;
  availableTracks: Track[];
  
  // Race state
  activeRace: RaceState | null;
  raceState: {
    isRaceStarted: boolean;
    isRaceComplete: boolean;
    currentLap: number;
    position: number;
    raceTime: string;
    bestLapTime: string | null;
    currentLapTime: string;
    experienceReward: number;
    currencyReward: number;
    finalPosition?: number;
    speed: number;
  };
  vehicleState: {
    x: number;
    y: number;
    speed: number;
    acceleration: number;
    direction: number;
    angularVelocity: number;
    isDrifting: boolean;
    isOffroadSurface: boolean;
    nitroFuel: number;
    isNitroActive: boolean;

    // Enhanced arcade features
    isJumping?: boolean;
    jumpHeight?: number;
    airTime?: number;
    driftTime?: number;
    driftScore?: number;
    comboMultiplier?: number;
    lastComboTime?: number;
    totalScore?: number;
    coinsCollected?: number;
    activePowerUps?: Array<{
      type: string;
      expiresAt: number;
      effect: any;
    }>;
    isInvincible?: boolean;
    invincibilityUntil?: number;
    speedBoostUntil?: number;
    coinMagnetUntil?: number;
    doublePointsUntil?: number;
  };
  controls: {
    up: boolean;
    down: boolean;
    left: boolean;
    right: boolean;
    nitro: boolean;
    brake: boolean;
  };
  raceHistory: RaceResult[];
  
  // Challenge mode
  availableChallenges: Challenge[];
  completedChallenges: string[];
  
  // Player progression
  storyProgress: number;
  unlockedAbilities: string[];
  experience: number;
  level: number;
  currency: number;
  
  // Game settings
  selectedTimeOfDay: TimeOfDay;
  selectedWeather: Weather;
  difficulty: DifficultyLevel;
  
  // UI state
  loading: boolean;
  error: string | null;
  uiAssets: Record<string, string>; // Added field for UI assets
}

const initialState: GameState = {
  // Navigation
  currentScreen: 'mainMenu',
  
  // Game status
  isPlaying: false,
  gameMode: 'race',
  
  // Race state
  raceState: {
    isRaceStarted: false,
    isRaceComplete: false,
    currentLap: 0,
    position: 0,
    raceTime: '00:00.000',
    bestLapTime: null,
    currentLapTime: '00:00.000',
    experienceReward: 0,
    currencyReward: 0,
    speed: 0
  },
  vehicleState: {
    x: 100,
    y: 100,
    speed: 0,
    acceleration: 0,
    direction: 0,
    angularVelocity: 0,
    isDrifting: false,
    isOffroadSurface: false,
    nitroFuel: 100,
    isNitroActive: false,

    // Enhanced arcade features - initial values
    isJumping: false,
    jumpHeight: 0,
    airTime: 0,
    driftTime: 0,
    driftScore: 0,
    comboMultiplier: 1,
    lastComboTime: 0,
    totalScore: 0,
    coinsCollected: 0,
    activePowerUps: [],
    isInvincible: false,
    invincibilityUntil: 0,
    speedBoostUntil: 0,
    coinMagnetUntil: 0,
    doublePointsUntil: 0
  },
  controls: {
    up: false,
    down: false,
    left: false,
    right: false,
    nitro: false,
    brake: false
  },
  
  // Track data
  currentTrackId: null,
  availableTracks: [
    {
      id: 'beginner_track',
      name: '🌈 Rainbow Speedway',
      type: 'beginner',
      length: 2000,
      difficulty: 'easy',
      unlocked: true,
      description: 'Perfect for young racers! Colorful and fun!',
      theme: 'rainbow',
      specialFeatures: ['lots_of_coins', 'easy_turns', 'speed_pads'],
      thumbnail: '/assets/images/track_rainbow.jpg'
    },
    {
      id: 'candy_land',
      name: '🍭 Candy Land Circuit',
      type: 'fantasy',
      length: 2500,
      difficulty: 'easy',
      unlocked: true,
      description: 'Sweet racing through candy wonderland!',
      theme: 'candy',
      specialFeatures: ['jump_ramps', 'bouncy_obstacles', 'sugar_rush_zones'],
      thumbnail: '/assets/images/track_candy.jpg'
    },
    {
      id: 'space_adventure',
      name: '🚀 Space Adventure',
      type: 'space',
      length: 3000,
      difficulty: 'medium',
      unlocked: true,
      description: 'Race among the stars with zero gravity zones!',
      theme: 'space',
      specialFeatures: ['zero_gravity', 'asteroid_obstacles', 'warp_tunnels'],
      thumbnail: '/assets/images/track_space.jpg'
    },
    {
      id: 'underwater_world',
      name: '🐠 Underwater World',
      type: 'underwater',
      length: 2800,
      difficulty: 'medium',
      unlocked: false,
      description: 'Dive deep and race with sea creatures!',
      theme: 'underwater',
      specialFeatures: ['bubble_boosts', 'sea_creatures', 'coral_obstacles'],
      thumbnail: '/assets/images/track_underwater.jpg'
    },
    {
      id: 'mountain_track',
      name: '⛰️ Mountain Adventure',
      type: 'mountain',
      length: 3500,
      difficulty: 'medium',
      unlocked: false,
      description: 'Climb high and race through mountain peaks!',
      theme: 'mountain',
      specialFeatures: ['steep_climbs', 'scenic_views', 'mountain_animals'],
      thumbnail: '/assets/images/track_mountain.jpg'
    },
    {
      id: 'jungle_safari',
      name: '🦁 Jungle Safari',
      type: 'jungle',
      length: 3200,
      difficulty: 'medium',
      unlocked: false,
      description: 'Race through wild jungles with animal friends!',
      theme: 'jungle',
      specialFeatures: ['animal_crossings', 'vine_swings', 'waterfall_jumps'],
      thumbnail: '/assets/images/track_jungle.jpg'
    },
    {
      id: 'ice_kingdom',
      name: '❄️ Ice Kingdom',
      type: 'ice',
      length: 2600,
      difficulty: 'hard',
      unlocked: false,
      description: 'Slippery ice racing with penguin friends!',
      theme: 'ice',
      specialFeatures: ['ice_slides', 'penguin_obstacles', 'aurora_lights'],
      thumbnail: '/assets/images/track_ice.jpg'
    },
    {
      id: 'volcano_valley',
      name: '🌋 Volcano Valley',
      type: 'volcano',
      length: 3800,
      difficulty: 'hard',
      unlocked: false,
      description: 'Race through fiery lava flows and hot springs!',
      theme: 'volcano',
      specialFeatures: ['lava_jumps', 'hot_springs', 'fire_obstacles'],
      thumbnail: '/assets/images/track_volcano.jpg'
    },
    {
      id: 'pirate_cove',
      name: '🏴‍☠️ Pirate Cove',
      type: 'pirate',
      length: 2900,
      difficulty: 'medium',
      unlocked: false,
      description: 'Ahoy! Race with pirates and treasure!',
      theme: 'pirate',
      specialFeatures: ['treasure_chests', 'pirate_ships', 'cannon_obstacles'],
      thumbnail: '/assets/images/track_pirate.jpg'
    },
    {
      id: 'rainbow_bridge',
      name: '🌈 Rainbow Bridge',
      type: 'fantasy',
      length: 2200,
      difficulty: 'easy',
      unlocked: false,
      description: 'Race across magical rainbow bridges!',
      theme: 'rainbow',
      specialFeatures: ['rainbow_boosts', 'cloud_platforms', 'unicorn_friends'],
      thumbnail: '/assets/images/track_rainbow_bridge.jpg'
    },
  ],
  
  // Race state
  activeRace: null,
  raceHistory: [],
  
  // Challenge mode
  availableChallenges: [
    {
      id: 'challenge1',
      name: 'Speed Challenge',
      description: 'Complete a lap in under 60 seconds',
      trackId: 'oval_track',
      objective: {
        type: 'time',
        target: 60,
        timeLimit: 120
      },
      reward: {
        money: 1000,
        experience: 500
      },
      difficulty: 'easy'
    },
    {
      id: 'challenge2',
      name: 'Overtake Challenge',
      description: 'Overtake 10 vehicles in 2 minutes',
      trackId: 'lake_track',
      objective: {
        type: 'overtake',
        target: 10,
        timeLimit: 120
      },
      reward: {
        money: 1500,
        experience: 700
      },
      difficulty: 'medium'
    },
    {
      id: 'challenge3',
      name: 'Mountain Master',
      description: 'Complete 3 laps of the mountain track',
      trackId: 'mountain_track',
      objective: {
        type: 'position',
        target: 1,
        timeLimit: 300
      },
      reward: {
        money: 2000,
        experience: 1000,
        unlockTrack: 'city_track'
      },
      difficulty: 'hard'
    },
    {
      id: 'challenge4',
      name: 'Forest Frenzy',
      description: 'Complete 2 laps of the forest track without crashing',
      trackId: 'forest_track',
      objective: {
        type: 'position',
        target: 1,
        timeLimit: 240
      },
      reward: {
        money: 2500,
        experience: 1200
      },
      difficulty: 'medium'
    }
  ],
  completedChallenges: [],
  
  // Player progression
  storyProgress: 0,
  unlockedAbilities: [],
  experience: 0,
  level: 1,
  currency: 1000,
  
  // Game settings
  selectedTimeOfDay: 'noon',
  selectedWeather: 'clear',
  difficulty: 'medium',
  
  // UI state
  loading: false,
  error: null,
  uiAssets: {
    tachometer_image: '/assets/images/TahometerInside.png',
    tachometer_arrow: '/assets/images/TahometerArrow.png'
  }
};

export const gameSlice = createSlice({
  name: 'game',
  initialState,
  reducers: {
    // Navigation actions
    setCurrentScreen: (state, action: PayloadAction<GameState['currentScreen']>) => {
      state.currentScreen = action.payload;
    },
    
    // Game mode actions
    setGameMode: (state, action: PayloadAction<GameMode>) => {
      state.gameMode = action.payload;
    },
    
    // Race actions
    startRace: (state, action: PayloadAction<{
      trackId: string;
      vehicleId: string;
      gameMode: GameMode;
      opponents?: number;
      laps?: number;
      weather?: Weather;
      timeOfDay?: TimeOfDay;
      difficulty?: DifficultyLevel;
      challengeId?: string;
    }>) => {
      const { 
        trackId, 
        vehicleId, 
        gameMode, 
        opponents = 3, 
        laps = 3, 
        weather = state.selectedWeather, 
        timeOfDay = state.selectedTimeOfDay, 
        difficulty = state.difficulty,
        challengeId 
      } = action.payload;
      
      state.isPlaying = true;
      state.currentTrackId = trackId;
      state.gameMode = gameMode;
      
      // Create checkpoints based on track type
      const track = state.availableTracks.find(t => t.id === trackId);
      const checkpoints: Checkpoint[] = [];
      
      // Simple checkpoint generation (would be more complex in real implementation)
      if (track) {
        const checkpointCount = Math.floor(track.length / 500);
        for (let i = 0; i < checkpointCount; i++) {
          checkpoints.push({
            id: i,
            position: [Math.cos(i / checkpointCount * Math.PI * 2) * 40, 0, Math.sin(i / checkpointCount * Math.PI * 2) * 40],
            passed: false
          });
        }
      }
      
      // Helper function to generate opponent vehicles
      const generateOpponents = (count: number, trackId: string, mode: GameMode): OpponentVehicle[] => {
        const opponentVehicles: OpponentVehicle[] = [];
        
        // Generate different opponents based on difficulty and game mode
        for (let i = 0; i < count; i++) {
          const vehicleTypes: VehicleType[] = ['sports', 'muscle', 'rally', 'supercar', 'concept'];
          const randomType = vehicleTypes[Math.floor(Math.random() * vehicleTypes.length)];
          
          opponentVehicles.push({
            id: `opponent-${i}`,
            vehicleId: `vehicle-${randomType}-${i}`,
            name: `AI Driver ${i + 1}`,
            position: i + 1,
            currentLap: 1,
            lapTimes: [],
            currentSpeed: 0,
            difficulty: state.difficulty
          });
        }
        
        return opponentVehicles;
      };
      
      // Initialize race state
      state.activeRace = {
        mode: gameMode,
        trackId,
        vehicleId,
        opponents: generateOpponents(opponents, trackId, gameMode),
        playerPosition: opponents,
        currentLap: 1,
        totalLaps: laps,
        raceTime: 0,
        bestLapTime: 0,
        currentLapTime: 0,
        checkpoints,
        weather: weather,
        timeOfDay: timeOfDay,
        difficulty: difficulty,
        paused: false,
        finished: false,
        challengeId: challengeId
      };
    },
    
    updateRaceState: (state, action: PayloadAction<Partial<{
      isRaceStarted: boolean;
      isRaceComplete: boolean;
      currentLap: number;
      position: number;
      raceTime: string;
      bestLapTime: string | null;
      currentLapTime: string;
      speed: number;
    }>>) => {
      state.raceState = {
        ...state.raceState,
        ...action.payload
      };
    },
    
    updateVehicleState: (state, action: PayloadAction<Partial<typeof initialState.vehicleState>>) => {
      state.vehicleState = {
        ...state.vehicleState,
        ...action.payload
      };
    },
    
    setControls: (state, action: PayloadAction<Partial<{
      up: boolean;
      down: boolean;
      left: boolean;
      right: boolean;
      nitro: boolean;
      brake: boolean;
    }>>) => {
      state.controls = {
        ...state.controls,
        ...action.payload
      };
    },

    setInputState: (state, action: PayloadAction<Partial<typeof initialState.controls>>) => {
      state.controls = {
        ...state.controls,
        ...action.payload
      };
    },
    
    endRace: (state, action: PayloadAction<{
      finalPosition: number;
      finalTime: string;
      experienceReward: number;
      currencyReward: number;
    }>) => {
      state.raceState = {
        ...state.raceState,
        isRaceComplete: true,
        finalPosition: action.payload.finalPosition,
        experienceReward: action.payload.experienceReward,
        currencyReward: action.payload.currencyReward
      };
      
      state.isPlaying = false;
      
      // Also update the old race state format for backward compatibility
      if (!state.activeRace) return;
      
      const { playerPosition, currentLap, raceTime, currentLapTime, checkpointId } = action.payload;
      
      if (playerPosition !== undefined) {
        state.activeRace.playerPosition = playerPosition;
      }
      
      if (currentLap !== undefined) {
        state.activeRace.currentLap = currentLap;
      }
      
      if (raceTime !== undefined) {
        state.activeRace.raceTime = raceTime;
      }
      
      if (currentLapTime !== undefined) {
        state.activeRace.currentLapTime = currentLapTime;
        
        // Update best lap time if this is a new best
        if (state.activeRace.bestLapTime === 0 || currentLapTime < state.activeRace.bestLapTime) {
          state.activeRace.bestLapTime = currentLapTime;
        }
      }
      
      if (checkpointId !== undefined) {
        const checkpoint = state.activeRace.checkpoints.find(c => c.id === checkpointId);
        if (checkpoint) {
          checkpoint.passed = true;
          checkpoint.passTime = state.activeRace.raceTime;
        }
      }
      
      // Check if race is finished
      if (state.activeRace.currentLap >= state.activeRace.totalLaps) {
        state.activeRace.finished = true;
      }
    },
    
    finishRace: (state, action: PayloadAction<RaceResult>) => {
      if (!state.activeRace) return;
      
      state.isPlaying = false;
      state.activeRace.finished = true;
      state.activeRace.raceResult = action.payload;
      state.raceHistory.push(action.payload);
      
      // Update best time for the track
      const track = state.availableTracks.find(t => t.id === state.currentTrackId);
      if (track && state.activeRace.vehicleId) {
        const vehicleId = state.activeRace.vehicleId;
        if (!track.bestTimes[vehicleId] || action.payload.totalTime < track.bestTimes[vehicleId]) {
          track.bestTimes[vehicleId] = action.payload.totalTime;
        }
      }
      
      // Add rewards
      state.currency += action.payload.moneyEarned;
      state.experience += action.payload.experienceEarned;
      state.level = Math.floor(state.experience / 1000) + 1;
    },
    
    pauseRace: (state, action: PayloadAction<boolean>) => {
      if (state.activeRace) {
        state.activeRace.paused = action.payload;
      }
    },
    
    resetRace: (state) => {
      if (state.activeRace) {
        state.activeRace.currentLap = 0;
        state.activeRace.raceTime = 0;
        state.activeRace.currentLapTime = 0;
        state.activeRace.playerPosition = state.activeRace.opponents.length + 1;
        state.activeRace.checkpoints.forEach(c => c.passed = false);
        state.activeRace.finished = false;
        state.activeRace.raceResult = undefined;
      }
    },
    
    // Challenge mode actions
    startChallenge: (state, action: PayloadAction<string>) => {
      const challengeId = action.payload;
      const challenge = state.availableChallenges.find(c => c.id === challengeId);
      
      if (challenge) {
        state.gameMode = 'challenge';
        state.isPlaying = true;
        state.currentTrackId = challenge.trackId;
        
        // Initialize race state for challenge
        // This would be more complex in a real implementation
      }
    },
    
    completeChallenge: (state, action: PayloadAction<{
      challengeId: string;
      success: boolean;
      time: number;
    }>) => {
      const { challengeId, success, time } = action.payload;
      
      if (success && !state.completedChallenges.includes(challengeId)) {
        state.completedChallenges.push(challengeId);
        
        // Award challenge rewards
        const challenge = state.availableChallenges.find(c => c.id === challengeId);
        if (challenge) {
          state.currency += challenge.reward.money;
          state.experience += challenge.reward.experience;
          
          // Unlock track if specified in reward
          if (challenge.reward.unlockTrack) {
            const track = state.availableTracks.find(t => t.id === challenge.reward.unlockTrack);
            if (track) {
              track.unlocked = true;
            }
          }
        }
      }
      
      state.isPlaying = false;
    },
    
    // Track actions
    setCurrentTrack: (state, action: PayloadAction<string>) => {
      state.currentTrackId = action.payload;
    },
    
    unlockTrack: (state, action: PayloadAction<string>) => {
      const track = state.availableTracks.find(t => t.id === action.payload);
      if (track) {
        track.unlocked = true;
      }
    },
    
    // Environment settings
    setTimeOfDay: (state, action: PayloadAction<TimeOfDay>) => {
      state.selectedTimeOfDay = action.payload;
    },
    
    setWeather: (state, action: PayloadAction<Weather>) => {
      state.selectedWeather = action.payload;
    },
    
    setDifficulty: (state, action: PayloadAction<DifficultyLevel>) => {
      state.difficulty = action.payload;
    },
    
    // Player progression
    advanceStory: (state) => {
      state.storyProgress += 1;
    },
    
    unlockAbility: (state, action: PayloadAction<string>) => {
      if (!state.unlockedAbilities.includes(action.payload)) {
        state.unlockedAbilities.push(action.payload);
      }
    },
    
    addExperience: (state, action: PayloadAction<number>) => {
      state.experience += action.payload;
      state.level = Math.floor(state.experience / 1000) + 1;
    },
    
    // Currency management
    addCurrency: (state, action: PayloadAction<number>) => {
      state.currency += action.payload;
    },
    
    spendCurrency: (state, action: PayloadAction<number>) => {
      state.currency = Math.max(0, state.currency - action.payload);
    },

    // Achievement system
    unlockAchievement: (state, action: PayloadAction<string>) => {
      // This would typically be handled by userSlice, but adding here for compatibility
      console.log('Achievement unlocked:', action.payload);
    },

    // Race ending
    endRace: (state) => {
      state.raceState.isRaceStarted = false;
      state.raceState.isRaceComplete = true;
    },

    // Asset management
    setUiAssets: (state, action: PayloadAction<any>) => {
      // Handle UI assets loading
      console.log('UI assets loaded:', action.payload);
    },

    // UI state
    setLoading: (state, action: PayloadAction<boolean>) => {
      state.loading = action.payload;
    },
    
    setError: (state, action: PayloadAction<string | null>) => {
      state.error = action.payload;
    },
  },
});

export const {
  // Navigation
  setCurrentScreen,
  
  // Game mode
  setGameMode,
  
  // Race actions
  startRace,
  updateRaceState,
  updateVehicleState,
  finishRace,
  pauseRace,
  resetRace,

  // Input actions
  setInputState,
  
  // Challenge mode
  startChallenge,
  completeChallenge,
  
  // Track actions
  setCurrentTrack,
  unlockTrack,
  
  // Environment settings
  setTimeOfDay,
  setWeather,
  setDifficulty,
  
  // Player progression
  advanceStory,
  unlockAbility,
  addExperience,
  
  // Currency management
  addCurrency,
  spendCurrency,

  // Achievement system
  unlockAchievement,

  // Race ending
  endRace,

  // Asset management
  setUiAssets,

  // UI state
  setLoading,
  setError,
} = gameSlice.actions;

export default gameSlice.reducer;
