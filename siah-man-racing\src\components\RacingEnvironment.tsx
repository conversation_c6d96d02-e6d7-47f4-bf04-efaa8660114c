import React, { useState, useEffect, useRef, useCallback, useMemo, memo, Suspense } from 'react';
import { Canvas, useFrame, useThree } from '@react-three/fiber';
import { Environment, useProgress, Html } from '@react-three/drei';
import { EffectComposer, Bloom, Noise, Vignette } from '@react-three/postprocessing';
import * as THREE from 'three';
import { GLTFLoader } from 'three/examples/jsm/loaders/GLTFLoader';
import { DRACOLoader } from 'three/examples/jsm/loaders/DRACOLoader';
import { RGBELoader } from 'three/examples/jsm/loaders/RGBELoader';

// Types
type TimeOfDay = 'dawn' | 'day' | 'dusk' | 'night';
type Weather = 'clear' | 'cloudy' | 'rain' | 'thunder' | 'fog' | 'snow';
type GameMode = 'race' | 'time-trial' | 'drift' | 'free-roam';

// Audio system implementation
class AudioSystem {
  private static instance: AudioSystem;
  private audioContext: AudioContext;
  private sounds: Map<string, AudioBuffer> = new Map();
  private playingSounds: Map<string, AudioBufferSourceNode> = new Map();
  private musicGain: GainNode;
  private sfxGain: GainNode;
  private masterGain: GainNode;
  private currentMusic: AudioBufferSourceNode | null = null;
  private musicVolume: number = 0.5;
  private sfxVolume: number = 0.7;
  private masterVolume: number = 1.0;

  private constructor() {
    this.audioContext = new (window.AudioContext || (window as any).webkitAudioContext)();
    this.masterGain = this.audioContext.createGain();
    this.musicGain = this.audioContext.createGain();
    this.sfxGain = this.audioContext.createGain();
    
    // Connect audio nodes
    this.musicGain.connect(this.masterGain);
    this.sfxGain.connect(this.masterGain);
    this.masterGain.connect(this.audioContext.destination);
    
    // Set initial volumes
    this.setVolume('master', this.masterVolume);
    this.setVolume('music', this.musicVolume);
    this.setVolume('sfx', this.sfxVolume);
  }

  public static getInstance(): AudioSystem {
    if (!AudioSystem.instance) {
      AudioSystem.instance = new AudioSystem();
    }
    return AudioSystem.instance;
  }

  public async loadSound(name: string, url: string): Promise<void> {
    try {
      const response = await fetch(url);
      const arrayBuffer = await response.arrayBuffer();
      const audioBuffer = await this.audioContext.decodeAudioData(arrayBuffer);
      this.sounds.set(name, audioBuffer);
    } catch (error) {
      console.error(`Failed to load sound ${name}:`, error);
      throw error;
    }
  }

  public play(name: string, options: { loop?: boolean; volume?: number } = {}): AudioBufferSourceNode | null {
    const audioBuffer = this.sounds.get(name);
    if (!audioBuffer) {
      console.warn(`Sound ${name} not found`);
      return null;
    }

    const source = this.audioContext.createBufferSource();
    source.buffer = audioBuffer;
    
    const gainNode = this.audioContext.createGain();
    gainNode.gain.value = (options.volume || 1.0) * this.sfxVolume * this.masterVolume;
    
    source.connect(gainNode);
    gainNode.connect(this.sfxGain);
    
    source.loop = options.loop || false;
    source.start();
    
    // Clean up after playback if not looping
    if (!options.loop) {
      source.onended = () => {
        this.playingSounds.delete(name);
      };
      this.playingSounds.set(name, source);
    }
    
    return source;
  }

  public playMusic(name: string, options: { loop?: boolean; volume?: number } = {}): void {
    this.stopMusic();
    
    const audioBuffer = this.sounds.get(name);
    if (!audioBuffer) {
      console.warn(`Music ${name} not found`);
      return;
    }
    
    const source = this.audioContext.createBufferSource();
    source.buffer = audioBuffer;
    
    const gainNode = this.audioContext.createGain();
    gainNode.gain.value = (options.volume || 1.0) * this.musicVolume * this.masterVolume;
    
    source.connect(gainNode);
    gainNode.connect(this.musicGain);
    
    source.loop = options.loop !== undefined ? options.loop : true;
    source.start();
    
    this.currentMusic = source;
  }

  public stop(name?: string): void {
    if (name) {
      const source = this.playingSounds.get(name);
      if (source) {
        source.stop();
        this.playingSounds.delete(name);
      }
    } else {
      // Stop all sounds and music
      this.playingSounds.forEach(source => source.stop());
      this.playingSounds.clear();
      this.stopMusic();
    }
  }

  public stopMusic(): void {
    if (this.currentMusic) {
      this.currentMusic.stop();
      this.currentMusic = null;
    }
  }

  public setVolume(type: 'master' | 'music' | 'sfx', volume: number): void {
    const normalizedVolume = Math.max(0, Math.min(1, volume));
    
    switch (type) {
      case 'master':
        this.masterVolume = normalizedVolume;
        this.masterGain.gain.value = this.masterVolume;
        // Update music and SFX volumes to reflect master volume change
        this.musicGain.gain.value = this.musicVolume * this.masterVolume;
        this.sfxGain.gain.value = this.sfxVolume * this.masterVolume;
        break;
      case 'music':
        this.musicVolume = normalizedVolume;
        this.musicGain.gain.value = this.musicVolume * this.masterVolume;
        break;
      case 'sfx':
        this.sfxVolume = normalizedVolume;
        this.sfxGain.gain.value = this.sfxVolume * this.masterVolume;
        break;
    }
  }

  public getVolume(type: 'master' | 'music' | 'sfx'): number {
    switch (type) {
      case 'master': return this.masterVolume;
      case 'music': return this.musicVolume;
      case 'sfx': return this.sfxVolume;
      default: return 1.0;
    }
  }

  public isPlaying(name: string): boolean {
    return this.playingSounds.has(name) || 
           (this.currentMusic && this.currentMusic.buffer === this.sounds.get(name));
  }
}

// Create a single instance of AudioSystem
const audioSystem = AudioSystem.getInstance();

// Types for audio system
type AudioTrack = {
  id: string;
  src: string;
  volume: number;
  loop: boolean;
};

type SoundEffect = AudioTrack & {
  name: string;
};

// Type definitions
interface NitroState {
  active: boolean;
  amount: number;
}

interface RacingEnvironmentProps {
  trackId: string;
  currentVehicleId: string;
  vehicleColor: {
    primary: string;
    secondary: string;
    detail: string;
  };
  equippedParts: any;
  timeOfDay: TimeOfDay;
  weather: Weather;
  showControls: boolean;
  gameMode: GameMode;
  onTrackLoaded: (trackData?: any) => void;
  onError?: (error: Error) => void;
}

interface SceneProps extends RacingEnvironmentProps {
  graphicsSettings: GraphicsSettings;
  onError: (error: Error) => void;
}

interface GraphicsSettings {
  shadows: boolean;
  particles: boolean;
  postProcessing: boolean;
  antialias: boolean;
  resolution: number;
}

// AudioTrack and SoundEffect types already defined above

// Loading screen component
function LoadingScreen() {
  const { progress } = useProgress();
  return (
    <Html center>
      <div style={{
        width: '100%',
        textAlign: 'center',
        background: 'rgba(0,0,0,0.8)',
        color: '#fff',
        padding: '20px',
        borderRadius: '8px',
        fontFamily: 'Arial, sans-serif'
      }}>
        <h2>Super Siah Man: Racing For Speed</h2>
        <div style={{ width: '300px', height: '20px', background: '#111', borderRadius: '10px', margin: '20px auto' }}>
          <div style={{
            width: `${progress}%`,
            height: '100%',
            background: 'linear-gradient(90deg, #ff0055, #ff3300)',
            borderRadius: '10px',
            transition: 'width 0.3s ease'
          }} />
        </div>
        <p>Loading... {progress.toFixed(0)}%</p>
      </div>
    </Html>
  );
}

// Game text component for 3D text display
const GameText = memo(({ text, position, fontSize = 0.2 }: { text: string, position: [number, number, number], fontSize?: number }) => {
  return (
    <Text
      position={position}
      rotation={[0, 0, 0]}
      fontSize={fontSize}
      color="white"
      anchorX="center"
      anchorY="middle"
      outlineWidth={0.01}
      outlineColor="black"
    >
      {text}
    </Text>
  );
});

// Memoized scene component to prevent unnecessary re-renders
const RacingEnvironment: React.FC<RacingEnvironmentProps> = ({
  trackId,
  currentVehicleId,
  vehicleColor,
  equippedParts,
  timeOfDay,
  weather,
  showControls,
  gameMode,
  onTrackLoaded,
  onError = (error) => console.error(error)
}) => {
  // Graphics settings based on quality
  const [graphicsQuality, setGraphicsQuality] = useState<'low' | 'medium' | 'high'>('medium');
  
  const graphicsSettings = useMemo<GraphicsSettings>(() => {
    switch (graphicsQuality) {
      case 'low':
        return {
          shadows: false,
          particles: false,
          postProcessing: false,
          antialias: false,
          resolution: 0.75
        };
      case 'high':
        return {
          shadows: true,
          particles: true,
          postProcessing: true,
          antialias: true,
          resolution: 1.5
        };
      case 'medium':
      default:
        return {
          shadows: true,
          particles: true,
          postProcessing: true,
          antialias: true,
          resolution: 1
        };
    }
  }, [graphicsQuality]);

  return (
    <Canvas
      shadows={graphicsSettings.shadows}
      camera={{ position: [0, 5, 15], fov: 50 }}
      gl={{ 
        antialias: graphicsSettings.antialias,
        powerPreference: 'high-performance'
      }}
      dpr={graphicsSettings.resolution}
    >
      <Suspense fallback={<LoadingScreen />}>
        <Scene 
          trackId={trackId}
          currentVehicleId={currentVehicleId}
          vehicleColor={vehicleColor}
          equippedParts={equippedParts}
          timeOfDay={timeOfDay}
          weather={weather}
          showControls={showControls}
          gameMode={gameMode}
          onTrackLoaded={onTrackLoaded}
          onError={onError}
          graphicsSettings={graphicsSettings}
        />
      </Suspense>
    </Canvas>
  );
};

// Internal Scene component for better organization

const Scene: React.FC<SceneProps> = ({
  trackId,
  currentVehicleId,
  vehicleColor,
  equippedParts,
  timeOfDay,
  weather,
  showControls,
  gameMode,
  onTrackLoaded,
  onError,
  graphicsSettings
}) => {
  // Race state
  const [raceStarted, setRaceStarted] = useState(false);
  const [raceTime, setRaceTime] = useState(0);
  const [position, setPosition] = useState(0);
  const [speed, setSpeed] = useState(0);
  const [nitroState, setNitroState] = useState<NitroState>({
    active: false,
    amount: 100
  });

  // Refs
  const audioSystem = useRef<AudioSystem | null>(null);
  const sceneRef = useRef<THREE.Scene>(null);
  const cameraRef = useRef<THREE.PerspectiveCamera>(null);
  const rendererRef = useRef<THREE.WebGLRenderer>(null);
  const frameRef = useRef<number>();
  const startTimeRef = useRef<number>(0);
  const lastTimeRef = useRef<number>(0);

  // Initialize audio system
  useEffect(() => {
    const audioSystemInstance = AudioSystem.getInstance();
    audioSystem.current = audioSystemInstance;
    
    const loadSoundEffects = async () => {
      try {
        // Engine sounds
        await audioSystemInstance.loadSoundEffect({
          id: 'engine_idle',
          name: 'Engine Idle',
          src: '/assets/Sounds/engine_idle.mp3',
          volume: 0.5,
          loop: true
        });
        
        await audioSystemInstance.loadSoundEffect({
          id: 'engine_rev',
          name: 'Engine Rev',
          src: '/assets/Sounds/engine_rev.mp3',
          volume: 0.6,
          loop: false
        });

        // Weather sounds
        if (weather === 'rainy' || weather === 'stormy') {
          await audioSystemInstance.loadSoundEffect({
            id: 'rain_ambient',
            name: 'Rain Ambient',
            src: '/assets/Sounds/rain_ambient.mp3',
            volume: 0.4,
            loop: true
          });
        }
      } catch (error) {
        console.error('Error loading sound effects:', error);
        onError(new Error('Failed to load sound effects'));
      }
    };

    const loadMusicTracks = async () => {
      try {
        const musicTracks = {
          race: '/assets/Music/race_music.mp3',
          menu: '/assets/Menu/menu_music.mp3',
          results: '/assets/Menu/results_music.mp3'
        };

        for (const [id, src] of Object.entries(musicTracks)) {
          await audioSystemInstance.loadMusic({
            id,
            src,
            volume: 0.5,
            loop: true
          });
        }
      } catch (error) {
        console.error('Error loading music:', error);
        onError(new Error('Failed to load music tracks'));
      }
    };

    // Initialize audio
    Promise.all([loadSoundEffects(), loadMusicTracks()])
      .then(() => {
        console.log('Audio system initialized');
      })
      .catch((error) => {
        console.error('Error initializing audio:', error);
        onError(error instanceof Error ? error : new Error('Audio initialization failed'));
      });

    // Cleanup function
    return () => {
      if (audioSystemInstance) {
        audioSystemInstance.stopAll();
      }
      if (frameRef.current) {
        cancelAnimationFrame(frameRef.current);
      }
    };
  }, [weather, onError]);

  // Game loop
  useFrame((state, delta) => {
    if (!raceStarted) return;
    
    // Update race time
    setRaceTime(prevTime => prevTime + delta);
    
    // Update vehicle position based on speed
    setPosition(prevPos => {
      const newPos = prevPos + speed * delta;
      return newPos;
    });
    
    // Update audio based on speed
    const engineVolume = Math.min(0.5 + speed / 50, 1);
    audioSystem.setVolume('sfx', engineVolume);
    
    // Play engine sound when moving
    if (speed > 0) {
      if (!audioSystem.isPlaying('engine')) {
        audioSystem.play('engine', { loop: true });
      }
      
      // Update engine pitch based on speed
      const engineSource = audioSystem.getSource('engine');
      if (engineSource) {
        engineSource.playbackRate.value = 0.5 + (speed / 200);
      }
    } else if (speed === 0) {
      audioSystem.stop('engine');
    }
  });
  
  // Handle race start/end
  useEffect(() => {
    if (gameMode === 'race' && !raceStarted) {
      // Play countdown sound
      audioSystem.play('countdown');
      
      // Start race after countdown
      const timer = setTimeout(() => {
        setRaceStarted(true);
        startTimeRef.current = performance.now();
        
        // Play race start sound
        audioSystem.play('engine');
        
        // Start race music
        audioSystem.playMusic('race_music', { loop: true });
      }, 3000); // 3 second countdown
      
      return () => clearTimeout(timer);
    }
    
    return () => {
      // Cleanup race state
      if (raceStarted) {
        audioSystem.stopMusic();
        audioSystem.stop('engine');
      }
    };
  }, [gameMode, raceStarted]);
  
  // Handle nitro activation
  const activateNitro = useCallback(() => {
    if (nitroState.amount <= 0) return;
    
    setNitroState(prev => ({
      active: true,
      amount: Math.max(0, prev.amount - 0.5)
    }));
    
    // Play nitro sound effect with higher volume
    audioSystem.play('nitro', { volume: 1.2 });
    
    // Schedule nitro deactivation
    const timer = setTimeout(() => {
      setNitroState(prev => ({
        ...prev,
        active: false
      }));
      
      // Play nitro end sound effect
      audioSystem.play('nitro_end', { volume: 0.8 });
    }, 2000);
    
    return () => clearTimeout(timer);
  }, [nitroState.amount]);
  
  // Render the scene
  return (
    <>
      {/* Environment */}
      <Environment preset={timeOfDay} background />
      
      {/* Lighting */}
      <ambientLight intensity={0.5} />
      <directionalLight
        position={[10, 10, 5]}
        intensity={1}
        castShadow={graphicsSettings.shadows}
        shadow-mapSize-width={2048}
        shadow-mapSize-height={2048}
      />
      
      {/* Track */}
      <TrackLoader 
        trackId={trackId} 
        onLoaded={onTrackLoaded} 
        onError={onError} 
      />
      
      {/* Vehicle */}
      <VehicleModel 
        vehicleId={currentVehicleId}
        color={vehicleColor}
        equippedParts={equippedParts}
        position={[0, 0.5, 0]}
        rotation={[0, Math.PI, 0]}
      />
      
      {/* Weather Effects */}
      {weather === 'rainy' && <RainEffect intensity={1} />}
      {weather === 'snowy' && <SnowEffect intensity={0.5} />}
      
      {/* Post-processing */}
      {graphicsSettings.postProcessing && (
        <EffectComposer>
          <Bloom
            intensity={1.0}
            luminanceThreshold={0.1}
            luminanceSmoothing={0.9}
            height={300}
          />
          <Noise opacity={0.02} />
          <Vignette eskil={false} offset={0.1} darkness={1.0} />
        </EffectComposer>
      )}
      
      {/* Debug UI */}
      {process.env.NODE_ENV === 'development' && (
        <Html position={[0, 2, 0]}>
          <div style={{
            background: 'rgba(0,0,0,0.7)',
            color: 'white',
            padding: '10px',
            borderRadius: '5px',
            fontSize: '12px',
            width: '200px'
          }}>
            <div>Speed: {speed.toFixed(1)} km/h</div>
            <div>Position: {position.toFixed(1)} m</div>
            <div>Time: {raceTime.toFixed(1)} s</div>
            <div>Nitro: {nitroState.amount.toFixed(0)}%</div>
          </div>
        </Html>
      )}
    </>
  );
};

// Weather effect components
const RainEffect = memo(({ intensity = 1 }) => {
  const particles = useMemo(() => {
    const particleCount = 5000;
    const positions = new Float32Array(particleCount * 3);
    
    for (let i = 0; i < particleCount; i++) {
      const i3 = i * 3;
      positions[i3] = (Math.random() - 0.5) * 100;
      positions[i3 + 1] = Math.random() * 50 + 10;
      positions[i3 + 2] = (Math.random() - 0.5) * 100;
    }
    
    return positions;
  }, []);
  
  const particlesRef = useRef<THREE.Points>(null);
  
  useFrame(() => {
    if (particlesRef.current) {
      particlesRef.current.rotation.y += 0.001;
    }
  });
  
  return (
    <points ref={particlesRef}>
      <bufferGeometry>
        <bufferAttribute
          attach="attributes-position"
          count={particles.length / 3}
          array={particles}
          itemSize={3}
        />
      </bufferGeometry>
      <pointsMaterial
        size={0.1}
        color="#ffffff"
        transparent
        opacity={0.6}
        sizeAttenuation
      />
    </points>
  );
});

const SnowEffect = memo(({ intensity = 0.5 }) => {
  const particles = useMemo(() => {
    const particleCount = 3000;
    const positions = new Float32Array(particleCount * 3);
    
    for (let i = 0; i < particleCount; i++) {
      const i3 = i * 3;
      positions[i3] = (Math.random() - 0.5) * 100;
      positions[i3 + 1] = Math.random() * 50;
      positions[i3 + 2] = (Math.random() - 0.5) * 100;
    }
    
    return positions;
  }, []);
  
  const particlesRef = useRef<THREE.Points>(null);
  
  useFrame(() => {
    if (particlesRef.current) {
      particlesRef.current.rotation.y += 0.001;
    }
  });
  
  return (
    <points ref={particlesRef}>
      <bufferGeometry>
        <bufferAttribute
          attach="attributes-position"
          count={particles.length / 3}
          array={particles}
          itemSize={3}
        />
      </bufferGeometry>
      <pointsMaterial
        size={0.15}
        color="#ffffff"
        transparent
        opacity={0.8}
        sizeAttenuation
      />
    </points>
  );
});

// Race countdown and start logic
const startRaceCountdown = () => {
  audioSystem.play('countdown');
  
  // Start race after a delay
  return setTimeout(() => {
        setRaceStarted(true);
        // Start at random position between 5-8
        setPosition(Math.floor(Math.random() * 4) + 5);
        
        // Play engine rev sound at race start
        audioSystem.current?.playSoundEffect('engine_rev');
        
        // Play thunder sound if rainy weather
        if (weather === 'rainy' || weather === 'cloudy') {
          // Random thunder sounds throughout the race
          const thunderInterval = setInterval(() => {
            if (Math.random() < 0.3) { // 30% chance every interval
              audioSystem.current?.playSoundEffect('thunder');
            }
          }, 20000); // Every 20 seconds check for thunder
          
          return () => clearInterval(thunderInterval);
        }
      }, 3000);
      
      return () => clearTimeout(timer);
    };
  }, [weather, raceStarted]);
  
  // Race logic when started
  useEffect(() => {
    if (raceStarted) {
      const interval = setInterval(() => {
        // Gradually increase speed to a random maximum
        setSpeed(prev => {
          const maxSpeed = 120 + Math.random() * 30;
          const newSpeed = prev < maxSpeed ? prev + 1 : prev;
          
          // Update audio system race intensity based on speed
          const normalizedSpeed = Math.min(newSpeed / 150, 1);
          audioSystem.current?.setRaceIntensity(normalizedSpeed);
          
          // Play engine rev sound when accelerating significantly
          if (Math.floor(prev / 20) < Math.floor(newSpeed / 20)) {
            audioSystem.current?.playSoundEffect('engine_rev');
          }
          
          // Switch to intense music at high speeds
          if (newSpeed > 100 && prev <= 100) {
            audioSystem.current?.playMusic('race_intense');
          } else if (newSpeed <= 100 && prev > 100) {
            audioSystem.current?.playMusic('race_normal');
          }
          
          return newSpeed;
        });
        
        // Update position based on speed (simplified racing simulation)
        setPosition(prev => {
          // Randomly improve position based on speed
          if (Math.random() < 0.05 && prev > 1 && speed > 100) {
            return prev - 1;
          }
          return prev;
        });
        
        // Update race time
        setRaceTime(prev => {
          const [mins, rest] = prev.split(':');
          const [secs, ms] = rest.split('.');
          
          let newMs = parseInt(ms) + 33;
          let newSecs = parseInt(secs);
          let newMins = parseInt(mins);
          
          if (newMs >= 1000) {
            newMs = 0;
            newSecs++;
          }
          
          if (newSecs >= 60) {
            newSecs = 0;
            newMins++;
          }
          
          return `${newMins.toString().padStart(2, '0')}:${newSecs.toString().padStart(2, '0')}.${newMs.toString().padStart(3, '0')}`;
        });
      }, 33); // ~30fps update
      
      await audioSystem.current?.loadSoundEffect({
        id: 'thunder',
        name: 'Thunder',
        src: '/assets/Sounds/thunder.mp3',
        volume: 0.7,
        loop: false
      });
      
      // Race sounds
      await audioSystem.current?.loadSoundEffect({
        id: 'countdown',
        name: 'Countdown',
        src: '/assets/Sounds/countdown.mp3',
        volume: 0.8,
        loop: false
      });
      
      await audioSystem.current?.loadSoundEffect({
        id: 'nitro',
        name: 'Nitro',
        src: '/assets/Sounds/nitro.mp3',
        volume: 0.7,
        loop: false
      });
      
      // Start engine idle sound
      audioSystem.current?.playSoundEffect('engine_idle');
      
      // Play weather sounds if needed
      if (weather === 'rainy') {
  // Audio system initialization
useEffect(() => {
  const initAudio = async () => {
    try {
      // Load audio files
      await Promise.all([
        audioSystem.loadSound('engine', '/sounds/engine.mp3'),
        audioSystem.loadSound('skid', '/sounds/skid.mp3'),
        audioSystem.loadSound('crash', '/sounds/crash.mp3'),
        audioSystem.loadSound('nitro', '/sounds/nitro.mp3'),
        audioSystem.loadSound('nitro_end', '/sounds/nitro_end.mp3'),
        audioSystem.loadSound('countdown', '/sounds/countdown.mp3'),
        audioSystem.loadSound('go', '/sounds/go.mp3'),
        audioSystem.loadSound('lap', '/sounds/lap.mp3'),
        audioSystem.loadSound('finish', '/sounds/finish.mp3'),
        audioSystem.loadSound('menu_select', '/sounds/menu_select.mp3'),
        audioSystem.loadSound('menu_navigate', '/sounds/menu_navigate.mp3'),
        audioSystem.loadSound('race_music', '/music/race_music.mp3'),
        audioSystem.loadSound('menu_music', '/music/menu_music.mp3')
          loop: false
        }),
        audioSystem.current?.loadSoundEffect({
          id: 'skid',
          name: 'Skid',
          src: '/assets/Sounds/skid.mp3',
          volume: 0.7,
          loop: false
        }),
        audioSystem.current?.loadSoundEffect({
          id: 'crash',
          name: 'Crash',
          src: '/assets/Sounds/crash.mp3',
          volume: 0.7,
          loop: false
        }),
        audioSystem.current?.loadSoundEffect({
          id: 'nitro',
          name: 'Nitro',
          src: '/assets/Sounds/nitro.mp3',
          volume: 0.7,
          loop: false
        }),
        audioSystem.current?.loadSoundEffect({
          id: 'nitro_end',
          name: 'Nitro End',
          src: '/assets/Sounds/nitro_end.mp3',
          volume: 0.7,
          loop: false
        }),
        audioSystem.current?.loadSoundEffect({
          id: 'countdown',
          name: 'Countdown',
          src: '/assets/Sounds/countdown.mp3',
          volume: 0.8,
          loop: false
        }),
        audioSystem.current?.loadSoundEffect({
          id: 'go',
          name: 'Go',
          src: '/assets/Sounds/go.mp3',
          volume: 0.8,
          loop: false
        }),
        audioSystem.current?.loadSoundEffect({
          id: 'lap',
          name: 'Lap',
          src: '/assets/Sounds/lap.mp3',
          volume: 0.8,
          loop: false
        }),
        audioSystem.current?.loadSoundEffect({
          id: 'finish',
          name: 'Finish',
          src: '/assets/Sounds/finish.mp3',
          volume: 0.8,
          loop: false
        }),
        audioSystem.current?.loadSoundEffect({
          id: 'menu_select',
          name: 'Menu Select',
          src: '/assets/Sounds/menu_select.mp3',
          volume: 0.8,
          loop: false
        }),
        audioSystem.current?.loadSoundEffect({
          id: 'menu_navigate',
          name: 'Menu Navigate',
          src: '/assets/Sounds/menu_navigate.mp3',
          volume: 0.8,
          loop: false
        }),
        audioSystem.current?.loadMusicTrack({
          id: 'race_music',
          name: 'Race Music',
          src: '/assets/Sounds/race_music.mp3',
          intensity: 'medium',
          loop: true,
          volume: 0.6,
          category: 'race'
        }),
        audioSystem.current?.loadMusicTrack({
          id: 'menu_music',
          name: 'Menu Music',
          src: '/assets/Sounds/menu_music.mp3',
          intensity: 'low',
          loop: true,
          volume: 0.6,
          category: 'menu'
        })
      ]);
      
      // Set initial volumes
      audioSystem.current?.setVolume('music', 0.7);
      audioSystem.current?.setVolume('sfx', 0.8);
      
      console.log('Audio system initialized successfully');
    } catch (error) {
      console.error('Error loading audio files:', error);
    }
  };

  initAudio();

  return () => {
    // Cleanup audio
    audioSystem.current?.stopAll();
    console.log('Audio system cleaned up');
  };
  // Update race time
  useEffect(() => {
  if (!raceStarted && gameMode !== 'freeRoam' as GameMode) {
    // Play countdown sound
    audioSystem.current?.playSoundEffect('countdown');
    
    // Start race after a delay
    const timer = setTimeout(() => {
      setRaceStarted(true);
      // Start at random position between 5-8
      setPosition(Math.floor(Math.random() * 4) + 5);
      
      // Play engine rev sound at race start
      audioSystem.current?.playSoundEffect('engine_rev');
      
      // Play thunder sound if rainy weather
      if (weather === 'rainy' || weather === 'cloudy') {
        // Random thunder sounds throughout the race
        const thunderInterval = setInterval(() => {
          if (Math.random() < 0.3) { // 30% chance every interval
            audioSystem.current?.playSoundEffect('thunder');
          }
        }, 20000); // Every 20 seconds check for thunder
        
        return () => clearInterval(thunderInterval);
      }
    }, 3000);
    
    return () => clearTimeout(timer);
  }
  
  if (raceStarted) {
    const interval = setInterval(() => {
      // Gradually increase speed to a random maximum
      setSpeed(prev => {
        const maxSpeed = 120 + Math.random() * 30;
        const newSpeed = prev < maxSpeed ? prev + 1 : prev;
        
        // Update audio system race intensity based on speed
        const normalizedSpeed = Math.min(newSpeed / 150, 1);
        audioSystem.current?.setRaceIntensity(normalizedSpeed);
        
        // Play engine rev sound when accelerating significantly
        if (Math.floor(prev / 20) < Math.floor(newSpeed / 20)) {
          audioSystem.current?.playSoundEffect('engine_rev');
        }
        
        // Switch to intense music at high speeds
        if (newSpeed > 100 && prev <= 100) {
          audioSystem.current?.playMusic('race_intense');
        } else if (newSpeed <= 100 && prev > 100) {
          audioSystem.current?.playMusic('race_normal');
        }
        
        return newSpeed;
      });
      
      // Update position based on speed (simplified racing simulation)
      setPosition(prev => {
        // Randomly improve position based on speed
        if (Math.random() < 0.05 && prev > 1 && speed > 100) {
          return prev - 1;
        }
        return prev;
      });
      
      // Update race time
      setRaceTime(prev => {
        const [mins, rest] = prev.split(':');
        const [secs, ms] = rest.split('.');
        
        let newMs = parseInt(ms) + 33;
        let newSecs = parseInt(secs);
        let newMins = parseInt(mins);
        
        if (newMs >= 1000) {
          newMs = 0;
          newSecs++;
        }
        
        if (newSecs >= 60) {
          newSecs = 0;
          newMins++;
        }
        
        return `${newMins.toString().padStart(2, '0')}:${newSecs.toString().padStart(2, '0')}.${newMs.toString().padStart(3, '0')}`;
      });
    }, 33); // ~30fps update
    
    return () => clearInterval(interval);
  }
}, [raceStarted, gameMode, speed]);

  // Sky configuration based on time of day
  const getSkyConfig = () => {
    const config = {
      turbidity: 1,
      rayleigh: 1,
      inclination: 0.5,
      azimuth: 0.25
    };
    
    switch(timeOfDay) {
      case 'evening':
        return { 
          ...config,
          sunPosition: new THREE.Vector3(0, 0.3, -1),
          turbidity: 10, 
          rayleigh: 3, 
          inclination: 0.1, 
          azimuth: 0.25 
        };
      case 'night':
        return { 
          ...config,
          sunPosition: new THREE.Vector3(0, -0.5, -1),
          turbidity: 2, 
          rayleigh: 1, 
          inclination: -0.5, 
          azimuth: 0.25 
        };
      case 'morning':
        return { 
          ...config,
          sunPosition: new THREE.Vector3(0, 0.1, 1),
          turbidity: 8, 
          rayleigh: 2.5, 
          inclination: 0.05, 
          azimuth: 0.75 
        };
      case 'noon':
      default:
        return { 
          ...config,
          sunPosition: new THREE.Vector3(0, 1, 0),
          turbidity: 10, 
          rayleigh: 0.5, 
          inclination: 0.5, 
          azimuth: 0.25 
        };
    }
  };
  
  const skyConfig = getSkyConfig();

// Configure fog based on weather
const getFogConfig = () => {
  switch(weather) {
    case 'foggy':
      return { color: '#aabbcc', near: 10, far: 50 };
    case 'rainy':
      return { color: '#8899aa', near: 20, far: 100 };
    case 'snowy':
      return { color: '#ffffff', near: 15, far: 80 };
    case 'cloudy':
      return { color: '#ccccdd', near: 30, far: 150 };
    default:
      return null;
  }
};

const fogConfig = getFogConfig();

return (
  <>
    {/* Controls */}
    {showControls && (
      <OrbitControls 
        enablePan={false}
        minDistance={5}
        maxDistance={50}
        maxPolarAngle={Math.PI / 2 - 0.1}
      />
    )}
    
    {/* Lighting */}
    <ambientLight intensity={timeOfDay === 'night' ? 0.2 : 0.5} />
    <directionalLight 
      position={skyConfig.sunPosition} 
      intensity={timeOfDay === 'night' ? 0.3 : timeOfDay === 'evening' || timeOfDay === 'morning' ? 0.8 : 1.2} 
      color={timeOfDay === 'evening' ? '#ff9e57' : timeOfDay === 'morning' ? '#ffd4a3' : timeOfDay === 'night' ? '#a0c0ff' : '#ffffff'}
      castShadow 
      shadow-mapSize={[4096, 4096]}
      shadow-camera-far={100}
      shadow-camera-left={-30}
      shadow-camera-right={30}
      shadow-camera-top={30}
      shadow-camera-bottom={-30}
      shadow-bias={-0.0001}
    />
    
    {/* Secondary fill light for better scene illumination */}
    <directionalLight 
      position={[-5, 3, -5]} 
      intensity={timeOfDay === 'night' ? 0.1 : 0.3} 
      color={timeOfDay === 'evening' ? '#5e8bff' : timeOfDay === 'morning' ? '#a0c0ff' : '#a0a0a0'}
    />
    
    {/* Sky */}
    <Sky {...skyConfig} />
    
    {/* Fog for weather effects */}
    {fogConfig && (
      <fog 
        attach="fog" 
        args={[fogConfig.color, fogConfig.near, fogConfig.far]}
      />
    )}
    
    {/* Ground plane */}
    <mesh rotation={[-Math.PI / 2, 0, 0]} position={[0, -0.1, 0]} receiveShadow>
      <planeGeometry args={[1000, 1000]} />
      <shadowMaterial opacity={0.5} />
    </mesh>
    
    {/* Track and vehicle models */}
    <Suspense fallback={<LoadingScreen />}>
      <TrackLoader trackId={trackId} onLoad={onTrackLoaded} />
      <VehicleModel 
        vehicleId={currentVehicleId}
        color={vehicleColor}
        parts={equippedParts}
        position={[0, 0, 0]}
        rotation={[0, Math.PI, 0]}
        scale={1}
        animate={true}
      />
      
      {/* Add opponent vehicles if in race mode */}
      {gameMode === 'race' && raceStarted && (
        <>
          <VehicleModel 
            vehicleId="sports_car"
            color={{ primary: '#ff0000', secondary: '#000000', detail: '#ffffff' }}
            parts={{
              body: null,
              wheels: null,
              spoiler: 'spoiler1',
              engine: null,
              suspension: null,
              tires: null,
              nitro: null,
              headlights: null,
              exhaust: null,
              decals: []
            }}
            position={[3, 0, -10]}
            rotation={[0, Math.PI, 0]}
            scale={1}
            animate={true}
          />
          <VehicleModel 
            vehicleId="muscle_car"
            color={{ primary: '#0000ff', secondary: '#ffffff', detail: '#ffff00' }}
            parts={{
              body: null,
              wheels: null,
              spoiler: null,
              engine: null,
              suspension: null,
              tires: null,
              nitro: null,
              headlights: null,
              exhaust: null,
              decals: []
            }}
            position={[-3, 0, -15]}
            rotation={[0, Math.PI, 0]}
            scale={1}
            animate={true}
          />
        </>
      )}
    </Suspense>
    
    {/* 3D Text for countdown */}
    {gameMode !== 'freeRoam' && !raceStarted && (
      <GameText 
        text="GET READY!" 
        position={[0, 1, -5]} 
        fontSize={1} 
      />
    )}
    
    {/* HTML overlay for 2D UI */}
    {gameMode !== 'free_roam' && raceStarted && (
      <Html fullscreen>
        <div style={{ position: 'absolute', top: 0, left: 0, width: '100%', height: '100%', pointerEvents: 'none' }}>
          <RaceUI 
            isVisible={true}
            currentSpeed={speed}
            nitroAmount={nitroState.amount}
          />
        </div>
      </Html>
    )}
    
    {/* Weather effects */}
    {weather === 'rainy' && <RainEffect />}
    {weather === 'snowy' && <SnowEffect />}
    
    {/* Post-processing effects with adaptive quality based on device capabilities */}
    {(() => {
      const quality = graphicsQuality as 'low' | 'medium' | 'high';
      if (quality === 'low') return null;
      
      return (
        <EffectComposer>
          {/* Depth of field effect - only on high settings */}
          {quality === 'high' && (
            <DepthOfField 
              focusDistance={0.01} 
              focalLength={0.2} 
              bokehScale={3} 
              height={480} 
            />
          )}
          
          {/* Bloom effect - adjusted based on quality settings */}
          <Bloom 
            luminanceThreshold={0.2} 
            luminanceSmoothing={0.9} 
            height={quality === 'high' ? 300 : 150} 
            intensity={timeOfDay === 'night' ? 
              (quality === 'high' ? 1.5 : 1.0) : 
              (quality === 'high' ? 0.8 : 0.5)}
          />
          
          {/* Subtle film grain - only on high quality */}
          {quality === 'high' && (
            <Noise opacity={0.02} />
          )}
          
          {/* Vignette - on both medium and high */}
          <Vignette 
            eskil={false} 
            offset={0.1} 
            darkness={timeOfDay === 'night' ? 
              (quality === 'high' ? 1.2 : 0.9) : 
              (quality === 'high' ? 0.8 : 0.6)} 
          />
        </EffectComposer>
      );
    })()}
  </>
);

const RacingEnvironment: React.FC<RacingEnvironmentProps> = ({ 
  trackId, 
  showControls = true,
  timeOfDay = 'noon',
  weather = 'clear' as Weather,
  gameMode = 'race' as GameMode
}) => {
  // Performance optimization state
  type GraphicsQuality = 'low' | 'medium' | 'high';
  
  interface GraphicsSettings {
    shadows: boolean;
    particles: number;
    lodDistance: number;
  }
  
  const graphicsSettings: Record<GraphicsQuality, GraphicsSettings> = {
    low: { shadows: false, particles: 10, lodDistance: 50 },
    medium: { shadows: true, particles: 30, lodDistance: 100 },
    high: { shadows: true, particles: 100, lodDistance: 200 }
  };
  
  const [graphicsQuality, setGraphicsQuality] = useState<GraphicsQuality>('medium');
  const settings = graphicsSettings[graphicsQuality];
  
  // Detect device capabilities on mount and set appropriate graphics quality
  useEffect(() => {
    const detectDeviceCapabilities = () => {
      // Check for mobile device
      const isMobile = /Android|webOS|iPhone|iPad|iPod|BlackBerry|IEMobile|Opera Mini/i.test(navigator.userAgent);
      
      // Check for GPU capabilities (simplified)
      const canvas = document.createElement('canvas');
      const gl = canvas.getContext('webgl');
      
      if (!gl) {
        // WebGL not supported, use lowest settings
        return 'low';
      }
      
      const debugInfo = gl.getExtension('WEBGL_debug_renderer_info');
      let gpuVendor = '';
      
      if (debugInfo) {
        gpuVendor = gl.getParameter(debugInfo.UNMASKED_RENDERER_WEBGL) || '';
      }
      
      // Check for high-end GPU keywords
      const isHighEndGPU = /(NVIDIA|RTX|GTX|Radeon|AMD|Intel Iris)/i.test(gpuVendor);
      
      // Set quality based on device and GPU
      if (isMobile) {
        return 'low';
      } else if (isHighEndGPU) {
        return 'high';
      } else {
        return 'medium';
      }
    };
    
    setGraphicsQuality(detectDeviceCapabilities());
  }, []);
  // Get race state from Redux
  const raceState = useSelector((state: RootState) => state.game.activeRace);
  const selectedWeather = useSelector((state: RootState) => state.game.selectedWeather || weather);
  const selectedTimeOfDay = useSelector((state: RootState) => state.game.selectedTimeOfDay || timeOfDay);
  // Track loading state
  const [trackLoaded, setTrackLoaded] = useState(false);
  
  // Handle track loaded
  const handleTrackLoaded = useCallback(() => {
    setTrackLoaded(true);
  }, []);
  // Get vehicle data from Redux store
  const currentVehicleId = useSelector((state: RootState) => state.vehicle.currentVehicleId);
  const vehicleColor = useSelector((state: RootState) => {
    if (!currentVehicleId) {
      return { primary: '#ff0000', secondary: '#000000', detail: '#ffffff' };
    }
    const colorId = state.vehicle.selectedColors[currentVehicleId]?.colorId;
    return state.vehicle.availableColors.find(c => c.id === colorId) || 
      state.vehicle.availableColors[0] || 
      { primary: '#ff0000', secondary: '#000000', detail: '#ffffff' };
  });
  
  const defaultEquippedParts = {
    body: null,
    wheels: null,
    spoiler: null,
    engine: null,
    suspension: null,
    tires: null,
    nitro: null,
    headlights: null,
    exhaust: null,
    decals: []
  } as const;
  
  const equippedParts = useSelector((state: RootState) => 
    (currentVehicleId ? state.vehicle.equippedParts[currentVehicleId] : null) || defaultEquippedParts
  );
  
  // Fallback if no vehicle is selected
  if (!currentVehicleId) {
    console.warn('No vehicle selected, using default vehicle');
  }

  return (
    <Canvas 
      shadows={settings.shadows} 
      dpr={[1, 2]} // Adaptive DPR for better performance on high-res displays
      gl={{ 
        antialias: true,
        powerPreference: 'high-performance',
        depth: true,
        stencil: false,
        alpha: false,
        precision: 'highp', // High precision rendering
        logarithmicDepthBuffer: true // Better depth handling for large scenes
      }}
      camera={{ position: [0, 5, 10], fov: 60, near: 0.1, far: 1000 }}
      frameloop="demand" // Render only when needed for better performance
    >
      <Scene 
        trackId={trackId}
        currentVehicleId={currentVehicleId}
        vehicleColor={vehicleColor}
        equippedParts={equippedParts}
        timeOfDay={selectedTimeOfDay}
        weather={selectedWeather}
        showControls={showControls}
        gameMode={gameMode}
        onTrackLoaded={handleTrackLoaded}
      />
    </Canvas>
  );
};

// Rain particle effect component - memoized to prevent re-renders
const RainEffect = memo(() => {
  // Create rain particles positions
  const rainCount = 8000; // Increased particle count for more dense rain
  const rainPositions = new Float32Array(rainCount * 3);
  const rainVelocities = new Float32Array(rainCount * 3);
  
  // Reference for animation
  const positionsRef = useRef<THREE.BufferAttribute | null>(null);
  
  // Initialize rain positions and velocities
  for (let i = 0; i < rainCount; i++) {
    const i3 = i * 3;
    // Distribute rain in a wider area
    rainPositions[i3] = Math.random() * 200 - 100;     // x
    rainPositions[i3 + 1] = Math.random() * 80;        // y
    rainPositions[i3 + 2] = Math.random() * 200 - 100; // z
    
    // Different velocities for more natural effect
    rainVelocities[i3] = (Math.random() - 0.5) * 0.2;  // slight x drift
    rainVelocities[i3 + 1] = -2 - Math.random() * 3;   // y falling speed
    rainVelocities[i3 + 2] = (Math.random() - 0.5) * 0.2;  // slight z drift
  }
  
  // Nitro effect handler
  useEffect(() => {
    if (nitroState.active && nitroState.amount > 0) {
      // Play nitro sound effect
      audioSystem.current?.playSound('nitro_start');
      const interval = setInterval(() => {
        setNitroState((prev: NitroState) => {
          const newAmount = prev.amount - 0.5;
          if (newAmount <= 0) {
            return { ...prev, active: false, amount: 0 };
          }
          return { ...prev, amount: newAmount };
        });
      }, 100);

      return () => clearInterval(interval);
    } else if (nitroState.active && nitroState.amount <= 0) {
      setNitroState((prev: NitroState) => ({ ...prev, active: false }));
    }
  }, [nitroState.active, nitroState.amount]);
  
  // Function to activate nitro
  const activateNitro = useCallback(() => {
    if (nitroState.amount > 0 && !nitroState.active) {
      setNitroState((prev: NitroState) => ({ ...prev, active: true }));
    }
  }, [nitroState.amount, nitroState.active]);
  
  // Animation loop for rain
  useFrame(() => {
    if (!positionsRef.current) return;
    
    const positions = positionsRef.current.array as Float32Array;
    
    for (let i = 0; i < rainCount; i++) {
      const i3 = i * 3;
      
      // Update positions based on velocities
      positions[i3] += rainVelocities[i3];
      positions[i3 + 1] += rainVelocities[i3 + 1];
      positions[i3 + 2] += rainVelocities[i3 + 2];
      
      // Reset particles that fall below ground
      if (positions[i3 + 1] < -5) {
        positions[i3] = Math.random() * 200 - 100;
        positions[i3 + 1] = 80;
        positions[i3 + 2] = Math.random() * 200 - 100;
      }
    }
    
    positionsRef.current.needsUpdate = true;
  });
  
  return (
    <points frustumCulled={false}>
      <bufferGeometry>
        <bufferAttribute
          ref={positionsRef}
          attach="attributes-position"
          count={rainCount}
          array={rainPositions}
          itemSize={3}
        />
      </bufferGeometry>
      <pointsMaterial 
        size={0.15} 
        color="#aaddff" 
        transparent 
        opacity={0.7} 
        sizeAttenuation 
        depthWrite={false}
      />
    </points>
  );
});

// Snow particle effect component - memoized to prevent re-renders
const SnowEffect = memo(() => {
  // Create snow particles positions
  const snowCount = 5000; // Increased particle count for more dense snow
  const snowPositions = new Float32Array(snowCount * 3);
  const snowVelocities = new Float32Array(snowCount * 3);
  const snowSizes = new Float32Array(snowCount);
  
  // Reference for animation
  const positionsRef = useRef<THREE.BufferAttribute | null>(null);
  const sizesRef = useRef<THREE.BufferAttribute | null>(null);
  
  // Initialize snow positions, velocities, and sizes
  for (let i = 0; i < snowCount; i++) {
    const i3 = i * 3;
    // Distribute snow in a wider area
    snowPositions[i3] = Math.random() * 200 - 100;     // x
    snowPositions[i3 + 1] = Math.random() * 80;        // y
    snowPositions[i3 + 2] = Math.random() * 200 - 100; // z
    
    // Different velocities for more natural effect - snow falls slower and drifts more
    snowVelocities[i3] = (Math.random() - 0.5) * 0.3;      // x drift
    snowVelocities[i3 + 1] = -0.5 - Math.random() * 1.5;   // y falling speed
    snowVelocities[i3 + 2] = (Math.random() - 0.5) * 0.3;  // z drift
    
    // Varied sizes for more natural look
    snowSizes[i] = 0.2 + Math.random() * 0.6;
  }
  
  // Animation loop for snow
  useFrame((state) => {
    if (!positionsRef.current || !sizesRef.current) return;
    
    const positions = positionsRef.current.array as Float32Array;
    const sizes = sizesRef.current.array as Float32Array;
    const time = state.clock.getElapsedTime();
    
    for (let i = 0; i < snowCount; i++) {
      const i3 = i * 3;
      
      // Add some gentle swaying motion based on time
      const swayX = Math.sin(time * 0.1 + i * 0.1) * 0.05;
      const swayZ = Math.cos(time * 0.1 + i * 0.1) * 0.05;
      
      // Update positions based on velocities and sway
      positions[i3] += snowVelocities[i3] + swayX;
      positions[i3 + 1] += snowVelocities[i3 + 1];
      positions[i3 + 2] += snowVelocities[i3 + 2] + swayZ;
      
      // Make size slightly pulse for twinkling effect
      sizes[i] = (snowSizes[i] * (0.9 + Math.sin(time * 0.5 + i) * 0.1));
      
      // Reset particles that fall below ground
      if (positions[i3 + 1] < -5) {
        positions[i3] = Math.random() * 200 - 100;
        positions[i3 + 1] = 80;
        positions[i3 + 2] = Math.random() * 200 - 100;
      }
      
      // Reset particles that drift too far horizontally
      const distSq = positions[i3]**2 + positions[i3+2]**2;
      if (distSq > 40000) { // 200^2
        positions[i3] = Math.random() * 200 - 100;
        positions[i3 + 1] = Math.random() * 80;
        positions[i3 + 2] = Math.random() * 200 - 100;
      }
    }
    
    positionsRef.current.needsUpdate = true;
    sizesRef.current.needsUpdate = true;
  });
  
  return (
    <points frustumCulled={false}>
      <bufferGeometry>
        <bufferAttribute
          ref={positionsRef}
          attach="attributes-position"
          count={snowCount}
          array={snowPositions}
          itemSize={3}
        />
        <bufferAttribute
          ref={sizesRef}
          attach="attributes-size"
          count={snowCount}
          array={snowSizes}
          itemSize={1}
        />
      </bufferGeometry>
      <pointsMaterial 
        color="#ffffff" 
        transparent 
        opacity={0.8} 
        sizeAttenuation 
        depthWrite={false}
        vertexColors={false}
        blending={THREE.AdditiveBlending}
      />
    </points>
  );
});

export default RacingEnvironment;
