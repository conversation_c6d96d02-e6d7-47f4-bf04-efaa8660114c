import React, { useEffect, useRef } from 'react';
import { useSelector, useDispatch } from 'react-redux';
import { RootState } from '../store';
import { updateVehicleState } from '../store/gameSlice';
import { AudioSystem } from '../game/AudioSystem';

// Enhanced Physics constants for arcade-style fun!
const PHYSICS_CONSTANTS = {
  MAX_SPEED: 15, // Increased for more excitement
  MAX_REVERSE_SPEED: -4,
  ACCELERATION: 0.08, // Faster acceleration for instant gratification
  DECELERATION: 0.03,
  BRAKE_DECELERATION: 0.15,
  MAX_STEERING_ANGLE: 0.04, // Better turning for easier control
  DRIFT_THRESHOLD: 6, // Lower threshold for easier drifting
  DRIFT_FACTOR: 1.5, // More dramatic drift effects
  NITRO_BOOST: 0.25, // Bigger nitro boost for "WOW" factor
  NITRO_DURATION: 4000, // Longer nitro for more fun
  COLLISION_PENALTY: 0.3, // Less harsh collision penalty
  OFFROAD_FRICTION: 0.08,

  // New arcade features!
  JUMP_FORCE: 0.5, // For jump ramps
  BOUNCE_FACTOR: 0.8, // Bouncy collisions
  SPEED_BOOST_MULTIPLIER: 1.8, // Speed boost pads
  DRIFT_SCORE_MULTIPLIER: 10, // Points for drifting
  COMBO_MULTIPLIER: 1.5, // Combo system
  PERFECT_DRIFT_BONUS: 2.0, // Perfect drift timing bonus
  AIR_TIME_BONUS: 5, // Points for air time
  NEAR_MISS_BONUS: 15 // Points for close calls
};

interface CollidableObject {
  x: number;
  y: number;
  radius: number;
  type: 'obstacle' | 'boost' | 'checkpoint' | 'finish' | 'jump_ramp' | 'speed_pad' | 'drift_zone' | 'coin' | 'power_up';
  effect?: {
    speedBoost?: number;
    jumpForce?: number;
    driftBonus?: number;
    points?: number;
    duration?: number;
    powerUpType?: 'invincibility' | 'mega_speed' | 'coin_magnet' | 'double_points';
  };
  isCollected?: boolean; // For collectibles like coins
  respawnTime?: number; // For respawning items
}

interface VehicleState {
  x: number;
  y: number;
  speed: number;
  acceleration: number;
  direction: number;
  angularVelocity: number;
  isDrifting: boolean;
  isOffroadSurface?: boolean;
  nitroFuel?: number;
  isNitroActive?: boolean;

  // Enhanced arcade features
  isJumping?: boolean;
  jumpHeight?: number;
  airTime?: number;
  driftTime?: number;
  driftScore?: number;
  comboMultiplier?: number;
  lastComboTime?: number;
  totalScore?: number;
  coinsCollected?: number;
  activePowerUps?: Array<{
    type: string;
    expiresAt: number;
    effect: any;
  }>;
  isInvincible?: boolean;
  invincibilityUntil?: number;
  speedBoostUntil?: number;
  coinMagnetUntil?: number;
  doublePointsUntil?: number;
}

const GamePhysics: React.FC = () => {
  const dispatch = useDispatch();
  const { controls, raceState } = useSelector((state: RootState) => state.game);
  const { vehicleState, currentVehicleId, currentTrackId } = useSelector((state: RootState) => state.game);
  const lastUpdateTime = useRef(Date.now());
  const nitroActiveUntil = useRef(0);
  const audioSystem = AudioSystem.getInstance();
  const animationFrame = useRef<number | null>(null);
  const vehicles = useSelector((state: RootState) => state.vehicle.vehicles);
  const equippedParts = useSelector((state: RootState) => 
    state.vehicle.equippedParts[currentVehicleId] || {}
  );
  
  // Get vehicle stats based on equipped parts and base vehicle
  const getVehicleStats = () => {
    // Default stats if nothing is equipped or vehicle not found
    let stats = {
      maxSpeed: 1.0,
      acceleration: 1.0,
      handling: 1.0,
      braking: 1.0,
      offroad: 1.0
    };
    
    // Get base vehicle stats
    const vehicle = vehicles?.[currentVehicleId];
    if (vehicle) {
      stats.maxSpeed = vehicle.stats?.speed / 10 || 1.0;
      stats.acceleration = vehicle.stats?.acceleration / 10 || 1.0;
      stats.handling = vehicle.stats?.handling / 10 || 1.0;
      stats.braking = vehicle.stats?.braking / 10 || 1.0;
      stats.offroad = vehicle.stats?.offroad / 10 || 1.0;
    }
    
    // Apply part bonuses
    if (equippedParts.engine) {
      stats.maxSpeed *= 1 + (equippedParts.engine.bonus?.speed || 0);
      stats.acceleration *= 1 + (equippedParts.engine.bonus?.acceleration || 0);
    }
    
    if (equippedParts.wheels) {
      stats.handling *= 1 + (equippedParts.wheels.bonus?.handling || 0);
      stats.braking *= 1 + (equippedParts.wheels.bonus?.braking || 0);
      stats.offroad *= 1 + (equippedParts.wheels.bonus?.offroad || 0);
    }
    
    if (equippedParts.suspension) {
      stats.handling *= 1 + (equippedParts.suspension.bonus?.handling || 0);
      stats.offroad *= 1 + (equippedParts.suspension.bonus?.offroad || 0);
    }
    
    return stats;
  };
  
  // Get track objects for collision detection - Enhanced with arcade elements!
  const getTrackObjects = (): CollidableObject[] => {
    // This would normally load from track data
    // Enhanced sample set with fun arcade elements
    return [
      // Traditional elements
      { x: 500, y: 200, radius: 30, type: 'obstacle' },
      { x: 300, y: 700, radius: 40, type: 'checkpoint' },
      { x: 100, y: 100, radius: 50, type: 'finish' },

      // Fun arcade elements!
      {
        x: 800, y: 600, radius: 25, type: 'speed_pad',
        effect: { speedBoost: 1.5, duration: 2000, points: 50 }
      },
      {
        x: 400, y: 300, radius: 35, type: 'jump_ramp',
        effect: { jumpForce: 0.8, points: 100 }
      },
      {
        x: 650, y: 450, radius: 40, type: 'drift_zone',
        effect: { driftBonus: 2.0, points: 25 }
      },

      // Collectible coins scattered around
      { x: 200, y: 150, radius: 15, type: 'coin', effect: { points: 10 }, isCollected: false },
      { x: 350, y: 250, radius: 15, type: 'coin', effect: { points: 10 }, isCollected: false },
      { x: 550, y: 350, radius: 15, type: 'coin', effect: { points: 10 }, isCollected: false },
      { x: 750, y: 150, radius: 15, type: 'coin', effect: { points: 10 }, isCollected: false },
      { x: 150, y: 500, radius: 15, type: 'coin', effect: { points: 10 }, isCollected: false },

      // Power-ups
      {
        x: 900, y: 300, radius: 20, type: 'power_up',
        effect: { powerUpType: 'invincibility', duration: 5000, points: 200 },
        respawnTime: 15000
      },
      {
        x: 250, y: 600, radius: 20, type: 'power_up',
        effect: { powerUpType: 'mega_speed', duration: 3000, points: 150 },
        respawnTime: 12000
      },
      {
        x: 700, y: 750, radius: 20, type: 'power_up',
        effect: { powerUpType: 'coin_magnet', duration: 8000, points: 100 },
        respawnTime: 20000
      }
    ];
  };
  
  // Check if vehicle is on track or offroad
  const isOffroad = (x: number, y: number): boolean => {
    // This would check against track boundaries
    // For now using a simple boundary check
    const trackWidth = 1000;
    const trackHeight = 800;
    
    return x < 0 || y < 0 || x > trackWidth || y > trackHeight;
  };
  
  // Enhanced collision detection with arcade elements
  const checkCollisions = (x: number, y: number, objects: CollidableObject[]): CollidableObject | null => {
    for (const obj of objects) {
      // Skip collected items
      if (obj.isCollected) continue;

      const dx = x - obj.x;
      const dy = y - obj.y;
      const distance = Math.sqrt(dx * dx + dy * dy);

      if (distance < obj.radius + 20) { // 20 is approximated vehicle radius
        return obj;
      }
    }
    return null;
  };

  // Handle arcade collision effects
  const handleCollisionEffect = (collision: CollidableObject, currentState: any) => {
    const now = Date.now();
    let newState = { ...currentState };
    let scoreGained = 0;

    switch (collision.type) {
      case 'speed_pad':
        newState.speedBoostUntil = now + (collision.effect?.duration || 2000);
        scoreGained = collision.effect?.points || 50;
        audioSystem.playSoundEffect('speed_boost');
        break;

      case 'jump_ramp':
        newState.isJumping = true;
        newState.jumpHeight = collision.effect?.jumpForce || 0.5;
        newState.airTime = 0;
        scoreGained = collision.effect?.points || 100;
        audioSystem.playSoundEffect('jump');
        break;

      case 'drift_zone':
        newState.driftScore = (newState.driftScore || 0) + (collision.effect?.points || 25);
        newState.comboMultiplier = Math.min((newState.comboMultiplier || 1) * 1.2, 5);
        scoreGained = collision.effect?.points || 25;
        break;

      case 'coin':
        collision.isCollected = true;
        newState.coinsCollected = (newState.coinsCollected || 0) + 1;
        scoreGained = collision.effect?.points || 10;
        audioSystem.playSoundEffect('coin_collect');
        break;

      case 'power_up':
        const powerUpType = collision.effect?.powerUpType;
        const duration = collision.effect?.duration || 5000;

        switch (powerUpType) {
          case 'invincibility':
            newState.isInvincible = true;
            newState.invincibilityUntil = now + duration;
            break;
          case 'mega_speed':
            newState.speedBoostUntil = now + duration;
            break;
          case 'coin_magnet':
            newState.coinMagnetUntil = now + duration;
            break;
          case 'double_points':
            newState.doublePointsUntil = now + duration;
            break;
        }

        collision.isCollected = true;
        scoreGained = collision.effect?.points || 100;
        audioSystem.playSoundEffect('power_up');

        // Schedule respawn
        if (collision.respawnTime) {
          setTimeout(() => {
            collision.isCollected = false;
          }, collision.respawnTime);
        }
        break;

      case 'obstacle':
        if (!newState.isInvincible) {
          newState.speed *= PHYSICS_CONSTANTS.COLLISION_PENALTY;
          audioSystem.playSoundEffect('collision');
        } else {
          // Invincible - bounce off with style!
          newState.speed *= PHYSICS_CONSTANTS.BOUNCE_FACTOR;
          scoreGained = 50; // Bonus for invincible collision
        }
        break;
    }

    // Apply score multipliers
    if (newState.doublePointsUntil && now < newState.doublePointsUntil) {
      scoreGained *= 2;
    }
    if (newState.comboMultiplier && newState.comboMultiplier > 1) {
      scoreGained *= newState.comboMultiplier;
    }

    newState.totalScore = (newState.totalScore || 0) + scoreGained;

    return newState;
  };
  
  // Handles the physics update on each animation frame
  const updatePhysics = () => {
    const now = Date.now();
    const deltaTime = Math.min(now - lastUpdateTime.current, 100); // Cap at 100ms to prevent huge jumps
    const timeScale = deltaTime / 16; // normalized for 60fps
    lastUpdateTime.current = now;
    
    // Skip updates with abnormal time deltas
    if (timeScale <= 0 || !raceState.isRaceStarted) {
      animationFrame.current = requestAnimationFrame(updatePhysics);
      return;
    }
    
    // Get vehicle stats
    const vehicleStats = getVehicleStats();
    
    // Enhanced state values with arcade features
    let {
      x, y, speed, acceleration, direction, angularVelocity, isDrifting,
      isOffroadSurface = false, nitroFuel = 100, isNitroActive = false,
      // Arcade features
      isJumping = false, jumpHeight = 0, airTime = 0, driftTime = 0,
      driftScore = 0, comboMultiplier = 1, lastComboTime = 0, totalScore = 0,
      coinsCollected = 0, activePowerUps = [], isInvincible = false,
      invincibilityUntil = 0, speedBoostUntil = 0, coinMagnetUntil = 0, doublePointsUntil = 0
    } = vehicleState;

    // Check active power-ups and effects
    isNitroActive = now < nitroActiveUntil.current;
    isInvincible = now < invincibilityUntil;
    const hasSpeedBoost = now < speedBoostUntil;
    const hasCoinMagnet = now < coinMagnetUntil;
    const hasDoublePoints = now < doublePointsUntil;

    // Update air time if jumping
    if (isJumping) {
      airTime += deltaTime;
      jumpHeight = Math.max(0, jumpHeight - (deltaTime * 0.002)); // Gravity effect

      if (jumpHeight <= 0) {
        isJumping = false;
        jumpHeight = 0;
        // Award air time bonus
        const airTimeBonus = Math.floor(airTime / 100) * PHYSICS_CONSTANTS.AIR_TIME_BONUS;
        if (airTimeBonus > 0) {
          totalScore += airTimeBonus * (hasDoublePoints ? 2 : 1);
          audioSystem.playSoundEffect('air_time_bonus');
        }
        airTime = 0;
      }
    }

    // Update drift tracking
    if (isDrifting) {
      driftTime += deltaTime;
      // Award drift points over time
      const driftPoints = Math.floor(driftTime / 500) * PHYSICS_CONSTANTS.DRIFT_SCORE_MULTIPLIER;
      if (driftPoints > 0) {
        driftScore += driftPoints;
        totalScore += driftPoints * comboMultiplier * (hasDoublePoints ? 2 : 1);
      }
    } else {
      if (driftTime > 0) {
        // End of drift - check for perfect drift bonus
        if (driftTime > 1000 && driftTime < 3000) { // Perfect drift window
          const perfectBonus = PHYSICS_CONSTANTS.PERFECT_DRIFT_BONUS * driftScore;
          totalScore += perfectBonus * (hasDoublePoints ? 2 : 1);
          audioSystem.playSoundEffect('perfect_drift');
        }
        driftTime = 0;
      }
    }

    // Decay combo multiplier over time
    if (now - lastComboTime > 3000) { // 3 seconds without combo action
      comboMultiplier = Math.max(1, comboMultiplier * 0.95);
    }
    
    // Enhanced acceleration with power-up effects
    if (controls.up) {
      let accelMultiplier = 1;

      // Apply speed boost power-up
      if (hasSpeedBoost) {
        accelMultiplier *= PHYSICS_CONSTANTS.SPEED_BOOST_MULTIPLIER;
      }

      // Apply nitro boost
      if (isNitroActive) {
        accelMultiplier *= (1 + PHYSICS_CONSTANTS.NITRO_BOOST);
      }

      acceleration = PHYSICS_CONSTANTS.ACCELERATION * vehicleStats.acceleration * timeScale * accelMultiplier;
    } else if (controls.down) {
      acceleration = -PHYSICS_CONSTANTS.ACCELERATION * vehicleStats.braking * timeScale;
    } else {
      acceleration = 0;
      // Natural deceleration when no input
      if (Math.abs(speed) > 0.01) {
        const decel = PHYSICS_CONSTANTS.DECELERATION * timeScale * Math.sign(speed);
        speed -= decel;
      } else {
        speed = 0;
      }
    }
    
    // Apply brake
    if (controls.brake && Math.abs(speed) > 0.1) {
      const brakeForce = PHYSICS_CONSTANTS.BRAKE_DECELERATION * vehicleStats.braking * timeScale * Math.sign(speed);
      speed -= brakeForce;
      
      // Play brake sound when braking at speed
      if (Math.abs(speed) > 2 && !audioSystem.isPlaying('brake_skid')) {
        audioSystem.playSoundEffect('brake_skid');
      }
    }
    
    // Apply nitro boost
    if (controls.nitro && speed > 0 && nitroFuel > 0) {
      nitroActiveUntil.current = now + PHYSICS_CONSTANTS.NITRO_DURATION;
      
      // Play nitro sound
      if (!audioSystem.isPlaying('nitro_boost')) {
        audioSystem.playSoundEffect('nitro_boost');
      }
      
      // Reduce nitro fuel
      nitroFuel = Math.max(0, nitroFuel - (timeScale * 0.05));
    }
    
    // Apply nitro boost if active
    if (isNitroActive && speed > 0) {
      speed += PHYSICS_CONSTANTS.NITRO_BOOST * timeScale;
    }
    
    // Update speed with acceleration
    speed += acceleration;
    
    // Check if terrain is offroad
    isOffroadSurface = isOffroad(x, y);
    
    // Apply offroad friction
    if (isOffroadSurface) {
      speed -= PHYSICS_CONSTANTS.OFFROAD_FRICTION * (1 - vehicleStats.offroad) * timeScale * Math.sign(speed);
    }
    
    // Clamp speed to max values
    const adjustedMaxSpeed = PHYSICS_CONSTANTS.MAX_SPEED * vehicleStats.maxSpeed;
    
    if (speed > adjustedMaxSpeed) {
      speed = adjustedMaxSpeed;
    } else if (speed < PHYSICS_CONSTANTS.MAX_REVERSE_SPEED) {
      speed = PHYSICS_CONSTANTS.MAX_REVERSE_SPEED;
    }
    
    // Check if car can drift (based on speed)
    isDrifting = Math.abs(speed) > PHYSICS_CONSTANTS.DRIFT_THRESHOLD && 
      (controls.left || controls.right) && !controls.brake;
      
    // Apply steering based on current speed
    let steeringFactor = Math.abs(speed) / PHYSICS_CONSTANTS.MAX_SPEED * vehicleStats.handling;
    if (isDrifting) {
      steeringFactor *= PHYSICS_CONSTANTS.DRIFT_FACTOR; // Increase turning during drift
      
      // Play drift sound
      if (!audioSystem.isPlaying('tire_skid')) {
        audioSystem.playSoundEffect('tire_skid');
      }
    } else {
      // Stop drift sound if not drifting
      if (audioSystem.isPlaying('tire_skid')) {
        audioSystem.stopSoundEffect('tire_skid');
      }
    }
    
    if (controls.left) {
      angularVelocity = -PHYSICS_CONSTANTS.MAX_STEERING_ANGLE * steeringFactor * timeScale;
    } else if (controls.right) {
      angularVelocity = PHYSICS_CONSTANTS.MAX_STEERING_ANGLE * steeringFactor * timeScale;
    } else {
      angularVelocity = 0;
    }
    
    // Update direction
    direction += angularVelocity;
    
    // Normalize direction to 0-2π
    direction = direction % (Math.PI * 2);
    if (direction < 0) direction += Math.PI * 2;
    
    // Calculate new position
    const moveX = Math.sin(direction) * speed * timeScale;
    const moveY = -Math.cos(direction) * speed * timeScale;
    
    const newX = x + moveX;
    const newY = y + moveY;
    
    // Enhanced collision detection with arcade elements
    const trackObjects = getTrackObjects();
    const collision = checkCollisions(newX, newY, trackObjects);

    if (collision) {
      // Handle collision with enhanced arcade system
      const currentState = {
        x, y, speed, acceleration, direction, angularVelocity, isDrifting,
        isOffroadSurface, nitroFuel, isNitroActive, isJumping, jumpHeight,
        airTime, driftTime, driftScore, comboMultiplier, lastComboTime,
        totalScore, coinsCollected, activePowerUps, isInvincible,
        invincibilityUntil, speedBoostUntil, coinMagnetUntil, doublePointsUntil
      };

      const newState = handleCollisionEffect(collision, currentState);

      // Update all state variables from the enhanced collision handling
      ({
        x, y, speed, acceleration, direction, angularVelocity, isDrifting,
        isOffroadSurface, nitroFuel, isNitroActive, isJumping, jumpHeight,
        airTime, driftTime, driftScore, comboMultiplier, lastComboTime,
        totalScore, coinsCollected, activePowerUps, isInvincible,
        invincibilityUntil, speedBoostUntil, coinMagnetUntil, doublePointsUntil
      } = newState);

      // Update combo timer
      lastComboTime = now;

      // For non-blocking collisions, still update position
      if (!['obstacle'].includes(collision.type) || isInvincible) {
        x = newX;
        y = newY;
      }
    } else {
      // No collision, update position
      x = newX;
      y = newY;
    }

    // Coin magnet effect - attract nearby coins
    if (hasCoinMagnet) {
      trackObjects.forEach(obj => {
        if (obj.type === 'coin' && !obj.isCollected) {
          const dx = x - obj.x;
          const dy = y - obj.y;
          const distance = Math.sqrt(dx * dx + dy * dy);

          if (distance < 100) { // Magnet range
            // Move coin towards player
            const magnetForce = 0.1;
            obj.x += dx * magnetForce;
            obj.y += dy * magnetForce;
          }
        }
      });
    }
    
    // Update engine sound pitch based on speed
    if (audioSystem.isPlaying('engine_idle')) {
      const normalizedSpeed = Math.abs(speed) / adjustedMaxSpeed;
      audioSystem.setPlaybackRate('engine_idle', 1.0 + normalizedSpeed * 0.5);
    }
    
    // Update redux store with enhanced vehicle state including arcade features
    dispatch(updateVehicleState({
      x,
      y,
      speed,
      acceleration,
      direction,
      angularVelocity,
      isDrifting,
      isOffroadSurface,
      nitroFuel,
      isNitroActive,

      // Enhanced arcade features
      isJumping,
      jumpHeight,
      airTime,
      driftTime,
      driftScore,
      comboMultiplier,
      lastComboTime,
      totalScore,
      coinsCollected,
      activePowerUps,
      isInvincible,
      invincibilityUntil,
      speedBoostUntil,
      coinMagnetUntil,
      doublePointsUntil
    }));
    
    // Continue animation loop
    animationFrame.current = requestAnimationFrame(updatePhysics);
  };
      
      // Set engine sound based on speed
      const enginePitch = 0.5 + (Math.abs(speed) / PHYSICS_CONSTANTS.MAX_SPEED) * 0.5;
      audioSystem.setEngineParameters(enginePitch, Math.abs(speed) / PHYSICS_CONSTANTS.MAX_SPEED);
      
      requestAnimationFrame(updatePhysics);
    };

    // Initialize lastUpdateTime before starting the loop
    lastUpdateTime.current = Date.now();

    // Start physics simulation when component mounts
    useEffect(() => {
    // Initialize engine sound
    audioSystem.playSoundEffect('engine_idle', { loop: true, volume: 0.4 });
    
    // Start physics update loop
    animationFrame.current = requestAnimationFrame(updatePhysics);
    
    return () => {
      // Clean up on unmount
      if (animationFrame.current) {
        cancelAnimationFrame(animationFrame.current);
      }
      audioSystem.stopSoundEffect('engine_idle');
      audioSystem.stopSoundEffect('tire_skid');
      audioSystem.stopSoundEffect('brake_skid');
      audioSystem.stopSoundEffect('nitro_boost');
    };
  }, []);

  return null; // This component doesn't render anything
};

export default GamePhysics;
