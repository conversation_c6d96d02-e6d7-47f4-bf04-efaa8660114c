{"ast": null, "code": "import { <PERSON><PERSON>, <PERSON><PERSON>oa<PERSON>, Vector3, <PERSON>Texture, UnsignedByteType, LinearFilter, ClampToEdgeWrapping } from \"three\";\nimport { Data3DTexture } from \"../_polyfill/Data3DTexture.js\";\nclass LUTCubeLoader extends Loader {\n  load(url, onLoad, onProgress, onError) {\n    const loader = new FileLoader(this.manager);\n    loader.setPath(this.path);\n    loader.setResponseType(\"text\");\n    loader.load(url, text => {\n      try {\n        onLoad(this.parse(text));\n      } catch (e) {\n        if (onError) {\n          onError(e);\n        } else {\n          console.error(e);\n        }\n        this.manager.itemError(url);\n      }\n    }, onProgress, onError);\n  }\n  parse(str) {\n    str = str.replace(/^#.*?(\\n|\\r)/gm, \"\").replace(/^\\s*?(\\n|\\r)/gm, \"\").trim();\n    let title = null;\n    let size = null;\n    const domainMin = new Vector3(0, 0, 0);\n    const domainMax = new Vector3(1, 1, 1);\n    const lines = str.split(/[\\n\\r]+/g);\n    let data = null;\n    let currIndex = 0;\n    for (let i = 0, l = lines.length; i < l; i++) {\n      const line = lines[i].trim();\n      const split = line.split(/\\s/g);\n      switch (split[0]) {\n        case \"TITLE\":\n          title = line.substring(7, line.length - 1);\n          break;\n        case \"LUT_3D_SIZE\":\n          const sizeToken = split[1];\n          size = parseFloat(sizeToken);\n          data = new Uint8Array(size * size * size * 4);\n          break;\n        case \"DOMAIN_MIN\":\n          domainMin.x = parseFloat(split[1]);\n          domainMin.y = parseFloat(split[2]);\n          domainMin.z = parseFloat(split[3]);\n          break;\n        case \"DOMAIN_MAX\":\n          domainMax.x = parseFloat(split[1]);\n          domainMax.y = parseFloat(split[2]);\n          domainMax.z = parseFloat(split[3]);\n          break;\n        default:\n          const r = parseFloat(split[0]);\n          const g = parseFloat(split[1]);\n          const b = parseFloat(split[2]);\n          if (r > 1 || r < 0 || g > 1 || g < 0 || b > 1 || b < 0) {\n            throw new Error(\"LUTCubeLoader : Non normalized values not supported.\");\n          }\n          data[currIndex + 0] = r * 255;\n          data[currIndex + 1] = g * 255;\n          data[currIndex + 2] = b * 255;\n          data[currIndex + 3] = 255;\n          currIndex += 4;\n      }\n    }\n    const texture = new DataTexture();\n    texture.image.data = data;\n    texture.image.width = size;\n    texture.image.height = size * size;\n    texture.type = UnsignedByteType;\n    texture.magFilter = LinearFilter;\n    texture.minFilter = LinearFilter;\n    texture.wrapS = ClampToEdgeWrapping;\n    texture.wrapT = ClampToEdgeWrapping;\n    texture.generateMipmaps = false;\n    texture.needsUpdate = true;\n    const texture3D = new Data3DTexture();\n    texture3D.image.data = data;\n    texture3D.image.width = size;\n    texture3D.image.height = size;\n    texture3D.image.depth = size;\n    texture3D.type = UnsignedByteType;\n    texture3D.magFilter = LinearFilter;\n    texture3D.minFilter = LinearFilter;\n    texture3D.wrapS = ClampToEdgeWrapping;\n    texture3D.wrapT = ClampToEdgeWrapping;\n    texture3D.wrapR = ClampToEdgeWrapping;\n    texture3D.generateMipmaps = false;\n    texture3D.needsUpdate = true;\n    return {\n      title,\n      size,\n      domainMin,\n      domainMax,\n      texture,\n      texture3D\n    };\n  }\n}\nexport { LUTCubeLoader };", "map": {"version": 3, "names": ["LUTCubeLoader", "Loader", "load", "url", "onLoad", "onProgress", "onError", "loader", "<PERSON><PERSON><PERSON><PERSON>", "manager", "set<PERSON>ath", "path", "setResponseType", "text", "parse", "e", "console", "error", "itemError", "str", "replace", "trim", "title", "size", "domainMin", "Vector3", "domainMax", "lines", "split", "data", "currIndex", "i", "l", "length", "line", "substring", "sizeToken", "parseFloat", "Uint8Array", "x", "y", "z", "r", "g", "b", "Error", "texture", "DataTexture", "image", "width", "height", "type", "UnsignedByteType", "magFilter", "LinearFilter", "minFilter", "wrapS", "ClampToEdgeWrapping", "wrapT", "generateMipmaps", "needsUpdate", "texture3D", "Data3DTexture", "depth", "wrapR"], "sources": ["C:\\Users\\<USER>\\Documents\\Apps_Creations\\SiahMan_Racing4Speed_WebGame\\siah-man-racing\\node_modules\\src\\loaders\\LUTCubeLoader.js"], "sourcesContent": ["// https://wwwimages2.adobe.com/content/dam/acom/en/products/speedgrade/cc/pdfs/cube-lut-specification-1.0.pdf\n\nimport { Loader, FileLoader, Vector3, DataTexture, UnsignedByteType, ClampToEdgeWrapping, LinearFilter } from 'three'\nimport { Data3DTexture } from '../_polyfill/Data3DTexture'\n\nexport class LUTCubeLoader extends Loader {\n  load(url, onLoad, onProgress, onError) {\n    const loader = new FileLoader(this.manager)\n    loader.setPath(this.path)\n    loader.setResponseType('text')\n    loader.load(\n      url,\n      (text) => {\n        try {\n          onLoad(this.parse(text))\n        } catch (e) {\n          if (onError) {\n            onError(e)\n          } else {\n            console.error(e)\n          }\n\n          this.manager.itemError(url)\n        }\n      },\n      onProgress,\n      onError,\n    )\n  }\n\n  parse(str) {\n    // Remove empty lines and comments\n    str = str\n      .replace(/^#.*?(\\n|\\r)/gm, '')\n      .replace(/^\\s*?(\\n|\\r)/gm, '')\n      .trim()\n\n    let title = null\n    let size = null\n    const domainMin = new Vector3(0, 0, 0)\n    const domainMax = new Vector3(1, 1, 1)\n\n    const lines = str.split(/[\\n\\r]+/g)\n    let data = null\n\n    let currIndex = 0\n    for (let i = 0, l = lines.length; i < l; i++) {\n      const line = lines[i].trim()\n      const split = line.split(/\\s/g)\n\n      switch (split[0]) {\n        case 'TITLE':\n          title = line.substring(7, line.length - 1)\n          break\n        case 'LUT_3D_SIZE':\n          // TODO: A .CUBE LUT file specifies floating point values and could be represented with\n          // more precision than can be captured with Uint8Array.\n          const sizeToken = split[1]\n          size = parseFloat(sizeToken)\n          data = new Uint8Array(size * size * size * 4)\n          break\n        case 'DOMAIN_MIN':\n          domainMin.x = parseFloat(split[1])\n          domainMin.y = parseFloat(split[2])\n          domainMin.z = parseFloat(split[3])\n          break\n        case 'DOMAIN_MAX':\n          domainMax.x = parseFloat(split[1])\n          domainMax.y = parseFloat(split[2])\n          domainMax.z = parseFloat(split[3])\n          break\n        default:\n          const r = parseFloat(split[0])\n          const g = parseFloat(split[1])\n          const b = parseFloat(split[2])\n\n          if (r > 1.0 || r < 0.0 || g > 1.0 || g < 0.0 || b > 1.0 || b < 0.0) {\n            throw new Error('LUTCubeLoader : Non normalized values not supported.')\n          }\n\n          data[currIndex + 0] = r * 255\n          data[currIndex + 1] = g * 255\n          data[currIndex + 2] = b * 255\n          data[currIndex + 3] = 255\n          currIndex += 4\n      }\n    }\n\n    const texture = new DataTexture()\n    texture.image.data = data\n    texture.image.width = size\n    texture.image.height = size * size\n    texture.type = UnsignedByteType\n    texture.magFilter = LinearFilter\n    texture.minFilter = LinearFilter\n    texture.wrapS = ClampToEdgeWrapping\n    texture.wrapT = ClampToEdgeWrapping\n    texture.generateMipmaps = false\n    texture.needsUpdate = true\n\n    const texture3D = new Data3DTexture()\n    texture3D.image.data = data\n    texture3D.image.width = size\n    texture3D.image.height = size\n    texture3D.image.depth = size\n    texture3D.type = UnsignedByteType\n    texture3D.magFilter = LinearFilter\n    texture3D.minFilter = LinearFilter\n    texture3D.wrapS = ClampToEdgeWrapping\n    texture3D.wrapT = ClampToEdgeWrapping\n    texture3D.wrapR = ClampToEdgeWrapping\n    texture3D.generateMipmaps = false\n    texture3D.needsUpdate = true\n\n    return {\n      title,\n      size,\n      domainMin,\n      domainMax,\n      texture,\n      texture3D,\n    }\n  }\n}\n"], "mappings": ";;AAKO,MAAMA,aAAA,SAAsBC,MAAA,CAAO;EACxCC,KAAKC,GAAA,EAAKC,MAAA,EAAQC,UAAA,EAAYC,OAAA,EAAS;IACrC,MAAMC,MAAA,GAAS,IAAIC,UAAA,CAAW,KAAKC,OAAO;IAC1CF,MAAA,CAAOG,OAAA,CAAQ,KAAKC,IAAI;IACxBJ,MAAA,CAAOK,eAAA,CAAgB,MAAM;IAC7BL,MAAA,CAAOL,IAAA,CACLC,GAAA,EACCU,IAAA,IAAS;MACR,IAAI;QACFT,MAAA,CAAO,KAAKU,KAAA,CAAMD,IAAI,CAAC;MACxB,SAAQE,CAAA,EAAP;QACA,IAAIT,OAAA,EAAS;UACXA,OAAA,CAAQS,CAAC;QACrB,OAAiB;UACLC,OAAA,CAAQC,KAAA,CAAMF,CAAC;QAChB;QAED,KAAKN,OAAA,CAAQS,SAAA,CAAUf,GAAG;MAC3B;IACF,GACDE,UAAA,EACAC,OACD;EACF;EAEDQ,MAAMK,GAAA,EAAK;IAETA,GAAA,GAAMA,GAAA,CACHC,OAAA,CAAQ,kBAAkB,EAAE,EAC5BA,OAAA,CAAQ,kBAAkB,EAAE,EAC5BC,IAAA,CAAM;IAET,IAAIC,KAAA,GAAQ;IACZ,IAAIC,IAAA,GAAO;IACX,MAAMC,SAAA,GAAY,IAAIC,OAAA,CAAQ,GAAG,GAAG,CAAC;IACrC,MAAMC,SAAA,GAAY,IAAID,OAAA,CAAQ,GAAG,GAAG,CAAC;IAErC,MAAME,KAAA,GAAQR,GAAA,CAAIS,KAAA,CAAM,UAAU;IAClC,IAAIC,IAAA,GAAO;IAEX,IAAIC,SAAA,GAAY;IAChB,SAASC,CAAA,GAAI,GAAGC,CAAA,GAAIL,KAAA,CAAMM,MAAA,EAAQF,CAAA,GAAIC,CAAA,EAAGD,CAAA,IAAK;MAC5C,MAAMG,IAAA,GAAOP,KAAA,CAAMI,CAAC,EAAEV,IAAA,CAAM;MAC5B,MAAMO,KAAA,GAAQM,IAAA,CAAKN,KAAA,CAAM,KAAK;MAE9B,QAAQA,KAAA,CAAM,CAAC;QACb,KAAK;UACHN,KAAA,GAAQY,IAAA,CAAKC,SAAA,CAAU,GAAGD,IAAA,CAAKD,MAAA,GAAS,CAAC;UACzC;QACF,KAAK;UAGH,MAAMG,SAAA,GAAYR,KAAA,CAAM,CAAC;UACzBL,IAAA,GAAOc,UAAA,CAAWD,SAAS;UAC3BP,IAAA,GAAO,IAAIS,UAAA,CAAWf,IAAA,GAAOA,IAAA,GAAOA,IAAA,GAAO,CAAC;UAC5C;QACF,KAAK;UACHC,SAAA,CAAUe,CAAA,GAAIF,UAAA,CAAWT,KAAA,CAAM,CAAC,CAAC;UACjCJ,SAAA,CAAUgB,CAAA,GAAIH,UAAA,CAAWT,KAAA,CAAM,CAAC,CAAC;UACjCJ,SAAA,CAAUiB,CAAA,GAAIJ,UAAA,CAAWT,KAAA,CAAM,CAAC,CAAC;UACjC;QACF,KAAK;UACHF,SAAA,CAAUa,CAAA,GAAIF,UAAA,CAAWT,KAAA,CAAM,CAAC,CAAC;UACjCF,SAAA,CAAUc,CAAA,GAAIH,UAAA,CAAWT,KAAA,CAAM,CAAC,CAAC;UACjCF,SAAA,CAAUe,CAAA,GAAIJ,UAAA,CAAWT,KAAA,CAAM,CAAC,CAAC;UACjC;QACF;UACE,MAAMc,CAAA,GAAIL,UAAA,CAAWT,KAAA,CAAM,CAAC,CAAC;UAC7B,MAAMe,CAAA,GAAIN,UAAA,CAAWT,KAAA,CAAM,CAAC,CAAC;UAC7B,MAAMgB,CAAA,GAAIP,UAAA,CAAWT,KAAA,CAAM,CAAC,CAAC;UAE7B,IAAIc,CAAA,GAAI,KAAOA,CAAA,GAAI,KAAOC,CAAA,GAAI,KAAOA,CAAA,GAAI,KAAOC,CAAA,GAAI,KAAOA,CAAA,GAAI,GAAK;YAClE,MAAM,IAAIC,KAAA,CAAM,sDAAsD;UACvE;UAEDhB,IAAA,CAAKC,SAAA,GAAY,CAAC,IAAIY,CAAA,GAAI;UAC1Bb,IAAA,CAAKC,SAAA,GAAY,CAAC,IAAIa,CAAA,GAAI;UAC1Bd,IAAA,CAAKC,SAAA,GAAY,CAAC,IAAIc,CAAA,GAAI;UAC1Bf,IAAA,CAAKC,SAAA,GAAY,CAAC,IAAI;UACtBA,SAAA,IAAa;MAChB;IACF;IAED,MAAMgB,OAAA,GAAU,IAAIC,WAAA,CAAa;IACjCD,OAAA,CAAQE,KAAA,CAAMnB,IAAA,GAAOA,IAAA;IACrBiB,OAAA,CAAQE,KAAA,CAAMC,KAAA,GAAQ1B,IAAA;IACtBuB,OAAA,CAAQE,KAAA,CAAME,MAAA,GAAS3B,IAAA,GAAOA,IAAA;IAC9BuB,OAAA,CAAQK,IAAA,GAAOC,gBAAA;IACfN,OAAA,CAAQO,SAAA,GAAYC,YAAA;IACpBR,OAAA,CAAQS,SAAA,GAAYD,YAAA;IACpBR,OAAA,CAAQU,KAAA,GAAQC,mBAAA;IAChBX,OAAA,CAAQY,KAAA,GAAQD,mBAAA;IAChBX,OAAA,CAAQa,eAAA,GAAkB;IAC1Bb,OAAA,CAAQc,WAAA,GAAc;IAEtB,MAAMC,SAAA,GAAY,IAAIC,aAAA,CAAe;IACrCD,SAAA,CAAUb,KAAA,CAAMnB,IAAA,GAAOA,IAAA;IACvBgC,SAAA,CAAUb,KAAA,CAAMC,KAAA,GAAQ1B,IAAA;IACxBsC,SAAA,CAAUb,KAAA,CAAME,MAAA,GAAS3B,IAAA;IACzBsC,SAAA,CAAUb,KAAA,CAAMe,KAAA,GAAQxC,IAAA;IACxBsC,SAAA,CAAUV,IAAA,GAAOC,gBAAA;IACjBS,SAAA,CAAUR,SAAA,GAAYC,YAAA;IACtBO,SAAA,CAAUN,SAAA,GAAYD,YAAA;IACtBO,SAAA,CAAUL,KAAA,GAAQC,mBAAA;IAClBI,SAAA,CAAUH,KAAA,GAAQD,mBAAA;IAClBI,SAAA,CAAUG,KAAA,GAAQP,mBAAA;IAClBI,SAAA,CAAUF,eAAA,GAAkB;IAC5BE,SAAA,CAAUD,WAAA,GAAc;IAExB,OAAO;MACLtC,KAAA;MACAC,IAAA;MACAC,SAAA;MACAE,SAAA;MACAoB,OAAA;MACAe;IACD;EACF;AACH", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}