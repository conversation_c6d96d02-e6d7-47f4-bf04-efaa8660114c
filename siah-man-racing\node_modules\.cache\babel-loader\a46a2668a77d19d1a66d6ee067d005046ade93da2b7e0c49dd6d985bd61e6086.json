{"ast": null, "code": "import { MathUtils } from \"three\";\nconst _hsl = {};\nconst ColorConverter = {\n  setHSV(color, h, s, v) {\n    h = MathUtils.euclideanModulo(h, 1);\n    s = MathUtils.clamp(s, 0, 1);\n    v = MathUtils.clamp(v, 0, 1);\n    return color.setHSL(h, s * v / ((h = (2 - s) * v) < 1 ? h : 2 - h), h * 0.5);\n  },\n  getHSV(color, target) {\n    color.getHSL(_hsl);\n    _hsl.s *= _hsl.l < 0.5 ? _hsl.l : 1 - _hsl.l;\n    target.h = _hsl.h;\n    target.s = 2 * _hsl.s / (_hsl.l + _hsl.s);\n    target.v = _hsl.l + _hsl.s;\n    return target;\n  },\n  // where c, m, y, k is between 0 and 1\n  setCMYK(color, c, m, y, k) {\n    const r = (1 - c) * (1 - k);\n    const g = (1 - m) * (1 - k);\n    const b = (1 - y) * (1 - k);\n    return color.setRGB(r, g, b);\n  },\n  getCMYK(color, target) {\n    const r = color.r;\n    const g = color.g;\n    const b = color.b;\n    const k = 1 - Math.max(r, g, b);\n    const c = (1 - r - k) / (1 - k);\n    const m = (1 - g - k) / (1 - k);\n    const y = (1 - b - k) / (1 - k);\n    target.c = c;\n    target.m = m;\n    target.y = y;\n    target.k = k;\n    return target;\n  }\n};\nexport { ColorConverter };", "map": {"version": 3, "names": ["_hsl", "ColorConverter", "setHSV", "color", "h", "s", "v", "MathUtils", "euclideanModulo", "clamp", "setHSL", "getHSV", "target", "getHSL", "l", "setCMYK", "c", "m", "y", "k", "r", "g", "b", "setRGB", "getCMYK", "Math", "max"], "sources": ["C:\\Users\\<USER>\\Documents\\Apps_Creations\\SiahMan_Racing4Speed_WebGame\\siah-man-racing\\node_modules\\src\\math\\ColorConverter.js"], "sourcesContent": ["import { MathUtils } from 'three'\n\nconst _hsl = {}\n\nconst ColorConverter = {\n  setHSV(color, h, s, v) {\n    // https://gist.github.com/xpansive/1337890#file-index-js\n\n    h = MathUtils.euclideanModulo(h, 1)\n    s = MathUtils.clamp(s, 0, 1)\n    v = MathUtils.clamp(v, 0, 1)\n\n    return color.setHSL(h, (s * v) / ((h = (2 - s) * v) < 1 ? h : 2 - h), h * 0.5)\n  },\n\n  getHSV(color, target) {\n    color.getHSL(_hsl)\n\n    // based on https://gist.github.com/xpansive/1337890#file-index-js\n    _hsl.s *= _hsl.l < 0.5 ? _hsl.l : 1 - _hsl.l\n\n    target.h = _hsl.h\n    target.s = (2 * _hsl.s) / (_hsl.l + _hsl.s)\n    target.v = _hsl.l + _hsl.s\n\n    return target\n  },\n\n  // where c, m, y, k is between 0 and 1\n\n  setCMYK(color, c, m, y, k) {\n    const r = (1 - c) * (1 - k)\n    const g = (1 - m) * (1 - k)\n    const b = (1 - y) * (1 - k)\n\n    return color.setRGB(r, g, b)\n  },\n\n  getCMYK(color, target) {\n    const r = color.r\n    const g = color.g\n    const b = color.b\n\n    const k = 1 - Math.max(r, g, b)\n    const c = (1 - r - k) / (1 - k)\n    const m = (1 - g - k) / (1 - k)\n    const y = (1 - b - k) / (1 - k)\n\n    target.c = c\n    target.m = m\n    target.y = y\n    target.k = k\n\n    return target\n  },\n}\n\nexport { ColorConverter }\n"], "mappings": ";AAEA,MAAMA,IAAA,GAAO,CAAE;AAEV,MAACC,cAAA,GAAiB;EACrBC,OAAOC,KAAA,EAAOC,CAAA,EAAGC,CAAA,EAAGC,CAAA,EAAG;IAGrBF,CAAA,GAAIG,SAAA,CAAUC,eAAA,CAAgBJ,CAAA,EAAG,CAAC;IAClCC,CAAA,GAAIE,SAAA,CAAUE,KAAA,CAAMJ,CAAA,EAAG,GAAG,CAAC;IAC3BC,CAAA,GAAIC,SAAA,CAAUE,KAAA,CAAMH,CAAA,EAAG,GAAG,CAAC;IAE3B,OAAOH,KAAA,CAAMO,MAAA,CAAON,CAAA,EAAIC,CAAA,GAAIC,CAAA,KAAOF,CAAA,IAAK,IAAIC,CAAA,IAAKC,CAAA,IAAK,IAAIF,CAAA,GAAI,IAAIA,CAAA,GAAIA,CAAA,GAAI,GAAG;EAC9E;EAEDO,OAAOR,KAAA,EAAOS,MAAA,EAAQ;IACpBT,KAAA,CAAMU,MAAA,CAAOb,IAAI;IAGjBA,IAAA,CAAKK,CAAA,IAAKL,IAAA,CAAKc,CAAA,GAAI,MAAMd,IAAA,CAAKc,CAAA,GAAI,IAAId,IAAA,CAAKc,CAAA;IAE3CF,MAAA,CAAOR,CAAA,GAAIJ,IAAA,CAAKI,CAAA;IAChBQ,MAAA,CAAOP,CAAA,GAAK,IAAIL,IAAA,CAAKK,CAAA,IAAML,IAAA,CAAKc,CAAA,GAAId,IAAA,CAAKK,CAAA;IACzCO,MAAA,CAAON,CAAA,GAAIN,IAAA,CAAKc,CAAA,GAAId,IAAA,CAAKK,CAAA;IAEzB,OAAOO,MAAA;EACR;EAAA;EAIDG,QAAQZ,KAAA,EAAOa,CAAA,EAAGC,CAAA,EAAGC,CAAA,EAAGC,CAAA,EAAG;IACzB,MAAMC,CAAA,IAAK,IAAIJ,CAAA,KAAM,IAAIG,CAAA;IACzB,MAAME,CAAA,IAAK,IAAIJ,CAAA,KAAM,IAAIE,CAAA;IACzB,MAAMG,CAAA,IAAK,IAAIJ,CAAA,KAAM,IAAIC,CAAA;IAEzB,OAAOhB,KAAA,CAAMoB,MAAA,CAAOH,CAAA,EAAGC,CAAA,EAAGC,CAAC;EAC5B;EAEDE,QAAQrB,KAAA,EAAOS,MAAA,EAAQ;IACrB,MAAMQ,CAAA,GAAIjB,KAAA,CAAMiB,CAAA;IAChB,MAAMC,CAAA,GAAIlB,KAAA,CAAMkB,CAAA;IAChB,MAAMC,CAAA,GAAInB,KAAA,CAAMmB,CAAA;IAEhB,MAAMH,CAAA,GAAI,IAAIM,IAAA,CAAKC,GAAA,CAAIN,CAAA,EAAGC,CAAA,EAAGC,CAAC;IAC9B,MAAMN,CAAA,IAAK,IAAII,CAAA,GAAID,CAAA,KAAM,IAAIA,CAAA;IAC7B,MAAMF,CAAA,IAAK,IAAII,CAAA,GAAIF,CAAA,KAAM,IAAIA,CAAA;IAC7B,MAAMD,CAAA,IAAK,IAAII,CAAA,GAAIH,CAAA,KAAM,IAAIA,CAAA;IAE7BP,MAAA,CAAOI,CAAA,GAAIA,CAAA;IACXJ,MAAA,CAAOK,CAAA,GAAIA,CAAA;IACXL,MAAA,CAAOM,CAAA,GAAIA,CAAA;IACXN,MAAA,CAAOO,CAAA,GAAIA,CAAA;IAEX,OAAOP,MAAA;EACR;AACH", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}