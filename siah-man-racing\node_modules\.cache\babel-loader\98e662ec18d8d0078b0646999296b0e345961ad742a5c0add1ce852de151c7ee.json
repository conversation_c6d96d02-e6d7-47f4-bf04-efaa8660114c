{"ast": null, "code": "var __defProp = Object.defineProperty;\nvar __defNormalProp = (obj, key, value) => key in obj ? __defProp(obj, key, {\n  enumerable: true,\n  configurable: true,\n  writable: true,\n  value\n}) : obj[key] = value;\nvar __publicField = (obj, key, value) => {\n  __defNormalProp(obj, typeof key !== \"symbol\" ? key + \"\" : key, value);\n  return value;\n};\nimport { Lo<PERSON>, FileLoader, ShapePath } from \"three\";\nclass FontLoader extends Loader {\n  constructor(manager) {\n    super(manager);\n  }\n  load(url, onLoad, onProgress, onError) {\n    const loader = new FileLoader(this.manager);\n    loader.setPath(this.path);\n    loader.setRequestHeader(this.requestHeader);\n    loader.setWithCredentials(this.withCredentials);\n    loader.load(url, response => {\n      if (typeof response !== \"string\") throw new Error(\"unsupported data type\");\n      const json = JSON.parse(response);\n      const font = this.parse(json);\n      if (onLoad) onLoad(font);\n    }, onProgress, onError);\n  }\n  loadAsync(url, onProgress) {\n    return super.loadAsync(url, onProgress);\n  }\n  parse(json) {\n    return new Font(json);\n  }\n}\nclass Font {\n  constructor(data) {\n    __publicField(this, \"data\");\n    __publicField(this, \"isFont\", true);\n    __publicField(this, \"type\", \"Font\");\n    this.data = data;\n  }\n  generateShapes(text, size = 100, _options) {\n    const shapes = [];\n    const options = {\n      letterSpacing: 0,\n      lineHeight: 1,\n      ..._options\n    };\n    const paths = createPaths(text, size, this.data, options);\n    for (let p = 0, pl = paths.length; p < pl; p++) {\n      Array.prototype.push.apply(shapes, paths[p].toShapes(false));\n    }\n    return shapes;\n  }\n}\nfunction createPaths(text, size, data, options) {\n  const chars = Array.from(text);\n  const scale = size / data.resolution;\n  const line_height = (data.boundingBox.yMax - data.boundingBox.yMin + data.underlineThickness) * scale;\n  const paths = [];\n  let offsetX = 0,\n    offsetY = 0;\n  for (let i = 0; i < chars.length; i++) {\n    const char = chars[i];\n    if (char === \"\\n\") {\n      offsetX = 0;\n      offsetY -= line_height * options.lineHeight;\n    } else {\n      const ret = createPath(char, scale, offsetX, offsetY, data);\n      if (ret) {\n        offsetX += ret.offsetX + options.letterSpacing;\n        paths.push(ret.path);\n      }\n    }\n  }\n  return paths;\n}\nfunction createPath(char, scale, offsetX, offsetY, data) {\n  const glyph = data.glyphs[char] || data.glyphs[\"?\"];\n  if (!glyph) {\n    console.error('THREE.Font: character \"' + char + '\" does not exists in font family ' + data.familyName + \".\");\n    return;\n  }\n  const path = new ShapePath();\n  let x, y, cpx, cpy, cpx1, cpy1, cpx2, cpy2;\n  if (glyph.o) {\n    const outline = glyph._cachedOutline || (glyph._cachedOutline = glyph.o.split(\" \"));\n    for (let i = 0, l = outline.length; i < l;) {\n      const action = outline[i++];\n      switch (action) {\n        case \"m\":\n          x = parseInt(outline[i++]) * scale + offsetX;\n          y = parseInt(outline[i++]) * scale + offsetY;\n          path.moveTo(x, y);\n          break;\n        case \"l\":\n          x = parseInt(outline[i++]) * scale + offsetX;\n          y = parseInt(outline[i++]) * scale + offsetY;\n          path.lineTo(x, y);\n          break;\n        case \"q\":\n          cpx = parseInt(outline[i++]) * scale + offsetX;\n          cpy = parseInt(outline[i++]) * scale + offsetY;\n          cpx1 = parseInt(outline[i++]) * scale + offsetX;\n          cpy1 = parseInt(outline[i++]) * scale + offsetY;\n          path.quadraticCurveTo(cpx1, cpy1, cpx, cpy);\n          break;\n        case \"b\":\n          cpx = parseInt(outline[i++]) * scale + offsetX;\n          cpy = parseInt(outline[i++]) * scale + offsetY;\n          cpx1 = parseInt(outline[i++]) * scale + offsetX;\n          cpy1 = parseInt(outline[i++]) * scale + offsetY;\n          cpx2 = parseInt(outline[i++]) * scale + offsetX;\n          cpy2 = parseInt(outline[i++]) * scale + offsetY;\n          path.bezierCurveTo(cpx1, cpy1, cpx2, cpy2, cpx, cpy);\n          break;\n      }\n    }\n  }\n  return {\n    offsetX: glyph.ha * scale,\n    path\n  };\n}\nexport { Font, FontLoader };", "map": {"version": 3, "names": ["<PERSON><PERSON><PERSON><PERSON><PERSON>", "Loader", "constructor", "manager", "load", "url", "onLoad", "onProgress", "onError", "loader", "<PERSON><PERSON><PERSON><PERSON>", "set<PERSON>ath", "path", "setRequestHeader", "requestHeader", "setWithCredentials", "withCredentials", "response", "Error", "json", "JSON", "parse", "font", "loadAsync", "Font", "data", "__publicField", "generateShapes", "text", "size", "_options", "shapes", "options", "letterSpacing", "lineHeight", "paths", "createPaths", "p", "pl", "length", "Array", "prototype", "push", "apply", "to<PERSON><PERSON><PERSON>", "chars", "from", "scale", "resolution", "line_height", "boundingBox", "yMax", "yMin", "underlineThickness", "offsetX", "offsetY", "i", "char", "ret", "createPath", "glyph", "glyphs", "console", "error", "<PERSON><PERSON>ame", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "x", "y", "cpx", "cpy", "cpx1", "cpy1", "cpx2", "cpy2", "o", "outline", "_cachedOutline", "split", "l", "action", "parseInt", "moveTo", "lineTo", "quadraticCurveTo", "bezierCurveTo", "ha"], "sources": ["C:\\Users\\<USER>\\Documents\\Apps_Creations\\SiahMan_Racing4Speed_WebGame\\siah-man-racing\\node_modules\\src\\loaders\\FontLoader.ts"], "sourcesContent": ["import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON> } from 'three'\n\nimport type { <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON>ha<PERSON> } from 'three'\n\ntype Options = {\n  lineHeight: number\n  letterSpacing: number\n}\n\nexport class FontLoader extends Loader {\n  constructor(manager?: LoadingManager) {\n    super(manager)\n  }\n\n  public load(\n    url: string,\n    onLoad?: (responseFont: Font) => void,\n    onProgress?: (event: ProgressEvent) => void,\n    onError?: (event: ErrorEvent) => void,\n  ): void {\n    const loader = new FileLoader(this.manager)\n\n    loader.setPath(this.path)\n    loader.setRequestHeader(this.requestHeader)\n    loader.setWithCredentials(this.withCredentials)\n\n    loader.load(\n      url,\n      (response) => {\n        if (typeof response !== 'string') throw new Error('unsupported data type')\n\n        const json = JSON.parse(response)\n\n        const font = this.parse(json)\n\n        if (onLoad) onLoad(font)\n      },\n      onProgress,\n      onError as (event: unknown) => void,\n    )\n  }\n\n  loadAsync(url: string, onProgress?: (event: ProgressEvent) => void): Promise<Font> {\n    // @ts-ignore\n    return super.loadAsync(url, onProgress)\n  }\n\n  public parse(json: FontData): Font {\n    return new Font(json)\n  }\n}\n\ntype Glyph = {\n  _cachedOutline: string[]\n  ha: number\n  o: string\n}\n\ntype FontData = {\n  boundingBox: { yMax: number; yMin: number }\n  familyName: string\n  glyphs: { [k: string]: Glyph }\n  resolution: number\n  underlineThickness: number\n}\n\nexport class Font {\n  public data: FontData\n  public isFont = true\n  public type = 'Font'\n\n  constructor(data: FontData) {\n    this.data = data\n  }\n\n  public generateShapes(text: string, size = 100, _options?: Partial<Options>): Shape[] {\n    const shapes: Shape[] = []\n    const options = { letterSpacing: 0, lineHeight: 1, ..._options }\n    const paths = createPaths(text, size, this.data, options)\n    for (let p = 0, pl = paths.length; p < pl; p++) {\n      Array.prototype.push.apply(shapes, paths[p].toShapes(false))\n    }\n    return shapes\n  }\n}\n\nfunction createPaths(text: string, size: number, data: FontData, options: Options): ShapePath[] {\n  const chars = Array.from(text)\n  const scale = size / data.resolution\n  const line_height = (data.boundingBox.yMax - data.boundingBox.yMin + data.underlineThickness) * scale\n\n  const paths: ShapePath[] = []\n\n  let offsetX = 0,\n    offsetY = 0\n\n  for (let i = 0; i < chars.length; i++) {\n    const char = chars[i]\n\n    if (char === '\\n') {\n      offsetX = 0\n      offsetY -= line_height * options.lineHeight\n    } else {\n      const ret = createPath(char, scale, offsetX, offsetY, data)\n      if (ret) {\n        offsetX += ret.offsetX + options.letterSpacing\n        paths.push(ret.path)\n      }\n    }\n  }\n\n  return paths\n}\n\nfunction createPath(\n  char: string,\n  scale: number,\n  offsetX: number,\n  offsetY: number,\n  data: FontData,\n): { offsetX: number; path: ShapePath } | undefined {\n  const glyph = data.glyphs[char] || data.glyphs['?']\n\n  if (!glyph) {\n    console.error('THREE.Font: character \"' + char + '\" does not exists in font family ' + data.familyName + '.')\n    return\n  }\n\n  const path = new ShapePath()\n\n  let x, y, cpx, cpy, cpx1, cpy1, cpx2, cpy2\n\n  if (glyph.o) {\n    const outline = glyph._cachedOutline || (glyph._cachedOutline = glyph.o.split(' '))\n\n    for (let i = 0, l = outline.length; i < l; ) {\n      const action = outline[i++]\n\n      switch (action) {\n        case 'm': // moveTo\n          x = parseInt(outline[i++]) * scale + offsetX\n          y = parseInt(outline[i++]) * scale + offsetY\n\n          path.moveTo(x, y)\n\n          break\n\n        case 'l': // lineTo\n          x = parseInt(outline[i++]) * scale + offsetX\n          y = parseInt(outline[i++]) * scale + offsetY\n\n          path.lineTo(x, y)\n\n          break\n\n        case 'q': // quadraticCurveTo\n          cpx = parseInt(outline[i++]) * scale + offsetX\n          cpy = parseInt(outline[i++]) * scale + offsetY\n          cpx1 = parseInt(outline[i++]) * scale + offsetX\n          cpy1 = parseInt(outline[i++]) * scale + offsetY\n\n          path.quadraticCurveTo(cpx1, cpy1, cpx, cpy)\n\n          break\n\n        case 'b': // bezierCurveTo\n          cpx = parseInt(outline[i++]) * scale + offsetX\n          cpy = parseInt(outline[i++]) * scale + offsetY\n          cpx1 = parseInt(outline[i++]) * scale + offsetX\n          cpy1 = parseInt(outline[i++]) * scale + offsetY\n          cpx2 = parseInt(outline[i++]) * scale + offsetX\n          cpy2 = parseInt(outline[i++]) * scale + offsetY\n\n          path.bezierCurveTo(cpx1, cpy1, cpx2, cpy2, cpx, cpy)\n\n          break\n      }\n    }\n  }\n\n  return { offsetX: glyph.ha * scale, path }\n}\n"], "mappings": ";;;;;;;;;;;;AASO,MAAMA,UAAA,SAAmBC,MAAA,CAAO;EACrCC,YAAYC,OAAA,EAA0B;IACpC,MAAMA,OAAO;EACf;EAEOC,KACLC,GAAA,EACAC,MAAA,EACAC,UAAA,EACAC,OAAA,EACM;IACN,MAAMC,MAAA,GAAS,IAAIC,UAAA,CAAW,KAAKP,OAAO;IAEnCM,MAAA,CAAAE,OAAA,CAAQ,KAAKC,IAAI;IACjBH,MAAA,CAAAI,gBAAA,CAAiB,KAAKC,aAAa;IACnCL,MAAA,CAAAM,kBAAA,CAAmB,KAAKC,eAAe;IAEvCP,MAAA,CAAAL,IAAA,CACLC,GAAA,EACCY,QAAA,IAAa;MACZ,IAAI,OAAOA,QAAA,KAAa,UAAgB,UAAIC,KAAA,CAAM,uBAAuB;MAEnE,MAAAC,IAAA,GAAOC,IAAA,CAAKC,KAAA,CAAMJ,QAAQ;MAE1B,MAAAK,IAAA,GAAO,KAAKD,KAAA,CAAMF,IAAI;MAExB,IAAAb,MAAA,EAAQA,MAAA,CAAOgB,IAAI;IACzB,GACAf,UAAA,EACAC,OAAA;EAEJ;EAEAe,UAAUlB,GAAA,EAAaE,UAAA,EAA4D;IAE1E,aAAMgB,SAAA,CAAUlB,GAAA,EAAKE,UAAU;EACxC;EAEOc,MAAMF,IAAA,EAAsB;IAC1B,WAAIK,IAAA,CAAKL,IAAI;EACtB;AACF;AAgBO,MAAMK,IAAA,CAAK;EAKhBtB,YAAYuB,IAAA,EAAgB;IAJrBC,aAAA;IACAA,aAAA,iBAAS;IACTA,aAAA,eAAO;IAGZ,KAAKD,IAAA,GAAOA,IAAA;EACd;EAEOE,eAAeC,IAAA,EAAcC,IAAA,GAAO,KAAKC,QAAA,EAAsC;IACpF,MAAMC,MAAA,GAAkB;IACxB,MAAMC,OAAA,GAAU;MAAEC,aAAA,EAAe;MAAGC,UAAA,EAAY;MAAG,GAAGJ;IAAA;IACtD,MAAMK,KAAA,GAAQC,WAAA,CAAYR,IAAA,EAAMC,IAAA,EAAM,KAAKJ,IAAA,EAAMO,OAAO;IACxD,SAASK,CAAA,GAAI,GAAGC,EAAA,GAAKH,KAAA,CAAMI,MAAA,EAAQF,CAAA,GAAIC,EAAA,EAAID,CAAA,IAAK;MACxCG,KAAA,CAAAC,SAAA,CAAUC,IAAA,CAAKC,KAAA,CAAMZ,MAAA,EAAQI,KAAA,CAAME,CAAC,EAAEO,QAAA,CAAS,KAAK,CAAC;IAC7D;IACO,OAAAb,MAAA;EACT;AACF;AAEA,SAASK,YAAYR,IAAA,EAAcC,IAAA,EAAcJ,IAAA,EAAgBO,OAAA,EAA+B;EACxF,MAAAa,KAAA,GAAQL,KAAA,CAAMM,IAAA,CAAKlB,IAAI;EACvB,MAAAmB,KAAA,GAAQlB,IAAA,GAAOJ,IAAA,CAAKuB,UAAA;EACpB,MAAAC,WAAA,IAAexB,IAAA,CAAKyB,WAAA,CAAYC,IAAA,GAAO1B,IAAA,CAAKyB,WAAA,CAAYE,IAAA,GAAO3B,IAAA,CAAK4B,kBAAA,IAAsBN,KAAA;EAEhG,MAAMZ,KAAA,GAAqB;EAEvB,IAAAmB,OAAA,GAAU;IACZC,OAAA,GAAU;EAEZ,SAASC,CAAA,GAAI,GAAGA,CAAA,GAAIX,KAAA,CAAMN,MAAA,EAAQiB,CAAA,IAAK;IAC/B,MAAAC,IAAA,GAAOZ,KAAA,CAAMW,CAAC;IAEpB,IAAIC,IAAA,KAAS,MAAM;MACPH,OAAA;MACVC,OAAA,IAAWN,WAAA,GAAcjB,OAAA,CAAQE,UAAA;IAAA,OAC5B;MACL,MAAMwB,GAAA,GAAMC,UAAA,CAAWF,IAAA,EAAMV,KAAA,EAAOO,OAAA,EAASC,OAAA,EAAS9B,IAAI;MAC1D,IAAIiC,GAAA,EAAK;QACIJ,OAAA,IAAAI,GAAA,CAAIJ,OAAA,GAAUtB,OAAA,CAAQC,aAAA;QAC3BE,KAAA,CAAAO,IAAA,CAAKgB,GAAA,CAAI9C,IAAI;MACrB;IACF;EACF;EAEO,OAAAuB,KAAA;AACT;AAEA,SAASwB,WACPF,IAAA,EACAV,KAAA,EACAO,OAAA,EACAC,OAAA,EACA9B,IAAA,EACkD;EAClD,MAAMmC,KAAA,GAAQnC,IAAA,CAAKoC,MAAA,CAAOJ,IAAI,KAAKhC,IAAA,CAAKoC,MAAA,CAAO,GAAG;EAElD,IAAI,CAACD,KAAA,EAAO;IACVE,OAAA,CAAQC,KAAA,CAAM,4BAA4BN,IAAA,GAAO,sCAAsChC,IAAA,CAAKuC,UAAA,GAAa,GAAG;IAC5G;EACF;EAEM,MAAApD,IAAA,GAAO,IAAIqD,SAAA;EAEjB,IAAIC,CAAA,EAAGC,CAAA,EAAGC,GAAA,EAAKC,GAAA,EAAKC,IAAA,EAAMC,IAAA,EAAMC,IAAA,EAAMC,IAAA;EAEtC,IAAIb,KAAA,CAAMc,CAAA,EAAG;IACL,MAAAC,OAAA,GAAUf,KAAA,CAAMgB,cAAA,KAAmBhB,KAAA,CAAMgB,cAAA,GAAiBhB,KAAA,CAAMc,CAAA,CAAEG,KAAA,CAAM,GAAG;IAEjF,SAASrB,CAAA,GAAI,GAAGsB,CAAA,GAAIH,OAAA,CAAQpC,MAAA,EAAQiB,CAAA,GAAIsB,CAAA,GAAK;MACrC,MAAAC,MAAA,GAASJ,OAAA,CAAQnB,CAAA,EAAG;MAE1B,QAAQuB,MAAA;QACN,KAAK;UACHb,CAAA,GAAIc,QAAA,CAASL,OAAA,CAAQnB,CAAA,EAAG,CAAC,IAAIT,KAAA,GAAQO,OAAA;UACrCa,CAAA,GAAIa,QAAA,CAASL,OAAA,CAAQnB,CAAA,EAAG,CAAC,IAAIT,KAAA,GAAQQ,OAAA;UAEhC3C,IAAA,CAAAqE,MAAA,CAAOf,CAAA,EAAGC,CAAC;UAEhB;QAEF,KAAK;UACHD,CAAA,GAAIc,QAAA,CAASL,OAAA,CAAQnB,CAAA,EAAG,CAAC,IAAIT,KAAA,GAAQO,OAAA;UACrCa,CAAA,GAAIa,QAAA,CAASL,OAAA,CAAQnB,CAAA,EAAG,CAAC,IAAIT,KAAA,GAAQQ,OAAA;UAEhC3C,IAAA,CAAAsE,MAAA,CAAOhB,CAAA,EAAGC,CAAC;UAEhB;QAEF,KAAK;UACHC,GAAA,GAAMY,QAAA,CAASL,OAAA,CAAQnB,CAAA,EAAG,CAAC,IAAIT,KAAA,GAAQO,OAAA;UACvCe,GAAA,GAAMW,QAAA,CAASL,OAAA,CAAQnB,CAAA,EAAG,CAAC,IAAIT,KAAA,GAAQQ,OAAA;UACvCe,IAAA,GAAOU,QAAA,CAASL,OAAA,CAAQnB,CAAA,EAAG,CAAC,IAAIT,KAAA,GAAQO,OAAA;UACxCiB,IAAA,GAAOS,QAAA,CAASL,OAAA,CAAQnB,CAAA,EAAG,CAAC,IAAIT,KAAA,GAAQQ,OAAA;UAExC3C,IAAA,CAAKuE,gBAAA,CAAiBb,IAAA,EAAMC,IAAA,EAAMH,GAAA,EAAKC,GAAG;UAE1C;QAEF,KAAK;UACHD,GAAA,GAAMY,QAAA,CAASL,OAAA,CAAQnB,CAAA,EAAG,CAAC,IAAIT,KAAA,GAAQO,OAAA;UACvCe,GAAA,GAAMW,QAAA,CAASL,OAAA,CAAQnB,CAAA,EAAG,CAAC,IAAIT,KAAA,GAAQQ,OAAA;UACvCe,IAAA,GAAOU,QAAA,CAASL,OAAA,CAAQnB,CAAA,EAAG,CAAC,IAAIT,KAAA,GAAQO,OAAA;UACxCiB,IAAA,GAAOS,QAAA,CAASL,OAAA,CAAQnB,CAAA,EAAG,CAAC,IAAIT,KAAA,GAAQQ,OAAA;UACxCiB,IAAA,GAAOQ,QAAA,CAASL,OAAA,CAAQnB,CAAA,EAAG,CAAC,IAAIT,KAAA,GAAQO,OAAA;UACxCmB,IAAA,GAAOO,QAAA,CAASL,OAAA,CAAQnB,CAAA,EAAG,CAAC,IAAIT,KAAA,GAAQQ,OAAA;UAExC3C,IAAA,CAAKwE,aAAA,CAAcd,IAAA,EAAMC,IAAA,EAAMC,IAAA,EAAMC,IAAA,EAAML,GAAA,EAAKC,GAAG;UAEnD;MACJ;IACF;EACF;EAEA,OAAO;IAAEf,OAAA,EAASM,KAAA,CAAMyB,EAAA,GAAKtC,KAAA;IAAOnC;EAAK;AAC3C", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}