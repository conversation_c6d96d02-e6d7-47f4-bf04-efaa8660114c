{"ast": null, "code": "import { useThree, useLoader } from '@react-three/fiber';\nimport { CubeReflectionMapping, EquirectangularReflectionMapping, CubeTextureLoader } from 'three';\nimport { RGBELoader, EXRLoader } from 'three-stdlib';\nimport { HDRJPGLoader, GainMapLoader } from '@monogrid/gainmap-js';\nimport { presetsObj } from '../helpers/environment-assets.js';\nimport { useLayoutEffect } from 'react';\nconst CUBEMAP_ROOT = 'https://raw.githack.com/pmndrs/drei-assets/456060a26bbeb8fdf79326f224b6d99b8bcce736/hdri/';\nconst isArray = arr => Array.isArray(arr);\nconst defaultFiles = ['/px.png', '/nx.png', '/py.png', '/ny.png', '/pz.png', '/nz.png'];\nfunction useEnvironment({\n  files = defaultFiles,\n  path = '',\n  preset = undefined,\n  colorSpace = undefined,\n  extensions\n} = {}) {\n  if (preset) {\n    validatePreset(preset);\n    files = presetsObj[preset];\n    path = CUBEMAP_ROOT;\n  }\n\n  // Everything else\n  const multiFile = isArray(files);\n  const {\n    extension,\n    isCubemap\n  } = getExtension(files);\n  const loader = getLoader(extension);\n  if (!loader) throw new Error('useEnvironment: Unrecognized file extension: ' + files);\n  const gl = useThree(state => state.gl);\n  useLayoutEffect(() => {\n    // Only required for gainmap\n    if (extension !== 'webp' && extension !== 'jpg' && extension !== 'jpeg') return;\n    function clearGainmapTexture() {\n      useLoader.clear(loader, multiFile ? [files] : files);\n    }\n    gl.domElement.addEventListener('webglcontextlost', clearGainmapTexture, {\n      once: true\n    });\n  }, [files, gl.domElement]);\n  const loaderResult = useLoader(loader, multiFile ? [files] : files, loader => {\n    // Gainmap requires a renderer\n    if (extension === 'webp' || extension === 'jpg' || extension === 'jpeg') {\n      // @ts-expect-error\n      loader.setRenderer(gl);\n    }\n    loader.setPath == null || loader.setPath(path);\n    // @ts-expect-error\n    if (extensions) extensions(loader);\n  });\n  let texture = multiFile ?\n  // @ts-ignore\n  loaderResult[0] : loaderResult;\n  if (extension === 'jpg' || extension === 'jpeg' || extension === 'webp') {\n    var _renderTarget;\n    texture = (_renderTarget = texture.renderTarget) == null ? void 0 : _renderTarget.texture;\n  }\n  texture.mapping = isCubemap ? CubeReflectionMapping : EquirectangularReflectionMapping;\n  texture.colorSpace = colorSpace !== null && colorSpace !== void 0 ? colorSpace : isCubemap ? 'srgb' : 'srgb-linear';\n  return texture;\n}\nconst preloadDefaultOptions = {\n  files: defaultFiles,\n  path: '',\n  preset: undefined,\n  extensions: undefined\n};\nuseEnvironment.preload = preloadOptions => {\n  const options = {\n    ...preloadDefaultOptions,\n    ...preloadOptions\n  };\n  let {\n    files,\n    path = ''\n  } = options;\n  const {\n    preset,\n    extensions\n  } = options;\n  if (preset) {\n    validatePreset(preset);\n    files = presetsObj[preset];\n    path = CUBEMAP_ROOT;\n  }\n  const {\n    extension\n  } = getExtension(files);\n  if (extension === 'webp' || extension === 'jpg' || extension === 'jpeg') {\n    throw new Error('useEnvironment: Preloading gainmaps is not supported');\n  }\n  const loader = getLoader(extension);\n  if (!loader) throw new Error('useEnvironment: Unrecognized file extension: ' + files);\n  useLoader.preload(loader, isArray(files) ? [files] : files, loader => {\n    loader.setPath == null || loader.setPath(path);\n    // @ts-expect-error\n    if (extensions) extensions(loader);\n  });\n};\nconst clearDefaultOptins = {\n  files: defaultFiles,\n  preset: undefined\n};\nuseEnvironment.clear = clearOptions => {\n  const options = {\n    ...clearDefaultOptins,\n    ...clearOptions\n  };\n  let {\n    files\n  } = options;\n  const {\n    preset\n  } = options;\n  if (preset) {\n    validatePreset(preset);\n    files = presetsObj[preset];\n  }\n  const {\n    extension\n  } = getExtension(files);\n  const loader = getLoader(extension);\n  if (!loader) throw new Error('useEnvironment: Unrecognized file extension: ' + files);\n  useLoader.clear(loader, isArray(files) ? [files] : files);\n};\nfunction validatePreset(preset) {\n  if (!(preset in presetsObj)) throw new Error('Preset must be one of: ' + Object.keys(presetsObj).join(', '));\n}\nfunction getExtension(files) {\n  var _firstEntry$split$pop;\n  const isCubemap = isArray(files) && files.length === 6;\n  const isGainmap = isArray(files) && files.length === 3 && files.some(file => file.endsWith('json'));\n  const firstEntry = isArray(files) ? files[0] : files;\n\n  // Everything else\n  const extension = isCubemap ? 'cube' : isGainmap ? 'webp' : firstEntry.startsWith('data:application/exr') ? 'exr' : firstEntry.startsWith('data:application/hdr') ? 'hdr' : firstEntry.startsWith('data:image/jpeg') ? 'jpg' : (_firstEntry$split$pop = firstEntry.split('.').pop()) == null || (_firstEntry$split$pop = _firstEntry$split$pop.split('?')) == null || (_firstEntry$split$pop = _firstEntry$split$pop.shift()) == null ? void 0 : _firstEntry$split$pop.toLowerCase();\n  return {\n    extension,\n    isCubemap,\n    isGainmap\n  };\n}\nfunction getLoader(extension) {\n  const loader = extension === 'cube' ? CubeTextureLoader : extension === 'hdr' ? RGBELoader : extension === 'exr' ? EXRLoader : extension === 'jpg' || extension === 'jpeg' ? HDRJPGLoader : extension === 'webp' ? GainMapLoader : null;\n  return loader;\n}\nexport { useEnvironment };", "map": {"version": 3, "names": ["useThree", "useLoader", "CubeReflectionMapping", "EquirectangularReflectionMapping", "CubeTextureLoader", "RGBELoader", "EXRLoader", "HDRJPGLoader", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "presetsObj", "useLayoutEffect", "CUBEMAP_ROOT", "isArray", "arr", "Array", "defaultFiles", "useEnvironment", "files", "path", "preset", "undefined", "colorSpace", "extensions", "validatePreset", "multiFile", "extension", "isCubemap", "getExtension", "loader", "<PERSON><PERSON><PERSON><PERSON>", "Error", "gl", "state", "clearGainmapTexture", "clear", "dom<PERSON>lement", "addEventListener", "once", "loaderResult", "<PERSON><PERSON><PERSON><PERSON>", "set<PERSON>ath", "texture", "_renderTarget", "renderTarget", "mapping", "preloadDefaultOptions", "preload", "preloadOptions", "options", "clearDefaultOptins", "clearOptions", "Object", "keys", "join", "_firstEntry$split$pop", "length", "isGainmap", "some", "file", "endsWith", "firstEntry", "startsWith", "split", "pop", "shift", "toLowerCase"], "sources": ["C:/Users/<USER>/Documents/Apps_Creations/SiahMan_Racing4Speed_WebGame/siah-man-racing/node_modules/@react-three/drei/core/useEnvironment.js"], "sourcesContent": ["import { useThree, useLoader } from '@react-three/fiber';\nimport { CubeReflectionMapping, EquirectangularReflectionMapping, CubeTextureLoader } from 'three';\nimport { RGBELoader, EXRLoader } from 'three-stdlib';\nimport { HDRJPGLoader, GainMapLoader } from '@monogrid/gainmap-js';\nimport { presetsObj } from '../helpers/environment-assets.js';\nimport { useLayoutEffect } from 'react';\n\nconst CUBEMAP_ROOT = 'https://raw.githack.com/pmndrs/drei-assets/456060a26bbeb8fdf79326f224b6d99b8bcce736/hdri/';\nconst isArray = arr => Array.isArray(arr);\nconst defaultFiles = ['/px.png', '/nx.png', '/py.png', '/ny.png', '/pz.png', '/nz.png'];\nfunction useEnvironment({\n  files = defaultFiles,\n  path = '',\n  preset = undefined,\n  colorSpace = undefined,\n  extensions\n} = {}) {\n  if (preset) {\n    validatePreset(preset);\n    files = presetsObj[preset];\n    path = CUBEMAP_ROOT;\n  }\n\n  // Everything else\n  const multiFile = isArray(files);\n  const {\n    extension,\n    isCubemap\n  } = getExtension(files);\n  const loader = getLoader(extension);\n  if (!loader) throw new Error('useEnvironment: Unrecognized file extension: ' + files);\n  const gl = useThree(state => state.gl);\n  useLayoutEffect(() => {\n    // Only required for gainmap\n    if (extension !== 'webp' && extension !== 'jpg' && extension !== 'jpeg') return;\n    function clearGainmapTexture() {\n      useLoader.clear(loader, multiFile ? [files] : files);\n    }\n    gl.domElement.addEventListener('webglcontextlost', clearGainmapTexture, {\n      once: true\n    });\n  }, [files, gl.domElement]);\n  const loaderResult = useLoader(loader, multiFile ? [files] : files, loader => {\n    // Gainmap requires a renderer\n    if (extension === 'webp' || extension === 'jpg' || extension === 'jpeg') {\n      // @ts-expect-error\n      loader.setRenderer(gl);\n    }\n    loader.setPath == null || loader.setPath(path);\n    // @ts-expect-error\n    if (extensions) extensions(loader);\n  });\n  let texture = multiFile ?\n  // @ts-ignore\n  loaderResult[0] : loaderResult;\n  if (extension === 'jpg' || extension === 'jpeg' || extension === 'webp') {\n    var _renderTarget;\n    texture = (_renderTarget = texture.renderTarget) == null ? void 0 : _renderTarget.texture;\n  }\n  texture.mapping = isCubemap ? CubeReflectionMapping : EquirectangularReflectionMapping;\n  texture.colorSpace = colorSpace !== null && colorSpace !== void 0 ? colorSpace : isCubemap ? 'srgb' : 'srgb-linear';\n  return texture;\n}\nconst preloadDefaultOptions = {\n  files: defaultFiles,\n  path: '',\n  preset: undefined,\n  extensions: undefined\n};\nuseEnvironment.preload = preloadOptions => {\n  const options = {\n    ...preloadDefaultOptions,\n    ...preloadOptions\n  };\n  let {\n    files,\n    path = ''\n  } = options;\n  const {\n    preset,\n    extensions\n  } = options;\n  if (preset) {\n    validatePreset(preset);\n    files = presetsObj[preset];\n    path = CUBEMAP_ROOT;\n  }\n  const {\n    extension\n  } = getExtension(files);\n  if (extension === 'webp' || extension === 'jpg' || extension === 'jpeg') {\n    throw new Error('useEnvironment: Preloading gainmaps is not supported');\n  }\n  const loader = getLoader(extension);\n  if (!loader) throw new Error('useEnvironment: Unrecognized file extension: ' + files);\n  useLoader.preload(loader, isArray(files) ? [files] : files, loader => {\n    loader.setPath == null || loader.setPath(path);\n    // @ts-expect-error\n    if (extensions) extensions(loader);\n  });\n};\nconst clearDefaultOptins = {\n  files: defaultFiles,\n  preset: undefined\n};\nuseEnvironment.clear = clearOptions => {\n  const options = {\n    ...clearDefaultOptins,\n    ...clearOptions\n  };\n  let {\n    files\n  } = options;\n  const {\n    preset\n  } = options;\n  if (preset) {\n    validatePreset(preset);\n    files = presetsObj[preset];\n  }\n  const {\n    extension\n  } = getExtension(files);\n  const loader = getLoader(extension);\n  if (!loader) throw new Error('useEnvironment: Unrecognized file extension: ' + files);\n  useLoader.clear(loader, isArray(files) ? [files] : files);\n};\nfunction validatePreset(preset) {\n  if (!(preset in presetsObj)) throw new Error('Preset must be one of: ' + Object.keys(presetsObj).join(', '));\n}\nfunction getExtension(files) {\n  var _firstEntry$split$pop;\n  const isCubemap = isArray(files) && files.length === 6;\n  const isGainmap = isArray(files) && files.length === 3 && files.some(file => file.endsWith('json'));\n  const firstEntry = isArray(files) ? files[0] : files;\n\n  // Everything else\n  const extension = isCubemap ? 'cube' : isGainmap ? 'webp' : firstEntry.startsWith('data:application/exr') ? 'exr' : firstEntry.startsWith('data:application/hdr') ? 'hdr' : firstEntry.startsWith('data:image/jpeg') ? 'jpg' : (_firstEntry$split$pop = firstEntry.split('.').pop()) == null || (_firstEntry$split$pop = _firstEntry$split$pop.split('?')) == null || (_firstEntry$split$pop = _firstEntry$split$pop.shift()) == null ? void 0 : _firstEntry$split$pop.toLowerCase();\n  return {\n    extension,\n    isCubemap,\n    isGainmap\n  };\n}\nfunction getLoader(extension) {\n  const loader = extension === 'cube' ? CubeTextureLoader : extension === 'hdr' ? RGBELoader : extension === 'exr' ? EXRLoader : extension === 'jpg' || extension === 'jpeg' ? HDRJPGLoader : extension === 'webp' ? GainMapLoader : null;\n  return loader;\n}\n\nexport { useEnvironment };\n"], "mappings": "AAAA,SAASA,QAAQ,EAAEC,SAAS,QAAQ,oBAAoB;AACxD,SAASC,qBAAqB,EAAEC,gCAAgC,EAAEC,iBAAiB,QAAQ,OAAO;AAClG,SAASC,UAAU,EAAEC,SAAS,QAAQ,cAAc;AACpD,SAASC,YAAY,EAAEC,aAAa,QAAQ,sBAAsB;AAClE,SAASC,UAAU,QAAQ,kCAAkC;AAC7D,SAASC,eAAe,QAAQ,OAAO;AAEvC,MAAMC,YAAY,GAAG,2FAA2F;AAChH,MAAMC,OAAO,GAAGC,GAAG,IAAIC,KAAK,CAACF,OAAO,CAACC,GAAG,CAAC;AACzC,MAAME,YAAY,GAAG,CAAC,SAAS,EAAE,SAAS,EAAE,SAAS,EAAE,SAAS,EAAE,SAAS,EAAE,SAAS,CAAC;AACvF,SAASC,cAAcA,CAAC;EACtBC,KAAK,GAAGF,YAAY;EACpBG,IAAI,GAAG,EAAE;EACTC,MAAM,GAAGC,SAAS;EAClBC,UAAU,GAAGD,SAAS;EACtBE;AACF,CAAC,GAAG,CAAC,CAAC,EAAE;EACN,IAAIH,MAAM,EAAE;IACVI,cAAc,CAACJ,MAAM,CAAC;IACtBF,KAAK,GAAGR,UAAU,CAACU,MAAM,CAAC;IAC1BD,IAAI,GAAGP,YAAY;EACrB;;EAEA;EACA,MAAMa,SAAS,GAAGZ,OAAO,CAACK,KAAK,CAAC;EAChC,MAAM;IACJQ,SAAS;IACTC;EACF,CAAC,GAAGC,YAAY,CAACV,KAAK,CAAC;EACvB,MAAMW,MAAM,GAAGC,SAAS,CAACJ,SAAS,CAAC;EACnC,IAAI,CAACG,MAAM,EAAE,MAAM,IAAIE,KAAK,CAAC,+CAA+C,GAAGb,KAAK,CAAC;EACrF,MAAMc,EAAE,GAAG/B,QAAQ,CAACgC,KAAK,IAAIA,KAAK,CAACD,EAAE,CAAC;EACtCrB,eAAe,CAAC,MAAM;IACpB;IACA,IAAIe,SAAS,KAAK,MAAM,IAAIA,SAAS,KAAK,KAAK,IAAIA,SAAS,KAAK,MAAM,EAAE;IACzE,SAASQ,mBAAmBA,CAAA,EAAG;MAC7BhC,SAAS,CAACiC,KAAK,CAACN,MAAM,EAAEJ,SAAS,GAAG,CAACP,KAAK,CAAC,GAAGA,KAAK,CAAC;IACtD;IACAc,EAAE,CAACI,UAAU,CAACC,gBAAgB,CAAC,kBAAkB,EAAEH,mBAAmB,EAAE;MACtEI,IAAI,EAAE;IACR,CAAC,CAAC;EACJ,CAAC,EAAE,CAACpB,KAAK,EAAEc,EAAE,CAACI,UAAU,CAAC,CAAC;EAC1B,MAAMG,YAAY,GAAGrC,SAAS,CAAC2B,MAAM,EAAEJ,SAAS,GAAG,CAACP,KAAK,CAAC,GAAGA,KAAK,EAAEW,MAAM,IAAI;IAC5E;IACA,IAAIH,SAAS,KAAK,MAAM,IAAIA,SAAS,KAAK,KAAK,IAAIA,SAAS,KAAK,MAAM,EAAE;MACvE;MACAG,MAAM,CAACW,WAAW,CAACR,EAAE,CAAC;IACxB;IACAH,MAAM,CAACY,OAAO,IAAI,IAAI,IAAIZ,MAAM,CAACY,OAAO,CAACtB,IAAI,CAAC;IAC9C;IACA,IAAII,UAAU,EAAEA,UAAU,CAACM,MAAM,CAAC;EACpC,CAAC,CAAC;EACF,IAAIa,OAAO,GAAGjB,SAAS;EACvB;EACAc,YAAY,CAAC,CAAC,CAAC,GAAGA,YAAY;EAC9B,IAAIb,SAAS,KAAK,KAAK,IAAIA,SAAS,KAAK,MAAM,IAAIA,SAAS,KAAK,MAAM,EAAE;IACvE,IAAIiB,aAAa;IACjBD,OAAO,GAAG,CAACC,aAAa,GAAGD,OAAO,CAACE,YAAY,KAAK,IAAI,GAAG,KAAK,CAAC,GAAGD,aAAa,CAACD,OAAO;EAC3F;EACAA,OAAO,CAACG,OAAO,GAAGlB,SAAS,GAAGxB,qBAAqB,GAAGC,gCAAgC;EACtFsC,OAAO,CAACpB,UAAU,GAAGA,UAAU,KAAK,IAAI,IAAIA,UAAU,KAAK,KAAK,CAAC,GAAGA,UAAU,GAAGK,SAAS,GAAG,MAAM,GAAG,aAAa;EACnH,OAAOe,OAAO;AAChB;AACA,MAAMI,qBAAqB,GAAG;EAC5B5B,KAAK,EAAEF,YAAY;EACnBG,IAAI,EAAE,EAAE;EACRC,MAAM,EAAEC,SAAS;EACjBE,UAAU,EAAEF;AACd,CAAC;AACDJ,cAAc,CAAC8B,OAAO,GAAGC,cAAc,IAAI;EACzC,MAAMC,OAAO,GAAG;IACd,GAAGH,qBAAqB;IACxB,GAAGE;EACL,CAAC;EACD,IAAI;IACF9B,KAAK;IACLC,IAAI,GAAG;EACT,CAAC,GAAG8B,OAAO;EACX,MAAM;IACJ7B,MAAM;IACNG;EACF,CAAC,GAAG0B,OAAO;EACX,IAAI7B,MAAM,EAAE;IACVI,cAAc,CAACJ,MAAM,CAAC;IACtBF,KAAK,GAAGR,UAAU,CAACU,MAAM,CAAC;IAC1BD,IAAI,GAAGP,YAAY;EACrB;EACA,MAAM;IACJc;EACF,CAAC,GAAGE,YAAY,CAACV,KAAK,CAAC;EACvB,IAAIQ,SAAS,KAAK,MAAM,IAAIA,SAAS,KAAK,KAAK,IAAIA,SAAS,KAAK,MAAM,EAAE;IACvE,MAAM,IAAIK,KAAK,CAAC,sDAAsD,CAAC;EACzE;EACA,MAAMF,MAAM,GAAGC,SAAS,CAACJ,SAAS,CAAC;EACnC,IAAI,CAACG,MAAM,EAAE,MAAM,IAAIE,KAAK,CAAC,+CAA+C,GAAGb,KAAK,CAAC;EACrFhB,SAAS,CAAC6C,OAAO,CAAClB,MAAM,EAAEhB,OAAO,CAACK,KAAK,CAAC,GAAG,CAACA,KAAK,CAAC,GAAGA,KAAK,EAAEW,MAAM,IAAI;IACpEA,MAAM,CAACY,OAAO,IAAI,IAAI,IAAIZ,MAAM,CAACY,OAAO,CAACtB,IAAI,CAAC;IAC9C;IACA,IAAII,UAAU,EAAEA,UAAU,CAACM,MAAM,CAAC;EACpC,CAAC,CAAC;AACJ,CAAC;AACD,MAAMqB,kBAAkB,GAAG;EACzBhC,KAAK,EAAEF,YAAY;EACnBI,MAAM,EAAEC;AACV,CAAC;AACDJ,cAAc,CAACkB,KAAK,GAAGgB,YAAY,IAAI;EACrC,MAAMF,OAAO,GAAG;IACd,GAAGC,kBAAkB;IACrB,GAAGC;EACL,CAAC;EACD,IAAI;IACFjC;EACF,CAAC,GAAG+B,OAAO;EACX,MAAM;IACJ7B;EACF,CAAC,GAAG6B,OAAO;EACX,IAAI7B,MAAM,EAAE;IACVI,cAAc,CAACJ,MAAM,CAAC;IACtBF,KAAK,GAAGR,UAAU,CAACU,MAAM,CAAC;EAC5B;EACA,MAAM;IACJM;EACF,CAAC,GAAGE,YAAY,CAACV,KAAK,CAAC;EACvB,MAAMW,MAAM,GAAGC,SAAS,CAACJ,SAAS,CAAC;EACnC,IAAI,CAACG,MAAM,EAAE,MAAM,IAAIE,KAAK,CAAC,+CAA+C,GAAGb,KAAK,CAAC;EACrFhB,SAAS,CAACiC,KAAK,CAACN,MAAM,EAAEhB,OAAO,CAACK,KAAK,CAAC,GAAG,CAACA,KAAK,CAAC,GAAGA,KAAK,CAAC;AAC3D,CAAC;AACD,SAASM,cAAcA,CAACJ,MAAM,EAAE;EAC9B,IAAI,EAAEA,MAAM,IAAIV,UAAU,CAAC,EAAE,MAAM,IAAIqB,KAAK,CAAC,yBAAyB,GAAGqB,MAAM,CAACC,IAAI,CAAC3C,UAAU,CAAC,CAAC4C,IAAI,CAAC,IAAI,CAAC,CAAC;AAC9G;AACA,SAAS1B,YAAYA,CAACV,KAAK,EAAE;EAC3B,IAAIqC,qBAAqB;EACzB,MAAM5B,SAAS,GAAGd,OAAO,CAACK,KAAK,CAAC,IAAIA,KAAK,CAACsC,MAAM,KAAK,CAAC;EACtD,MAAMC,SAAS,GAAG5C,OAAO,CAACK,KAAK,CAAC,IAAIA,KAAK,CAACsC,MAAM,KAAK,CAAC,IAAItC,KAAK,CAACwC,IAAI,CAACC,IAAI,IAAIA,IAAI,CAACC,QAAQ,CAAC,MAAM,CAAC,CAAC;EACnG,MAAMC,UAAU,GAAGhD,OAAO,CAACK,KAAK,CAAC,GAAGA,KAAK,CAAC,CAAC,CAAC,GAAGA,KAAK;;EAEpD;EACA,MAAMQ,SAAS,GAAGC,SAAS,GAAG,MAAM,GAAG8B,SAAS,GAAG,MAAM,GAAGI,UAAU,CAACC,UAAU,CAAC,sBAAsB,CAAC,GAAG,KAAK,GAAGD,UAAU,CAACC,UAAU,CAAC,sBAAsB,CAAC,GAAG,KAAK,GAAGD,UAAU,CAACC,UAAU,CAAC,iBAAiB,CAAC,GAAG,KAAK,GAAG,CAACP,qBAAqB,GAAGM,UAAU,CAACE,KAAK,CAAC,GAAG,CAAC,CAACC,GAAG,CAAC,CAAC,KAAK,IAAI,IAAI,CAACT,qBAAqB,GAAGA,qBAAqB,CAACQ,KAAK,CAAC,GAAG,CAAC,KAAK,IAAI,IAAI,CAACR,qBAAqB,GAAGA,qBAAqB,CAACU,KAAK,CAAC,CAAC,KAAK,IAAI,GAAG,KAAK,CAAC,GAAGV,qBAAqB,CAACW,WAAW,CAAC,CAAC;EACpd,OAAO;IACLxC,SAAS;IACTC,SAAS;IACT8B;EACF,CAAC;AACH;AACA,SAAS3B,SAASA,CAACJ,SAAS,EAAE;EAC5B,MAAMG,MAAM,GAAGH,SAAS,KAAK,MAAM,GAAGrB,iBAAiB,GAAGqB,SAAS,KAAK,KAAK,GAAGpB,UAAU,GAAGoB,SAAS,KAAK,KAAK,GAAGnB,SAAS,GAAGmB,SAAS,KAAK,KAAK,IAAIA,SAAS,KAAK,MAAM,GAAGlB,YAAY,GAAGkB,SAAS,KAAK,MAAM,GAAGjB,aAAa,GAAG,IAAI;EACvO,OAAOoB,MAAM;AACf;AAEA,SAASZ,cAAc", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}