{"ast": null, "code": "const DOFMipMapShader = {\n  uniforms: {\n    tColor: {\n      value: null\n    },\n    tDepth: {\n      value: null\n    },\n    focus: {\n      value: 1\n    },\n    maxblur: {\n      value: 1\n    }\n  },\n  vertexShader: (/* glsl */\n  `\n    varying vec2 vUv;\n\n    void main() {\n\n    \tvUv = uv;\n    \tgl_Position = projectionMatrix * modelViewMatrix * vec4( position, 1.0 );\n\n    }\n  `),\n  fragmentShader: (/* glsl */\n  `\n    uniform float focus;\n    uniform float maxblur;\n\n    uniform sampler2D tColor;\n    uniform sampler2D tDepth;\n\n    varying vec2 vUv;\n\n    void main() {\n\n    \tvec4 depth = texture2D( tDepth, vUv );\n\n    \tfloat factor = depth.x - focus;\n\n    \tvec4 col = texture2D( tColor, vUv, 2.0 * maxblur * abs( focus - depth.x ) );\n\n    \tgl_FragColor = col;\n    \tgl_FragColor.a = 1.0;\n\n    }\n  `)\n};\nexport { DOFMipMapShader };", "map": {"version": 3, "names": ["DOFMipMapShader", "uniforms", "tColor", "value", "tD<PERSON>h", "focus", "maxblur", "vertexShader", "fragmentShader"], "sources": ["C:\\Users\\<USER>\\Documents\\Apps_Creations\\SiahMan_Racing4Speed_WebGame\\siah-man-racing\\node_modules\\src\\shaders\\DOFMipMapShader.ts"], "sourcesContent": ["/**\n * Depth-of-field shader using mipmaps\n * - from <PERSON> @applmak\n * - requires power-of-2 sized render target with enabled mipmaps\n */\n\nexport const DOFMipMapShader = {\n  uniforms: {\n    tColor: { value: null },\n    tDepth: { value: null },\n    focus: { value: 1.0 },\n    maxblur: { value: 1.0 },\n  },\n\n  vertexShader: /* glsl */ `\n    varying vec2 vUv;\n\n    void main() {\n\n    \tvUv = uv;\n    \tgl_Position = projectionMatrix * modelViewMatrix * vec4( position, 1.0 );\n\n    }\n  `,\n\n  fragmentShader: /* glsl */ `\n    uniform float focus;\n    uniform float maxblur;\n\n    uniform sampler2D tColor;\n    uniform sampler2D tDepth;\n\n    varying vec2 vUv;\n\n    void main() {\n\n    \tvec4 depth = texture2D( tDepth, vUv );\n\n    \tfloat factor = depth.x - focus;\n\n    \tvec4 col = texture2D( tColor, vUv, 2.0 * maxblur * abs( focus - depth.x ) );\n\n    \tgl_FragColor = col;\n    \tgl_FragColor.a = 1.0;\n\n    }\n  `,\n}\n"], "mappings": "AAMO,MAAMA,eAAA,GAAkB;EAC7BC,QAAA,EAAU;IACRC,MAAA,EAAQ;MAAEC,KAAA,EAAO;IAAK;IACtBC,MAAA,EAAQ;MAAED,KAAA,EAAO;IAAK;IACtBE,KAAA,EAAO;MAAEF,KAAA,EAAO;IAAI;IACpBG,OAAA,EAAS;MAAEH,KAAA,EAAO;IAAI;EACxB;EAEAI,YAAA;EAAyB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;EAWzBC,cAAA;EAA2B;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAsB7B", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}