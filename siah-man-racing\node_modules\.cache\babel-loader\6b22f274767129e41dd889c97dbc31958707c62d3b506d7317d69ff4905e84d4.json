{"ast": null, "code": "import { Vector2 } from \"three\";\nclass SelectionHelper {\n  constructor(selectionBox, renderer, cssClassName) {\n    this.element = document.createElement(\"div\");\n    this.element.classList.add(cssClassName);\n    this.element.style.pointerEvents = \"none\";\n    this.renderer = renderer;\n    this.startPoint = new Vector2();\n    this.pointTopLeft = new Vector2();\n    this.pointBottomRight = new Vector2();\n    this.isDown = false;\n    this.renderer.domElement.addEventListener(\"pointerdown\", event => {\n      this.isDown = true;\n      this.onSelectStart(event);\n    });\n    this.renderer.domElement.addEventListener(\"pointermove\", event => {\n      if (this.isDown) {\n        this.onSelectMove(event);\n      }\n    });\n    this.renderer.domElement.addEventListener(\"pointerup\", event => {\n      this.isDown = false;\n      this.onSelectOver(event);\n    });\n  }\n  onSelectStart(event) {\n    this.renderer.domElement.parentElement.appendChild(this.element);\n    this.element.style.left = `${event.clientX}px`;\n    this.element.style.top = `${event.clientY}px`;\n    this.element.style.width = \"0px\";\n    this.element.style.height = \"0px\";\n    this.startPoint.x = event.clientX;\n    this.startPoint.y = event.clientY;\n  }\n  onSelectMove(event) {\n    this.pointBottomRight.x = Math.max(this.startPoint.x, event.clientX);\n    this.pointBottomRight.y = Math.max(this.startPoint.y, event.clientY);\n    this.pointTopLeft.x = Math.min(this.startPoint.x, event.clientX);\n    this.pointTopLeft.y = Math.min(this.startPoint.y, event.clientY);\n    this.element.style.left = `${this.pointTopLeft.x}px`;\n    this.element.style.top = `${this.pointTopLeft.y}px`;\n    this.element.style.width = `${this.pointBottomRight.x - this.pointTopLeft.x}px`;\n    this.element.style.height = `${this.pointBottomRight.y - this.pointTopLeft.y}px`;\n  }\n  onSelectOver() {\n    this.element.parentElement.removeChild(this.element);\n  }\n}\nexport { SelectionHelper };", "map": {"version": 3, "names": ["Selection<PERSON>elper", "constructor", "selectionBox", "renderer", "cssClassName", "element", "document", "createElement", "classList", "add", "style", "pointerEvents", "startPoint", "Vector2", "pointTopLeft", "pointBottomRight", "isDown", "dom<PERSON>lement", "addEventListener", "event", "onSelectStart", "onSelectMove", "onSelectOver", "parentElement", "append<PERSON><PERSON><PERSON>", "left", "clientX", "top", "clientY", "width", "height", "x", "y", "Math", "max", "min", "<PERSON><PERSON><PERSON><PERSON>"], "sources": ["C:\\Users\\<USER>\\Documents\\Apps_Creations\\SiahMan_Racing4Speed_WebGame\\siah-man-racing\\node_modules\\src\\interactive\\SelectionHelper.js"], "sourcesContent": ["import { Vector2 } from 'three'\n\nclass SelectionHelper {\n  constructor(selectionBox, renderer, cssClassName) {\n    this.element = document.createElement('div')\n    this.element.classList.add(cssClassName)\n    this.element.style.pointerEvents = 'none'\n\n    this.renderer = renderer\n\n    this.startPoint = new Vector2()\n    this.pointTopLeft = new Vector2()\n    this.pointBottomRight = new Vector2()\n\n    this.isDown = false\n\n    this.renderer.domElement.addEventListener('pointerdown', (event) => {\n      this.isDown = true\n      this.onSelectStart(event)\n    })\n\n    this.renderer.domElement.addEventListener('pointermove', (event) => {\n      if (this.isDown) {\n        this.onSelectMove(event)\n      }\n    })\n\n    this.renderer.domElement.addEventListener('pointerup', (event) => {\n      this.isDown = false\n      this.onSelectOver(event)\n    })\n  }\n\n  onSelectStart(event) {\n    this.renderer.domElement.parentElement.appendChild(this.element)\n\n    this.element.style.left = `${event.clientX}px`\n    this.element.style.top = `${event.clientY}px`\n    this.element.style.width = '0px'\n    this.element.style.height = '0px'\n\n    this.startPoint.x = event.clientX\n    this.startPoint.y = event.clientY\n  }\n\n  onSelectMove(event) {\n    this.pointBottomRight.x = Math.max(this.startPoint.x, event.clientX)\n    this.pointBottomRight.y = Math.max(this.startPoint.y, event.clientY)\n    this.pointTopLeft.x = Math.min(this.startPoint.x, event.clientX)\n    this.pointTopLeft.y = Math.min(this.startPoint.y, event.clientY)\n\n    this.element.style.left = `${this.pointTopLeft.x}px`\n    this.element.style.top = `${this.pointTopLeft.y}px`\n    this.element.style.width = `${this.pointBottomRight.x - this.pointTopLeft.x}px`\n    this.element.style.height = `${this.pointBottomRight.y - this.pointTopLeft.y}px`\n  }\n\n  onSelectOver() {\n    this.element.parentElement.removeChild(this.element)\n  }\n}\n\nexport { SelectionHelper }\n"], "mappings": ";AAEA,MAAMA,eAAA,CAAgB;EACpBC,YAAYC,YAAA,EAAcC,QAAA,EAAUC,YAAA,EAAc;IAChD,KAAKC,OAAA,GAAUC,QAAA,CAASC,aAAA,CAAc,KAAK;IAC3C,KAAKF,OAAA,CAAQG,SAAA,CAAUC,GAAA,CAAIL,YAAY;IACvC,KAAKC,OAAA,CAAQK,KAAA,CAAMC,aAAA,GAAgB;IAEnC,KAAKR,QAAA,GAAWA,QAAA;IAEhB,KAAKS,UAAA,GAAa,IAAIC,OAAA,CAAS;IAC/B,KAAKC,YAAA,GAAe,IAAID,OAAA,CAAS;IACjC,KAAKE,gBAAA,GAAmB,IAAIF,OAAA,CAAS;IAErC,KAAKG,MAAA,GAAS;IAEd,KAAKb,QAAA,CAASc,UAAA,CAAWC,gBAAA,CAAiB,eAAgBC,KAAA,IAAU;MAClE,KAAKH,MAAA,GAAS;MACd,KAAKI,aAAA,CAAcD,KAAK;IAC9B,CAAK;IAED,KAAKhB,QAAA,CAASc,UAAA,CAAWC,gBAAA,CAAiB,eAAgBC,KAAA,IAAU;MAClE,IAAI,KAAKH,MAAA,EAAQ;QACf,KAAKK,YAAA,CAAaF,KAAK;MACxB;IACP,CAAK;IAED,KAAKhB,QAAA,CAASc,UAAA,CAAWC,gBAAA,CAAiB,aAAcC,KAAA,IAAU;MAChE,KAAKH,MAAA,GAAS;MACd,KAAKM,YAAA,CAAaH,KAAK;IAC7B,CAAK;EACF;EAEDC,cAAcD,KAAA,EAAO;IACnB,KAAKhB,QAAA,CAASc,UAAA,CAAWM,aAAA,CAAcC,WAAA,CAAY,KAAKnB,OAAO;IAE/D,KAAKA,OAAA,CAAQK,KAAA,CAAMe,IAAA,GAAO,GAAGN,KAAA,CAAMO,OAAA;IACnC,KAAKrB,OAAA,CAAQK,KAAA,CAAMiB,GAAA,GAAM,GAAGR,KAAA,CAAMS,OAAA;IAClC,KAAKvB,OAAA,CAAQK,KAAA,CAAMmB,KAAA,GAAQ;IAC3B,KAAKxB,OAAA,CAAQK,KAAA,CAAMoB,MAAA,GAAS;IAE5B,KAAKlB,UAAA,CAAWmB,CAAA,GAAIZ,KAAA,CAAMO,OAAA;IAC1B,KAAKd,UAAA,CAAWoB,CAAA,GAAIb,KAAA,CAAMS,OAAA;EAC3B;EAEDP,aAAaF,KAAA,EAAO;IAClB,KAAKJ,gBAAA,CAAiBgB,CAAA,GAAIE,IAAA,CAAKC,GAAA,CAAI,KAAKtB,UAAA,CAAWmB,CAAA,EAAGZ,KAAA,CAAMO,OAAO;IACnE,KAAKX,gBAAA,CAAiBiB,CAAA,GAAIC,IAAA,CAAKC,GAAA,CAAI,KAAKtB,UAAA,CAAWoB,CAAA,EAAGb,KAAA,CAAMS,OAAO;IACnE,KAAKd,YAAA,CAAaiB,CAAA,GAAIE,IAAA,CAAKE,GAAA,CAAI,KAAKvB,UAAA,CAAWmB,CAAA,EAAGZ,KAAA,CAAMO,OAAO;IAC/D,KAAKZ,YAAA,CAAakB,CAAA,GAAIC,IAAA,CAAKE,GAAA,CAAI,KAAKvB,UAAA,CAAWoB,CAAA,EAAGb,KAAA,CAAMS,OAAO;IAE/D,KAAKvB,OAAA,CAAQK,KAAA,CAAMe,IAAA,GAAO,GAAG,KAAKX,YAAA,CAAaiB,CAAA;IAC/C,KAAK1B,OAAA,CAAQK,KAAA,CAAMiB,GAAA,GAAM,GAAG,KAAKb,YAAA,CAAakB,CAAA;IAC9C,KAAK3B,OAAA,CAAQK,KAAA,CAAMmB,KAAA,GAAQ,GAAG,KAAKd,gBAAA,CAAiBgB,CAAA,GAAI,KAAKjB,YAAA,CAAaiB,CAAA;IAC1E,KAAK1B,OAAA,CAAQK,KAAA,CAAMoB,MAAA,GAAS,GAAG,KAAKf,gBAAA,CAAiBiB,CAAA,GAAI,KAAKlB,YAAA,CAAakB,CAAA;EAC5E;EAEDV,aAAA,EAAe;IACb,KAAKjB,OAAA,CAAQkB,aAAA,CAAca,WAAA,CAAY,KAAK/B,OAAO;EACpD;AACH", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}