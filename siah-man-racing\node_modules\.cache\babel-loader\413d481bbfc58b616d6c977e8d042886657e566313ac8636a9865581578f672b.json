{"ast": null, "code": "import { Vector3, Color } from \"three\";\nconst ToonShader1 = {\n  uniforms: {\n    uDirLightPos: {\n      value: /* @__PURE__ */new Vector3()\n    },\n    uDirLightColor: {\n      value: /* @__PURE__ */new Color(15658734)\n    },\n    uAmbientLightColor: {\n      value: /* @__PURE__ */new Color(328965)\n    },\n    uBaseColor: {\n      value: /* @__PURE__ */new Color(16777215)\n    }\n  },\n  vertexShader: (/* glsl */\n  `\n    varying vec3 vNormal;\n    varying vec3 vRefract;\n\n    void main() {\n\n    \tvec4 worldPosition = modelMatrix * vec4( position, 1.0 );\n    \tvec4 mvPosition = modelViewMatrix * vec4( position, 1.0 );\n    \tvec3 worldNormal = normalize ( mat3( modelMatrix[0].xyz, modelMatrix[1].xyz, modelMatrix[2].xyz ) * normal );\n\n    \tvNormal = normalize( normalMatrix * normal );\n\n    \tvec3 I = worldPosition.xyz - cameraPosition;\n    \tvRefract = refract( normalize( I ), worldNormal, 1.02 );\n\n    \tgl_Position = projectionMatrix * mvPosition;\n\n    }\n  `),\n  fragmentShader: (/* glsl */\n  `\n    uniform vec3 uBaseColor;\n\n    uniform vec3 uDirLightPos;\n    uniform vec3 uDirLightColor;\n\n    uniform vec3 uAmbientLightColor;\n\n    varying vec3 vNormal;\n\n    varying vec3 vRefract;\n\n    void main() {\n\n    \tfloat directionalLightWeighting = max( dot( normalize( vNormal ), uDirLightPos ), 0.0);\n    \tvec3 lightWeighting = uAmbientLightColor + uDirLightColor * directionalLightWeighting;\n\n    \tfloat intensity = smoothstep( - 0.5, 1.0, pow( length(lightWeighting), 20.0 ) );\n    \tintensity += length(lightWeighting) * 0.2;\n\n    \tfloat cameraWeighting = dot( normalize( vNormal ), vRefract );\n    \tintensity += pow( 1.0 - length( cameraWeighting ), 6.0 );\n    \tintensity = intensity * 0.2 + 0.3;\n\n    \tif ( intensity < 0.50 ) {\n\n    \t\tgl_FragColor = vec4( 2.0 * intensity * uBaseColor, 1.0 );\n\n    \t} else {\n\n    \t\tgl_FragColor = vec4( 1.0 - 2.0 * ( 1.0 - intensity ) * ( 1.0 - uBaseColor ), 1.0 );\n\n    }\n\n    }\n  `)\n};\nconst ToonShader2 = {\n  uniforms: {\n    uDirLightPos: {\n      value: /* @__PURE__ */new Vector3()\n    },\n    uDirLightColor: {\n      value: /* @__PURE__ */new Color(15658734)\n    },\n    uAmbientLightColor: {\n      value: /* @__PURE__ */new Color(328965)\n    },\n    uBaseColor: {\n      value: /* @__PURE__ */new Color(15658734)\n    },\n    uLineColor1: {\n      value: /* @__PURE__ */new Color(8421504)\n    },\n    uLineColor2: {\n      value: /* @__PURE__ */new Color(0)\n    },\n    uLineColor3: {\n      value: /* @__PURE__ */new Color(0)\n    },\n    uLineColor4: {\n      value: /* @__PURE__ */new Color(0)\n    }\n  },\n  vertexShader: (/* glsl */\n  `\n    varying vec3 vNormal;\n\n    void main() {\n\n    \tgl_Position = projectionMatrix * modelViewMatrix * vec4( position, 1.0 );\n    \tvNormal = normalize( normalMatrix * normal );\n\n    }\n  `),\n  fragmentShader: (/* glsl */\n  `\n    uniform vec3 uBaseColor;\n    uniform vec3 uLineColor1;\n    uniform vec3 uLineColor2;\n    uniform vec3 uLineColor3;\n    uniform vec3 uLineColor4;\n\n    uniform vec3 uDirLightPos;\n    uniform vec3 uDirLightColor;\n\n    uniform vec3 uAmbientLightColor;\n\n    varying vec3 vNormal;\n\n    void main() {\n\n    \tfloat camera = max( dot( normalize( vNormal ), vec3( 0.0, 0.0, 1.0 ) ), 0.4);\n    \tfloat light = max( dot( normalize( vNormal ), uDirLightPos ), 0.0);\n\n    \tgl_FragColor = vec4( uBaseColor, 1.0 );\n\n    \tif ( length(uAmbientLightColor + uDirLightColor * light) < 1.00 ) {\n\n    \t\tgl_FragColor *= vec4( uLineColor1, 1.0 );\n\n    \t}\n\n    \tif ( length(uAmbientLightColor + uDirLightColor * camera) < 0.50 ) {\n\n    \t\tgl_FragColor *= vec4( uLineColor2, 1.0 );\n\n    \t}\n\n    }\n  `)\n};\nconst ToonShaderHatching = {\n  uniforms: {\n    uDirLightPos: {\n      value: /* @__PURE__ */new Vector3()\n    },\n    uDirLightColor: {\n      value: /* @__PURE__ */new Color(15658734)\n    },\n    uAmbientLightColor: {\n      value: /* @__PURE__ */new Color(328965)\n    },\n    uBaseColor: {\n      value: /* @__PURE__ */new Color(16777215)\n    },\n    uLineColor1: {\n      value: /* @__PURE__ */new Color(0)\n    },\n    uLineColor2: {\n      value: /* @__PURE__ */new Color(0)\n    },\n    uLineColor3: {\n      value: /* @__PURE__ */new Color(0)\n    },\n    uLineColor4: {\n      value: /* @__PURE__ */new Color(0)\n    }\n  },\n  vertexShader: (/* glsl */\n  `\n    varying vec3 vNormal;\n\n    void main() {\n\n    \tgl_Position = projectionMatrix * modelViewMatrix * vec4( position, 1.0 );\n    \tvNormal = normalize( normalMatrix * normal );\n\n    }\n  `),\n  fragmentShader: (/* glsl */\n  `\n    uniform vec3 uBaseColor;\n    uniform vec3 uLineColor1;\n    uniform vec3 uLineColor2;\n    uniform vec3 uLineColor3;\n    uniform vec3 uLineColor4;\n\n    uniform vec3 uDirLightPos;\n    uniform vec3 uDirLightColor;\n\n    uniform vec3 uAmbientLightColor;\n\n    varying vec3 vNormal;\n\n    void main() {\n\n    \tfloat directionalLightWeighting = max( dot( normalize(vNormal), uDirLightPos ), 0.0);\n    \tvec3 lightWeighting = uAmbientLightColor + uDirLightColor * directionalLightWeighting;\n\n    \tgl_FragColor = vec4( uBaseColor, 1.0 );\n\n    \tif ( length(lightWeighting) < 1.00 ) {\n\n    \t\tif ( mod(gl_FragCoord.x + gl_FragCoord.y, 10.0) == 0.0) {\n\n    \t\t\tgl_FragColor = vec4( uLineColor1, 1.0 );\n\n    \t\t}\n\n    \t}\n\n    \tif ( length(lightWeighting) < 0.75 ) {\n\n    \t\tif (mod(gl_FragCoord.x - gl_FragCoord.y, 10.0) == 0.0) {\n\n    \t\t\tgl_FragColor = vec4( uLineColor2, 1.0 );\n\n    \t\t}\n\n    \t}\n\n    \tif ( length(lightWeighting) < 0.50 ) {\n\n    \t\tif (mod(gl_FragCoord.x + gl_FragCoord.y - 5.0, 10.0) == 0.0) {\n\n    \t\t\tgl_FragColor = vec4( uLineColor3, 1.0 );\n\n    \t\t}\n\n    \t}\n\n    \tif ( length(lightWeighting) < 0.3465 ) {\n\n    \t\tif (mod(gl_FragCoord.x - gl_FragCoord.y - 5.0, 10.0) == 0.0) {\n\n    \t\t\tgl_FragColor = vec4( uLineColor4, 1.0 );\n\n    \t}\n\n    \t}\n\n    }\n  `)\n};\nconst ToonShaderDotted = {\n  uniforms: {\n    uDirLightPos: {\n      value: /* @__PURE__ */new Vector3()\n    },\n    uDirLightColor: {\n      value: /* @__PURE__ */new Color(15658734)\n    },\n    uAmbientLightColor: {\n      value: /* @__PURE__ */new Color(328965)\n    },\n    uBaseColor: {\n      value: /* @__PURE__ */new Color(16777215)\n    },\n    uLineColor1: {\n      value: /* @__PURE__ */new Color(0)\n    }\n  },\n  vertexShader: (/* glsl */\n  `\n    varying vec3 vNormal;\n\n    void main() {\n\n    \tgl_Position = projectionMatrix * modelViewMatrix * vec4( position, 1.0 );\n    \tvNormal = normalize( normalMatrix * normal );\n\n    }\n  `),\n  fragmentShader: (/* glsl */\n  `\n    uniform vec3 uBaseColor;\n    uniform vec3 uLineColor1;\n    uniform vec3 uLineColor2;\n    uniform vec3 uLineColor3;\n    uniform vec3 uLineColor4;\n\n    uniform vec3 uDirLightPos;\n    uniform vec3 uDirLightColor;\n\n    uniform vec3 uAmbientLightColor;\n\n    varying vec3 vNormal;\n\n    void main() {\n\n    float directionalLightWeighting = max( dot( normalize(vNormal), uDirLightPos ), 0.0);\n    vec3 lightWeighting = uAmbientLightColor + uDirLightColor * directionalLightWeighting;\n\n    gl_FragColor = vec4( uBaseColor, 1.0 );\n\n    if ( length(lightWeighting) < 1.00 ) {\n\n    \t\tif ( ( mod(gl_FragCoord.x, 4.001) + mod(gl_FragCoord.y, 4.0) ) > 6.00 ) {\n\n    \t\t\tgl_FragColor = vec4( uLineColor1, 1.0 );\n\n    \t\t}\n\n    \t}\n\n    \tif ( length(lightWeighting) < 0.50 ) {\n\n    \t\tif ( ( mod(gl_FragCoord.x + 2.0, 4.001) + mod(gl_FragCoord.y + 2.0, 4.0) ) > 6.00 ) {\n\n    \t\t\tgl_FragColor = vec4( uLineColor1, 1.0 );\n\n    \t\t}\n\n    \t}\n\n    }\n  `)\n};\nexport { ToonShader1, ToonShader2, ToonShaderDotted, ToonShaderHatching };", "map": {"version": 3, "names": ["ToonShader1", "uniforms", "uDirLightPos", "value", "Vector3", "uDirLightColor", "Color", "uAmbientLightColor", "uBaseColor", "vertexShader", "fragmentShader", "ToonShader2", "uLineColor1", "uLineColor2", "uLineColor3", "uLineColor4", "ToonShaderHatching", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>otted"], "sources": ["C:\\Users\\<USER>\\Documents\\Apps_Creations\\SiahMan_Racing4Speed_WebGame\\siah-man-racing\\node_modules\\src\\shaders\\ToonShader.ts"], "sourcesContent": ["import { Color, Vector3 } from 'three'\n\n/**\n * Currently contains:\n *\n *\ttoon1\n *\ttoon2\n *\thatching\n *\tdotted\n */\n\nexport const ToonShader1 = {\n  uniforms: {\n    uDirLightPos: { value: /* @__PURE__ */ new Vector3() },\n    uDirLightColor: { value: /* @__PURE__ */ new Color(0xeeeeee) },\n\n    uAmbientLightColor: { value: /* @__PURE__ */ new Color(0x050505) },\n\n    uBaseColor: { value: /* @__PURE__ */ new Color(0xffffff) },\n  },\n\n  vertexShader: /* glsl */ `\n    varying vec3 vNormal;\n    varying vec3 vRefract;\n\n    void main() {\n\n    \tvec4 worldPosition = modelMatrix * vec4( position, 1.0 );\n    \tvec4 mvPosition = modelViewMatrix * vec4( position, 1.0 );\n    \tvec3 worldNormal = normalize ( mat3( modelMatrix[0].xyz, modelMatrix[1].xyz, modelMatrix[2].xyz ) * normal );\n\n    \tvNormal = normalize( normalMatrix * normal );\n\n    \tvec3 I = worldPosition.xyz - cameraPosition;\n    \tvRefract = refract( normalize( I ), worldNormal, 1.02 );\n\n    \tgl_Position = projectionMatrix * mvPosition;\n\n    }\n  `,\n\n  fragmentShader: /* glsl */ `\n    uniform vec3 uBaseColor;\n\n    uniform vec3 uDirLightPos;\n    uniform vec3 uDirLightColor;\n\n    uniform vec3 uAmbientLightColor;\n\n    varying vec3 vNormal;\n\n    varying vec3 vRefract;\n\n    void main() {\n\n    \tfloat directionalLightWeighting = max( dot( normalize( vNormal ), uDirLightPos ), 0.0);\n    \tvec3 lightWeighting = uAmbientLightColor + uDirLightColor * directionalLightWeighting;\n\n    \tfloat intensity = smoothstep( - 0.5, 1.0, pow( length(lightWeighting), 20.0 ) );\n    \tintensity += length(lightWeighting) * 0.2;\n\n    \tfloat cameraWeighting = dot( normalize( vNormal ), vRefract );\n    \tintensity += pow( 1.0 - length( cameraWeighting ), 6.0 );\n    \tintensity = intensity * 0.2 + 0.3;\n\n    \tif ( intensity < 0.50 ) {\n\n    \t\tgl_FragColor = vec4( 2.0 * intensity * uBaseColor, 1.0 );\n\n    \t} else {\n\n    \t\tgl_FragColor = vec4( 1.0 - 2.0 * ( 1.0 - intensity ) * ( 1.0 - uBaseColor ), 1.0 );\n\n    }\n\n    }\n  `,\n}\n\nexport const ToonShader2 = {\n  uniforms: {\n    uDirLightPos: { value: /* @__PURE__ */ new Vector3() },\n    uDirLightColor: { value: /* @__PURE__ */ new Color(0xeeeeee) },\n\n    uAmbientLightColor: { value: /* @__PURE__ */ new Color(0x050505) },\n\n    uBaseColor: { value: /* @__PURE__ */ new Color(0xeeeeee) },\n    uLineColor1: { value: /* @__PURE__ */ new Color(0x808080) },\n    uLineColor2: { value: /* @__PURE__ */ new Color(0x000000) },\n    uLineColor3: { value: /* @__PURE__ */ new Color(0x000000) },\n    uLineColor4: { value: /* @__PURE__ */ new Color(0x000000) },\n  },\n\n  vertexShader: /* glsl */ `\n    varying vec3 vNormal;\n\n    void main() {\n\n    \tgl_Position = projectionMatrix * modelViewMatrix * vec4( position, 1.0 );\n    \tvNormal = normalize( normalMatrix * normal );\n\n    }\n  `,\n\n  fragmentShader: /* glsl */ `\n    uniform vec3 uBaseColor;\n    uniform vec3 uLineColor1;\n    uniform vec3 uLineColor2;\n    uniform vec3 uLineColor3;\n    uniform vec3 uLineColor4;\n\n    uniform vec3 uDirLightPos;\n    uniform vec3 uDirLightColor;\n\n    uniform vec3 uAmbientLightColor;\n\n    varying vec3 vNormal;\n\n    void main() {\n\n    \tfloat camera = max( dot( normalize( vNormal ), vec3( 0.0, 0.0, 1.0 ) ), 0.4);\n    \tfloat light = max( dot( normalize( vNormal ), uDirLightPos ), 0.0);\n\n    \tgl_FragColor = vec4( uBaseColor, 1.0 );\n\n    \tif ( length(uAmbientLightColor + uDirLightColor * light) < 1.00 ) {\n\n    \t\tgl_FragColor *= vec4( uLineColor1, 1.0 );\n\n    \t}\n\n    \tif ( length(uAmbientLightColor + uDirLightColor * camera) < 0.50 ) {\n\n    \t\tgl_FragColor *= vec4( uLineColor2, 1.0 );\n\n    \t}\n\n    }\n  `,\n}\n\nexport const ToonShaderHatching = {\n  uniforms: {\n    uDirLightPos: { value: /* @__PURE__ */ new Vector3() },\n    uDirLightColor: { value: /* @__PURE__ */ new Color(0xeeeeee) },\n\n    uAmbientLightColor: { value: /* @__PURE__ */ new Color(0x050505) },\n\n    uBaseColor: { value: /* @__PURE__ */ new Color(0xffffff) },\n    uLineColor1: { value: /* @__PURE__ */ new Color(0x000000) },\n    uLineColor2: { value: /* @__PURE__ */ new Color(0x000000) },\n    uLineColor3: { value: /* @__PURE__ */ new Color(0x000000) },\n    uLineColor4: { value: /* @__PURE__ */ new Color(0x000000) },\n  },\n\n  vertexShader: /* glsl */ `\n    varying vec3 vNormal;\n\n    void main() {\n\n    \tgl_Position = projectionMatrix * modelViewMatrix * vec4( position, 1.0 );\n    \tvNormal = normalize( normalMatrix * normal );\n\n    }\n  `,\n\n  fragmentShader: /* glsl */ `\n    uniform vec3 uBaseColor;\n    uniform vec3 uLineColor1;\n    uniform vec3 uLineColor2;\n    uniform vec3 uLineColor3;\n    uniform vec3 uLineColor4;\n\n    uniform vec3 uDirLightPos;\n    uniform vec3 uDirLightColor;\n\n    uniform vec3 uAmbientLightColor;\n\n    varying vec3 vNormal;\n\n    void main() {\n\n    \tfloat directionalLightWeighting = max( dot( normalize(vNormal), uDirLightPos ), 0.0);\n    \tvec3 lightWeighting = uAmbientLightColor + uDirLightColor * directionalLightWeighting;\n\n    \tgl_FragColor = vec4( uBaseColor, 1.0 );\n\n    \tif ( length(lightWeighting) < 1.00 ) {\n\n    \t\tif ( mod(gl_FragCoord.x + gl_FragCoord.y, 10.0) == 0.0) {\n\n    \t\t\tgl_FragColor = vec4( uLineColor1, 1.0 );\n\n    \t\t}\n\n    \t}\n\n    \tif ( length(lightWeighting) < 0.75 ) {\n\n    \t\tif (mod(gl_FragCoord.x - gl_FragCoord.y, 10.0) == 0.0) {\n\n    \t\t\tgl_FragColor = vec4( uLineColor2, 1.0 );\n\n    \t\t}\n\n    \t}\n\n    \tif ( length(lightWeighting) < 0.50 ) {\n\n    \t\tif (mod(gl_FragCoord.x + gl_FragCoord.y - 5.0, 10.0) == 0.0) {\n\n    \t\t\tgl_FragColor = vec4( uLineColor3, 1.0 );\n\n    \t\t}\n\n    \t}\n\n    \tif ( length(lightWeighting) < 0.3465 ) {\n\n    \t\tif (mod(gl_FragCoord.x - gl_FragCoord.y - 5.0, 10.0) == 0.0) {\n\n    \t\t\tgl_FragColor = vec4( uLineColor4, 1.0 );\n\n    \t}\n\n    \t}\n\n    }\n  `,\n}\n\nexport const ToonShaderDotted = {\n  uniforms: {\n    uDirLightPos: { value: /* @__PURE__ */ new Vector3() },\n    uDirLightColor: { value: /* @__PURE__ */ new Color(0xeeeeee) },\n\n    uAmbientLightColor: { value: /* @__PURE__ */ new Color(0x050505) },\n\n    uBaseColor: { value: /* @__PURE__ */ new Color(0xffffff) },\n    uLineColor1: { value: /* @__PURE__ */ new Color(0x000000) },\n  },\n\n  vertexShader: /* glsl */ `\n    varying vec3 vNormal;\n\n    void main() {\n\n    \tgl_Position = projectionMatrix * modelViewMatrix * vec4( position, 1.0 );\n    \tvNormal = normalize( normalMatrix * normal );\n\n    }\n  `,\n\n  fragmentShader: /* glsl */ `\n    uniform vec3 uBaseColor;\n    uniform vec3 uLineColor1;\n    uniform vec3 uLineColor2;\n    uniform vec3 uLineColor3;\n    uniform vec3 uLineColor4;\n\n    uniform vec3 uDirLightPos;\n    uniform vec3 uDirLightColor;\n\n    uniform vec3 uAmbientLightColor;\n\n    varying vec3 vNormal;\n\n    void main() {\n\n    float directionalLightWeighting = max( dot( normalize(vNormal), uDirLightPos ), 0.0);\n    vec3 lightWeighting = uAmbientLightColor + uDirLightColor * directionalLightWeighting;\n\n    gl_FragColor = vec4( uBaseColor, 1.0 );\n\n    if ( length(lightWeighting) < 1.00 ) {\n\n    \t\tif ( ( mod(gl_FragCoord.x, 4.001) + mod(gl_FragCoord.y, 4.0) ) > 6.00 ) {\n\n    \t\t\tgl_FragColor = vec4( uLineColor1, 1.0 );\n\n    \t\t}\n\n    \t}\n\n    \tif ( length(lightWeighting) < 0.50 ) {\n\n    \t\tif ( ( mod(gl_FragCoord.x + 2.0, 4.001) + mod(gl_FragCoord.y + 2.0, 4.0) ) > 6.00 ) {\n\n    \t\t\tgl_FragColor = vec4( uLineColor1, 1.0 );\n\n    \t\t}\n\n    \t}\n\n    }\n  `,\n}\n"], "mappings": ";AAWO,MAAMA,WAAA,GAAc;EACzBC,QAAA,EAAU;IACRC,YAAA,EAAc;MAAEC,KAAA,EAAuB,mBAAIC,OAAA;IAAU;IACrDC,cAAA,EAAgB;MAAEF,KAAA,EAA2B,mBAAAG,KAAA,CAAM,QAAQ;IAAE;IAE7DC,kBAAA,EAAoB;MAAEJ,KAAA,EAA2B,mBAAAG,KAAA,CAAM,MAAQ;IAAE;IAEjEE,UAAA,EAAY;MAAEL,KAAA,EAA2B,mBAAAG,KAAA,CAAM,QAAQ;IAAE;EAC3D;EAEAG,YAAA;EAAyB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;EAoBzBC,cAAA;EAA2B;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAoC7B;AAEO,MAAMC,WAAA,GAAc;EACzBV,QAAA,EAAU;IACRC,YAAA,EAAc;MAAEC,KAAA,EAAuB,mBAAIC,OAAA;IAAU;IACrDC,cAAA,EAAgB;MAAEF,KAAA,EAA2B,mBAAAG,KAAA,CAAM,QAAQ;IAAE;IAE7DC,kBAAA,EAAoB;MAAEJ,KAAA,EAA2B,mBAAAG,KAAA,CAAM,MAAQ;IAAE;IAEjEE,UAAA,EAAY;MAAEL,KAAA,EAA2B,mBAAAG,KAAA,CAAM,QAAQ;IAAE;IACzDM,WAAA,EAAa;MAAET,KAAA,EAA2B,mBAAAG,KAAA,CAAM,OAAQ;IAAE;IAC1DO,WAAA,EAAa;MAAEV,KAAA,EAA2B,mBAAAG,KAAA,CAAM,CAAQ;IAAE;IAC1DQ,WAAA,EAAa;MAAEX,KAAA,EAA2B,mBAAAG,KAAA,CAAM,CAAQ;IAAE;IAC1DS,WAAA,EAAa;MAAEZ,KAAA,EAA2B,mBAAAG,KAAA,CAAM,CAAQ;IAAE;EAC5D;EAEAG,YAAA;EAAyB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;EAWzBC,cAAA;EAA2B;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAmC7B;AAEO,MAAMM,kBAAA,GAAqB;EAChCf,QAAA,EAAU;IACRC,YAAA,EAAc;MAAEC,KAAA,EAAuB,mBAAIC,OAAA;IAAU;IACrDC,cAAA,EAAgB;MAAEF,KAAA,EAA2B,mBAAAG,KAAA,CAAM,QAAQ;IAAE;IAE7DC,kBAAA,EAAoB;MAAEJ,KAAA,EAA2B,mBAAAG,KAAA,CAAM,MAAQ;IAAE;IAEjEE,UAAA,EAAY;MAAEL,KAAA,EAA2B,mBAAAG,KAAA,CAAM,QAAQ;IAAE;IACzDM,WAAA,EAAa;MAAET,KAAA,EAA2B,mBAAAG,KAAA,CAAM,CAAQ;IAAE;IAC1DO,WAAA,EAAa;MAAEV,KAAA,EAA2B,mBAAAG,KAAA,CAAM,CAAQ;IAAE;IAC1DQ,WAAA,EAAa;MAAEX,KAAA,EAA2B,mBAAAG,KAAA,CAAM,CAAQ;IAAE;IAC1DS,WAAA,EAAa;MAAEZ,KAAA,EAA2B,mBAAAG,KAAA,CAAM,CAAQ;IAAE;EAC5D;EAEAG,YAAA;EAAyB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;EAWzBC,cAAA;EAA2B;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AA+D7B;AAEO,MAAMO,gBAAA,GAAmB;EAC9BhB,QAAA,EAAU;IACRC,YAAA,EAAc;MAAEC,KAAA,EAAuB,mBAAIC,OAAA;IAAU;IACrDC,cAAA,EAAgB;MAAEF,KAAA,EAA2B,mBAAAG,KAAA,CAAM,QAAQ;IAAE;IAE7DC,kBAAA,EAAoB;MAAEJ,KAAA,EAA2B,mBAAAG,KAAA,CAAM,MAAQ;IAAE;IAEjEE,UAAA,EAAY;MAAEL,KAAA,EAA2B,mBAAAG,KAAA,CAAM,QAAQ;IAAE;IACzDM,WAAA,EAAa;MAAET,KAAA,EAA2B,mBAAAG,KAAA,CAAM,CAAQ;IAAE;EAC5D;EAEAG,YAAA;EAAyB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;EAWzBC,cAAA;EAA2B;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AA2C7B", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}