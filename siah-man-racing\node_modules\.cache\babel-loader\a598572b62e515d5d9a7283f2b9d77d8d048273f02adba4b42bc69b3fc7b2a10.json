{"ast": null, "code": "import { LineSegmentsGeometry } from \"./LineSegmentsGeometry.js\";\nclass LineGeometry extends LineSegmentsGeometry {\n  constructor() {\n    super();\n    this.isLineGeometry = true;\n    this.type = \"LineGeometry\";\n  }\n  setPositions(array) {\n    const length = array.length - 3;\n    const points = new Float32Array(2 * length);\n    for (let i = 0; i < length; i += 3) {\n      points[2 * i] = array[i];\n      points[2 * i + 1] = array[i + 1];\n      points[2 * i + 2] = array[i + 2];\n      points[2 * i + 3] = array[i + 3];\n      points[2 * i + 4] = array[i + 4];\n      points[2 * i + 5] = array[i + 5];\n    }\n    super.setPositions(points);\n    return this;\n  }\n  setColors(array, itemSize = 3) {\n    const length = array.length - itemSize;\n    const colors = new Float32Array(2 * length);\n    if (itemSize === 3) {\n      for (let i = 0; i < length; i += itemSize) {\n        colors[2 * i] = array[i];\n        colors[2 * i + 1] = array[i + 1];\n        colors[2 * i + 2] = array[i + 2];\n        colors[2 * i + 3] = array[i + 3];\n        colors[2 * i + 4] = array[i + 4];\n        colors[2 * i + 5] = array[i + 5];\n      }\n    } else {\n      for (let i = 0; i < length; i += itemSize) {\n        colors[2 * i] = array[i];\n        colors[2 * i + 1] = array[i + 1];\n        colors[2 * i + 2] = array[i + 2];\n        colors[2 * i + 3] = array[i + 3];\n        colors[2 * i + 4] = array[i + 4];\n        colors[2 * i + 5] = array[i + 5];\n        colors[2 * i + 6] = array[i + 6];\n        colors[2 * i + 7] = array[i + 7];\n      }\n    }\n    super.setColors(colors, itemSize);\n    return this;\n  }\n  fromLine(line) {\n    const geometry = line.geometry;\n    this.setPositions(geometry.attributes.position.array);\n    return this;\n  }\n}\nexport { LineGeometry };", "map": {"version": 3, "names": ["LineGeometry", "LineSegmentsGeometry", "constructor", "isLineGeometry", "type", "setPositions", "array", "length", "points", "Float32Array", "i", "setColors", "itemSize", "colors", "fromLine", "line", "geometry", "attributes", "position"], "sources": ["C:\\Users\\<USER>\\Documents\\Apps_Creations\\SiahMan_Racing4Speed_WebGame\\siah-man-racing\\node_modules\\src\\lines\\LineGeometry.js"], "sourcesContent": ["import { LineSegmentsGeometry } from '../lines/LineSegmentsGeometry'\n\nclass LineGeometry extends LineSegmentsGeometry {\n  constructor() {\n    super()\n\n    this.isLineGeometry = true\n\n    this.type = 'LineGeometry'\n  }\n\n  setPositions(array) {\n    // converts [ x1, y1, z1,  x2, y2, z2, ... ] to pairs format\n\n    const length = array.length - 3\n    const points = new Float32Array(2 * length)\n\n    for (let i = 0; i < length; i += 3) {\n      points[2 * i] = array[i]\n      points[2 * i + 1] = array[i + 1]\n      points[2 * i + 2] = array[i + 2]\n\n      points[2 * i + 3] = array[i + 3]\n      points[2 * i + 4] = array[i + 4]\n      points[2 * i + 5] = array[i + 5]\n    }\n\n    super.setPositions(points)\n\n    return this\n  }\n\n  setColors(array, itemSize = 3) {\n    // converts [ r1, g1, b1, (a1),  r2, g2, b2, (a2), ... ] to pairs format\n\n    const length = array.length - itemSize\n    const colors = new Float32Array(2 * length)\n\n    if (itemSize === 3) {\n      for (let i = 0; i < length; i += itemSize) {\n        colors[2 * i] = array[i]\n        colors[2 * i + 1] = array[i + 1]\n        colors[2 * i + 2] = array[i + 2]\n\n        colors[2 * i + 3] = array[i + 3]\n        colors[2 * i + 4] = array[i + 4]\n        colors[2 * i + 5] = array[i + 5]\n      }\n    } else {\n      for (let i = 0; i < length; i += itemSize) {\n        colors[2 * i] = array[i]\n        colors[2 * i + 1] = array[i + 1]\n        colors[2 * i + 2] = array[i + 2]\n        colors[2 * i + 3] = array[i + 3]\n\n        colors[2 * i + 4] = array[i + 4]\n        colors[2 * i + 5] = array[i + 5]\n        colors[2 * i + 6] = array[i + 6]\n        colors[2 * i + 7] = array[i + 7]\n      }\n    }\n\n    super.setColors(colors, itemSize)\n\n    return this\n  }\n\n  fromLine(line) {\n    const geometry = line.geometry\n\n    this.setPositions(geometry.attributes.position.array) // assumes non-indexed\n\n    // set colors, maybe\n\n    return this\n  }\n}\n\nexport { LineGeometry }\n"], "mappings": ";AAEA,MAAMA,YAAA,SAAqBC,oBAAA,CAAqB;EAC9CC,YAAA,EAAc;IACZ,MAAO;IAEP,KAAKC,cAAA,GAAiB;IAEtB,KAAKC,IAAA,GAAO;EACb;EAEDC,aAAaC,KAAA,EAAO;IAGlB,MAAMC,MAAA,GAASD,KAAA,CAAMC,MAAA,GAAS;IAC9B,MAAMC,MAAA,GAAS,IAAIC,YAAA,CAAa,IAAIF,MAAM;IAE1C,SAASG,CAAA,GAAI,GAAGA,CAAA,GAAIH,MAAA,EAAQG,CAAA,IAAK,GAAG;MAClCF,MAAA,CAAO,IAAIE,CAAC,IAAIJ,KAAA,CAAMI,CAAC;MACvBF,MAAA,CAAO,IAAIE,CAAA,GAAI,CAAC,IAAIJ,KAAA,CAAMI,CAAA,GAAI,CAAC;MAC/BF,MAAA,CAAO,IAAIE,CAAA,GAAI,CAAC,IAAIJ,KAAA,CAAMI,CAAA,GAAI,CAAC;MAE/BF,MAAA,CAAO,IAAIE,CAAA,GAAI,CAAC,IAAIJ,KAAA,CAAMI,CAAA,GAAI,CAAC;MAC/BF,MAAA,CAAO,IAAIE,CAAA,GAAI,CAAC,IAAIJ,KAAA,CAAMI,CAAA,GAAI,CAAC;MAC/BF,MAAA,CAAO,IAAIE,CAAA,GAAI,CAAC,IAAIJ,KAAA,CAAMI,CAAA,GAAI,CAAC;IAChC;IAED,MAAML,YAAA,CAAaG,MAAM;IAEzB,OAAO;EACR;EAEDG,UAAUL,KAAA,EAAOM,QAAA,GAAW,GAAG;IAG7B,MAAML,MAAA,GAASD,KAAA,CAAMC,MAAA,GAASK,QAAA;IAC9B,MAAMC,MAAA,GAAS,IAAIJ,YAAA,CAAa,IAAIF,MAAM;IAE1C,IAAIK,QAAA,KAAa,GAAG;MAClB,SAASF,CAAA,GAAI,GAAGA,CAAA,GAAIH,MAAA,EAAQG,CAAA,IAAKE,QAAA,EAAU;QACzCC,MAAA,CAAO,IAAIH,CAAC,IAAIJ,KAAA,CAAMI,CAAC;QACvBG,MAAA,CAAO,IAAIH,CAAA,GAAI,CAAC,IAAIJ,KAAA,CAAMI,CAAA,GAAI,CAAC;QAC/BG,MAAA,CAAO,IAAIH,CAAA,GAAI,CAAC,IAAIJ,KAAA,CAAMI,CAAA,GAAI,CAAC;QAE/BG,MAAA,CAAO,IAAIH,CAAA,GAAI,CAAC,IAAIJ,KAAA,CAAMI,CAAA,GAAI,CAAC;QAC/BG,MAAA,CAAO,IAAIH,CAAA,GAAI,CAAC,IAAIJ,KAAA,CAAMI,CAAA,GAAI,CAAC;QAC/BG,MAAA,CAAO,IAAIH,CAAA,GAAI,CAAC,IAAIJ,KAAA,CAAMI,CAAA,GAAI,CAAC;MAChC;IACP,OAAW;MACL,SAASA,CAAA,GAAI,GAAGA,CAAA,GAAIH,MAAA,EAAQG,CAAA,IAAKE,QAAA,EAAU;QACzCC,MAAA,CAAO,IAAIH,CAAC,IAAIJ,KAAA,CAAMI,CAAC;QACvBG,MAAA,CAAO,IAAIH,CAAA,GAAI,CAAC,IAAIJ,KAAA,CAAMI,CAAA,GAAI,CAAC;QAC/BG,MAAA,CAAO,IAAIH,CAAA,GAAI,CAAC,IAAIJ,KAAA,CAAMI,CAAA,GAAI,CAAC;QAC/BG,MAAA,CAAO,IAAIH,CAAA,GAAI,CAAC,IAAIJ,KAAA,CAAMI,CAAA,GAAI,CAAC;QAE/BG,MAAA,CAAO,IAAIH,CAAA,GAAI,CAAC,IAAIJ,KAAA,CAAMI,CAAA,GAAI,CAAC;QAC/BG,MAAA,CAAO,IAAIH,CAAA,GAAI,CAAC,IAAIJ,KAAA,CAAMI,CAAA,GAAI,CAAC;QAC/BG,MAAA,CAAO,IAAIH,CAAA,GAAI,CAAC,IAAIJ,KAAA,CAAMI,CAAA,GAAI,CAAC;QAC/BG,MAAA,CAAO,IAAIH,CAAA,GAAI,CAAC,IAAIJ,KAAA,CAAMI,CAAA,GAAI,CAAC;MAChC;IACF;IAED,MAAMC,SAAA,CAAUE,MAAA,EAAQD,QAAQ;IAEhC,OAAO;EACR;EAEDE,SAASC,IAAA,EAAM;IACb,MAAMC,QAAA,GAAWD,IAAA,CAAKC,QAAA;IAEtB,KAAKX,YAAA,CAAaW,QAAA,CAASC,UAAA,CAAWC,QAAA,CAASZ,KAAK;IAIpD,OAAO;EACR;AACH", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}