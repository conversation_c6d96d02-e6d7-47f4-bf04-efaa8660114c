{"ast": null, "code": "import { Vector4 } from \"three\";\nimport { calcSurfacePoint } from \"./NURBSUtils.js\";\nclass NURBSSurface {\n  constructor(degree1, degree2, knots1, knots2, controlPoints) {\n    this.degree1 = degree1;\n    this.degree2 = degree2;\n    this.knots1 = knots1;\n    this.knots2 = knots2;\n    this.controlPoints = [];\n    const len1 = knots1.length - degree1 - 1;\n    const len2 = knots2.length - degree2 - 1;\n    for (let i = 0; i < len1; ++i) {\n      this.controlPoints[i] = [];\n      for (let j = 0; j < len2; ++j) {\n        const point = controlPoints[i][j];\n        this.controlPoints[i][j] = new Vector4(point.x, point.y, point.z, point.w);\n      }\n    }\n  }\n  getPoint(t1, t2, target) {\n    const u = this.knots1[0] + t1 * (this.knots1[this.knots1.length - 1] - this.knots1[0]);\n    const v = this.knots2[0] + t2 * (this.knots2[this.knots2.length - 1] - this.knots2[0]);\n    calcSurfacePoint(this.degree1, this.degree2, this.knots1, this.knots2, this.controlPoints, u, v, target);\n  }\n}\nexport { NURBSSurface };", "map": {"version": 3, "names": ["NURBSSurface", "constructor", "degree1", "degree2", "knots1", "knots2", "controlPoints", "len1", "length", "len2", "i", "j", "point", "Vector4", "x", "y", "z", "w", "getPoint", "t1", "t2", "target", "u", "v", "calcSurfacePoint"], "sources": ["C:\\Users\\<USER>\\Documents\\Apps_Creations\\SiahMan_Racing4Speed_WebGame\\siah-man-racing\\node_modules\\src\\curves\\NURBSSurface.js"], "sourcesContent": ["import { Vector4 } from 'three'\nimport * as NURBSUtils from '../curves/NURBSUtils'\n\n/**\n * NURBS surface object\n *\n * Implementation is based on (x, y [, z=0 [, w=1]]) control points with w=weight.\n **/\n\nclass NURBSSurface {\n  constructor(degree1, degree2, knots1, knots2 /* arrays of reals */, controlPoints /* array^2 of Vector(2|3|4) */) {\n    this.degree1 = degree1\n    this.degree2 = degree2\n    this.knots1 = knots1\n    this.knots2 = knots2\n    this.controlPoints = []\n\n    const len1 = knots1.length - degree1 - 1\n    const len2 = knots2.length - degree2 - 1\n\n    // ensure Vector4 for control points\n    for (let i = 0; i < len1; ++i) {\n      this.controlPoints[i] = []\n      for (let j = 0; j < len2; ++j) {\n        const point = controlPoints[i][j]\n        this.controlPoints[i][j] = new Vector4(point.x, point.y, point.z, point.w)\n      }\n    }\n  }\n\n  getPoint(t1, t2, target) {\n    const u = this.knots1[0] + t1 * (this.knots1[this.knots1.length - 1] - this.knots1[0]) // linear mapping t1->u\n    const v = this.knots2[0] + t2 * (this.knots2[this.knots2.length - 1] - this.knots2[0]) // linear mapping t2->u\n\n    NURBSUtils.calcSurfacePoint(this.degree1, this.degree2, this.knots1, this.knots2, this.controlPoints, u, v, target)\n  }\n}\n\nexport { NURBSSurface }\n"], "mappings": ";;AASA,MAAMA,YAAA,CAAa;EACjBC,YAAYC,OAAA,EAASC,OAAA,EAASC,MAAA,EAAQC,MAAA,EAA8BC,aAAA,EAA8C;IAChH,KAAKJ,OAAA,GAAUA,OAAA;IACf,KAAKC,OAAA,GAAUA,OAAA;IACf,KAAKC,MAAA,GAASA,MAAA;IACd,KAAKC,MAAA,GAASA,MAAA;IACd,KAAKC,aAAA,GAAgB,EAAE;IAEvB,MAAMC,IAAA,GAAOH,MAAA,CAAOI,MAAA,GAASN,OAAA,GAAU;IACvC,MAAMO,IAAA,GAAOJ,MAAA,CAAOG,MAAA,GAASL,OAAA,GAAU;IAGvC,SAASO,CAAA,GAAI,GAAGA,CAAA,GAAIH,IAAA,EAAM,EAAEG,CAAA,EAAG;MAC7B,KAAKJ,aAAA,CAAcI,CAAC,IAAI,EAAE;MAC1B,SAASC,CAAA,GAAI,GAAGA,CAAA,GAAIF,IAAA,EAAM,EAAEE,CAAA,EAAG;QAC7B,MAAMC,KAAA,GAAQN,aAAA,CAAcI,CAAC,EAAEC,CAAC;QAChC,KAAKL,aAAA,CAAcI,CAAC,EAAEC,CAAC,IAAI,IAAIE,OAAA,CAAQD,KAAA,CAAME,CAAA,EAAGF,KAAA,CAAMG,CAAA,EAAGH,KAAA,CAAMI,CAAA,EAAGJ,KAAA,CAAMK,CAAC;MAC1E;IACF;EACF;EAEDC,SAASC,EAAA,EAAIC,EAAA,EAAIC,MAAA,EAAQ;IACvB,MAAMC,CAAA,GAAI,KAAKlB,MAAA,CAAO,CAAC,IAAIe,EAAA,IAAM,KAAKf,MAAA,CAAO,KAAKA,MAAA,CAAOI,MAAA,GAAS,CAAC,IAAI,KAAKJ,MAAA,CAAO,CAAC;IACpF,MAAMmB,CAAA,GAAI,KAAKlB,MAAA,CAAO,CAAC,IAAIe,EAAA,IAAM,KAAKf,MAAA,CAAO,KAAKA,MAAA,CAAOG,MAAA,GAAS,CAAC,IAAI,KAAKH,MAAA,CAAO,CAAC;IAEpFmB,gBAAA,CAA4B,KAAKtB,OAAA,EAAS,KAAKC,OAAA,EAAS,KAAKC,MAAA,EAAQ,KAAKC,MAAA,EAAQ,KAAKC,aAAA,EAAegB,CAAA,EAAGC,CAAA,EAAGF,MAAM;EACnH;AACH", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}