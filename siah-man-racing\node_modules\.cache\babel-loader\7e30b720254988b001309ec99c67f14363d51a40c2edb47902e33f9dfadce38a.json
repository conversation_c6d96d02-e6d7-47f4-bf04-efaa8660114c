{"ast": null, "code": "var __defProp = Object.defineProperty;\nvar __defNormalProp = (obj, key, value) => key in obj ? __defProp(obj, key, {\n  enumerable: true,\n  configurable: true,\n  writable: true,\n  value\n}) : obj[key] = value;\nvar __publicField = (obj, key, value) => {\n  __defNormalProp(obj, typeof key !== \"symbol\" ? key + \"\" : key, value);\n  return value;\n};\nimport { Pass, FullScreenQuad } from \"./Pass.js\";\nimport { Vector2, Color, WebGLRenderTarget, MeshDepthMaterial, DoubleSide, RGBADepthPacking, NoBlending, UniformsUtils, ShaderMaterial, Matrix4, Vector3, AdditiveBlending } from \"three\";\nimport { CopyShader } from \"../shaders/CopyShader.js\";\nclass OutlinePass extends Pass {\n  constructor(resolution, scene, camera, selectedObjects) {\n    super();\n    __publicField(this, \"renderScene\");\n    __publicField(this, \"renderCamera\");\n    __publicField(this, \"selectedObjects\");\n    __publicField(this, \"visibleEdgeColor\");\n    __publicField(this, \"hiddenEdgeColor\");\n    __publicField(this, \"edgeGlow\");\n    __publicField(this, \"usePatternTexture\");\n    __publicField(this, \"edgeThickness\");\n    __publicField(this, \"edgeStrength\");\n    __publicField(this, \"downSampleRatio\");\n    __publicField(this, \"pulsePeriod\");\n    __publicField(this, \"resolution\");\n    __publicField(this, \"renderTargetMaskBuffer\");\n    __publicField(this, \"depthMaterial\");\n    __publicField(this, \"prepareMaskMaterial\");\n    __publicField(this, \"renderTargetDepthBuffer\");\n    __publicField(this, \"renderTargetMaskDownSampleBuffer\");\n    __publicField(this, \"renderTargetBlurBuffer1\");\n    __publicField(this, \"renderTargetBlurBuffer2\");\n    __publicField(this, \"edgeDetectionMaterial\");\n    __publicField(this, \"renderTargetEdgeBuffer1\");\n    __publicField(this, \"renderTargetEdgeBuffer2\");\n    __publicField(this, \"separableBlurMaterial1\");\n    __publicField(this, \"separableBlurMaterial2\");\n    __publicField(this, \"overlayMaterial\");\n    __publicField(this, \"materialCopy\");\n    __publicField(this, \"oldClearAlpha\");\n    __publicField(this, \"fsQuad\");\n    __publicField(this, \"tempPulseColor1\");\n    __publicField(this, \"tempPulseColor2\");\n    __publicField(this, \"textureMatrix\");\n    __publicField(this, \"patternTexture\");\n    __publicField(this, \"_visibilityCache\");\n    __publicField(this, \"_oldClearColor\");\n    __publicField(this, \"copyUniforms\");\n    __publicField(this, \"BlurDirectionX\", new Vector2(1, 0));\n    __publicField(this, \"BlurDirectionY\", new Vector2(0, 1));\n    this.renderScene = scene;\n    this.renderCamera = camera;\n    this.selectedObjects = selectedObjects !== void 0 ? selectedObjects : [];\n    this.visibleEdgeColor = new Color(1, 1, 1);\n    this.hiddenEdgeColor = new Color(0.1, 0.04, 0.02);\n    this.edgeGlow = 0;\n    this.usePatternTexture = false;\n    this.edgeThickness = 1;\n    this.edgeStrength = 3;\n    this.downSampleRatio = 2;\n    this.pulsePeriod = 0;\n    this._visibilityCache = /* @__PURE__ */new Map();\n    this.resolution = resolution !== void 0 ? new Vector2(resolution.x, resolution.y) : new Vector2(256, 256);\n    const resx = Math.round(this.resolution.x / this.downSampleRatio);\n    const resy = Math.round(this.resolution.y / this.downSampleRatio);\n    this.renderTargetMaskBuffer = new WebGLRenderTarget(this.resolution.x, this.resolution.y);\n    this.renderTargetMaskBuffer.texture.name = \"OutlinePass.mask\";\n    this.renderTargetMaskBuffer.texture.generateMipmaps = false;\n    this.depthMaterial = new MeshDepthMaterial();\n    this.depthMaterial.side = DoubleSide;\n    this.depthMaterial.depthPacking = RGBADepthPacking;\n    this.depthMaterial.blending = NoBlending;\n    this.prepareMaskMaterial = this.getPrepareMaskMaterial();\n    this.prepareMaskMaterial.side = DoubleSide;\n    this.prepareMaskMaterial.fragmentShader = replaceDepthToViewZ(this.prepareMaskMaterial.fragmentShader, this.renderCamera);\n    this.renderTargetDepthBuffer = new WebGLRenderTarget(this.resolution.x, this.resolution.y);\n    this.renderTargetDepthBuffer.texture.name = \"OutlinePass.depth\";\n    this.renderTargetDepthBuffer.texture.generateMipmaps = false;\n    this.renderTargetMaskDownSampleBuffer = new WebGLRenderTarget(resx, resy);\n    this.renderTargetMaskDownSampleBuffer.texture.name = \"OutlinePass.depthDownSample\";\n    this.renderTargetMaskDownSampleBuffer.texture.generateMipmaps = false;\n    this.renderTargetBlurBuffer1 = new WebGLRenderTarget(resx, resy);\n    this.renderTargetBlurBuffer1.texture.name = \"OutlinePass.blur1\";\n    this.renderTargetBlurBuffer1.texture.generateMipmaps = false;\n    this.renderTargetBlurBuffer2 = new WebGLRenderTarget(Math.round(resx / 2), Math.round(resy / 2));\n    this.renderTargetBlurBuffer2.texture.name = \"OutlinePass.blur2\";\n    this.renderTargetBlurBuffer2.texture.generateMipmaps = false;\n    this.edgeDetectionMaterial = this.getEdgeDetectionMaterial();\n    this.renderTargetEdgeBuffer1 = new WebGLRenderTarget(resx, resy);\n    this.renderTargetEdgeBuffer1.texture.name = \"OutlinePass.edge1\";\n    this.renderTargetEdgeBuffer1.texture.generateMipmaps = false;\n    this.renderTargetEdgeBuffer2 = new WebGLRenderTarget(Math.round(resx / 2), Math.round(resy / 2));\n    this.renderTargetEdgeBuffer2.texture.name = \"OutlinePass.edge2\";\n    this.renderTargetEdgeBuffer2.texture.generateMipmaps = false;\n    const MAX_EDGE_THICKNESS = 4;\n    const MAX_EDGE_GLOW = 4;\n    this.separableBlurMaterial1 = this.getSeperableBlurMaterial(MAX_EDGE_THICKNESS);\n    this.separableBlurMaterial1.uniforms[\"texSize\"].value.set(resx, resy);\n    this.separableBlurMaterial1.uniforms[\"kernelRadius\"].value = 1;\n    this.separableBlurMaterial2 = this.getSeperableBlurMaterial(MAX_EDGE_GLOW);\n    this.separableBlurMaterial2.uniforms[\"texSize\"].value.set(Math.round(resx / 2), Math.round(resy / 2));\n    this.separableBlurMaterial2.uniforms[\"kernelRadius\"].value = MAX_EDGE_GLOW;\n    this.overlayMaterial = this.getOverlayMaterial();\n    if (CopyShader === void 0) console.error(\"THREE.OutlinePass relies on CopyShader\");\n    const copyShader = CopyShader;\n    this.copyUniforms = UniformsUtils.clone(copyShader.uniforms);\n    this.copyUniforms[\"opacity\"].value = 1;\n    this.materialCopy = new ShaderMaterial({\n      uniforms: this.copyUniforms,\n      vertexShader: copyShader.vertexShader,\n      fragmentShader: copyShader.fragmentShader,\n      blending: NoBlending,\n      depthTest: false,\n      depthWrite: false,\n      transparent: true\n    });\n    this.enabled = true;\n    this.needsSwap = false;\n    this._oldClearColor = new Color();\n    this.oldClearAlpha = 1;\n    this.fsQuad = new FullScreenQuad(this.materialCopy);\n    this.tempPulseColor1 = new Color();\n    this.tempPulseColor2 = new Color();\n    this.textureMatrix = new Matrix4();\n    function replaceDepthToViewZ(string, camera2) {\n      const type = camera2.isPerspectiveCamera ? \"perspective\" : \"orthographic\";\n      return string.replace(/DEPTH_TO_VIEW_Z/g, type + \"DepthToViewZ\");\n    }\n  }\n  dispose() {\n    this.renderTargetMaskBuffer.dispose();\n    this.renderTargetDepthBuffer.dispose();\n    this.renderTargetMaskDownSampleBuffer.dispose();\n    this.renderTargetBlurBuffer1.dispose();\n    this.renderTargetBlurBuffer2.dispose();\n    this.renderTargetEdgeBuffer1.dispose();\n    this.renderTargetEdgeBuffer2.dispose();\n  }\n  setSize(width, height) {\n    this.renderTargetMaskBuffer.setSize(width, height);\n    this.renderTargetDepthBuffer.setSize(width, height);\n    let resx = Math.round(width / this.downSampleRatio);\n    let resy = Math.round(height / this.downSampleRatio);\n    this.renderTargetMaskDownSampleBuffer.setSize(resx, resy);\n    this.renderTargetBlurBuffer1.setSize(resx, resy);\n    this.renderTargetEdgeBuffer1.setSize(resx, resy);\n    this.separableBlurMaterial1.uniforms[\"texSize\"].value.set(resx, resy);\n    resx = Math.round(resx / 2);\n    resy = Math.round(resy / 2);\n    this.renderTargetBlurBuffer2.setSize(resx, resy);\n    this.renderTargetEdgeBuffer2.setSize(resx, resy);\n    this.separableBlurMaterial2.uniforms[\"texSize\"].value.set(resx, resy);\n  }\n  changeVisibilityOfSelectedObjects(bVisible) {\n    const cache = this._visibilityCache;\n    function gatherSelectedMeshesCallBack(object) {\n      if (object.isMesh) {\n        if (bVisible === true) {\n          object.visible = cache.get(object);\n        } else {\n          cache.set(object, object.visible);\n          object.visible = bVisible;\n        }\n      }\n    }\n    for (let i = 0; i < this.selectedObjects.length; i++) {\n      const selectedObject = this.selectedObjects[i];\n      selectedObject.traverse(gatherSelectedMeshesCallBack);\n    }\n  }\n  changeVisibilityOfNonSelectedObjects(bVisible) {\n    const cache = this._visibilityCache;\n    const selectedMeshes = [];\n    function gatherSelectedMeshesCallBack(object) {\n      if (object.isMesh) selectedMeshes.push(object);\n    }\n    for (let i = 0; i < this.selectedObjects.length; i++) {\n      const selectedObject = this.selectedObjects[i];\n      selectedObject.traverse(gatherSelectedMeshesCallBack);\n    }\n    function VisibilityChangeCallBack(object) {\n      if (object.isMesh || object.isSprite) {\n        let bFound = false;\n        for (let i = 0; i < selectedMeshes.length; i++) {\n          const selectedObjectId = selectedMeshes[i].id;\n          if (selectedObjectId === object.id) {\n            bFound = true;\n            break;\n          }\n        }\n        if (bFound === false) {\n          const visibility = object.visible;\n          if (bVisible === false || cache.get(object) === true) {\n            object.visible = bVisible;\n          }\n          cache.set(object, visibility);\n        }\n      } else if (object.isPoints || object.isLine) {\n        if (bVisible === true) {\n          object.visible = cache.get(object);\n        } else {\n          cache.set(object, object.visible);\n          object.visible = bVisible;\n        }\n      }\n    }\n    this.renderScene.traverse(VisibilityChangeCallBack);\n  }\n  updateTextureMatrix() {\n    this.textureMatrix.set(0.5, 0, 0, 0.5, 0, 0.5, 0, 0.5, 0, 0, 0.5, 0.5, 0, 0, 0, 1);\n    this.textureMatrix.multiply(this.renderCamera.projectionMatrix);\n    this.textureMatrix.multiply(this.renderCamera.matrixWorldInverse);\n  }\n  render(renderer, writeBuffer, readBuffer, deltaTime, maskActive) {\n    if (this.selectedObjects.length > 0) {\n      renderer.getClearColor(this._oldClearColor);\n      this.oldClearAlpha = renderer.getClearAlpha();\n      const oldAutoClear = renderer.autoClear;\n      renderer.autoClear = false;\n      if (maskActive) renderer.state.buffers.stencil.setTest(false);\n      renderer.setClearColor(16777215, 1);\n      this.changeVisibilityOfSelectedObjects(false);\n      const currentBackground = this.renderScene.background;\n      this.renderScene.background = null;\n      this.renderScene.overrideMaterial = this.depthMaterial;\n      renderer.setRenderTarget(this.renderTargetDepthBuffer);\n      renderer.clear();\n      renderer.render(this.renderScene, this.renderCamera);\n      this.changeVisibilityOfSelectedObjects(true);\n      this._visibilityCache.clear();\n      this.updateTextureMatrix();\n      this.changeVisibilityOfNonSelectedObjects(false);\n      this.renderScene.overrideMaterial = this.prepareMaskMaterial;\n      this.prepareMaskMaterial.uniforms[\"cameraNearFar\"].value.set(this.renderCamera.near, this.renderCamera.far);\n      this.prepareMaskMaterial.uniforms[\"depthTexture\"].value = this.renderTargetDepthBuffer.texture;\n      this.prepareMaskMaterial.uniforms[\"textureMatrix\"].value = this.textureMatrix;\n      renderer.setRenderTarget(this.renderTargetMaskBuffer);\n      renderer.clear();\n      renderer.render(this.renderScene, this.renderCamera);\n      this.renderScene.overrideMaterial = null;\n      this.changeVisibilityOfNonSelectedObjects(true);\n      this._visibilityCache.clear();\n      this.renderScene.background = currentBackground;\n      this.fsQuad.material = this.materialCopy;\n      this.copyUniforms[\"tDiffuse\"].value = this.renderTargetMaskBuffer.texture;\n      renderer.setRenderTarget(this.renderTargetMaskDownSampleBuffer);\n      renderer.clear();\n      this.fsQuad.render(renderer);\n      this.tempPulseColor1.copy(this.visibleEdgeColor);\n      this.tempPulseColor2.copy(this.hiddenEdgeColor);\n      if (this.pulsePeriod > 0) {\n        const scalar = (1 + 0.25) / 2 + Math.cos(performance.now() * 0.01 / this.pulsePeriod) * (1 - 0.25) / 2;\n        this.tempPulseColor1.multiplyScalar(scalar);\n        this.tempPulseColor2.multiplyScalar(scalar);\n      }\n      this.fsQuad.material = this.edgeDetectionMaterial;\n      this.edgeDetectionMaterial.uniforms[\"maskTexture\"].value = this.renderTargetMaskDownSampleBuffer.texture;\n      this.edgeDetectionMaterial.uniforms[\"texSize\"].value.set(this.renderTargetMaskDownSampleBuffer.width, this.renderTargetMaskDownSampleBuffer.height);\n      this.edgeDetectionMaterial.uniforms[\"visibleEdgeColor\"].value = this.tempPulseColor1;\n      this.edgeDetectionMaterial.uniforms[\"hiddenEdgeColor\"].value = this.tempPulseColor2;\n      renderer.setRenderTarget(this.renderTargetEdgeBuffer1);\n      renderer.clear();\n      this.fsQuad.render(renderer);\n      this.fsQuad.material = this.separableBlurMaterial1;\n      this.separableBlurMaterial1.uniforms[\"colorTexture\"].value = this.renderTargetEdgeBuffer1.texture;\n      this.separableBlurMaterial1.uniforms[\"direction\"].value = this.BlurDirectionX;\n      this.separableBlurMaterial1.uniforms[\"kernelRadius\"].value = this.edgeThickness;\n      renderer.setRenderTarget(this.renderTargetBlurBuffer1);\n      renderer.clear();\n      this.fsQuad.render(renderer);\n      this.separableBlurMaterial1.uniforms[\"colorTexture\"].value = this.renderTargetBlurBuffer1.texture;\n      this.separableBlurMaterial1.uniforms[\"direction\"].value = this.BlurDirectionY;\n      renderer.setRenderTarget(this.renderTargetEdgeBuffer1);\n      renderer.clear();\n      this.fsQuad.render(renderer);\n      this.fsQuad.material = this.separableBlurMaterial2;\n      this.separableBlurMaterial2.uniforms[\"colorTexture\"].value = this.renderTargetEdgeBuffer1.texture;\n      this.separableBlurMaterial2.uniforms[\"direction\"].value = this.BlurDirectionX;\n      renderer.setRenderTarget(this.renderTargetBlurBuffer2);\n      renderer.clear();\n      this.fsQuad.render(renderer);\n      this.separableBlurMaterial2.uniforms[\"colorTexture\"].value = this.renderTargetBlurBuffer2.texture;\n      this.separableBlurMaterial2.uniforms[\"direction\"].value = this.BlurDirectionY;\n      renderer.setRenderTarget(this.renderTargetEdgeBuffer2);\n      renderer.clear();\n      this.fsQuad.render(renderer);\n      this.fsQuad.material = this.overlayMaterial;\n      this.overlayMaterial.uniforms[\"maskTexture\"].value = this.renderTargetMaskBuffer.texture;\n      this.overlayMaterial.uniforms[\"edgeTexture1\"].value = this.renderTargetEdgeBuffer1.texture;\n      this.overlayMaterial.uniforms[\"edgeTexture2\"].value = this.renderTargetEdgeBuffer2.texture;\n      this.overlayMaterial.uniforms[\"patternTexture\"].value = this.patternTexture;\n      this.overlayMaterial.uniforms[\"edgeStrength\"].value = this.edgeStrength;\n      this.overlayMaterial.uniforms[\"edgeGlow\"].value = this.edgeGlow;\n      this.overlayMaterial.uniforms[\"usePatternTexture\"].value = this.usePatternTexture;\n      if (maskActive) renderer.state.buffers.stencil.setTest(true);\n      renderer.setRenderTarget(readBuffer);\n      this.fsQuad.render(renderer);\n      renderer.setClearColor(this._oldClearColor, this.oldClearAlpha);\n      renderer.autoClear = oldAutoClear;\n    }\n    if (this.renderToScreen) {\n      this.fsQuad.material = this.materialCopy;\n      this.copyUniforms[\"tDiffuse\"].value = readBuffer.texture;\n      renderer.setRenderTarget(null);\n      this.fsQuad.render(renderer);\n    }\n  }\n  getPrepareMaskMaterial() {\n    return new ShaderMaterial({\n      uniforms: {\n        depthTexture: {\n          value: null\n        },\n        cameraNearFar: {\n          value: new Vector2(0.5, 0.5)\n        },\n        textureMatrix: {\n          value: null\n        }\n      },\n      vertexShader: `#include <morphtarget_pars_vertex>\n\t\t\t\t#include <skinning_pars_vertex>\n\t\t\t\tvarying vec4 projTexCoord;\n\t\t\t\tvarying vec4 vPosition;\n\t\t\t\tuniform mat4 textureMatrix;\n\t\t\t\tvoid main() {\n\t\t\t\t\t#include <skinbase_vertex>\n\t\t\t\t\t#include <begin_vertex>\n\t\t\t\t\t#include <morphtarget_vertex>\n\t\t\t\t\t#include <skinning_vertex>\n\t\t\t\t\t#include <project_vertex>\n\t\t\t\t\tvPosition = mvPosition;\n\t\t\t\t\tvec4 worldPosition = modelMatrix * vec4( transformed, 1.0 );\n\t\t\t\t\tprojTexCoord = textureMatrix * worldPosition;\n\t\t\t\t}`,\n      fragmentShader: `#include <packing>\n\t\t\t\tvarying vec4 vPosition;\n\t\t\t\tvarying vec4 projTexCoord;\n\t\t\t\tuniform sampler2D depthTexture;\n\t\t\t\tuniform vec2 cameraNearFar;\n\t\t\t\tvoid main() {\n\t\t\t\t\tfloat depth = unpackRGBAToDepth(texture2DProj( depthTexture, projTexCoord ));\n\t\t\t\t\tfloat viewZ = - DEPTH_TO_VIEW_Z( depth, cameraNearFar.x, cameraNearFar.y );\n\t\t\t\t\tfloat depthTest = (-vPosition.z > viewZ) ? 1.0 : 0.0;\n\t\t\t\t\tgl_FragColor = vec4(0.0, depthTest, 1.0, 1.0);\n\t\t\t\t}`\n    });\n  }\n  getEdgeDetectionMaterial() {\n    return new ShaderMaterial({\n      uniforms: {\n        maskTexture: {\n          value: null\n        },\n        texSize: {\n          value: new Vector2(0.5, 0.5)\n        },\n        visibleEdgeColor: {\n          value: new Vector3(1, 1, 1)\n        },\n        hiddenEdgeColor: {\n          value: new Vector3(1, 1, 1)\n        }\n      },\n      vertexShader: `varying vec2 vUv;\n\t\t\t\tvoid main() {\n\t\t\t\t\tvUv = uv;\n\t\t\t\t\tgl_Position = projectionMatrix * modelViewMatrix * vec4( position, 1.0 );\n\t\t\t\t}`,\n      fragmentShader: `varying vec2 vUv;\n\t\t\t\tuniform sampler2D maskTexture;\n\t\t\t\tuniform vec2 texSize;\n\t\t\t\tuniform vec3 visibleEdgeColor;\n\t\t\t\tuniform vec3 hiddenEdgeColor;\n\t\t\t\tvoid main() {\n\t\t\t\t\tvec2 invSize = 1.0 / texSize;\n\t\t\t\t\tvec4 uvOffset = vec4(1.0, 0.0, 0.0, 1.0) * vec4(invSize, invSize);\n\t\t\t\t\tvec4 c1 = texture2D( maskTexture, vUv + uvOffset.xy);\n\t\t\t\t\tvec4 c2 = texture2D( maskTexture, vUv - uvOffset.xy);\n\t\t\t\t\tvec4 c3 = texture2D( maskTexture, vUv + uvOffset.yw);\n\t\t\t\t\tvec4 c4 = texture2D( maskTexture, vUv - uvOffset.yw);\n\t\t\t\t\tfloat diff1 = (c1.r - c2.r)*0.5;\n\t\t\t\t\tfloat diff2 = (c3.r - c4.r)*0.5;\n\t\t\t\t\tfloat d = length( vec2(diff1, diff2) );\n\t\t\t\t\tfloat a1 = min(c1.g, c2.g);\n\t\t\t\t\tfloat a2 = min(c3.g, c4.g);\n\t\t\t\t\tfloat visibilityFactor = min(a1, a2);\n\t\t\t\t\tvec3 edgeColor = 1.0 - visibilityFactor > 0.001 ? visibleEdgeColor : hiddenEdgeColor;\n\t\t\t\t\tgl_FragColor = vec4(edgeColor, 1.0) * vec4(d);\n\t\t\t\t}`\n    });\n  }\n  getSeperableBlurMaterial(maxRadius) {\n    return new ShaderMaterial({\n      defines: {\n        MAX_RADIUS: maxRadius\n      },\n      uniforms: {\n        colorTexture: {\n          value: null\n        },\n        texSize: {\n          value: new Vector2(0.5, 0.5)\n        },\n        direction: {\n          value: new Vector2(0.5, 0.5)\n        },\n        kernelRadius: {\n          value: 1\n        }\n      },\n      vertexShader: `varying vec2 vUv;\n\t\t\t\tvoid main() {\n\t\t\t\t\tvUv = uv;\n\t\t\t\t\tgl_Position = projectionMatrix * modelViewMatrix * vec4( position, 1.0 );\n\t\t\t\t}`,\n      fragmentShader: `#include <common>\n\t\t\t\tvarying vec2 vUv;\n\t\t\t\tuniform sampler2D colorTexture;\n\t\t\t\tuniform vec2 texSize;\n\t\t\t\tuniform vec2 direction;\n\t\t\t\tuniform float kernelRadius;\n\t\t\t\tfloat gaussianPdf(in float x, in float sigma) {\n\t\t\t\t\treturn 0.39894 * exp( -0.5 * x * x/( sigma * sigma))/sigma;\n\t\t\t\t}\n\t\t\t\tvoid main() {\n\t\t\t\t\tvec2 invSize = 1.0 / texSize;\n\t\t\t\t\tfloat weightSum = gaussianPdf(0.0, kernelRadius);\n\t\t\t\t\tvec4 diffuseSum = texture2D( colorTexture, vUv) * weightSum;\n\t\t\t\t\tvec2 delta = direction * invSize * kernelRadius/float(MAX_RADIUS);\n\t\t\t\t\tvec2 uvOffset = delta;\n\t\t\t\t\tfor( int i = 1; i <= MAX_RADIUS; i ++ ) {\n\t\t\t\t\t\tfloat w = gaussianPdf(uvOffset.x, kernelRadius);\n\t\t\t\t\t\tvec4 sample1 = texture2D( colorTexture, vUv + uvOffset);\n\t\t\t\t\t\tvec4 sample2 = texture2D( colorTexture, vUv - uvOffset);\n\t\t\t\t\t\tdiffuseSum += ((sample1 + sample2) * w);\n\t\t\t\t\t\tweightSum += (2.0 * w);\n\t\t\t\t\t\tuvOffset += delta;\n\t\t\t\t\t}\n\t\t\t\t\tgl_FragColor = diffuseSum/weightSum;\n\t\t\t\t}`\n    });\n  }\n  getOverlayMaterial() {\n    return new ShaderMaterial({\n      uniforms: {\n        maskTexture: {\n          value: null\n        },\n        edgeTexture1: {\n          value: null\n        },\n        edgeTexture2: {\n          value: null\n        },\n        patternTexture: {\n          value: null\n        },\n        edgeStrength: {\n          value: 1\n        },\n        edgeGlow: {\n          value: 1\n        },\n        usePatternTexture: {\n          value: 0\n        }\n      },\n      vertexShader: `varying vec2 vUv;\n\t\t\t\tvoid main() {\n\t\t\t\t\tvUv = uv;\n\t\t\t\t\tgl_Position = projectionMatrix * modelViewMatrix * vec4( position, 1.0 );\n\t\t\t\t}`,\n      fragmentShader: `varying vec2 vUv;\n\t\t\t\tuniform sampler2D maskTexture;\n\t\t\t\tuniform sampler2D edgeTexture1;\n\t\t\t\tuniform sampler2D edgeTexture2;\n\t\t\t\tuniform sampler2D patternTexture;\n\t\t\t\tuniform float edgeStrength;\n\t\t\t\tuniform float edgeGlow;\n\t\t\t\tuniform bool usePatternTexture;\n\t\t\t\tvoid main() {\n\t\t\t\t\tvec4 edgeValue1 = texture2D(edgeTexture1, vUv);\n\t\t\t\t\tvec4 edgeValue2 = texture2D(edgeTexture2, vUv);\n\t\t\t\t\tvec4 maskColor = texture2D(maskTexture, vUv);\n\t\t\t\t\tvec4 patternColor = texture2D(patternTexture, 6.0 * vUv);\n\t\t\t\t\tfloat visibilityFactor = 1.0 - maskColor.g > 0.0 ? 1.0 : 0.5;\n\t\t\t\t\tvec4 edgeValue = edgeValue1 + edgeValue2 * edgeGlow;\n\t\t\t\t\tvec4 finalColor = edgeStrength * maskColor.r * edgeValue;\n\t\t\t\t\tif(usePatternTexture)\n\t\t\t\t\t\tfinalColor += + visibilityFactor * (1.0 - maskColor.r) * (1.0 - patternColor.r);\n\t\t\t\t\tgl_FragColor = finalColor;\n\t\t\t\t}`,\n      blending: AdditiveBlending,\n      depthTest: false,\n      depthWrite: false,\n      transparent: true\n    });\n  }\n}\nexport { OutlinePass };", "map": {"version": 3, "names": ["OutlinePass", "Pass", "constructor", "resolution", "scene", "camera", "selectedObjects", "__publicField", "Vector2", "renderScene", "renderCamera", "visibleEdgeColor", "Color", "hiddenEdgeColor", "edgeGlow", "usePatternTexture", "edgeThickness", "edgeStrength", "downSampleRatio", "pulsePeriod", "_visibilityCache", "Map", "x", "y", "resx", "Math", "round", "resy", "renderTargetMaskBuffer", "WebGLRenderTarget", "texture", "name", "generateMipmaps", "depthMaterial", "MeshDepthMaterial", "side", "DoubleSide", "depthPacking", "RGBADepthPacking", "blending", "NoBlending", "prepareMaskMaterial", "getPrepareMaskMaterial", "fragmentShader", "replaceDepthToViewZ", "renderTargetDepthBuffer", "renderTargetMaskDownSampleBuffer", "renderTargetBlurBuffer1", "renderTargetBlurBuffer2", "edgeDetectionMaterial", "getEdgeDetectionMaterial", "renderTargetEdgeBuffer1", "renderTargetEdgeBuffer2", "MAX_EDGE_THICKNESS", "MAX_EDGE_GLOW", "separableBlurMaterial1", "getSeperableBlurMaterial", "uniforms", "value", "set", "separableBlurMaterial2", "overlayMaterial", "getOverlayMaterial", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "console", "error", "copyShader", "copyUniforms", "UniformsUtils", "clone", "materialCopy", "ShaderMaterial", "vertexShader", "depthTest", "depthWrite", "transparent", "enabled", "needsSwap", "_oldClearColor", "oldClearAlpha", "fsQuad", "FullScreenQuad", "tempPulseColor1", "tempPulseColor2", "textureMatrix", "Matrix4", "string", "camera2", "type", "isPerspectiveCamera", "replace", "dispose", "setSize", "width", "height", "changeVisibilityOfSelectedObjects", "bVisible", "cache", "gatherSelectedMeshesCallBack", "object", "<PERSON><PERSON><PERSON>", "visible", "get", "i", "length", "selectedObject", "traverse", "changeVisibilityOfNonSelectedObjects", "<PERSON><PERSON><PERSON><PERSON>", "push", "VisibilityChangeCallBack", "isSprite", "bFound", "selectedObjectId", "id", "visibility", "isPoints", "isLine", "updateTextureMatrix", "multiply", "projectionMatrix", "matrixWorldInverse", "render", "renderer", "writeBuffer", "readBuffer", "deltaTime", "maskActive", "getClearColor", "getClearAlpha", "oldAutoClear", "autoClear", "state", "buffers", "stencil", "setTest", "setClearColor", "currentBackground", "background", "overrideMaterial", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "clear", "near", "far", "material", "copy", "scalar", "cos", "performance", "now", "multiplyScalar", "BlurDirectionX", "BlurDirectionY", "patternTexture", "renderToScreen", "depthTexture", "cameraNearFar", "maskTexture", "texSize", "Vector3", "maxRadius", "defines", "MAX_RADIUS", "colorTexture", "direction", "kernelRadius", "edgeTexture1", "edgeTexture2", "AdditiveBlending"], "sources": ["C:\\Users\\<USER>\\Documents\\Apps_Creations\\SiahMan_Racing4Speed_WebGame\\siah-man-racing\\node_modules\\src\\postprocessing\\OutlinePass.ts"], "sourcesContent": ["import { Pass, FullScreenQuad } from './Pass'\nimport {\n  AdditiveBlending,\n  Camera,\n  Color,\n  DoubleSide,\n  Line,\n  Matrix4,\n  Mesh,\n  MeshDepthMaterial,\n  NoBlending,\n  Object3D,\n  PerspectiveCamera,\n  Points,\n  RGBADepthPacking,\n  Scene,\n  ShaderMaterial,\n  Sprite,\n  Texture,\n  UniformsUtils,\n  Vector2,\n  Vector3,\n  WebG<PERSON><PERSON>er,\n  WebGLRenderTarget,\n} from 'three'\nimport { CopyShader } from '../shaders/CopyShader'\n\nclass OutlinePass extends Pass {\n  public renderScene: Scene\n  public renderCamera: Camera\n  public selectedObjects: Object3D[]\n  public visibleEdgeColor: Color\n  public hiddenEdgeColor: Color\n  public edgeGlow: number\n  public usePatternTexture: boolean\n  public edgeThickness: number\n  public edgeStrength: number\n  public downSampleRatio: number\n  public pulsePeriod: number\n  public resolution: Vector2\n  public renderTargetMaskBuffer: WebGLRenderTarget\n  public depthMaterial: MeshDepthMaterial\n  public prepareMaskMaterial: ShaderMaterial\n  public renderTargetDepthBuffer: WebGLRenderTarget\n  public renderTargetMaskDownSampleBuffer: WebGLRenderTarget\n  public renderTargetBlurBuffer1: WebGLRenderTarget\n  public renderTargetBlurBuffer2: WebGLRenderTarget\n  public edgeDetectionMaterial: ShaderMaterial\n  public renderTargetEdgeBuffer1: WebGLRenderTarget\n  public renderTargetEdgeBuffer2: WebGLRenderTarget\n  public separableBlurMaterial1: ShaderMaterial\n  public separableBlurMaterial2: ShaderMaterial\n  public overlayMaterial: ShaderMaterial\n  public materialCopy: ShaderMaterial\n  public oldClearAlpha: number\n  public fsQuad: FullScreenQuad\n  public tempPulseColor1: Color\n  public tempPulseColor2: Color\n  public textureMatrix: Matrix4\n  public patternTexture?: Texture\n\n  private _visibilityCache: Map<Object3D, boolean>\n  private _oldClearColor: Color\n\n  public copyUniforms\n\n  public BlurDirectionX = new Vector2(1.0, 0.0)\n  public BlurDirectionY = new Vector2(0.0, 1.0)\n\n  constructor(resolution: Vector2, scene: Scene, camera: Camera, selectedObjects?: Object3D[]) {\n    super()\n\n    this.renderScene = scene\n    this.renderCamera = camera\n    this.selectedObjects = selectedObjects !== undefined ? selectedObjects : []\n    this.visibleEdgeColor = new Color(1, 1, 1)\n    this.hiddenEdgeColor = new Color(0.1, 0.04, 0.02)\n    this.edgeGlow = 0.0\n    this.usePatternTexture = false\n    this.edgeThickness = 1.0\n    this.edgeStrength = 3.0\n    this.downSampleRatio = 2\n    this.pulsePeriod = 0\n\n    this._visibilityCache = new Map()\n\n    this.resolution = resolution !== undefined ? new Vector2(resolution.x, resolution.y) : new Vector2(256, 256)\n\n    const resx = Math.round(this.resolution.x / this.downSampleRatio)\n    const resy = Math.round(this.resolution.y / this.downSampleRatio)\n\n    this.renderTargetMaskBuffer = new WebGLRenderTarget(this.resolution.x, this.resolution.y)\n    this.renderTargetMaskBuffer.texture.name = 'OutlinePass.mask'\n    this.renderTargetMaskBuffer.texture.generateMipmaps = false\n\n    this.depthMaterial = new MeshDepthMaterial()\n    this.depthMaterial.side = DoubleSide\n    this.depthMaterial.depthPacking = RGBADepthPacking\n    this.depthMaterial.blending = NoBlending\n\n    this.prepareMaskMaterial = this.getPrepareMaskMaterial()\n    this.prepareMaskMaterial.side = DoubleSide\n    this.prepareMaskMaterial.fragmentShader = replaceDepthToViewZ(\n      this.prepareMaskMaterial.fragmentShader,\n      this.renderCamera as PerspectiveCamera,\n    )\n\n    this.renderTargetDepthBuffer = new WebGLRenderTarget(this.resolution.x, this.resolution.y)\n    this.renderTargetDepthBuffer.texture.name = 'OutlinePass.depth'\n    this.renderTargetDepthBuffer.texture.generateMipmaps = false\n\n    this.renderTargetMaskDownSampleBuffer = new WebGLRenderTarget(resx, resy)\n    this.renderTargetMaskDownSampleBuffer.texture.name = 'OutlinePass.depthDownSample'\n    this.renderTargetMaskDownSampleBuffer.texture.generateMipmaps = false\n\n    this.renderTargetBlurBuffer1 = new WebGLRenderTarget(resx, resy)\n    this.renderTargetBlurBuffer1.texture.name = 'OutlinePass.blur1'\n    this.renderTargetBlurBuffer1.texture.generateMipmaps = false\n    this.renderTargetBlurBuffer2 = new WebGLRenderTarget(Math.round(resx / 2), Math.round(resy / 2))\n    this.renderTargetBlurBuffer2.texture.name = 'OutlinePass.blur2'\n    this.renderTargetBlurBuffer2.texture.generateMipmaps = false\n\n    this.edgeDetectionMaterial = this.getEdgeDetectionMaterial()\n    this.renderTargetEdgeBuffer1 = new WebGLRenderTarget(resx, resy)\n    this.renderTargetEdgeBuffer1.texture.name = 'OutlinePass.edge1'\n    this.renderTargetEdgeBuffer1.texture.generateMipmaps = false\n    this.renderTargetEdgeBuffer2 = new WebGLRenderTarget(Math.round(resx / 2), Math.round(resy / 2))\n    this.renderTargetEdgeBuffer2.texture.name = 'OutlinePass.edge2'\n    this.renderTargetEdgeBuffer2.texture.generateMipmaps = false\n\n    const MAX_EDGE_THICKNESS = 4\n    const MAX_EDGE_GLOW = 4\n\n    this.separableBlurMaterial1 = this.getSeperableBlurMaterial(MAX_EDGE_THICKNESS)\n    this.separableBlurMaterial1.uniforms['texSize'].value.set(resx, resy)\n    this.separableBlurMaterial1.uniforms['kernelRadius'].value = 1\n    this.separableBlurMaterial2 = this.getSeperableBlurMaterial(MAX_EDGE_GLOW)\n    this.separableBlurMaterial2.uniforms['texSize'].value.set(Math.round(resx / 2), Math.round(resy / 2))\n    this.separableBlurMaterial2.uniforms['kernelRadius'].value = MAX_EDGE_GLOW\n\n    // Overlay material\n    this.overlayMaterial = this.getOverlayMaterial()\n\n    // copy material\n    if (CopyShader === undefined) console.error('THREE.OutlinePass relies on CopyShader')\n\n    const copyShader = CopyShader\n\n    this.copyUniforms = UniformsUtils.clone(copyShader.uniforms)\n    this.copyUniforms['opacity'].value = 1.0\n\n    this.materialCopy = new ShaderMaterial({\n      uniforms: this.copyUniforms,\n      vertexShader: copyShader.vertexShader,\n      fragmentShader: copyShader.fragmentShader,\n      blending: NoBlending,\n      depthTest: false,\n      depthWrite: false,\n      transparent: true,\n    })\n\n    this.enabled = true\n    this.needsSwap = false\n\n    this._oldClearColor = new Color()\n    this.oldClearAlpha = 1\n\n    this.fsQuad = new FullScreenQuad(this.materialCopy)\n\n    this.tempPulseColor1 = new Color()\n    this.tempPulseColor2 = new Color()\n    this.textureMatrix = new Matrix4()\n\n    function replaceDepthToViewZ(string: string, camera: PerspectiveCamera): string {\n      const type = camera.isPerspectiveCamera ? 'perspective' : 'orthographic'\n\n      return string.replace(/DEPTH_TO_VIEW_Z/g, type + 'DepthToViewZ')\n    }\n  }\n\n  public dispose(): void {\n    this.renderTargetMaskBuffer.dispose()\n    this.renderTargetDepthBuffer.dispose()\n    this.renderTargetMaskDownSampleBuffer.dispose()\n    this.renderTargetBlurBuffer1.dispose()\n    this.renderTargetBlurBuffer2.dispose()\n    this.renderTargetEdgeBuffer1.dispose()\n    this.renderTargetEdgeBuffer2.dispose()\n  }\n\n  public setSize(width: number, height: number): void {\n    this.renderTargetMaskBuffer.setSize(width, height)\n    this.renderTargetDepthBuffer.setSize(width, height)\n\n    let resx = Math.round(width / this.downSampleRatio)\n    let resy = Math.round(height / this.downSampleRatio)\n    this.renderTargetMaskDownSampleBuffer.setSize(resx, resy)\n    this.renderTargetBlurBuffer1.setSize(resx, resy)\n    this.renderTargetEdgeBuffer1.setSize(resx, resy)\n    this.separableBlurMaterial1.uniforms['texSize'].value.set(resx, resy)\n\n    resx = Math.round(resx / 2)\n    resy = Math.round(resy / 2)\n\n    this.renderTargetBlurBuffer2.setSize(resx, resy)\n    this.renderTargetEdgeBuffer2.setSize(resx, resy)\n\n    this.separableBlurMaterial2.uniforms['texSize'].value.set(resx, resy)\n  }\n\n  public changeVisibilityOfSelectedObjects(bVisible: boolean): void {\n    const cache = this._visibilityCache\n\n    function gatherSelectedMeshesCallBack(object: Object3D): void {\n      if ((object as Mesh).isMesh) {\n        if (bVisible === true) {\n          object.visible = cache.get(object) as boolean\n        } else {\n          cache.set(object, object.visible)\n          object.visible = bVisible\n        }\n      }\n    }\n\n    for (let i = 0; i < this.selectedObjects.length; i++) {\n      const selectedObject = this.selectedObjects[i]\n      selectedObject.traverse(gatherSelectedMeshesCallBack)\n    }\n  }\n\n  public changeVisibilityOfNonSelectedObjects(bVisible: boolean): void {\n    const cache = this._visibilityCache\n    const selectedMeshes: Object3D[] = []\n\n    function gatherSelectedMeshesCallBack(object: Object3D): void {\n      if ((object as Mesh).isMesh) selectedMeshes.push(object)\n    }\n\n    for (let i = 0; i < this.selectedObjects.length; i++) {\n      const selectedObject = this.selectedObjects[i]\n      selectedObject.traverse(gatherSelectedMeshesCallBack)\n    }\n\n    function VisibilityChangeCallBack(object: Object3D): void {\n      if ((object as Mesh).isMesh || (object as Sprite).isSprite) {\n        // only meshes and sprites are supported by OutlinePass\n\n        let bFound = false\n\n        for (let i = 0; i < selectedMeshes.length; i++) {\n          const selectedObjectId = selectedMeshes[i].id\n\n          if (selectedObjectId === object.id) {\n            bFound = true\n            break\n          }\n        }\n\n        if (bFound === false) {\n          const visibility = object.visible\n\n          if (bVisible === false || cache.get(object) === true) {\n            object.visible = bVisible\n          }\n\n          cache.set(object, visibility)\n        }\n      } else if ((object as Points).isPoints || (object as Line).isLine) {\n        // the visibilty of points and lines is always set to false in order to\n        // not affect the outline computation\n\n        if (bVisible === true) {\n          object.visible = cache.get(object) as boolean // restore\n        } else {\n          cache.set(object, object.visible)\n          object.visible = bVisible\n        }\n      }\n    }\n\n    this.renderScene.traverse(VisibilityChangeCallBack)\n  }\n\n  public updateTextureMatrix(): void {\n    this.textureMatrix.set(0.5, 0.0, 0.0, 0.5, 0.0, 0.5, 0.0, 0.5, 0.0, 0.0, 0.5, 0.5, 0.0, 0.0, 0.0, 1.0)\n    this.textureMatrix.multiply(this.renderCamera.projectionMatrix)\n    this.textureMatrix.multiply(this.renderCamera.matrixWorldInverse)\n  }\n\n  public render(\n    renderer: WebGLRenderer,\n    writeBuffer: WebGLRenderTarget,\n    readBuffer: WebGLRenderTarget,\n    deltaTime: number,\n    maskActive: boolean,\n  ): void {\n    if (this.selectedObjects.length > 0) {\n      renderer.getClearColor(this._oldClearColor)\n      this.oldClearAlpha = renderer.getClearAlpha()\n      const oldAutoClear = renderer.autoClear\n\n      renderer.autoClear = false\n\n      if (maskActive) renderer.state.buffers.stencil.setTest(false)\n\n      renderer.setClearColor(0xffffff, 1)\n\n      // Make selected objects invisible\n      this.changeVisibilityOfSelectedObjects(false)\n\n      const currentBackground = this.renderScene.background\n      this.renderScene.background = null\n\n      // 1. Draw Non Selected objects in the depth buffer\n      this.renderScene.overrideMaterial = this.depthMaterial\n      renderer.setRenderTarget(this.renderTargetDepthBuffer)\n      renderer.clear()\n      renderer.render(this.renderScene, this.renderCamera)\n\n      // Make selected objects visible\n      this.changeVisibilityOfSelectedObjects(true)\n      this._visibilityCache.clear()\n\n      // Update Texture Matrix for Depth compare\n      this.updateTextureMatrix()\n\n      // Make non selected objects invisible, and draw only the selected objects, by comparing the depth buffer of non selected objects\n      this.changeVisibilityOfNonSelectedObjects(false)\n      this.renderScene.overrideMaterial = this.prepareMaskMaterial\n      this.prepareMaskMaterial.uniforms['cameraNearFar'].value.set(\n        (this.renderCamera as PerspectiveCamera).near,\n        (this.renderCamera as PerspectiveCamera).far,\n      )\n      this.prepareMaskMaterial.uniforms['depthTexture'].value = this.renderTargetDepthBuffer.texture\n      this.prepareMaskMaterial.uniforms['textureMatrix'].value = this.textureMatrix\n      renderer.setRenderTarget(this.renderTargetMaskBuffer)\n      renderer.clear()\n      renderer.render(this.renderScene, this.renderCamera)\n      this.renderScene.overrideMaterial = null\n      this.changeVisibilityOfNonSelectedObjects(true)\n      this._visibilityCache.clear()\n\n      this.renderScene.background = currentBackground\n\n      // 2. Downsample to Half resolution\n      this.fsQuad.material = this.materialCopy\n      this.copyUniforms['tDiffuse'].value = this.renderTargetMaskBuffer.texture\n      renderer.setRenderTarget(this.renderTargetMaskDownSampleBuffer)\n      renderer.clear()\n      this.fsQuad.render(renderer)\n\n      this.tempPulseColor1.copy(this.visibleEdgeColor)\n      this.tempPulseColor2.copy(this.hiddenEdgeColor)\n\n      if (this.pulsePeriod > 0) {\n        const scalar = (1 + 0.25) / 2 + (Math.cos((performance.now() * 0.01) / this.pulsePeriod) * (1.0 - 0.25)) / 2\n        this.tempPulseColor1.multiplyScalar(scalar)\n        this.tempPulseColor2.multiplyScalar(scalar)\n      }\n\n      // 3. Apply Edge Detection Pass\n      this.fsQuad.material = this.edgeDetectionMaterial\n      this.edgeDetectionMaterial.uniforms['maskTexture'].value = this.renderTargetMaskDownSampleBuffer.texture\n      this.edgeDetectionMaterial.uniforms['texSize'].value.set(\n        this.renderTargetMaskDownSampleBuffer.width,\n        this.renderTargetMaskDownSampleBuffer.height,\n      )\n      this.edgeDetectionMaterial.uniforms['visibleEdgeColor'].value = this.tempPulseColor1\n      this.edgeDetectionMaterial.uniforms['hiddenEdgeColor'].value = this.tempPulseColor2\n      renderer.setRenderTarget(this.renderTargetEdgeBuffer1)\n      renderer.clear()\n      this.fsQuad.render(renderer)\n\n      // 4. Apply Blur on Half res\n      this.fsQuad.material = this.separableBlurMaterial1\n      this.separableBlurMaterial1.uniforms['colorTexture'].value = this.renderTargetEdgeBuffer1.texture\n      this.separableBlurMaterial1.uniforms['direction'].value = this.BlurDirectionX\n      this.separableBlurMaterial1.uniforms['kernelRadius'].value = this.edgeThickness\n      renderer.setRenderTarget(this.renderTargetBlurBuffer1)\n      renderer.clear()\n      this.fsQuad.render(renderer)\n      this.separableBlurMaterial1.uniforms['colorTexture'].value = this.renderTargetBlurBuffer1.texture\n      this.separableBlurMaterial1.uniforms['direction'].value = this.BlurDirectionY\n      renderer.setRenderTarget(this.renderTargetEdgeBuffer1)\n      renderer.clear()\n      this.fsQuad.render(renderer)\n\n      // Apply Blur on quarter res\n      this.fsQuad.material = this.separableBlurMaterial2\n      this.separableBlurMaterial2.uniforms['colorTexture'].value = this.renderTargetEdgeBuffer1.texture\n      this.separableBlurMaterial2.uniforms['direction'].value = this.BlurDirectionX\n      renderer.setRenderTarget(this.renderTargetBlurBuffer2)\n      renderer.clear()\n      this.fsQuad.render(renderer)\n      this.separableBlurMaterial2.uniforms['colorTexture'].value = this.renderTargetBlurBuffer2.texture\n      this.separableBlurMaterial2.uniforms['direction'].value = this.BlurDirectionY\n      renderer.setRenderTarget(this.renderTargetEdgeBuffer2)\n      renderer.clear()\n      this.fsQuad.render(renderer)\n\n      // Blend it additively over the input texture\n      this.fsQuad.material = this.overlayMaterial\n      this.overlayMaterial.uniforms['maskTexture'].value = this.renderTargetMaskBuffer.texture\n      this.overlayMaterial.uniforms['edgeTexture1'].value = this.renderTargetEdgeBuffer1.texture\n      this.overlayMaterial.uniforms['edgeTexture2'].value = this.renderTargetEdgeBuffer2.texture\n      this.overlayMaterial.uniforms['patternTexture'].value = this.patternTexture\n      this.overlayMaterial.uniforms['edgeStrength'].value = this.edgeStrength\n      this.overlayMaterial.uniforms['edgeGlow'].value = this.edgeGlow\n      this.overlayMaterial.uniforms['usePatternTexture'].value = this.usePatternTexture\n\n      if (maskActive) renderer.state.buffers.stencil.setTest(true)\n\n      renderer.setRenderTarget(readBuffer)\n      this.fsQuad.render(renderer)\n\n      renderer.setClearColor(this._oldClearColor, this.oldClearAlpha)\n      renderer.autoClear = oldAutoClear\n    }\n\n    if (this.renderToScreen) {\n      this.fsQuad.material = this.materialCopy\n      this.copyUniforms['tDiffuse'].value = readBuffer.texture\n      renderer.setRenderTarget(null)\n      this.fsQuad.render(renderer)\n    }\n  }\n\n  public getPrepareMaskMaterial(): ShaderMaterial {\n    return new ShaderMaterial({\n      uniforms: {\n        depthTexture: { value: null },\n        cameraNearFar: { value: new Vector2(0.5, 0.5) },\n        textureMatrix: { value: null },\n      },\n\n      vertexShader: `#include <morphtarget_pars_vertex>\n\t\t\t\t#include <skinning_pars_vertex>\n\t\t\t\tvarying vec4 projTexCoord;\n\t\t\t\tvarying vec4 vPosition;\n\t\t\t\tuniform mat4 textureMatrix;\n\t\t\t\tvoid main() {\n\t\t\t\t\t#include <skinbase_vertex>\n\t\t\t\t\t#include <begin_vertex>\n\t\t\t\t\t#include <morphtarget_vertex>\n\t\t\t\t\t#include <skinning_vertex>\n\t\t\t\t\t#include <project_vertex>\n\t\t\t\t\tvPosition = mvPosition;\n\t\t\t\t\tvec4 worldPosition = modelMatrix * vec4( transformed, 1.0 );\n\t\t\t\t\tprojTexCoord = textureMatrix * worldPosition;\n\t\t\t\t}`,\n\n      fragmentShader: `#include <packing>\n\t\t\t\tvarying vec4 vPosition;\n\t\t\t\tvarying vec4 projTexCoord;\n\t\t\t\tuniform sampler2D depthTexture;\n\t\t\t\tuniform vec2 cameraNearFar;\n\t\t\t\tvoid main() {\n\t\t\t\t\tfloat depth = unpackRGBAToDepth(texture2DProj( depthTexture, projTexCoord ));\n\t\t\t\t\tfloat viewZ = - DEPTH_TO_VIEW_Z( depth, cameraNearFar.x, cameraNearFar.y );\n\t\t\t\t\tfloat depthTest = (-vPosition.z > viewZ) ? 1.0 : 0.0;\n\t\t\t\t\tgl_FragColor = vec4(0.0, depthTest, 1.0, 1.0);\n\t\t\t\t}`,\n    })\n  }\n\n  public getEdgeDetectionMaterial(): ShaderMaterial {\n    return new ShaderMaterial({\n      uniforms: {\n        maskTexture: { value: null },\n        texSize: { value: new Vector2(0.5, 0.5) },\n        visibleEdgeColor: { value: new Vector3(1.0, 1.0, 1.0) },\n        hiddenEdgeColor: { value: new Vector3(1.0, 1.0, 1.0) },\n      },\n\n      vertexShader: `varying vec2 vUv;\n\t\t\t\tvoid main() {\n\t\t\t\t\tvUv = uv;\n\t\t\t\t\tgl_Position = projectionMatrix * modelViewMatrix * vec4( position, 1.0 );\n\t\t\t\t}`,\n\n      fragmentShader: `varying vec2 vUv;\n\t\t\t\tuniform sampler2D maskTexture;\n\t\t\t\tuniform vec2 texSize;\n\t\t\t\tuniform vec3 visibleEdgeColor;\n\t\t\t\tuniform vec3 hiddenEdgeColor;\n\t\t\t\tvoid main() {\n\t\t\t\t\tvec2 invSize = 1.0 / texSize;\n\t\t\t\t\tvec4 uvOffset = vec4(1.0, 0.0, 0.0, 1.0) * vec4(invSize, invSize);\n\t\t\t\t\tvec4 c1 = texture2D( maskTexture, vUv + uvOffset.xy);\n\t\t\t\t\tvec4 c2 = texture2D( maskTexture, vUv - uvOffset.xy);\n\t\t\t\t\tvec4 c3 = texture2D( maskTexture, vUv + uvOffset.yw);\n\t\t\t\t\tvec4 c4 = texture2D( maskTexture, vUv - uvOffset.yw);\n\t\t\t\t\tfloat diff1 = (c1.r - c2.r)*0.5;\n\t\t\t\t\tfloat diff2 = (c3.r - c4.r)*0.5;\n\t\t\t\t\tfloat d = length( vec2(diff1, diff2) );\n\t\t\t\t\tfloat a1 = min(c1.g, c2.g);\n\t\t\t\t\tfloat a2 = min(c3.g, c4.g);\n\t\t\t\t\tfloat visibilityFactor = min(a1, a2);\n\t\t\t\t\tvec3 edgeColor = 1.0 - visibilityFactor > 0.001 ? visibleEdgeColor : hiddenEdgeColor;\n\t\t\t\t\tgl_FragColor = vec4(edgeColor, 1.0) * vec4(d);\n\t\t\t\t}`,\n    })\n  }\n\n  public getSeperableBlurMaterial(maxRadius: number): ShaderMaterial {\n    return new ShaderMaterial({\n      defines: {\n        MAX_RADIUS: maxRadius,\n      },\n\n      uniforms: {\n        colorTexture: { value: null },\n        texSize: { value: new Vector2(0.5, 0.5) },\n        direction: { value: new Vector2(0.5, 0.5) },\n        kernelRadius: { value: 1.0 },\n      },\n\n      vertexShader: `varying vec2 vUv;\n\t\t\t\tvoid main() {\n\t\t\t\t\tvUv = uv;\n\t\t\t\t\tgl_Position = projectionMatrix * modelViewMatrix * vec4( position, 1.0 );\n\t\t\t\t}`,\n\n      fragmentShader: `#include <common>\n\t\t\t\tvarying vec2 vUv;\n\t\t\t\tuniform sampler2D colorTexture;\n\t\t\t\tuniform vec2 texSize;\n\t\t\t\tuniform vec2 direction;\n\t\t\t\tuniform float kernelRadius;\n\t\t\t\tfloat gaussianPdf(in float x, in float sigma) {\n\t\t\t\t\treturn 0.39894 * exp( -0.5 * x * x/( sigma * sigma))/sigma;\n\t\t\t\t}\n\t\t\t\tvoid main() {\n\t\t\t\t\tvec2 invSize = 1.0 / texSize;\n\t\t\t\t\tfloat weightSum = gaussianPdf(0.0, kernelRadius);\n\t\t\t\t\tvec4 diffuseSum = texture2D( colorTexture, vUv) * weightSum;\n\t\t\t\t\tvec2 delta = direction * invSize * kernelRadius/float(MAX_RADIUS);\n\t\t\t\t\tvec2 uvOffset = delta;\n\t\t\t\t\tfor( int i = 1; i <= MAX_RADIUS; i ++ ) {\n\t\t\t\t\t\tfloat w = gaussianPdf(uvOffset.x, kernelRadius);\n\t\t\t\t\t\tvec4 sample1 = texture2D( colorTexture, vUv + uvOffset);\n\t\t\t\t\t\tvec4 sample2 = texture2D( colorTexture, vUv - uvOffset);\n\t\t\t\t\t\tdiffuseSum += ((sample1 + sample2) * w);\n\t\t\t\t\t\tweightSum += (2.0 * w);\n\t\t\t\t\t\tuvOffset += delta;\n\t\t\t\t\t}\n\t\t\t\t\tgl_FragColor = diffuseSum/weightSum;\n\t\t\t\t}`,\n    })\n  }\n\n  public getOverlayMaterial(): ShaderMaterial {\n    return new ShaderMaterial({\n      uniforms: {\n        maskTexture: { value: null },\n        edgeTexture1: { value: null },\n        edgeTexture2: { value: null },\n        patternTexture: { value: null },\n        edgeStrength: { value: 1.0 },\n        edgeGlow: { value: 1.0 },\n        usePatternTexture: { value: 0.0 },\n      },\n\n      vertexShader: `varying vec2 vUv;\n\t\t\t\tvoid main() {\n\t\t\t\t\tvUv = uv;\n\t\t\t\t\tgl_Position = projectionMatrix * modelViewMatrix * vec4( position, 1.0 );\n\t\t\t\t}`,\n\n      fragmentShader: `varying vec2 vUv;\n\t\t\t\tuniform sampler2D maskTexture;\n\t\t\t\tuniform sampler2D edgeTexture1;\n\t\t\t\tuniform sampler2D edgeTexture2;\n\t\t\t\tuniform sampler2D patternTexture;\n\t\t\t\tuniform float edgeStrength;\n\t\t\t\tuniform float edgeGlow;\n\t\t\t\tuniform bool usePatternTexture;\n\t\t\t\tvoid main() {\n\t\t\t\t\tvec4 edgeValue1 = texture2D(edgeTexture1, vUv);\n\t\t\t\t\tvec4 edgeValue2 = texture2D(edgeTexture2, vUv);\n\t\t\t\t\tvec4 maskColor = texture2D(maskTexture, vUv);\n\t\t\t\t\tvec4 patternColor = texture2D(patternTexture, 6.0 * vUv);\n\t\t\t\t\tfloat visibilityFactor = 1.0 - maskColor.g > 0.0 ? 1.0 : 0.5;\n\t\t\t\t\tvec4 edgeValue = edgeValue1 + edgeValue2 * edgeGlow;\n\t\t\t\t\tvec4 finalColor = edgeStrength * maskColor.r * edgeValue;\n\t\t\t\t\tif(usePatternTexture)\n\t\t\t\t\t\tfinalColor += + visibilityFactor * (1.0 - maskColor.r) * (1.0 - patternColor.r);\n\t\t\t\t\tgl_FragColor = finalColor;\n\t\t\t\t}`,\n      blending: AdditiveBlending,\n      depthTest: false,\n      depthWrite: false,\n      transparent: true,\n    })\n  }\n}\n\nexport { OutlinePass }\n"], "mappings": ";;;;;;;;;;;;;;AA2BA,MAAMA,WAAA,SAAoBC,IAAA,CAAK;EA0C7BC,YAAYC,UAAA,EAAqBC,KAAA,EAAcC,MAAA,EAAgBC,eAAA,EAA8B;IACrF;IA1CDC,aAAA;IACAA,aAAA;IACAA,aAAA;IACAA,aAAA;IACAA,aAAA;IACAA,aAAA;IACAA,aAAA;IACAA,aAAA;IACAA,aAAA;IACAA,aAAA;IACAA,aAAA;IACAA,aAAA;IACAA,aAAA;IACAA,aAAA;IACAA,aAAA;IACAA,aAAA;IACAA,aAAA;IACAA,aAAA;IACAA,aAAA;IACAA,aAAA;IACAA,aAAA;IACAA,aAAA;IACAA,aAAA;IACAA,aAAA;IACAA,aAAA;IACAA,aAAA;IACAA,aAAA;IACAA,aAAA;IACAA,aAAA;IACAA,aAAA;IACAA,aAAA;IACAA,aAAA;IAECA,aAAA;IACAA,aAAA;IAEDA,aAAA;IAEAA,aAAA,yBAAiB,IAAIC,OAAA,CAAQ,GAAK,CAAG;IACrCD,aAAA,yBAAiB,IAAIC,OAAA,CAAQ,GAAK,CAAG;IAK1C,KAAKC,WAAA,GAAcL,KAAA;IACnB,KAAKM,YAAA,GAAeL,MAAA;IACpB,KAAKC,eAAA,GAAkBA,eAAA,KAAoB,SAAYA,eAAA,GAAkB;IACzE,KAAKK,gBAAA,GAAmB,IAAIC,KAAA,CAAM,GAAG,GAAG,CAAC;IACzC,KAAKC,eAAA,GAAkB,IAAID,KAAA,CAAM,KAAK,MAAM,IAAI;IAChD,KAAKE,QAAA,GAAW;IAChB,KAAKC,iBAAA,GAAoB;IACzB,KAAKC,aAAA,GAAgB;IACrB,KAAKC,YAAA,GAAe;IACpB,KAAKC,eAAA,GAAkB;IACvB,KAAKC,WAAA,GAAc;IAEd,KAAAC,gBAAA,sBAAuBC,GAAA;IAE5B,KAAKlB,UAAA,GAAaA,UAAA,KAAe,SAAY,IAAIK,OAAA,CAAQL,UAAA,CAAWmB,CAAA,EAAGnB,UAAA,CAAWoB,CAAC,IAAI,IAAIf,OAAA,CAAQ,KAAK,GAAG;IAE3G,MAAMgB,IAAA,GAAOC,IAAA,CAAKC,KAAA,CAAM,KAAKvB,UAAA,CAAWmB,CAAA,GAAI,KAAKJ,eAAe;IAChE,MAAMS,IAAA,GAAOF,IAAA,CAAKC,KAAA,CAAM,KAAKvB,UAAA,CAAWoB,CAAA,GAAI,KAAKL,eAAe;IAE3D,KAAAU,sBAAA,GAAyB,IAAIC,iBAAA,CAAkB,KAAK1B,UAAA,CAAWmB,CAAA,EAAG,KAAKnB,UAAA,CAAWoB,CAAC;IACnF,KAAAK,sBAAA,CAAuBE,OAAA,CAAQC,IAAA,GAAO;IACtC,KAAAH,sBAAA,CAAuBE,OAAA,CAAQE,eAAA,GAAkB;IAEjD,KAAAC,aAAA,GAAgB,IAAIC,iBAAA;IACzB,KAAKD,aAAA,CAAcE,IAAA,GAAOC,UAAA;IAC1B,KAAKH,aAAA,CAAcI,YAAA,GAAeC,gBAAA;IAClC,KAAKL,aAAA,CAAcM,QAAA,GAAWC,UAAA;IAEzB,KAAAC,mBAAA,GAAsB,KAAKC,sBAAA;IAChC,KAAKD,mBAAA,CAAoBN,IAAA,GAAOC,UAAA;IAChC,KAAKK,mBAAA,CAAoBE,cAAA,GAAiBC,mBAAA,CACxC,KAAKH,mBAAA,CAAoBE,cAAA,EACzB,KAAKjC,YAAA;IAGF,KAAAmC,uBAAA,GAA0B,IAAIhB,iBAAA,CAAkB,KAAK1B,UAAA,CAAWmB,CAAA,EAAG,KAAKnB,UAAA,CAAWoB,CAAC;IACpF,KAAAsB,uBAAA,CAAwBf,OAAA,CAAQC,IAAA,GAAO;IACvC,KAAAc,uBAAA,CAAwBf,OAAA,CAAQE,eAAA,GAAkB;IAEvD,KAAKc,gCAAA,GAAmC,IAAIjB,iBAAA,CAAkBL,IAAA,EAAMG,IAAI;IACnE,KAAAmB,gCAAA,CAAiChB,OAAA,CAAQC,IAAA,GAAO;IAChD,KAAAe,gCAAA,CAAiChB,OAAA,CAAQE,eAAA,GAAkB;IAEhE,KAAKe,uBAAA,GAA0B,IAAIlB,iBAAA,CAAkBL,IAAA,EAAMG,IAAI;IAC1D,KAAAoB,uBAAA,CAAwBjB,OAAA,CAAQC,IAAA,GAAO;IACvC,KAAAgB,uBAAA,CAAwBjB,OAAA,CAAQE,eAAA,GAAkB;IACvD,KAAKgB,uBAAA,GAA0B,IAAInB,iBAAA,CAAkBJ,IAAA,CAAKC,KAAA,CAAMF,IAAA,GAAO,CAAC,GAAGC,IAAA,CAAKC,KAAA,CAAMC,IAAA,GAAO,CAAC,CAAC;IAC1F,KAAAqB,uBAAA,CAAwBlB,OAAA,CAAQC,IAAA,GAAO;IACvC,KAAAiB,uBAAA,CAAwBlB,OAAA,CAAQE,eAAA,GAAkB;IAElD,KAAAiB,qBAAA,GAAwB,KAAKC,wBAAA;IAClC,KAAKC,uBAAA,GAA0B,IAAItB,iBAAA,CAAkBL,IAAA,EAAMG,IAAI;IAC1D,KAAAwB,uBAAA,CAAwBrB,OAAA,CAAQC,IAAA,GAAO;IACvC,KAAAoB,uBAAA,CAAwBrB,OAAA,CAAQE,eAAA,GAAkB;IACvD,KAAKoB,uBAAA,GAA0B,IAAIvB,iBAAA,CAAkBJ,IAAA,CAAKC,KAAA,CAAMF,IAAA,GAAO,CAAC,GAAGC,IAAA,CAAKC,KAAA,CAAMC,IAAA,GAAO,CAAC,CAAC;IAC1F,KAAAyB,uBAAA,CAAwBtB,OAAA,CAAQC,IAAA,GAAO;IACvC,KAAAqB,uBAAA,CAAwBtB,OAAA,CAAQE,eAAA,GAAkB;IAEvD,MAAMqB,kBAAA,GAAqB;IAC3B,MAAMC,aAAA,GAAgB;IAEjB,KAAAC,sBAAA,GAAyB,KAAKC,wBAAA,CAAyBH,kBAAkB;IAC9E,KAAKE,sBAAA,CAAuBE,QAAA,CAAS,SAAS,EAAEC,KAAA,CAAMC,GAAA,CAAInC,IAAA,EAAMG,IAAI;IACpE,KAAK4B,sBAAA,CAAuBE,QAAA,CAAS,cAAc,EAAEC,KAAA,GAAQ;IACxD,KAAAE,sBAAA,GAAyB,KAAKJ,wBAAA,CAAyBF,aAAa;IACzE,KAAKM,sBAAA,CAAuBH,QAAA,CAAS,SAAS,EAAEC,KAAA,CAAMC,GAAA,CAAIlC,IAAA,CAAKC,KAAA,CAAMF,IAAA,GAAO,CAAC,GAAGC,IAAA,CAAKC,KAAA,CAAMC,IAAA,GAAO,CAAC,CAAC;IACpG,KAAKiC,sBAAA,CAAuBH,QAAA,CAAS,cAAc,EAAEC,KAAA,GAAQJ,aAAA;IAGxD,KAAAO,eAAA,GAAkB,KAAKC,kBAAA;IAG5B,IAAIC,UAAA,KAAe,QAAWC,OAAA,CAAQC,KAAA,CAAM,wCAAwC;IAEpF,MAAMC,UAAA,GAAaH,UAAA;IAEnB,KAAKI,YAAA,GAAeC,aAAA,CAAcC,KAAA,CAAMH,UAAA,CAAWT,QAAQ;IACtD,KAAAU,YAAA,CAAa,SAAS,EAAET,KAAA,GAAQ;IAEhC,KAAAY,YAAA,GAAe,IAAIC,cAAA,CAAe;MACrCd,QAAA,EAAU,KAAKU,YAAA;MACfK,YAAA,EAAcN,UAAA,CAAWM,YAAA;MACzB7B,cAAA,EAAgBuB,UAAA,CAAWvB,cAAA;MAC3BJ,QAAA,EAAUC,UAAA;MACViC,SAAA,EAAW;MACXC,UAAA,EAAY;MACZC,WAAA,EAAa;IAAA,CACd;IAED,KAAKC,OAAA,GAAU;IACf,KAAKC,SAAA,GAAY;IAEZ,KAAAC,cAAA,GAAiB,IAAIlE,KAAA;IAC1B,KAAKmE,aAAA,GAAgB;IAErB,KAAKC,MAAA,GAAS,IAAIC,cAAA,CAAe,KAAKX,YAAY;IAE7C,KAAAY,eAAA,GAAkB,IAAItE,KAAA;IACtB,KAAAuE,eAAA,GAAkB,IAAIvE,KAAA;IACtB,KAAAwE,aAAA,GAAgB,IAAIC,OAAA;IAEhB,SAAAzC,oBAAoB0C,MAAA,EAAgBC,OAAA,EAAmC;MACxE,MAAAC,IAAA,GAAOD,OAAA,CAAOE,mBAAA,GAAsB,gBAAgB;MAE1D,OAAOH,MAAA,CAAOI,OAAA,CAAQ,oBAAoBF,IAAA,GAAO,cAAc;IACjE;EACF;EAEOG,QAAA,EAAgB;IACrB,KAAK/D,sBAAA,CAAuB+D,OAAA;IAC5B,KAAK9C,uBAAA,CAAwB8C,OAAA;IAC7B,KAAK7C,gCAAA,CAAiC6C,OAAA;IACtC,KAAK5C,uBAAA,CAAwB4C,OAAA;IAC7B,KAAK3C,uBAAA,CAAwB2C,OAAA;IAC7B,KAAKxC,uBAAA,CAAwBwC,OAAA;IAC7B,KAAKvC,uBAAA,CAAwBuC,OAAA;EAC/B;EAEOC,QAAQC,KAAA,EAAeC,MAAA,EAAsB;IAC7C,KAAAlE,sBAAA,CAAuBgE,OAAA,CAAQC,KAAA,EAAOC,MAAM;IAC5C,KAAAjD,uBAAA,CAAwB+C,OAAA,CAAQC,KAAA,EAAOC,MAAM;IAElD,IAAItE,IAAA,GAAOC,IAAA,CAAKC,KAAA,CAAMmE,KAAA,GAAQ,KAAK3E,eAAe;IAClD,IAAIS,IAAA,GAAOF,IAAA,CAAKC,KAAA,CAAMoE,MAAA,GAAS,KAAK5E,eAAe;IAC9C,KAAA4B,gCAAA,CAAiC8C,OAAA,CAAQpE,IAAA,EAAMG,IAAI;IACnD,KAAAoB,uBAAA,CAAwB6C,OAAA,CAAQpE,IAAA,EAAMG,IAAI;IAC1C,KAAAwB,uBAAA,CAAwByC,OAAA,CAAQpE,IAAA,EAAMG,IAAI;IAC/C,KAAK4B,sBAAA,CAAuBE,QAAA,CAAS,SAAS,EAAEC,KAAA,CAAMC,GAAA,CAAInC,IAAA,EAAMG,IAAI;IAE7DH,IAAA,GAAAC,IAAA,CAAKC,KAAA,CAAMF,IAAA,GAAO,CAAC;IACnBG,IAAA,GAAAF,IAAA,CAAKC,KAAA,CAAMC,IAAA,GAAO,CAAC;IAErB,KAAAqB,uBAAA,CAAwB4C,OAAA,CAAQpE,IAAA,EAAMG,IAAI;IAC1C,KAAAyB,uBAAA,CAAwBwC,OAAA,CAAQpE,IAAA,EAAMG,IAAI;IAE/C,KAAKiC,sBAAA,CAAuBH,QAAA,CAAS,SAAS,EAAEC,KAAA,CAAMC,GAAA,CAAInC,IAAA,EAAMG,IAAI;EACtE;EAEOoE,kCAAkCC,QAAA,EAAyB;IAChE,MAAMC,KAAA,GAAQ,KAAK7E,gBAAA;IAEnB,SAAS8E,6BAA6BC,MAAA,EAAwB;MAC5D,IAAKA,MAAA,CAAgBC,MAAA,EAAQ;QAC3B,IAAIJ,QAAA,KAAa,MAAM;UACdG,MAAA,CAAAE,OAAA,GAAUJ,KAAA,CAAMK,GAAA,CAAIH,MAAM;QAAA,OAC5B;UACCF,KAAA,CAAAtC,GAAA,CAAIwC,MAAA,EAAQA,MAAA,CAAOE,OAAO;UAChCF,MAAA,CAAOE,OAAA,GAAUL,QAAA;QACnB;MACF;IACF;IAEA,SAASO,CAAA,GAAI,GAAGA,CAAA,GAAI,KAAKjG,eAAA,CAAgBkG,MAAA,EAAQD,CAAA,IAAK;MAC9C,MAAAE,cAAA,GAAiB,KAAKnG,eAAA,CAAgBiG,CAAC;MAC7CE,cAAA,CAAeC,QAAA,CAASR,4BAA4B;IACtD;EACF;EAEOS,qCAAqCX,QAAA,EAAyB;IACnE,MAAMC,KAAA,GAAQ,KAAK7E,gBAAA;IACnB,MAAMwF,cAAA,GAA6B;IAEnC,SAASV,6BAA6BC,MAAA,EAAwB;MAC5D,IAAKA,MAAA,CAAgBC,MAAA,EAAQQ,cAAA,CAAeC,IAAA,CAAKV,MAAM;IACzD;IAEA,SAASI,CAAA,GAAI,GAAGA,CAAA,GAAI,KAAKjG,eAAA,CAAgBkG,MAAA,EAAQD,CAAA,IAAK;MAC9C,MAAAE,cAAA,GAAiB,KAAKnG,eAAA,CAAgBiG,CAAC;MAC7CE,cAAA,CAAeC,QAAA,CAASR,4BAA4B;IACtD;IAEA,SAASY,yBAAyBX,MAAA,EAAwB;MACnD,IAAAA,MAAA,CAAgBC,MAAA,IAAWD,MAAA,CAAkBY,QAAA,EAAU;QAG1D,IAAIC,MAAA,GAAS;QAEb,SAAST,CAAA,GAAI,GAAGA,CAAA,GAAIK,cAAA,CAAeJ,MAAA,EAAQD,CAAA,IAAK;UACxC,MAAAU,gBAAA,GAAmBL,cAAA,CAAeL,CAAC,EAAEW,EAAA;UAEvC,IAAAD,gBAAA,KAAqBd,MAAA,CAAOe,EAAA,EAAI;YACzBF,MAAA;YACT;UACF;QACF;QAEA,IAAIA,MAAA,KAAW,OAAO;UACpB,MAAMG,UAAA,GAAahB,MAAA,CAAOE,OAAA;UAE1B,IAAIL,QAAA,KAAa,SAASC,KAAA,CAAMK,GAAA,CAAIH,MAAM,MAAM,MAAM;YACpDA,MAAA,CAAOE,OAAA,GAAUL,QAAA;UACnB;UAEMC,KAAA,CAAAtC,GAAA,CAAIwC,MAAA,EAAQgB,UAAU;QAC9B;MACU,WAAAhB,MAAA,CAAkBiB,QAAA,IAAajB,MAAA,CAAgBkB,MAAA,EAAQ;QAIjE,IAAIrB,QAAA,KAAa,MAAM;UACdG,MAAA,CAAAE,OAAA,GAAUJ,KAAA,CAAMK,GAAA,CAAIH,MAAM;QAAA,OAC5B;UACCF,KAAA,CAAAtC,GAAA,CAAIwC,MAAA,EAAQA,MAAA,CAAOE,OAAO;UAChCF,MAAA,CAAOE,OAAA,GAAUL,QAAA;QACnB;MACF;IACF;IAEK,KAAAvF,WAAA,CAAYiG,QAAA,CAASI,wBAAwB;EACpD;EAEOQ,oBAAA,EAA4B;IACjC,KAAKlC,aAAA,CAAczB,GAAA,CAAI,KAAK,GAAK,GAAK,KAAK,GAAK,KAAK,GAAK,KAAK,GAAK,GAAK,KAAK,KAAK,GAAK,GAAK,GAAK,CAAG;IACrG,KAAKyB,aAAA,CAAcmC,QAAA,CAAS,KAAK7G,YAAA,CAAa8G,gBAAgB;IAC9D,KAAKpC,aAAA,CAAcmC,QAAA,CAAS,KAAK7G,YAAA,CAAa+G,kBAAkB;EAClE;EAEOC,OACLC,QAAA,EACAC,WAAA,EACAC,UAAA,EACAC,SAAA,EACAC,UAAA,EACM;IACF,SAAKzH,eAAA,CAAgBkG,MAAA,GAAS,GAAG;MAC1BmB,QAAA,CAAAK,aAAA,CAAc,KAAKlD,cAAc;MACrC,KAAAC,aAAA,GAAgB4C,QAAA,CAASM,aAAA;MAC9B,MAAMC,YAAA,GAAeP,QAAA,CAASQ,SAAA;MAE9BR,QAAA,CAASQ,SAAA,GAAY;MAEjB,IAAAJ,UAAA,EAAYJ,QAAA,CAASS,KAAA,CAAMC,OAAA,CAAQC,OAAA,CAAQC,OAAA,CAAQ,KAAK;MAEnDZ,QAAA,CAAAa,aAAA,CAAc,UAAU,CAAC;MAGlC,KAAKzC,iCAAA,CAAkC,KAAK;MAEtC,MAAA0C,iBAAA,GAAoB,KAAKhI,WAAA,CAAYiI,UAAA;MAC3C,KAAKjI,WAAA,CAAYiI,UAAA,GAAa;MAGzB,KAAAjI,WAAA,CAAYkI,gBAAA,GAAmB,KAAK1G,aAAA;MAChC0F,QAAA,CAAAiB,eAAA,CAAgB,KAAK/F,uBAAuB;MACrD8E,QAAA,CAASkB,KAAA,CAAM;MACflB,QAAA,CAASD,MAAA,CAAO,KAAKjH,WAAA,EAAa,KAAKC,YAAY;MAGnD,KAAKqF,iCAAA,CAAkC,IAAI;MAC3C,KAAK3E,gBAAA,CAAiByH,KAAA;MAGtB,KAAKvB,mBAAA,CAAoB;MAGzB,KAAKX,oCAAA,CAAqC,KAAK;MAC1C,KAAAlG,WAAA,CAAYkI,gBAAA,GAAmB,KAAKlG,mBAAA;MACzC,KAAKA,mBAAA,CAAoBgB,QAAA,CAAS,eAAe,EAAEC,KAAA,CAAMC,GAAA,CACtD,KAAKjD,YAAA,CAAmCoI,IAAA,EACxC,KAAKpI,YAAA,CAAmCqI,GAAA;MAE3C,KAAKtG,mBAAA,CAAoBgB,QAAA,CAAS,cAAc,EAAEC,KAAA,GAAQ,KAAKb,uBAAA,CAAwBf,OAAA;MACvF,KAAKW,mBAAA,CAAoBgB,QAAA,CAAS,eAAe,EAAEC,KAAA,GAAQ,KAAK0B,aAAA;MACvDuC,QAAA,CAAAiB,eAAA,CAAgB,KAAKhH,sBAAsB;MACpD+F,QAAA,CAASkB,KAAA,CAAM;MACflB,QAAA,CAASD,MAAA,CAAO,KAAKjH,WAAA,EAAa,KAAKC,YAAY;MACnD,KAAKD,WAAA,CAAYkI,gBAAA,GAAmB;MACpC,KAAKhC,oCAAA,CAAqC,IAAI;MAC9C,KAAKvF,gBAAA,CAAiByH,KAAA;MAEtB,KAAKpI,WAAA,CAAYiI,UAAA,GAAaD,iBAAA;MAGzB,KAAAzD,MAAA,CAAOgE,QAAA,GAAW,KAAK1E,YAAA;MAC5B,KAAKH,YAAA,CAAa,UAAU,EAAET,KAAA,GAAQ,KAAK9B,sBAAA,CAAuBE,OAAA;MACzD6F,QAAA,CAAAiB,eAAA,CAAgB,KAAK9F,gCAAgC;MAC9D6E,QAAA,CAASkB,KAAA,CAAM;MACV,KAAA7D,MAAA,CAAO0C,MAAA,CAAOC,QAAQ;MAEtB,KAAAzC,eAAA,CAAgB+D,IAAA,CAAK,KAAKtI,gBAAgB;MAC1C,KAAAwE,eAAA,CAAgB8D,IAAA,CAAK,KAAKpI,eAAe;MAE1C,SAAKM,WAAA,GAAc,GAAG;QACxB,MAAM+H,MAAA,IAAU,IAAI,QAAQ,IAAKzH,IAAA,CAAK0H,GAAA,CAAKC,WAAA,CAAYC,GAAA,CAAI,IAAI,OAAQ,KAAKlI,WAAW,KAAK,IAAM,QAAS;QACtG,KAAA+D,eAAA,CAAgBoE,cAAA,CAAeJ,MAAM;QACrC,KAAA/D,eAAA,CAAgBmE,cAAA,CAAeJ,MAAM;MAC5C;MAGK,KAAAlE,MAAA,CAAOgE,QAAA,GAAW,KAAK/F,qBAAA;MAC5B,KAAKA,qBAAA,CAAsBQ,QAAA,CAAS,aAAa,EAAEC,KAAA,GAAQ,KAAKZ,gCAAA,CAAiChB,OAAA;MACjG,KAAKmB,qBAAA,CAAsBQ,QAAA,CAAS,SAAS,EAAEC,KAAA,CAAMC,GAAA,CACnD,KAAKb,gCAAA,CAAiC+C,KAAA,EACtC,KAAK/C,gCAAA,CAAiCgD,MAAA;MAExC,KAAK7C,qBAAA,CAAsBQ,QAAA,CAAS,kBAAkB,EAAEC,KAAA,GAAQ,KAAKwB,eAAA;MACrE,KAAKjC,qBAAA,CAAsBQ,QAAA,CAAS,iBAAiB,EAAEC,KAAA,GAAQ,KAAKyB,eAAA;MAC3DwC,QAAA,CAAAiB,eAAA,CAAgB,KAAKzF,uBAAuB;MACrDwE,QAAA,CAASkB,KAAA,CAAM;MACV,KAAA7D,MAAA,CAAO0C,MAAA,CAAOC,QAAQ;MAGtB,KAAA3C,MAAA,CAAOgE,QAAA,GAAW,KAAKzF,sBAAA;MAC5B,KAAKA,sBAAA,CAAuBE,QAAA,CAAS,cAAc,EAAEC,KAAA,GAAQ,KAAKP,uBAAA,CAAwBrB,OAAA;MAC1F,KAAKyB,sBAAA,CAAuBE,QAAA,CAAS,WAAW,EAAEC,KAAA,GAAQ,KAAK6F,cAAA;MAC/D,KAAKhG,sBAAA,CAAuBE,QAAA,CAAS,cAAc,EAAEC,KAAA,GAAQ,KAAK1C,aAAA;MACzD2G,QAAA,CAAAiB,eAAA,CAAgB,KAAK7F,uBAAuB;MACrD4E,QAAA,CAASkB,KAAA,CAAM;MACV,KAAA7D,MAAA,CAAO0C,MAAA,CAAOC,QAAQ;MAC3B,KAAKpE,sBAAA,CAAuBE,QAAA,CAAS,cAAc,EAAEC,KAAA,GAAQ,KAAKX,uBAAA,CAAwBjB,OAAA;MAC1F,KAAKyB,sBAAA,CAAuBE,QAAA,CAAS,WAAW,EAAEC,KAAA,GAAQ,KAAK8F,cAAA;MACtD7B,QAAA,CAAAiB,eAAA,CAAgB,KAAKzF,uBAAuB;MACrDwE,QAAA,CAASkB,KAAA,CAAM;MACV,KAAA7D,MAAA,CAAO0C,MAAA,CAAOC,QAAQ;MAGtB,KAAA3C,MAAA,CAAOgE,QAAA,GAAW,KAAKpF,sBAAA;MAC5B,KAAKA,sBAAA,CAAuBH,QAAA,CAAS,cAAc,EAAEC,KAAA,GAAQ,KAAKP,uBAAA,CAAwBrB,OAAA;MAC1F,KAAK8B,sBAAA,CAAuBH,QAAA,CAAS,WAAW,EAAEC,KAAA,GAAQ,KAAK6F,cAAA;MACtD5B,QAAA,CAAAiB,eAAA,CAAgB,KAAK5F,uBAAuB;MACrD2E,QAAA,CAASkB,KAAA,CAAM;MACV,KAAA7D,MAAA,CAAO0C,MAAA,CAAOC,QAAQ;MAC3B,KAAK/D,sBAAA,CAAuBH,QAAA,CAAS,cAAc,EAAEC,KAAA,GAAQ,KAAKV,uBAAA,CAAwBlB,OAAA;MAC1F,KAAK8B,sBAAA,CAAuBH,QAAA,CAAS,WAAW,EAAEC,KAAA,GAAQ,KAAK8F,cAAA;MACtD7B,QAAA,CAAAiB,eAAA,CAAgB,KAAKxF,uBAAuB;MACrDuE,QAAA,CAASkB,KAAA,CAAM;MACV,KAAA7D,MAAA,CAAO0C,MAAA,CAAOC,QAAQ;MAGtB,KAAA3C,MAAA,CAAOgE,QAAA,GAAW,KAAKnF,eAAA;MAC5B,KAAKA,eAAA,CAAgBJ,QAAA,CAAS,aAAa,EAAEC,KAAA,GAAQ,KAAK9B,sBAAA,CAAuBE,OAAA;MACjF,KAAK+B,eAAA,CAAgBJ,QAAA,CAAS,cAAc,EAAEC,KAAA,GAAQ,KAAKP,uBAAA,CAAwBrB,OAAA;MACnF,KAAK+B,eAAA,CAAgBJ,QAAA,CAAS,cAAc,EAAEC,KAAA,GAAQ,KAAKN,uBAAA,CAAwBtB,OAAA;MACnF,KAAK+B,eAAA,CAAgBJ,QAAA,CAAS,gBAAgB,EAAEC,KAAA,GAAQ,KAAK+F,cAAA;MAC7D,KAAK5F,eAAA,CAAgBJ,QAAA,CAAS,cAAc,EAAEC,KAAA,GAAQ,KAAKzC,YAAA;MAC3D,KAAK4C,eAAA,CAAgBJ,QAAA,CAAS,UAAU,EAAEC,KAAA,GAAQ,KAAK5C,QAAA;MACvD,KAAK+C,eAAA,CAAgBJ,QAAA,CAAS,mBAAmB,EAAEC,KAAA,GAAQ,KAAK3C,iBAAA;MAE5D,IAAAgH,UAAA,EAAYJ,QAAA,CAASS,KAAA,CAAMC,OAAA,CAAQC,OAAA,CAAQC,OAAA,CAAQ,IAAI;MAE3DZ,QAAA,CAASiB,eAAA,CAAgBf,UAAU;MAC9B,KAAA7C,MAAA,CAAO0C,MAAA,CAAOC,QAAQ;MAE3BA,QAAA,CAASa,aAAA,CAAc,KAAK1D,cAAA,EAAgB,KAAKC,aAAa;MAC9D4C,QAAA,CAASQ,SAAA,GAAYD,YAAA;IACvB;IAEA,IAAI,KAAKwB,cAAA,EAAgB;MAClB,KAAA1E,MAAA,CAAOgE,QAAA,GAAW,KAAK1E,YAAA;MAC5B,KAAKH,YAAA,CAAa,UAAU,EAAET,KAAA,GAAQmE,UAAA,CAAW/F,OAAA;MACjD6F,QAAA,CAASiB,eAAA,CAAgB,IAAI;MACxB,KAAA5D,MAAA,CAAO0C,MAAA,CAAOC,QAAQ;IAC7B;EACF;EAEOjF,uBAAA,EAAyC;IAC9C,OAAO,IAAI6B,cAAA,CAAe;MACxBd,QAAA,EAAU;QACRkG,YAAA,EAAc;UAAEjG,KAAA,EAAO;QAAK;QAC5BkG,aAAA,EAAe;UAAElG,KAAA,EAAO,IAAIlD,OAAA,CAAQ,KAAK,GAAG;QAAE;QAC9C4E,aAAA,EAAe;UAAE1B,KAAA,EAAO;QAAK;MAC/B;MAEAc,YAAA,EAAc;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;MAgBd7B,cAAA,EAAgB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;IAAA,CAWjB;EACH;EAEOO,yBAAA,EAA2C;IAChD,OAAO,IAAIqB,cAAA,CAAe;MACxBd,QAAA,EAAU;QACRoG,WAAA,EAAa;UAAEnG,KAAA,EAAO;QAAK;QAC3BoG,OAAA,EAAS;UAAEpG,KAAA,EAAO,IAAIlD,OAAA,CAAQ,KAAK,GAAG;QAAE;QACxCG,gBAAA,EAAkB;UAAE+C,KAAA,EAAO,IAAIqG,OAAA,CAAQ,GAAK,GAAK,CAAG;QAAE;QACtDlJ,eAAA,EAAiB;UAAE6C,KAAA,EAAO,IAAIqG,OAAA,CAAQ,GAAK,GAAK,CAAG;QAAE;MACvD;MAEAvF,YAAA,EAAc;AAAA;AAAA;AAAA;AAAA;MAMd7B,cAAA,EAAgB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;IAAA,CAqBjB;EACH;EAEOa,yBAAyBwG,SAAA,EAAmC;IACjE,OAAO,IAAIzF,cAAA,CAAe;MACxB0F,OAAA,EAAS;QACPC,UAAA,EAAYF;MACd;MAEAvG,QAAA,EAAU;QACR0G,YAAA,EAAc;UAAEzG,KAAA,EAAO;QAAK;QAC5BoG,OAAA,EAAS;UAAEpG,KAAA,EAAO,IAAIlD,OAAA,CAAQ,KAAK,GAAG;QAAE;QACxC4J,SAAA,EAAW;UAAE1G,KAAA,EAAO,IAAIlD,OAAA,CAAQ,KAAK,GAAG;QAAE;QAC1C6J,YAAA,EAAc;UAAE3G,KAAA,EAAO;QAAI;MAC7B;MAEAc,YAAA,EAAc;AAAA;AAAA;AAAA;AAAA;MAMd7B,cAAA,EAAgB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;IAAA,CAyBjB;EACH;EAEOmB,mBAAA,EAAqC;IAC1C,OAAO,IAAIS,cAAA,CAAe;MACxBd,QAAA,EAAU;QACRoG,WAAA,EAAa;UAAEnG,KAAA,EAAO;QAAK;QAC3B4G,YAAA,EAAc;UAAE5G,KAAA,EAAO;QAAK;QAC5B6G,YAAA,EAAc;UAAE7G,KAAA,EAAO;QAAK;QAC5B+F,cAAA,EAAgB;UAAE/F,KAAA,EAAO;QAAK;QAC9BzC,YAAA,EAAc;UAAEyC,KAAA,EAAO;QAAI;QAC3B5C,QAAA,EAAU;UAAE4C,KAAA,EAAO;QAAI;QACvB3C,iBAAA,EAAmB;UAAE2C,KAAA,EAAO;QAAI;MAClC;MAEAc,YAAA,EAAc;AAAA;AAAA;AAAA;AAAA;MAMd7B,cAAA,EAAgB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;MAoBhBJ,QAAA,EAAUiI,gBAAA;MACV/F,SAAA,EAAW;MACXC,UAAA,EAAY;MACZC,WAAA,EAAa;IAAA,CACd;EACH;AACF", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}