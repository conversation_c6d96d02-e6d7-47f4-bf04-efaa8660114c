{"ast": null, "code": "import { useFrame } from '@react-three/fiber';\nimport * as React from 'react';\nimport * as THREE from 'three';\n\n/**\n * Integration and compilation: <PERSON><PERSON> (https://twitter.com/CantBeFaraz)\n *\n * Based on:\n *  - https://gkjohnson.github.io/threejs-sandbox/screendoor-transparency/ by <PERSON> (https://github.com/gk<PERSON><PERSON><PERSON>)\n *\n * Note:\n *  - Must depreciate in favor of https://github.com/mrdoob/three.js/issues/10600 when it's ready.\n */\nfunction ShadowAlpha({\n  opacity,\n  alphaMap\n}) {\n  const depthMaterialRef = React.useRef(null);\n  const distanceMaterialRef = React.useRef(null);\n  const uShadowOpacity = React.useRef({\n    value: 1\n  });\n  const uAlphaMap = React.useRef({\n    value: null\n  });\n  const uHasAlphaMap = React.useRef({\n    value: false\n  });\n  React.useLayoutEffect(() => {\n    depthMaterialRef.current.onBeforeCompile = distanceMaterialRef.current.onBeforeCompile = shader => {\n      // Need to get the \"void main\" line dynamically because the lines for\n      // MeshDistanceMaterial and MeshDepthMaterial are different 🤦‍♂️\n      const mainLineStart = shader.fragmentShader.indexOf('void main');\n      let mainLine = '';\n      let ch;\n      let i = mainLineStart;\n      while (ch !== '\\n' && i < mainLineStart + 100) {\n        ch = shader.fragmentShader.charAt(i);\n        mainLine += ch;\n        i++;\n      }\n      mainLine = mainLine.trim();\n      shader.vertexShader = shader.vertexShader.replace('void main() {', `\n        varying vec2 custom_vUv;\n\n        void main() {\n          custom_vUv = uv;\n          \n        `);\n      shader.fragmentShader = shader.fragmentShader.replace(mainLine, `\n          uniform float uShadowOpacity;\n          uniform sampler2D uAlphaMap;\n          uniform bool uHasAlphaMap;\n\n          varying vec2 custom_vUv;\n  \n          float bayerDither2x2( vec2 v ) {\n            return mod( 3.0 * v.y + 2.0 * v.x, 4.0 );\n          }\n    \n          float bayerDither4x4( vec2 v ) {\n            vec2 P1 = mod( v, 2.0 );\n            vec2 P2 = mod( floor( 0.5  * v ), 2.0 );\n            return 4.0 * bayerDither2x2( P1 ) + bayerDither2x2( P2 );\n          }\n  \n          void main() {\n            float alpha = \n              uHasAlphaMap ? \n                uShadowOpacity * texture2D(uAlphaMap, custom_vUv).x\n              : uShadowOpacity;\n\n            if( ( bayerDither4x4( floor( mod( gl_FragCoord.xy, 4.0 ) ) ) ) / 16.0 >= alpha ) discard;\n            \n          `);\n      shader.uniforms['uShadowOpacity'] = uShadowOpacity.current;\n      shader.uniforms['uAlphaMap'] = uAlphaMap.current;\n      shader.uniforms['uHasAlphaMap'] = uHasAlphaMap.current;\n    };\n  }, []);\n  useFrame(() => {\n    var _r3f;\n    const parent = (_r3f = depthMaterialRef.current.__r3f) == null || (_r3f = _r3f.parent) == null ? void 0 : _r3f.object;\n    if (parent) {\n      const parentMainMaterial = parent.material;\n      if (parentMainMaterial) {\n        uShadowOpacity.current.value = opacity !== null && opacity !== void 0 ? opacity : parentMainMaterial.opacity;\n        if (alphaMap === false) {\n          uAlphaMap.current.value = null;\n          uHasAlphaMap.current.value = false;\n        } else {\n          uAlphaMap.current.value = alphaMap || parentMainMaterial.alphaMap;\n          uHasAlphaMap.current.value = !!uAlphaMap.current.value;\n        }\n      }\n    }\n  });\n  return /*#__PURE__*/React.createElement(React.Fragment, null, /*#__PURE__*/React.createElement(\"meshDepthMaterial\", {\n    ref: depthMaterialRef,\n    attach: \"customDepthMaterial\",\n    depthPacking: THREE.RGBADepthPacking\n  }), /*#__PURE__*/React.createElement(\"meshDistanceMaterial\", {\n    ref: distanceMaterialRef,\n    attach: \"customDistanceMaterial\"\n  }));\n}\nexport { ShadowAlpha };", "map": {"version": 3, "names": ["useFrame", "React", "THREE", "ShadowAlpha", "opacity", "alphaMap", "depthMaterialRef", "useRef", "distanceMaterialRef", "uShadowOpacity", "value", "uAlphaMap", "uHasAlphaMap", "useLayoutEffect", "current", "onBeforeCompile", "shader", "mainLineStart", "fragmentShader", "indexOf", "mainLine", "ch", "i", "char<PERSON>t", "trim", "vertexShader", "replace", "uniforms", "_r3f", "parent", "__r3f", "object", "parentMainMaterial", "material", "createElement", "Fragment", "ref", "attach", "depthPacking", "RGBADepthPacking"], "sources": ["C:/Users/<USER>/Documents/Apps_Creations/SiahMan_Racing4Speed_WebGame/siah-man-racing/node_modules/@react-three/drei/core/ShadowAlpha.js"], "sourcesContent": ["import { useFrame } from '@react-three/fiber';\nimport * as React from 'react';\nimport * as THREE from 'three';\n\n/**\n * Integration and compilation: <PERSON><PERSON> (https://twitter.com/CantBeFaraz)\n *\n * Based on:\n *  - https://gkjohnson.github.io/threejs-sandbox/screendoor-transparency/ by <PERSON> (https://github.com/gk<PERSON><PERSON><PERSON>)\n *\n * Note:\n *  - Must depreciate in favor of https://github.com/mrdoob/three.js/issues/10600 when it's ready.\n */\nfunction ShadowAlpha({\n  opacity,\n  alphaMap\n}) {\n  const depthMaterialRef = React.useRef(null);\n  const distanceMaterialRef = React.useRef(null);\n  const uShadowOpacity = React.useRef({\n    value: 1\n  });\n  const uAlphaMap = React.useRef({\n    value: null\n  });\n  const uHasAlphaMap = React.useRef({\n    value: false\n  });\n  React.useLayoutEffect(() => {\n    depthMaterialRef.current.onBeforeCompile = distanceMaterialRef.current.onBeforeCompile = shader => {\n      // Need to get the \"void main\" line dynamically because the lines for\n      // MeshDistanceMaterial and MeshDepthMaterial are different 🤦‍♂️\n      const mainLineStart = shader.fragmentShader.indexOf('void main');\n      let mainLine = '';\n      let ch;\n      let i = mainLineStart;\n      while (ch !== '\\n' && i < mainLineStart + 100) {\n        ch = shader.fragmentShader.charAt(i);\n        mainLine += ch;\n        i++;\n      }\n      mainLine = mainLine.trim();\n      shader.vertexShader = shader.vertexShader.replace('void main() {', `\n        varying vec2 custom_vUv;\n\n        void main() {\n          custom_vUv = uv;\n          \n        `);\n      shader.fragmentShader = shader.fragmentShader.replace(mainLine, `\n          uniform float uShadowOpacity;\n          uniform sampler2D uAlphaMap;\n          uniform bool uHasAlphaMap;\n\n          varying vec2 custom_vUv;\n  \n          float bayerDither2x2( vec2 v ) {\n            return mod( 3.0 * v.y + 2.0 * v.x, 4.0 );\n          }\n    \n          float bayerDither4x4( vec2 v ) {\n            vec2 P1 = mod( v, 2.0 );\n            vec2 P2 = mod( floor( 0.5  * v ), 2.0 );\n            return 4.0 * bayerDither2x2( P1 ) + bayerDither2x2( P2 );\n          }\n  \n          void main() {\n            float alpha = \n              uHasAlphaMap ? \n                uShadowOpacity * texture2D(uAlphaMap, custom_vUv).x\n              : uShadowOpacity;\n\n            if( ( bayerDither4x4( floor( mod( gl_FragCoord.xy, 4.0 ) ) ) ) / 16.0 >= alpha ) discard;\n            \n          `);\n      shader.uniforms['uShadowOpacity'] = uShadowOpacity.current;\n      shader.uniforms['uAlphaMap'] = uAlphaMap.current;\n      shader.uniforms['uHasAlphaMap'] = uHasAlphaMap.current;\n    };\n  }, []);\n  useFrame(() => {\n    var _r3f;\n    const parent = (_r3f = depthMaterialRef.current.__r3f) == null || (_r3f = _r3f.parent) == null ? void 0 : _r3f.object;\n    if (parent) {\n      const parentMainMaterial = parent.material;\n      if (parentMainMaterial) {\n        uShadowOpacity.current.value = opacity !== null && opacity !== void 0 ? opacity : parentMainMaterial.opacity;\n        if (alphaMap === false) {\n          uAlphaMap.current.value = null;\n          uHasAlphaMap.current.value = false;\n        } else {\n          uAlphaMap.current.value = alphaMap || parentMainMaterial.alphaMap;\n          uHasAlphaMap.current.value = !!uAlphaMap.current.value;\n        }\n      }\n    }\n  });\n  return /*#__PURE__*/React.createElement(React.Fragment, null, /*#__PURE__*/React.createElement(\"meshDepthMaterial\", {\n    ref: depthMaterialRef,\n    attach: \"customDepthMaterial\",\n    depthPacking: THREE.RGBADepthPacking\n  }), /*#__PURE__*/React.createElement(\"meshDistanceMaterial\", {\n    ref: distanceMaterialRef,\n    attach: \"customDistanceMaterial\"\n  }));\n}\n\nexport { ShadowAlpha };\n"], "mappings": "AAAA,SAASA,QAAQ,QAAQ,oBAAoB;AAC7C,OAAO,KAAKC,KAAK,MAAM,OAAO;AAC9B,OAAO,KAAKC,KAAK,MAAM,OAAO;;AAE9B;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASC,WAAWA,CAAC;EACnBC,OAAO;EACPC;AACF,CAAC,EAAE;EACD,MAAMC,gBAAgB,GAAGL,KAAK,CAACM,MAAM,CAAC,IAAI,CAAC;EAC3C,MAAMC,mBAAmB,GAAGP,KAAK,CAACM,MAAM,CAAC,IAAI,CAAC;EAC9C,MAAME,cAAc,GAAGR,KAAK,CAACM,MAAM,CAAC;IAClCG,KAAK,EAAE;EACT,CAAC,CAAC;EACF,MAAMC,SAAS,GAAGV,KAAK,CAACM,MAAM,CAAC;IAC7BG,KAAK,EAAE;EACT,CAAC,CAAC;EACF,MAAME,YAAY,GAAGX,KAAK,CAACM,MAAM,CAAC;IAChCG,KAAK,EAAE;EACT,CAAC,CAAC;EACFT,KAAK,CAACY,eAAe,CAAC,MAAM;IAC1BP,gBAAgB,CAACQ,OAAO,CAACC,eAAe,GAAGP,mBAAmB,CAACM,OAAO,CAACC,eAAe,GAAGC,MAAM,IAAI;MACjG;MACA;MACA,MAAMC,aAAa,GAAGD,MAAM,CAACE,cAAc,CAACC,OAAO,CAAC,WAAW,CAAC;MAChE,IAAIC,QAAQ,GAAG,EAAE;MACjB,IAAIC,EAAE;MACN,IAAIC,CAAC,GAAGL,aAAa;MACrB,OAAOI,EAAE,KAAK,IAAI,IAAIC,CAAC,GAAGL,aAAa,GAAG,GAAG,EAAE;QAC7CI,EAAE,GAAGL,MAAM,CAACE,cAAc,CAACK,MAAM,CAACD,CAAC,CAAC;QACpCF,QAAQ,IAAIC,EAAE;QACdC,CAAC,EAAE;MACL;MACAF,QAAQ,GAAGA,QAAQ,CAACI,IAAI,CAAC,CAAC;MAC1BR,MAAM,CAACS,YAAY,GAAGT,MAAM,CAACS,YAAY,CAACC,OAAO,CAAC,eAAe,EAAE;AACzE;AACA;AACA;AACA;AACA;AACA,SAAS,CAAC;MACJV,MAAM,CAACE,cAAc,GAAGF,MAAM,CAACE,cAAc,CAACQ,OAAO,CAACN,QAAQ,EAAE;AACtE;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,WAAW,CAAC;MACNJ,MAAM,CAACW,QAAQ,CAAC,gBAAgB,CAAC,GAAGlB,cAAc,CAACK,OAAO;MAC1DE,MAAM,CAACW,QAAQ,CAAC,WAAW,CAAC,GAAGhB,SAAS,CAACG,OAAO;MAChDE,MAAM,CAACW,QAAQ,CAAC,cAAc,CAAC,GAAGf,YAAY,CAACE,OAAO;IACxD,CAAC;EACH,CAAC,EAAE,EAAE,CAAC;EACNd,QAAQ,CAAC,MAAM;IACb,IAAI4B,IAAI;IACR,MAAMC,MAAM,GAAG,CAACD,IAAI,GAAGtB,gBAAgB,CAACQ,OAAO,CAACgB,KAAK,KAAK,IAAI,IAAI,CAACF,IAAI,GAAGA,IAAI,CAACC,MAAM,KAAK,IAAI,GAAG,KAAK,CAAC,GAAGD,IAAI,CAACG,MAAM;IACrH,IAAIF,MAAM,EAAE;MACV,MAAMG,kBAAkB,GAAGH,MAAM,CAACI,QAAQ;MAC1C,IAAID,kBAAkB,EAAE;QACtBvB,cAAc,CAACK,OAAO,CAACJ,KAAK,GAAGN,OAAO,KAAK,IAAI,IAAIA,OAAO,KAAK,KAAK,CAAC,GAAGA,OAAO,GAAG4B,kBAAkB,CAAC5B,OAAO;QAC5G,IAAIC,QAAQ,KAAK,KAAK,EAAE;UACtBM,SAAS,CAACG,OAAO,CAACJ,KAAK,GAAG,IAAI;UAC9BE,YAAY,CAACE,OAAO,CAACJ,KAAK,GAAG,KAAK;QACpC,CAAC,MAAM;UACLC,SAAS,CAACG,OAAO,CAACJ,KAAK,GAAGL,QAAQ,IAAI2B,kBAAkB,CAAC3B,QAAQ;UACjEO,YAAY,CAACE,OAAO,CAACJ,KAAK,GAAG,CAAC,CAACC,SAAS,CAACG,OAAO,CAACJ,KAAK;QACxD;MACF;IACF;EACF,CAAC,CAAC;EACF,OAAO,aAAaT,KAAK,CAACiC,aAAa,CAACjC,KAAK,CAACkC,QAAQ,EAAE,IAAI,EAAE,aAAalC,KAAK,CAACiC,aAAa,CAAC,mBAAmB,EAAE;IAClHE,GAAG,EAAE9B,gBAAgB;IACrB+B,MAAM,EAAE,qBAAqB;IAC7BC,YAAY,EAAEpC,KAAK,CAACqC;EACtB,CAAC,CAAC,EAAE,aAAatC,KAAK,CAACiC,aAAa,CAAC,sBAAsB,EAAE;IAC3DE,GAAG,EAAE5B,mBAAmB;IACxB6B,MAAM,EAAE;EACV,CAAC,CAAC,CAAC;AACL;AAEA,SAASlC,WAAW", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}