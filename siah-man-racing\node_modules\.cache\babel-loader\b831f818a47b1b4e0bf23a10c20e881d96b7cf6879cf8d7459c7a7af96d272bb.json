{"ast": null, "code": "var __defProp = Object.defineProperty;\nvar __defNormalProp = (obj, key, value) => key in obj ? __defProp(obj, key, {\n  enumerable: true,\n  configurable: true,\n  writable: true,\n  value\n}) : obj[key] = value;\nvar __publicField = (obj, key, value) => {\n  __defNormalProp(obj, typeof key !== \"symbol\" ? key + \"\" : key, value);\n  return value;\n};\nimport { BufferGeometry, Mesh, Points } from \"three\";\nconst DRACOExporter = /* @__PURE__ */(() => {\n  const _DRACOExporter = class {\n    parse(object, options = {\n      decodeSpeed: 5,\n      encodeSpeed: 5,\n      encoderMethod: _DRACOExporter.MESH_EDGEBREAKER_ENCODING,\n      quantization: [16, 8, 8, 8, 8],\n      exportUvs: true,\n      exportNormals: true,\n      exportColor: false\n    }) {\n      if (object instanceof BufferGeometry && object.isBufferGeometry) {\n        throw new Error(\"DRACOExporter: The first parameter of parse() is now an instance of Mesh or Points.\");\n      }\n      if (DracoEncoderModule === void 0) {\n        throw new Error(\"THREE.DRACOExporter: required the draco_encoder to work.\");\n      }\n      const geometry = object.geometry;\n      const dracoEncoder = DracoEncoderModule();\n      const encoder = new dracoEncoder.Encoder();\n      let builder;\n      let dracoObject;\n      if (!geometry.isBufferGeometry) {\n        throw new Error(\"THREE.DRACOExporter.parse(geometry, options): geometry is not a THREE.BufferGeometry instance.\");\n      }\n      if (object instanceof Mesh && object.isMesh) {\n        builder = new dracoEncoder.MeshBuilder();\n        dracoObject = new dracoEncoder.Mesh();\n        const vertices = geometry.getAttribute(\"position\");\n        builder.AddFloatAttributeToMesh(dracoObject, dracoEncoder.POSITION, vertices.count, vertices.itemSize, vertices.array);\n        const faces = geometry.getIndex();\n        if (faces !== null) {\n          builder.AddFacesToMesh(dracoObject, faces.count / 3, faces.array);\n        } else {\n          const faces2 = new (vertices.count > 65535 ? Uint32Array : Uint16Array)(vertices.count);\n          for (let i = 0; i < faces2.length; i++) {\n            faces2[i] = i;\n          }\n          builder.AddFacesToMesh(dracoObject, vertices.count, faces2);\n        }\n        if (options.exportNormals) {\n          const normals = geometry.getAttribute(\"normal\");\n          if (normals !== void 0) {\n            builder.AddFloatAttributeToMesh(dracoObject, dracoEncoder.NORMAL, normals.count, normals.itemSize, normals.array);\n          }\n        }\n        if (options.exportUvs) {\n          const uvs = geometry.getAttribute(\"uv\");\n          if (uvs !== void 0) {\n            builder.AddFloatAttributeToMesh(dracoObject, dracoEncoder.TEX_COORD, uvs.count, uvs.itemSize, uvs.array);\n          }\n        }\n        if (options.exportColor) {\n          const colors = geometry.getAttribute(\"color\");\n          if (colors !== void 0) {\n            builder.AddFloatAttributeToMesh(dracoObject, dracoEncoder.COLOR, colors.count, colors.itemSize, colors.array);\n          }\n        }\n      } else if (object instanceof Points && object.isPoints) {\n        builder = new dracoEncoder.PointCloudBuilder();\n        dracoObject = new dracoEncoder.PointCloud();\n        const vertices = geometry.getAttribute(\"position\");\n        builder.AddFloatAttribute(dracoObject, dracoEncoder.POSITION, vertices.count, vertices.itemSize, vertices.array);\n        if (options.exportColor) {\n          const colors = geometry.getAttribute(\"color\");\n          if (colors !== void 0) {\n            builder.AddFloatAttribute(dracoObject, dracoEncoder.COLOR, colors.count, colors.itemSize, colors.array);\n          }\n        }\n      } else {\n        throw new Error(\"DRACOExporter: Unsupported object type.\");\n      }\n      const encodedData = new dracoEncoder.DracoInt8Array();\n      const encodeSpeed = options.encodeSpeed !== void 0 ? options.encodeSpeed : 5;\n      const decodeSpeed = options.decodeSpeed !== void 0 ? options.decodeSpeed : 5;\n      encoder.SetSpeedOptions(encodeSpeed, decodeSpeed);\n      if (options.encoderMethod !== void 0) {\n        encoder.SetEncodingMethod(options.encoderMethod);\n      }\n      if (options.quantization !== void 0) {\n        for (let i = 0; i < 5; i++) {\n          if (options.quantization[i] !== void 0) {\n            encoder.SetAttributeQuantization(i, options.quantization[i]);\n          }\n        }\n      }\n      let length;\n      if (object instanceof Mesh && object.isMesh) {\n        length = encoder.EncodeMeshToDracoBuffer(dracoObject, encodedData);\n      } else {\n        length = encoder.EncodePointCloudToDracoBuffer(dracoObject, true, encodedData);\n      }\n      dracoEncoder.destroy(dracoObject);\n      if (length === 0) {\n        throw new Error(\"THREE.DRACOExporter: Draco encoding failed.\");\n      }\n      const outputData = new Int8Array(new ArrayBuffer(length));\n      for (let i = 0; i < length; i++) {\n        outputData[i] = encodedData.GetValue(i);\n      }\n      dracoEncoder.destroy(encodedData);\n      dracoEncoder.destroy(encoder);\n      dracoEncoder.destroy(builder);\n      return outputData;\n    }\n  };\n  let DRACOExporter2 = _DRACOExporter;\n  // Encoder methods\n  __publicField(DRACOExporter2, \"MESH_EDGEBREAKER_ENCODING\", 1);\n  __publicField(DRACOExporter2, \"MESH_SEQUENTIAL_ENCODING\", 0);\n  // Geometry type\n  __publicField(DRACOExporter2, \"POINT_CLOUD\", 0);\n  __publicField(DRACOExporter2, \"TRIANGULAR_MESH\", 1);\n  // Attribute type\n  __publicField(DRACOExporter2, \"INVALID\", -1);\n  __publicField(DRACOExporter2, \"POSITION\", 0);\n  __publicField(DRACOExporter2, \"NORMAL\", 1);\n  __publicField(DRACOExporter2, \"COLOR\", 2);\n  __publicField(DRACOExporter2, \"TEX_COORD\", 3);\n  __publicField(DRACOExporter2, \"GENERIC\", 4);\n  return DRACOExporter2;\n})();\nexport { DRACOExporter };", "map": {"version": 3, "names": ["DRACOExporter", "_DRACOExporter", "parse", "object", "options", "decodeSpeed", "encodeSpeed", "encoder<PERSON><PERSON><PERSON>", "MESH_EDGEBREAKER_ENCODING", "quantization", "exportUvs", "exportNormals", "exportColor", "BufferGeometry", "isBufferGeometry", "Error", "DracoEncoderModule", "geometry", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "encoder", "Encoder", "builder", "dracoObject", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "MeshBuilder", "vertices", "getAttribute", "AddFloatAttributeToMesh", "POSITION", "count", "itemSize", "array", "faces", "getIndex", "AddFacesToMesh", "faces2", "Uint32Array", "Uint16Array", "i", "length", "normals", "NORMAL", "uvs", "TEX_COORD", "colors", "COLOR", "Points", "isPoints", "PointCloudBuilder", "PointCloud", "AddFloatAttribute", "encodedData", "DracoInt8Array", "SetSpeedOptions", "SetEncodingMethod", "SetAttributeQuantization", "EncodeMeshToDracoBuffer", "EncodePointCloudToDracoBuffer", "destroy", "outputData", "Int8Array", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "GetValue", "DRACOExporter2", "__publicField"], "sources": ["C:\\Users\\<USER>\\Documents\\Apps_Creations\\SiahMan_Racing4Speed_WebGame\\siah-man-racing\\node_modules\\src\\exporters\\DRACOExporter.js"], "sourcesContent": ["import { BufferGeo<PERSON>, Mesh, Points } from 'three'\n\n/**\n * Export draco compressed files from threejs geometry objects.\n *\n * Draco files are compressed and usually are smaller than conventional 3D file formats.\n *\n * The exporter receives a options object containing\n *  - decodeSpeed, indicates how to tune the encoder regarding decode speed (0 gives better speed but worst quality)\n *  - encodeSpeed, indicates how to tune the encoder parameters (0 gives better speed but worst quality)\n *  - encoderMethod\n *  - quantization, indicates the presision of each type of data stored in the draco file in the order (POSITION, NORMAL, COLOR, TEX_COORD, GENERIC)\n *  - exportUvs\n *  - exportNormals\n */\n\nconst DRACOExporter = /* @__PURE__ */ (() => {\n  class DRACOExporter {\n    // Encoder methods\n\n    static MESH_EDGEBREAKER_ENCODING = 1\n    static MESH_SEQUENTIAL_ENCODING = 0\n\n    // Geometry type\n\n    static POINT_CLOUD = 0\n    static TRIANGULAR_MESH = 1\n\n    // Attribute type\n    static INVALID = -1\n    static POSITION = 0\n    static NORMAL = 1\n    static COLOR = 2\n    static TEX_COORD = 3\n    static GENERIC = 4\n\n    parse(\n      object,\n      options = {\n        decodeSpeed: 5,\n        encodeSpeed: 5,\n        encoderMethod: DRACOExporter.MESH_EDGEBREAKER_ENCODING,\n        quantization: [16, 8, 8, 8, 8],\n        exportUvs: true,\n        exportNormals: true,\n        exportColor: false,\n      },\n    ) {\n      if (object instanceof BufferGeometry && object.isBufferGeometry) {\n        throw new Error('DRACOExporter: The first parameter of parse() is now an instance of Mesh or Points.')\n      }\n\n      if (DracoEncoderModule === undefined) {\n        throw new Error('THREE.DRACOExporter: required the draco_encoder to work.')\n      }\n\n      const geometry = object.geometry\n\n      const dracoEncoder = DracoEncoderModule()\n      const encoder = new dracoEncoder.Encoder()\n      let builder\n      let dracoObject\n\n      if (!geometry.isBufferGeometry) {\n        throw new Error(\n          'THREE.DRACOExporter.parse(geometry, options): geometry is not a THREE.BufferGeometry instance.',\n        )\n      }\n\n      if (object instanceof Mesh && object.isMesh) {\n        builder = new dracoEncoder.MeshBuilder()\n        dracoObject = new dracoEncoder.Mesh()\n\n        const vertices = geometry.getAttribute('position')\n        // @ts-ignore\n        builder.AddFloatAttributeToMesh(\n          dracoObject,\n          dracoEncoder.POSITION,\n          vertices.count,\n          vertices.itemSize,\n          vertices.array,\n        )\n\n        const faces = geometry.getIndex()\n\n        if (faces !== null) {\n          builder.AddFacesToMesh(dracoObject, faces.count / 3, faces.array)\n        } else {\n          const faces = new (vertices.count > 65535 ? Uint32Array : Uint16Array)(vertices.count)\n\n          for (let i = 0; i < faces.length; i++) {\n            faces[i] = i\n          }\n\n          builder.AddFacesToMesh(dracoObject, vertices.count, faces)\n        }\n\n        if (options.exportNormals) {\n          const normals = geometry.getAttribute('normal')\n\n          if (normals !== undefined) {\n            // @ts-ignore\n            builder.AddFloatAttributeToMesh(\n              dracoObject,\n              dracoEncoder.NORMAL,\n              normals.count,\n              normals.itemSize,\n              normals.array,\n            )\n          }\n        }\n\n        if (options.exportUvs) {\n          const uvs = geometry.getAttribute('uv')\n\n          if (uvs !== undefined) {\n            // @ts-ignore\n            builder.AddFloatAttributeToMesh(dracoObject, dracoEncoder.TEX_COORD, uvs.count, uvs.itemSize, uvs.array)\n          }\n        }\n\n        if (options.exportColor) {\n          const colors = geometry.getAttribute('color')\n\n          if (colors !== undefined) {\n            // @ts-ignore\n            builder.AddFloatAttributeToMesh(\n              dracoObject,\n              dracoEncoder.COLOR,\n              colors.count,\n              colors.itemSize,\n              colors.array,\n            )\n          }\n        }\n      } else if (object instanceof Points && object.isPoints) {\n        // @ts-ignore\n        builder = new dracoEncoder.PointCloudBuilder()\n        // @ts-ignore\n        dracoObject = new dracoEncoder.PointCloud()\n\n        const vertices = geometry.getAttribute('position')\n        builder.AddFloatAttribute(dracoObject, dracoEncoder.POSITION, vertices.count, vertices.itemSize, vertices.array)\n\n        if (options.exportColor) {\n          const colors = geometry.getAttribute('color')\n\n          if (colors !== undefined) {\n            builder.AddFloatAttribute(dracoObject, dracoEncoder.COLOR, colors.count, colors.itemSize, colors.array)\n          }\n        }\n      } else {\n        throw new Error('DRACOExporter: Unsupported object type.')\n      }\n\n      //Compress using draco encoder\n\n      const encodedData = new dracoEncoder.DracoInt8Array()\n\n      //Sets the desired encoding and decoding speed for the given options from 0 (slowest speed, but the best compression) to 10 (fastest, but the worst compression).\n\n      const encodeSpeed = options.encodeSpeed !== undefined ? options.encodeSpeed : 5\n      const decodeSpeed = options.decodeSpeed !== undefined ? options.decodeSpeed : 5\n\n      encoder.SetSpeedOptions(encodeSpeed, decodeSpeed)\n\n      // Sets the desired encoding method for a given geometry.\n\n      if (options.encoderMethod !== undefined) {\n        encoder.SetEncodingMethod(options.encoderMethod)\n      }\n\n      // Sets the quantization (number of bits used to represent) compression options for a named attribute.\n      // The attribute values will be quantized in a box defined by the maximum extent of the attribute values.\n      if (options.quantization !== undefined) {\n        for (let i = 0; i < 5; i++) {\n          if (options.quantization[i] !== undefined) {\n            encoder.SetAttributeQuantization(i, options.quantization[i])\n          }\n        }\n      }\n\n      let length\n\n      if (object instanceof Mesh && object.isMesh) {\n        length = encoder.EncodeMeshToDracoBuffer(dracoObject, encodedData)\n      } else {\n        // @ts-ignore\n        length = encoder.EncodePointCloudToDracoBuffer(dracoObject, true, encodedData)\n      }\n\n      dracoEncoder.destroy(dracoObject)\n\n      if (length === 0) {\n        throw new Error('THREE.DRACOExporter: Draco encoding failed.')\n      }\n\n      //Copy encoded data to buffer.\n      const outputData = new Int8Array(new ArrayBuffer(length))\n\n      for (let i = 0; i < length; i++) {\n        outputData[i] = encodedData.GetValue(i)\n      }\n\n      dracoEncoder.destroy(encodedData)\n      dracoEncoder.destroy(encoder)\n      dracoEncoder.destroy(builder)\n\n      return outputData\n    }\n  }\n\n  return DRACOExporter\n})()\n\nexport { DRACOExporter }\n"], "mappings": ";;;;;;;;;;;;AAgBK,MAACA,aAAA,GAAiC,sBAAM;EAC3C,MAAMC,cAAA,GAAN,MAAoB;IAmBlBC,MACEC,MAAA,EACAC,OAAA,GAAU;MACRC,WAAA,EAAa;MACbC,WAAA,EAAa;MACbC,aAAA,EAAeN,cAAA,CAAcO,yBAAA;MAC7BC,YAAA,EAAc,CAAC,IAAI,GAAG,GAAG,GAAG,CAAC;MAC7BC,SAAA,EAAW;MACXC,aAAA,EAAe;MACfC,WAAA,EAAa;IACd,GACD;MACA,IAAIT,MAAA,YAAkBU,cAAA,IAAkBV,MAAA,CAAOW,gBAAA,EAAkB;QAC/D,MAAM,IAAIC,KAAA,CAAM,qFAAqF;MACtG;MAED,IAAIC,kBAAA,KAAuB,QAAW;QACpC,MAAM,IAAID,KAAA,CAAM,0DAA0D;MAC3E;MAED,MAAME,QAAA,GAAWd,MAAA,CAAOc,QAAA;MAExB,MAAMC,YAAA,GAAeF,kBAAA,CAAoB;MACzC,MAAMG,OAAA,GAAU,IAAID,YAAA,CAAaE,OAAA,CAAS;MAC1C,IAAIC,OAAA;MACJ,IAAIC,WAAA;MAEJ,IAAI,CAACL,QAAA,CAASH,gBAAA,EAAkB;QAC9B,MAAM,IAAIC,KAAA,CACR,gGACD;MACF;MAED,IAAIZ,MAAA,YAAkBoB,IAAA,IAAQpB,MAAA,CAAOqB,MAAA,EAAQ;QAC3CH,OAAA,GAAU,IAAIH,YAAA,CAAaO,WAAA,CAAa;QACxCH,WAAA,GAAc,IAAIJ,YAAA,CAAaK,IAAA,CAAM;QAErC,MAAMG,QAAA,GAAWT,QAAA,CAASU,YAAA,CAAa,UAAU;QAEjDN,OAAA,CAAQO,uBAAA,CACNN,WAAA,EACAJ,YAAA,CAAaW,QAAA,EACbH,QAAA,CAASI,KAAA,EACTJ,QAAA,CAASK,QAAA,EACTL,QAAA,CAASM,KACV;QAED,MAAMC,KAAA,GAAQhB,QAAA,CAASiB,QAAA,CAAU;QAEjC,IAAID,KAAA,KAAU,MAAM;UAClBZ,OAAA,CAAQc,cAAA,CAAeb,WAAA,EAAaW,KAAA,CAAMH,KAAA,GAAQ,GAAGG,KAAA,CAAMD,KAAK;QAC1E,OAAe;UACL,MAAMI,MAAA,GAAQ,KAAKV,QAAA,CAASI,KAAA,GAAQ,QAAQO,WAAA,GAAcC,WAAA,EAAaZ,QAAA,CAASI,KAAK;UAErF,SAASS,CAAA,GAAI,GAAGA,CAAA,GAAIH,MAAA,CAAMI,MAAA,EAAQD,CAAA,IAAK;YACrCH,MAAA,CAAMG,CAAC,IAAIA,CAAA;UACZ;UAEDlB,OAAA,CAAQc,cAAA,CAAeb,WAAA,EAAaI,QAAA,CAASI,KAAA,EAAOM,MAAK;QAC1D;QAED,IAAIhC,OAAA,CAAQO,aAAA,EAAe;UACzB,MAAM8B,OAAA,GAAUxB,QAAA,CAASU,YAAA,CAAa,QAAQ;UAE9C,IAAIc,OAAA,KAAY,QAAW;YAEzBpB,OAAA,CAAQO,uBAAA,CACNN,WAAA,EACAJ,YAAA,CAAawB,MAAA,EACbD,OAAA,CAAQX,KAAA,EACRW,OAAA,CAAQV,QAAA,EACRU,OAAA,CAAQT,KACT;UACF;QACF;QAED,IAAI5B,OAAA,CAAQM,SAAA,EAAW;UACrB,MAAMiC,GAAA,GAAM1B,QAAA,CAASU,YAAA,CAAa,IAAI;UAEtC,IAAIgB,GAAA,KAAQ,QAAW;YAErBtB,OAAA,CAAQO,uBAAA,CAAwBN,WAAA,EAAaJ,YAAA,CAAa0B,SAAA,EAAWD,GAAA,CAAIb,KAAA,EAAOa,GAAA,CAAIZ,QAAA,EAAUY,GAAA,CAAIX,KAAK;UACxG;QACF;QAED,IAAI5B,OAAA,CAAQQ,WAAA,EAAa;UACvB,MAAMiC,MAAA,GAAS5B,QAAA,CAASU,YAAA,CAAa,OAAO;UAE5C,IAAIkB,MAAA,KAAW,QAAW;YAExBxB,OAAA,CAAQO,uBAAA,CACNN,WAAA,EACAJ,YAAA,CAAa4B,KAAA,EACbD,MAAA,CAAOf,KAAA,EACPe,MAAA,CAAOd,QAAA,EACPc,MAAA,CAAOb,KACR;UACF;QACF;MACF,WAAU7B,MAAA,YAAkB4C,MAAA,IAAU5C,MAAA,CAAO6C,QAAA,EAAU;QAEtD3B,OAAA,GAAU,IAAIH,YAAA,CAAa+B,iBAAA,CAAmB;QAE9C3B,WAAA,GAAc,IAAIJ,YAAA,CAAagC,UAAA,CAAY;QAE3C,MAAMxB,QAAA,GAAWT,QAAA,CAASU,YAAA,CAAa,UAAU;QACjDN,OAAA,CAAQ8B,iBAAA,CAAkB7B,WAAA,EAAaJ,YAAA,CAAaW,QAAA,EAAUH,QAAA,CAASI,KAAA,EAAOJ,QAAA,CAASK,QAAA,EAAUL,QAAA,CAASM,KAAK;QAE/G,IAAI5B,OAAA,CAAQQ,WAAA,EAAa;UACvB,MAAMiC,MAAA,GAAS5B,QAAA,CAASU,YAAA,CAAa,OAAO;UAE5C,IAAIkB,MAAA,KAAW,QAAW;YACxBxB,OAAA,CAAQ8B,iBAAA,CAAkB7B,WAAA,EAAaJ,YAAA,CAAa4B,KAAA,EAAOD,MAAA,CAAOf,KAAA,EAAOe,MAAA,CAAOd,QAAA,EAAUc,MAAA,CAAOb,KAAK;UACvG;QACF;MACT,OAAa;QACL,MAAM,IAAIjB,KAAA,CAAM,yCAAyC;MAC1D;MAID,MAAMqC,WAAA,GAAc,IAAIlC,YAAA,CAAamC,cAAA,CAAgB;MAIrD,MAAM/C,WAAA,GAAcF,OAAA,CAAQE,WAAA,KAAgB,SAAYF,OAAA,CAAQE,WAAA,GAAc;MAC9E,MAAMD,WAAA,GAAcD,OAAA,CAAQC,WAAA,KAAgB,SAAYD,OAAA,CAAQC,WAAA,GAAc;MAE9Ec,OAAA,CAAQmC,eAAA,CAAgBhD,WAAA,EAAaD,WAAW;MAIhD,IAAID,OAAA,CAAQG,aAAA,KAAkB,QAAW;QACvCY,OAAA,CAAQoC,iBAAA,CAAkBnD,OAAA,CAAQG,aAAa;MAChD;MAID,IAAIH,OAAA,CAAQK,YAAA,KAAiB,QAAW;QACtC,SAAS8B,CAAA,GAAI,GAAGA,CAAA,GAAI,GAAGA,CAAA,IAAK;UAC1B,IAAInC,OAAA,CAAQK,YAAA,CAAa8B,CAAC,MAAM,QAAW;YACzCpB,OAAA,CAAQqC,wBAAA,CAAyBjB,CAAA,EAAGnC,OAAA,CAAQK,YAAA,CAAa8B,CAAC,CAAC;UAC5D;QACF;MACF;MAED,IAAIC,MAAA;MAEJ,IAAIrC,MAAA,YAAkBoB,IAAA,IAAQpB,MAAA,CAAOqB,MAAA,EAAQ;QAC3CgB,MAAA,GAASrB,OAAA,CAAQsC,uBAAA,CAAwBnC,WAAA,EAAa8B,WAAW;MACzE,OAAa;QAELZ,MAAA,GAASrB,OAAA,CAAQuC,6BAAA,CAA8BpC,WAAA,EAAa,MAAM8B,WAAW;MAC9E;MAEDlC,YAAA,CAAayC,OAAA,CAAQrC,WAAW;MAEhC,IAAIkB,MAAA,KAAW,GAAG;QAChB,MAAM,IAAIzB,KAAA,CAAM,6CAA6C;MAC9D;MAGD,MAAM6C,UAAA,GAAa,IAAIC,SAAA,CAAU,IAAIC,WAAA,CAAYtB,MAAM,CAAC;MAExD,SAASD,CAAA,GAAI,GAAGA,CAAA,GAAIC,MAAA,EAAQD,CAAA,IAAK;QAC/BqB,UAAA,CAAWrB,CAAC,IAAIa,WAAA,CAAYW,QAAA,CAASxB,CAAC;MACvC;MAEDrB,YAAA,CAAayC,OAAA,CAAQP,WAAW;MAChClC,YAAA,CAAayC,OAAA,CAAQxC,OAAO;MAC5BD,YAAA,CAAayC,OAAA,CAAQtC,OAAO;MAE5B,OAAOuC,UAAA;IACR;EACF;EAjMD,IAAMI,cAAA,GAAN/D,cAAA;EAGE;EAAAgE,aAAA,CAHID,cAAA,EAGG,6BAA4B;EACnCC,aAAA,CAJID,cAAA,EAIG,4BAA2B;EAIlC;EAAAC,aAAA,CARID,cAAA,EAQG,eAAc;EACrBC,aAAA,CATID,cAAA,EASG,mBAAkB;EAGzB;EAAAC,aAAA,CAZID,cAAA,EAYG,WAAU;EACjBC,aAAA,CAbID,cAAA,EAaG,YAAW;EAClBC,aAAA,CAdID,cAAA,EAcG,UAAS;EAChBC,aAAA,CAfID,cAAA,EAeG,SAAQ;EACfC,aAAA,CAhBID,cAAA,EAgBG,aAAY;EACnBC,aAAA,CAjBID,cAAA,EAiBG,WAAU;EAkLnB,OAAOA,cAAA;AACT,GAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}