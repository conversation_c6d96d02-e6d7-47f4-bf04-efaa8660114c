import React, { useEffect, useRef } from 'react';
import { useSelector, useDispatch } from 'react-redux';
import { RootState } from '../store';
import { updateVehicleState } from '../store/gameSlice';

// Power-up spawning system for arcade fun!
interface PowerUpSpawnerProps {
  trackId: string;
  enabled?: boolean;
}

interface SpawnPoint {
  x: number;
  y: number;
  type: 'power_up' | 'coin' | 'speed_pad' | 'jump_ramp';
  respawnTime: number;
  lastSpawned: number;
  isActive: boolean;
}

const PowerUpSpawner: React.FC<PowerUpSpawnerProps> = ({ trackId, enabled = true }) => {
  const dispatch = useDispatch();
  const { raceState, vehicleState } = useSelector((state: RootState) => state.game);
  const spawnPoints = useRef<SpawnPoint[]>([]);
  const animationFrame = useRef<number | null>(null);

  // Initialize spawn points based on track
  useEffect(() => {
    if (!trackId) return;

    // Define spawn points for different track types
    const getSpawnPointsForTrack = (trackId: string): SpawnPoint[] => {
      const basePoints: SpawnPoint[] = [
        // Power-up spawn points
        { x: 200, y: 300, type: 'power_up', respawnTime: 15000, lastSpawned: 0, isActive: false },
        { x: 600, y: 500, type: 'power_up', respawnTime: 20000, lastSpawned: 0, isActive: false },
        { x: 800, y: 200, type: 'power_up', respawnTime: 18000, lastSpawned: 0, isActive: false },
        
        // Coin spawn points (more frequent)
        { x: 150, y: 150, type: 'coin', respawnTime: 5000, lastSpawned: 0, isActive: true },
        { x: 350, y: 250, type: 'coin', respawnTime: 5000, lastSpawned: 0, isActive: true },
        { x: 550, y: 350, type: 'coin', respawnTime: 5000, lastSpawned: 0, isActive: true },
        { x: 750, y: 450, type: 'coin', respawnTime: 5000, lastSpawned: 0, isActive: true },
        { x: 250, y: 550, type: 'coin', respawnTime: 5000, lastSpawned: 0, isActive: true },
        { x: 450, y: 650, type: 'coin', respawnTime: 5000, lastSpawned: 0, isActive: true },
        { x: 650, y: 750, type: 'coin', respawnTime: 5000, lastSpawned: 0, isActive: true },
        { x: 850, y: 650, type: 'coin', respawnTime: 5000, lastSpawned: 0, isActive: true },
        
        // Speed pads (strategic locations)
        { x: 400, y: 100, type: 'speed_pad', respawnTime: 0, lastSpawned: 0, isActive: true },
        { x: 700, y: 300, type: 'speed_pad', respawnTime: 0, lastSpawned: 0, isActive: true },
        { x: 300, y: 600, type: 'speed_pad', respawnTime: 0, lastSpawned: 0, isActive: true },
        
        // Jump ramps (fun locations)
        { x: 500, y: 400, type: 'jump_ramp', respawnTime: 0, lastSpawned: 0, isActive: true },
        { x: 200, y: 700, type: 'jump_ramp', respawnTime: 0, lastSpawned: 0, isActive: true }
      ];

      // Modify spawn points based on track type
      switch (trackId) {
        case 'mountain_track':
          // Add more jump ramps for mountain track
          basePoints.push(
            { x: 600, y: 150, type: 'jump_ramp', respawnTime: 0, lastSpawned: 0, isActive: true },
            { x: 100, y: 400, type: 'jump_ramp', respawnTime: 0, lastSpawned: 0, isActive: true }
          );
          break;
        case 'city_circuit':
          // Add more speed pads for city track
          basePoints.push(
            { x: 150, y: 200, type: 'speed_pad', respawnTime: 0, lastSpawned: 0, isActive: true },
            { x: 750, y: 600, type: 'speed_pad', respawnTime: 0, lastSpawned: 0, isActive: true }
          );
          break;
        case 'desert_highway':
          // Add more coins for desert track
          for (let i = 0; i < 10; i++) {
            basePoints.push({
              x: Math.random() * 800 + 100,
              y: Math.random() * 600 + 100,
              type: 'coin',
              respawnTime: 3000,
              lastSpawned: 0,
              isActive: true
            });
          }
          break;
      }

      return basePoints;
    };

    spawnPoints.current = getSpawnPointsForTrack(trackId);
  }, [trackId]);

  // Spawn management system
  const updateSpawns = () => {
    if (!enabled || !raceState.isRaceStarted) {
      animationFrame.current = requestAnimationFrame(updateSpawns);
      return;
    }

    const now = Date.now();

    spawnPoints.current.forEach(point => {
      // Check if it's time to respawn
      if (!point.isActive && (now - point.lastSpawned) >= point.respawnTime) {
        point.isActive = true;
        point.lastSpawned = now;
      }

      // Check for collection by player
      if (point.isActive) {
        const dx = vehicleState.x - point.x;
        const dy = vehicleState.y - point.y;
        const distance = Math.sqrt(dx * dx + dy * dy);

        if (distance < 30) { // Collection radius
          handleCollection(point);
          
          if (point.type === 'coin' || point.type === 'power_up') {
            point.isActive = false;
            point.lastSpawned = now;
          }
        }
      }
    });

    animationFrame.current = requestAnimationFrame(updateSpawns);
  };

  // Handle item collection
  const handleCollection = (point: SpawnPoint) => {
    const now = Date.now();
    let newState = { ...vehicleState };

    switch (point.type) {
      case 'coin':
        newState.coinsCollected = (newState.coinsCollected || 0) + 1;
        newState.totalScore = (newState.totalScore || 0) + 10;
        newState.comboMultiplier = Math.min((newState.comboMultiplier || 1) * 1.1, 5);
        newState.lastComboTime = now;
        break;

      case 'power_up':
        // Random power-up selection
        const powerUps = ['invincibility', 'mega_speed', 'coin_magnet', 'double_points'];
        const selectedPowerUp = powerUps[Math.floor(Math.random() * powerUps.length)];
        
        switch (selectedPowerUp) {
          case 'invincibility':
            newState.isInvincible = true;
            newState.invincibilityUntil = now + 5000;
            break;
          case 'mega_speed':
            newState.speedBoostUntil = now + 3000;
            break;
          case 'coin_magnet':
            newState.coinMagnetUntil = now + 8000;
            break;
          case 'double_points':
            newState.doublePointsUntil = now + 10000;
            break;
        }
        
        newState.totalScore = (newState.totalScore || 0) + 100;
        newState.comboMultiplier = Math.min((newState.comboMultiplier || 1) * 1.5, 5);
        newState.lastComboTime = now;
        break;

      case 'speed_pad':
        newState.speedBoostUntil = now + 2000;
        newState.totalScore = (newState.totalScore || 0) + 50;
        newState.comboMultiplier = Math.min((newState.comboMultiplier || 1) * 1.2, 5);
        newState.lastComboTime = now;
        break;

      case 'jump_ramp':
        newState.isJumping = true;
        newState.jumpHeight = 0.8;
        newState.airTime = 0;
        newState.totalScore = (newState.totalScore || 0) + 100;
        newState.comboMultiplier = Math.min((newState.comboMultiplier || 1) * 1.3, 5);
        newState.lastComboTime = now;
        break;
    }

    // Apply double points if active
    if (newState.doublePointsUntil && now < newState.doublePointsUntil) {
      const baseScore = newState.totalScore || 0;
      const originalScore = vehicleState.totalScore || 0;
      const scoreGained = baseScore - originalScore;
      newState.totalScore = originalScore + (scoreGained * 2);
    }

    dispatch(updateVehicleState(newState));
  };

  // Start the spawn management loop
  useEffect(() => {
    if (enabled && raceState.isRaceStarted) {
      animationFrame.current = requestAnimationFrame(updateSpawns);
    }

    return () => {
      if (animationFrame.current) {
        cancelAnimationFrame(animationFrame.current);
      }
    };
  }, [enabled, raceState.isRaceStarted]);

  // Get current spawn points for rendering (can be used by other components)
  const getActiveSpawnPoints = () => {
    return spawnPoints.current.filter(point => point.isActive);
  };

  // Expose spawn points for other components to use
  useEffect(() => {
    // Store spawn points in a way that other components can access them
    // This could be through a context or by dispatching to Redux
    // For now, we'll just make them available through a ref
    (window as any).currentSpawnPoints = getActiveSpawnPoints;
  }, []);

  // This component doesn't render anything visible - it's a logic component
  return null;
};

export default PowerUpSpawner;
