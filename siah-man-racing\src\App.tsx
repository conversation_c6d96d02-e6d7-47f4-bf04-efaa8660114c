import React, { useState } from 'react';
import { Provider } from 'react-redux';
import { BrowserRouter as Router, Routes, Route } from 'react-router-dom';
import { store } from './store';
import './App.css';
import { ThemeProvider } from 'styled-components';
import { GlobalStyle } from './GlobalStyle';
import styled from 'styled-components';
import theme from './theme';

// Pages
import MainMenu from './pages/MainMenu';
import Laboratory from './pages/Laboratory';
import VehicleCustomization from './pages/VehicleCustomization';
import Racing from './pages/IntegratedRacing';
import StoryMode from './pages/StoryMode';
import Settings from './pages/Settings';
import GameModePage from './pages/GameMode';
import ErrorBoundary from './components/ErrorBoundary';

// Game Components
import AssetLoaderComponent from './components/AssetLoader';
import AudioController from './components/AudioController';
import InputController from './components/InputController';

// Game Systems
import { AudioSystem } from './game/AudioSystem';
import { TTSSystem } from './game/TTSSystem';

const AppContainer = styled.div`
  position: relative;
  min-height: 100vh;
`;

function App() {
  const [assetsLoaded, setAssetsLoaded] = useState(false);
  const [audioInitialized, setAudioInitialized] = useState(false);

  // Initialize audio systems
  React.useEffect(() => {
    const initializeAudio = async () => {
      try {
        console.log('Initializing Siah Man Racing audio systems...');

        // Get audio system instance
        const audioSystem = AudioSystem.getInstance();
        const ttsSystem = TTSSystem.getInstance();

        // Connect TTS to AudioSystem for voice synchronization
        ttsSystem.setAudioSystem(audioSystem);

        // Load all game assets and character voices
        await Promise.all([
          audioSystem.loadGameAssets(),
          audioSystem.loadCharacterVoices()
        ]);

        console.log('Audio systems initialized successfully!');
        setAudioInitialized(true);

      } catch (error) {
        console.error('Failed to initialize audio systems:', error);
        // Continue anyway - game can work without audio
        setAudioInitialized(true);
      }
    };

    initializeAudio();
  }, []);

  // Handle asset loading completion
  const handleLoadingComplete = () => {
    setAssetsLoaded(true);
  };
  
  // Show loading screen if assets or audio aren't loaded yet
  if (!assetsLoaded || !audioInitialized) {
    return (
      <Provider store={store}>
        <ThemeProvider theme={theme}>
          <GlobalStyle />
          <AssetLoaderComponent
            onLoadingComplete={handleLoadingComplete}
            audioInitialized={audioInitialized}
          />
        </ThemeProvider>
      </Provider>
    );
  }
  
  // Main app with all routes once assets are loaded
  return (
    <Provider store={store}>
      <ThemeProvider theme={theme}>
        <GlobalStyle />
        <Router>
          <ErrorBoundary>
            <AppContainer>
              {/* Global Audio Controls */}
              <AudioController />
              
              {/* Global Input Controller */}
              <InputController />
              
              <Routes>
                <Route path="/" element={<MainMenu />} />
                <Route path="/main-menu" element={<MainMenu />} />
                <Route path="/laboratory" element={<Laboratory />} />
                <Route path="/customize" element={<VehicleCustomization />} />
                <Route path="/race" element={<Racing />} />
                <Route path="/racing" element={<Racing />} />
                <Route path="/game-mode" element={<GameModePage />} />
                <Route path="/story" element={<StoryMode />} />
                <Route path="/settings" element={<Settings />} />
              </Routes>
            </AppContainer>
          </ErrorBoundary>
        </Router>
      </ThemeProvider>
    </Provider>
  );
}

export default App;
