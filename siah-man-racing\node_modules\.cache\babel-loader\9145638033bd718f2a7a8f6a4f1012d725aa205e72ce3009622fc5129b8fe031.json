{"ast": null, "code": "import { Curve, Vector3, Vector4 } from 'three';\nimport * as NURBSUtils from '../curves/NURBSUtils.js';\n\n/**\n * This class represents a NURBS curve.\n *\n * Implementation is based on `(x, y [, z=0 [, w=1]])` control points with `w=weight`.\n *\n * @augments Curve\n * @three_import import { NURBSCurve } from 'three/addons/curves/NURBSCurve.js';\n */\nclass NURBSCurve extends Curve {\n  /**\n   * Constructs a new NURBS curve.\n   *\n   * @param {number} degree - The NURBS degree.\n   * @param {Array<number>} knots - The knots as a flat array of numbers.\n   * @param {Array<Vector2|Vector3|Vector4>} controlPoints - An array holding control points.\n   * @param {number} [startKnot] - Index of the start knot into the `knots` array.\n   * @param {number} [endKnot] - Index of the end knot into the `knots` array.\n   */\n  constructor(degree, knots, controlPoints, startKnot, endKnot) {\n    super();\n    const knotsLength = knots ? knots.length - 1 : 0;\n    const pointsLength = controlPoints ? controlPoints.length : 0;\n\n    /**\n     * The NURBS degree.\n     *\n     * @type {number}\n     */\n    this.degree = degree;\n\n    /**\n     * The knots as a flat array of numbers.\n     *\n     * @type {Array<number>}\n     */\n    this.knots = knots;\n\n    /**\n     * An array of control points.\n     *\n     * @type {Array<Vector4>}\n     */\n    this.controlPoints = [];\n\n    /**\n     * Index of the start knot into the `knots` array.\n     *\n     * @type {number}\n     */\n    this.startKnot = startKnot || 0;\n\n    /**\n     * Index of the end knot into the `knots` array.\n     *\n     * @type {number}\n     */\n    this.endKnot = endKnot || knotsLength;\n    for (let i = 0; i < pointsLength; ++i) {\n      // ensure Vector4 for control points\n      const point = controlPoints[i];\n      this.controlPoints[i] = new Vector4(point.x, point.y, point.z, point.w);\n    }\n  }\n\n  /**\n   * This method returns a vector in 3D space for the given interpolation factor.\n   *\n   * @param {number} t - A interpolation factor representing a position on the curve. Must be in the range `[0,1]`.\n   * @param {Vector3} [optionalTarget] - The optional target vector the result is written to.\n   * @return {Vector3} The position on the curve.\n   */\n  getPoint(t, optionalTarget = new Vector3()) {\n    const point = optionalTarget;\n    const u = this.knots[this.startKnot] + t * (this.knots[this.endKnot] - this.knots[this.startKnot]); // linear mapping t->u\n\n    // following results in (wx, wy, wz, w) homogeneous point\n    const hpoint = NURBSUtils.calcBSplinePoint(this.degree, this.knots, this.controlPoints, u);\n    if (hpoint.w !== 1.0) {\n      // project to 3D space: (wx, wy, wz, w) -> (x, y, z, 1)\n      hpoint.divideScalar(hpoint.w);\n    }\n    return point.set(hpoint.x, hpoint.y, hpoint.z);\n  }\n\n  /**\n   * Returns a unit vector tangent for the given interpolation factor.\n   *\n   * @param {number} t - The interpolation factor.\n   * @param {Vector3} [optionalTarget] - The optional target vector the result is written to.\n   * @return {Vector3} The tangent vector.\n   */\n  getTangent(t, optionalTarget = new Vector3()) {\n    const tangent = optionalTarget;\n    const u = this.knots[0] + t * (this.knots[this.knots.length - 1] - this.knots[0]);\n    const ders = NURBSUtils.calcNURBSDerivatives(this.degree, this.knots, this.controlPoints, u, 1);\n    tangent.copy(ders[1]).normalize();\n    return tangent;\n  }\n  toJSON() {\n    const data = super.toJSON();\n    data.degree = this.degree;\n    data.knots = [...this.knots];\n    data.controlPoints = this.controlPoints.map(p => p.toArray());\n    data.startKnot = this.startKnot;\n    data.endKnot = this.endKnot;\n    return data;\n  }\n  fromJSON(json) {\n    super.fromJSON(json);\n    this.degree = json.degree;\n    this.knots = [...json.knots];\n    this.controlPoints = json.controlPoints.map(p => new Vector4(p[0], p[1], p[2], p[3]));\n    this.startKnot = json.startKnot;\n    this.endKnot = json.endKnot;\n    return this;\n  }\n}\nexport { NURBSCurve };", "map": {"version": 3, "names": ["Curve", "Vector3", "Vector4", "NURBSUtils", "NURBSCurve", "constructor", "degree", "knots", "controlPoints", "startKnot", "endKnot", "<PERSON><PERSON><PERSON><PERSON>", "length", "pointsLength", "i", "point", "x", "y", "z", "w", "getPoint", "t", "optionalTarget", "u", "hpoint", "calcBSplinePoint", "divideScalar", "set", "getTangent", "tangent", "ders", "calcNURBSDerivatives", "copy", "normalize", "toJSON", "data", "map", "p", "toArray", "fromJSON", "json"], "sources": ["C:/Users/<USER>/Documents/Apps_Creations/SiahMan_Racing4Speed_WebGame/siah-man-racing/node_modules/three/examples/jsm/curves/NURBSCurve.js"], "sourcesContent": ["import {\n\tCurve,\n\tVector3,\n\tVector4\n} from 'three';\nimport * as NURBSUtils from '../curves/NURBSUtils.js';\n\n/**\n * This class represents a NURBS curve.\n *\n * Implementation is based on `(x, y [, z=0 [, w=1]])` control points with `w=weight`.\n *\n * @augments Curve\n * @three_import import { NURBSCurve } from 'three/addons/curves/NURBSCurve.js';\n */\nclass NURBSCurve extends Curve {\n\n\t/**\n\t * Constructs a new NURBS curve.\n\t *\n\t * @param {number} degree - The NURBS degree.\n\t * @param {Array<number>} knots - The knots as a flat array of numbers.\n\t * @param {Array<Vector2|Vector3|Vector4>} controlPoints - An array holding control points.\n\t * @param {number} [startKnot] - Index of the start knot into the `knots` array.\n\t * @param {number} [endKnot] - Index of the end knot into the `knots` array.\n\t */\n\tconstructor( degree, knots, controlPoints, startKnot, endKnot ) {\n\n\t\tsuper();\n\n\t\tconst knotsLength = knots ? knots.length - 1 : 0;\n\t\tconst pointsLength = controlPoints ? controlPoints.length : 0;\n\n\t\t/**\n\t\t * The NURBS degree.\n\t\t *\n\t\t * @type {number}\n\t\t */\n\t\tthis.degree = degree;\n\n\t\t/**\n\t\t * The knots as a flat array of numbers.\n\t\t *\n\t\t * @type {Array<number>}\n\t\t */\n\t\tthis.knots = knots;\n\n\t\t/**\n\t\t * An array of control points.\n\t\t *\n\t\t * @type {Array<Vector4>}\n\t\t */\n\t\tthis.controlPoints = [];\n\n\t\t/**\n\t\t * Index of the start knot into the `knots` array.\n\t\t *\n\t\t * @type {number}\n\t\t */\n\t\tthis.startKnot = startKnot || 0;\n\n\t\t/**\n\t\t * Index of the end knot into the `knots` array.\n\t\t *\n\t\t * @type {number}\n\t\t */\n\t\tthis.endKnot = endKnot || knotsLength;\n\n\t\tfor ( let i = 0; i < pointsLength; ++ i ) {\n\n\t\t\t// ensure Vector4 for control points\n\t\t\tconst point = controlPoints[ i ];\n\t\t\tthis.controlPoints[ i ] = new Vector4( point.x, point.y, point.z, point.w );\n\n\t\t}\n\n\t}\n\n\t/**\n\t * This method returns a vector in 3D space for the given interpolation factor.\n\t *\n\t * @param {number} t - A interpolation factor representing a position on the curve. Must be in the range `[0,1]`.\n\t * @param {Vector3} [optionalTarget] - The optional target vector the result is written to.\n\t * @return {Vector3} The position on the curve.\n\t */\n\tgetPoint( t, optionalTarget = new Vector3() ) {\n\n\t\tconst point = optionalTarget;\n\n\t\tconst u = this.knots[ this.startKnot ] + t * ( this.knots[ this.endKnot ] - this.knots[ this.startKnot ] ); // linear mapping t->u\n\n\t\t// following results in (wx, wy, wz, w) homogeneous point\n\t\tconst hpoint = NURBSUtils.calcBSplinePoint( this.degree, this.knots, this.controlPoints, u );\n\n\t\tif ( hpoint.w !== 1.0 ) {\n\n\t\t\t// project to 3D space: (wx, wy, wz, w) -> (x, y, z, 1)\n\t\t\thpoint.divideScalar( hpoint.w );\n\n\t\t}\n\n\t\treturn point.set( hpoint.x, hpoint.y, hpoint.z );\n\n\t}\n\n\t/**\n\t * Returns a unit vector tangent for the given interpolation factor.\n\t *\n\t * @param {number} t - The interpolation factor.\n\t * @param {Vector3} [optionalTarget] - The optional target vector the result is written to.\n\t * @return {Vector3} The tangent vector.\n\t */\n\tgetTangent( t, optionalTarget = new Vector3() ) {\n\n\t\tconst tangent = optionalTarget;\n\n\t\tconst u = this.knots[ 0 ] + t * ( this.knots[ this.knots.length - 1 ] - this.knots[ 0 ] );\n\t\tconst ders = NURBSUtils.calcNURBSDerivatives( this.degree, this.knots, this.controlPoints, u, 1 );\n\t\ttangent.copy( ders[ 1 ] ).normalize();\n\n\t\treturn tangent;\n\n\t}\n\n\ttoJSON() {\n\n\t\tconst data = super.toJSON();\n\n\t\tdata.degree = this.degree;\n\t\tdata.knots = [ ...this.knots ];\n\t\tdata.controlPoints = this.controlPoints.map( p => p.toArray() );\n\t\tdata.startKnot = this.startKnot;\n\t\tdata.endKnot = this.endKnot;\n\n\t\treturn data;\n\n\t}\n\n\tfromJSON( json ) {\n\n\t\tsuper.fromJSON( json );\n\n\t\tthis.degree = json.degree;\n\t\tthis.knots = [ ...json.knots ];\n\t\tthis.controlPoints = json.controlPoints.map( p => new Vector4( p[ 0 ], p[ 1 ], p[ 2 ], p[ 3 ] ) );\n\t\tthis.startKnot = json.startKnot;\n\t\tthis.endKnot = json.endKnot;\n\n\t\treturn this;\n\n\t}\n\n}\n\nexport { NURBSCurve };\n"], "mappings": "AAAA,SACCA,KAAK,EACLC,OAAO,EACPC,OAAO,QACD,OAAO;AACd,OAAO,KAAKC,UAAU,MAAM,yBAAyB;;AAErD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAMC,UAAU,SAASJ,KAAK,CAAC;EAE9B;AACD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;EACCK,WAAWA,CAAEC,MAAM,EAAEC,KAAK,EAAEC,aAAa,EAAEC,SAAS,EAAEC,OAAO,EAAG;IAE/D,KAAK,CAAC,CAAC;IAEP,MAAMC,WAAW,GAAGJ,KAAK,GAAGA,KAAK,CAACK,MAAM,GAAG,CAAC,GAAG,CAAC;IAChD,MAAMC,YAAY,GAAGL,aAAa,GAAGA,aAAa,CAACI,MAAM,GAAG,CAAC;;IAE7D;AACF;AACA;AACA;AACA;IACE,IAAI,CAACN,MAAM,GAAGA,MAAM;;IAEpB;AACF;AACA;AACA;AACA;IACE,IAAI,CAACC,KAAK,GAAGA,KAAK;;IAElB;AACF;AACA;AACA;AACA;IACE,IAAI,CAACC,aAAa,GAAG,EAAE;;IAEvB;AACF;AACA;AACA;AACA;IACE,IAAI,CAACC,SAAS,GAAGA,SAAS,IAAI,CAAC;;IAE/B;AACF;AACA;AACA;AACA;IACE,IAAI,CAACC,OAAO,GAAGA,OAAO,IAAIC,WAAW;IAErC,KAAM,IAAIG,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGD,YAAY,EAAE,EAAGC,CAAC,EAAG;MAEzC;MACA,MAAMC,KAAK,GAAGP,aAAa,CAAEM,CAAC,CAAE;MAChC,IAAI,CAACN,aAAa,CAAEM,CAAC,CAAE,GAAG,IAAIZ,OAAO,CAAEa,KAAK,CAACC,CAAC,EAAED,KAAK,CAACE,CAAC,EAAEF,KAAK,CAACG,CAAC,EAAEH,KAAK,CAACI,CAAE,CAAC;IAE5E;EAED;;EAEA;AACD;AACA;AACA;AACA;AACA;AACA;EACCC,QAAQA,CAAEC,CAAC,EAAEC,cAAc,GAAG,IAAIrB,OAAO,CAAC,CAAC,EAAG;IAE7C,MAAMc,KAAK,GAAGO,cAAc;IAE5B,MAAMC,CAAC,GAAG,IAAI,CAAChB,KAAK,CAAE,IAAI,CAACE,SAAS,CAAE,GAAGY,CAAC,IAAK,IAAI,CAACd,KAAK,CAAE,IAAI,CAACG,OAAO,CAAE,GAAG,IAAI,CAACH,KAAK,CAAE,IAAI,CAACE,SAAS,CAAE,CAAE,CAAC,CAAC;;IAE5G;IACA,MAAMe,MAAM,GAAGrB,UAAU,CAACsB,gBAAgB,CAAE,IAAI,CAACnB,MAAM,EAAE,IAAI,CAACC,KAAK,EAAE,IAAI,CAACC,aAAa,EAAEe,CAAE,CAAC;IAE5F,IAAKC,MAAM,CAACL,CAAC,KAAK,GAAG,EAAG;MAEvB;MACAK,MAAM,CAACE,YAAY,CAAEF,MAAM,CAACL,CAAE,CAAC;IAEhC;IAEA,OAAOJ,KAAK,CAACY,GAAG,CAAEH,MAAM,CAACR,CAAC,EAAEQ,MAAM,CAACP,CAAC,EAAEO,MAAM,CAACN,CAAE,CAAC;EAEjD;;EAEA;AACD;AACA;AACA;AACA;AACA;AACA;EACCU,UAAUA,CAAEP,CAAC,EAAEC,cAAc,GAAG,IAAIrB,OAAO,CAAC,CAAC,EAAG;IAE/C,MAAM4B,OAAO,GAAGP,cAAc;IAE9B,MAAMC,CAAC,GAAG,IAAI,CAAChB,KAAK,CAAE,CAAC,CAAE,GAAGc,CAAC,IAAK,IAAI,CAACd,KAAK,CAAE,IAAI,CAACA,KAAK,CAACK,MAAM,GAAG,CAAC,CAAE,GAAG,IAAI,CAACL,KAAK,CAAE,CAAC,CAAE,CAAE;IACzF,MAAMuB,IAAI,GAAG3B,UAAU,CAAC4B,oBAAoB,CAAE,IAAI,CAACzB,MAAM,EAAE,IAAI,CAACC,KAAK,EAAE,IAAI,CAACC,aAAa,EAAEe,CAAC,EAAE,CAAE,CAAC;IACjGM,OAAO,CAACG,IAAI,CAAEF,IAAI,CAAE,CAAC,CAAG,CAAC,CAACG,SAAS,CAAC,CAAC;IAErC,OAAOJ,OAAO;EAEf;EAEAK,MAAMA,CAAA,EAAG;IAER,MAAMC,IAAI,GAAG,KAAK,CAACD,MAAM,CAAC,CAAC;IAE3BC,IAAI,CAAC7B,MAAM,GAAG,IAAI,CAACA,MAAM;IACzB6B,IAAI,CAAC5B,KAAK,GAAG,CAAE,GAAG,IAAI,CAACA,KAAK,CAAE;IAC9B4B,IAAI,CAAC3B,aAAa,GAAG,IAAI,CAACA,aAAa,CAAC4B,GAAG,CAAEC,CAAC,IAAIA,CAAC,CAACC,OAAO,CAAC,CAAE,CAAC;IAC/DH,IAAI,CAAC1B,SAAS,GAAG,IAAI,CAACA,SAAS;IAC/B0B,IAAI,CAACzB,OAAO,GAAG,IAAI,CAACA,OAAO;IAE3B,OAAOyB,IAAI;EAEZ;EAEAI,QAAQA,CAAEC,IAAI,EAAG;IAEhB,KAAK,CAACD,QAAQ,CAAEC,IAAK,CAAC;IAEtB,IAAI,CAAClC,MAAM,GAAGkC,IAAI,CAAClC,MAAM;IACzB,IAAI,CAACC,KAAK,GAAG,CAAE,GAAGiC,IAAI,CAACjC,KAAK,CAAE;IAC9B,IAAI,CAACC,aAAa,GAAGgC,IAAI,CAAChC,aAAa,CAAC4B,GAAG,CAAEC,CAAC,IAAI,IAAInC,OAAO,CAAEmC,CAAC,CAAE,CAAC,CAAE,EAAEA,CAAC,CAAE,CAAC,CAAE,EAAEA,CAAC,CAAE,CAAC,CAAE,EAAEA,CAAC,CAAE,CAAC,CAAG,CAAE,CAAC;IACjG,IAAI,CAAC5B,SAAS,GAAG+B,IAAI,CAAC/B,SAAS;IAC/B,IAAI,CAACC,OAAO,GAAG8B,IAAI,CAAC9B,OAAO;IAE3B,OAAO,IAAI;EAEZ;AAED;AAEA,SAASN,UAAU", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}