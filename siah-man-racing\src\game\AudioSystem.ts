// Dynamic Music System for Super Siah Man Racing
import { Howl, Howler } from 'howler';

export interface TrackInfo {
  id: string;
  name: string;
  src: string;
  intensity: 'low' | 'medium' | 'high';
  loop: boolean;
  volume: number;
  category: 'menu' | 'race' | 'lab' | 'story' | 'customization';
}

export interface SoundEffect {
  id: string;
  name: string;
  src: string;
  volume: number;
  loop: boolean;
}

export interface VoiceDialogue {
  id: string;
  character: string;
  text: string;
  audioSrc: string;
  duration?: number;
  priority: 'low' | 'medium' | 'high';
}

export class AudioSystem {
  private static instance: AudioSystem;
  private musicTracks: Map<string, { howl: Howl, info: TrackInfo }>;
  private soundEffects: Map<string, { howl: Howl, info: SoundEffect }>;
  private voiceDialogues: Map<string, { howl: Howl, info: VoiceDialogue }>;
  private currentMusic: string | null;
  private nextMusic: string | null;
  private currentVoice: string | null;
  private voiceQueue: VoiceDialogue[];
  private fadeDuration: number;
  private musicVolume: number;
  private sfxVolume: number;
  private voiceVolume: number;
  private isMusicMuted: boolean;
  private isSfxMuted: boolean;
  private isVoiceMuted: boolean;
  private raceIntensity: number; // 0-1 value representing race intensity
  private customizationMood: 'sporty' | 'elegant' | 'aggressive' | 'mystical'; // Mood based on vehicle customization
  private storyChapter: number; // Current story chapter

  // Audio processing settings
  private audioContext: AudioContext | null = null;
  private masterCompressor: DynamicsCompressorNode | null = null;
  private voiceCompressor: DynamicsCompressorNode | null = null;
  private musicEQ: BiquadFilterNode[] = [];
  private voiceEQ: BiquadFilterNode[] = [];
  private sfxEQ: BiquadFilterNode[] = [];
  
  private constructor() {
    this.musicTracks = new Map();
    this.soundEffects = new Map();
    this.voiceDialogues = new Map();
    this.currentMusic = null;
    this.nextMusic = null;
    this.currentVoice = null;
    this.voiceQueue = [];
    this.fadeDuration = 2000; // 2 seconds fade duration
    this.musicVolume = 0.7;
    this.sfxVolume = 0.8;
    this.voiceVolume = 0.9;
    this.isMusicMuted = false;
    this.isSfxMuted = false;
    this.isVoiceMuted = false;
    this.raceIntensity = 0;
    this.customizationMood = 'sporty';
    this.storyChapter = 1;

    this.initializeAudioProcessing();
    
    // Set global Howler settings
    Howler.autoUnlock = true;
    Howler.html5PoolSize = 10;
  }
  
  public static getInstance(): AudioSystem {
    if (!AudioSystem.instance) {
      AudioSystem.instance = new AudioSystem();
    }
    return AudioSystem.instance;
  }

  private async initializeAudioProcessing() {
    try {
      // Initialize Web Audio API context
      this.audioContext = new (window.AudioContext || (window as any).webkitAudioContext)();

      // Create master compressor for overall dynamics control
      this.masterCompressor = this.audioContext.createDynamicsCompressor();
      this.masterCompressor.threshold.setValueAtTime(-24, this.audioContext.currentTime);
      this.masterCompressor.knee.setValueAtTime(30, this.audioContext.currentTime);
      this.masterCompressor.ratio.setValueAtTime(12, this.audioContext.currentTime);
      this.masterCompressor.attack.setValueAtTime(0.003, this.audioContext.currentTime);
      this.masterCompressor.release.setValueAtTime(0.25, this.audioContext.currentTime);

      // Create voice-specific compressor for dialogue clarity
      this.voiceCompressor = this.audioContext.createDynamicsCompressor();
      this.voiceCompressor.threshold.setValueAtTime(-18, this.audioContext.currentTime);
      this.voiceCompressor.knee.setValueAtTime(20, this.audioContext.currentTime);
      this.voiceCompressor.ratio.setValueAtTime(8, this.audioContext.currentTime);
      this.voiceCompressor.attack.setValueAtTime(0.001, this.audioContext.currentTime);
      this.voiceCompressor.release.setValueAtTime(0.1, this.audioContext.currentTime);

      // Create EQ for music (3-band)
      this.musicEQ = [
        this.createBiquadFilter('lowshelf', 200, 0, 1), // Bass
        this.createBiquadFilter('peaking', 1000, 0, 1), // Mids
        this.createBiquadFilter('highshelf', 8000, 0, 1) // Treble
      ];

      // Create EQ for voice (optimized for speech clarity)
      this.voiceEQ = [
        this.createBiquadFilter('highpass', 80, 0, 0.7), // Remove low rumble
        this.createBiquadFilter('peaking', 2500, 3, 1.5), // Boost presence
        this.createBiquadFilter('peaking', 5000, 2, 1.2), // Boost clarity
        this.createBiquadFilter('lowpass', 12000, 0, 0.7) // Remove harsh highs
      ];

      // Create EQ for SFX (wide range)
      this.sfxEQ = [
        this.createBiquadFilter('lowshelf', 100, 0, 1),
        this.createBiquadFilter('peaking', 2000, 0, 1),
        this.createBiquadFilter('highshelf', 10000, 0, 1)
      ];

      console.log('Audio processing initialized with compression and EQ');

    } catch (error) {
      console.warn('Web Audio API not supported, falling back to basic audio:', error);
    }
  }

  private createBiquadFilter(type: BiquadFilterType, frequency: number, gain: number, Q: number): BiquadFilterNode {
    if (!this.audioContext) throw new Error('Audio context not initialized');

    const filter = this.audioContext.createBiquadFilter();
    filter.type = type;
    filter.frequency.setValueAtTime(frequency, this.audioContext.currentTime);
    filter.gain.setValueAtTime(gain, this.audioContext.currentTime);
    filter.Q.setValueAtTime(Q, this.audioContext.currentTime);
    return filter;
  }

  // Load music tracks
  public loadMusicTrack(trackInfo: TrackInfo): Promise<void> {
    return new Promise((resolve, reject) => {
      try {
        const howl = new Howl({
          src: [trackInfo.src],
          loop: trackInfo.loop,
          volume: 0, // Start at 0 volume for crossfading
          html5: true,
          onload: () => {
            this.musicTracks.set(trackInfo.id, { howl, info: trackInfo });
            resolve();
          },
          onloaderror: (id: number | undefined, error: any) => {
            reject(`Error loading music track ${trackInfo.name}: ${error}`);
          }
        });
      } catch (error) {
        reject(`Error creating Howl for ${trackInfo.name}: ${error}`);
      }
    });
  }
  
  // Load sound effect
  public loadSoundEffect(effectInfo: SoundEffect): Promise<void> {
    return new Promise((resolve, reject) => {
      try {
        const howl = new Howl({
          src: [effectInfo.src],
          loop: effectInfo.loop,
          volume: effectInfo.volume * this.sfxVolume,
          html5: false, // WebAudio for sound effects for better performance
          onload: () => {
            this.soundEffects.set(effectInfo.id, { howl, info: effectInfo });
            resolve();
          },
          onloaderror: (id: number | undefined, error: any) => {
            reject(`Error loading sound effect ${effectInfo.name}: ${error}`);
          }
        });
      } catch (error) {
        reject(`Error creating Howl for ${effectInfo.name}: ${error}`);
      }
    });
  }
  
  // Play music with crossfade
  public playMusic(trackId: string): void {
    if (!this.musicTracks.has(trackId)) {
      console.error(`Music track ${trackId} not found`);
      return;
    }
    
    if (this.currentMusic === trackId) {
      return; // Already playing this track
    }
    
    this.nextMusic = trackId;
    
    // If no music is currently playing, start immediately
    if (this.currentMusic === null) {
      this.startMusic(trackId);
      return;
    }
    
    // Otherwise crossfade
    this.crossfade(this.currentMusic, trackId);
  }
  
  // Start playing music
  private startMusic(trackId: string): void {
    const track = this.musicTracks.get(trackId);
    if (!track) return;
    
    const { howl, info } = track;
    
    // Set volume based on mute state
    const targetVolume = this.isMusicMuted ? 0 : info.volume * this.musicVolume;
    
    howl.volume(targetVolume);
    howl.play();
    
    this.currentMusic = trackId;
    this.nextMusic = null;
  }
  
  // Crossfade between tracks
  private crossfade(fromTrackId: string, toTrackId: string): void {
    const fromTrack = this.musicTracks.get(fromTrackId);
    const toTrack = this.musicTracks.get(toTrackId);
    
    if (!fromTrack || !toTrack) return;
    
    const { howl: fromHowl } = fromTrack;
    const { howl: toHowl, info: toInfo } = toTrack; // 'toInfo' might be used later if track properties influence crossfade
    
    // Calculate target volumes
    const fromTargetVolume = 0;
    const toTargetVolume = this.isMusicMuted ? 0 : toInfo.volume * this.musicVolume;
    
    // Start the new track
    toHowl.volume(0);
    toHowl.play();
    
    // Fade out the current track
    fromHowl.fade(fromHowl.volume(), fromTargetVolume, this.fadeDuration);
    
    // Fade in the new track
    toHowl.fade(0, toTargetVolume, this.fadeDuration);
    
    // Update current track after fade duration
    setTimeout(() => {
      fromHowl.stop();
      this.currentMusic = toTrackId;
      this.nextMusic = null;
    }, this.fadeDuration);
  }
  
  // Play sound effect
  public playSoundEffect(effectId: string): number {
    if (this.isSfxMuted) return -1;
    
    const effect = this.soundEffects.get(effectId);
    if (!effect) {
      console.error(`Sound effect ${effectId} not found`);
      return -1;
    }
    
    const { howl } = effect;
    return howl.play();
  }
  
  // Stop sound effect
  public stopSoundEffect(effectId: string, id?: number): void {
    const effect = this.soundEffects.get(effectId);
    if (!effect) return;
    
    if (id !== undefined) {
      effect.howl.stop(id);
    } else {
      effect.howl.stop();
    }
  }
  
  // Set music volume
  public setMusicVolume(volume: number): void {
    this.musicVolume = Math.max(0, Math.min(1, volume));
    
    if (this.currentMusic && !this.isMusicMuted) {
      const track = this.musicTracks.get(this.currentMusic);
      if (track) {
        track.howl.volume(track.info.volume * this.musicVolume);
      }
    }
  }
  
  // Set sound effects volume
  public setSfxVolume(volume: number): void {
    this.sfxVolume = Math.max(0, Math.min(1, volume));
    
    if (!this.isSfxMuted) {
      this.soundEffects.forEach(({ howl, info }) => {
        howl.volume(info.volume * this.sfxVolume);
      });
    }
  }
  
  // Mute/unmute music
  public toggleMuteMusic(): boolean {
    this.isMusicMuted = !this.isMusicMuted;
    
    if (this.currentMusic) {
      const track = this.musicTracks.get(this.currentMusic);
      if (track) {
        track.howl.volume(this.isMusicMuted ? 0 : track.info.volume * this.musicVolume);
      }
    }
    
    return this.isMusicMuted;
  }
  
  // Mute/unmute sound effects
  public toggleMuteSfx(): boolean {
    this.isSfxMuted = !this.isSfxMuted;

    this.soundEffects.forEach(({ howl, info }) => {
      howl.volume(this.isSfxMuted ? 0 : info.volume * this.sfxVolume);
    });

    return this.isSfxMuted;
  }

  // Voice volume control
  public setVoiceVolume(volume: number): void {
    this.voiceVolume = Math.max(0, Math.min(1, volume));

    if (!this.isVoiceMuted) {
      this.voiceDialogues.forEach(({ howl, info }) => {
        howl.volume(this.voiceVolume);
      });
    }
  }

  // Mute/unmute voices
  public toggleMuteVoice(): boolean {
    this.isVoiceMuted = !this.isVoiceMuted;

    this.voiceDialogues.forEach(({ howl }) => {
      howl.volume(this.isVoiceMuted ? 0 : this.voiceVolume);
    });

    return this.isVoiceMuted;
  }

  // Load voice dialogue
  public loadVoiceDialogue(dialogue: VoiceDialogue): Promise<void> {
    return new Promise((resolve, reject) => {
      try {
        const howl = new Howl({
          src: [dialogue.audioSrc],
          volume: this.isVoiceMuted ? 0 : this.voiceVolume,
          html5: true,
          preload: true,
          onload: () => {
            // Get actual duration from the loaded audio
            dialogue.duration = howl.duration() * 1000; // Convert to milliseconds
            resolve();
          },
          onerror: (error) => {
            console.error(`Failed to load voice dialogue ${dialogue.id}:`, error);
            reject(error);
          }
        });

        this.voiceDialogues.set(dialogue.id, { howl, info: dialogue });

      } catch (error) {
        console.error(`Error loading voice dialogue ${dialogue.id}:`, error);
        reject(error);
      }
    });
  }

  // Play voice dialogue with character synchronization
  public playVoiceDialogue(dialogueId: string, onComplete?: () => void): Promise<void> {
    return new Promise((resolve, reject) => {
      const dialogue = this.voiceDialogues.get(dialogueId);
      if (!dialogue) {
        console.error(`Voice dialogue ${dialogueId} not found`);
        reject(new Error(`Voice dialogue ${dialogueId} not found`));
        return;
      }

      // Stop current voice if playing
      if (this.currentVoice) {
        this.stopVoiceDialogue(this.currentVoice);
      }

      const { howl, info } = dialogue;

      // Set up completion handler
      const handleComplete = () => {
        this.currentVoice = null;
        if (onComplete) onComplete();
        resolve();

        // Process voice queue
        this.processVoiceQueue();
      };

      howl.once('end', handleComplete);
      howl.once('stop', handleComplete);

      // Play with proper volume and processing
      howl.volume(this.isVoiceMuted ? 0 : this.voiceVolume);
      howl.play();

      this.currentVoice = dialogueId;

      console.log(`Playing voice dialogue: ${info.character} - "${info.text}"`);
    });
  }

  // Queue voice dialogue for sequential playback
  public queueVoiceDialogue(dialogue: VoiceDialogue): void {
    this.voiceQueue.push(dialogue);

    // If no voice is currently playing, start processing queue
    if (!this.currentVoice) {
      this.processVoiceQueue();
    }
  }

  // Process voice queue
  private processVoiceQueue(): void {
    if (this.voiceQueue.length === 0 || this.currentVoice) {
      return;
    }

    const nextDialogue = this.voiceQueue.shift();
    if (nextDialogue) {
      // Load and play the dialogue
      this.loadVoiceDialogue(nextDialogue).then(() => {
        this.playVoiceDialogue(nextDialogue.id);
      }).catch(error => {
        console.error('Failed to load queued dialogue:', error);
        this.processVoiceQueue(); // Try next in queue
      });
    }
  }

  // Stop voice dialogue
  public stopVoiceDialogue(dialogueId?: string): void {
    if (dialogueId) {
      const dialogue = this.voiceDialogues.get(dialogueId);
      if (dialogue) {
        dialogue.howl.stop();
      }
      if (this.currentVoice === dialogueId) {
        this.currentVoice = null;
      }
    } else {
      // Stop all voices
      this.voiceDialogues.forEach(({ howl }) => {
        howl.stop();
      });
      this.currentVoice = null;
      this.voiceQueue = [];
    }
  }

  // Set race intensity (0-1) for adaptive music
  public setRaceIntensity(intensity: number): void {
    this.raceIntensity = Math.max(0, Math.min(1, intensity));
    this.updateAdaptiveMusic();
  }
  
  // Set customization mood for adaptive music
  public setCustomizationMood(mood: 'sporty' | 'elegant' | 'aggressive' | 'mystical'): void {
    this.customizationMood = mood;
    this.updateAdaptiveMusic();
  }
  
  // Set story chapter for adaptive music
  public setStoryChapter(chapter: number): void {
    this.storyChapter = chapter;
    this.updateAdaptiveMusic();
  }
  
  // Update adaptive music based on current game state
  private updateAdaptiveMusic(): void {
    // Get current game screen from the current music track
    if (!this.currentMusic) return;
    
    const currentTrack = this.musicTracks.get(this.currentMusic);
    if (!currentTrack) return;
    
    const category = currentTrack.info.category;
    
    // Select appropriate music based on current state
    let targetTrackId: string | null = null;
    
    switch (category) {
      case 'race':
        // Select race music based on intensity
        if (this.raceIntensity < 0.3) {
          targetTrackId = this.findTrackByIntensity('race', 'low');
        } else if (this.raceIntensity < 0.7) {
          targetTrackId = this.findTrackByIntensity('race', 'medium');
        } else {
          targetTrackId = this.findTrackByIntensity('race', 'high');
        }
        break;
        
      case 'lab':
        // Select lab music based on customization mood
        targetTrackId = this.findTrackByMood(this.customizationMood);
        break;
        
      case 'story':
        // Select story music based on chapter
        targetTrackId = this.findTrackByChapter(this.storyChapter);
        break;
    }
    
    // Play the selected track if different from current
    if (targetTrackId && targetTrackId !== this.currentMusic && targetTrackId !== this.nextMusic) {
      this.playMusic(targetTrackId);
    }
  }
  
  // Find a track by intensity and category
  private findTrackByIntensity(category: 'menu' | 'race' | 'lab' | 'story', intensity: 'low' | 'medium' | 'high'): string | null {
    for (const [id, { info }] of this.musicTracks.entries()) {
      if (info.category === category && info.intensity === intensity) {
        return id;
      }
    }
    return null;
  }
  
  // Find a track by customization mood
  private findTrackByMood(mood: 'sporty' | 'elegant' | 'aggressive' | 'mystical'): string | null {
    // This would require additional metadata on tracks
    // For now, we'll use a simple mapping
    const moodToIntensity: { [key: string]: 'low' | 'medium' | 'high' } = {
      'sporty': 'medium',
      'elegant': 'low',
      'aggressive': 'high',
      'mystical': 'medium'
    };
    
    return this.findTrackByIntensity('lab', moodToIntensity[mood]);
  }
  
  // Find a track by story chapter
  private findTrackByChapter(chapter: number): string | null {
    // This would require additional metadata on tracks
    // For now, we'll use a simple approach based on intensity
    const chapterToIntensity: { [key: number]: 'low' | 'medium' | 'high' } = {
      1: 'low',
      2: 'medium',
      3: 'high',
      4: 'high',
      5: 'high'
    };
    
    const intensity = chapterToIntensity[chapter] || 'medium';
    return this.findTrackByIntensity('story', intensity);
  }
  
  // Stop all audio
  public stopAll(): void {
    this.musicTracks.forEach(({ howl }) => {
      howl.stop();
      howl.unload(); // Unload each music track
    });
    this.soundEffects.forEach(({ howl }) => {
      howl.stop();
      howl.unload(); // Unload each sound effect
    });
    Howler.stop(); // Global stop as a final measure
    this.currentMusic = null;
    this.nextMusic = null;
  }
  
  // Load all game assets with your custom audio files
  public async loadGameAssets(): Promise<void> {
    console.log('Loading Siah Man Racing audio assets...');

    // Load your custom music tracks
    const musicTracks: TrackInfo[] = [
      {
        id: 'atl_ride',
        name: 'ATL Ride by Treal',
        src: '/assets/music/ATL RIDE BY TREAL_041133.mp3',
        intensity: 'high',
        loop: true,
        volume: 0.7,
        category: 'race'
      },
      {
        id: 'big_balling',
        name: 'Big Balling Instrumental',
        src: '/assets/music/BIG BALLING INSTRUMENTAL_041206.mp3',
        intensity: 'medium',
        loop: true,
        volume: 0.6,
        category: 'menu'
      },
      {
        id: 'epiclations',
        name: 'Epiclations The Book 1',
        src: '/assets/music/EPICLATIONS THE BOOK 1_034752.mp3',
        intensity: 'low',
        loop: true,
        volume: 0.5,
        category: 'story'
      },
      {
        id: 'home_skillet',
        name: 'Home Skillet Instrumental',
        src: '/assets/music/HOME SKILLET INSTRUMENTAL BY TREAL_041033.mp3',
        intensity: 'medium',
        loop: true,
        volume: 0.6,
        category: 'lab'
      },
      {
        id: 'you_want_me_back',
        name: 'You Want Me Back Instrumental',
        src: '/assets/music/YOU WANT ME BACK INSTRUMENTAL TREAL_041115.mp3',
        intensity: 'high',
        loop: true,
        volume: 0.7,
        category: 'customization'
      },
      {
        id: 'zaris_classroom',
        name: 'Zaris Classroom',
        src: '/assets/music/Zaris_Classroom.m4a',
        intensity: 'low',
        loop: true,
        volume: 0.4,
        category: 'story'
      }
    ];

    // Load sound effects
    const soundEffects: SoundEffect[] = [
      // Engine sounds
      { id: 'engine_idle', name: 'Engine Idle', src: '/assets/sounds/engine_idle.wav', volume: 0.3, loop: true },
      { id: 'engine_rev', name: 'Engine Rev', src: '/assets/sounds/engine_rev.wav', volume: 0.5, loop: false },
      { id: 'engine_high', name: 'Engine High RPM', src: '/assets/sounds/engine_high.wav', volume: 0.4, loop: true },

      // Collision and impact sounds
      { id: 'collision_light', name: 'Light Collision', src: '/assets/sounds/collision_light.wav', volume: 0.6, loop: false },
      { id: 'collision_heavy', name: 'Heavy Collision', src: '/assets/sounds/collision_heavy.wav', volume: 0.8, loop: false },
      { id: 'crash', name: 'Crash', src: '/assets/sounds/crash.wav', volume: 0.7, loop: false },

      // Nitro and power-up effects
      { id: 'nitro_activate', name: 'Nitro Activate', src: '/assets/sounds/nitro_activate.wav', volume: 0.7, loop: false },
      { id: 'nitro_loop', name: 'Nitro Loop', src: '/assets/sounds/nitro_loop.wav', volume: 0.5, loop: true },
      { id: 'powerup_collect', name: 'Power-up Collect', src: '/assets/sounds/powerup_collect.wav', volume: 0.6, loop: false },

      // UI and menu sounds
      { id: 'ui_click', name: 'UI Click', src: '/assets/sounds/ui_click.wav', volume: 0.4, loop: false },
      { id: 'ui_hover', name: 'UI Hover', src: '/assets/sounds/ui_hover.wav', volume: 0.3, loop: false },
      { id: 'ui_confirm', name: 'UI Confirm', src: '/assets/sounds/ui_confirm.wav', volume: 0.5, loop: false },
      { id: 'ui_back', name: 'UI Back', src: '/assets/sounds/ui_back.wav', volume: 0.4, loop: false },

      // Race events
      { id: 'race_start', name: 'Race Start', src: '/assets/sounds/race_start.wav', volume: 0.8, loop: false },
      { id: 'lap_complete', name: 'Lap Complete', src: '/assets/sounds/lap_complete.wav', volume: 0.7, loop: false },
      { id: 'race_finish', name: 'Race Finish', src: '/assets/sounds/race_finish.wav', volume: 0.8, loop: false },
      { id: 'countdown_beep', name: 'Countdown Beep', src: '/assets/sounds/countdown_beep.wav', volume: 0.6, loop: false },

      // Environmental sounds
      { id: 'rain', name: 'Rain', src: '/assets/sounds/rain.wav', volume: 0.3, loop: true },
      { id: 'thunder', name: 'Thunder', src: '/assets/sounds/thunder.wav', volume: 0.5, loop: false },
      { id: 'wind', name: 'Wind', src: '/assets/sounds/wind.wav', volume: 0.2, loop: true },

      // Tire and surface sounds
      { id: 'tire_screech', name: 'Tire Screech', src: '/assets/sounds/tire_screech.wav', volume: 0.6, loop: false },
      { id: 'drift_start', name: 'Drift Start', src: '/assets/sounds/drift_start.wav', volume: 0.5, loop: false },
      { id: 'offroad_surface', name: 'Offroad Surface', src: '/assets/sounds/offroad_surface.wav', volume: 0.4, loop: true }
    ];

    // Load all music tracks
    const musicPromises = musicTracks.map(track => this.loadMusicTrack(track));

    // Load all sound effects
    const sfxPromises = soundEffects.map(sfx => this.loadSoundEffect(sfx));

    try {
      await Promise.all([...musicPromises, ...sfxPromises]);
      console.log('All audio assets loaded successfully!');
    } catch (error) {
      console.error('Error loading audio assets:', error);
    }
  }

  // Load all character voices for story mode
  public async loadCharacterVoices(): Promise<void> {
    console.log('Loading character voice dialogues...');

    const voiceDialogues: VoiceDialogue[] = [
      // Dr. Neal's voice lines
      { id: 'drneal_ch1_1', character: 'dr_neal', text: "Siah, I've been working on something special in my MAD Laboratory.", audioSrc: '/assets/voices/drneal_story_ch1_line1.mp3', priority: 'high' },
      { id: 'drneal_ch1_2', character: 'dr_neal', text: "This formula creates a connection between driver and vehicle unlike anything we've seen before.", audioSrc: '/assets/voices/drneal_story_ch1_line2.mp3', priority: 'high' },
      { id: 'drneal_ch1_3', character: 'dr_neal', text: "Well, that's why I called you here. I need someone who understands cars on an intuitive level.", audioSrc: '/assets/voices/drneal_story_ch1_line3.mp3', priority: 'high' },
      { id: 'drneal_ch1_4', character: 'dr_neal', text: "Trust me, son. This could be revolutionary.", audioSrc: '/assets/voices/drneal_story_ch1_line4.mp3', priority: 'high' },
      { id: 'drneal_ch2_1', character: 'dr_neal', text: "The formula is ready. Are you sure you want to proceed?", audioSrc: '/assets/voices/drneal_story_ch2_line1.mp3', priority: 'high' },
      { id: 'drneal_ch2_2', character: 'dr_neal', text: "Take a deep breath. This might feel... unusual.", audioSrc: '/assets/voices/drneal_story_ch2_line2.mp3', priority: 'high' },
      { id: 'drneal_ch2_3', character: 'dr_neal', text: "It's working! The neural pathways are forming.", audioSrc: '/assets/voices/drneal_story_ch2_line3.mp3', priority: 'high' },

      // Siah's voice lines
      { id: 'siah_ch1_1', character: 'siah_man', text: "Dad, you always say that about your experiments. What makes this one different?", audioSrc: '/assets/voices/siah_story_ch1_line1.mp3', priority: 'high' },
      { id: 'siah_ch1_2', character: 'siah_man', text: "Sounds dangerous. Have you tested it?", audioSrc: '/assets/voices/siah_story_ch1_line2.mp3', priority: 'high' },
      { id: 'siah_ch1_3', character: 'siah_man', text: "Wait, you want ME to test it? Dad, I'm not sure about this...", audioSrc: '/assets/voices/siah_story_ch1_line3.mp3', priority: 'high' },
      { id: 'siah_ch1_4', character: 'siah_man', text: "...Alright. Let's do it. What's the worst that could happen?", audioSrc: '/assets/voices/siah_story_ch1_line4.mp3', priority: 'high' },
      { id: 'siah_ch2_1', character: 'siah_man', text: "I'm ready, Dad. Let's see what this 'Car Whisperer' formula can do.", audioSrc: '/assets/voices/siah_story_ch2_line1.mp3', priority: 'high' },
      { id: 'siah_ch2_2', character: 'siah_man', text: "I feel... strange. Like I can hear the engine talking to me.", audioSrc: '/assets/voices/siah_story_ch2_line2.mp3', priority: 'high' },
      { id: 'siah_ch2_3', character: 'siah_man', text: "I understand the vehicle now. Not just how it works, but how it feels.", audioSrc: '/assets/voices/siah_story_ch2_line3.mp3', priority: 'high' },
      { id: 'siah_ch3_1', character: 'siah_man', text: "I'm just here to race, Max. Nothing more.", audioSrc: '/assets/voices/siah_story_ch3_line1.mp3', priority: 'high' },
      { id: 'siah_ch3_2', character: 'siah_man', text: "You're on. One lap around the MAD Lab Circuit.", audioSrc: '/assets/voices/siah_story_ch3_line2.mp3', priority: 'high' },
      { id: 'siah_ch3_3', character: 'siah_man', text: "And when I win, you stop calling my dad crazy. Deal.", audioSrc: '/assets/voices/siah_story_ch3_line3.mp3', priority: 'high' },

      // Max Velocity's voice lines
      { id: 'max_ch3_1', character: 'max_velocity', text: "Well, well, if it isn't Dr. Neal's kid.", audioSrc: '/assets/voices/maxvelocity_story_ch3_line1.mp3', priority: 'high' },
      { id: 'max_ch3_2', character: 'max_velocity', text: "Racing is my territory. Your dad's crazy experiments won't change that.", audioSrc: '/assets/voices/maxvelocity_story_ch3_line2.mp3', priority: 'high' },
      { id: 'max_ch3_3', character: 'max_velocity', text: "When I win, you and your dad stay away from the championship. Deal?", audioSrc: '/assets/voices/maxvelocity_story_ch3_line3.mp3', priority: 'high' },

      // Gameplay voices
      { id: 'siah_harmony', character: 'siah_man', text: "Harmony ability activated!", audioSrc: '/assets/voices/siah_gameplay_ability_harmony.mp3', priority: 'medium' },
      { id: 'siah_intuition', character: 'siah_man', text: "Intuition ability activated!", audioSrc: '/assets/voices/siah_gameplay_ability_intuition.mp3', priority: 'medium' },
      { id: 'siah_empathy', character: 'siah_man', text: "Empathy ability activated!", audioSrc: '/assets/voices/siah_gameplay_avility_empathy.mp3', priority: 'medium' },
      { id: 'siah_collision_1', character: 'siah_man', text: "Whoa!", audioSrc: '/assets/voices/siah_gameplay_collision1.mp3', priority: 'low' },
      { id: 'siah_collision_2', character: 'siah_man', text: "That was close!", audioSrc: '/assets/voices/siah_gameplay_collision2.mp3', priority: 'low' },
      { id: 'siah_collision_3', character: 'siah_man', text: "Need to be more careful!", audioSrc: '/assets/voices/siah_gameplay_collision3.mp3', priority: 'low' },
      { id: 'siah_drift_1', character: 'siah_man', text: "Nice drift!", audioSrc: '/assets/voices/siah_gameplay_drifting1.mp3', priority: 'low' },
      { id: 'siah_drift_2', character: 'siah_man', text: "Perfect control!", audioSrc: '/assets/voices/siah_gameplay_drifting2.mp3', priority: 'low' },

      // Announcer voices
      { id: 'announcer_countdown_3', character: 'announcer', text: "Three!", audioSrc: '/assets/voices/announcer_race_countdown3.mp3', priority: 'high' },
      { id: 'announcer_countdown_2', character: 'announcer', text: "Two!", audioSrc: '/assets/voices/announcer_race_countdown2.mp3', priority: 'high' },
      { id: 'announcer_countdown_1', character: 'announcer', text: "One!", audioSrc: '/assets/voices/announcer_race_countdown1.mp3', priority: 'high' },
      { id: 'announcer_go', character: 'announcer', text: "GO!", audioSrc: '/assets/voices/announcer_race_go.mp3', priority: 'high' },
      { id: 'announcer_final_lap', character: 'announcer', text: "Final lap!", audioSrc: '/assets/voices/announcer_race_finallap.mp3', priority: 'medium' },
      { id: 'announcer_complete', character: 'announcer', text: "Race complete!", audioSrc: '/assets/voices/announcer_race_complete.mp3', priority: 'high' },
      { id: 'announcer_first', character: 'announcer', text: "First place!", audioSrc: '/assets/voices/announcer_results_first.mp3', priority: 'high' },
      { id: 'announcer_second', character: 'announcer', text: "Second place!", audioSrc: '/assets/voices/announcer_results_second.mp3', priority: 'high' },
      { id: 'announcer_third', character: 'announcer', text: "Third place!", audioSrc: '/assets/voices/announcer_results_third.mp3', priority: 'high' },

      // Navigation voices
      { id: 'nav_main_menu', character: 'narrator', text: "Main menu", audioSrc: '/assets/voices/nav_menu_main.mp3', priority: 'low' },
      { id: 'nav_customization', character: 'narrator', text: "Customization", audioSrc: '/assets/voices/nav_menu_customization.mp3', priority: 'low' },
      { id: 'nav_tutorial_controls', character: 'narrator', text: "Tutorial controls", audioSrc: '/assets/voices/nav_tutorial_controls.mp3', priority: 'medium' },
      { id: 'nav_tutorial_general', character: 'narrator', text: "General tutorial", audioSrc: '/assets/voices/nav_tutorial_general1.mp3', priority: 'medium' },
      { id: 'nav_tutorial_track', character: 'narrator', text: "Track tutorial", audioSrc: '/assets/voices/nav_tutorial_track.mp3', priority: 'medium' }
    ];

    // Load all voice dialogues
    const voicePromises = voiceDialogues.map(dialogue => this.loadVoiceDialogue(dialogue));

    try {
      await Promise.all(voicePromises);
      console.log('All character voices loaded successfully!');
    } catch (error) {
      console.error('Error loading character voices:', error);
    }
  }

  // Clean up resources
  public dispose(): void {
    this.stopAll(); // stopAll now also unloads
    this.musicTracks.clear();
    this.soundEffects.clear();
    this.voiceDialogues.clear();
    // Howler.unload(); // Consider if a global unload is needed here, but stopAll is quite thorough.
  }
}
