import { createSlice, PayloadAction } from '@reduxjs/toolkit';
import { Vehicle, VehicleType, VehicleUpgrades } from '../types/GameTypes';

interface VehiclePart {
  id: string;
  name: string;
  type: 'body' | 'wheels' | 'spoiler' | 'engine' | 'transmission' | 'suspension' | 'tires' | 'nitro' | 'decal' | 'headlights' | 'exhaust' | 'horn' | 'antenna' | 'stickers';
  unlocked: boolean;
  stats: {
    speed: number;
    acceleration: number;
    handling: number;
    braking: number;
    fun?: number; // New fun factor stat!
  };
  price: number;
  image: string;
  vehicleTypes?: VehicleType[];
  description?: string;
  rarity?: 'common' | 'rare' | 'epic' | 'legendary';
  specialEffect?: string; // Special visual or sound effects
  emoji?: string; // Fun emoji representation
}

interface VehicleColor {
  id: string;
  name: string;
  primary: string;
  secondary: string;
  detail: string;
  unlocked: boolean;
  price: number;
}

interface VehicleState {
  // Available vehicles with their base properties
  vehicles: Record<string, Vehicle>;

  // Currently selected vehicle
  currentVehicleId: string;

  // Unlocked vehicles list
  unlockedVehicles: string[];
  
  // Available parts for customization
  availableParts: VehiclePart[];
  
  // Equipped parts for each vehicle
  equippedParts: {
    [vehicleId: string]: {
      body: string | null;
      wheels: string | null;
      spoiler: string | null;
      engine: string | null;
      transmission: string | null;
      suspension: string | null;
      tires: string | null;
      nitro: string | null;
      headlights: string | null;
      exhaust: string | null;
      decals: string[];
    };
  };
  
  // Available colors for vehicles
  availableColors: VehicleColor[];
  
  // Selected colors for each vehicle
  selectedColors: {
    [vehicleId: string]: {
      colorId: string;
    };
  };
  
  // Saved vehicle configurations
  savedConfigurations: {
    id: string;
    name: string;
    vehicleId: string;
    parts: {
      body: string | null;
      wheels: string | null;
      spoiler: string | null;
      engine: string | null;
      transmission: string | null;
      suspension: string | null;
      tires: string | null;
      nitro: string | null;
      headlights: string | null;
      exhaust: string | null;
      decals: string[];
    };
    colorId: string;
  }[];
  
  // Performance stats cache for quick access
  cachedStats: {
    [vehicleId: string]: {
      speed: number;
      acceleration: number;
      handling: number;
      braking: number;
    };
  };
}

const initialState: VehicleState = {
  vehicles: {
    'rainbow_racer': {
      id: 'rainbow_racer',
      name: '🌈 Rainbow Racer',
      type: 'sports',
      topSpeed: 200,
      acceleration: 9.0,
      handling: 8.5,
      braking: 8.0,
      price: 0, // Free starter car!
      unlocked: true,
      colors: ['#ff0000', '#ff8000', '#ffff00', '#00ff00', '#0080ff', '#8000ff'],
      upgrades: {
        engine: 0,
        transmission: 0,
        suspension: 0,
        tires: 0,
        nitro: 0
      },
      boostRechargeMax: 100,
      boostRechargeRate: 1.5,
      boostRechargeDelay: 2000,
      boostRechargeRemaining: 100
    },
    'candy_cruiser': {
      id: 'candy_cruiser',
      name: '🍭 Candy Cruiser',
      type: 'sports',
      topSpeed: 180,
      acceleration: 8.0,
      handling: 9.5,
      braking: 7.5,
      price: 5000,
      unlocked: false,
      colors: ['#ff69b4', '#ffc0cb', '#ff1493', '#da70d6'],
      description: 'Sweet as candy and twice as fast!',
      emoji: '🍭',
      upgrades: {
        engine: 0,
        transmission: 0,
        suspension: 0,
        tires: 0,
        nitro: 0
      },
      boostRechargeMax: 90,
      boostRechargeRate: 1.3,
      boostRechargeDelay: 2200,
      boostRechargeRemaining: 90
    },
    'muscle_car': {
      id: 'muscle_car',
      name: 'Turbo Whisperer',
      type: 'muscle',
      topSpeed: 200,
      acceleration: 9.5,
      handling: 7.0,
      braking: 6.5,
      price: 35000,
      unlocked: false,
      colors: ['#ff0000', '#0000ff', '#ffffff', '#000000'],
      upgrades: {
        engine: 0,
        transmission: 0,
        suspension: 0,
        tires: 0,
        nitro: 0
      },
      boostRechargeMax: 120,
      boostRechargeRate: 1.8,
      boostRechargeDelay: 1800,
      boostRechargeRemaining: 120
    },
    'rally_car': {
      id: 'rally_car',
      name: 'MAD Prototype',
      type: 'rally',
      topSpeed: 180,
      acceleration: 8.0,
      handling: 9.5,
      braking: 9.0,
      price: 40000,
      unlocked: false,
      colors: ['#ff0000', '#0000ff', '#ffffff', '#000000'],
      upgrades: {
        engine: 0,
        transmission: 0,
        suspension: 0,
        tires: 0,
        nitro: 0
      },
      boostRechargeMax: 110,
      boostRechargeRate: 1.6,
      boostRechargeDelay: 1900,
      boostRechargeRemaining: 110
    },
    'supercar': {
      id: 'supercar',
      name: 'Velocity Vortex',
      type: 'supercar',
      topSpeed: 250,
      acceleration: 10.0,
      handling: 8.5,
      braking: 9.0,
      price: 100000,
      unlocked: false,
      colors: ['#ff0000', '#0000ff', '#ffffff', '#000000'],
      upgrades: {
        engine: 0,
        transmission: 0,
        suspension: 0,
        tires: 0,
        nitro: 0
      },
      boostRechargeMax: 150,
      boostRechargeRate: 2.2,
      boostRechargeDelay: 1500,
      boostRechargeRemaining: 150
    },
    'concept_car': {
      id: 'concept_car',
      name: 'Quantum Racer',
      type: 'concept',
      topSpeed: 280,
      acceleration: 10.0,
      handling: 10.0,
      braking: 10.0,
      price: 200000,
      unlocked: false,
      colors: ['#ff0000', '#0000ff', '#ffffff', '#000000'],
      upgrades: {
        engine: 0,
        transmission: 0,
        suspension: 0,
        tires: 0,
        nitro: 0
      },
      boostRechargeMax: 200,
      boostRechargeRate: 2.5,
      boostRechargeDelay: 1200,
      boostRechargeRemaining: 200
    }
  },
  currentVehicleId: 'rainbow_racer',
  unlockedVehicles: ['rainbow_racer'], // Start with the free rainbow racer unlocked
  availableParts: [
    {
      id: 'body1',
      name: 'Standard Body',
      type: 'body',
      unlocked: true,
      stats: {
        speed: 0,
        acceleration: 0,
        handling: 0,
        braking: 0,
      },
      price: 0,
      image: 'standard_body.png',
    },
    {
      id: 'body2',
      name: 'Aerodynamic Body',
      type: 'body',
      unlocked: false,
      stats: {
        speed: 5,
        acceleration: 2,
        handling: 0,
        braking: 0,
      },
      price: 1000,
      image: 'aero_body.png',
    },
    {
      id: 'wheels1',
      name: 'Standard Wheels',
      type: 'wheels',
      unlocked: true,
      stats: {
        speed: 0,
        acceleration: 0,
        handling: 0,
        braking: 0,
      },
      price: 0,
      image: 'standard_wheels.png',
    },
    {
      id: 'wheels2',
      name: 'Performance Wheels',
      type: 'wheels',
      unlocked: false,
      stats: {
        speed: 0,
        acceleration: 3,
        handling: 5,
        braking: 3,
      },
      price: 800,
      image: 'performance_wheels.png',
    },
    {
      id: 'engine1',
      name: 'Standard Engine',
      type: 'engine',
      unlocked: true,
      stats: {
        speed: 0,
        acceleration: 0,
        handling: 0,
        braking: 0,
      },
      price: 0,
      image: 'standard_engine.png',
    },
    {
      id: 'engine2',
      name: 'Turbo Engine',
      type: 'engine',
      unlocked: false,
      stats: {
        speed: 10,
        acceleration: 8,
        handling: 0,
        braking: 0,
      },
      price: 2000,
      image: 'turbo_engine.png',
    },
  ],
  equippedParts: {
    'sports_car': {
      body: 'body1',
      wheels: 'wheels1',
      spoiler: null,
      engine: 'engine1',
      transmission: null,
      suspension: null,
      tires: null,
      nitro: null,
      headlights: null,
      exhaust: null,
      decals: [],
    },
    'muscle_car': {
      body: 'body1',
      wheels: 'wheels1',
      spoiler: null,
      engine: 'engine1',
      transmission: null,
      suspension: null,
      tires: null,
      nitro: null,
      headlights: null,
      exhaust: null,
      decals: [],
    },
    'rally_car': {
      body: 'body1',
      wheels: 'wheels1',
      spoiler: null,
      engine: 'engine1',
      transmission: null,
      suspension: null,
      tires: null,
      nitro: null,
      headlights: null,
      exhaust: null,
      decals: [],
    },
  },
  availableColors: [
    {
      id: 'color1',
      name: 'Classic Red',
      primary: '#ff0000',
      secondary: '#990000',
      detail: '#ffffff',
      unlocked: true,
      price: 0,
    },
    {
      id: 'color2',
      name: 'Electric Blue',
      primary: '#0066ff',
      secondary: '#003399',
      detail: '#ffffff',
      unlocked: true,
      price: 0,
    },
    {
      id: 'color3',
      name: 'Mystic Purple',
      primary: '#9900cc',
      secondary: '#660099',
      detail: '#ffcc00',
      unlocked: false,
      price: 500,
    },
  ],
  selectedColors: {
    vehicle1: {
      colorId: 'color1',
    },
  },
  savedConfigurations: [],
  // Performance stats cache
  cachedStats: {}
};

export const vehicleSlice = createSlice({
  name: 'vehicle',
  initialState,
  reducers: {
    setCurrentVehicle: (state, action: PayloadAction<string>) => {
      state.currentVehicleId = action.payload;
    },
    unlockVehicle: (state, action: PayloadAction<string>) => {
      if (state.vehicles[action.payload]) {
        state.vehicles[action.payload].unlocked = true;
      }
    },
    unlockPart: (state, action: PayloadAction<string>) => {
      const part = state.availableParts.find(p => p.id === action.payload);
      if (part) {
        part.unlocked = true;
      }
    },
    equipPart: (state, action: PayloadAction<{ vehicleId: string; partId: string }>) => {
      const part = state.availableParts.find(p => p.id === action.payload.partId);
      if (part && part.unlocked) {
        // Check if the part is compatible with this vehicle type
        const vehicle = state.vehicles[action.payload.vehicleId];
        if (vehicle && (!part.vehicleTypes || part.vehicleTypes.includes(vehicle.type))) {
          // Initialize parts object if it doesn't exist
          if (!state.equippedParts[action.payload.vehicleId]) {
            state.equippedParts[action.payload.vehicleId] = {
              body: null,
              wheels: null,
              spoiler: null,
              engine: null,
              transmission: null,
              suspension: null,
              tires: null,
              nitro: null,
              headlights: null,
              exhaust: null,
              decals: [],
            };
          }

          // Handle decals differently (can have multiple)
          if (part.type === 'decal') {
            if (!state.equippedParts[action.payload.vehicleId].decals.includes(part.id)) {
              state.equippedParts[action.payload.vehicleId].decals.push(part.id);
            }
          } else {
            (state.equippedParts[action.payload.vehicleId] as any)[part.type] = part.id;
          }
          
          // Update vehicle upgrades if it's an upgrade part
          if (['engine', 'transmission', 'suspension', 'tires', 'nitro'].includes(part.type)) {
            const upgradeLevel = part.stats.speed + part.stats.acceleration + 
                               part.stats.handling + part.stats.braking;
            const normalizedLevel = Math.floor(upgradeLevel / 10);
            
            // Update the upgrade level in the vehicle
            if (vehicle.upgrades) {
              (vehicle.upgrades as any)[part.type] = normalizedLevel;
            }
          }
          
          // Recalculate and cache the vehicle stats
          calculateVehicleStats(state, action.payload.vehicleId);
        }
      }
    },
    removePart: (state, action: PayloadAction<{ vehicleId: string; partType: string; partId?: string }>) => {
      if (state.equippedParts[action.payload.vehicleId]) {
        if (action.payload.partType === 'decals' && action.payload.partId) {
          state.equippedParts[action.payload.vehicleId].decals = 
            state.equippedParts[action.payload.vehicleId].decals.filter(id => id !== action.payload.partId);
        } else if (action.payload.partType !== 'decals') {
          (state.equippedParts[action.payload.vehicleId] as any)[action.payload.partType] = null;
          
          // Reset upgrade level if it's an upgrade part
          if (['engine', 'transmission', 'suspension', 'tires', 'nitro'].includes(action.payload.partType)) {
            const vehicle = state.vehicles[action.payload.vehicleId];
            if (vehicle && vehicle.upgrades) {
              (vehicle.upgrades as any)[action.payload.partType] = 0;
            }
          }
        }
        
        // Recalculate and cache the vehicle stats
        calculateVehicleStats(state, action.payload.vehicleId);
      }
    },
    selectColor: (state, action: PayloadAction<{ vehicleId: string; colorId: string }>) => {
      const color = state.availableColors.find(c => c.id === action.payload.colorId);
      if (color && color.unlocked) {
        state.selectedColors[action.payload.vehicleId] = {
          colorId: color.id,
        };
      }
    },
    unlockColor: (state, action: PayloadAction<string>) => {
      const color = state.availableColors.find(c => c.id === action.payload);
      if (color) {
        color.unlocked = true;
      }
    },
    saveConfiguration: (state, action: PayloadAction<{ name: string }>) => {
      const vehicleId = state.currentVehicleId;
      const parts = state.equippedParts[vehicleId] || {
        body: null,
        wheels: null,
        spoiler: null,
        engine: null,
        transmission: null,
        suspension: null,
        tires: null,
        nitro: null,
        headlights: null,
        exhaust: null,
        decals: [],
      };
      const colorId = state.selectedColors[vehicleId]?.colorId || state.availableColors[0].id;
      
      const configId = `config_${Date.now()}`;
      state.savedConfigurations.push({
        id: configId,
        name: action.payload.name,
        vehicleId,
        parts: { ...parts },
        colorId,
      });
    },
    loadConfiguration: (state, action: PayloadAction<string>) => {
      const config = state.savedConfigurations.find(c => c.id === action.payload);
      if (config) {
        state.currentVehicleId = config.vehicleId;
        state.equippedParts[config.vehicleId] = { ...config.parts };
        state.selectedColors[config.vehicleId] = {
          colorId: config.colorId,
        };
        
        // Recalculate and cache the vehicle stats
        calculateVehicleStats(state, config.vehicleId);
      }
    },
    deleteConfiguration: (state, action: PayloadAction<string>) => {
      state.savedConfigurations = state.savedConfigurations.filter(c => c.id !== action.payload);
    },
    
    upgradeVehicle: (state, action: PayloadAction<{ vehicleId: string; upgradeType: keyof VehicleUpgrades; level: number }>) => {
      const { vehicleId, upgradeType, level } = action.payload;
      const vehicle = state.vehicles[vehicleId];
      
      if (vehicle && vehicle.upgrades) {
        vehicle.upgrades[upgradeType] = level;
        
        // Recalculate and cache the vehicle stats
        calculateVehicleStats(state, vehicleId);
      }
    },
    
    calculateAllVehicleStats: (state) => {
      // Calculate stats for all vehicles
      Object.keys(state.vehicles).forEach(vehicleId => {
        calculateVehicleStats(state, vehicleId);
      });
    }
  }
});

// Helper function to calculate vehicle stats based on base stats and equipped parts
const calculateVehicleStats = (state: VehicleState, vehicleId: string) => {
  const vehicle = state.vehicles[vehicleId];
  const parts = state.equippedParts[vehicleId];
  
  if (!vehicle || !parts) return;
  
  // Start with base stats
  let speed = vehicle.topSpeed;
  let acceleration = vehicle.acceleration * 10; // Scale to 0-100
  let handling = vehicle.handling * 10;
  let braking = vehicle.braking * 10;
  
  // Add stats from equipped parts
  Object.entries(parts).forEach(([partType, partId]) => {
    if (partId && partType !== 'decals') {
      const part = state.availableParts.find(p => p.id === partId);
      if (part) {
        speed += part.stats.speed;
        acceleration += part.stats.acceleration;
        handling += part.stats.handling;
        braking += part.stats.braking;
      }
    }
  });
  
  // Add stats from upgrades
  if (vehicle.upgrades) {
    speed += vehicle.upgrades.engine * 10;
    acceleration += vehicle.upgrades.transmission * 5;
    handling += vehicle.upgrades.suspension * 5;
    braking += vehicle.upgrades.tires * 5;
    
    // Nitro adds to both speed and acceleration
    speed += vehicle.upgrades.nitro * 5;
    acceleration += vehicle.upgrades.nitro * 5;
  }
  
  // Cache the calculated stats
  state.cachedStats[vehicleId] = {
    speed,
    acceleration,
    handling,
    braking
  };
};

export const {
  setCurrentVehicle,
  unlockVehicle,
  unlockPart,
  equipPart,
  removePart,
  selectColor,
  unlockColor,
  saveConfiguration,
  loadConfiguration,
  deleteConfiguration,
  upgradeVehicle,
  calculateAllVehicleStats,
} = vehicleSlice.actions;

export default vehicleSlice.reducer;
