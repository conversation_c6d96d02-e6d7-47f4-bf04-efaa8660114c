{"ast": null, "code": "var __defProp = Object.defineProperty;\nvar __defNormalProp = (obj, key, value) => key in obj ? __defProp(obj, key, {\n  enumerable: true,\n  configurable: true,\n  writable: true,\n  value\n}) : obj[key] = value;\nvar __publicField = (obj, key, value) => {\n  __defNormalProp(obj, typeof key !== \"symbol\" ? key + \"\" : key, value);\n  return value;\n};\nimport { strToU8, zipSync } from \"fflate\";\nimport { Mesh, MeshPhysicalMaterial } from \"three\";\nclass USDZExporter {\n  constructor() {\n    __publicField(this, \"PRECISION\", 7);\n    __publicField(this, \"materials\");\n    __publicField(this, \"textures\");\n    __publicField(this, \"files\");\n    this.materials = {};\n    this.textures = {};\n    this.files = {};\n  }\n  async parse(scene) {\n    const modelFileName = \"model.usda\";\n    this.files[modelFileName] = null;\n    let output = this.buildHeader();\n    scene.traverseVisible(object => {\n      if (object instanceof Mesh && object.isMesh && object.material.isMeshStandardMaterial) {\n        const geometry = object.geometry;\n        const material = object.material;\n        const geometryFileName = \"geometries/Geometry_\" + geometry.id + \".usd\";\n        if (!(geometryFileName in this.files)) {\n          const meshObject = this.buildMeshObject(geometry);\n          this.files[geometryFileName] = this.buildUSDFileAsString(meshObject);\n        }\n        if (!(material.uuid in this.materials)) {\n          this.materials[material.uuid] = material;\n        }\n        output += this.buildXform(object, geometry, material);\n      }\n    });\n    output += this.buildMaterials(this.materials);\n    this.files[modelFileName] = strToU8(output);\n    output = null;\n    for (const id in this.textures) {\n      const texture = this.textures[id];\n      const color = id.split(\"_\")[1];\n      const isRGBA = texture.format === 1023;\n      const canvas = this.imageToCanvas(texture.image, color);\n      const blob = await new Promise(resolve => canvas == null ? void 0 : canvas.toBlob(resolve, isRGBA ? \"image/png\" : \"image/jpeg\", 1));\n      if (blob) {\n        this.files[`textures/Texture_${id}.${isRGBA ? \"png\" : \"jpg\"}`] = new Uint8Array(await blob.arrayBuffer());\n      }\n    }\n    let offset = 0;\n    for (const filename in this.files) {\n      const file = this.files[filename];\n      const headerSize = 34 + filename.length;\n      offset += headerSize;\n      const offsetMod64 = offset & 63;\n      if (offsetMod64 !== 4 && file !== null && file instanceof Uint8Array) {\n        const padLength = 64 - offsetMod64;\n        const padding = new Uint8Array(padLength);\n        this.files[filename] = [file, {\n          extra: {\n            12345: padding\n          }\n        }];\n      }\n      if (file && typeof file.length === \"number\") {\n        offset = file.length;\n      }\n    }\n    return zipSync(this.files, {\n      level: 0\n    });\n  }\n  imageToCanvas(image, color) {\n    if (typeof HTMLImageElement !== \"undefined\" && image instanceof HTMLImageElement || typeof HTMLCanvasElement !== \"undefined\" && image instanceof HTMLCanvasElement || typeof OffscreenCanvas !== \"undefined\" && image instanceof OffscreenCanvas || typeof ImageBitmap !== \"undefined\" && image instanceof ImageBitmap) {\n      const scale = 1024 / Math.max(image.width, image.height);\n      const canvas = document.createElement(\"canvas\");\n      canvas.width = image.width * Math.min(1, scale);\n      canvas.height = image.height * Math.min(1, scale);\n      const context = canvas.getContext(\"2d\");\n      context == null ? void 0 : context.drawImage(image, 0, 0, canvas.width, canvas.height);\n      if (color !== void 0) {\n        const hex = parseInt(color, 16);\n        const r = (hex >> 16 & 255) / 255;\n        const g = (hex >> 8 & 255) / 255;\n        const b = (hex & 255) / 255;\n        const imagedata = context == null ? void 0 : context.getImageData(0, 0, canvas.width, canvas.height);\n        if (imagedata) {\n          const data = imagedata == null ? void 0 : imagedata.data;\n          for (let i = 0; i < data.length; i += 4) {\n            data[i + 0] = data[i + 0] * r;\n            data[i + 1] = data[i + 1] * g;\n            data[i + 2] = data[i + 2] * b;\n          }\n          context == null ? void 0 : context.putImageData(imagedata, 0, 0);\n        }\n      }\n      return canvas;\n    }\n  }\n  buildHeader() {\n    return `#usda 1.0\n(\n    customLayerData = {\n        string creator = \"Three.js USDZExporter\"\n    }\n    metersPerUnit = 1\n    upAxis = \"Y\"\n)\n`;\n  }\n  buildUSDFileAsString(dataToInsert) {\n    let output = this.buildHeader();\n    output += dataToInsert;\n    return strToU8(output);\n  }\n  // Xform\n  buildXform(object, geometry, material) {\n    const name = \"Object_\" + object.id;\n    const transform = this.buildMatrix(object.matrixWorld);\n    if (object.matrixWorld.determinant() < 0) {\n      console.warn(\"THREE.USDZExporter: USDZ does not support negative scales\", object);\n    }\n    return `def Xform \"${name}\" (\n    prepend references = @./geometries/Geometry_${geometry.id}.usd@</Geometry>\n)\n{\n    matrix4d xformOp:transform = ${transform}\n    uniform token[] xformOpOrder = [\"xformOp:transform\"]\n    rel material:binding = </Materials/Material_${material.id}>\n}\n`;\n  }\n  buildMatrix(matrix) {\n    const array = matrix.elements;\n    return `( ${this.buildMatrixRow(array, 0)}, ${this.buildMatrixRow(array, 4)}, ${this.buildMatrixRow(array, 8)}, ${this.buildMatrixRow(array, 12)} )`;\n  }\n  buildMatrixRow(array, offset) {\n    return `(${array[offset + 0]}, ${array[offset + 1]}, ${array[offset + 2]}, ${array[offset + 3]})`;\n  }\n  // Mesh\n  buildMeshObject(geometry) {\n    const mesh = this.buildMesh(geometry);\n    return `\ndef \"Geometry\"\n{\n  ${mesh}\n}\n`;\n  }\n  buildMesh(geometry) {\n    const name = \"Geometry\";\n    const attributes = geometry.attributes;\n    const count = attributes.position.count;\n    return `\n    def Mesh \"${name}\"\n    {\n        int[] faceVertexCounts = [${this.buildMeshVertexCount(geometry)}]\n        int[] faceVertexIndices = [${this.buildMeshVertexIndices(geometry)}]\n        normal3f[] normals = [${this.buildVector3Array(attributes.normal, count)}] (\n            interpolation = \"vertex\"\n        )\n        point3f[] points = [${this.buildVector3Array(attributes.position, count)}]\n        float2[] primvars:st = [${this.buildVector2Array(attributes.uv, count)}] (\n            interpolation = \"vertex\"\n        )\n        uniform token subdivisionScheme = \"none\"\n    }\n`;\n  }\n  buildMeshVertexCount(geometry) {\n    const count = geometry.index !== null ? geometry.index.array.length : geometry.attributes.position.count;\n    return Array(count / 3).fill(3).join(\", \");\n  }\n  buildMeshVertexIndices(geometry) {\n    if (geometry.index !== null) {\n      return geometry.index.array.join(\", \");\n    }\n    const array = [];\n    const length = geometry.attributes.position.count;\n    for (let i = 0; i < length; i++) {\n      array.push(i);\n    }\n    return array.join(\", \");\n  }\n  buildVector3Array(attribute, count) {\n    if (attribute === void 0) {\n      console.warn(\"USDZExporter: Normals missing.\");\n      return Array(count).fill(\"(0, 0, 0)\").join(\", \");\n    }\n    const array = [];\n    const data = attribute.array;\n    for (let i = 0; i < data.length; i += 3) {\n      array.push(`(${data[i + 0].toPrecision(this.PRECISION)}, ${data[i + 1].toPrecision(this.PRECISION)}, ${data[i + 2].toPrecision(this.PRECISION)})`);\n    }\n    return array.join(\", \");\n  }\n  buildVector2Array(attribute, count) {\n    if (attribute === void 0) {\n      console.warn(\"USDZExporter: UVs missing.\");\n      return Array(count).fill(\"(0, 0)\").join(\", \");\n    }\n    const array = [];\n    const data = attribute.array;\n    for (let i = 0; i < data.length; i += 2) {\n      array.push(`(${data[i + 0].toPrecision(this.PRECISION)}, ${1 - data[i + 1].toPrecision(this.PRECISION)})`);\n    }\n    return array.join(\", \");\n  }\n  // Materials\n  buildMaterials(materials) {\n    const array = [];\n    for (const uuid in materials) {\n      const material = materials[uuid];\n      array.push(this.buildMaterial(material));\n    }\n    return `def \"Materials\"\n{\n${array.join(\"\")}\n}\n`;\n  }\n  buildMaterial(material) {\n    const pad = \"            \";\n    const inputs = [];\n    const samplers = [];\n    if (material.map !== null) {\n      inputs.push(`${pad}color3f inputs:diffuseColor.connect = </Materials/Material_${material.id}/Texture_${material.map.id}_diffuse.outputs:rgb>`);\n      if (material.transparent || material.alphaTest > 0) {\n        inputs.push(`${pad}float inputs:opacity.connect = </Materials/Material_${material.id}/Texture_${material.map.id}_diffuse.outputs:a>`);\n      }\n      if (material.alphaTest > 0.01) {\n        inputs.push(`${pad}float inputs:opacityThreshold = ${material.alphaTest}`);\n      } else if (material.transparent || material.alphaTest > 0) {\n        inputs.push(`${pad}float inputs:opacityThreshold = 0.01`);\n      }\n      samplers.push(this.buildTexture(material, material.map, \"diffuse\", material.color));\n    } else {\n      inputs.push(`${pad}color3f inputs:diffuseColor = ${this.buildColor(material.color)}`);\n    }\n    if (material.emissiveMap !== null) {\n      inputs.push(`${pad}color3f inputs:emissiveColor.connect = </Materials/Material_${material.id}/Texture_${material.emissiveMap.id}_emissive.outputs:rgb>`);\n      samplers.push(this.buildTexture(material, material.emissiveMap, \"emissive\"));\n    } else if (material.emissive.getHex() > 0) {\n      inputs.push(`${pad}color3f inputs:emissiveColor = ${this.buildColor(material.emissive)}`);\n    }\n    if (material.normalMap !== null) {\n      inputs.push(`${pad}normal3f inputs:normal.connect = </Materials/Material_${material.id}/Texture_${material.normalMap.id}_normal.outputs:rgb>`);\n      samplers.push(this.buildTexture(material, material.normalMap, \"normal\"));\n    }\n    if (material.aoMap !== null) {\n      inputs.push(`${pad}float inputs:occlusion.connect = </Materials/Material_${material.id}/Texture_${material.aoMap.id}_occlusion.outputs:r>`);\n      samplers.push(this.buildTexture(material, material.aoMap, \"occlusion\"));\n    }\n    if (material.roughnessMap !== null && material.roughness === 1) {\n      inputs.push(`${pad}float inputs:roughness.connect = </Materials/Material_${material.id}/Texture_${material.roughnessMap.id}_roughness.outputs:g>`);\n      samplers.push(this.buildTexture(material, material.roughnessMap, \"roughness\"));\n    } else {\n      inputs.push(`${pad}float inputs:roughness = ${material.roughness}`);\n    }\n    if (material.metalnessMap !== null && material.metalness === 1) {\n      inputs.push(`${pad}float inputs:metallic.connect = </Materials/Material_${material.id}/Texture_${material.metalnessMap.id}_metallic.outputs:b>`);\n      samplers.push(this.buildTexture(material, material.metalnessMap, \"metallic\"));\n    } else {\n      inputs.push(`${pad}float inputs:metallic = ${material.metalness}`);\n    }\n    inputs.push(`${pad}float inputs:opacity = ${material.opacity}`);\n    if (material instanceof MeshPhysicalMaterial) {\n      inputs.push(`${pad}float inputs:clearcoat = ${material.clearcoat}`);\n      inputs.push(`${pad}float inputs:clearcoatRoughness = ${material.clearcoatRoughness}`);\n      inputs.push(`${pad}float inputs:ior = ${material.ior}`);\n    }\n    return `\n    def Material \"Material_${material.id}\"\n    {\n        def Shader \"PreviewSurface\"\n        {\n            uniform token info:id = \"UsdPreviewSurface\"\n${inputs.join(\"\\n\")}\n            int inputs:useSpecularWorkflow = 0\n            token outputs:surface\n        }\n        token outputs:surface.connect = </Materials/Material_${material.id}/PreviewSurface.outputs:surface>\n        token inputs:frame:stPrimvarName = \"st\"\n        def Shader \"uvReader_st\"\n        {\n            uniform token info:id = \"UsdPrimvarReader_float2\"\n            token inputs:varname.connect = </Materials/Material_${material.id}.inputs:frame:stPrimvarName>\n            float2 inputs:fallback = (0.0, 0.0)\n            float2 outputs:result\n        }\n${samplers.join(\"\\n\")}\n    }\n`;\n  }\n  buildTexture(material, texture, mapType, color) {\n    const id = texture.id + (color ? \"_\" + color.getHexString() : \"\");\n    const isRGBA = texture.format === 1023;\n    this.textures[id] = texture;\n    return `\n      def Shader \"Transform2d_${mapType}\" (\n          sdrMetadata = {\n              string role = \"math\"\n          }\n      )\n      {\n          uniform token info:id = \"UsdTransform2d\"\n          float2 inputs:in.connect = </Materials/Material_${material.id}/uvReader_st.outputs:result>\n          float2 inputs:scale = ${this.buildVector2(texture.repeat)}\n          float2 inputs:translation = ${this.buildVector2(texture.offset)}\n          float2 outputs:result\n      }\n      def Shader \"Texture_${texture.id}_${mapType}\"\n      {\n          uniform token info:id = \"UsdUVTexture\"\n          asset inputs:file = @textures/Texture_${id}.${isRGBA ? \"png\" : \"jpg\"}@\n          float2 inputs:st.connect = </Materials/Material_${material.id}/Transform2d_${mapType}.outputs:result>\n          token inputs:wrapS = \"repeat\"\n          token inputs:wrapT = \"repeat\"\n          float outputs:r\n          float outputs:g\n          float outputs:b\n          float3 outputs:rgb\n          ${material.transparent || material.alphaTest > 0 ? \"float outputs:a\" : \"\"}\n      }`;\n  }\n  buildColor(color) {\n    return `(${color.r}, ${color.g}, ${color.b})`;\n  }\n  buildVector2(vector) {\n    return `(${vector.x}, ${vector.y})`;\n  }\n}\nexport { USDZExporter };", "map": {"version": 3, "names": ["USDZExporter", "constructor", "__publicField", "materials", "textures", "files", "parse", "scene", "modelFileName", "output", "buildHeader", "traverseVisible", "object", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "material", "isMeshStandardMaterial", "geometry", "geometryFileName", "id", "meshObject", "buildMeshObject", "buildUSDFileAsString", "uuid", "buildXform", "buildMaterials", "strToU8", "texture", "color", "split", "isRGBA", "format", "canvas", "imageToCanvas", "image", "blob", "Promise", "resolve", "toBlob", "Uint8Array", "arrayBuffer", "offset", "filename", "file", "headerSize", "length", "offsetMod64", "<PERSON><PERSON><PERSON><PERSON>", "padding", "extra", "zipSync", "level", "HTMLImageElement", "HTMLCanvasElement", "OffscreenCanvas", "ImageBitmap", "scale", "Math", "max", "width", "height", "document", "createElement", "min", "context", "getContext", "drawImage", "hex", "parseInt", "r", "g", "b", "imagedata", "getImageData", "data", "i", "putImageData", "dataToInsert", "name", "transform", "buildMatrix", "matrixWorld", "determinant", "console", "warn", "matrix", "array", "elements", "buildMatrixRow", "mesh", "buildMesh", "attributes", "count", "position", "buildMeshVertexCount", "buildMeshVertexIndices", "buildVector3Array", "normal", "buildVector2Array", "uv", "index", "Array", "fill", "join", "push", "attribute", "toPrecision", "PRECISION", "buildMaterial", "pad", "inputs", "samplers", "map", "transparent", "alphaTest", "buildTexture", "buildColor", "emissiveMap", "emissive", "getHex", "normalMap", "aoMap", "roughnessMap", "roughness", "metalnessMap", "metalness", "opacity", "MeshPhysicalMaterial", "clearcoat", "clearcoatRoughness", "ior", "mapType", "getHexString", "buildVector2", "repeat", "vector", "x", "y"], "sources": ["C:\\Users\\<USER>\\Documents\\Apps_Creations\\SiahMan_Racing4Speed_WebGame\\siah-man-racing\\node_modules\\src\\exporters\\USDZExporter.ts"], "sourcesContent": ["import { zipSync, strToU8, Zippable } from 'fflate'\nimport {\n  BufferGeometry,\n  Color,\n  Matrix4,\n  Mesh,\n  MeshPhysicalMaterial,\n  MeshStandardMaterial,\n  Object3D,\n  Texture,\n  Vector2,\n} from 'three'\nimport { Nullable } from '../types/utils'\n\ntype MaterialRepresentaion = MeshStandardMaterial | MeshPhysicalMaterial\n\nclass USDZExporter {\n  private readonly PRECISION = 7\n\n  private materials: { [key: string]: MaterialRepresentaion }\n  private textures: { [key: string]: Texture }\n\n  private files: Nullable<Zippable>\n\n  constructor() {\n    this.materials = {}\n    this.textures = {}\n\n    this.files = {}\n  }\n\n  public async parse(scene: Object3D): Promise<Uint8Array> {\n    const modelFileName = 'model.usda'\n\n    // model file should be first in USDZ archive so we init it here\n    this.files[modelFileName] = null\n\n    let output: string | null = this.buildHeader()\n\n    scene.traverseVisible((object) => {\n      if (object instanceof Mesh && object.isMesh && object.material.isMeshStandardMaterial) {\n        const geometry: BufferGeometry = object.geometry\n        const material: MaterialRepresentaion = object.material\n\n        const geometryFileName = 'geometries/Geometry_' + geometry.id + '.usd'\n\n        if (!(geometryFileName in this.files)) {\n          const meshObject = this.buildMeshObject(geometry)\n          this.files[geometryFileName] = this.buildUSDFileAsString(meshObject)\n        }\n\n        if (!(material.uuid in this.materials)) {\n          this.materials[material.uuid] = material\n        }\n\n        output += this.buildXform(object, geometry, material)\n      }\n    })\n\n    output += this.buildMaterials(this.materials)\n\n    this.files[modelFileName] = strToU8(output)\n    output = null\n\n    for (const id in this.textures) {\n      const texture = this.textures[id]\n      const color = id.split('_')[1]\n      const isRGBA = texture.format === 1023\n\n      const canvas = this.imageToCanvas(texture.image, color)\n      const blob = await new Promise<Blob | null>((resolve) =>\n        canvas?.toBlob(resolve, isRGBA ? 'image/png' : 'image/jpeg', 1),\n      )\n\n      if (blob) {\n        this.files[`textures/Texture_${id}.${isRGBA ? 'png' : 'jpg'}`] = new Uint8Array(await blob.arrayBuffer())\n      }\n    }\n\n    // 64 byte alignment\n    // https://github.com/101arrowz/fflate/issues/39#issuecomment-777263109\n\n    let offset = 0\n\n    for (const filename in this.files) {\n      const file = this.files[filename]\n      const headerSize = 34 + filename.length\n\n      offset += headerSize\n\n      const offsetMod64 = offset & 63\n\n      if (offsetMod64 !== 4 && file !== null && file instanceof Uint8Array) {\n        const padLength = 64 - offsetMod64\n        const padding = new Uint8Array(padLength)\n\n        this.files[filename] = [file, { extra: { 12345: padding } }]\n      }\n\n      if (file && typeof file.length === 'number') {\n        offset = file.length\n      }\n    }\n\n    return zipSync(this.files as Zippable, { level: 0 })\n  }\n\n  private imageToCanvas(\n    image: HTMLImageElement | HTMLCanvasElement | OffscreenCanvas | ImageBitmap,\n    color: string,\n  ): HTMLCanvasElement | undefined {\n    if (\n      (typeof HTMLImageElement !== 'undefined' && image instanceof HTMLImageElement) ||\n      (typeof HTMLCanvasElement !== 'undefined' && image instanceof HTMLCanvasElement) ||\n      (typeof OffscreenCanvas !== 'undefined' && image instanceof OffscreenCanvas) ||\n      (typeof ImageBitmap !== 'undefined' && image instanceof ImageBitmap)\n    ) {\n      const scale = 1024 / Math.max(image.width, image.height)\n\n      const canvas = document.createElement('canvas')\n      canvas.width = image.width * Math.min(1, scale)\n      canvas.height = image.height * Math.min(1, scale)\n\n      const context = canvas.getContext('2d')\n      context?.drawImage(image, 0, 0, canvas.width, canvas.height)\n\n      if (color !== undefined) {\n        const hex = parseInt(color, 16)\n\n        const r = ((hex >> 16) & 255) / 255\n        const g = ((hex >> 8) & 255) / 255\n        const b = (hex & 255) / 255\n\n        const imagedata = context?.getImageData(0, 0, canvas.width, canvas.height)\n        if (imagedata) {\n          const data = imagedata?.data\n\n          for (let i = 0; i < data.length; i += 4) {\n            data[i + 0] = data[i + 0] * r\n            data[i + 1] = data[i + 1] * g\n            data[i + 2] = data[i + 2] * b\n          }\n\n          context?.putImageData(imagedata, 0, 0)\n        }\n      }\n\n      return canvas\n    }\n  }\n\n  private buildHeader(): string {\n    return `#usda 1.0\n(\n    customLayerData = {\n        string creator = \"Three.js USDZExporter\"\n    }\n    metersPerUnit = 1\n    upAxis = \"Y\"\n)\n`\n  }\n\n  private buildUSDFileAsString(dataToInsert: string): Uint8Array {\n    let output = this.buildHeader()\n    output += dataToInsert\n    return strToU8(output)\n  }\n\n  // Xform\n  private buildXform(object: Object3D, geometry: BufferGeometry, material: MaterialRepresentaion): string {\n    const name = 'Object_' + object.id\n    const transform = this.buildMatrix(object.matrixWorld)\n\n    if (object.matrixWorld.determinant() < 0) {\n      console.warn('THREE.USDZExporter: USDZ does not support negative scales', object)\n    }\n\n    return `def Xform \"${name}\" (\n    prepend references = @./geometries/Geometry_${geometry.id}.usd@</Geometry>\n)\n{\n    matrix4d xformOp:transform = ${transform}\n    uniform token[] xformOpOrder = [\"xformOp:transform\"]\n    rel material:binding = </Materials/Material_${material.id}>\n}\n`\n  }\n\n  private buildMatrix(matrix: Matrix4): string {\n    const array = matrix.elements\n\n    return `( ${this.buildMatrixRow(array, 0)}, ${this.buildMatrixRow(array, 4)}, ${this.buildMatrixRow(\n      array,\n      8,\n    )}, ${this.buildMatrixRow(array, 12)} )`\n  }\n\n  private buildMatrixRow(array: number[], offset: number): string {\n    return `(${array[offset + 0]}, ${array[offset + 1]}, ${array[offset + 2]}, ${array[offset + 3]})`\n  }\n\n  // Mesh\n  private buildMeshObject(geometry: BufferGeometry): string {\n    const mesh = this.buildMesh(geometry)\n    return `\ndef \"Geometry\"\n{\n  ${mesh}\n}\n`\n  }\n\n  private buildMesh(geometry: BufferGeometry): string {\n    const name = 'Geometry'\n    const attributes = geometry.attributes\n    const count = attributes.position.count\n\n    return `\n    def Mesh \"${name}\"\n    {\n        int[] faceVertexCounts = [${this.buildMeshVertexCount(geometry)}]\n        int[] faceVertexIndices = [${this.buildMeshVertexIndices(geometry)}]\n        normal3f[] normals = [${this.buildVector3Array(attributes.normal, count)}] (\n            interpolation = \"vertex\"\n        )\n        point3f[] points = [${this.buildVector3Array(attributes.position, count)}]\n        float2[] primvars:st = [${this.buildVector2Array(attributes.uv, count)}] (\n            interpolation = \"vertex\"\n        )\n        uniform token subdivisionScheme = \"none\"\n    }\n`\n  }\n\n  private buildMeshVertexCount(geometry: BufferGeometry): string {\n    const count = geometry.index !== null ? geometry.index.array.length : geometry.attributes.position.count\n\n    return Array(count / 3)\n      .fill(3)\n      .join(', ')\n  }\n\n  private buildMeshVertexIndices(geometry: BufferGeometry): string {\n    if (geometry.index !== null) {\n      // @ts-ignore\n      return geometry.index.array.join(', ')\n    }\n\n    const array: number[] = []\n    const length = geometry.attributes.position.count\n\n    for (let i = 0; i < length; i++) {\n      array.push(i)\n    }\n\n    return array.join(', ')\n  }\n\n  private buildVector3Array(attribute: BufferGeometry['attributes'][number], count: number): string {\n    if (attribute === undefined) {\n      console.warn('USDZExporter: Normals missing.')\n      return Array(count).fill('(0, 0, 0)').join(', ')\n    }\n\n    const array: string[] = []\n    const data = attribute.array\n\n    for (let i = 0; i < data.length; i += 3) {\n      array.push(\n        `(${data[i + 0].toPrecision(this.PRECISION)}, ${data[i + 1].toPrecision(this.PRECISION)}, ${data[\n          i + 2\n        ].toPrecision(this.PRECISION)})`,\n      )\n    }\n\n    return array.join(', ')\n  }\n\n  private buildVector2Array(attribute: BufferGeometry['attributes'][number], count: number): string {\n    if (attribute === undefined) {\n      console.warn('USDZExporter: UVs missing.')\n      return Array(count).fill('(0, 0)').join(', ')\n    }\n\n    const array: string[] = []\n    const data = attribute.array\n\n    for (let i = 0; i < data.length; i += 2) {\n      // @ts-ignore\n      array.push(`(${data[i + 0].toPrecision(this.PRECISION)}, ${1 - data[i + 1].toPrecision(this.PRECISION)})`)\n    }\n\n    return array.join(', ')\n  }\n\n  // Materials\n  private buildMaterials(materials: USDZExporter['materials']): string {\n    const array: string[] = []\n\n    for (const uuid in materials) {\n      const material = materials[uuid]\n\n      array.push(this.buildMaterial(material))\n    }\n\n    return `def \"Materials\"\n{\n${array.join('')}\n}\n`\n  }\n\n  private buildMaterial(material: MaterialRepresentaion): string {\n    // https://graphics.pixar.com/usd/docs/UsdPreviewSurface-Proposal.html\n\n    const pad = '            '\n    const inputs = []\n    const samplers = []\n\n    if (material.map !== null) {\n      inputs.push(\n        `${pad}color3f inputs:diffuseColor.connect = </Materials/Material_${material.id}/Texture_${material.map.id}_diffuse.outputs:rgb>`,\n      )\n\n      // Include alpha input\n      if (material.transparent || material.alphaTest > 0.0) {\n        inputs.push(`${pad}float inputs:opacity.connect = </Materials/Material_${material.id}/Texture_${material.map.id}_diffuse.outputs:a>`);\n      }\n\n      // Check if alpha test is bigger than minimum of 0.01, if not and it is transparent still apply a 0.01 alpha clip in order to remove white blur in transparent place.\n      if (material.alphaTest > 0.01) {\n        inputs.push(`${pad}float inputs:opacityThreshold = ${material.alphaTest}`);\n      }\n      else if(material.transparent || material.alphaTest > 0.0) {\n        inputs.push(`${pad}float inputs:opacityThreshold = 0.01`);\n      }\n      \n      samplers.push(this.buildTexture(material, material.map, 'diffuse', material.color))\n    } else {\n      inputs.push(`${pad}color3f inputs:diffuseColor = ${this.buildColor(material.color)}`)\n    }\n\n    if (material.emissiveMap !== null) {\n      inputs.push(\n        `${pad}color3f inputs:emissiveColor.connect = </Materials/Material_${material.id}/Texture_${material.emissiveMap.id}_emissive.outputs:rgb>`,\n      )\n\n      samplers.push(this.buildTexture(material, material.emissiveMap, 'emissive'))\n    } else if (material.emissive.getHex() > 0) {\n      inputs.push(`${pad}color3f inputs:emissiveColor = ${this.buildColor(material.emissive)}`)\n    }\n\n    if (material.normalMap !== null) {\n      inputs.push(\n        `${pad}normal3f inputs:normal.connect = </Materials/Material_${material.id}/Texture_${material.normalMap.id}_normal.outputs:rgb>`,\n      )\n\n      samplers.push(this.buildTexture(material, material.normalMap, 'normal'))\n    }\n\n    if (material.aoMap !== null) {\n      inputs.push(\n        `${pad}float inputs:occlusion.connect = </Materials/Material_${material.id}/Texture_${material.aoMap.id}_occlusion.outputs:r>`,\n      )\n\n      samplers.push(this.buildTexture(material, material.aoMap, 'occlusion'))\n    }\n\n    if (material.roughnessMap !== null && material.roughness === 1) {\n      inputs.push(\n        `${pad}float inputs:roughness.connect = </Materials/Material_${material.id}/Texture_${material.roughnessMap.id}_roughness.outputs:g>`,\n      )\n\n      samplers.push(this.buildTexture(material, material.roughnessMap, 'roughness'))\n    } else {\n      inputs.push(`${pad}float inputs:roughness = ${material.roughness}`)\n    }\n\n    if (material.metalnessMap !== null && material.metalness === 1) {\n      inputs.push(\n        `${pad}float inputs:metallic.connect = </Materials/Material_${material.id}/Texture_${material.metalnessMap.id}_metallic.outputs:b>`,\n      )\n\n      samplers.push(this.buildTexture(material, material.metalnessMap, 'metallic'))\n    } else {\n      inputs.push(`${pad}float inputs:metallic = ${material.metalness}`)\n    }\n\n    inputs.push(`${pad}float inputs:opacity = ${material.opacity}`)\n\n    if (material instanceof MeshPhysicalMaterial) {\n      inputs.push(`${pad}float inputs:clearcoat = ${material.clearcoat}`)\n      inputs.push(`${pad}float inputs:clearcoatRoughness = ${material.clearcoatRoughness}`)\n      inputs.push(`${pad}float inputs:ior = ${material.ior}`)\n    }\n\n    return `\n    def Material \"Material_${material.id}\"\n    {\n        def Shader \"PreviewSurface\"\n        {\n            uniform token info:id = \"UsdPreviewSurface\"\n${inputs.join('\\n')}\n            int inputs:useSpecularWorkflow = 0\n            token outputs:surface\n        }\n        token outputs:surface.connect = </Materials/Material_${material.id}/PreviewSurface.outputs:surface>\n        token inputs:frame:stPrimvarName = \"st\"\n        def Shader \"uvReader_st\"\n        {\n            uniform token info:id = \"UsdPrimvarReader_float2\"\n            token inputs:varname.connect = </Materials/Material_${material.id}.inputs:frame:stPrimvarName>\n            float2 inputs:fallback = (0.0, 0.0)\n            float2 outputs:result\n        }\n${samplers.join('\\n')}\n    }\n`\n  }\n\n  private buildTexture(material: MaterialRepresentaion, texture: Texture, mapType: string, color?: Color): string {\n    const id = texture.id + (color ? '_' + color.getHexString() : '')\n    const isRGBA = texture.format === 1023\n\n    this.textures[id] = texture\n\n    // Add the alpha output for when transparency is set or the alpha test is above 0\n    return `\n      def Shader \"Transform2d_${mapType}\" (\n          sdrMetadata = {\n              string role = \"math\"\n          }\n      )\n      {\n          uniform token info:id = \"UsdTransform2d\"\n          float2 inputs:in.connect = </Materials/Material_${material.id}/uvReader_st.outputs:result>\n          float2 inputs:scale = ${this.buildVector2(texture.repeat)}\n          float2 inputs:translation = ${this.buildVector2(texture.offset)}\n          float2 outputs:result\n      }\n      def Shader \"Texture_${texture.id}_${mapType}\"\n      {\n          uniform token info:id = \"UsdUVTexture\"\n          asset inputs:file = @textures/Texture_${id}.${isRGBA ? 'png' : 'jpg'}@\n          float2 inputs:st.connect = </Materials/Material_${material.id}/Transform2d_${mapType}.outputs:result>\n          token inputs:wrapS = \"repeat\"\n          token inputs:wrapT = \"repeat\"\n          float outputs:r\n          float outputs:g\n          float outputs:b\n          float3 outputs:rgb\n          ${material.transparent || material.alphaTest > 0.0 ? 'float outputs:a' : ''}\n      }`\n  }\n\n  private buildColor(color: Color): string {\n    return `(${color.r}, ${color.g}, ${color.b})`\n  }\n\n  private buildVector2(vector: Vector2): string {\n    return `(${vector.x}, ${vector.y})`\n  }\n}\n\nexport { USDZExporter }\n"], "mappings": ";;;;;;;;;;;;;AAgBA,MAAMA,YAAA,CAAa;EAQjBC,YAAA,EAAc;IAPGC,aAAA,oBAAY;IAErBA,aAAA;IACAA,aAAA;IAEAA,aAAA;IAGN,KAAKC,SAAA,GAAY;IACjB,KAAKC,QAAA,GAAW;IAEhB,KAAKC,KAAA,GAAQ;EACf;EAEA,MAAaC,MAAMC,KAAA,EAAsC;IACvD,MAAMC,aAAA,GAAgB;IAGjB,KAAAH,KAAA,CAAMG,aAAa,IAAI;IAExB,IAAAC,MAAA,GAAwB,KAAKC,WAAA;IAE3BH,KAAA,CAAAI,eAAA,CAAiBC,MAAA,IAAW;MAChC,IAAIA,MAAA,YAAkBC,IAAA,IAAQD,MAAA,CAAOE,MAAA,IAAUF,MAAA,CAAOG,QAAA,CAASC,sBAAA,EAAwB;QACrF,MAAMC,QAAA,GAA2BL,MAAA,CAAOK,QAAA;QACxC,MAAMF,QAAA,GAAkCH,MAAA,CAAOG,QAAA;QAEzC,MAAAG,gBAAA,GAAmB,yBAAyBD,QAAA,CAASE,EAAA,GAAK;QAE5D,MAAED,gBAAA,IAAoB,KAAKb,KAAA,GAAQ;UAC/B,MAAAe,UAAA,GAAa,KAAKC,eAAA,CAAgBJ,QAAQ;UAChD,KAAKZ,KAAA,CAAMa,gBAAgB,IAAI,KAAKI,oBAAA,CAAqBF,UAAU;QACrE;QAEA,IAAI,EAAEL,QAAA,CAASQ,IAAA,IAAQ,KAAKpB,SAAA,GAAY;UACjC,KAAAA,SAAA,CAAUY,QAAA,CAASQ,IAAI,IAAIR,QAAA;QAClC;QAEAN,MAAA,IAAU,KAAKe,UAAA,CAAWZ,MAAA,EAAQK,QAAA,EAAUF,QAAQ;MACtD;IAAA,CACD;IAESN,MAAA,SAAKgB,cAAA,CAAe,KAAKtB,SAAS;IAE5C,KAAKE,KAAA,CAAMG,aAAa,IAAIkB,OAAA,CAAQjB,MAAM;IACjCA,MAAA;IAEE,WAAAU,EAAA,IAAM,KAAKf,QAAA,EAAU;MACxB,MAAAuB,OAAA,GAAU,KAAKvB,QAAA,CAASe,EAAE;MAChC,MAAMS,KAAA,GAAQT,EAAA,CAAGU,KAAA,CAAM,GAAG,EAAE,CAAC;MACvB,MAAAC,MAAA,GAASH,OAAA,CAAQI,MAAA,KAAW;MAElC,MAAMC,MAAA,GAAS,KAAKC,aAAA,CAAcN,OAAA,CAAQO,KAAA,EAAON,KAAK;MAChD,MAAAO,IAAA,GAAO,MAAM,IAAIC,OAAA,CAAsBC,OAAA,IAC3CL,MAAA,oBAAAA,MAAA,CAAQM,MAAA,CAAOD,OAAA,EAASP,MAAA,GAAS,cAAc,cAAc,EAAC;MAGhE,IAAIK,IAAA,EAAM;QACR,KAAK9B,KAAA,CAAM,oBAAoBc,EAAA,IAAMW,MAAA,GAAS,QAAQ,OAAO,IAAI,IAAIS,UAAA,CAAW,MAAMJ,IAAA,CAAKK,WAAA,EAAa;MAC1G;IACF;IAKA,IAAIC,MAAA,GAAS;IAEF,WAAAC,QAAA,IAAY,KAAKrC,KAAA,EAAO;MAC3B,MAAAsC,IAAA,GAAO,KAAKtC,KAAA,CAAMqC,QAAQ;MAC1B,MAAAE,UAAA,GAAa,KAAKF,QAAA,CAASG,MAAA;MAEvBJ,MAAA,IAAAG,UAAA;MAEV,MAAME,WAAA,GAAcL,MAAA,GAAS;MAE7B,IAAIK,WAAA,KAAgB,KAAKH,IAAA,KAAS,QAAQA,IAAA,YAAgBJ,UAAA,EAAY;QACpE,MAAMQ,SAAA,GAAY,KAAKD,WAAA;QACjB,MAAAE,OAAA,GAAU,IAAIT,UAAA,CAAWQ,SAAS;QAEnC,KAAA1C,KAAA,CAAMqC,QAAQ,IAAI,CAACC,IAAA,EAAM;UAAEM,KAAA,EAAO;YAAE,OAAOD;UAAQ;QAAA,CAAG;MAC7D;MAEA,IAAIL,IAAA,IAAQ,OAAOA,IAAA,CAAKE,MAAA,KAAW,UAAU;QAC3CJ,MAAA,GAASE,IAAA,CAAKE,MAAA;MAChB;IACF;IAEA,OAAOK,OAAA,CAAQ,KAAK7C,KAAA,EAAmB;MAAE8C,KAAA,EAAO;IAAA,CAAG;EACrD;EAEQlB,cACNC,KAAA,EACAN,KAAA,EAC+B;IAC/B,IACG,OAAOwB,gBAAA,KAAqB,eAAelB,KAAA,YAAiBkB,gBAAA,IAC5D,OAAOC,iBAAA,KAAsB,eAAenB,KAAA,YAAiBmB,iBAAA,IAC7D,OAAOC,eAAA,KAAoB,eAAepB,KAAA,YAAiBoB,eAAA,IAC3D,OAAOC,WAAA,KAAgB,eAAerB,KAAA,YAAiBqB,WAAA,EACxD;MACA,MAAMC,KAAA,GAAQ,OAAOC,IAAA,CAAKC,GAAA,CAAIxB,KAAA,CAAMyB,KAAA,EAAOzB,KAAA,CAAM0B,MAAM;MAEjD,MAAA5B,MAAA,GAAS6B,QAAA,CAASC,aAAA,CAAc,QAAQ;MAC9C9B,MAAA,CAAO2B,KAAA,GAAQzB,KAAA,CAAMyB,KAAA,GAAQF,IAAA,CAAKM,GAAA,CAAI,GAAGP,KAAK;MAC9CxB,MAAA,CAAO4B,MAAA,GAAS1B,KAAA,CAAM0B,MAAA,GAASH,IAAA,CAAKM,GAAA,CAAI,GAAGP,KAAK;MAE1C,MAAAQ,OAAA,GAAUhC,MAAA,CAAOiC,UAAA,CAAW,IAAI;MACtCD,OAAA,oBAAAA,OAAA,CAASE,SAAA,CAAUhC,KAAA,EAAO,GAAG,GAAGF,MAAA,CAAO2B,KAAA,EAAO3B,MAAA,CAAO4B,MAAA;MAErD,IAAIhC,KAAA,KAAU,QAAW;QACjB,MAAAuC,GAAA,GAAMC,QAAA,CAASxC,KAAA,EAAO,EAAE;QAExB,MAAAyC,CAAA,IAAMF,GAAA,IAAO,KAAM,OAAO;QAC1B,MAAAG,CAAA,IAAMH,GAAA,IAAO,IAAK,OAAO;QACzB,MAAAI,CAAA,IAAKJ,GAAA,GAAM,OAAO;QAElB,MAAAK,SAAA,GAAYR,OAAA,oBAAAA,OAAA,CAASS,YAAA,CAAa,GAAG,GAAGzC,MAAA,CAAO2B,KAAA,EAAO3B,MAAA,CAAO4B,MAAA;QACnE,IAAIY,SAAA,EAAW;UACb,MAAME,IAAA,GAAOF,SAAA,oBAAAA,SAAA,CAAWE,IAAA;UAExB,SAASC,CAAA,GAAI,GAAGA,CAAA,GAAID,IAAA,CAAK7B,MAAA,EAAQ8B,CAAA,IAAK,GAAG;YACvCD,IAAA,CAAKC,CAAA,GAAI,CAAC,IAAID,IAAA,CAAKC,CAAA,GAAI,CAAC,IAAIN,CAAA;YAC5BK,IAAA,CAAKC,CAAA,GAAI,CAAC,IAAID,IAAA,CAAKC,CAAA,GAAI,CAAC,IAAIL,CAAA;YAC5BI,IAAA,CAAKC,CAAA,GAAI,CAAC,IAAID,IAAA,CAAKC,CAAA,GAAI,CAAC,IAAIJ,CAAA;UAC9B;UAESP,OAAA,oBAAAA,OAAA,CAAAY,YAAA,CAAaJ,SAAA,EAAW,GAAG;QACtC;MACF;MAEO,OAAAxC,MAAA;IACT;EACF;EAEQtB,YAAA,EAAsB;IACrB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;EAST;EAEQY,qBAAqBuD,YAAA,EAAkC;IACzD,IAAApE,MAAA,GAAS,KAAKC,WAAA;IACRD,MAAA,IAAAoE,YAAA;IACV,OAAOnD,OAAA,CAAQjB,MAAM;EACvB;EAAA;EAGQe,WAAWZ,MAAA,EAAkBK,QAAA,EAA0BF,QAAA,EAAyC;IAChG,MAAA+D,IAAA,GAAO,YAAYlE,MAAA,CAAOO,EAAA;IAChC,MAAM4D,SAAA,GAAY,KAAKC,WAAA,CAAYpE,MAAA,CAAOqE,WAAW;IAErD,IAAIrE,MAAA,CAAOqE,WAAA,CAAYC,WAAA,CAAY,IAAI,GAAG;MAChCC,OAAA,CAAAC,IAAA,CAAK,6DAA6DxE,MAAM;IAClF;IAEA,OAAO,cAAckE,IAAA;AAAA,kDACyB7D,QAAA,CAASE,EAAA;AAAA;AAAA;AAAA,mCAGxB4D,SAAA;AAAA;AAAA,kDAEehE,QAAA,CAASI,EAAA;AAAA;AAAA;EAGzD;EAEQ6D,YAAYK,MAAA,EAAyB;IAC3C,MAAMC,KAAA,GAAQD,MAAA,CAAOE,QAAA;IAEd,YAAK,KAAKC,cAAA,CAAeF,KAAA,EAAO,CAAC,MAAM,KAAKE,cAAA,CAAeF,KAAA,EAAO,CAAC,MAAM,KAAKE,cAAA,CACnFF,KAAA,EACA,OACI,KAAKE,cAAA,CAAeF,KAAA,EAAO,EAAE;EACrC;EAEQE,eAAeF,KAAA,EAAiB7C,MAAA,EAAwB;IAC9D,OAAO,IAAI6C,KAAA,CAAM7C,MAAA,GAAS,CAAC,MAAM6C,KAAA,CAAM7C,MAAA,GAAS,CAAC,MAAM6C,KAAA,CAAM7C,MAAA,GAAS,CAAC,MAAM6C,KAAA,CAAM7C,MAAA,GAAS,CAAC;EAC/F;EAAA;EAGQpB,gBAAgBJ,QAAA,EAAkC;IAClD,MAAAwE,IAAA,GAAO,KAAKC,SAAA,CAAUzE,QAAQ;IAC7B;AAAA;AAAA;AAAA,IAGPwE,IAAA;AAAA;AAAA;EAGF;EAEQC,UAAUzE,QAAA,EAAkC;IAClD,MAAM6D,IAAA,GAAO;IACb,MAAMa,UAAA,GAAa1E,QAAA,CAAS0E,UAAA;IACtB,MAAAC,KAAA,GAAQD,UAAA,CAAWE,QAAA,CAASD,KAAA;IAE3B;AAAA,gBACKd,IAAA;AAAA;AAAA,oCAEoB,KAAKgB,oBAAA,CAAqB7E,QAAQ;AAAA,qCACjC,KAAK8E,sBAAA,CAAuB9E,QAAQ;AAAA,gCACzC,KAAK+E,iBAAA,CAAkBL,UAAA,CAAWM,MAAA,EAAQL,KAAK;AAAA;AAAA;AAAA,8BAGjD,KAAKI,iBAAA,CAAkBL,UAAA,CAAWE,QAAA,EAAUD,KAAK;AAAA,kCAC7C,KAAKM,iBAAA,CAAkBP,UAAA,CAAWQ,EAAA,EAAIP,KAAK;AAAA;AAAA;AAAA;AAAA;AAAA;EAM3E;EAEQE,qBAAqB7E,QAAA,EAAkC;IACvD,MAAA2E,KAAA,GAAQ3E,QAAA,CAASmF,KAAA,KAAU,OAAOnF,QAAA,CAASmF,KAAA,CAAMd,KAAA,CAAMzC,MAAA,GAAS5B,QAAA,CAAS0E,UAAA,CAAWE,QAAA,CAASD,KAAA;IAE5F,OAAAS,KAAA,CAAMT,KAAA,GAAQ,CAAC,EACnBU,IAAA,CAAK,CAAC,EACNC,IAAA,CAAK,IAAI;EACd;EAEQR,uBAAuB9E,QAAA,EAAkC;IAC3D,IAAAA,QAAA,CAASmF,KAAA,KAAU,MAAM;MAE3B,OAAOnF,QAAA,CAASmF,KAAA,CAAMd,KAAA,CAAMiB,IAAA,CAAK,IAAI;IACvC;IAEA,MAAMjB,KAAA,GAAkB;IAClB,MAAAzC,MAAA,GAAS5B,QAAA,CAAS0E,UAAA,CAAWE,QAAA,CAASD,KAAA;IAE5C,SAASjB,CAAA,GAAI,GAAGA,CAAA,GAAI9B,MAAA,EAAQ8B,CAAA,IAAK;MAC/BW,KAAA,CAAMkB,IAAA,CAAK7B,CAAC;IACd;IAEO,OAAAW,KAAA,CAAMiB,IAAA,CAAK,IAAI;EACxB;EAEQP,kBAAkBS,SAAA,EAAiDb,KAAA,EAAuB;IAChG,IAAIa,SAAA,KAAc,QAAW;MAC3BtB,OAAA,CAAQC,IAAA,CAAK,gCAAgC;MAC7C,OAAOiB,KAAA,CAAMT,KAAK,EAAEU,IAAA,CAAK,WAAW,EAAEC,IAAA,CAAK,IAAI;IACjD;IAEA,MAAMjB,KAAA,GAAkB;IACxB,MAAMZ,IAAA,GAAO+B,SAAA,CAAUnB,KAAA;IAEvB,SAASX,CAAA,GAAI,GAAGA,CAAA,GAAID,IAAA,CAAK7B,MAAA,EAAQ8B,CAAA,IAAK,GAAG;MACjCW,KAAA,CAAAkB,IAAA,CACJ,IAAI9B,IAAA,CAAKC,CAAA,GAAI,CAAC,EAAE+B,WAAA,CAAY,KAAKC,SAAS,MAAMjC,IAAA,CAAKC,CAAA,GAAI,CAAC,EAAE+B,WAAA,CAAY,KAAKC,SAAS,MAAMjC,IAAA,CAC1FC,CAAA,GAAI,CACN,EAAE+B,WAAA,CAAY,KAAKC,SAAS;IAEhC;IAEO,OAAArB,KAAA,CAAMiB,IAAA,CAAK,IAAI;EACxB;EAEQL,kBAAkBO,SAAA,EAAiDb,KAAA,EAAuB;IAChG,IAAIa,SAAA,KAAc,QAAW;MAC3BtB,OAAA,CAAQC,IAAA,CAAK,4BAA4B;MACzC,OAAOiB,KAAA,CAAMT,KAAK,EAAEU,IAAA,CAAK,QAAQ,EAAEC,IAAA,CAAK,IAAI;IAC9C;IAEA,MAAMjB,KAAA,GAAkB;IACxB,MAAMZ,IAAA,GAAO+B,SAAA,CAAUnB,KAAA;IAEvB,SAASX,CAAA,GAAI,GAAGA,CAAA,GAAID,IAAA,CAAK7B,MAAA,EAAQ8B,CAAA,IAAK,GAAG;MAEvCW,KAAA,CAAMkB,IAAA,CAAK,IAAI9B,IAAA,CAAKC,CAAA,GAAI,CAAC,EAAE+B,WAAA,CAAY,KAAKC,SAAS,MAAM,IAAIjC,IAAA,CAAKC,CAAA,GAAI,CAAC,EAAE+B,WAAA,CAAY,KAAKC,SAAS,IAAI;IAC3G;IAEO,OAAArB,KAAA,CAAMiB,IAAA,CAAK,IAAI;EACxB;EAAA;EAGQ9E,eAAetB,SAAA,EAA8C;IACnE,MAAMmF,KAAA,GAAkB;IAExB,WAAW/D,IAAA,IAAQpB,SAAA,EAAW;MACtB,MAAAY,QAAA,GAAWZ,SAAA,CAAUoB,IAAI;MAE/B+D,KAAA,CAAMkB,IAAA,CAAK,KAAKI,aAAA,CAAc7F,QAAQ,CAAC;IACzC;IAEO;AAAA;AAAA,EAETuE,KAAA,CAAMiB,IAAA,CAAK,EAAE;AAAA;AAAA;EAGb;EAEQK,cAAc7F,QAAA,EAAyC;IAG7D,MAAM8F,GAAA,GAAM;IACZ,MAAMC,MAAA,GAAS;IACf,MAAMC,QAAA,GAAW;IAEb,IAAAhG,QAAA,CAASiG,GAAA,KAAQ,MAAM;MAClBF,MAAA,CAAAN,IAAA,CACL,GAAGK,GAAA,8DAAiE9F,QAAA,CAASI,EAAA,YAAcJ,QAAA,CAASiG,GAAA,CAAI7F,EAAA;MAI1G,IAAIJ,QAAA,CAASkG,WAAA,IAAelG,QAAA,CAASmG,SAAA,GAAY,GAAK;QACpDJ,MAAA,CAAON,IAAA,CAAK,GAAGK,GAAA,uDAA0D9F,QAAA,CAASI,EAAA,YAAcJ,QAAA,CAASiG,GAAA,CAAI7F,EAAA,qBAAuB;MACtI;MAGI,IAAAJ,QAAA,CAASmG,SAAA,GAAY,MAAM;QAC7BJ,MAAA,CAAON,IAAA,CAAK,GAAGK,GAAA,mCAAsC9F,QAAA,CAASmG,SAAA,EAAW;MAEnE,WAAAnG,QAAA,CAASkG,WAAA,IAAelG,QAAA,CAASmG,SAAA,GAAY,GAAK;QACjDJ,MAAA,CAAAN,IAAA,CAAK,GAAGK,GAAA,sCAAyC;MAC1D;MAESE,QAAA,CAAAP,IAAA,CAAK,KAAKW,YAAA,CAAapG,QAAA,EAAUA,QAAA,CAASiG,GAAA,EAAK,WAAWjG,QAAA,CAASa,KAAK,CAAC;IAAA,OAC7E;MACLkF,MAAA,CAAON,IAAA,CAAK,GAAGK,GAAA,iCAAoC,KAAKO,UAAA,CAAWrG,QAAA,CAASa,KAAK,GAAG;IACtF;IAEI,IAAAb,QAAA,CAASsG,WAAA,KAAgB,MAAM;MAC1BP,MAAA,CAAAN,IAAA,CACL,GAAGK,GAAA,+DAAkE9F,QAAA,CAASI,EAAA,YAAcJ,QAAA,CAASsG,WAAA,CAAYlG,EAAA;MAGnH4F,QAAA,CAASP,IAAA,CAAK,KAAKW,YAAA,CAAapG,QAAA,EAAUA,QAAA,CAASsG,WAAA,EAAa,UAAU,CAAC;IAClE,WAAAtG,QAAA,CAASuG,QAAA,CAASC,MAAA,KAAW,GAAG;MACzCT,MAAA,CAAON,IAAA,CAAK,GAAGK,GAAA,kCAAqC,KAAKO,UAAA,CAAWrG,QAAA,CAASuG,QAAQ,GAAG;IAC1F;IAEI,IAAAvG,QAAA,CAASyG,SAAA,KAAc,MAAM;MACxBV,MAAA,CAAAN,IAAA,CACL,GAAGK,GAAA,yDAA4D9F,QAAA,CAASI,EAAA,YAAcJ,QAAA,CAASyG,SAAA,CAAUrG,EAAA;MAG3G4F,QAAA,CAASP,IAAA,CAAK,KAAKW,YAAA,CAAapG,QAAA,EAAUA,QAAA,CAASyG,SAAA,EAAW,QAAQ,CAAC;IACzE;IAEI,IAAAzG,QAAA,CAAS0G,KAAA,KAAU,MAAM;MACpBX,MAAA,CAAAN,IAAA,CACL,GAAGK,GAAA,yDAA4D9F,QAAA,CAASI,EAAA,YAAcJ,QAAA,CAAS0G,KAAA,CAAMtG,EAAA;MAGvG4F,QAAA,CAASP,IAAA,CAAK,KAAKW,YAAA,CAAapG,QAAA,EAAUA,QAAA,CAAS0G,KAAA,EAAO,WAAW,CAAC;IACxE;IAEA,IAAI1G,QAAA,CAAS2G,YAAA,KAAiB,QAAQ3G,QAAA,CAAS4G,SAAA,KAAc,GAAG;MACvDb,MAAA,CAAAN,IAAA,CACL,GAAGK,GAAA,yDAA4D9F,QAAA,CAASI,EAAA,YAAcJ,QAAA,CAAS2G,YAAA,CAAavG,EAAA;MAG9G4F,QAAA,CAASP,IAAA,CAAK,KAAKW,YAAA,CAAapG,QAAA,EAAUA,QAAA,CAAS2G,YAAA,EAAc,WAAW,CAAC;IAAA,OACxE;MACLZ,MAAA,CAAON,IAAA,CAAK,GAAGK,GAAA,4BAA+B9F,QAAA,CAAS4G,SAAA,EAAW;IACpE;IAEA,IAAI5G,QAAA,CAAS6G,YAAA,KAAiB,QAAQ7G,QAAA,CAAS8G,SAAA,KAAc,GAAG;MACvDf,MAAA,CAAAN,IAAA,CACL,GAAGK,GAAA,wDAA2D9F,QAAA,CAASI,EAAA,YAAcJ,QAAA,CAAS6G,YAAA,CAAazG,EAAA;MAG7G4F,QAAA,CAASP,IAAA,CAAK,KAAKW,YAAA,CAAapG,QAAA,EAAUA,QAAA,CAAS6G,YAAA,EAAc,UAAU,CAAC;IAAA,OACvE;MACLd,MAAA,CAAON,IAAA,CAAK,GAAGK,GAAA,2BAA8B9F,QAAA,CAAS8G,SAAA,EAAW;IACnE;IAEAf,MAAA,CAAON,IAAA,CAAK,GAAGK,GAAA,0BAA6B9F,QAAA,CAAS+G,OAAA,EAAS;IAE9D,IAAI/G,QAAA,YAAoBgH,oBAAA,EAAsB;MAC5CjB,MAAA,CAAON,IAAA,CAAK,GAAGK,GAAA,4BAA+B9F,QAAA,CAASiH,SAAA,EAAW;MAClElB,MAAA,CAAON,IAAA,CAAK,GAAGK,GAAA,qCAAwC9F,QAAA,CAASkH,kBAAA,EAAoB;MACpFnB,MAAA,CAAON,IAAA,CAAK,GAAGK,GAAA,sBAAyB9F,QAAA,CAASmH,GAAA,EAAK;IACxD;IAEO;AAAA,6BACkBnH,QAAA,CAASI,EAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAKpC2F,MAAA,CAAOP,IAAA,CAAK,IAAI;AAAA;AAAA;AAAA;AAAA,+DAI6CxF,QAAA,CAASI,EAAA;AAAA;AAAA;AAAA;AAAA;AAAA,kEAKNJ,QAAA,CAASI,EAAA;AAAA;AAAA;AAAA;AAAA,EAIzE4F,QAAA,CAASR,IAAA,CAAK,IAAI;AAAA;AAAA;EAGlB;EAEQY,aAAapG,QAAA,EAAiCY,OAAA,EAAkBwG,OAAA,EAAiBvG,KAAA,EAAuB;IAC9G,MAAMT,EAAA,GAAKQ,OAAA,CAAQR,EAAA,IAAMS,KAAA,GAAQ,MAAMA,KAAA,CAAMwG,YAAA,KAAiB;IACxD,MAAAtG,MAAA,GAASH,OAAA,CAAQI,MAAA,KAAW;IAE7B,KAAA3B,QAAA,CAASe,EAAE,IAAIQ,OAAA;IAGb;AAAA,gCACqBwG,OAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,4DAO4BpH,QAAA,CAASI,EAAA;AAAA,kCACnC,KAAKkH,YAAA,CAAa1G,OAAA,CAAQ2G,MAAM;AAAA,wCAC1B,KAAKD,YAAA,CAAa1G,OAAA,CAAQc,MAAM;AAAA;AAAA;AAAA,4BAG5Cd,OAAA,CAAQR,EAAA,IAAMgH,OAAA;AAAA;AAAA;AAAA,kDAGQhH,EAAA,IAAMW,MAAA,GAAS,QAAQ;AAAA,4DACbf,QAAA,CAASI,EAAA,gBAAkBgH,OAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,YAO3EpH,QAAA,CAASkG,WAAA,IAAelG,QAAA,CAASmG,SAAA,GAAY,IAAM,oBAAoB;AAAA;EAEjF;EAEQE,WAAWxF,KAAA,EAAsB;IACvC,OAAO,IAAIA,KAAA,CAAMyC,CAAA,KAAMzC,KAAA,CAAM0C,CAAA,KAAM1C,KAAA,CAAM2C,CAAA;EAC3C;EAEQ8D,aAAaE,MAAA,EAAyB;IACrC,WAAIA,MAAA,CAAOC,CAAA,KAAMD,MAAA,CAAOE,CAAA;EACjC;AACF", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}