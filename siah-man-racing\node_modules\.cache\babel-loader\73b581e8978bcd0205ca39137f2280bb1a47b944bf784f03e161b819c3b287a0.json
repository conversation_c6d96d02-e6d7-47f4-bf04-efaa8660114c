{"ast": null, "code": "const PixelShader = {\n  uniforms: {\n    tDiffuse: {\n      value: null\n    },\n    resolution: {\n      value: null\n    },\n    pixelSize: {\n      value: 1\n    }\n  },\n  vertexShader: (/* glsl */\n  `\n    varying highp vec2 vUv;\n\n    void main() {\n\n      vUv = uv;\n      gl_Position = projectionMatrix * modelViewMatrix * vec4( position, 1.0 );\n\n    }\n  `),\n  fragmentShader: (/* glsl */\n  `\n    uniform sampler2D tDiffuse;\n    uniform float pixelSize;\n    uniform vec2 resolution;\n\n    varying highp vec2 vUv;\n\n    void main(){\n\n      vec2 dxy = pixelSize / resolution;\n      vec2 coord = dxy * floor( vUv / dxy );\n      gl_FragColor = texture2D(tDiffuse, coord);\n\n    }\n  `)\n};\nexport { PixelShader };", "map": {"version": 3, "names": ["PixelShader", "uniforms", "tDiffuse", "value", "resolution", "pixelSize", "vertexShader", "fragmentShader"], "sources": ["C:\\Users\\<USER>\\Documents\\Apps_Creations\\SiahMan_Racing4Speed_WebGame\\siah-man-racing\\node_modules\\src\\shaders\\PixelShader.ts"], "sourcesContent": ["/**\n * Pixelation shader\n */\n\nexport const PixelShader = {\n  uniforms: {\n    tDiffuse: { value: null },\n    resolution: { value: null },\n    pixelSize: { value: 1 },\n  },\n\n  vertexShader: /* glsl */ `\n    varying highp vec2 vUv;\n\n    void main() {\n\n      vUv = uv;\n      gl_Position = projectionMatrix * modelViewMatrix * vec4( position, 1.0 );\n\n    }\n  `,\n\n  fragmentShader: /* glsl */ `\n    uniform sampler2D tDiffuse;\n    uniform float pixelSize;\n    uniform vec2 resolution;\n\n    varying highp vec2 vUv;\n\n    void main(){\n\n      vec2 dxy = pixelSize / resolution;\n      vec2 coord = dxy * floor( vUv / dxy );\n      gl_FragColor = texture2D(tDiffuse, coord);\n\n    }\n  `,\n}\n"], "mappings": "AAIO,MAAMA,WAAA,GAAc;EACzBC,QAAA,EAAU;IACRC,QAAA,EAAU;MAAEC,KAAA,EAAO;IAAK;IACxBC,UAAA,EAAY;MAAED,KAAA,EAAO;IAAK;IAC1BE,SAAA,EAAW;MAAEF,KAAA,EAAO;IAAE;EACxB;EAEAG,YAAA;EAAyB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;EAWzBC,cAAA;EAA2B;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAe7B", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}