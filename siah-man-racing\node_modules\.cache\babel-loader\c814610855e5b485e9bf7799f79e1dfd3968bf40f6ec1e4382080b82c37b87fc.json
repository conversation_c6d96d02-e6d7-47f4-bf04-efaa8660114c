{"ast": null, "code": "import { Vector3, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, VectorKeyframeTrack, QuaternionKeyframeTrack, AnimationClip, SkeletonHelper } from \"three\";\nfunction retarget(target, source, options = {}) {\n  const pos = new Vector3(),\n    quat = new Quaternion(),\n    scale = new Vector3(),\n    bindBoneMatrix = new Matrix4(),\n    relativeMatrix = new Matrix4(),\n    globalMatrix = new Matrix4();\n  options.preserveMatrix = options.preserveMatrix !== void 0 ? options.preserveMatrix : true;\n  options.preservePosition = options.preservePosition !== void 0 ? options.preservePosition : true;\n  options.preserveHipPosition = options.preserveHipPosition !== void 0 ? options.preserveHipPosition : false;\n  options.useTargetMatrix = options.useTargetMatrix !== void 0 ? options.useTargetMatrix : false;\n  options.hip = options.hip !== void 0 ? options.hip : \"hip\";\n  options.names = options.names || {};\n  const sourceBones = source.isObject3D ? source.skeleton.bones : getBones(source),\n    bones = target.isObject3D ? target.skeleton.bones : getBones(target);\n  let bindBones, bone, name, boneTo, bonesPosition;\n  if (target.isObject3D) {\n    target.skeleton.pose();\n  } else {\n    options.useTargetMatrix = true;\n    options.preserveMatrix = false;\n  }\n  if (options.preservePosition) {\n    bonesPosition = [];\n    for (let i = 0; i < bones.length; i++) {\n      bonesPosition.push(bones[i].position.clone());\n    }\n  }\n  if (options.preserveMatrix) {\n    target.updateMatrixWorld();\n    target.matrixWorld.identity();\n    for (let i = 0; i < target.children.length; ++i) {\n      target.children[i].updateMatrixWorld(true);\n    }\n  }\n  if (options.offsets) {\n    bindBones = [];\n    for (let i = 0; i < bones.length; ++i) {\n      bone = bones[i];\n      name = options.names[bone.name] || bone.name;\n      if (options.offsets[name]) {\n        bone.matrix.multiply(options.offsets[name]);\n        bone.matrix.decompose(bone.position, bone.quaternion, bone.scale);\n        bone.updateMatrixWorld();\n      }\n      bindBones.push(bone.matrixWorld.clone());\n    }\n  }\n  for (let i = 0; i < bones.length; ++i) {\n    bone = bones[i];\n    name = options.names[bone.name] || bone.name;\n    boneTo = getBoneByName(name, sourceBones);\n    globalMatrix.copy(bone.matrixWorld);\n    if (boneTo) {\n      boneTo.updateMatrixWorld();\n      if (options.useTargetMatrix) {\n        relativeMatrix.copy(boneTo.matrixWorld);\n      } else {\n        relativeMatrix.copy(target.matrixWorld).invert();\n        relativeMatrix.multiply(boneTo.matrixWorld);\n      }\n      scale.setFromMatrixScale(relativeMatrix);\n      relativeMatrix.scale(scale.set(1 / scale.x, 1 / scale.y, 1 / scale.z));\n      globalMatrix.makeRotationFromQuaternion(quat.setFromRotationMatrix(relativeMatrix));\n      if (target.isObject3D) {\n        const boneIndex = bones.indexOf(bone),\n          wBindMatrix = bindBones ? bindBones[boneIndex] : bindBoneMatrix.copy(target.skeleton.boneInverses[boneIndex]).invert();\n        globalMatrix.multiply(wBindMatrix);\n      }\n      globalMatrix.copyPosition(relativeMatrix);\n    }\n    if (bone.parent && bone.parent.isBone) {\n      bone.matrix.copy(bone.parent.matrixWorld).invert();\n      bone.matrix.multiply(globalMatrix);\n    } else {\n      bone.matrix.copy(globalMatrix);\n    }\n    if (options.preserveHipPosition && name === options.hip) {\n      bone.matrix.setPosition(pos.set(0, bone.position.y, 0));\n    }\n    bone.matrix.decompose(bone.position, bone.quaternion, bone.scale);\n    bone.updateMatrixWorld();\n  }\n  if (options.preservePosition) {\n    for (let i = 0; i < bones.length; ++i) {\n      bone = bones[i];\n      name = options.names[bone.name] || bone.name;\n      if (name !== options.hip) {\n        bone.position.copy(bonesPosition[i]);\n      }\n    }\n  }\n  if (options.preserveMatrix) {\n    target.updateMatrixWorld(true);\n  }\n}\nfunction retargetClip(target, source, clip, options = {}) {\n  options.useFirstFramePosition = options.useFirstFramePosition !== void 0 ? options.useFirstFramePosition : false;\n  options.fps = options.fps !== void 0 ? options.fps : 30;\n  options.names = options.names || [];\n  if (!source.isObject3D) {\n    source = getHelperFromSkeleton(source);\n  }\n  const numFrames = Math.round(clip.duration * (options.fps / 1e3) * 1e3),\n    delta = 1 / options.fps,\n    convertedTracks = [],\n    mixer = new AnimationMixer(source),\n    bones = getBones(target.skeleton),\n    boneDatas = [];\n  let positionOffset, bone, boneTo, boneData, name;\n  mixer.clipAction(clip).play();\n  mixer.update(0);\n  source.updateMatrixWorld();\n  for (let i = 0; i < numFrames; ++i) {\n    const time = i * delta;\n    retarget(target, source, options);\n    for (let j = 0; j < bones.length; ++j) {\n      name = options.names[bones[j].name] || bones[j].name;\n      boneTo = getBoneByName(name, source.skeleton);\n      if (boneTo) {\n        bone = bones[j];\n        boneData = boneDatas[j] = boneDatas[j] || {\n          bone\n        };\n        if (options.hip === name) {\n          if (!boneData.pos) {\n            boneData.pos = {\n              times: new Float32Array(numFrames),\n              values: new Float32Array(numFrames * 3)\n            };\n          }\n          if (options.useFirstFramePosition) {\n            if (i === 0) {\n              positionOffset = bone.position.clone();\n            }\n            bone.position.sub(positionOffset);\n          }\n          boneData.pos.times[i] = time;\n          bone.position.toArray(boneData.pos.values, i * 3);\n        }\n        if (!boneData.quat) {\n          boneData.quat = {\n            times: new Float32Array(numFrames),\n            values: new Float32Array(numFrames * 4)\n          };\n        }\n        boneData.quat.times[i] = time;\n        bone.quaternion.toArray(boneData.quat.values, i * 4);\n      }\n    }\n    mixer.update(delta);\n    source.updateMatrixWorld();\n  }\n  for (let i = 0; i < boneDatas.length; ++i) {\n    boneData = boneDatas[i];\n    if (boneData) {\n      if (boneData.pos) {\n        convertedTracks.push(new VectorKeyframeTrack(\".bones[\" + boneData.bone.name + \"].position\", boneData.pos.times, boneData.pos.values));\n      }\n      convertedTracks.push(new QuaternionKeyframeTrack(\".bones[\" + boneData.bone.name + \"].quaternion\", boneData.quat.times, boneData.quat.values));\n    }\n  }\n  mixer.uncacheAction(clip);\n  return new AnimationClip(clip.name, -1, convertedTracks);\n}\nfunction clone(source) {\n  const sourceLookup = /* @__PURE__ */new Map();\n  const cloneLookup = /* @__PURE__ */new Map();\n  const clone2 = source.clone();\n  parallelTraverse(source, clone2, function (sourceNode, clonedNode) {\n    sourceLookup.set(clonedNode, sourceNode);\n    cloneLookup.set(sourceNode, clonedNode);\n  });\n  clone2.traverse(function (node) {\n    if (!node.isSkinnedMesh) return;\n    const clonedMesh = node;\n    const sourceMesh = sourceLookup.get(node);\n    const sourceBones = sourceMesh.skeleton.bones;\n    clonedMesh.skeleton = sourceMesh.skeleton.clone();\n    clonedMesh.bindMatrix.copy(sourceMesh.bindMatrix);\n    clonedMesh.skeleton.bones = sourceBones.map(function (bone) {\n      return cloneLookup.get(bone);\n    });\n    clonedMesh.bind(clonedMesh.skeleton, clonedMesh.bindMatrix);\n  });\n  return clone2;\n}\nfunction getBoneByName(name, skeleton) {\n  for (let i = 0, bones = getBones(skeleton); i < bones.length; i++) {\n    if (name === bones[i].name) return bones[i];\n  }\n}\nfunction getBones(skeleton) {\n  return Array.isArray(skeleton) ? skeleton : skeleton.bones;\n}\nfunction getHelperFromSkeleton(skeleton) {\n  const source = new SkeletonHelper(skeleton.bones[0]);\n  source.skeleton = skeleton;\n  return source;\n}\nfunction parallelTraverse(a, b, callback) {\n  callback(a, b);\n  for (let i = 0; i < a.children.length; i++) {\n    parallelTraverse(a.children[i], b.children[i], callback);\n  }\n}\nconst SkeletonUtils = {\n  retarget,\n  retargetClip,\n  clone\n};\nexport { SkeletonUtils };", "map": {"version": 3, "names": ["retarget", "target", "source", "options", "pos", "Vector3", "quat", "Quaternion", "scale", "bindBoneMatrix", "Matrix4", "relativeMatrix", "globalMatrix", "preserveMatrix", "preservePosition", "preserveHipPosition", "useTargetMatrix", "hip", "names", "sourceBones", "isObject3D", "skeleton", "bones", "getBones", "bindBones", "bone", "name", "boneTo", "bonesPosition", "pose", "i", "length", "push", "position", "clone", "updateMatrixWorld", "matrixWorld", "identity", "children", "offsets", "matrix", "multiply", "decompose", "quaternion", "getBoneByName", "copy", "invert", "setFromMatrixScale", "set", "x", "y", "z", "makeRotationFromQuaternion", "setFromRotationMatrix", "boneIndex", "indexOf", "wBindMatrix", "boneInverses", "copyPosition", "parent", "isBone", "setPosition", "retargetClip", "clip", "useFirstFramePosition", "fps", "getHelperFromSkeleton", "numFrames", "Math", "round", "duration", "delta", "convertedTracks", "mixer", "AnimationMixer", "boneDatas", "positionOffset", "boneData", "clipAction", "play", "update", "time", "j", "times", "Float32Array", "values", "sub", "toArray", "VectorKeyframeTrack", "QuaternionKeyframeTrack", "uncacheAction", "AnimationClip", "sourceLookup", "Map", "cloneLookup", "clone2", "parallelTraverse", "sourceNode", "clonedNode", "traverse", "node", "isSkinnedMesh", "cloned<PERSON><PERSON>", "sourceMesh", "get", "bindMatrix", "map", "bind", "Array", "isArray", "SkeletonHelper", "a", "b", "callback", "SkeletonUtils"], "sources": ["C:\\Users\\<USER>\\Documents\\Apps_Creations\\SiahMan_Racing4Speed_WebGame\\siah-man-racing\\node_modules\\src\\utils\\SkeletonUtils.js"], "sourcesContent": ["import {\n  <PERSON><PERSON><PERSON>,\n  <PERSON><PERSON><PERSON><PERSON>,\n  <PERSON><PERSON>,\n  <PERSON><PERSON><PERSON><PERSON>,\n  QuaternionKeyframeTrack,\n  SkeletonHelper,\n  Vector3,\n  VectorKeyframeTrack,\n} from 'three'\n\nfunction retarget(target, source, options = {}) {\n  const pos = new Vector3(),\n    quat = new Quaternion(),\n    scale = new Vector3(),\n    bindBoneMatrix = new Matrix4(),\n    relativeMatrix = new Matrix4(),\n    globalMatrix = new Matrix4()\n\n  options.preserveMatrix = options.preserveMatrix !== undefined ? options.preserveMatrix : true\n  options.preservePosition = options.preservePosition !== undefined ? options.preservePosition : true\n  options.preserveHipPosition = options.preserveHipPosition !== undefined ? options.preserveHipPosition : false\n  options.useTargetMatrix = options.useTargetMatrix !== undefined ? options.useTargetMatrix : false\n  options.hip = options.hip !== undefined ? options.hip : 'hip'\n  options.names = options.names || {}\n\n  const sourceBones = source.isObject3D ? source.skeleton.bones : getBones(source),\n    bones = target.isObject3D ? target.skeleton.bones : getBones(target)\n\n  let bindBones, bone, name, boneTo, bonesPosition\n\n  // reset bones\n\n  if (target.isObject3D) {\n    target.skeleton.pose()\n  } else {\n    options.useTargetMatrix = true\n    options.preserveMatrix = false\n  }\n\n  if (options.preservePosition) {\n    bonesPosition = []\n\n    for (let i = 0; i < bones.length; i++) {\n      bonesPosition.push(bones[i].position.clone())\n    }\n  }\n\n  if (options.preserveMatrix) {\n    // reset matrix\n\n    target.updateMatrixWorld()\n\n    target.matrixWorld.identity()\n\n    // reset children matrix\n\n    for (let i = 0; i < target.children.length; ++i) {\n      target.children[i].updateMatrixWorld(true)\n    }\n  }\n\n  if (options.offsets) {\n    bindBones = []\n\n    for (let i = 0; i < bones.length; ++i) {\n      bone = bones[i]\n      name = options.names[bone.name] || bone.name\n\n      if (options.offsets[name]) {\n        bone.matrix.multiply(options.offsets[name])\n\n        bone.matrix.decompose(bone.position, bone.quaternion, bone.scale)\n\n        bone.updateMatrixWorld()\n      }\n\n      bindBones.push(bone.matrixWorld.clone())\n    }\n  }\n\n  for (let i = 0; i < bones.length; ++i) {\n    bone = bones[i]\n    name = options.names[bone.name] || bone.name\n\n    boneTo = getBoneByName(name, sourceBones)\n\n    globalMatrix.copy(bone.matrixWorld)\n\n    if (boneTo) {\n      boneTo.updateMatrixWorld()\n\n      if (options.useTargetMatrix) {\n        relativeMatrix.copy(boneTo.matrixWorld)\n      } else {\n        relativeMatrix.copy(target.matrixWorld).invert()\n        relativeMatrix.multiply(boneTo.matrixWorld)\n      }\n\n      // ignore scale to extract rotation\n\n      scale.setFromMatrixScale(relativeMatrix)\n      relativeMatrix.scale(scale.set(1 / scale.x, 1 / scale.y, 1 / scale.z))\n\n      // apply to global matrix\n\n      globalMatrix.makeRotationFromQuaternion(quat.setFromRotationMatrix(relativeMatrix))\n\n      if (target.isObject3D) {\n        const boneIndex = bones.indexOf(bone),\n          wBindMatrix = bindBones\n            ? bindBones[boneIndex]\n            : bindBoneMatrix.copy(target.skeleton.boneInverses[boneIndex]).invert()\n\n        globalMatrix.multiply(wBindMatrix)\n      }\n\n      globalMatrix.copyPosition(relativeMatrix)\n    }\n\n    if (bone.parent && bone.parent.isBone) {\n      bone.matrix.copy(bone.parent.matrixWorld).invert()\n      bone.matrix.multiply(globalMatrix)\n    } else {\n      bone.matrix.copy(globalMatrix)\n    }\n\n    if (options.preserveHipPosition && name === options.hip) {\n      bone.matrix.setPosition(pos.set(0, bone.position.y, 0))\n    }\n\n    bone.matrix.decompose(bone.position, bone.quaternion, bone.scale)\n\n    bone.updateMatrixWorld()\n  }\n\n  if (options.preservePosition) {\n    for (let i = 0; i < bones.length; ++i) {\n      bone = bones[i]\n      name = options.names[bone.name] || bone.name\n\n      if (name !== options.hip) {\n        bone.position.copy(bonesPosition[i])\n      }\n    }\n  }\n\n  if (options.preserveMatrix) {\n    // restore matrix\n\n    target.updateMatrixWorld(true)\n  }\n}\n\nfunction retargetClip(target, source, clip, options = {}) {\n  options.useFirstFramePosition = options.useFirstFramePosition !== undefined ? options.useFirstFramePosition : false\n  options.fps = options.fps !== undefined ? options.fps : 30\n  options.names = options.names || []\n\n  if (!source.isObject3D) {\n    source = getHelperFromSkeleton(source)\n  }\n\n  const numFrames = Math.round(clip.duration * (options.fps / 1000) * 1000),\n    delta = 1 / options.fps,\n    convertedTracks = [],\n    mixer = new AnimationMixer(source),\n    bones = getBones(target.skeleton),\n    boneDatas = []\n  let positionOffset, bone, boneTo, boneData, name\n\n  mixer.clipAction(clip).play()\n  mixer.update(0)\n\n  source.updateMatrixWorld()\n\n  for (let i = 0; i < numFrames; ++i) {\n    const time = i * delta\n\n    retarget(target, source, options)\n\n    for (let j = 0; j < bones.length; ++j) {\n      name = options.names[bones[j].name] || bones[j].name\n\n      boneTo = getBoneByName(name, source.skeleton)\n\n      if (boneTo) {\n        bone = bones[j]\n        boneData = boneDatas[j] = boneDatas[j] || { bone: bone }\n\n        if (options.hip === name) {\n          if (!boneData.pos) {\n            boneData.pos = {\n              times: new Float32Array(numFrames),\n              values: new Float32Array(numFrames * 3),\n            }\n          }\n\n          if (options.useFirstFramePosition) {\n            if (i === 0) {\n              positionOffset = bone.position.clone()\n            }\n\n            bone.position.sub(positionOffset)\n          }\n\n          boneData.pos.times[i] = time\n\n          bone.position.toArray(boneData.pos.values, i * 3)\n        }\n\n        if (!boneData.quat) {\n          boneData.quat = {\n            times: new Float32Array(numFrames),\n            values: new Float32Array(numFrames * 4),\n          }\n        }\n\n        boneData.quat.times[i] = time\n\n        bone.quaternion.toArray(boneData.quat.values, i * 4)\n      }\n    }\n\n    mixer.update(delta)\n\n    source.updateMatrixWorld()\n  }\n\n  for (let i = 0; i < boneDatas.length; ++i) {\n    boneData = boneDatas[i]\n\n    if (boneData) {\n      if (boneData.pos) {\n        convertedTracks.push(\n          new VectorKeyframeTrack(\n            '.bones[' + boneData.bone.name + '].position',\n            boneData.pos.times,\n            boneData.pos.values,\n          ),\n        )\n      }\n\n      convertedTracks.push(\n        new QuaternionKeyframeTrack(\n          '.bones[' + boneData.bone.name + '].quaternion',\n          boneData.quat.times,\n          boneData.quat.values,\n        ),\n      )\n    }\n  }\n\n  mixer.uncacheAction(clip)\n\n  return new AnimationClip(clip.name, -1, convertedTracks)\n}\n\nfunction clone(source) {\n  const sourceLookup = new Map()\n  const cloneLookup = new Map()\n\n  const clone = source.clone()\n\n  parallelTraverse(source, clone, function (sourceNode, clonedNode) {\n    sourceLookup.set(clonedNode, sourceNode)\n    cloneLookup.set(sourceNode, clonedNode)\n  })\n\n  clone.traverse(function (node) {\n    if (!node.isSkinnedMesh) return\n\n    const clonedMesh = node\n    const sourceMesh = sourceLookup.get(node)\n    const sourceBones = sourceMesh.skeleton.bones\n\n    clonedMesh.skeleton = sourceMesh.skeleton.clone()\n    clonedMesh.bindMatrix.copy(sourceMesh.bindMatrix)\n\n    clonedMesh.skeleton.bones = sourceBones.map(function (bone) {\n      return cloneLookup.get(bone)\n    })\n\n    clonedMesh.bind(clonedMesh.skeleton, clonedMesh.bindMatrix)\n  })\n\n  return clone\n}\n\n// internal helper\n\nfunction getBoneByName(name, skeleton) {\n  for (let i = 0, bones = getBones(skeleton); i < bones.length; i++) {\n    if (name === bones[i].name) return bones[i]\n  }\n}\n\nfunction getBones(skeleton) {\n  return Array.isArray(skeleton) ? skeleton : skeleton.bones\n}\n\nfunction getHelperFromSkeleton(skeleton) {\n  const source = new SkeletonHelper(skeleton.bones[0])\n  source.skeleton = skeleton\n\n  return source\n}\n\nfunction parallelTraverse(a, b, callback) {\n  callback(a, b)\n\n  for (let i = 0; i < a.children.length; i++) {\n    parallelTraverse(a.children[i], b.children[i], callback)\n  }\n}\n\nexport const SkeletonUtils = { retarget, retargetClip, clone }\n"], "mappings": ";AAWA,SAASA,SAASC,MAAA,EAAQC,MAAA,EAAQC,OAAA,GAAU,IAAI;EAC9C,MAAMC,GAAA,GAAM,IAAIC,OAAA,CAAS;IACvBC,IAAA,GAAO,IAAIC,UAAA,CAAY;IACvBC,KAAA,GAAQ,IAAIH,OAAA,CAAS;IACrBI,cAAA,GAAiB,IAAIC,OAAA,CAAS;IAC9BC,cAAA,GAAiB,IAAID,OAAA,CAAS;IAC9BE,YAAA,GAAe,IAAIF,OAAA,CAAS;EAE9BP,OAAA,CAAQU,cAAA,GAAiBV,OAAA,CAAQU,cAAA,KAAmB,SAAYV,OAAA,CAAQU,cAAA,GAAiB;EACzFV,OAAA,CAAQW,gBAAA,GAAmBX,OAAA,CAAQW,gBAAA,KAAqB,SAAYX,OAAA,CAAQW,gBAAA,GAAmB;EAC/FX,OAAA,CAAQY,mBAAA,GAAsBZ,OAAA,CAAQY,mBAAA,KAAwB,SAAYZ,OAAA,CAAQY,mBAAA,GAAsB;EACxGZ,OAAA,CAAQa,eAAA,GAAkBb,OAAA,CAAQa,eAAA,KAAoB,SAAYb,OAAA,CAAQa,eAAA,GAAkB;EAC5Fb,OAAA,CAAQc,GAAA,GAAMd,OAAA,CAAQc,GAAA,KAAQ,SAAYd,OAAA,CAAQc,GAAA,GAAM;EACxDd,OAAA,CAAQe,KAAA,GAAQf,OAAA,CAAQe,KAAA,IAAS,CAAE;EAEnC,MAAMC,WAAA,GAAcjB,MAAA,CAAOkB,UAAA,GAAalB,MAAA,CAAOmB,QAAA,CAASC,KAAA,GAAQC,QAAA,CAASrB,MAAM;IAC7EoB,KAAA,GAAQrB,MAAA,CAAOmB,UAAA,GAAanB,MAAA,CAAOoB,QAAA,CAASC,KAAA,GAAQC,QAAA,CAAStB,MAAM;EAErE,IAAIuB,SAAA,EAAWC,IAAA,EAAMC,IAAA,EAAMC,MAAA,EAAQC,aAAA;EAInC,IAAI3B,MAAA,CAAOmB,UAAA,EAAY;IACrBnB,MAAA,CAAOoB,QAAA,CAASQ,IAAA,CAAM;EAC1B,OAAS;IACL1B,OAAA,CAAQa,eAAA,GAAkB;IAC1Bb,OAAA,CAAQU,cAAA,GAAiB;EAC1B;EAED,IAAIV,OAAA,CAAQW,gBAAA,EAAkB;IAC5Bc,aAAA,GAAgB,EAAE;IAElB,SAASE,CAAA,GAAI,GAAGA,CAAA,GAAIR,KAAA,CAAMS,MAAA,EAAQD,CAAA,IAAK;MACrCF,aAAA,CAAcI,IAAA,CAAKV,KAAA,CAAMQ,CAAC,EAAEG,QAAA,CAASC,KAAA,EAAO;IAC7C;EACF;EAED,IAAI/B,OAAA,CAAQU,cAAA,EAAgB;IAG1BZ,MAAA,CAAOkC,iBAAA,CAAmB;IAE1BlC,MAAA,CAAOmC,WAAA,CAAYC,QAAA,CAAU;IAI7B,SAASP,CAAA,GAAI,GAAGA,CAAA,GAAI7B,MAAA,CAAOqC,QAAA,CAASP,MAAA,EAAQ,EAAED,CAAA,EAAG;MAC/C7B,MAAA,CAAOqC,QAAA,CAASR,CAAC,EAAEK,iBAAA,CAAkB,IAAI;IAC1C;EACF;EAED,IAAIhC,OAAA,CAAQoC,OAAA,EAAS;IACnBf,SAAA,GAAY,EAAE;IAEd,SAASM,CAAA,GAAI,GAAGA,CAAA,GAAIR,KAAA,CAAMS,MAAA,EAAQ,EAAED,CAAA,EAAG;MACrCL,IAAA,GAAOH,KAAA,CAAMQ,CAAC;MACdJ,IAAA,GAAOvB,OAAA,CAAQe,KAAA,CAAMO,IAAA,CAAKC,IAAI,KAAKD,IAAA,CAAKC,IAAA;MAExC,IAAIvB,OAAA,CAAQoC,OAAA,CAAQb,IAAI,GAAG;QACzBD,IAAA,CAAKe,MAAA,CAAOC,QAAA,CAAStC,OAAA,CAAQoC,OAAA,CAAQb,IAAI,CAAC;QAE1CD,IAAA,CAAKe,MAAA,CAAOE,SAAA,CAAUjB,IAAA,CAAKQ,QAAA,EAAUR,IAAA,CAAKkB,UAAA,EAAYlB,IAAA,CAAKjB,KAAK;QAEhEiB,IAAA,CAAKU,iBAAA,CAAmB;MACzB;MAEDX,SAAA,CAAUQ,IAAA,CAAKP,IAAA,CAAKW,WAAA,CAAYF,KAAA,CAAK,CAAE;IACxC;EACF;EAED,SAASJ,CAAA,GAAI,GAAGA,CAAA,GAAIR,KAAA,CAAMS,MAAA,EAAQ,EAAED,CAAA,EAAG;IACrCL,IAAA,GAAOH,KAAA,CAAMQ,CAAC;IACdJ,IAAA,GAAOvB,OAAA,CAAQe,KAAA,CAAMO,IAAA,CAAKC,IAAI,KAAKD,IAAA,CAAKC,IAAA;IAExCC,MAAA,GAASiB,aAAA,CAAclB,IAAA,EAAMP,WAAW;IAExCP,YAAA,CAAaiC,IAAA,CAAKpB,IAAA,CAAKW,WAAW;IAElC,IAAIT,MAAA,EAAQ;MACVA,MAAA,CAAOQ,iBAAA,CAAmB;MAE1B,IAAIhC,OAAA,CAAQa,eAAA,EAAiB;QAC3BL,cAAA,CAAekC,IAAA,CAAKlB,MAAA,CAAOS,WAAW;MAC9C,OAAa;QACLzB,cAAA,CAAekC,IAAA,CAAK5C,MAAA,CAAOmC,WAAW,EAAEU,MAAA,CAAQ;QAChDnC,cAAA,CAAe8B,QAAA,CAASd,MAAA,CAAOS,WAAW;MAC3C;MAID5B,KAAA,CAAMuC,kBAAA,CAAmBpC,cAAc;MACvCA,cAAA,CAAeH,KAAA,CAAMA,KAAA,CAAMwC,GAAA,CAAI,IAAIxC,KAAA,CAAMyC,CAAA,EAAG,IAAIzC,KAAA,CAAM0C,CAAA,EAAG,IAAI1C,KAAA,CAAM2C,CAAC,CAAC;MAIrEvC,YAAA,CAAawC,0BAAA,CAA2B9C,IAAA,CAAK+C,qBAAA,CAAsB1C,cAAc,CAAC;MAElF,IAAIV,MAAA,CAAOmB,UAAA,EAAY;QACrB,MAAMkC,SAAA,GAAYhC,KAAA,CAAMiC,OAAA,CAAQ9B,IAAI;UAClC+B,WAAA,GAAchC,SAAA,GACVA,SAAA,CAAU8B,SAAS,IACnB7C,cAAA,CAAeoC,IAAA,CAAK5C,MAAA,CAAOoB,QAAA,CAASoC,YAAA,CAAaH,SAAS,CAAC,EAAER,MAAA,CAAQ;QAE3ElC,YAAA,CAAa6B,QAAA,CAASe,WAAW;MAClC;MAED5C,YAAA,CAAa8C,YAAA,CAAa/C,cAAc;IACzC;IAED,IAAIc,IAAA,CAAKkC,MAAA,IAAUlC,IAAA,CAAKkC,MAAA,CAAOC,MAAA,EAAQ;MACrCnC,IAAA,CAAKe,MAAA,CAAOK,IAAA,CAAKpB,IAAA,CAAKkC,MAAA,CAAOvB,WAAW,EAAEU,MAAA,CAAQ;MAClDrB,IAAA,CAAKe,MAAA,CAAOC,QAAA,CAAS7B,YAAY;IACvC,OAAW;MACLa,IAAA,CAAKe,MAAA,CAAOK,IAAA,CAAKjC,YAAY;IAC9B;IAED,IAAIT,OAAA,CAAQY,mBAAA,IAAuBW,IAAA,KAASvB,OAAA,CAAQc,GAAA,EAAK;MACvDQ,IAAA,CAAKe,MAAA,CAAOqB,WAAA,CAAYzD,GAAA,CAAI4C,GAAA,CAAI,GAAGvB,IAAA,CAAKQ,QAAA,CAASiB,CAAA,EAAG,CAAC,CAAC;IACvD;IAEDzB,IAAA,CAAKe,MAAA,CAAOE,SAAA,CAAUjB,IAAA,CAAKQ,QAAA,EAAUR,IAAA,CAAKkB,UAAA,EAAYlB,IAAA,CAAKjB,KAAK;IAEhEiB,IAAA,CAAKU,iBAAA,CAAmB;EACzB;EAED,IAAIhC,OAAA,CAAQW,gBAAA,EAAkB;IAC5B,SAASgB,CAAA,GAAI,GAAGA,CAAA,GAAIR,KAAA,CAAMS,MAAA,EAAQ,EAAED,CAAA,EAAG;MACrCL,IAAA,GAAOH,KAAA,CAAMQ,CAAC;MACdJ,IAAA,GAAOvB,OAAA,CAAQe,KAAA,CAAMO,IAAA,CAAKC,IAAI,KAAKD,IAAA,CAAKC,IAAA;MAExC,IAAIA,IAAA,KAASvB,OAAA,CAAQc,GAAA,EAAK;QACxBQ,IAAA,CAAKQ,QAAA,CAASY,IAAA,CAAKjB,aAAA,CAAcE,CAAC,CAAC;MACpC;IACF;EACF;EAED,IAAI3B,OAAA,CAAQU,cAAA,EAAgB;IAG1BZ,MAAA,CAAOkC,iBAAA,CAAkB,IAAI;EAC9B;AACH;AAEA,SAAS2B,aAAa7D,MAAA,EAAQC,MAAA,EAAQ6D,IAAA,EAAM5D,OAAA,GAAU,IAAI;EACxDA,OAAA,CAAQ6D,qBAAA,GAAwB7D,OAAA,CAAQ6D,qBAAA,KAA0B,SAAY7D,OAAA,CAAQ6D,qBAAA,GAAwB;EAC9G7D,OAAA,CAAQ8D,GAAA,GAAM9D,OAAA,CAAQ8D,GAAA,KAAQ,SAAY9D,OAAA,CAAQ8D,GAAA,GAAM;EACxD9D,OAAA,CAAQe,KAAA,GAAQf,OAAA,CAAQe,KAAA,IAAS,EAAE;EAEnC,IAAI,CAAChB,MAAA,CAAOkB,UAAA,EAAY;IACtBlB,MAAA,GAASgE,qBAAA,CAAsBhE,MAAM;EACtC;EAED,MAAMiE,SAAA,GAAYC,IAAA,CAAKC,KAAA,CAAMN,IAAA,CAAKO,QAAA,IAAYnE,OAAA,CAAQ8D,GAAA,GAAM,OAAQ,GAAI;IACtEM,KAAA,GAAQ,IAAIpE,OAAA,CAAQ8D,GAAA;IACpBO,eAAA,GAAkB,EAAE;IACpBC,KAAA,GAAQ,IAAIC,cAAA,CAAexE,MAAM;IACjCoB,KAAA,GAAQC,QAAA,CAAStB,MAAA,CAAOoB,QAAQ;IAChCsD,SAAA,GAAY,EAAE;EAChB,IAAIC,cAAA,EAAgBnD,IAAA,EAAME,MAAA,EAAQkD,QAAA,EAAUnD,IAAA;EAE5C+C,KAAA,CAAMK,UAAA,CAAWf,IAAI,EAAEgB,IAAA,CAAM;EAC7BN,KAAA,CAAMO,MAAA,CAAO,CAAC;EAEd9E,MAAA,CAAOiC,iBAAA,CAAmB;EAE1B,SAASL,CAAA,GAAI,GAAGA,CAAA,GAAIqC,SAAA,EAAW,EAAErC,CAAA,EAAG;IAClC,MAAMmD,IAAA,GAAOnD,CAAA,GAAIyC,KAAA;IAEjBvE,QAAA,CAASC,MAAA,EAAQC,MAAA,EAAQC,OAAO;IAEhC,SAAS+E,CAAA,GAAI,GAAGA,CAAA,GAAI5D,KAAA,CAAMS,MAAA,EAAQ,EAAEmD,CAAA,EAAG;MACrCxD,IAAA,GAAOvB,OAAA,CAAQe,KAAA,CAAMI,KAAA,CAAM4D,CAAC,EAAExD,IAAI,KAAKJ,KAAA,CAAM4D,CAAC,EAAExD,IAAA;MAEhDC,MAAA,GAASiB,aAAA,CAAclB,IAAA,EAAMxB,MAAA,CAAOmB,QAAQ;MAE5C,IAAIM,MAAA,EAAQ;QACVF,IAAA,GAAOH,KAAA,CAAM4D,CAAC;QACdL,QAAA,GAAWF,SAAA,CAAUO,CAAC,IAAIP,SAAA,CAAUO,CAAC,KAAK;UAAEzD;QAAY;QAExD,IAAItB,OAAA,CAAQc,GAAA,KAAQS,IAAA,EAAM;UACxB,IAAI,CAACmD,QAAA,CAASzE,GAAA,EAAK;YACjByE,QAAA,CAASzE,GAAA,GAAM;cACb+E,KAAA,EAAO,IAAIC,YAAA,CAAajB,SAAS;cACjCkB,MAAA,EAAQ,IAAID,YAAA,CAAajB,SAAA,GAAY,CAAC;YACvC;UACF;UAED,IAAIhE,OAAA,CAAQ6D,qBAAA,EAAuB;YACjC,IAAIlC,CAAA,KAAM,GAAG;cACX8C,cAAA,GAAiBnD,IAAA,CAAKQ,QAAA,CAASC,KAAA,CAAO;YACvC;YAEDT,IAAA,CAAKQ,QAAA,CAASqD,GAAA,CAAIV,cAAc;UACjC;UAEDC,QAAA,CAASzE,GAAA,CAAI+E,KAAA,CAAMrD,CAAC,IAAImD,IAAA;UAExBxD,IAAA,CAAKQ,QAAA,CAASsD,OAAA,CAAQV,QAAA,CAASzE,GAAA,CAAIiF,MAAA,EAAQvD,CAAA,GAAI,CAAC;QACjD;QAED,IAAI,CAAC+C,QAAA,CAASvE,IAAA,EAAM;UAClBuE,QAAA,CAASvE,IAAA,GAAO;YACd6E,KAAA,EAAO,IAAIC,YAAA,CAAajB,SAAS;YACjCkB,MAAA,EAAQ,IAAID,YAAA,CAAajB,SAAA,GAAY,CAAC;UACvC;QACF;QAEDU,QAAA,CAASvE,IAAA,CAAK6E,KAAA,CAAMrD,CAAC,IAAImD,IAAA;QAEzBxD,IAAA,CAAKkB,UAAA,CAAW4C,OAAA,CAAQV,QAAA,CAASvE,IAAA,CAAK+E,MAAA,EAAQvD,CAAA,GAAI,CAAC;MACpD;IACF;IAED2C,KAAA,CAAMO,MAAA,CAAOT,KAAK;IAElBrE,MAAA,CAAOiC,iBAAA,CAAmB;EAC3B;EAED,SAASL,CAAA,GAAI,GAAGA,CAAA,GAAI6C,SAAA,CAAU5C,MAAA,EAAQ,EAAED,CAAA,EAAG;IACzC+C,QAAA,GAAWF,SAAA,CAAU7C,CAAC;IAEtB,IAAI+C,QAAA,EAAU;MACZ,IAAIA,QAAA,CAASzE,GAAA,EAAK;QAChBoE,eAAA,CAAgBxC,IAAA,CACd,IAAIwD,mBAAA,CACF,YAAYX,QAAA,CAASpD,IAAA,CAAKC,IAAA,GAAO,cACjCmD,QAAA,CAASzE,GAAA,CAAI+E,KAAA,EACbN,QAAA,CAASzE,GAAA,CAAIiF,MACd,CACF;MACF;MAEDb,eAAA,CAAgBxC,IAAA,CACd,IAAIyD,uBAAA,CACF,YAAYZ,QAAA,CAASpD,IAAA,CAAKC,IAAA,GAAO,gBACjCmD,QAAA,CAASvE,IAAA,CAAK6E,KAAA,EACdN,QAAA,CAASvE,IAAA,CAAK+E,MACf,CACF;IACF;EACF;EAEDZ,KAAA,CAAMiB,aAAA,CAAc3B,IAAI;EAExB,OAAO,IAAI4B,aAAA,CAAc5B,IAAA,CAAKrC,IAAA,EAAM,IAAI8C,eAAe;AACzD;AAEA,SAAStC,MAAMhC,MAAA,EAAQ;EACrB,MAAM0F,YAAA,GAAe,mBAAIC,GAAA,CAAK;EAC9B,MAAMC,WAAA,GAAc,mBAAID,GAAA,CAAK;EAE7B,MAAME,MAAA,GAAQ7F,MAAA,CAAOgC,KAAA,CAAO;EAE5B8D,gBAAA,CAAiB9F,MAAA,EAAQ6F,MAAA,EAAO,UAAUE,UAAA,EAAYC,UAAA,EAAY;IAChEN,YAAA,CAAa5C,GAAA,CAAIkD,UAAA,EAAYD,UAAU;IACvCH,WAAA,CAAY9C,GAAA,CAAIiD,UAAA,EAAYC,UAAU;EAC1C,CAAG;EAEDH,MAAA,CAAMI,QAAA,CAAS,UAAUC,IAAA,EAAM;IAC7B,IAAI,CAACA,IAAA,CAAKC,aAAA,EAAe;IAEzB,MAAMC,UAAA,GAAaF,IAAA;IACnB,MAAMG,UAAA,GAAaX,YAAA,CAAaY,GAAA,CAAIJ,IAAI;IACxC,MAAMjF,WAAA,GAAcoF,UAAA,CAAWlF,QAAA,CAASC,KAAA;IAExCgF,UAAA,CAAWjF,QAAA,GAAWkF,UAAA,CAAWlF,QAAA,CAASa,KAAA,CAAO;IACjDoE,UAAA,CAAWG,UAAA,CAAW5D,IAAA,CAAK0D,UAAA,CAAWE,UAAU;IAEhDH,UAAA,CAAWjF,QAAA,CAASC,KAAA,GAAQH,WAAA,CAAYuF,GAAA,CAAI,UAAUjF,IAAA,EAAM;MAC1D,OAAOqE,WAAA,CAAYU,GAAA,CAAI/E,IAAI;IACjC,CAAK;IAED6E,UAAA,CAAWK,IAAA,CAAKL,UAAA,CAAWjF,QAAA,EAAUiF,UAAA,CAAWG,UAAU;EAC9D,CAAG;EAED,OAAOV,MAAA;AACT;AAIA,SAASnD,cAAclB,IAAA,EAAML,QAAA,EAAU;EACrC,SAASS,CAAA,GAAI,GAAGR,KAAA,GAAQC,QAAA,CAASF,QAAQ,GAAGS,CAAA,GAAIR,KAAA,CAAMS,MAAA,EAAQD,CAAA,IAAK;IACjE,IAAIJ,IAAA,KAASJ,KAAA,CAAMQ,CAAC,EAAEJ,IAAA,EAAM,OAAOJ,KAAA,CAAMQ,CAAC;EAC3C;AACH;AAEA,SAASP,SAASF,QAAA,EAAU;EAC1B,OAAOuF,KAAA,CAAMC,OAAA,CAAQxF,QAAQ,IAAIA,QAAA,GAAWA,QAAA,CAASC,KAAA;AACvD;AAEA,SAAS4C,sBAAsB7C,QAAA,EAAU;EACvC,MAAMnB,MAAA,GAAS,IAAI4G,cAAA,CAAezF,QAAA,CAASC,KAAA,CAAM,CAAC,CAAC;EACnDpB,MAAA,CAAOmB,QAAA,GAAWA,QAAA;EAElB,OAAOnB,MAAA;AACT;AAEA,SAAS8F,iBAAiBe,CAAA,EAAGC,CAAA,EAAGC,QAAA,EAAU;EACxCA,QAAA,CAASF,CAAA,EAAGC,CAAC;EAEb,SAASlF,CAAA,GAAI,GAAGA,CAAA,GAAIiF,CAAA,CAAEzE,QAAA,CAASP,MAAA,EAAQD,CAAA,IAAK;IAC1CkE,gBAAA,CAAiBe,CAAA,CAAEzE,QAAA,CAASR,CAAC,GAAGkF,CAAA,CAAE1E,QAAA,CAASR,CAAC,GAAGmF,QAAQ;EACxD;AACH;AAEY,MAACC,aAAA,GAAgB;EAAElH,QAAA;EAAU8D,YAAA;EAAc5B;AAAK", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}