{"ast": null, "code": "import { Mesh, Vector3, Matrix3 } from \"three\";\nclass PLYExporter {\n  parse(object, onDone, options) {\n    if (onDone && typeof onDone === \"object\") {\n      console.warn('THREE.PLYExporter: The options parameter is now the third argument to the \"parse\" function. See the documentation for the new API.');\n      options = onDone;\n      onDone = void 0;\n    }\n    const defaultOptions = {\n      binary: false,\n      excludeAttributes: [],\n      // normal, uv, color, index\n      littleEndian: false\n    };\n    options = Object.assign(defaultOptions, options);\n    const excludeAttributes = options.excludeAttributes;\n    let includeNormals = false;\n    let includeColors = false;\n    let includeUVs = false;\n    let vertexCount = 0;\n    let faceCount = 0;\n    object.traverse(function (child) {\n      if (child instanceof Mesh && child.isMesh) {\n        const mesh = child;\n        const geometry = mesh.geometry;\n        if (!geometry.isBufferGeometry) {\n          throw new Error(\"THREE.PLYExporter: Geometry is not of type THREE.BufferGeometry.\");\n        }\n        const vertices = geometry.getAttribute(\"position\");\n        const normals = geometry.getAttribute(\"normal\");\n        const uvs = geometry.getAttribute(\"uv\");\n        const colors = geometry.getAttribute(\"color\");\n        const indices = geometry.getIndex();\n        if (vertices === void 0) {\n          return;\n        }\n        vertexCount += vertices.count;\n        faceCount += indices ? indices.count / 3 : vertices.count / 3;\n        if (normals !== void 0) includeNormals = true;\n        if (uvs !== void 0) includeUVs = true;\n        if (colors !== void 0) includeColors = true;\n      }\n    });\n    const includeIndices = (excludeAttributes == null ? void 0 : excludeAttributes.indexOf(\"index\")) === -1;\n    includeNormals = includeNormals && (excludeAttributes == null ? void 0 : excludeAttributes.indexOf(\"normal\")) === -1;\n    includeColors = includeColors && (excludeAttributes == null ? void 0 : excludeAttributes.indexOf(\"color\")) === -1;\n    includeUVs = includeUVs && (excludeAttributes == null ? void 0 : excludeAttributes.indexOf(\"uv\")) === -1;\n    if (includeIndices && faceCount !== Math.floor(faceCount)) {\n      console.error(\"PLYExporter: Failed to generate a valid PLY file with triangle indices because the number of indices is not divisible by 3.\");\n      return null;\n    }\n    const indexByteCount = 4;\n    let header = `ply\nformat ${options.binary ? options.littleEndian ? \"binary_little_endian\" : \"binary_big_endian\" : \"ascii\"} 1.0\nelement vertex ${vertexCount}\nproperty float x\nproperty float y\nproperty float z\n`;\n    if (includeNormals) {\n      header += \"property float nx\\nproperty float ny\\nproperty float nz\\n\";\n    }\n    if (includeUVs) {\n      header += \"property float s\\nproperty float t\\n\";\n    }\n    if (includeColors) {\n      header += \"property uchar red\\nproperty uchar green\\nproperty uchar blue\\n\";\n    }\n    if (includeIndices) {\n      header += `${`element face ${faceCount}\n`}property list uchar int vertex_index\n`;\n    }\n    header += \"end_header\\n\";\n    const vertex = new Vector3();\n    const normalMatrixWorld = new Matrix3();\n    let result = null;\n    if (options.binary) {\n      const headerBin = new TextEncoder().encode(header);\n      const vertexListLength = vertexCount * (4 * 3 + (includeNormals ? 4 * 3 : 0) + (includeColors ? 3 : 0) + (includeUVs ? 4 * 2 : 0));\n      const faceListLength = includeIndices ? faceCount * (indexByteCount * 3 + 1) : 0;\n      const output = new DataView(new ArrayBuffer(headerBin.length + vertexListLength + faceListLength));\n      new Uint8Array(output.buffer).set(headerBin, 0);\n      let vOffset = headerBin.length;\n      let fOffset = headerBin.length + vertexListLength;\n      let writtenVertices = 0;\n      this.traverseMeshes(object, function (mesh, geometry) {\n        const vertices = geometry.getAttribute(\"position\");\n        const normals = geometry.getAttribute(\"normal\");\n        const uvs = geometry.getAttribute(\"uv\");\n        const colors = geometry.getAttribute(\"color\");\n        const indices = geometry.getIndex();\n        normalMatrixWorld.getNormalMatrix(mesh.matrixWorld);\n        for (let i = 0, l = vertices.count; i < l; i++) {\n          vertex.x = vertices.getX(i);\n          vertex.y = vertices.getY(i);\n          vertex.z = vertices.getZ(i);\n          vertex.applyMatrix4(mesh.matrixWorld);\n          output.setFloat32(vOffset, vertex.x, options.littleEndian);\n          vOffset += 4;\n          output.setFloat32(vOffset, vertex.y, options.littleEndian);\n          vOffset += 4;\n          output.setFloat32(vOffset, vertex.z, options.littleEndian);\n          vOffset += 4;\n          if (includeNormals) {\n            if (normals != null) {\n              vertex.x = normals.getX(i);\n              vertex.y = normals.getY(i);\n              vertex.z = normals.getZ(i);\n              vertex.applyMatrix3(normalMatrixWorld).normalize();\n              output.setFloat32(vOffset, vertex.x, options.littleEndian);\n              vOffset += 4;\n              output.setFloat32(vOffset, vertex.y, options.littleEndian);\n              vOffset += 4;\n              output.setFloat32(vOffset, vertex.z, options.littleEndian);\n              vOffset += 4;\n            } else {\n              output.setFloat32(vOffset, 0, options.littleEndian);\n              vOffset += 4;\n              output.setFloat32(vOffset, 0, options.littleEndian);\n              vOffset += 4;\n              output.setFloat32(vOffset, 0, options.littleEndian);\n              vOffset += 4;\n            }\n          }\n          if (includeUVs) {\n            if (uvs != null) {\n              output.setFloat32(vOffset, uvs.getX(i), options.littleEndian);\n              vOffset += 4;\n              output.setFloat32(vOffset, uvs.getY(i), options.littleEndian);\n              vOffset += 4;\n            } else if (!includeUVs) {\n              output.setFloat32(vOffset, 0, options.littleEndian);\n              vOffset += 4;\n              output.setFloat32(vOffset, 0, options.littleEndian);\n              vOffset += 4;\n            }\n          }\n          if (includeColors) {\n            if (colors != null) {\n              output.setUint8(vOffset, Math.floor(colors.getX(i) * 255));\n              vOffset += 1;\n              output.setUint8(vOffset, Math.floor(colors.getY(i) * 255));\n              vOffset += 1;\n              output.setUint8(vOffset, Math.floor(colors.getZ(i) * 255));\n              vOffset += 1;\n            } else {\n              output.setUint8(vOffset, 255);\n              vOffset += 1;\n              output.setUint8(vOffset, 255);\n              vOffset += 1;\n              output.setUint8(vOffset, 255);\n              vOffset += 1;\n            }\n          }\n        }\n        if (includeIndices) {\n          if (indices !== null) {\n            for (let i = 0, l = indices.count; i < l; i += 3) {\n              output.setUint8(fOffset, 3);\n              fOffset += 1;\n              output.setUint32(fOffset, indices.getX(i + 0) + writtenVertices, options.littleEndian);\n              fOffset += indexByteCount;\n              output.setUint32(fOffset, indices.getX(i + 1) + writtenVertices, options.littleEndian);\n              fOffset += indexByteCount;\n              output.setUint32(fOffset, indices.getX(i + 2) + writtenVertices, options.littleEndian);\n              fOffset += indexByteCount;\n            }\n          } else {\n            for (let i = 0, l = vertices.count; i < l; i += 3) {\n              output.setUint8(fOffset, 3);\n              fOffset += 1;\n              output.setUint32(fOffset, writtenVertices + i, options.littleEndian);\n              fOffset += indexByteCount;\n              output.setUint32(fOffset, writtenVertices + i + 1, options.littleEndian);\n              fOffset += indexByteCount;\n              output.setUint32(fOffset, writtenVertices + i + 2, options.littleEndian);\n              fOffset += indexByteCount;\n            }\n          }\n        }\n        writtenVertices += vertices.count;\n      });\n      result = output.buffer;\n    } else {\n      let writtenVertices = 0;\n      let vertexList = \"\";\n      let faceList = \"\";\n      this.traverseMeshes(object, function (mesh, geometry) {\n        const vertices = geometry.getAttribute(\"position\");\n        const normals = geometry.getAttribute(\"normal\");\n        const uvs = geometry.getAttribute(\"uv\");\n        const colors = geometry.getAttribute(\"color\");\n        const indices = geometry.getIndex();\n        normalMatrixWorld.getNormalMatrix(mesh.matrixWorld);\n        for (let i = 0, l = vertices.count; i < l; i++) {\n          vertex.x = vertices.getX(i);\n          vertex.y = vertices.getY(i);\n          vertex.z = vertices.getZ(i);\n          vertex.applyMatrix4(mesh.matrixWorld);\n          let line = vertex.x + \" \" + vertex.y + \" \" + vertex.z;\n          if (includeNormals) {\n            if (normals != null) {\n              vertex.x = normals.getX(i);\n              vertex.y = normals.getY(i);\n              vertex.z = normals.getZ(i);\n              vertex.applyMatrix3(normalMatrixWorld).normalize();\n              line += \" \" + vertex.x + \" \" + vertex.y + \" \" + vertex.z;\n            } else {\n              line += \" 0 0 0\";\n            }\n          }\n          if (includeUVs) {\n            if (uvs != null) {\n              line += \" \" + uvs.getX(i) + \" \" + uvs.getY(i);\n            } else if (includeUVs) {\n              line += \" 0 0\";\n            }\n          }\n          if (includeColors) {\n            if (colors != null) {\n              line += \" \" + Math.floor(colors.getX(i) * 255) + \" \" + Math.floor(colors.getY(i) * 255) + \" \" + Math.floor(colors.getZ(i) * 255);\n            } else {\n              line += \" 255 255 255\";\n            }\n          }\n          vertexList += line + \"\\n\";\n        }\n        if (includeIndices) {\n          if (indices !== null) {\n            for (let i = 0, l = indices.count; i < l; i += 3) {\n              faceList += `3 ${indices.getX(i + 0) + writtenVertices}`;\n              faceList += ` ${indices.getX(i + 1) + writtenVertices}`;\n              faceList += ` ${indices.getX(i + 2) + writtenVertices}\n`;\n            }\n          } else {\n            for (let i = 0, l = vertices.count; i < l; i += 3) {\n              faceList += `3 ${writtenVertices + i} ${writtenVertices + i + 1} ${writtenVertices + i + 2}\n`;\n            }\n          }\n          faceCount += indices ? indices.count / 3 : vertices.count / 3;\n        }\n        writtenVertices += vertices.count;\n      });\n      result = `${header}${vertexList}${includeIndices ? `${faceList}\n` : \"\\n\"}`;\n    }\n    if (typeof onDone === \"function\") {\n      requestAnimationFrame(() => onDone && onDone(typeof result === \"string\" ? result : \"\"));\n    }\n    return result;\n  }\n  // Iterate over the valid meshes in the object\n  traverseMeshes(object, cb) {\n    object.traverse(function (child) {\n      if (child instanceof Mesh && child.isMesh) {\n        const mesh = child;\n        const geometry = mesh.geometry;\n        if (!geometry.isBufferGeometry) {\n          throw new Error(\"THREE.PLYExporter: Geometry is not of type THREE.BufferGeometry.\");\n        }\n        if (geometry.hasAttribute(\"position\")) {\n          cb(mesh, geometry);\n        }\n      }\n    });\n  }\n}\nexport { PLYExporter };", "map": {"version": 3, "names": ["PLYExporter", "parse", "object", "onDone", "options", "console", "warn", "defaultOptions", "binary", "excludeAttributes", "littleEndian", "Object", "assign", "includeNormals", "includeColors", "includeUVs", "vertexCount", "faceCount", "traverse", "child", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "mesh", "geometry", "isBufferGeometry", "Error", "vertices", "getAttribute", "normals", "uvs", "colors", "indices", "getIndex", "count", "includeIndices", "indexOf", "Math", "floor", "error", "indexByteCount", "header", "vertex", "Vector3", "normalMatrixWorld", "Matrix3", "result", "headerBin", "TextEncoder", "encode", "vertexListLength", "faceList<PERSON><PERSON><PERSON>", "output", "DataView", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "length", "Uint8Array", "buffer", "set", "vOffset", "fOffset", "writtenVertices", "traverseMeshes", "getNormalMatrix", "matrixWorld", "i", "l", "x", "getX", "y", "getY", "z", "getZ", "applyMatrix4", "setFloat32", "applyMatrix3", "normalize", "setUint8", "setUint32", "vertexList", "faceList", "line", "requestAnimationFrame", "cb", "hasAttribute"], "sources": ["C:\\Users\\<USER>\\Documents\\Apps_Creations\\SiahMan_Racing4Speed_WebGame\\siah-man-racing\\node_modules\\src\\exporters\\PLYExporter.ts"], "sourcesContent": ["import { BufferGeometry, Matrix3, Mesh, Object3D, Vector3 } from 'three'\n\n/**\n * https://github.com/gkjo<PERSON>son/ply-exporter-js\n *\n * Usage:\n *  const exporter = new PLYExporter();\n *\n *  // second argument is a list of options\n *  exporter.parse(mesh, data => console.log(data), { binary: true, excludeAttributes: [ 'color' ], littleEndian: true });\n *\n * Format Definition:\n * http://paulbourke.net/dataformats/ply/\n */\n\nexport interface PLYExporterOptions {\n  binary?: boolean\n  excludeAttributes?: string[]\n  littleEndian?: boolean\n}\n\nclass PLYExporter {\n  public parse(\n    object: Object3D,\n    onDone: ((res: string) => void) | undefined,\n    options: PLYExporterOptions,\n  ): string | ArrayBuffer | null {\n    if (onDone && typeof onDone === 'object') {\n      console.warn(\n        'THREE.PLYExporter: The options parameter is now the third argument to the \"parse\" function. See the documentation for the new API.',\n      )\n      options = onDone\n      onDone = undefined\n    }\n\n    // Default options\n    const defaultOptions = {\n      binary: false,\n      excludeAttributes: [], // normal, uv, color, index\n      littleEndian: false,\n    }\n\n    options = Object.assign(defaultOptions, options)\n\n    const excludeAttributes = options.excludeAttributes\n    let includeNormals = false\n    let includeColors = false\n    let includeUVs = false\n\n    // count the vertices, check which properties are used,\n    // and cache the BufferGeometry\n    let vertexCount = 0\n    let faceCount = 0\n    object.traverse(function (child) {\n      if (child instanceof Mesh && child.isMesh) {\n        const mesh = child\n        const geometry = mesh.geometry\n\n        if (!geometry.isBufferGeometry) {\n          throw new Error('THREE.PLYExporter: Geometry is not of type THREE.BufferGeometry.')\n        }\n\n        const vertices = geometry.getAttribute('position')\n        const normals = geometry.getAttribute('normal')\n        const uvs = geometry.getAttribute('uv')\n        const colors = geometry.getAttribute('color')\n        const indices = geometry.getIndex()\n\n        if (vertices === undefined) {\n          return\n        }\n\n        vertexCount += vertices.count\n        faceCount += indices ? indices.count / 3 : vertices.count / 3\n\n        if (normals !== undefined) includeNormals = true\n\n        if (uvs !== undefined) includeUVs = true\n\n        if (colors !== undefined) includeColors = true\n      }\n    })\n\n    const includeIndices = excludeAttributes?.indexOf('index') === -1\n    includeNormals = includeNormals && excludeAttributes?.indexOf('normal') === -1\n    includeColors = includeColors && excludeAttributes?.indexOf('color') === -1\n    includeUVs = includeUVs && excludeAttributes?.indexOf('uv') === -1\n\n    if (includeIndices && faceCount !== Math.floor(faceCount)) {\n      // point cloud meshes will not have an index array and may not have a\n      // number of vertices that is divisble by 3 (and therefore representable\n      // as triangles)\n      console.error(\n        'PLYExporter: Failed to generate a valid PLY file with triangle indices because the ' +\n          'number of indices is not divisible by 3.',\n      )\n\n      return null\n    }\n\n    const indexByteCount = 4\n\n    let header =\n      'ply\\n' +\n      `format ${\n        options.binary ? (options.littleEndian ? 'binary_little_endian' : 'binary_big_endian') : 'ascii'\n      } 1.0\\n` +\n      `element vertex ${vertexCount}\\n` +\n      // position\n      'property float x\\n' +\n      'property float y\\n' +\n      'property float z\\n'\n\n    if (includeNormals) {\n      // normal\n      header += 'property float nx\\n' + 'property float ny\\n' + 'property float nz\\n'\n    }\n\n    if (includeUVs) {\n      // uvs\n      header += 'property float s\\n' + 'property float t\\n'\n    }\n\n    if (includeColors) {\n      // colors\n      header += 'property uchar red\\n' + 'property uchar green\\n' + 'property uchar blue\\n'\n    }\n\n    if (includeIndices) {\n      // faces\n      header += `${`element face ${faceCount}\\n`}property list uchar int vertex_index\\n`\n    }\n\n    header += 'end_header\\n'\n\n    // Generate attribute data\n    const vertex = new Vector3()\n    const normalMatrixWorld = new Matrix3()\n    let result: string | ArrayBuffer | null = null\n\n    if (options.binary) {\n      // Binary File Generation\n      const headerBin = new TextEncoder().encode(header)\n\n      // 3 position values at 4 bytes\n      // 3 normal values at 4 bytes\n      // 3 color channels with 1 byte\n      // 2 uv values at 4 bytes\n      const vertexListLength =\n        vertexCount * (4 * 3 + (includeNormals ? 4 * 3 : 0) + (includeColors ? 3 : 0) + (includeUVs ? 4 * 2 : 0))\n\n      // 1 byte shape desciptor\n      // 3 vertex indices at ${indexByteCount} bytes\n      const faceListLength = includeIndices ? faceCount * (indexByteCount * 3 + 1) : 0\n      const output = new DataView(new ArrayBuffer(headerBin.length + vertexListLength + faceListLength))\n      new Uint8Array(output.buffer).set(headerBin, 0)\n\n      let vOffset = headerBin.length\n      let fOffset = headerBin.length + vertexListLength\n      let writtenVertices = 0\n      this.traverseMeshes(object, function (mesh, geometry) {\n        const vertices = geometry.getAttribute('position')\n        const normals = geometry.getAttribute('normal')\n        const uvs = geometry.getAttribute('uv')\n        const colors = geometry.getAttribute('color')\n        const indices = geometry.getIndex()\n\n        normalMatrixWorld.getNormalMatrix(mesh.matrixWorld)\n\n        for (let i = 0, l = vertices.count; i < l; i++) {\n          vertex.x = vertices.getX(i)\n          vertex.y = vertices.getY(i)\n          vertex.z = vertices.getZ(i)\n\n          vertex.applyMatrix4(mesh.matrixWorld)\n\n          // Position information\n          output.setFloat32(vOffset, vertex.x, options.littleEndian)\n          vOffset += 4\n\n          output.setFloat32(vOffset, vertex.y, options.littleEndian)\n          vOffset += 4\n\n          output.setFloat32(vOffset, vertex.z, options.littleEndian)\n          vOffset += 4\n\n          // Normal information\n          if (includeNormals) {\n            if (normals != null) {\n              vertex.x = normals.getX(i)\n              vertex.y = normals.getY(i)\n              vertex.z = normals.getZ(i)\n\n              vertex.applyMatrix3(normalMatrixWorld).normalize()\n\n              output.setFloat32(vOffset, vertex.x, options.littleEndian)\n              vOffset += 4\n\n              output.setFloat32(vOffset, vertex.y, options.littleEndian)\n              vOffset += 4\n\n              output.setFloat32(vOffset, vertex.z, options.littleEndian)\n              vOffset += 4\n            } else {\n              output.setFloat32(vOffset, 0, options.littleEndian)\n              vOffset += 4\n\n              output.setFloat32(vOffset, 0, options.littleEndian)\n              vOffset += 4\n\n              output.setFloat32(vOffset, 0, options.littleEndian)\n              vOffset += 4\n            }\n          }\n\n          // UV information\n          if (includeUVs) {\n            if (uvs != null) {\n              output.setFloat32(vOffset, uvs.getX(i), options.littleEndian)\n              vOffset += 4\n\n              output.setFloat32(vOffset, uvs.getY(i), options.littleEndian)\n              vOffset += 4\n            } else if (!includeUVs) {\n              output.setFloat32(vOffset, 0, options.littleEndian)\n              vOffset += 4\n\n              output.setFloat32(vOffset, 0, options.littleEndian)\n              vOffset += 4\n            }\n          }\n\n          // Color information\n          if (includeColors) {\n            if (colors != null) {\n              output.setUint8(vOffset, Math.floor(colors.getX(i) * 255))\n              vOffset += 1\n\n              output.setUint8(vOffset, Math.floor(colors.getY(i) * 255))\n              vOffset += 1\n\n              output.setUint8(vOffset, Math.floor(colors.getZ(i) * 255))\n              vOffset += 1\n            } else {\n              output.setUint8(vOffset, 255)\n              vOffset += 1\n\n              output.setUint8(vOffset, 255)\n              vOffset += 1\n\n              output.setUint8(vOffset, 255)\n              vOffset += 1\n            }\n          }\n        }\n\n        if (includeIndices) {\n          // Create the face list\n\n          if (indices !== null) {\n            for (let i = 0, l = indices.count; i < l; i += 3) {\n              output.setUint8(fOffset, 3)\n              fOffset += 1\n\n              output.setUint32(fOffset, indices.getX(i + 0) + writtenVertices, options.littleEndian)\n              fOffset += indexByteCount\n\n              output.setUint32(fOffset, indices.getX(i + 1) + writtenVertices, options.littleEndian)\n              fOffset += indexByteCount\n\n              output.setUint32(fOffset, indices.getX(i + 2) + writtenVertices, options.littleEndian)\n              fOffset += indexByteCount\n            }\n          } else {\n            for (let i = 0, l = vertices.count; i < l; i += 3) {\n              output.setUint8(fOffset, 3)\n              fOffset += 1\n\n              output.setUint32(fOffset, writtenVertices + i, options.littleEndian)\n              fOffset += indexByteCount\n\n              output.setUint32(fOffset, writtenVertices + i + 1, options.littleEndian)\n              fOffset += indexByteCount\n\n              output.setUint32(fOffset, writtenVertices + i + 2, options.littleEndian)\n              fOffset += indexByteCount\n            }\n          }\n        }\n\n        // Save the amount of verts we've already written so we can offset\n        // the face index on the next mesh\n        writtenVertices += vertices.count\n      })\n\n      result = output.buffer\n    } else {\n      // Ascii File Generation\n      // count the number of vertices\n      let writtenVertices = 0\n      let vertexList = ''\n      let faceList = ''\n\n      this.traverseMeshes(object, function (mesh, geometry) {\n        const vertices = geometry.getAttribute('position')\n        const normals = geometry.getAttribute('normal')\n        const uvs = geometry.getAttribute('uv')\n        const colors = geometry.getAttribute('color')\n        const indices = geometry.getIndex()\n\n        normalMatrixWorld.getNormalMatrix(mesh.matrixWorld)\n\n        // form each line\n        for (let i = 0, l = vertices.count; i < l; i++) {\n          vertex.x = vertices.getX(i)\n          vertex.y = vertices.getY(i)\n          vertex.z = vertices.getZ(i)\n\n          vertex.applyMatrix4(mesh.matrixWorld)\n\n          // Position information\n          let line = vertex.x + ' ' + vertex.y + ' ' + vertex.z\n\n          // Normal information\n          if (includeNormals) {\n            if (normals != null) {\n              vertex.x = normals.getX(i)\n              vertex.y = normals.getY(i)\n              vertex.z = normals.getZ(i)\n\n              vertex.applyMatrix3(normalMatrixWorld).normalize()\n\n              line += ' ' + vertex.x + ' ' + vertex.y + ' ' + vertex.z\n            } else {\n              line += ' 0 0 0'\n            }\n          }\n\n          // UV information\n          if (includeUVs) {\n            if (uvs != null) {\n              line += ' ' + uvs.getX(i) + ' ' + uvs.getY(i)\n            } else if (includeUVs) {\n              line += ' 0 0'\n            }\n          }\n\n          // Color information\n          if (includeColors) {\n            if (colors != null) {\n              line +=\n                ' ' +\n                Math.floor(colors.getX(i) * 255) +\n                ' ' +\n                Math.floor(colors.getY(i) * 255) +\n                ' ' +\n                Math.floor(colors.getZ(i) * 255)\n            } else {\n              line += ' 255 255 255'\n            }\n          }\n\n          vertexList += line + '\\n'\n        }\n\n        // Create the face list\n        if (includeIndices) {\n          if (indices !== null) {\n            for (let i = 0, l = indices.count; i < l; i += 3) {\n              faceList += `3 ${indices.getX(i + 0) + writtenVertices}`\n              faceList += ` ${indices.getX(i + 1) + writtenVertices}`\n              faceList += ` ${indices.getX(i + 2) + writtenVertices}\\n`\n            }\n          } else {\n            for (let i = 0, l = vertices.count; i < l; i += 3) {\n              faceList += `3 ${writtenVertices + i} ${writtenVertices + i + 1} ${writtenVertices + i + 2}\\n`\n            }\n          }\n\n          faceCount += indices ? indices.count / 3 : vertices.count / 3\n        }\n\n        writtenVertices += vertices.count\n      })\n\n      result = `${header}${vertexList}${includeIndices ? `${faceList}\\n` : '\\n'}`\n    }\n\n    if (typeof onDone === 'function') {\n      requestAnimationFrame(() => onDone && onDone(typeof result === 'string' ? result : ''))\n    }\n\n    return result\n  }\n\n  // Iterate over the valid meshes in the object\n  private traverseMeshes(object: Object3D, cb: (mesh: Mesh, geometry: BufferGeometry) => void): void {\n    object.traverse(function (child) {\n      if (child instanceof Mesh && child.isMesh) {\n        const mesh = child\n        const geometry = mesh.geometry\n\n        if (!geometry.isBufferGeometry) {\n          throw new Error('THREE.PLYExporter: Geometry is not of type THREE.BufferGeometry.')\n        }\n\n        if (geometry.hasAttribute('position')) {\n          cb(mesh, geometry)\n        }\n      }\n    })\n  }\n}\n\nexport { PLYExporter }\n"], "mappings": ";AAqBA,MAAMA,WAAA,CAAY;EACTC,MACLC,MAAA,EACAC,MAAA,EACAC,OAAA,EAC6B;IACzB,IAAAD,MAAA,IAAU,OAAOA,MAAA,KAAW,UAAU;MAChCE,OAAA,CAAAC,IAAA,CACN;MAEQF,OAAA,GAAAD,MAAA;MACDA,MAAA;IACX;IAGA,MAAMI,cAAA,GAAiB;MACrBC,MAAA,EAAQ;MACRC,iBAAA,EAAmB,EAAC;MAAA;MACpBC,YAAA,EAAc;IAAA;IAGNN,OAAA,GAAAO,MAAA,CAAOC,MAAA,CAAOL,cAAA,EAAgBH,OAAO;IAE/C,MAAMK,iBAAA,GAAoBL,OAAA,CAAQK,iBAAA;IAClC,IAAII,cAAA,GAAiB;IACrB,IAAIC,aAAA,GAAgB;IACpB,IAAIC,UAAA,GAAa;IAIjB,IAAIC,WAAA,GAAc;IAClB,IAAIC,SAAA,GAAY;IACTf,MAAA,CAAAgB,QAAA,CAAS,UAAUC,KAAA,EAAO;MAC3B,IAAAA,KAAA,YAAiBC,IAAA,IAAQD,KAAA,CAAME,MAAA,EAAQ;QACzC,MAAMC,IAAA,GAAOH,KAAA;QACb,MAAMI,QAAA,GAAWD,IAAA,CAAKC,QAAA;QAElB,KAACA,QAAA,CAASC,gBAAA,EAAkB;UACxB,UAAIC,KAAA,CAAM,kEAAkE;QACpF;QAEM,MAAAC,QAAA,GAAWH,QAAA,CAASI,YAAA,CAAa,UAAU;QAC3C,MAAAC,OAAA,GAAUL,QAAA,CAASI,YAAA,CAAa,QAAQ;QACxC,MAAAE,GAAA,GAAMN,QAAA,CAASI,YAAA,CAAa,IAAI;QAChC,MAAAG,MAAA,GAASP,QAAA,CAASI,YAAA,CAAa,OAAO;QACtC,MAAAI,OAAA,GAAUR,QAAA,CAASS,QAAA;QAEzB,IAAIN,QAAA,KAAa,QAAW;UAC1B;QACF;QAEAV,WAAA,IAAeU,QAAA,CAASO,KAAA;QACxBhB,SAAA,IAAac,OAAA,GAAUA,OAAA,CAAQE,KAAA,GAAQ,IAAIP,QAAA,CAASO,KAAA,GAAQ;QAE5D,IAAIL,OAAA,KAAY,QAA4Bf,cAAA;QAE5C,IAAIgB,GAAA,KAAQ,QAAwBd,UAAA;QAEpC,IAAIe,MAAA,KAAW,QAA2BhB,aAAA;MAC5C;IAAA,CACD;IAED,MAAMoB,cAAA,IAAiBzB,iBAAA,oBAAAA,iBAAA,CAAmB0B,OAAA,CAAQ,cAAa;IAC/DtB,cAAA,GAAiBA,cAAA,KAAkBJ,iBAAA,oBAAAA,iBAAA,CAAmB0B,OAAA,CAAQ,eAAc;IAC5ErB,aAAA,GAAgBA,aAAA,KAAiBL,iBAAA,oBAAAA,iBAAA,CAAmB0B,OAAA,CAAQ,cAAa;IACzEpB,UAAA,GAAaA,UAAA,KAAcN,iBAAA,oBAAAA,iBAAA,CAAmB0B,OAAA,CAAQ,WAAU;IAEhE,IAAID,cAAA,IAAkBjB,SAAA,KAAcmB,IAAA,CAAKC,KAAA,CAAMpB,SAAS,GAAG;MAIjDZ,OAAA,CAAAiC,KAAA,CACN;MAIK;IACT;IAEA,MAAMC,cAAA,GAAiB;IAEvB,IAAIC,MAAA,GACF;AAAA,SAEEpC,OAAA,CAAQI,MAAA,GAAUJ,OAAA,CAAQM,YAAA,GAAe,yBAAyB,sBAAuB;AAAA,iBAEzEM,WAAA;AAAA;AAAA;AAAA;AAAA;IAMpB,IAAIH,cAAA,EAAgB;MAER2B,MAAA;IACZ;IAEA,IAAIzB,UAAA,EAAY;MAEJyB,MAAA;IACZ;IAEA,IAAI1B,aAAA,EAAe;MAEP0B,MAAA;IACZ;IAEA,IAAIN,cAAA,EAAgB;MAElBM,MAAA,IAAU,GAAG,gBAAgBvB,SAAA;AAAA;AAAA;IAC/B;IAEUuB,MAAA;IAGJ,MAAAC,MAAA,GAAS,IAAIC,OAAA;IACb,MAAAC,iBAAA,GAAoB,IAAIC,OAAA;IAC9B,IAAIC,MAAA,GAAsC;IAE1C,IAAIzC,OAAA,CAAQI,MAAA,EAAQ;MAElB,MAAMsC,SAAA,GAAY,IAAIC,WAAA,CAAY,EAAEC,MAAA,CAAOR,MAAM;MAMjD,MAAMS,gBAAA,GACJjC,WAAA,IAAe,IAAI,KAAKH,cAAA,GAAiB,IAAI,IAAI,MAAMC,aAAA,GAAgB,IAAI,MAAMC,UAAA,GAAa,IAAI,IAAI;MAIxG,MAAMmC,cAAA,GAAiBhB,cAAA,GAAiBjB,SAAA,IAAasB,cAAA,GAAiB,IAAI,KAAK;MACzE,MAAAY,MAAA,GAAS,IAAIC,QAAA,CAAS,IAAIC,WAAA,CAAYP,SAAA,CAAUQ,MAAA,GAASL,gBAAA,GAAmBC,cAAc,CAAC;MACjG,IAAIK,UAAA,CAAWJ,MAAA,CAAOK,MAAM,EAAEC,GAAA,CAAIX,SAAA,EAAW,CAAC;MAE9C,IAAIY,OAAA,GAAUZ,SAAA,CAAUQ,MAAA;MACpB,IAAAK,OAAA,GAAUb,SAAA,CAAUQ,MAAA,GAASL,gBAAA;MACjC,IAAIW,eAAA,GAAkB;MACtB,KAAKC,cAAA,CAAe3D,MAAA,EAAQ,UAAUoB,IAAA,EAAMC,QAAA,EAAU;QAC9C,MAAAG,QAAA,GAAWH,QAAA,CAASI,YAAA,CAAa,UAAU;QAC3C,MAAAC,OAAA,GAAUL,QAAA,CAASI,YAAA,CAAa,QAAQ;QACxC,MAAAE,GAAA,GAAMN,QAAA,CAASI,YAAA,CAAa,IAAI;QAChC,MAAAG,MAAA,GAASP,QAAA,CAASI,YAAA,CAAa,OAAO;QACtC,MAAAI,OAAA,GAAUR,QAAA,CAASS,QAAA;QAEPW,iBAAA,CAAAmB,eAAA,CAAgBxC,IAAA,CAAKyC,WAAW;QAElD,SAASC,CAAA,GAAI,GAAGC,CAAA,GAAIvC,QAAA,CAASO,KAAA,EAAO+B,CAAA,GAAIC,CAAA,EAAGD,CAAA,IAAK;UACvCvB,MAAA,CAAAyB,CAAA,GAAIxC,QAAA,CAASyC,IAAA,CAAKH,CAAC;UACnBvB,MAAA,CAAA2B,CAAA,GAAI1C,QAAA,CAAS2C,IAAA,CAAKL,CAAC;UACnBvB,MAAA,CAAA6B,CAAA,GAAI5C,QAAA,CAAS6C,IAAA,CAAKP,CAAC;UAEnBvB,MAAA,CAAA+B,YAAA,CAAalD,IAAA,CAAKyC,WAAW;UAGpCZ,MAAA,CAAOsB,UAAA,CAAWf,OAAA,EAASjB,MAAA,CAAOyB,CAAA,EAAG9D,OAAA,CAAQM,YAAY;UAC9CgD,OAAA;UAEXP,MAAA,CAAOsB,UAAA,CAAWf,OAAA,EAASjB,MAAA,CAAO2B,CAAA,EAAGhE,OAAA,CAAQM,YAAY;UAC9CgD,OAAA;UAEXP,MAAA,CAAOsB,UAAA,CAAWf,OAAA,EAASjB,MAAA,CAAO6B,CAAA,EAAGlE,OAAA,CAAQM,YAAY;UAC9CgD,OAAA;UAGX,IAAI7C,cAAA,EAAgB;YAClB,IAAIe,OAAA,IAAW,MAAM;cACZa,MAAA,CAAAyB,CAAA,GAAItC,OAAA,CAAQuC,IAAA,CAAKH,CAAC;cAClBvB,MAAA,CAAA2B,CAAA,GAAIxC,OAAA,CAAQyC,IAAA,CAAKL,CAAC;cAClBvB,MAAA,CAAA6B,CAAA,GAAI1C,OAAA,CAAQ2C,IAAA,CAAKP,CAAC;cAElBvB,MAAA,CAAAiC,YAAA,CAAa/B,iBAAiB,EAAEgC,SAAA,CAAU;cAEjDxB,MAAA,CAAOsB,UAAA,CAAWf,OAAA,EAASjB,MAAA,CAAOyB,CAAA,EAAG9D,OAAA,CAAQM,YAAY;cAC9CgD,OAAA;cAEXP,MAAA,CAAOsB,UAAA,CAAWf,OAAA,EAASjB,MAAA,CAAO2B,CAAA,EAAGhE,OAAA,CAAQM,YAAY;cAC9CgD,OAAA;cAEXP,MAAA,CAAOsB,UAAA,CAAWf,OAAA,EAASjB,MAAA,CAAO6B,CAAA,EAAGlE,OAAA,CAAQM,YAAY;cAC9CgD,OAAA;YAAA,OACN;cACLP,MAAA,CAAOsB,UAAA,CAAWf,OAAA,EAAS,GAAGtD,OAAA,CAAQM,YAAY;cACvCgD,OAAA;cAEXP,MAAA,CAAOsB,UAAA,CAAWf,OAAA,EAAS,GAAGtD,OAAA,CAAQM,YAAY;cACvCgD,OAAA;cAEXP,MAAA,CAAOsB,UAAA,CAAWf,OAAA,EAAS,GAAGtD,OAAA,CAAQM,YAAY;cACvCgD,OAAA;YACb;UACF;UAGA,IAAI3C,UAAA,EAAY;YACd,IAAIc,GAAA,IAAO,MAAM;cACfsB,MAAA,CAAOsB,UAAA,CAAWf,OAAA,EAAS7B,GAAA,CAAIsC,IAAA,CAAKH,CAAC,GAAG5D,OAAA,CAAQM,YAAY;cACjDgD,OAAA;cAEXP,MAAA,CAAOsB,UAAA,CAAWf,OAAA,EAAS7B,GAAA,CAAIwC,IAAA,CAAKL,CAAC,GAAG5D,OAAA,CAAQM,YAAY;cACjDgD,OAAA;YAAA,WACF,CAAC3C,UAAA,EAAY;cACtBoC,MAAA,CAAOsB,UAAA,CAAWf,OAAA,EAAS,GAAGtD,OAAA,CAAQM,YAAY;cACvCgD,OAAA;cAEXP,MAAA,CAAOsB,UAAA,CAAWf,OAAA,EAAS,GAAGtD,OAAA,CAAQM,YAAY;cACvCgD,OAAA;YACb;UACF;UAGA,IAAI5C,aAAA,EAAe;YACjB,IAAIgB,MAAA,IAAU,MAAM;cACXqB,MAAA,CAAAyB,QAAA,CAASlB,OAAA,EAAStB,IAAA,CAAKC,KAAA,CAAMP,MAAA,CAAOqC,IAAA,CAAKH,CAAC,IAAI,GAAG,CAAC;cAC9CN,OAAA;cAEJP,MAAA,CAAAyB,QAAA,CAASlB,OAAA,EAAStB,IAAA,CAAKC,KAAA,CAAMP,MAAA,CAAOuC,IAAA,CAAKL,CAAC,IAAI,GAAG,CAAC;cAC9CN,OAAA;cAEJP,MAAA,CAAAyB,QAAA,CAASlB,OAAA,EAAStB,IAAA,CAAKC,KAAA,CAAMP,MAAA,CAAOyC,IAAA,CAAKP,CAAC,IAAI,GAAG,CAAC;cAC9CN,OAAA;YAAA,OACN;cACEP,MAAA,CAAAyB,QAAA,CAASlB,OAAA,EAAS,GAAG;cACjBA,OAAA;cAEJP,MAAA,CAAAyB,QAAA,CAASlB,OAAA,EAAS,GAAG;cACjBA,OAAA;cAEJP,MAAA,CAAAyB,QAAA,CAASlB,OAAA,EAAS,GAAG;cACjBA,OAAA;YACb;UACF;QACF;QAEA,IAAIxB,cAAA,EAAgB;UAGlB,IAAIH,OAAA,KAAY,MAAM;YACX,SAAAiC,CAAA,GAAI,GAAGC,CAAA,GAAIlC,OAAA,CAAQE,KAAA,EAAO+B,CAAA,GAAIC,CAAA,EAAGD,CAAA,IAAK,GAAG;cACzCb,MAAA,CAAAyB,QAAA,CAASjB,OAAA,EAAS,CAAC;cACfA,OAAA;cAEJR,MAAA,CAAA0B,SAAA,CAAUlB,OAAA,EAAS5B,OAAA,CAAQoC,IAAA,CAAKH,CAAA,GAAI,CAAC,IAAIJ,eAAA,EAAiBxD,OAAA,CAAQM,YAAY;cAC1EiD,OAAA,IAAApB,cAAA;cAEJY,MAAA,CAAA0B,SAAA,CAAUlB,OAAA,EAAS5B,OAAA,CAAQoC,IAAA,CAAKH,CAAA,GAAI,CAAC,IAAIJ,eAAA,EAAiBxD,OAAA,CAAQM,YAAY;cAC1EiD,OAAA,IAAApB,cAAA;cAEJY,MAAA,CAAA0B,SAAA,CAAUlB,OAAA,EAAS5B,OAAA,CAAQoC,IAAA,CAAKH,CAAA,GAAI,CAAC,IAAIJ,eAAA,EAAiBxD,OAAA,CAAQM,YAAY;cAC1EiD,OAAA,IAAApB,cAAA;YACb;UAAA,OACK;YACI,SAAAyB,CAAA,GAAI,GAAGC,CAAA,GAAIvC,QAAA,CAASO,KAAA,EAAO+B,CAAA,GAAIC,CAAA,EAAGD,CAAA,IAAK,GAAG;cAC1Cb,MAAA,CAAAyB,QAAA,CAASjB,OAAA,EAAS,CAAC;cACfA,OAAA;cAEXR,MAAA,CAAO0B,SAAA,CAAUlB,OAAA,EAASC,eAAA,GAAkBI,CAAA,EAAG5D,OAAA,CAAQM,YAAY;cACxDiD,OAAA,IAAApB,cAAA;cAEXY,MAAA,CAAO0B,SAAA,CAAUlB,OAAA,EAASC,eAAA,GAAkBI,CAAA,GAAI,GAAG5D,OAAA,CAAQM,YAAY;cAC5DiD,OAAA,IAAApB,cAAA;cAEXY,MAAA,CAAO0B,SAAA,CAAUlB,OAAA,EAASC,eAAA,GAAkBI,CAAA,GAAI,GAAG5D,OAAA,CAAQM,YAAY;cAC5DiD,OAAA,IAAApB,cAAA;YACb;UACF;QACF;QAIAqB,eAAA,IAAmBlC,QAAA,CAASO,KAAA;MAAA,CAC7B;MAEDY,MAAA,GAASM,MAAA,CAAOK,MAAA;IAAA,OACX;MAGL,IAAII,eAAA,GAAkB;MACtB,IAAIkB,UAAA,GAAa;MACjB,IAAIC,QAAA,GAAW;MAEf,KAAKlB,cAAA,CAAe3D,MAAA,EAAQ,UAAUoB,IAAA,EAAMC,QAAA,EAAU;QAC9C,MAAAG,QAAA,GAAWH,QAAA,CAASI,YAAA,CAAa,UAAU;QAC3C,MAAAC,OAAA,GAAUL,QAAA,CAASI,YAAA,CAAa,QAAQ;QACxC,MAAAE,GAAA,GAAMN,QAAA,CAASI,YAAA,CAAa,IAAI;QAChC,MAAAG,MAAA,GAASP,QAAA,CAASI,YAAA,CAAa,OAAO;QACtC,MAAAI,OAAA,GAAUR,QAAA,CAASS,QAAA;QAEPW,iBAAA,CAAAmB,eAAA,CAAgBxC,IAAA,CAAKyC,WAAW;QAGlD,SAASC,CAAA,GAAI,GAAGC,CAAA,GAAIvC,QAAA,CAASO,KAAA,EAAO+B,CAAA,GAAIC,CAAA,EAAGD,CAAA,IAAK;UACvCvB,MAAA,CAAAyB,CAAA,GAAIxC,QAAA,CAASyC,IAAA,CAAKH,CAAC;UACnBvB,MAAA,CAAA2B,CAAA,GAAI1C,QAAA,CAAS2C,IAAA,CAAKL,CAAC;UACnBvB,MAAA,CAAA6B,CAAA,GAAI5C,QAAA,CAAS6C,IAAA,CAAKP,CAAC;UAEnBvB,MAAA,CAAA+B,YAAA,CAAalD,IAAA,CAAKyC,WAAW;UAGpC,IAAIiB,IAAA,GAAOvC,MAAA,CAAOyB,CAAA,GAAI,MAAMzB,MAAA,CAAO2B,CAAA,GAAI,MAAM3B,MAAA,CAAO6B,CAAA;UAGpD,IAAIzD,cAAA,EAAgB;YAClB,IAAIe,OAAA,IAAW,MAAM;cACZa,MAAA,CAAAyB,CAAA,GAAItC,OAAA,CAAQuC,IAAA,CAAKH,CAAC;cAClBvB,MAAA,CAAA2B,CAAA,GAAIxC,OAAA,CAAQyC,IAAA,CAAKL,CAAC;cAClBvB,MAAA,CAAA6B,CAAA,GAAI1C,OAAA,CAAQ2C,IAAA,CAAKP,CAAC;cAElBvB,MAAA,CAAAiC,YAAA,CAAa/B,iBAAiB,EAAEgC,SAAA,CAAU;cAEjDK,IAAA,IAAQ,MAAMvC,MAAA,CAAOyB,CAAA,GAAI,MAAMzB,MAAA,CAAO2B,CAAA,GAAI,MAAM3B,MAAA,CAAO6B,CAAA;YAAA,OAClD;cACGU,IAAA;YACV;UACF;UAGA,IAAIjE,UAAA,EAAY;YACd,IAAIc,GAAA,IAAO,MAAM;cACPmD,IAAA,UAAMnD,GAAA,CAAIsC,IAAA,CAAKH,CAAC,IAAI,MAAMnC,GAAA,CAAIwC,IAAA,CAAKL,CAAC;YAAA,WACnCjD,UAAA,EAAY;cACbiE,IAAA;YACV;UACF;UAGA,IAAIlE,aAAA,EAAe;YACjB,IAAIgB,MAAA,IAAU,MAAM;cAEhBkD,IAAA,UACA5C,IAAA,CAAKC,KAAA,CAAMP,MAAA,CAAOqC,IAAA,CAAKH,CAAC,IAAI,GAAG,IAC/B,MACA5B,IAAA,CAAKC,KAAA,CAAMP,MAAA,CAAOuC,IAAA,CAAKL,CAAC,IAAI,GAAG,IAC/B,MACA5B,IAAA,CAAKC,KAAA,CAAMP,MAAA,CAAOyC,IAAA,CAAKP,CAAC,IAAI,GAAG;YAAA,OAC5B;cACGgB,IAAA;YACV;UACF;UAEAF,UAAA,IAAcE,IAAA,GAAO;QACvB;QAGA,IAAI9C,cAAA,EAAgB;UAClB,IAAIH,OAAA,KAAY,MAAM;YACX,SAAAiC,CAAA,GAAI,GAAGC,CAAA,GAAIlC,OAAA,CAAQE,KAAA,EAAO+B,CAAA,GAAIC,CAAA,EAAGD,CAAA,IAAK,GAAG;cAChDe,QAAA,IAAY,KAAKhD,OAAA,CAAQoC,IAAA,CAAKH,CAAA,GAAI,CAAC,IAAIJ,eAAA;cACvCmB,QAAA,IAAY,IAAIhD,OAAA,CAAQoC,IAAA,CAAKH,CAAA,GAAI,CAAC,IAAIJ,eAAA;cACtCmB,QAAA,IAAY,IAAIhD,OAAA,CAAQoC,IAAA,CAAKH,CAAA,GAAI,CAAC,IAAIJ,eAAA;AAAA;YACxC;UAAA,OACK;YACI,SAAAI,CAAA,GAAI,GAAGC,CAAA,GAAIvC,QAAA,CAASO,KAAA,EAAO+B,CAAA,GAAIC,CAAA,EAAGD,CAAA,IAAK,GAAG;cACjDe,QAAA,IAAY,KAAKnB,eAAA,GAAkBI,CAAA,IAAKJ,eAAA,GAAkBI,CAAA,GAAI,KAAKJ,eAAA,GAAkBI,CAAA,GAAI;AAAA;YAC3F;UACF;UAEA/C,SAAA,IAAac,OAAA,GAAUA,OAAA,CAAQE,KAAA,GAAQ,IAAIP,QAAA,CAASO,KAAA,GAAQ;QAC9D;QAEA2B,eAAA,IAAmBlC,QAAA,CAASO,KAAA;MAAA,CAC7B;MAEDY,MAAA,GAAS,GAAGL,MAAA,GAASsC,UAAA,GAAa5C,cAAA,GAAiB,GAAG6C,QAAA;AAAA,IAAe;IACvE;IAEI,WAAO5E,MAAA,KAAW,YAAY;MACV8E,qBAAA,OAAM9E,MAAA,IAAUA,MAAA,CAAO,OAAO0C,MAAA,KAAW,WAAWA,MAAA,GAAS,EAAE,CAAC;IACxF;IAEO,OAAAA,MAAA;EACT;EAAA;EAGQgB,eAAe3D,MAAA,EAAkBgF,EAAA,EAA0D;IAC1FhF,MAAA,CAAAgB,QAAA,CAAS,UAAUC,KAAA,EAAO;MAC3B,IAAAA,KAAA,YAAiBC,IAAA,IAAQD,KAAA,CAAME,MAAA,EAAQ;QACzC,MAAMC,IAAA,GAAOH,KAAA;QACb,MAAMI,QAAA,GAAWD,IAAA,CAAKC,QAAA;QAElB,KAACA,QAAA,CAASC,gBAAA,EAAkB;UACxB,UAAIC,KAAA,CAAM,kEAAkE;QACpF;QAEI,IAAAF,QAAA,CAAS4D,YAAA,CAAa,UAAU,GAAG;UACrCD,EAAA,CAAG5D,IAAA,EAAMC,QAAQ;QACnB;MACF;IAAA,CACD;EACH;AACF", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}