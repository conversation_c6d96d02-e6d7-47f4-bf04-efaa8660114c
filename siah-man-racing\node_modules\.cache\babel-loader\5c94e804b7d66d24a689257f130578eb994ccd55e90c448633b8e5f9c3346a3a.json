{"ast": null, "code": "var __defProp = Object.defineProperty;\nvar __defNormalProp = (obj, key, value) => key in obj ? __defProp(obj, key, {\n  enumerable: true,\n  configurable: true,\n  writable: true,\n  value\n}) : obj[key] = value;\nvar __publicField = (obj, key, value) => {\n  __defNormalProp(obj, typeof key !== \"symbol\" ? key + \"\" : key, value);\n  return value;\n};\nimport { Object3D, Mesh, SphereGeometry, MeshBasicMaterial } from \"three\";\nimport { GLTFLoader } from \"../loaders/GLTFLoader.js\";\nimport { MotionControllerConstants, fetchProfile, MotionController } from \"../libs/MotionControllers.js\";\nconst DEFAULT_PROFILES_PATH = \"https://cdn.jsdelivr.net/npm/@webxr-input-profiles/assets@1.0/dist/profiles\";\nconst DEFAULT_PROFILE = \"generic-trigger\";\nconst applyEnvironmentMap = (envMap, obj) => {\n  obj.traverse(child => {\n    if (child instanceof Mesh && \"envMap\" in child.material) {\n      child.material.envMap = envMap;\n      child.material.needsUpdate = true;\n    }\n  });\n};\nclass XRControllerModel extends Object3D {\n  constructor() {\n    super();\n    __publicField(this, \"envMap\");\n    __publicField(this, \"motionController\");\n    this.motionController = null;\n    this.envMap = null;\n  }\n  setEnvironmentMap(envMap) {\n    if (this.envMap == envMap) {\n      return this;\n    }\n    this.envMap = envMap;\n    applyEnvironmentMap(this.envMap, this);\n    return this;\n  }\n  /**\n   * Polls data from the XRInputSource and updates the model's components to match\n   * the real world data\n   */\n  updateMatrixWorld(force) {\n    super.updateMatrixWorld(force);\n    if (!this.motionController) return;\n    this.motionController.updateFromGamepad();\n    Object.values(this.motionController.components).forEach(component => {\n      Object.values(component.visualResponses).forEach(visualResponse => {\n        const {\n          valueNode,\n          minNode,\n          maxNode,\n          value,\n          valueNodeProperty\n        } = visualResponse;\n        if (!valueNode) return;\n        if (valueNodeProperty === MotionControllerConstants.VisualResponseProperty.VISIBILITY && typeof value === \"boolean\") {\n          valueNode.visible = value;\n        } else if (valueNodeProperty === MotionControllerConstants.VisualResponseProperty.TRANSFORM && minNode && maxNode && typeof value === \"number\") {\n          valueNode.quaternion.slerpQuaternions(minNode.quaternion, maxNode.quaternion, value);\n          valueNode.position.lerpVectors(minNode.position, maxNode.position, value);\n        }\n      });\n    });\n  }\n}\nfunction findNodes(motionController, scene) {\n  Object.values(motionController.components).forEach(component => {\n    const {\n      type,\n      touchPointNodeName,\n      visualResponses\n    } = component;\n    if (type === MotionControllerConstants.ComponentType.TOUCHPAD && touchPointNodeName) {\n      component.touchPointNode = scene.getObjectByName(touchPointNodeName);\n      if (component.touchPointNode) {\n        const sphereGeometry = new SphereGeometry(1e-3);\n        const material = new MeshBasicMaterial({\n          color: 255\n        });\n        const sphere = new Mesh(sphereGeometry, material);\n        component.touchPointNode.add(sphere);\n      } else {\n        console.warn(`Could not find touch dot, ${component.touchPointNodeName}, in touchpad component ${component.id}`);\n      }\n    }\n    Object.values(visualResponses).forEach(visualResponse => {\n      const {\n        valueNodeName,\n        minNodeName,\n        maxNodeName,\n        valueNodeProperty\n      } = visualResponse;\n      if (valueNodeProperty === MotionControllerConstants.VisualResponseProperty.TRANSFORM && minNodeName && maxNodeName) {\n        visualResponse.minNode = scene.getObjectByName(minNodeName);\n        visualResponse.maxNode = scene.getObjectByName(maxNodeName);\n        if (!visualResponse.minNode) {\n          console.warn(`Could not find ${minNodeName} in the model`);\n          return;\n        }\n        if (!visualResponse.maxNode) {\n          console.warn(`Could not find ${maxNodeName} in the model`);\n          return;\n        }\n      }\n      visualResponse.valueNode = scene.getObjectByName(valueNodeName);\n      if (!visualResponse.valueNode) {\n        console.warn(`Could not find ${valueNodeName} in the model`);\n      }\n    });\n  });\n}\nfunction addAssetSceneToControllerModel(controllerModel, scene) {\n  findNodes(controllerModel.motionController, scene);\n  if (controllerModel.envMap) {\n    applyEnvironmentMap(controllerModel.envMap, scene);\n  }\n  controllerModel.add(scene);\n}\nclass XRControllerModelFactory {\n  constructor(gltfLoader = null) {\n    __publicField(this, \"gltfLoader\");\n    __publicField(this, \"path\");\n    __publicField(this, \"_assetCache\");\n    this.gltfLoader = gltfLoader;\n    this.path = DEFAULT_PROFILES_PATH;\n    this._assetCache = {};\n    if (!this.gltfLoader) {\n      this.gltfLoader = new GLTFLoader();\n    }\n  }\n  createControllerModel(controller) {\n    const controllerModel = new XRControllerModel();\n    let scene = null;\n    const onConnected = event => {\n      const xrInputSource = event.data;\n      if (xrInputSource.targetRayMode !== \"tracked-pointer\" || !xrInputSource.gamepad) return;\n      fetchProfile(xrInputSource, this.path, DEFAULT_PROFILE).then(({\n        profile,\n        assetPath\n      }) => {\n        if (!assetPath) {\n          throw new Error(\"no asset path\");\n        }\n        controllerModel.motionController = new MotionController(xrInputSource, profile, assetPath);\n        const assetUrl = controllerModel.motionController.assetUrl;\n        const cachedAsset = this._assetCache[assetUrl];\n        if (cachedAsset) {\n          scene = cachedAsset.scene.clone();\n          addAssetSceneToControllerModel(controllerModel, scene);\n        } else {\n          if (!this.gltfLoader) {\n            throw new Error(\"GLTFLoader not set.\");\n          }\n          this.gltfLoader.setPath(\"\");\n          this.gltfLoader.load(controllerModel.motionController.assetUrl, asset => {\n            if (!controllerModel.motionController) {\n              console.warn(\"motionController gone while gltf load, bailing...\");\n              return;\n            }\n            this._assetCache[assetUrl] = asset;\n            scene = asset.scene.clone();\n            addAssetSceneToControllerModel(controllerModel, scene);\n          }, () => {}, () => {\n            throw new Error(`Asset ${assetUrl} missing or malformed.`);\n          });\n        }\n      }).catch(err => {\n        console.warn(err);\n      });\n    };\n    controller.addEventListener(\"connected\", onConnected);\n    const onDisconnected = () => {\n      controller.removeEventListener(\"connected\", onConnected);\n      controller.removeEventListener(\"disconnected\", onDisconnected);\n      controllerModel.motionController = null;\n      if (scene) {\n        controllerModel.remove(scene);\n      }\n      scene = null;\n    };\n    controller.addEventListener(\"disconnected\", onDisconnected);\n    return controllerModel;\n  }\n}\nexport { XRControllerModelFactory };", "map": {"version": 3, "names": ["DEFAULT_PROFILES_PATH", "DEFAULT_PROFILE", "applyEnvironmentMap", "envMap", "obj", "traverse", "child", "<PERSON><PERSON>", "material", "needsUpdate", "XRControllerModel", "Object3D", "constructor", "__publicField", "motionController", "setEnvironmentMap", "updateMatrixWorld", "force", "updateFromGamepad", "Object", "values", "components", "for<PERSON>ach", "component", "visualResponses", "visualResponse", "valueNode", "minNode", "maxNode", "value", "valueNodeProperty", "MotionControllerConstants", "VisualResponseProperty", "VISIBILITY", "visible", "TRANSFORM", "quaternion", "slerpQuaternions", "position", "lerpVectors", "findNodes", "scene", "type", "touchPointNodeName", "ComponentType", "TOUCHPAD", "touchPointNode", "getObjectByName", "sphereGeometry", "SphereGeometry", "MeshBasicMaterial", "color", "sphere", "add", "console", "warn", "id", "valueNodeName", "minNodeName", "maxNodeName", "addAssetSceneToControllerModel", "controllerModel", "XRControllerModelFactory", "gltfLoader", "path", "_assetCache", "GLTFLoader", "createControllerModel", "controller", "onConnected", "event", "xrInputSource", "data", "targetRayMode", "gamepad", "fetchProfile", "then", "profile", "assetPath", "Error", "MotionController", "assetUrl", "cachedAsset", "clone", "set<PERSON>ath", "load", "asset", "catch", "err", "addEventListener", "onDisconnected", "removeEventListener", "remove"], "sources": ["C:\\Users\\<USER>\\Documents\\Apps_Creations\\SiahMan_Racing4Speed_WebGame\\siah-man-racing\\node_modules\\src\\webxr\\XRControllerModelFactory.ts"], "sourcesContent": ["import { Mesh, Object3D, SphereGeometry, MeshBasicMaterial } from 'three'\nimport type { Texture, Group } from 'three'\nimport { GLTFLoader } from '../loaders/GLTFLoader'\nimport { fetchProfile, MotionController, MotionControllerConstants } from '../libs/MotionControllers'\n\nconst DEFAULT_PROFILES_PATH = 'https://cdn.jsdelivr.net/npm/@webxr-input-profiles/assets@1.0/dist/profiles'\nconst DEFAULT_PROFILE = 'generic-trigger'\n\nconst applyEnvironmentMap = (envMap: Texture, obj: Object3D): void => {\n  obj.traverse((child) => {\n    if (child instanceof Mesh && 'envMap' in child.material) {\n      child.material.envMap = envMap\n      child.material.needsUpdate = true\n    }\n  })\n}\n\nclass XRControllerModel extends Object3D {\n  envMap: Texture | null\n  motionController: MotionController | null\n  constructor() {\n    super()\n\n    this.motionController = null\n    this.envMap = null\n  }\n\n  setEnvironmentMap(envMap: Texture): XRControllerModel {\n    if (this.envMap == envMap) {\n      return this\n    }\n\n    this.envMap = envMap\n    applyEnvironmentMap(this.envMap, this)\n\n    return this\n  }\n\n  /**\n   * Polls data from the XRInputSource and updates the model's components to match\n   * the real world data\n   */\n  updateMatrixWorld(force: boolean): void {\n    super.updateMatrixWorld(force)\n\n    if (!this.motionController) return\n\n    // Cause the MotionController to poll the Gamepad for data\n    this.motionController.updateFromGamepad()\n\n    // Update the 3D model to reflect the button, thumbstick, and touchpad state\n    Object.values(this.motionController.components).forEach((component) => {\n      // Update node data based on the visual responses' current states\n      Object.values(component.visualResponses).forEach((visualResponse) => {\n        const { valueNode, minNode, maxNode, value, valueNodeProperty } = visualResponse\n\n        // Skip if the visual response node is not found. No error is needed,\n        // because it will have been reported at load time.\n        if (!valueNode) return\n\n        // Calculate the new properties based on the weight supplied\n        if (\n          valueNodeProperty === MotionControllerConstants.VisualResponseProperty.VISIBILITY &&\n          typeof value === 'boolean'\n        ) {\n          valueNode.visible = value\n        } else if (\n          valueNodeProperty === MotionControllerConstants.VisualResponseProperty.TRANSFORM &&\n          minNode &&\n          maxNode &&\n          typeof value === 'number'\n        ) {\n          valueNode.quaternion.slerpQuaternions(minNode.quaternion, maxNode.quaternion, value)\n\n          valueNode.position.lerpVectors(minNode.position, maxNode.position, value)\n        }\n      })\n    })\n  }\n}\n\n/**\n * Walks the model's tree to find the nodes needed to animate the components and\n * saves them to the motionContoller components for use in the frame loop. When\n * touchpads are found, attaches a touch dot to them.\n */\nfunction findNodes(motionController: MotionController, scene: Object3D): void {\n  // Loop through the components and find the nodes needed for each components' visual responses\n  Object.values(motionController.components).forEach((component) => {\n    const { type, touchPointNodeName, visualResponses } = component\n\n    if (type === MotionControllerConstants.ComponentType.TOUCHPAD && touchPointNodeName) {\n      component.touchPointNode = scene.getObjectByName(touchPointNodeName)\n      if (component.touchPointNode) {\n        // Attach a touch dot to the touchpad.\n        const sphereGeometry = new SphereGeometry(0.001)\n        const material = new MeshBasicMaterial({ color: 0x0000ff })\n        const sphere = new Mesh(sphereGeometry, material)\n        component.touchPointNode.add(sphere)\n      } else {\n        console.warn(`Could not find touch dot, ${component.touchPointNodeName}, in touchpad component ${component.id}`)\n      }\n    }\n\n    // Loop through all the visual responses to be applied to this component\n    Object.values(visualResponses).forEach((visualResponse) => {\n      const { valueNodeName, minNodeName, maxNodeName, valueNodeProperty } = visualResponse\n\n      // If animating a transform, find the two nodes to be interpolated between.\n      if (\n        valueNodeProperty === MotionControllerConstants.VisualResponseProperty.TRANSFORM &&\n        minNodeName &&\n        maxNodeName\n      ) {\n        visualResponse.minNode = scene.getObjectByName(minNodeName)\n        visualResponse.maxNode = scene.getObjectByName(maxNodeName)\n\n        // If the extents cannot be found, skip this animation\n        if (!visualResponse.minNode) {\n          console.warn(`Could not find ${minNodeName} in the model`)\n          return\n        }\n\n        if (!visualResponse.maxNode) {\n          console.warn(`Could not find ${maxNodeName} in the model`)\n          return\n        }\n      }\n\n      // If the target node cannot be found, skip this animation\n      visualResponse.valueNode = scene.getObjectByName(valueNodeName)\n      if (!visualResponse.valueNode) {\n        console.warn(`Could not find ${valueNodeName} in the model`)\n      }\n    })\n  })\n}\n\nfunction addAssetSceneToControllerModel(controllerModel: XRControllerModel, scene: Object3D): void {\n  // Find the nodes needed for animation and cache them on the motionController.\n  findNodes(controllerModel.motionController!, scene)\n\n  // Apply any environment map that the mesh already has set.\n  if (controllerModel.envMap) {\n    applyEnvironmentMap(controllerModel.envMap, scene)\n  }\n\n  // Add the glTF scene to the controllerModel.\n  controllerModel.add(scene)\n}\n\nclass XRControllerModelFactory {\n  gltfLoader: GLTFLoader\n  path: string\n  private _assetCache: Record<string, { scene: Object3D } | undefined>\n  constructor(gltfLoader: GLTFLoader = null!) {\n    this.gltfLoader = gltfLoader\n    this.path = DEFAULT_PROFILES_PATH\n    this._assetCache = {}\n\n    // If a GLTFLoader wasn't supplied to the constructor create a new one.\n    if (!this.gltfLoader) {\n      this.gltfLoader = new GLTFLoader()\n    }\n  }\n\n  createControllerModel(controller: Group): XRControllerModel {\n    const controllerModel = new XRControllerModel()\n    let scene: Object3D | null = null\n\n    const onConnected = (event: any): void => {\n      const xrInputSource = event.data\n\n      if (xrInputSource.targetRayMode !== 'tracked-pointer' || !xrInputSource.gamepad) return\n\n      fetchProfile(xrInputSource, this.path, DEFAULT_PROFILE)\n        .then(({ profile, assetPath }) => {\n          if (!assetPath) {\n            throw new Error('no asset path')\n          }\n\n          controllerModel.motionController = new MotionController(xrInputSource, profile, assetPath)\n\n          const assetUrl = controllerModel.motionController.assetUrl\n\n          const cachedAsset = this._assetCache[assetUrl]\n          if (cachedAsset) {\n            scene = cachedAsset.scene.clone()\n\n            addAssetSceneToControllerModel(controllerModel, scene)\n          } else {\n            if (!this.gltfLoader) {\n              throw new Error('GLTFLoader not set.')\n            }\n\n            this.gltfLoader.setPath('')\n            this.gltfLoader.load(\n              controllerModel.motionController.assetUrl,\n              (asset: { scene: Object3D }) => {\n                if (!controllerModel.motionController) {\n                  console.warn('motionController gone while gltf load, bailing...')\n                  return\n                }\n\n                this._assetCache[assetUrl] = asset\n\n                scene = asset.scene.clone()\n\n                addAssetSceneToControllerModel(controllerModel, scene)\n              },\n              () => {},\n              () => {\n                throw new Error(`Asset ${assetUrl} missing or malformed.`)\n              },\n            )\n          }\n        })\n        .catch((err) => {\n          console.warn(err)\n        })\n    }\n\n    controller.addEventListener('connected', onConnected)\n\n    const onDisconnected = (): void => {\n      controller.removeEventListener('connected', onConnected)\n      controller.removeEventListener('disconnected', onDisconnected)\n      controllerModel.motionController = null\n      if (scene) {\n        controllerModel.remove(scene)\n      }\n      scene = null\n    }\n\n    controller.addEventListener('disconnected', onDisconnected)\n\n    return controllerModel\n  }\n}\n\nexport { XRControllerModelFactory }\n"], "mappings": ";;;;;;;;;;;;;;AAKA,MAAMA,qBAAA,GAAwB;AAC9B,MAAMC,eAAA,GAAkB;AAExB,MAAMC,mBAAA,GAAsBA,CAACC,MAAA,EAAiBC,GAAA,KAAwB;EAChEA,GAAA,CAAAC,QAAA,CAAUC,KAAA,IAAU;IACtB,IAAIA,KAAA,YAAiBC,IAAA,IAAQ,YAAYD,KAAA,CAAME,QAAA,EAAU;MACvDF,KAAA,CAAME,QAAA,CAASL,MAAA,GAASA,MAAA;MACxBG,KAAA,CAAME,QAAA,CAASC,WAAA,GAAc;IAC/B;EAAA,CACD;AACH;AAEA,MAAMC,iBAAA,SAA0BC,QAAA,CAAS;EAGvCC,YAAA,EAAc;IACN;IAHRC,aAAA;IACAA,aAAA;IAIE,KAAKC,gBAAA,GAAmB;IACxB,KAAKX,MAAA,GAAS;EAChB;EAEAY,kBAAkBZ,MAAA,EAAoC;IAChD,SAAKA,MAAA,IAAUA,MAAA,EAAQ;MAClB;IACT;IAEA,KAAKA,MAAA,GAASA,MAAA;IACMD,mBAAA,MAAKC,MAAA,EAAQ,IAAI;IAE9B;EACT;EAAA;AAAA;AAAA;AAAA;EAMAa,kBAAkBC,KAAA,EAAsB;IACtC,MAAMD,iBAAA,CAAkBC,KAAK;IAE7B,IAAI,CAAC,KAAKH,gBAAA,EAAkB;IAG5B,KAAKA,gBAAA,CAAiBI,iBAAA;IAGtBC,MAAA,CAAOC,MAAA,CAAO,KAAKN,gBAAA,CAAiBO,UAAU,EAAEC,OAAA,CAASC,SAAA,IAAc;MAErEJ,MAAA,CAAOC,MAAA,CAAOG,SAAA,CAAUC,eAAe,EAAEF,OAAA,CAASG,cAAA,IAAmB;QACnE,MAAM;UAAEC,SAAA;UAAWC,OAAA;UAASC,OAAA;UAASC,KAAA;UAAOC;QAAsB,IAAAL,cAAA;QAIlE,IAAI,CAACC,SAAA,EAAW;QAGhB,IACEI,iBAAA,KAAsBC,yBAAA,CAA0BC,sBAAA,CAAuBC,UAAA,IACvE,OAAOJ,KAAA,KAAU,WACjB;UACAH,SAAA,CAAUQ,OAAA,GAAUL,KAAA;QAAA,WAEpBC,iBAAA,KAAsBC,yBAAA,CAA0BC,sBAAA,CAAuBG,SAAA,IACvER,OAAA,IACAC,OAAA,IACA,OAAOC,KAAA,KAAU,UACjB;UACAH,SAAA,CAAUU,UAAA,CAAWC,gBAAA,CAAiBV,OAAA,CAAQS,UAAA,EAAYR,OAAA,CAAQQ,UAAA,EAAYP,KAAK;UAEnFH,SAAA,CAAUY,QAAA,CAASC,WAAA,CAAYZ,OAAA,CAAQW,QAAA,EAAUV,OAAA,CAAQU,QAAA,EAAUT,KAAK;QAC1E;MAAA,CACD;IAAA,CACF;EACH;AACF;AAOA,SAASW,UAAU1B,gBAAA,EAAoC2B,KAAA,EAAuB;EAE5EtB,MAAA,CAAOC,MAAA,CAAON,gBAAA,CAAiBO,UAAU,EAAEC,OAAA,CAASC,SAAA,IAAc;IAChE,MAAM;MAAEmB,IAAA;MAAMC,kBAAA;MAAoBnB;IAAA,IAAoBD,SAAA;IAEtD,IAAImB,IAAA,KAASX,yBAAA,CAA0Ba,aAAA,CAAcC,QAAA,IAAYF,kBAAA,EAAoB;MACzEpB,SAAA,CAAAuB,cAAA,GAAiBL,KAAA,CAAMM,eAAA,CAAgBJ,kBAAkB;MACnE,IAAIpB,SAAA,CAAUuB,cAAA,EAAgB;QAEtB,MAAAE,cAAA,GAAiB,IAAIC,cAAA,CAAe,IAAK;QAC/C,MAAMzC,QAAA,GAAW,IAAI0C,iBAAA,CAAkB;UAAEC,KAAA,EAAO;QAAU;QAC1D,MAAMC,MAAA,GAAS,IAAI7C,IAAA,CAAKyC,cAAA,EAAgBxC,QAAQ;QACtCe,SAAA,CAAAuB,cAAA,CAAeO,GAAA,CAAID,MAAM;MAAA,OAC9B;QACLE,OAAA,CAAQC,IAAA,CAAK,6BAA6BhC,SAAA,CAAUoB,kBAAA,2BAA6CpB,SAAA,CAAUiC,EAAA,EAAI;MACjH;IACF;IAGArC,MAAA,CAAOC,MAAA,CAAOI,eAAe,EAAEF,OAAA,CAASG,cAAA,IAAmB;MACzD,MAAM;QAAEgC,aAAA;QAAeC,WAAA;QAAaC,WAAA;QAAa7B;MAAA,IAAsBL,cAAA;MAGvE,IACEK,iBAAA,KAAsBC,yBAAA,CAA0BC,sBAAA,CAAuBG,SAAA,IACvEuB,WAAA,IACAC,WAAA,EACA;QACelC,cAAA,CAAAE,OAAA,GAAUc,KAAA,CAAMM,eAAA,CAAgBW,WAAW;QAC3CjC,cAAA,CAAAG,OAAA,GAAUa,KAAA,CAAMM,eAAA,CAAgBY,WAAW;QAGtD,KAAClC,cAAA,CAAeE,OAAA,EAAS;UACnB2B,OAAA,CAAAC,IAAA,CAAK,kBAAkBG,WAAA,eAA0B;UACzD;QACF;QAEI,KAACjC,cAAA,CAAeG,OAAA,EAAS;UACnB0B,OAAA,CAAAC,IAAA,CAAK,kBAAkBI,WAAA,eAA0B;UACzD;QACF;MACF;MAGelC,cAAA,CAAAC,SAAA,GAAYe,KAAA,CAAMM,eAAA,CAAgBU,aAAa;MAC1D,KAAChC,cAAA,CAAeC,SAAA,EAAW;QACrB4B,OAAA,CAAAC,IAAA,CAAK,kBAAkBE,aAAA,eAA4B;MAC7D;IAAA,CACD;EAAA,CACF;AACH;AAEA,SAASG,+BAA+BC,eAAA,EAAoCpB,KAAA,EAAuB;EAEvFD,SAAA,CAAAqB,eAAA,CAAgB/C,gBAAA,EAAmB2B,KAAK;EAGlD,IAAIoB,eAAA,CAAgB1D,MAAA,EAAQ;IACND,mBAAA,CAAA2D,eAAA,CAAgB1D,MAAA,EAAQsC,KAAK;EACnD;EAGAoB,eAAA,CAAgBR,GAAA,CAAIZ,KAAK;AAC3B;AAEA,MAAMqB,wBAAA,CAAyB;EAI7BlD,YAAYmD,UAAA,GAAyB,MAAO;IAH5ClD,aAAA;IACAA,aAAA;IACQA,aAAA;IAEN,KAAKkD,UAAA,GAAaA,UAAA;IAClB,KAAKC,IAAA,GAAOhE,qBAAA;IACZ,KAAKiE,WAAA,GAAc;IAGf,KAAC,KAAKF,UAAA,EAAY;MACf,KAAAA,UAAA,GAAa,IAAIG,UAAA;IACxB;EACF;EAEAC,sBAAsBC,UAAA,EAAsC;IACpD,MAAAP,eAAA,GAAkB,IAAInD,iBAAA;IAC5B,IAAI+B,KAAA,GAAyB;IAEvB,MAAA4B,WAAA,GAAeC,KAAA,IAAqB;MACxC,MAAMC,aAAA,GAAgBD,KAAA,CAAME,IAAA;MAE5B,IAAID,aAAA,CAAcE,aAAA,KAAkB,qBAAqB,CAACF,aAAA,CAAcG,OAAA,EAAS;MAEpEC,YAAA,CAAAJ,aAAA,EAAe,KAAKP,IAAA,EAAM/D,eAAe,EACnD2E,IAAA,CAAK,CAAC;QAAEC,OAAA;QAASC;MAAA,MAAgB;QAChC,IAAI,CAACA,SAAA,EAAW;UACR,UAAIC,KAAA,CAAM,eAAe;QACjC;QAEAlB,eAAA,CAAgB/C,gBAAA,GAAmB,IAAIkE,gBAAA,CAAiBT,aAAA,EAAeM,OAAA,EAASC,SAAS;QAEnF,MAAAG,QAAA,GAAWpB,eAAA,CAAgB/C,gBAAA,CAAiBmE,QAAA;QAE5C,MAAAC,WAAA,GAAc,KAAKjB,WAAA,CAAYgB,QAAQ;QAC7C,IAAIC,WAAA,EAAa;UACPzC,KAAA,GAAAyC,WAAA,CAAYzC,KAAA,CAAM0C,KAAA;UAE1BvB,8BAAA,CAA+BC,eAAA,EAAiBpB,KAAK;QAAA,OAChD;UACD,KAAC,KAAKsB,UAAA,EAAY;YACd,UAAIgB,KAAA,CAAM,qBAAqB;UACvC;UAEK,KAAAhB,UAAA,CAAWqB,OAAA,CAAQ,EAAE;UAC1B,KAAKrB,UAAA,CAAWsB,IAAA,CACdxB,eAAA,CAAgB/C,gBAAA,CAAiBmE,QAAA,EAChCK,KAAA,IAA+B;YAC1B,KAACzB,eAAA,CAAgB/C,gBAAA,EAAkB;cACrCwC,OAAA,CAAQC,IAAA,CAAK,mDAAmD;cAChE;YACF;YAEK,KAAAU,WAAA,CAAYgB,QAAQ,IAAIK,KAAA;YAErB7C,KAAA,GAAA6C,KAAA,CAAM7C,KAAA,CAAM0C,KAAA;YAEpBvB,8BAAA,CAA+BC,eAAA,EAAiBpB,KAAK;UACvD,GACA,MAAM,CAAC,GACP,MAAM;YACE,UAAIsC,KAAA,CAAM,SAASE,QAAA,wBAAgC;UAC3D;QAEJ;MAAA,CACD,EACAM,KAAA,CAAOC,GAAA,IAAQ;QACdlC,OAAA,CAAQC,IAAA,CAAKiC,GAAG;MAAA,CACjB;IAAA;IAGMpB,UAAA,CAAAqB,gBAAA,CAAiB,aAAapB,WAAW;IAEpD,MAAMqB,cAAA,GAAiBA,CAAA,KAAY;MACtBtB,UAAA,CAAAuB,mBAAA,CAAoB,aAAatB,WAAW;MAC5CD,UAAA,CAAAuB,mBAAA,CAAoB,gBAAgBD,cAAc;MAC7D7B,eAAA,CAAgB/C,gBAAA,GAAmB;MACnC,IAAI2B,KAAA,EAAO;QACToB,eAAA,CAAgB+B,MAAA,CAAOnD,KAAK;MAC9B;MACQA,KAAA;IAAA;IAGC2B,UAAA,CAAAqB,gBAAA,CAAiB,gBAAgBC,cAAc;IAEnD,OAAA7B,eAAA;EACT;AACF", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}