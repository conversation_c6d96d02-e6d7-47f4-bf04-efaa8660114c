{"ast": null, "code": "import _extends from '@babel/runtime/helpers/esm/extends';\nimport * as THREE from 'three';\nimport * as React from 'react';\nimport { extend, useThree, useFrame } from '@react-three/fiber';\nimport { useIntersect } from './useIntersect.js';\nimport { useFBO } from './Fbo.js';\nimport { RenderTexture } from './RenderTexture.js';\nimport { shaderMaterial } from './shaderMaterial.js';\nimport { FullScreenQuad } from 'three-stdlib';\nimport { version } from '../helpers/constants.js';\nconst PortalMaterialImpl = /* @__PURE__ */shaderMaterial({\n  blur: 0,\n  map: null,\n  sdf: null,\n  blend: 0,\n  size: 0,\n  resolution: /* @__PURE__ */new THREE.Vector2()\n}, `varying vec2 vUv;\n   void main() {\n     gl_Position = projectionMatrix * modelViewMatrix * vec4(position, 1.0);\n     vUv = uv;\n   }`, `uniform sampler2D sdf;\n   uniform sampler2D map;\n   uniform float blur;\n   uniform float size;\n   uniform float time;\n   uniform vec2 resolution;\n   varying vec2 vUv;\n   #include <packing>\n   void main() {\n     vec2 uv = gl_FragCoord.xy / resolution.xy;\n     vec4 t = texture2D(map, uv);\n     float k = blur;\n     float d = texture2D(sdf, vUv).r/size;\n     float alpha = 1.0 - smoothstep(0.0, 1.0, clamp(d/k + 1.0, 0.0, 1.0));\n     gl_FragColor = vec4(t.rgb, blur == 0.0 ? t.a : t.a * alpha);\n     #include <tonemapping_fragment>\n     #include <${version >= 154 ? 'colorspace_fragment' : 'encodings_fragment'}>\n   }`);\nconst MeshPortalMaterial = /* @__PURE__ */React.forwardRef(({\n  children,\n  events = undefined,\n  blur = 0,\n  eventPriority = 0,\n  renderPriority = 0,\n  worldUnits = false,\n  resolution = 512,\n  ...props\n}, fref) => {\n  extend({\n    PortalMaterialImpl\n  });\n  const ref = React.useRef(null);\n  const {\n    scene,\n    gl,\n    size,\n    viewport,\n    setEvents\n  } = useThree();\n  const maskRenderTarget = useFBO(resolution, resolution);\n  const [priority, setPriority] = React.useState(0);\n  useFrame(() => {\n    // If blend is > 0 then the portal is being entered, the render-priority must change\n    const p = ref.current.blend > 0 ? Math.max(1, renderPriority) : 0;\n    if (priority !== p) setPriority(p);\n  });\n  React.useEffect(() => {\n    if (events !== undefined) setEvents({\n      enabled: !events\n    });\n  }, [events]);\n  const [visible, setVisible] = React.useState(true);\n  // See if the parent mesh is in the camera frustum\n  const parent = useIntersect(setVisible);\n  React.useLayoutEffect(() => {\n    var _ref$current;\n    // Since the ref above is not tied to a mesh directly (we're inside a material),\n    // it has to be tied to the parent mesh here\n    parent.current = (_ref$current = ref.current) == null || (_ref$current = _ref$current.__r3f.parent) == null ? void 0 : _ref$current.object;\n  }, []);\n  React.useLayoutEffect(() => {\n    if (!parent.current) return;\n\n    // Apply the SDF mask only once\n    if (blur && ref.current.sdf === null) {\n      const tempMesh = new THREE.Mesh(parent.current.geometry, new THREE.MeshBasicMaterial());\n      const boundingBox = new THREE.Box3().setFromBufferAttribute(tempMesh.geometry.attributes.position);\n      const orthoCam = new THREE.OrthographicCamera(boundingBox.min.x * (1 + 2 / resolution), boundingBox.max.x * (1 + 2 / resolution), boundingBox.max.y * (1 + 2 / resolution), boundingBox.min.y * (1 + 2 / resolution), 0.1, 1000);\n      orthoCam.position.set(0, 0, 1);\n      orthoCam.lookAt(0, 0, 0);\n      gl.setRenderTarget(maskRenderTarget);\n      gl.render(tempMesh, orthoCam);\n      const sg = makeSDFGenerator(resolution, resolution, gl);\n      const sdf = sg(maskRenderTarget.texture);\n      const readSdf = new Float32Array(resolution * resolution);\n      gl.readRenderTargetPixels(sdf, 0, 0, resolution, resolution, readSdf);\n      // Get smallest value in sdf\n      let min = Infinity;\n      for (let i = 0; i < readSdf.length; i++) {\n        if (readSdf[i] < min) min = readSdf[i];\n      }\n      min = -min;\n      ref.current.size = min;\n      ref.current.sdf = sdf.texture;\n      gl.setRenderTarget(null);\n    }\n  }, [resolution, blur]);\n  React.useImperativeHandle(fref, () => ref.current);\n  const compute = React.useCallback((event, state, previous) => {\n    var _ref$current2;\n    if (!parent.current) return false;\n    state.pointer.set(event.offsetX / state.size.width * 2 - 1, -(event.offsetY / state.size.height) * 2 + 1);\n    state.raycaster.setFromCamera(state.pointer, state.camera);\n    if (((_ref$current2 = ref.current) == null ? void 0 : _ref$current2.blend) === 0) {\n      // We run a quick check against the parent, if it isn't hit there's no need to raycast at all\n      const [intersection] = state.raycaster.intersectObject(parent.current);\n      if (!intersection) {\n        // Cancel out the raycast camera if the parent mesh isn't hit\n        state.raycaster.camera = undefined;\n        return false;\n      }\n    }\n  }, []);\n  return /*#__PURE__*/React.createElement(\"portalMaterialImpl\", _extends({\n    ref: ref,\n    blur: blur,\n    blend: 0,\n    resolution: [size.width * viewport.dpr, size.height * viewport.dpr],\n    attach: \"material\"\n  }, props), /*#__PURE__*/React.createElement(RenderTexture, {\n    attach: \"map\",\n    frames: visible ? Infinity : 0,\n    eventPriority: eventPriority,\n    renderPriority: renderPriority,\n    compute: compute\n  }, children, /*#__PURE__*/React.createElement(ManagePortalScene, {\n    events: events,\n    rootScene: scene,\n    priority: priority,\n    material: ref,\n    worldUnits: worldUnits\n  })));\n});\nfunction ManagePortalScene({\n  events = undefined,\n  rootScene,\n  material,\n  priority,\n  worldUnits\n}) {\n  const scene = useThree(state => state.scene);\n  const setEvents = useThree(state => state.setEvents);\n  const buffer1 = useFBO();\n  const buffer2 = useFBO();\n  React.useLayoutEffect(() => {\n    scene.matrixAutoUpdate = false;\n  }, []);\n  React.useEffect(() => {\n    if (events !== undefined) setEvents({\n      enabled: events\n    });\n  }, [events]);\n  const [quad, blend] = React.useMemo(() => {\n    // This fullscreen-quad is used to blend the two textures\n    const blend = {\n      value: 0\n    };\n    const quad = new FullScreenQuad(new THREE.ShaderMaterial({\n      uniforms: {\n        a: {\n          value: buffer1.texture\n        },\n        b: {\n          value: buffer2.texture\n        },\n        blend\n      },\n      vertexShader: /*glsl*/`\n          varying vec2 vUv;\n          void main() {\n            vUv = uv;\n            gl_Position = projectionMatrix * modelViewMatrix * vec4( position, 1.0 );\n          }`,\n      fragmentShader: /*glsl*/`\n          uniform sampler2D a;\n          uniform sampler2D b;\n          uniform float blend;\n          varying vec2 vUv;\n          #include <packing>\n          void main() {\n            vec4 ta = texture2D(a, vUv);\n            vec4 tb = texture2D(b, vUv);\n            gl_FragColor = mix(tb, ta, blend);\n            #include <tonemapping_fragment>\n            #include <${version >= 154 ? 'colorspace_fragment' : 'encodings_fragment'}>\n          }`\n    }));\n    return [quad, blend];\n  }, []);\n  useFrame(state => {\n    var _material$current;\n    let parent = material == null || (_material$current = material.current) == null || (_material$current = _material$current.__r3f.parent) == null ? void 0 : _material$current.object;\n    if (parent) {\n      // Move portal contents along with the parent if worldUnits is true\n      if (!worldUnits) {\n        var _material$current2;\n        // If the portal renders exclusively the original scene needs to be updated\n        if (priority && ((_material$current2 = material.current) == null ? void 0 : _material$current2.blend) === 1) parent.updateWorldMatrix(true, false);\n        scene.matrixWorld.copy(parent.matrixWorld);\n      } else scene.matrixWorld.identity();\n\n      // This bit is only necessary if the portal is blended, now it has a render-priority\n      // and will take over the render loop\n      if (priority) {\n        var _material$current3, _material$current4, _material$current5;\n        if (((_material$current3 = material.current) == null ? void 0 : _material$current3.blend) > 0 && ((_material$current4 = material.current) == null ? void 0 : _material$current4.blend) < 1) {\n          // If blend is ongoing (> 0 and < 1) then we need to render both the root scene\n          // and the portal scene, both will then be mixed in the quad from above\n          blend.value = material.current.blend;\n          state.gl.setRenderTarget(buffer1);\n          state.gl.render(scene, state.camera);\n          state.gl.setRenderTarget(buffer2);\n          state.gl.render(rootScene, state.camera);\n          state.gl.setRenderTarget(null);\n          quad.render(state.gl);\n        } else if (((_material$current5 = material.current) == null ? void 0 : _material$current5.blend) === 1) {\n          // However if blend is 1 we only need to render the portal scene\n          state.gl.render(scene, state.camera);\n        }\n      }\n    }\n  }, priority);\n  return /*#__PURE__*/React.createElement(React.Fragment, null);\n}\nconst makeSDFGenerator = (clientWidth, clientHeight, renderer) => {\n  let finalTarget = new THREE.WebGLRenderTarget(clientWidth, clientHeight, {\n    minFilter: THREE.LinearMipmapLinearFilter,\n    magFilter: THREE.LinearFilter,\n    type: THREE.FloatType,\n    format: THREE.RedFormat,\n    generateMipmaps: true\n  });\n  let outsideRenderTarget = new THREE.WebGLRenderTarget(clientWidth, clientHeight, {\n    minFilter: THREE.NearestFilter,\n    magFilter: THREE.NearestFilter\n  });\n  let insideRenderTarget = new THREE.WebGLRenderTarget(clientWidth, clientHeight, {\n    minFilter: THREE.NearestFilter,\n    magFilter: THREE.NearestFilter\n  });\n  let outsideRenderTarget2 = new THREE.WebGLRenderTarget(clientWidth, clientHeight, {\n    minFilter: THREE.NearestFilter,\n    magFilter: THREE.NearestFilter\n  });\n  let insideRenderTarget2 = new THREE.WebGLRenderTarget(clientWidth, clientHeight, {\n    minFilter: THREE.NearestFilter,\n    magFilter: THREE.NearestFilter\n  });\n  let outsideRenderTargetFinal = new THREE.WebGLRenderTarget(clientWidth, clientHeight, {\n    minFilter: THREE.NearestFilter,\n    magFilter: THREE.NearestFilter,\n    type: THREE.FloatType,\n    format: THREE.RedFormat\n  });\n  let insideRenderTargetFinal = new THREE.WebGLRenderTarget(clientWidth, clientHeight, {\n    minFilter: THREE.NearestFilter,\n    magFilter: THREE.NearestFilter,\n    type: THREE.FloatType,\n    format: THREE.RedFormat\n  });\n  const uvRender = new FullScreenQuad(new THREE.ShaderMaterial({\n    uniforms: {\n      tex: {\n        value: null\n      }\n    },\n    vertexShader: /*glsl*/`\n        varying vec2 vUv;\n        void main() {\n          vUv = uv;\n          gl_Position = projectionMatrix * modelViewMatrix * vec4( position, 1.0 );\n        }`,\n    fragmentShader: /*glsl*/`\n        uniform sampler2D tex;\n        varying vec2 vUv;\n        #include <packing>\n        void main() {\n          gl_FragColor = pack2HalfToRGBA(vUv * (round(texture2D(tex, vUv).x)));\n        }`\n  }));\n  const uvRenderInside = new FullScreenQuad(new THREE.ShaderMaterial({\n    uniforms: {\n      tex: {\n        value: null\n      }\n    },\n    vertexShader: /*glsl*/`\n        varying vec2 vUv;\n        void main() {\n          vUv = uv;\n          gl_Position = projectionMatrix * modelViewMatrix * vec4( position, 1.0 );\n        }`,\n    fragmentShader: /*glsl*/`\n        uniform sampler2D tex;\n        varying vec2 vUv;\n        #include <packing>\n        void main() {\n          gl_FragColor = pack2HalfToRGBA(vUv * (1.0 - round(texture2D(tex, vUv).x)));\n        }`\n  }));\n  const jumpFloodRender = new FullScreenQuad(new THREE.ShaderMaterial({\n    uniforms: {\n      tex: {\n        value: null\n      },\n      offset: {\n        value: 0.0\n      },\n      level: {\n        value: 0.0\n      },\n      maxSteps: {\n        value: 0.0\n      }\n    },\n    vertexShader: /*glsl*/`\n        varying vec2 vUv;\n        void main() {\n          vUv = uv;\n          gl_Position = projectionMatrix * modelViewMatrix * vec4( position, 1.0 );\n        }`,\n    fragmentShader: /*glsl*/`\n        varying vec2 vUv;\n        uniform sampler2D tex;\n        uniform float offset;\n        uniform float level;\n        uniform float maxSteps;\n        #include <packing>\n        void main() {\n          float closestDist = 9999999.9;\n          vec2 closestPos = vec2(0.0);\n          for (float x = -1.0; x <= 1.0; x += 1.0) {\n            for (float y = -1.0; y <= 1.0; y += 1.0) {\n              vec2 voffset = vUv;\n              voffset += vec2(x, y) * vec2(${1 / clientWidth}, ${1 / clientHeight}) * offset;\n              vec2 pos = unpackRGBATo2Half(texture2D(tex, voffset));\n              float dist = distance(pos.xy, vUv);\n              if(pos.x != 0.0 && pos.y != 0.0 && dist < closestDist) {\n                closestDist = dist;\n                closestPos = pos;\n              }\n            }\n          }\n          gl_FragColor = pack2HalfToRGBA(closestPos);\n        }`\n  }));\n  const distanceFieldRender = new FullScreenQuad(new THREE.ShaderMaterial({\n    uniforms: {\n      tex: {\n        value: null\n      },\n      size: {\n        value: new THREE.Vector2(clientWidth, clientHeight)\n      }\n    },\n    vertexShader: /*glsl*/`\n        varying vec2 vUv;\n        void main() {\n          vUv = uv;\n          gl_Position = projectionMatrix * modelViewMatrix * vec4( position, 1.0 );\n        }`,\n    fragmentShader: /*glsl*/`\n        varying vec2 vUv;\n        uniform sampler2D tex;\n        uniform vec2 size;\n        #include <packing>\n        void main() {\n          gl_FragColor = vec4(distance(size * unpackRGBATo2Half(texture2D(tex, vUv)), size * vUv), 0.0, 0.0, 0.0);\n        }`\n  }));\n  const compositeRender = new FullScreenQuad(new THREE.ShaderMaterial({\n    uniforms: {\n      inside: {\n        value: insideRenderTargetFinal.texture\n      },\n      outside: {\n        value: outsideRenderTargetFinal.texture\n      },\n      tex: {\n        value: null\n      }\n    },\n    vertexShader: /*glsl*/`\n        varying vec2 vUv;\n        void main() {\n          vUv = uv;\n          gl_Position = projectionMatrix * modelViewMatrix * vec4( position, 1.0 );\n        }`,\n    fragmentShader: /*glsl*/`\n        varying vec2 vUv;\n        uniform sampler2D inside;\n        uniform sampler2D outside;\n        uniform sampler2D tex;\n        #include <packing>\n        void main() {\n          float i = texture2D(inside, vUv).x;\n          float o =texture2D(outside, vUv).x;\n          if (texture2D(tex, vUv).x == 0.0) {\n            gl_FragColor = vec4(o, 0.0, 0.0, 0.0);\n          } else {\n            gl_FragColor = vec4(-i, 0.0, 0.0, 0.0);\n          }\n        }`\n  }));\n  return image => {\n    let ft = finalTarget;\n    image.minFilter = THREE.NearestFilter;\n    image.magFilter = THREE.NearestFilter;\n    uvRender.material.uniforms.tex.value = image;\n    renderer.setRenderTarget(outsideRenderTarget);\n    uvRender.render(renderer);\n    const passes = Math.ceil(Math.log(Math.max(clientWidth, clientHeight)) / Math.log(2.0));\n    let lastTarget = outsideRenderTarget;\n    let target = null;\n    for (let i = 0; i < passes; i++) {\n      const offset = Math.pow(2, passes - i - 1);\n      target = lastTarget === outsideRenderTarget ? outsideRenderTarget2 : outsideRenderTarget;\n      jumpFloodRender.material.uniforms.level.value = i;\n      jumpFloodRender.material.uniforms.maxSteps.value = passes;\n      jumpFloodRender.material.uniforms.offset.value = offset;\n      jumpFloodRender.material.uniforms.tex.value = lastTarget.texture;\n      renderer.setRenderTarget(target);\n      jumpFloodRender.render(renderer);\n      lastTarget = target;\n    }\n    renderer.setRenderTarget(outsideRenderTargetFinal);\n    distanceFieldRender.material.uniforms.tex.value = target.texture;\n    distanceFieldRender.render(renderer);\n    uvRenderInside.material.uniforms.tex.value = image;\n    renderer.setRenderTarget(insideRenderTarget);\n    uvRenderInside.render(renderer);\n    lastTarget = insideRenderTarget;\n    for (let i = 0; i < passes; i++) {\n      const offset = Math.pow(2, passes - i - 1);\n      target = lastTarget === insideRenderTarget ? insideRenderTarget2 : insideRenderTarget;\n      jumpFloodRender.material.uniforms.level.value = i;\n      jumpFloodRender.material.uniforms.maxSteps.value = passes;\n      jumpFloodRender.material.uniforms.offset.value = offset;\n      jumpFloodRender.material.uniforms.tex.value = lastTarget.texture;\n      renderer.setRenderTarget(target);\n      jumpFloodRender.render(renderer);\n      lastTarget = target;\n    }\n    renderer.setRenderTarget(insideRenderTargetFinal);\n    distanceFieldRender.material.uniforms.tex.value = target.texture;\n    distanceFieldRender.render(renderer);\n    renderer.setRenderTarget(ft);\n    compositeRender.material.uniforms.tex.value = image;\n    compositeRender.render(renderer);\n    renderer.setRenderTarget(null);\n    return ft;\n  };\n};\nexport { MeshPortalMaterial };", "map": {"version": 3, "names": ["_extends", "THREE", "React", "extend", "useThree", "useFrame", "useIntersect", "useFBO", "RenderTexture", "shaderMaterial", "FullScreenQuad", "version", "PortalMaterialImpl", "blur", "map", "sdf", "blend", "size", "resolution", "Vector2", "MeshPortalMaterial", "forwardRef", "children", "events", "undefined", "eventPriority", "renderPriority", "worldUnits", "props", "fref", "ref", "useRef", "scene", "gl", "viewport", "setEvents", "mask<PERSON><PERSON><PERSON><PERSON><PERSON>", "priority", "setPriority", "useState", "p", "current", "Math", "max", "useEffect", "enabled", "visible", "setVisible", "parent", "useLayoutEffect", "_ref$current", "__r3f", "object", "<PERSON>mp<PERSON><PERSON>", "<PERSON><PERSON>", "geometry", "MeshBasicMaterial", "boundingBox", "Box3", "setFromBufferAttribute", "attributes", "position", "orthoCam", "OrthographicCamera", "min", "x", "y", "set", "lookAt", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "render", "sg", "makeSDFGenerator", "texture", "readSdf", "Float32Array", "readRenderTargetPixels", "Infinity", "i", "length", "useImperativeHandle", "compute", "useCallback", "event", "state", "previous", "_ref$current2", "pointer", "offsetX", "width", "offsetY", "height", "raycaster", "setFromCamera", "camera", "intersection", "intersectObject", "createElement", "dpr", "attach", "frames", "ManagePortalScene", "rootScene", "material", "buffer1", "buffer2", "matrixAutoUpdate", "quad", "useMemo", "value", "ShaderMaterial", "uniforms", "a", "b", "vertexShader", "fragmentShader", "_material$current", "_material$current2", "updateWorldMatrix", "matrixWorld", "copy", "identity", "_material$current3", "_material$current4", "_material$current5", "Fragment", "clientWidth", "clientHeight", "renderer", "finalTarget", "WebGLRenderTarget", "minFilter", "LinearMipmapLinearFilter", "magFilter", "LinearFilter", "type", "FloatType", "format", "RedFormat", "generateMipmaps", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "NearestFilter", "insideRenderTarget", "outsideRenderTarget2", "insideRenderTarget2", "outsideRenderTargetFinal", "insideRenderTargetFinal", "uvRender", "tex", "uvRenderInside", "jump<PERSON><PERSON>od<PERSON><PERSON>", "offset", "level", "maxSteps", "distanceField<PERSON>ender", "compositeRender", "inside", "outside", "image", "ft", "passes", "ceil", "log", "last<PERSON><PERSON><PERSON>", "target", "pow"], "sources": ["C:/Users/<USER>/Documents/Apps_Creations/SiahMan_Racing4Speed_WebGame/siah-man-racing/node_modules/@react-three/drei/core/MeshPortalMaterial.js"], "sourcesContent": ["import _extends from '@babel/runtime/helpers/esm/extends';\nimport * as THREE from 'three';\nimport * as React from 'react';\nimport { extend, useThree, useFrame } from '@react-three/fiber';\nimport { useIntersect } from './useIntersect.js';\nimport { useFBO } from './Fbo.js';\nimport { RenderTexture } from './RenderTexture.js';\nimport { shaderMaterial } from './shaderMaterial.js';\nimport { FullScreenQuad } from 'three-stdlib';\nimport { version } from '../helpers/constants.js';\n\nconst PortalMaterialImpl = /* @__PURE__ */shaderMaterial({\n  blur: 0,\n  map: null,\n  sdf: null,\n  blend: 0,\n  size: 0,\n  resolution: /* @__PURE__ */new THREE.Vector2()\n}, `varying vec2 vUv;\n   void main() {\n     gl_Position = projectionMatrix * modelViewMatrix * vec4(position, 1.0);\n     vUv = uv;\n   }`, `uniform sampler2D sdf;\n   uniform sampler2D map;\n   uniform float blur;\n   uniform float size;\n   uniform float time;\n   uniform vec2 resolution;\n   varying vec2 vUv;\n   #include <packing>\n   void main() {\n     vec2 uv = gl_FragCoord.xy / resolution.xy;\n     vec4 t = texture2D(map, uv);\n     float k = blur;\n     float d = texture2D(sdf, vUv).r/size;\n     float alpha = 1.0 - smoothstep(0.0, 1.0, clamp(d/k + 1.0, 0.0, 1.0));\n     gl_FragColor = vec4(t.rgb, blur == 0.0 ? t.a : t.a * alpha);\n     #include <tonemapping_fragment>\n     #include <${version >= 154 ? 'colorspace_fragment' : 'encodings_fragment'}>\n   }`);\nconst MeshPortalMaterial = /* @__PURE__ */React.forwardRef(({\n  children,\n  events = undefined,\n  blur = 0,\n  eventPriority = 0,\n  renderPriority = 0,\n  worldUnits = false,\n  resolution = 512,\n  ...props\n}, fref) => {\n  extend({\n    PortalMaterialImpl\n  });\n  const ref = React.useRef(null);\n  const {\n    scene,\n    gl,\n    size,\n    viewport,\n    setEvents\n  } = useThree();\n  const maskRenderTarget = useFBO(resolution, resolution);\n  const [priority, setPriority] = React.useState(0);\n  useFrame(() => {\n    // If blend is > 0 then the portal is being entered, the render-priority must change\n    const p = ref.current.blend > 0 ? Math.max(1, renderPriority) : 0;\n    if (priority !== p) setPriority(p);\n  });\n  React.useEffect(() => {\n    if (events !== undefined) setEvents({\n      enabled: !events\n    });\n  }, [events]);\n  const [visible, setVisible] = React.useState(true);\n  // See if the parent mesh is in the camera frustum\n  const parent = useIntersect(setVisible);\n  React.useLayoutEffect(() => {\n    var _ref$current;\n    // Since the ref above is not tied to a mesh directly (we're inside a material),\n    // it has to be tied to the parent mesh here\n    parent.current = (_ref$current = ref.current) == null || (_ref$current = _ref$current.__r3f.parent) == null ? void 0 : _ref$current.object;\n  }, []);\n  React.useLayoutEffect(() => {\n    if (!parent.current) return;\n\n    // Apply the SDF mask only once\n    if (blur && ref.current.sdf === null) {\n      const tempMesh = new THREE.Mesh(parent.current.geometry, new THREE.MeshBasicMaterial());\n      const boundingBox = new THREE.Box3().setFromBufferAttribute(tempMesh.geometry.attributes.position);\n      const orthoCam = new THREE.OrthographicCamera(boundingBox.min.x * (1 + 2 / resolution), boundingBox.max.x * (1 + 2 / resolution), boundingBox.max.y * (1 + 2 / resolution), boundingBox.min.y * (1 + 2 / resolution), 0.1, 1000);\n      orthoCam.position.set(0, 0, 1);\n      orthoCam.lookAt(0, 0, 0);\n      gl.setRenderTarget(maskRenderTarget);\n      gl.render(tempMesh, orthoCam);\n      const sg = makeSDFGenerator(resolution, resolution, gl);\n      const sdf = sg(maskRenderTarget.texture);\n      const readSdf = new Float32Array(resolution * resolution);\n      gl.readRenderTargetPixels(sdf, 0, 0, resolution, resolution, readSdf);\n      // Get smallest value in sdf\n      let min = Infinity;\n      for (let i = 0; i < readSdf.length; i++) {\n        if (readSdf[i] < min) min = readSdf[i];\n      }\n      min = -min;\n      ref.current.size = min;\n      ref.current.sdf = sdf.texture;\n      gl.setRenderTarget(null);\n    }\n  }, [resolution, blur]);\n  React.useImperativeHandle(fref, () => ref.current);\n  const compute = React.useCallback((event, state, previous) => {\n    var _ref$current2;\n    if (!parent.current) return false;\n    state.pointer.set(event.offsetX / state.size.width * 2 - 1, -(event.offsetY / state.size.height) * 2 + 1);\n    state.raycaster.setFromCamera(state.pointer, state.camera);\n    if (((_ref$current2 = ref.current) == null ? void 0 : _ref$current2.blend) === 0) {\n      // We run a quick check against the parent, if it isn't hit there's no need to raycast at all\n      const [intersection] = state.raycaster.intersectObject(parent.current);\n      if (!intersection) {\n        // Cancel out the raycast camera if the parent mesh isn't hit\n        state.raycaster.camera = undefined;\n        return false;\n      }\n    }\n  }, []);\n  return /*#__PURE__*/React.createElement(\"portalMaterialImpl\", _extends({\n    ref: ref,\n    blur: blur,\n    blend: 0,\n    resolution: [size.width * viewport.dpr, size.height * viewport.dpr],\n    attach: \"material\"\n  }, props), /*#__PURE__*/React.createElement(RenderTexture, {\n    attach: \"map\",\n    frames: visible ? Infinity : 0,\n    eventPriority: eventPriority,\n    renderPriority: renderPriority,\n    compute: compute\n  }, children, /*#__PURE__*/React.createElement(ManagePortalScene, {\n    events: events,\n    rootScene: scene,\n    priority: priority,\n    material: ref,\n    worldUnits: worldUnits\n  })));\n});\nfunction ManagePortalScene({\n  events = undefined,\n  rootScene,\n  material,\n  priority,\n  worldUnits\n}) {\n  const scene = useThree(state => state.scene);\n  const setEvents = useThree(state => state.setEvents);\n  const buffer1 = useFBO();\n  const buffer2 = useFBO();\n  React.useLayoutEffect(() => {\n    scene.matrixAutoUpdate = false;\n  }, []);\n  React.useEffect(() => {\n    if (events !== undefined) setEvents({\n      enabled: events\n    });\n  }, [events]);\n  const [quad, blend] = React.useMemo(() => {\n    // This fullscreen-quad is used to blend the two textures\n    const blend = {\n      value: 0\n    };\n    const quad = new FullScreenQuad(new THREE.ShaderMaterial({\n      uniforms: {\n        a: {\n          value: buffer1.texture\n        },\n        b: {\n          value: buffer2.texture\n        },\n        blend\n      },\n      vertexShader: /*glsl*/`\n          varying vec2 vUv;\n          void main() {\n            vUv = uv;\n            gl_Position = projectionMatrix * modelViewMatrix * vec4( position, 1.0 );\n          }`,\n      fragmentShader: /*glsl*/`\n          uniform sampler2D a;\n          uniform sampler2D b;\n          uniform float blend;\n          varying vec2 vUv;\n          #include <packing>\n          void main() {\n            vec4 ta = texture2D(a, vUv);\n            vec4 tb = texture2D(b, vUv);\n            gl_FragColor = mix(tb, ta, blend);\n            #include <tonemapping_fragment>\n            #include <${version >= 154 ? 'colorspace_fragment' : 'encodings_fragment'}>\n          }`\n    }));\n    return [quad, blend];\n  }, []);\n  useFrame(state => {\n    var _material$current;\n    let parent = material == null || (_material$current = material.current) == null || (_material$current = _material$current.__r3f.parent) == null ? void 0 : _material$current.object;\n    if (parent) {\n      // Move portal contents along with the parent if worldUnits is true\n      if (!worldUnits) {\n        var _material$current2;\n        // If the portal renders exclusively the original scene needs to be updated\n        if (priority && ((_material$current2 = material.current) == null ? void 0 : _material$current2.blend) === 1) parent.updateWorldMatrix(true, false);\n        scene.matrixWorld.copy(parent.matrixWorld);\n      } else scene.matrixWorld.identity();\n\n      // This bit is only necessary if the portal is blended, now it has a render-priority\n      // and will take over the render loop\n      if (priority) {\n        var _material$current3, _material$current4, _material$current5;\n        if (((_material$current3 = material.current) == null ? void 0 : _material$current3.blend) > 0 && ((_material$current4 = material.current) == null ? void 0 : _material$current4.blend) < 1) {\n          // If blend is ongoing (> 0 and < 1) then we need to render both the root scene\n          // and the portal scene, both will then be mixed in the quad from above\n          blend.value = material.current.blend;\n          state.gl.setRenderTarget(buffer1);\n          state.gl.render(scene, state.camera);\n          state.gl.setRenderTarget(buffer2);\n          state.gl.render(rootScene, state.camera);\n          state.gl.setRenderTarget(null);\n          quad.render(state.gl);\n        } else if (((_material$current5 = material.current) == null ? void 0 : _material$current5.blend) === 1) {\n          // However if blend is 1 we only need to render the portal scene\n          state.gl.render(scene, state.camera);\n        }\n      }\n    }\n  }, priority);\n  return /*#__PURE__*/React.createElement(React.Fragment, null);\n}\nconst makeSDFGenerator = (clientWidth, clientHeight, renderer) => {\n  let finalTarget = new THREE.WebGLRenderTarget(clientWidth, clientHeight, {\n    minFilter: THREE.LinearMipmapLinearFilter,\n    magFilter: THREE.LinearFilter,\n    type: THREE.FloatType,\n    format: THREE.RedFormat,\n    generateMipmaps: true\n  });\n  let outsideRenderTarget = new THREE.WebGLRenderTarget(clientWidth, clientHeight, {\n    minFilter: THREE.NearestFilter,\n    magFilter: THREE.NearestFilter\n  });\n  let insideRenderTarget = new THREE.WebGLRenderTarget(clientWidth, clientHeight, {\n    minFilter: THREE.NearestFilter,\n    magFilter: THREE.NearestFilter\n  });\n  let outsideRenderTarget2 = new THREE.WebGLRenderTarget(clientWidth, clientHeight, {\n    minFilter: THREE.NearestFilter,\n    magFilter: THREE.NearestFilter\n  });\n  let insideRenderTarget2 = new THREE.WebGLRenderTarget(clientWidth, clientHeight, {\n    minFilter: THREE.NearestFilter,\n    magFilter: THREE.NearestFilter\n  });\n  let outsideRenderTargetFinal = new THREE.WebGLRenderTarget(clientWidth, clientHeight, {\n    minFilter: THREE.NearestFilter,\n    magFilter: THREE.NearestFilter,\n    type: THREE.FloatType,\n    format: THREE.RedFormat\n  });\n  let insideRenderTargetFinal = new THREE.WebGLRenderTarget(clientWidth, clientHeight, {\n    minFilter: THREE.NearestFilter,\n    magFilter: THREE.NearestFilter,\n    type: THREE.FloatType,\n    format: THREE.RedFormat\n  });\n  const uvRender = new FullScreenQuad(new THREE.ShaderMaterial({\n    uniforms: {\n      tex: {\n        value: null\n      }\n    },\n    vertexShader: /*glsl*/`\n        varying vec2 vUv;\n        void main() {\n          vUv = uv;\n          gl_Position = projectionMatrix * modelViewMatrix * vec4( position, 1.0 );\n        }`,\n    fragmentShader: /*glsl*/`\n        uniform sampler2D tex;\n        varying vec2 vUv;\n        #include <packing>\n        void main() {\n          gl_FragColor = pack2HalfToRGBA(vUv * (round(texture2D(tex, vUv).x)));\n        }`\n  }));\n  const uvRenderInside = new FullScreenQuad(new THREE.ShaderMaterial({\n    uniforms: {\n      tex: {\n        value: null\n      }\n    },\n    vertexShader: /*glsl*/`\n        varying vec2 vUv;\n        void main() {\n          vUv = uv;\n          gl_Position = projectionMatrix * modelViewMatrix * vec4( position, 1.0 );\n        }`,\n    fragmentShader: /*glsl*/`\n        uniform sampler2D tex;\n        varying vec2 vUv;\n        #include <packing>\n        void main() {\n          gl_FragColor = pack2HalfToRGBA(vUv * (1.0 - round(texture2D(tex, vUv).x)));\n        }`\n  }));\n  const jumpFloodRender = new FullScreenQuad(new THREE.ShaderMaterial({\n    uniforms: {\n      tex: {\n        value: null\n      },\n      offset: {\n        value: 0.0\n      },\n      level: {\n        value: 0.0\n      },\n      maxSteps: {\n        value: 0.0\n      }\n    },\n    vertexShader: /*glsl*/`\n        varying vec2 vUv;\n        void main() {\n          vUv = uv;\n          gl_Position = projectionMatrix * modelViewMatrix * vec4( position, 1.0 );\n        }`,\n    fragmentShader: /*glsl*/`\n        varying vec2 vUv;\n        uniform sampler2D tex;\n        uniform float offset;\n        uniform float level;\n        uniform float maxSteps;\n        #include <packing>\n        void main() {\n          float closestDist = 9999999.9;\n          vec2 closestPos = vec2(0.0);\n          for (float x = -1.0; x <= 1.0; x += 1.0) {\n            for (float y = -1.0; y <= 1.0; y += 1.0) {\n              vec2 voffset = vUv;\n              voffset += vec2(x, y) * vec2(${1 / clientWidth}, ${1 / clientHeight}) * offset;\n              vec2 pos = unpackRGBATo2Half(texture2D(tex, voffset));\n              float dist = distance(pos.xy, vUv);\n              if(pos.x != 0.0 && pos.y != 0.0 && dist < closestDist) {\n                closestDist = dist;\n                closestPos = pos;\n              }\n            }\n          }\n          gl_FragColor = pack2HalfToRGBA(closestPos);\n        }`\n  }));\n  const distanceFieldRender = new FullScreenQuad(new THREE.ShaderMaterial({\n    uniforms: {\n      tex: {\n        value: null\n      },\n      size: {\n        value: new THREE.Vector2(clientWidth, clientHeight)\n      }\n    },\n    vertexShader: /*glsl*/`\n        varying vec2 vUv;\n        void main() {\n          vUv = uv;\n          gl_Position = projectionMatrix * modelViewMatrix * vec4( position, 1.0 );\n        }`,\n    fragmentShader: /*glsl*/`\n        varying vec2 vUv;\n        uniform sampler2D tex;\n        uniform vec2 size;\n        #include <packing>\n        void main() {\n          gl_FragColor = vec4(distance(size * unpackRGBATo2Half(texture2D(tex, vUv)), size * vUv), 0.0, 0.0, 0.0);\n        }`\n  }));\n  const compositeRender = new FullScreenQuad(new THREE.ShaderMaterial({\n    uniforms: {\n      inside: {\n        value: insideRenderTargetFinal.texture\n      },\n      outside: {\n        value: outsideRenderTargetFinal.texture\n      },\n      tex: {\n        value: null\n      }\n    },\n    vertexShader: /*glsl*/`\n        varying vec2 vUv;\n        void main() {\n          vUv = uv;\n          gl_Position = projectionMatrix * modelViewMatrix * vec4( position, 1.0 );\n        }`,\n    fragmentShader: /*glsl*/`\n        varying vec2 vUv;\n        uniform sampler2D inside;\n        uniform sampler2D outside;\n        uniform sampler2D tex;\n        #include <packing>\n        void main() {\n          float i = texture2D(inside, vUv).x;\n          float o =texture2D(outside, vUv).x;\n          if (texture2D(tex, vUv).x == 0.0) {\n            gl_FragColor = vec4(o, 0.0, 0.0, 0.0);\n          } else {\n            gl_FragColor = vec4(-i, 0.0, 0.0, 0.0);\n          }\n        }`\n  }));\n  return image => {\n    let ft = finalTarget;\n    image.minFilter = THREE.NearestFilter;\n    image.magFilter = THREE.NearestFilter;\n    uvRender.material.uniforms.tex.value = image;\n    renderer.setRenderTarget(outsideRenderTarget);\n    uvRender.render(renderer);\n    const passes = Math.ceil(Math.log(Math.max(clientWidth, clientHeight)) / Math.log(2.0));\n    let lastTarget = outsideRenderTarget;\n    let target = null;\n    for (let i = 0; i < passes; i++) {\n      const offset = Math.pow(2, passes - i - 1);\n      target = lastTarget === outsideRenderTarget ? outsideRenderTarget2 : outsideRenderTarget;\n      jumpFloodRender.material.uniforms.level.value = i;\n      jumpFloodRender.material.uniforms.maxSteps.value = passes;\n      jumpFloodRender.material.uniforms.offset.value = offset;\n      jumpFloodRender.material.uniforms.tex.value = lastTarget.texture;\n      renderer.setRenderTarget(target);\n      jumpFloodRender.render(renderer);\n      lastTarget = target;\n    }\n    renderer.setRenderTarget(outsideRenderTargetFinal);\n    distanceFieldRender.material.uniforms.tex.value = target.texture;\n    distanceFieldRender.render(renderer);\n    uvRenderInside.material.uniforms.tex.value = image;\n    renderer.setRenderTarget(insideRenderTarget);\n    uvRenderInside.render(renderer);\n    lastTarget = insideRenderTarget;\n    for (let i = 0; i < passes; i++) {\n      const offset = Math.pow(2, passes - i - 1);\n      target = lastTarget === insideRenderTarget ? insideRenderTarget2 : insideRenderTarget;\n      jumpFloodRender.material.uniforms.level.value = i;\n      jumpFloodRender.material.uniforms.maxSteps.value = passes;\n      jumpFloodRender.material.uniforms.offset.value = offset;\n      jumpFloodRender.material.uniforms.tex.value = lastTarget.texture;\n      renderer.setRenderTarget(target);\n      jumpFloodRender.render(renderer);\n      lastTarget = target;\n    }\n    renderer.setRenderTarget(insideRenderTargetFinal);\n    distanceFieldRender.material.uniforms.tex.value = target.texture;\n    distanceFieldRender.render(renderer);\n    renderer.setRenderTarget(ft);\n    compositeRender.material.uniforms.tex.value = image;\n    compositeRender.render(renderer);\n    renderer.setRenderTarget(null);\n    return ft;\n  };\n};\n\nexport { MeshPortalMaterial };\n"], "mappings": "AAAA,OAAOA,QAAQ,MAAM,oCAAoC;AACzD,OAAO,KAAKC,KAAK,MAAM,OAAO;AAC9B,OAAO,KAAKC,KAAK,MAAM,OAAO;AAC9B,SAASC,MAAM,EAAEC,QAAQ,EAAEC,QAAQ,QAAQ,oBAAoB;AAC/D,SAASC,YAAY,QAAQ,mBAAmB;AAChD,SAASC,MAAM,QAAQ,UAAU;AACjC,SAASC,aAAa,QAAQ,oBAAoB;AAClD,SAASC,cAAc,QAAQ,qBAAqB;AACpD,SAASC,cAAc,QAAQ,cAAc;AAC7C,SAASC,OAAO,QAAQ,yBAAyB;AAEjD,MAAMC,kBAAkB,GAAG,eAAeH,cAAc,CAAC;EACvDI,IAAI,EAAE,CAAC;EACPC,GAAG,EAAE,IAAI;EACTC,GAAG,EAAE,IAAI;EACTC,KAAK,EAAE,CAAC;EACRC,IAAI,EAAE,CAAC;EACPC,UAAU,EAAE,eAAe,IAAIjB,KAAK,CAACkB,OAAO,CAAC;AAC/C,CAAC,EAAE;AACH;AACA;AACA;AACA,KAAK,EAAE;AACP;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,iBAAiBR,OAAO,IAAI,GAAG,GAAG,qBAAqB,GAAG,oBAAoB;AAC9E,KAAK,CAAC;AACN,MAAMS,kBAAkB,GAAG,eAAelB,KAAK,CAACmB,UAAU,CAAC,CAAC;EAC1DC,QAAQ;EACRC,MAAM,GAAGC,SAAS;EAClBX,IAAI,GAAG,CAAC;EACRY,aAAa,GAAG,CAAC;EACjBC,cAAc,GAAG,CAAC;EAClBC,UAAU,GAAG,KAAK;EAClBT,UAAU,GAAG,GAAG;EAChB,GAAGU;AACL,CAAC,EAAEC,IAAI,KAAK;EACV1B,MAAM,CAAC;IACLS;EACF,CAAC,CAAC;EACF,MAAMkB,GAAG,GAAG5B,KAAK,CAAC6B,MAAM,CAAC,IAAI,CAAC;EAC9B,MAAM;IACJC,KAAK;IACLC,EAAE;IACFhB,IAAI;IACJiB,QAAQ;IACRC;EACF,CAAC,GAAG/B,QAAQ,CAAC,CAAC;EACd,MAAMgC,gBAAgB,GAAG7B,MAAM,CAACW,UAAU,EAAEA,UAAU,CAAC;EACvD,MAAM,CAACmB,QAAQ,EAAEC,WAAW,CAAC,GAAGpC,KAAK,CAACqC,QAAQ,CAAC,CAAC,CAAC;EACjDlC,QAAQ,CAAC,MAAM;IACb;IACA,MAAMmC,CAAC,GAAGV,GAAG,CAACW,OAAO,CAACzB,KAAK,GAAG,CAAC,GAAG0B,IAAI,CAACC,GAAG,CAAC,CAAC,EAAEjB,cAAc,CAAC,GAAG,CAAC;IACjE,IAAIW,QAAQ,KAAKG,CAAC,EAAEF,WAAW,CAACE,CAAC,CAAC;EACpC,CAAC,CAAC;EACFtC,KAAK,CAAC0C,SAAS,CAAC,MAAM;IACpB,IAAIrB,MAAM,KAAKC,SAAS,EAAEW,SAAS,CAAC;MAClCU,OAAO,EAAE,CAACtB;IACZ,CAAC,CAAC;EACJ,CAAC,EAAE,CAACA,MAAM,CAAC,CAAC;EACZ,MAAM,CAACuB,OAAO,EAAEC,UAAU,CAAC,GAAG7C,KAAK,CAACqC,QAAQ,CAAC,IAAI,CAAC;EAClD;EACA,MAAMS,MAAM,GAAG1C,YAAY,CAACyC,UAAU,CAAC;EACvC7C,KAAK,CAAC+C,eAAe,CAAC,MAAM;IAC1B,IAAIC,YAAY;IAChB;IACA;IACAF,MAAM,CAACP,OAAO,GAAG,CAACS,YAAY,GAAGpB,GAAG,CAACW,OAAO,KAAK,IAAI,IAAI,CAACS,YAAY,GAAGA,YAAY,CAACC,KAAK,CAACH,MAAM,KAAK,IAAI,GAAG,KAAK,CAAC,GAAGE,YAAY,CAACE,MAAM;EAC5I,CAAC,EAAE,EAAE,CAAC;EACNlD,KAAK,CAAC+C,eAAe,CAAC,MAAM;IAC1B,IAAI,CAACD,MAAM,CAACP,OAAO,EAAE;;IAErB;IACA,IAAI5B,IAAI,IAAIiB,GAAG,CAACW,OAAO,CAAC1B,GAAG,KAAK,IAAI,EAAE;MACpC,MAAMsC,QAAQ,GAAG,IAAIpD,KAAK,CAACqD,IAAI,CAACN,MAAM,CAACP,OAAO,CAACc,QAAQ,EAAE,IAAItD,KAAK,CAACuD,iBAAiB,CAAC,CAAC,CAAC;MACvF,MAAMC,WAAW,GAAG,IAAIxD,KAAK,CAACyD,IAAI,CAAC,CAAC,CAACC,sBAAsB,CAACN,QAAQ,CAACE,QAAQ,CAACK,UAAU,CAACC,QAAQ,CAAC;MAClG,MAAMC,QAAQ,GAAG,IAAI7D,KAAK,CAAC8D,kBAAkB,CAACN,WAAW,CAACO,GAAG,CAACC,CAAC,IAAI,CAAC,GAAG,CAAC,GAAG/C,UAAU,CAAC,EAAEuC,WAAW,CAACd,GAAG,CAACsB,CAAC,IAAI,CAAC,GAAG,CAAC,GAAG/C,UAAU,CAAC,EAAEuC,WAAW,CAACd,GAAG,CAACuB,CAAC,IAAI,CAAC,GAAG,CAAC,GAAGhD,UAAU,CAAC,EAAEuC,WAAW,CAACO,GAAG,CAACE,CAAC,IAAI,CAAC,GAAG,CAAC,GAAGhD,UAAU,CAAC,EAAE,GAAG,EAAE,IAAI,CAAC;MAChO4C,QAAQ,CAACD,QAAQ,CAACM,GAAG,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC;MAC9BL,QAAQ,CAACM,MAAM,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC;MACxBnC,EAAE,CAACoC,eAAe,CAACjC,gBAAgB,CAAC;MACpCH,EAAE,CAACqC,MAAM,CAACjB,QAAQ,EAAES,QAAQ,CAAC;MAC7B,MAAMS,EAAE,GAAGC,gBAAgB,CAACtD,UAAU,EAAEA,UAAU,EAAEe,EAAE,CAAC;MACvD,MAAMlB,GAAG,GAAGwD,EAAE,CAACnC,gBAAgB,CAACqC,OAAO,CAAC;MACxC,MAAMC,OAAO,GAAG,IAAIC,YAAY,CAACzD,UAAU,GAAGA,UAAU,CAAC;MACzDe,EAAE,CAAC2C,sBAAsB,CAAC7D,GAAG,EAAE,CAAC,EAAE,CAAC,EAAEG,UAAU,EAAEA,UAAU,EAAEwD,OAAO,CAAC;MACrE;MACA,IAAIV,GAAG,GAAGa,QAAQ;MAClB,KAAK,IAAIC,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGJ,OAAO,CAACK,MAAM,EAAED,CAAC,EAAE,EAAE;QACvC,IAAIJ,OAAO,CAACI,CAAC,CAAC,GAAGd,GAAG,EAAEA,GAAG,GAAGU,OAAO,CAACI,CAAC,CAAC;MACxC;MACAd,GAAG,GAAG,CAACA,GAAG;MACVlC,GAAG,CAACW,OAAO,CAACxB,IAAI,GAAG+C,GAAG;MACtBlC,GAAG,CAACW,OAAO,CAAC1B,GAAG,GAAGA,GAAG,CAAC0D,OAAO;MAC7BxC,EAAE,CAACoC,eAAe,CAAC,IAAI,CAAC;IAC1B;EACF,CAAC,EAAE,CAACnD,UAAU,EAAEL,IAAI,CAAC,CAAC;EACtBX,KAAK,CAAC8E,mBAAmB,CAACnD,IAAI,EAAE,MAAMC,GAAG,CAACW,OAAO,CAAC;EAClD,MAAMwC,OAAO,GAAG/E,KAAK,CAACgF,WAAW,CAAC,CAACC,KAAK,EAAEC,KAAK,EAAEC,QAAQ,KAAK;IAC5D,IAAIC,aAAa;IACjB,IAAI,CAACtC,MAAM,CAACP,OAAO,EAAE,OAAO,KAAK;IACjC2C,KAAK,CAACG,OAAO,CAACpB,GAAG,CAACgB,KAAK,CAACK,OAAO,GAAGJ,KAAK,CAACnE,IAAI,CAACwE,KAAK,GAAG,CAAC,GAAG,CAAC,EAAE,EAAEN,KAAK,CAACO,OAAO,GAAGN,KAAK,CAACnE,IAAI,CAAC0E,MAAM,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC;IACzGP,KAAK,CAACQ,SAAS,CAACC,aAAa,CAACT,KAAK,CAACG,OAAO,EAAEH,KAAK,CAACU,MAAM,CAAC;IAC1D,IAAI,CAAC,CAACR,aAAa,GAAGxD,GAAG,CAACW,OAAO,KAAK,IAAI,GAAG,KAAK,CAAC,GAAG6C,aAAa,CAACtE,KAAK,MAAM,CAAC,EAAE;MAChF;MACA,MAAM,CAAC+E,YAAY,CAAC,GAAGX,KAAK,CAACQ,SAAS,CAACI,eAAe,CAAChD,MAAM,CAACP,OAAO,CAAC;MACtE,IAAI,CAACsD,YAAY,EAAE;QACjB;QACAX,KAAK,CAACQ,SAAS,CAACE,MAAM,GAAGtE,SAAS;QAClC,OAAO,KAAK;MACd;IACF;EACF,CAAC,EAAE,EAAE,CAAC;EACN,OAAO,aAAatB,KAAK,CAAC+F,aAAa,CAAC,oBAAoB,EAAEjG,QAAQ,CAAC;IACrE8B,GAAG,EAAEA,GAAG;IACRjB,IAAI,EAAEA,IAAI;IACVG,KAAK,EAAE,CAAC;IACRE,UAAU,EAAE,CAACD,IAAI,CAACwE,KAAK,GAAGvD,QAAQ,CAACgE,GAAG,EAAEjF,IAAI,CAAC0E,MAAM,GAAGzD,QAAQ,CAACgE,GAAG,CAAC;IACnEC,MAAM,EAAE;EACV,CAAC,EAAEvE,KAAK,CAAC,EAAE,aAAa1B,KAAK,CAAC+F,aAAa,CAACzF,aAAa,EAAE;IACzD2F,MAAM,EAAE,KAAK;IACbC,MAAM,EAAEtD,OAAO,GAAG+B,QAAQ,GAAG,CAAC;IAC9BpD,aAAa,EAAEA,aAAa;IAC5BC,cAAc,EAAEA,cAAc;IAC9BuD,OAAO,EAAEA;EACX,CAAC,EAAE3D,QAAQ,EAAE,aAAapB,KAAK,CAAC+F,aAAa,CAACI,iBAAiB,EAAE;IAC/D9E,MAAM,EAAEA,MAAM;IACd+E,SAAS,EAAEtE,KAAK;IAChBK,QAAQ,EAAEA,QAAQ;IAClBkE,QAAQ,EAAEzE,GAAG;IACbH,UAAU,EAAEA;EACd,CAAC,CAAC,CAAC,CAAC;AACN,CAAC,CAAC;AACF,SAAS0E,iBAAiBA,CAAC;EACzB9E,MAAM,GAAGC,SAAS;EAClB8E,SAAS;EACTC,QAAQ;EACRlE,QAAQ;EACRV;AACF,CAAC,EAAE;EACD,MAAMK,KAAK,GAAG5B,QAAQ,CAACgF,KAAK,IAAIA,KAAK,CAACpD,KAAK,CAAC;EAC5C,MAAMG,SAAS,GAAG/B,QAAQ,CAACgF,KAAK,IAAIA,KAAK,CAACjD,SAAS,CAAC;EACpD,MAAMqE,OAAO,GAAGjG,MAAM,CAAC,CAAC;EACxB,MAAMkG,OAAO,GAAGlG,MAAM,CAAC,CAAC;EACxBL,KAAK,CAAC+C,eAAe,CAAC,MAAM;IAC1BjB,KAAK,CAAC0E,gBAAgB,GAAG,KAAK;EAChC,CAAC,EAAE,EAAE,CAAC;EACNxG,KAAK,CAAC0C,SAAS,CAAC,MAAM;IACpB,IAAIrB,MAAM,KAAKC,SAAS,EAAEW,SAAS,CAAC;MAClCU,OAAO,EAAEtB;IACX,CAAC,CAAC;EACJ,CAAC,EAAE,CAACA,MAAM,CAAC,CAAC;EACZ,MAAM,CAACoF,IAAI,EAAE3F,KAAK,CAAC,GAAGd,KAAK,CAAC0G,OAAO,CAAC,MAAM;IACxC;IACA,MAAM5F,KAAK,GAAG;MACZ6F,KAAK,EAAE;IACT,CAAC;IACD,MAAMF,IAAI,GAAG,IAAIjG,cAAc,CAAC,IAAIT,KAAK,CAAC6G,cAAc,CAAC;MACvDC,QAAQ,EAAE;QACRC,CAAC,EAAE;UACDH,KAAK,EAAEL,OAAO,CAAC/B;QACjB,CAAC;QACDwC,CAAC,EAAE;UACDJ,KAAK,EAAEJ,OAAO,CAAChC;QACjB,CAAC;QACDzD;MACF,CAAC;MACDkG,YAAY,EAAE,QAAQ;AAC5B;AACA;AACA;AACA;AACA,YAAY;MACNC,cAAc,EAAE,QAAQ;AAC9B;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,wBAAwBxG,OAAO,IAAI,GAAG,GAAG,qBAAqB,GAAG,oBAAoB;AACrF;IACI,CAAC,CAAC,CAAC;IACH,OAAO,CAACgG,IAAI,EAAE3F,KAAK,CAAC;EACtB,CAAC,EAAE,EAAE,CAAC;EACNX,QAAQ,CAAC+E,KAAK,IAAI;IAChB,IAAIgC,iBAAiB;IACrB,IAAIpE,MAAM,GAAGuD,QAAQ,IAAI,IAAI,IAAI,CAACa,iBAAiB,GAAGb,QAAQ,CAAC9D,OAAO,KAAK,IAAI,IAAI,CAAC2E,iBAAiB,GAAGA,iBAAiB,CAACjE,KAAK,CAACH,MAAM,KAAK,IAAI,GAAG,KAAK,CAAC,GAAGoE,iBAAiB,CAAChE,MAAM;IACnL,IAAIJ,MAAM,EAAE;MACV;MACA,IAAI,CAACrB,UAAU,EAAE;QACf,IAAI0F,kBAAkB;QACtB;QACA,IAAIhF,QAAQ,IAAI,CAAC,CAACgF,kBAAkB,GAAGd,QAAQ,CAAC9D,OAAO,KAAK,IAAI,GAAG,KAAK,CAAC,GAAG4E,kBAAkB,CAACrG,KAAK,MAAM,CAAC,EAAEgC,MAAM,CAACsE,iBAAiB,CAAC,IAAI,EAAE,KAAK,CAAC;QAClJtF,KAAK,CAACuF,WAAW,CAACC,IAAI,CAACxE,MAAM,CAACuE,WAAW,CAAC;MAC5C,CAAC,MAAMvF,KAAK,CAACuF,WAAW,CAACE,QAAQ,CAAC,CAAC;;MAEnC;MACA;MACA,IAAIpF,QAAQ,EAAE;QACZ,IAAIqF,kBAAkB,EAAEC,kBAAkB,EAAEC,kBAAkB;QAC9D,IAAI,CAAC,CAACF,kBAAkB,GAAGnB,QAAQ,CAAC9D,OAAO,KAAK,IAAI,GAAG,KAAK,CAAC,GAAGiF,kBAAkB,CAAC1G,KAAK,IAAI,CAAC,IAAI,CAAC,CAAC2G,kBAAkB,GAAGpB,QAAQ,CAAC9D,OAAO,KAAK,IAAI,GAAG,KAAK,CAAC,GAAGkF,kBAAkB,CAAC3G,KAAK,IAAI,CAAC,EAAE;UAC1L;UACA;UACAA,KAAK,CAAC6F,KAAK,GAAGN,QAAQ,CAAC9D,OAAO,CAACzB,KAAK;UACpCoE,KAAK,CAACnD,EAAE,CAACoC,eAAe,CAACmC,OAAO,CAAC;UACjCpB,KAAK,CAACnD,EAAE,CAACqC,MAAM,CAACtC,KAAK,EAAEoD,KAAK,CAACU,MAAM,CAAC;UACpCV,KAAK,CAACnD,EAAE,CAACoC,eAAe,CAACoC,OAAO,CAAC;UACjCrB,KAAK,CAACnD,EAAE,CAACqC,MAAM,CAACgC,SAAS,EAAElB,KAAK,CAACU,MAAM,CAAC;UACxCV,KAAK,CAACnD,EAAE,CAACoC,eAAe,CAAC,IAAI,CAAC;UAC9BsC,IAAI,CAACrC,MAAM,CAACc,KAAK,CAACnD,EAAE,CAAC;QACvB,CAAC,MAAM,IAAI,CAAC,CAAC2F,kBAAkB,GAAGrB,QAAQ,CAAC9D,OAAO,KAAK,IAAI,GAAG,KAAK,CAAC,GAAGmF,kBAAkB,CAAC5G,KAAK,MAAM,CAAC,EAAE;UACtG;UACAoE,KAAK,CAACnD,EAAE,CAACqC,MAAM,CAACtC,KAAK,EAAEoD,KAAK,CAACU,MAAM,CAAC;QACtC;MACF;IACF;EACF,CAAC,EAAEzD,QAAQ,CAAC;EACZ,OAAO,aAAanC,KAAK,CAAC+F,aAAa,CAAC/F,KAAK,CAAC2H,QAAQ,EAAE,IAAI,CAAC;AAC/D;AACA,MAAMrD,gBAAgB,GAAGA,CAACsD,WAAW,EAAEC,YAAY,EAAEC,QAAQ,KAAK;EAChE,IAAIC,WAAW,GAAG,IAAIhI,KAAK,CAACiI,iBAAiB,CAACJ,WAAW,EAAEC,YAAY,EAAE;IACvEI,SAAS,EAAElI,KAAK,CAACmI,wBAAwB;IACzCC,SAAS,EAAEpI,KAAK,CAACqI,YAAY;IAC7BC,IAAI,EAAEtI,KAAK,CAACuI,SAAS;IACrBC,MAAM,EAAExI,KAAK,CAACyI,SAAS;IACvBC,eAAe,EAAE;EACnB,CAAC,CAAC;EACF,IAAIC,mBAAmB,GAAG,IAAI3I,KAAK,CAACiI,iBAAiB,CAACJ,WAAW,EAAEC,YAAY,EAAE;IAC/EI,SAAS,EAAElI,KAAK,CAAC4I,aAAa;IAC9BR,SAAS,EAAEpI,KAAK,CAAC4I;EACnB,CAAC,CAAC;EACF,IAAIC,kBAAkB,GAAG,IAAI7I,KAAK,CAACiI,iBAAiB,CAACJ,WAAW,EAAEC,YAAY,EAAE;IAC9EI,SAAS,EAAElI,KAAK,CAAC4I,aAAa;IAC9BR,SAAS,EAAEpI,KAAK,CAAC4I;EACnB,CAAC,CAAC;EACF,IAAIE,oBAAoB,GAAG,IAAI9I,KAAK,CAACiI,iBAAiB,CAACJ,WAAW,EAAEC,YAAY,EAAE;IAChFI,SAAS,EAAElI,KAAK,CAAC4I,aAAa;IAC9BR,SAAS,EAAEpI,KAAK,CAAC4I;EACnB,CAAC,CAAC;EACF,IAAIG,mBAAmB,GAAG,IAAI/I,KAAK,CAACiI,iBAAiB,CAACJ,WAAW,EAAEC,YAAY,EAAE;IAC/EI,SAAS,EAAElI,KAAK,CAAC4I,aAAa;IAC9BR,SAAS,EAAEpI,KAAK,CAAC4I;EACnB,CAAC,CAAC;EACF,IAAII,wBAAwB,GAAG,IAAIhJ,KAAK,CAACiI,iBAAiB,CAACJ,WAAW,EAAEC,YAAY,EAAE;IACpFI,SAAS,EAAElI,KAAK,CAAC4I,aAAa;IAC9BR,SAAS,EAAEpI,KAAK,CAAC4I,aAAa;IAC9BN,IAAI,EAAEtI,KAAK,CAACuI,SAAS;IACrBC,MAAM,EAAExI,KAAK,CAACyI;EAChB,CAAC,CAAC;EACF,IAAIQ,uBAAuB,GAAG,IAAIjJ,KAAK,CAACiI,iBAAiB,CAACJ,WAAW,EAAEC,YAAY,EAAE;IACnFI,SAAS,EAAElI,KAAK,CAAC4I,aAAa;IAC9BR,SAAS,EAAEpI,KAAK,CAAC4I,aAAa;IAC9BN,IAAI,EAAEtI,KAAK,CAACuI,SAAS;IACrBC,MAAM,EAAExI,KAAK,CAACyI;EAChB,CAAC,CAAC;EACF,MAAMS,QAAQ,GAAG,IAAIzI,cAAc,CAAC,IAAIT,KAAK,CAAC6G,cAAc,CAAC;IAC3DC,QAAQ,EAAE;MACRqC,GAAG,EAAE;QACHvC,KAAK,EAAE;MACT;IACF,CAAC;IACDK,YAAY,EAAE,QAAQ;AAC1B;AACA;AACA;AACA;AACA,UAAU;IACNC,cAAc,EAAE,QAAQ;AAC5B;AACA;AACA;AACA;AACA;AACA;EACE,CAAC,CAAC,CAAC;EACH,MAAMkC,cAAc,GAAG,IAAI3I,cAAc,CAAC,IAAIT,KAAK,CAAC6G,cAAc,CAAC;IACjEC,QAAQ,EAAE;MACRqC,GAAG,EAAE;QACHvC,KAAK,EAAE;MACT;IACF,CAAC;IACDK,YAAY,EAAE,QAAQ;AAC1B;AACA;AACA;AACA;AACA,UAAU;IACNC,cAAc,EAAE,QAAQ;AAC5B;AACA;AACA;AACA;AACA;AACA;EACE,CAAC,CAAC,CAAC;EACH,MAAMmC,eAAe,GAAG,IAAI5I,cAAc,CAAC,IAAIT,KAAK,CAAC6G,cAAc,CAAC;IAClEC,QAAQ,EAAE;MACRqC,GAAG,EAAE;QACHvC,KAAK,EAAE;MACT,CAAC;MACD0C,MAAM,EAAE;QACN1C,KAAK,EAAE;MACT,CAAC;MACD2C,KAAK,EAAE;QACL3C,KAAK,EAAE;MACT,CAAC;MACD4C,QAAQ,EAAE;QACR5C,KAAK,EAAE;MACT;IACF,CAAC;IACDK,YAAY,EAAE,QAAQ;AAC1B;AACA;AACA;AACA;AACA,UAAU;IACNC,cAAc,EAAE,QAAQ;AAC5B;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,6CAA6C,CAAC,GAAGW,WAAW,KAAK,CAAC,GAAGC,YAAY;AACjF;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;EACE,CAAC,CAAC,CAAC;EACH,MAAM2B,mBAAmB,GAAG,IAAIhJ,cAAc,CAAC,IAAIT,KAAK,CAAC6G,cAAc,CAAC;IACtEC,QAAQ,EAAE;MACRqC,GAAG,EAAE;QACHvC,KAAK,EAAE;MACT,CAAC;MACD5F,IAAI,EAAE;QACJ4F,KAAK,EAAE,IAAI5G,KAAK,CAACkB,OAAO,CAAC2G,WAAW,EAAEC,YAAY;MACpD;IACF,CAAC;IACDb,YAAY,EAAE,QAAQ;AAC1B;AACA;AACA;AACA;AACA,UAAU;IACNC,cAAc,EAAE,QAAQ;AAC5B;AACA;AACA;AACA;AACA;AACA;AACA;EACE,CAAC,CAAC,CAAC;EACH,MAAMwC,eAAe,GAAG,IAAIjJ,cAAc,CAAC,IAAIT,KAAK,CAAC6G,cAAc,CAAC;IAClEC,QAAQ,EAAE;MACR6C,MAAM,EAAE;QACN/C,KAAK,EAAEqC,uBAAuB,CAACzE;MACjC,CAAC;MACDoF,OAAO,EAAE;QACPhD,KAAK,EAAEoC,wBAAwB,CAACxE;MAClC,CAAC;MACD2E,GAAG,EAAE;QACHvC,KAAK,EAAE;MACT;IACF,CAAC;IACDK,YAAY,EAAE,QAAQ;AAC1B;AACA;AACA;AACA;AACA,UAAU;IACNC,cAAc,EAAE,QAAQ;AAC5B;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;EACE,CAAC,CAAC,CAAC;EACH,OAAO2C,KAAK,IAAI;IACd,IAAIC,EAAE,GAAG9B,WAAW;IACpB6B,KAAK,CAAC3B,SAAS,GAAGlI,KAAK,CAAC4I,aAAa;IACrCiB,KAAK,CAACzB,SAAS,GAAGpI,KAAK,CAAC4I,aAAa;IACrCM,QAAQ,CAAC5C,QAAQ,CAACQ,QAAQ,CAACqC,GAAG,CAACvC,KAAK,GAAGiD,KAAK;IAC5C9B,QAAQ,CAAC3D,eAAe,CAACuE,mBAAmB,CAAC;IAC7CO,QAAQ,CAAC7E,MAAM,CAAC0D,QAAQ,CAAC;IACzB,MAAMgC,MAAM,GAAGtH,IAAI,CAACuH,IAAI,CAACvH,IAAI,CAACwH,GAAG,CAACxH,IAAI,CAACC,GAAG,CAACmF,WAAW,EAAEC,YAAY,CAAC,CAAC,GAAGrF,IAAI,CAACwH,GAAG,CAAC,GAAG,CAAC,CAAC;IACvF,IAAIC,UAAU,GAAGvB,mBAAmB;IACpC,IAAIwB,MAAM,GAAG,IAAI;IACjB,KAAK,IAAItF,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGkF,MAAM,EAAElF,CAAC,EAAE,EAAE;MAC/B,MAAMyE,MAAM,GAAG7G,IAAI,CAAC2H,GAAG,CAAC,CAAC,EAAEL,MAAM,GAAGlF,CAAC,GAAG,CAAC,CAAC;MAC1CsF,MAAM,GAAGD,UAAU,KAAKvB,mBAAmB,GAAGG,oBAAoB,GAAGH,mBAAmB;MACxFU,eAAe,CAAC/C,QAAQ,CAACQ,QAAQ,CAACyC,KAAK,CAAC3C,KAAK,GAAG/B,CAAC;MACjDwE,eAAe,CAAC/C,QAAQ,CAACQ,QAAQ,CAAC0C,QAAQ,CAAC5C,KAAK,GAAGmD,MAAM;MACzDV,eAAe,CAAC/C,QAAQ,CAACQ,QAAQ,CAACwC,MAAM,CAAC1C,KAAK,GAAG0C,MAAM;MACvDD,eAAe,CAAC/C,QAAQ,CAACQ,QAAQ,CAACqC,GAAG,CAACvC,KAAK,GAAGsD,UAAU,CAAC1F,OAAO;MAChEuD,QAAQ,CAAC3D,eAAe,CAAC+F,MAAM,CAAC;MAChCd,eAAe,CAAChF,MAAM,CAAC0D,QAAQ,CAAC;MAChCmC,UAAU,GAAGC,MAAM;IACrB;IACApC,QAAQ,CAAC3D,eAAe,CAAC4E,wBAAwB,CAAC;IAClDS,mBAAmB,CAACnD,QAAQ,CAACQ,QAAQ,CAACqC,GAAG,CAACvC,KAAK,GAAGuD,MAAM,CAAC3F,OAAO;IAChEiF,mBAAmB,CAACpF,MAAM,CAAC0D,QAAQ,CAAC;IACpCqB,cAAc,CAAC9C,QAAQ,CAACQ,QAAQ,CAACqC,GAAG,CAACvC,KAAK,GAAGiD,KAAK;IAClD9B,QAAQ,CAAC3D,eAAe,CAACyE,kBAAkB,CAAC;IAC5CO,cAAc,CAAC/E,MAAM,CAAC0D,QAAQ,CAAC;IAC/BmC,UAAU,GAAGrB,kBAAkB;IAC/B,KAAK,IAAIhE,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGkF,MAAM,EAAElF,CAAC,EAAE,EAAE;MAC/B,MAAMyE,MAAM,GAAG7G,IAAI,CAAC2H,GAAG,CAAC,CAAC,EAAEL,MAAM,GAAGlF,CAAC,GAAG,CAAC,CAAC;MAC1CsF,MAAM,GAAGD,UAAU,KAAKrB,kBAAkB,GAAGE,mBAAmB,GAAGF,kBAAkB;MACrFQ,eAAe,CAAC/C,QAAQ,CAACQ,QAAQ,CAACyC,KAAK,CAAC3C,KAAK,GAAG/B,CAAC;MACjDwE,eAAe,CAAC/C,QAAQ,CAACQ,QAAQ,CAAC0C,QAAQ,CAAC5C,KAAK,GAAGmD,MAAM;MACzDV,eAAe,CAAC/C,QAAQ,CAACQ,QAAQ,CAACwC,MAAM,CAAC1C,KAAK,GAAG0C,MAAM;MACvDD,eAAe,CAAC/C,QAAQ,CAACQ,QAAQ,CAACqC,GAAG,CAACvC,KAAK,GAAGsD,UAAU,CAAC1F,OAAO;MAChEuD,QAAQ,CAAC3D,eAAe,CAAC+F,MAAM,CAAC;MAChCd,eAAe,CAAChF,MAAM,CAAC0D,QAAQ,CAAC;MAChCmC,UAAU,GAAGC,MAAM;IACrB;IACApC,QAAQ,CAAC3D,eAAe,CAAC6E,uBAAuB,CAAC;IACjDQ,mBAAmB,CAACnD,QAAQ,CAACQ,QAAQ,CAACqC,GAAG,CAACvC,KAAK,GAAGuD,MAAM,CAAC3F,OAAO;IAChEiF,mBAAmB,CAACpF,MAAM,CAAC0D,QAAQ,CAAC;IACpCA,QAAQ,CAAC3D,eAAe,CAAC0F,EAAE,CAAC;IAC5BJ,eAAe,CAACpD,QAAQ,CAACQ,QAAQ,CAACqC,GAAG,CAACvC,KAAK,GAAGiD,KAAK;IACnDH,eAAe,CAACrF,MAAM,CAAC0D,QAAQ,CAAC;IAChCA,QAAQ,CAAC3D,eAAe,CAAC,IAAI,CAAC;IAC9B,OAAO0F,EAAE;EACX,CAAC;AACH,CAAC;AAED,SAAS3I,kBAAkB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}