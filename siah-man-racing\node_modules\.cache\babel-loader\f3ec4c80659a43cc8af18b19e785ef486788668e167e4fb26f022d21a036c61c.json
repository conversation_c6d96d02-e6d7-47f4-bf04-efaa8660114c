{"ast": null, "code": "let webGLAvailable, webGL2Available;\nfunction isWebGLAvailable() {\n  var _a;\n  if (webGLAvailable !== void 0) return webGLAvailable;\n  try {\n    let gl;\n    const canvas = document.createElement(\"canvas\");\n    webGLAvailable = !!(window.WebGLRenderingContext && (gl = canvas.getContext(\"webgl\")));\n    if (gl) (_a = gl.getExtension(\"WEBGL_lose_context\")) == null ? void 0 : _a.loseContext();\n    return webGLAvailable;\n  } catch (e) {\n    return webGLAvailable = false;\n  }\n}\nfunction isWebGL2Available() {\n  var _a;\n  if (webGL2Available !== void 0) return webGL2Available;\n  try {\n    let gl;\n    const canvas = document.createElement(\"canvas\");\n    webGL2Available = !!(window.WebGL2RenderingContext && (gl = canvas.getContext(\"webgl2\")));\n    if (gl) (_a = gl.getExtension(\"WEBGL_lose_context\")) == null ? void 0 : _a.loseContext();\n    return webGL2Available;\n  } catch (e) {\n    return webGL2Available = false;\n  }\n}\nfunction getWebGLErrorMessage() {\n  return getErrorMessage(1);\n}\nfunction getWebGL2ErrorMessage() {\n  return getErrorMessage(2);\n}\nfunction getErrorMessage(version) {\n  const names = {\n    1: \"WebGL\",\n    2: \"WebGL 2\"\n  };\n  const contexts = {\n    1: window.WebGLRenderingContext,\n    2: window.WebGL2RenderingContext\n  };\n  const element = document.createElement(\"div\");\n  element.id = \"webglmessage\";\n  element.style.fontFamily = \"monospace\";\n  element.style.fontSize = \"13px\";\n  element.style.fontWeight = \"normal\";\n  element.style.textAlign = \"center\";\n  element.style.background = \"#fff\";\n  element.style.color = \"#000\";\n  element.style.padding = \"1.5em\";\n  element.style.width = \"400px\";\n  element.style.margin = \"5em auto 0\";\n  let message = 'Your $0 does not seem to support <a href=\"http://khronos.org/webgl/wiki/Getting_a_WebGL_Implementation\" style=\"color:#000\">$1</a>';\n  if (contexts[version]) {\n    message = message.replace(\"$0\", \"graphics card\");\n  } else {\n    message = message.replace(\"$0\", \"browser\");\n  }\n  message = message.replace(\"$1\", names[version]);\n  element.innerHTML = message;\n  return element;\n}\nexport { getErrorMessage, getWebGL2ErrorMessage, getWebGLErrorMessage, isWebGL2Available, isWebGLAvailable };", "map": {"version": 3, "names": ["webGLAvailable", "webGL2Available", "isWebGLAvailable", "_a", "gl", "canvas", "document", "createElement", "window", "WebGLRenderingContext", "getContext", "getExtension", "loseContext", "e", "isWebGL2Available", "WebGL2RenderingContext", "getWebGLErrorMessage", "getErrorMessage", "getWebGL2ErrorMessage", "version", "names", "contexts", "element", "id", "style", "fontFamily", "fontSize", "fontWeight", "textAlign", "background", "color", "padding", "width", "margin", "message", "replace", "innerHTML"], "sources": ["C:\\Users\\<USER>\\Documents\\Apps_Creations\\SiahMan_Racing4Speed_WebGame\\siah-man-racing\\node_modules\\src\\misc\\WebGL.ts"], "sourcesContent": ["let webGLAvailable: boolean, webGL2Available: boolean\n\nexport function isWebGLAvailable(): boolean {\n  if (webGLAvailable !== undefined) return webGLAvailable\n  try {\n    let gl\n    const canvas = document.createElement('canvas')\n    webGLAvailable = !!(window.WebGLRenderingContext && (gl = canvas.getContext('webgl')))\n    if (gl) gl.getExtension('WEBGL_lose_context')?.loseContext()\n    return webGLAvailable\n  } catch (e) {\n    return (webGLAvailable = false)\n  }\n}\n\nexport function isWebGL2Available(): boolean {\n  if (webGL2Available !== undefined) return webGL2Available\n  try {\n    let gl\n    const canvas = document.createElement('canvas')\n    webGL2Available = !!(window.WebGL2RenderingContext && (gl = canvas.getContext('webgl2')))\n    if (gl) gl.getExtension('WEBGL_lose_context')?.loseContext()\n    return webGL2Available\n  } catch (e) {\n    return (webGL2Available = false)\n  }\n}\n\nexport function getWebGLErrorMessage(): HTMLDivElement {\n  return getErrorMessage(1)\n}\n\nexport function getWebGL2ErrorMessage(): HTMLDivElement {\n  return getErrorMessage(2)\n}\n\nexport function getErrorMessage(version: 1 | 2): HTMLDivElement {\n  const names = {\n    1: 'WebGL',\n    2: 'WebGL 2',\n  }\n\n  const contexts = {\n    1: window.WebGLRenderingContext,\n    2: window.WebGL2RenderingContext,\n  }\n\n  const element = document.createElement('div')\n  element.id = 'webglmessage'\n  element.style.fontFamily = 'monospace'\n  element.style.fontSize = '13px'\n  element.style.fontWeight = 'normal'\n  element.style.textAlign = 'center'\n  element.style.background = '#fff'\n  element.style.color = '#000'\n  element.style.padding = '1.5em'\n  element.style.width = '400px'\n  element.style.margin = '5em auto 0'\n\n  let message =\n    'Your $0 does not seem to support <a href=\"http://khronos.org/webgl/wiki/Getting_a_WebGL_Implementation\" style=\"color:#000\">$1</a>'\n\n  if (contexts[version]) {\n    message = message.replace('$0', 'graphics card')\n  } else {\n    message = message.replace('$0', 'browser')\n  }\n\n  message = message.replace('$1', names[version])\n  element.innerHTML = message\n  return element\n}\n"], "mappings": "AAAA,IAAIA,cAAA,EAAyBC,eAAA;AAEtB,SAASC,iBAAA,EAA4B;EAF5C,IAAAC,EAAA;EAGE,IAAIH,cAAA,KAAmB,QAAkB,OAAAA,cAAA;EACrC;IACE,IAAAI,EAAA;IACE,MAAAC,MAAA,GAASC,QAAA,CAASC,aAAA,CAAc,QAAQ;IAC9CP,cAAA,GAAiB,CAAC,EAAEQ,MAAA,CAAOC,qBAAA,KAA0BL,EAAA,GAAKC,MAAA,CAAOK,UAAA,CAAW,OAAO;IAC/E,IAAAN,EAAA,EAAO,CAAAD,EAAA,GAAAC,EAAA,CAAAO,YAAA,CAAa,oBAAoB,MAAjC,gBAAAR,EAAA,CAAoCS,WAAA;IACxC,OAAAZ,cAAA;EAAA,SACAa,CAAA;IACP,OAAQb,cAAA,GAAiB;EAC3B;AACF;AAEO,SAASc,kBAAA,EAA6B;EAf7C,IAAAX,EAAA;EAgBE,IAAIF,eAAA,KAAoB,QAAkB,OAAAA,eAAA;EACtC;IACE,IAAAG,EAAA;IACE,MAAAC,MAAA,GAASC,QAAA,CAASC,aAAA,CAAc,QAAQ;IAC9CN,eAAA,GAAkB,CAAC,EAAEO,MAAA,CAAOO,sBAAA,KAA2BX,EAAA,GAAKC,MAAA,CAAOK,UAAA,CAAW,QAAQ;IAClF,IAAAN,EAAA,EAAO,CAAAD,EAAA,GAAAC,EAAA,CAAAO,YAAA,CAAa,oBAAoB,MAAjC,gBAAAR,EAAA,CAAoCS,WAAA;IACxC,OAAAX,eAAA;EAAA,SACAY,CAAA;IACP,OAAQZ,eAAA,GAAkB;EAC5B;AACF;AAEO,SAASe,qBAAA,EAAuC;EACrD,OAAOC,eAAA,CAAgB,CAAC;AAC1B;AAEO,SAASC,sBAAA,EAAwC;EACtD,OAAOD,eAAA,CAAgB,CAAC;AAC1B;AAEO,SAASA,gBAAgBE,OAAA,EAAgC;EAC9D,MAAMC,KAAA,GAAQ;IACZ,GAAG;IACH,GAAG;EAAA;EAGL,MAAMC,QAAA,GAAW;IACf,GAAGb,MAAA,CAAOC,qBAAA;IACV,GAAGD,MAAA,CAAOO;EAAA;EAGN,MAAAO,OAAA,GAAUhB,QAAA,CAASC,aAAA,CAAc,KAAK;EAC5Ce,OAAA,CAAQC,EAAA,GAAK;EACbD,OAAA,CAAQE,KAAA,CAAMC,UAAA,GAAa;EAC3BH,OAAA,CAAQE,KAAA,CAAME,QAAA,GAAW;EACzBJ,OAAA,CAAQE,KAAA,CAAMG,UAAA,GAAa;EAC3BL,OAAA,CAAQE,KAAA,CAAMI,SAAA,GAAY;EAC1BN,OAAA,CAAQE,KAAA,CAAMK,UAAA,GAAa;EAC3BP,OAAA,CAAQE,KAAA,CAAMM,KAAA,GAAQ;EACtBR,OAAA,CAAQE,KAAA,CAAMO,OAAA,GAAU;EACxBT,OAAA,CAAQE,KAAA,CAAMQ,KAAA,GAAQ;EACtBV,OAAA,CAAQE,KAAA,CAAMS,MAAA,GAAS;EAEvB,IAAIC,OAAA,GACF;EAEE,IAAAb,QAAA,CAASF,OAAO,GAAG;IACXe,OAAA,GAAAA,OAAA,CAAQC,OAAA,CAAQ,MAAM,eAAe;EAAA,OAC1C;IACKD,OAAA,GAAAA,OAAA,CAAQC,OAAA,CAAQ,MAAM,SAAS;EAC3C;EAEAD,OAAA,GAAUA,OAAA,CAAQC,OAAA,CAAQ,MAAMf,KAAA,CAAMD,OAAO,CAAC;EAC9CG,OAAA,CAAQc,SAAA,GAAYF,OAAA;EACb,OAAAZ,OAAA;AACT", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}