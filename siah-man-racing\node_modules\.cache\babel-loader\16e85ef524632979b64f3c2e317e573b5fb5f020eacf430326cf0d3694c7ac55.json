{"ast": null, "code": "// src/devModeChecks/identityFunctionCheck.ts\nvar runIdentityFunctionCheck = (resultFunc, inputSelectorsResults, outputSelectorResult) => {\n  if (inputSelectorsResults.length === 1 && inputSelectorsResults[0] === outputSelectorResult) {\n    let isInputSameAsOutput = false;\n    try {\n      const emptyObject = {};\n      if (resultFunc(emptyObject) === emptyObject) isInputSameAsOutput = true;\n    } catch {}\n    if (isInputSameAsOutput) {\n      let stack = void 0;\n      try {\n        throw new Error();\n      } catch (e) {\n        ;\n        ({\n          stack\n        } = e);\n      }\n      console.warn(\"The result function returned its own inputs without modification. e.g\\n`createSelector([state => state.todos], todos => todos)`\\nThis could lead to inefficient memoization and unnecessary re-renders.\\nEnsure transformation logic is in the result function, and extraction logic is in the input selectors.\", {\n        stack\n      });\n    }\n  }\n};\n\n// src/devModeChecks/inputStabilityCheck.ts\nvar runInputStabilityCheck = (inputSelectorResultsObject, options, inputSelectorArgs) => {\n  const {\n    memoize,\n    memoizeOptions\n  } = options;\n  const {\n    inputSelectorResults,\n    inputSelectorResultsCopy\n  } = inputSelectorResultsObject;\n  const createAnEmptyObject = memoize(() => ({}), ...memoizeOptions);\n  const areInputSelectorResultsEqual = createAnEmptyObject.apply(null, inputSelectorResults) === createAnEmptyObject.apply(null, inputSelectorResultsCopy);\n  if (!areInputSelectorResultsEqual) {\n    let stack = void 0;\n    try {\n      throw new Error();\n    } catch (e) {\n      ;\n      ({\n        stack\n      } = e);\n    }\n    console.warn(\"An input selector returned a different result when passed same arguments.\\nThis means your output selector will likely run more frequently than intended.\\nAvoid returning a new reference inside your input selector, e.g.\\n`createSelector([state => state.todos.map(todo => todo.id)], todoIds => todoIds.length)`\", {\n      arguments: inputSelectorArgs,\n      firstInputs: inputSelectorResults,\n      secondInputs: inputSelectorResultsCopy,\n      stack\n    });\n  }\n};\n\n// src/devModeChecks/setGlobalDevModeChecks.ts\nvar globalDevModeChecks = {\n  inputStabilityCheck: \"once\",\n  identityFunctionCheck: \"once\"\n};\nvar setGlobalDevModeChecks = devModeChecks => {\n  Object.assign(globalDevModeChecks, devModeChecks);\n};\n\n// src/utils.ts\nvar NOT_FOUND = /* @__PURE__ */Symbol(\"NOT_FOUND\");\nfunction assertIsFunction(func, errorMessage = `expected a function, instead received ${typeof func}`) {\n  if (typeof func !== \"function\") {\n    throw new TypeError(errorMessage);\n  }\n}\nfunction assertIsObject(object, errorMessage = `expected an object, instead received ${typeof object}`) {\n  if (typeof object !== \"object\") {\n    throw new TypeError(errorMessage);\n  }\n}\nfunction assertIsArrayOfFunctions(array, errorMessage = `expected all items to be functions, instead received the following types: `) {\n  if (!array.every(item => typeof item === \"function\")) {\n    const itemTypes = array.map(item => typeof item === \"function\" ? `function ${item.name || \"unnamed\"}()` : typeof item).join(\", \");\n    throw new TypeError(`${errorMessage}[${itemTypes}]`);\n  }\n}\nvar ensureIsArray = item => {\n  return Array.isArray(item) ? item : [item];\n};\nfunction getDependencies(createSelectorArgs) {\n  const dependencies = Array.isArray(createSelectorArgs[0]) ? createSelectorArgs[0] : createSelectorArgs;\n  assertIsArrayOfFunctions(dependencies, `createSelector expects all input-selectors to be functions, but received the following types: `);\n  return dependencies;\n}\nfunction collectInputSelectorResults(dependencies, inputSelectorArgs) {\n  const inputSelectorResults = [];\n  const {\n    length\n  } = dependencies;\n  for (let i = 0; i < length; i++) {\n    inputSelectorResults.push(dependencies[i].apply(null, inputSelectorArgs));\n  }\n  return inputSelectorResults;\n}\nvar getDevModeChecksExecutionInfo = (firstRun, devModeChecks) => {\n  const {\n    identityFunctionCheck,\n    inputStabilityCheck\n  } = {\n    ...globalDevModeChecks,\n    ...devModeChecks\n  };\n  return {\n    identityFunctionCheck: {\n      shouldRun: identityFunctionCheck === \"always\" || identityFunctionCheck === \"once\" && firstRun,\n      run: runIdentityFunctionCheck\n    },\n    inputStabilityCheck: {\n      shouldRun: inputStabilityCheck === \"always\" || inputStabilityCheck === \"once\" && firstRun,\n      run: runInputStabilityCheck\n    }\n  };\n};\n\n// src/autotrackMemoize/autotracking.ts\nvar $REVISION = 0;\nvar CURRENT_TRACKER = null;\nvar Cell = class {\n  revision = $REVISION;\n  _value;\n  _lastValue;\n  _isEqual = tripleEq;\n  constructor(initialValue, isEqual = tripleEq) {\n    this._value = this._lastValue = initialValue;\n    this._isEqual = isEqual;\n  }\n  // Whenever a storage value is read, it'll add itself to the current tracker if\n  // one exists, entangling its state with that cache.\n  get value() {\n    CURRENT_TRACKER?.add(this);\n    return this._value;\n  }\n  // Whenever a storage value is updated, we bump the global revision clock,\n  // assign the revision for this storage to the new value, _and_ we schedule a\n  // rerender. This is important, and it's what makes autotracking  _pull_\n  // based. We don't actively tell the caches which depend on the storage that\n  // anything has happened. Instead, we recompute the caches when needed.\n  set value(newValue) {\n    if (this.value === newValue) return;\n    this._value = newValue;\n    this.revision = ++$REVISION;\n  }\n};\nfunction tripleEq(a, b) {\n  return a === b;\n}\nvar TrackingCache = class {\n  _cachedValue;\n  _cachedRevision = -1;\n  _deps = [];\n  hits = 0;\n  fn;\n  constructor(fn) {\n    this.fn = fn;\n  }\n  clear() {\n    this._cachedValue = void 0;\n    this._cachedRevision = -1;\n    this._deps = [];\n    this.hits = 0;\n  }\n  get value() {\n    if (this.revision > this._cachedRevision) {\n      const {\n        fn\n      } = this;\n      const currentTracker = /* @__PURE__ */new Set();\n      const prevTracker = CURRENT_TRACKER;\n      CURRENT_TRACKER = currentTracker;\n      this._cachedValue = fn();\n      CURRENT_TRACKER = prevTracker;\n      this.hits++;\n      this._deps = Array.from(currentTracker);\n      this._cachedRevision = this.revision;\n    }\n    CURRENT_TRACKER?.add(this);\n    return this._cachedValue;\n  }\n  get revision() {\n    return Math.max(...this._deps.map(d => d.revision), 0);\n  }\n};\nfunction getValue(cell) {\n  if (!(cell instanceof Cell)) {\n    console.warn(\"Not a valid cell! \", cell);\n  }\n  return cell.value;\n}\nfunction setValue(storage, value) {\n  if (!(storage instanceof Cell)) {\n    throw new TypeError(\"setValue must be passed a tracked store created with `createStorage`.\");\n  }\n  storage.value = storage._lastValue = value;\n}\nfunction createCell(initialValue, isEqual = tripleEq) {\n  return new Cell(initialValue, isEqual);\n}\nfunction createCache(fn) {\n  assertIsFunction(fn, \"the first parameter to `createCache` must be a function\");\n  return new TrackingCache(fn);\n}\n\n// src/autotrackMemoize/tracking.ts\nvar neverEq = (a, b) => false;\nfunction createTag() {\n  return createCell(null, neverEq);\n}\nfunction dirtyTag(tag, value) {\n  setValue(tag, value);\n}\nvar consumeCollection = node => {\n  let tag = node.collectionTag;\n  if (tag === null) {\n    tag = node.collectionTag = createTag();\n  }\n  getValue(tag);\n};\nvar dirtyCollection = node => {\n  const tag = node.collectionTag;\n  if (tag !== null) {\n    dirtyTag(tag, null);\n  }\n};\n\n// src/autotrackMemoize/proxy.ts\nvar REDUX_PROXY_LABEL = Symbol();\nvar nextId = 0;\nvar proto = Object.getPrototypeOf({});\nvar ObjectTreeNode = class {\n  constructor(value) {\n    this.value = value;\n    this.value = value;\n    this.tag.value = value;\n  }\n  proxy = new Proxy(this, objectProxyHandler);\n  tag = createTag();\n  tags = {};\n  children = {};\n  collectionTag = null;\n  id = nextId++;\n};\nvar objectProxyHandler = {\n  get(node, key) {\n    function calculateResult() {\n      const {\n        value\n      } = node;\n      const childValue = Reflect.get(value, key);\n      if (typeof key === \"symbol\") {\n        return childValue;\n      }\n      if (key in proto) {\n        return childValue;\n      }\n      if (typeof childValue === \"object\" && childValue !== null) {\n        let childNode = node.children[key];\n        if (childNode === void 0) {\n          childNode = node.children[key] = createNode(childValue);\n        }\n        if (childNode.tag) {\n          getValue(childNode.tag);\n        }\n        return childNode.proxy;\n      } else {\n        let tag = node.tags[key];\n        if (tag === void 0) {\n          tag = node.tags[key] = createTag();\n          tag.value = childValue;\n        }\n        getValue(tag);\n        return childValue;\n      }\n    }\n    const res = calculateResult();\n    return res;\n  },\n  ownKeys(node) {\n    consumeCollection(node);\n    return Reflect.ownKeys(node.value);\n  },\n  getOwnPropertyDescriptor(node, prop) {\n    return Reflect.getOwnPropertyDescriptor(node.value, prop);\n  },\n  has(node, prop) {\n    return Reflect.has(node.value, prop);\n  }\n};\nvar ArrayTreeNode = class {\n  constructor(value) {\n    this.value = value;\n    this.value = value;\n    this.tag.value = value;\n  }\n  proxy = new Proxy([this], arrayProxyHandler);\n  tag = createTag();\n  tags = {};\n  children = {};\n  collectionTag = null;\n  id = nextId++;\n};\nvar arrayProxyHandler = {\n  get([node], key) {\n    if (key === \"length\") {\n      consumeCollection(node);\n    }\n    return objectProxyHandler.get(node, key);\n  },\n  ownKeys([node]) {\n    return objectProxyHandler.ownKeys(node);\n  },\n  getOwnPropertyDescriptor([node], prop) {\n    return objectProxyHandler.getOwnPropertyDescriptor(node, prop);\n  },\n  has([node], prop) {\n    return objectProxyHandler.has(node, prop);\n  }\n};\nfunction createNode(value) {\n  if (Array.isArray(value)) {\n    return new ArrayTreeNode(value);\n  }\n  return new ObjectTreeNode(value);\n}\nfunction updateNode(node, newValue) {\n  const {\n    value,\n    tags,\n    children\n  } = node;\n  node.value = newValue;\n  if (Array.isArray(value) && Array.isArray(newValue) && value.length !== newValue.length) {\n    dirtyCollection(node);\n  } else {\n    if (value !== newValue) {\n      let oldKeysSize = 0;\n      let newKeysSize = 0;\n      let anyKeysAdded = false;\n      for (const _key in value) {\n        oldKeysSize++;\n      }\n      for (const key in newValue) {\n        newKeysSize++;\n        if (!(key in value)) {\n          anyKeysAdded = true;\n          break;\n        }\n      }\n      const isDifferent = anyKeysAdded || oldKeysSize !== newKeysSize;\n      if (isDifferent) {\n        dirtyCollection(node);\n      }\n    }\n  }\n  for (const key in tags) {\n    const childValue = value[key];\n    const newChildValue = newValue[key];\n    if (childValue !== newChildValue) {\n      dirtyCollection(node);\n      dirtyTag(tags[key], newChildValue);\n    }\n    if (typeof newChildValue === \"object\" && newChildValue !== null) {\n      delete tags[key];\n    }\n  }\n  for (const key in children) {\n    const childNode = children[key];\n    const newChildValue = newValue[key];\n    const childValue = childNode.value;\n    if (childValue === newChildValue) {\n      continue;\n    } else if (typeof newChildValue === \"object\" && newChildValue !== null) {\n      updateNode(childNode, newChildValue);\n    } else {\n      deleteNode(childNode);\n      delete children[key];\n    }\n  }\n}\nfunction deleteNode(node) {\n  if (node.tag) {\n    dirtyTag(node.tag, null);\n  }\n  dirtyCollection(node);\n  for (const key in node.tags) {\n    dirtyTag(node.tags[key], null);\n  }\n  for (const key in node.children) {\n    deleteNode(node.children[key]);\n  }\n}\n\n// src/lruMemoize.ts\nfunction createSingletonCache(equals) {\n  let entry;\n  return {\n    get(key) {\n      if (entry && equals(entry.key, key)) {\n        return entry.value;\n      }\n      return NOT_FOUND;\n    },\n    put(key, value) {\n      entry = {\n        key,\n        value\n      };\n    },\n    getEntries() {\n      return entry ? [entry] : [];\n    },\n    clear() {\n      entry = void 0;\n    }\n  };\n}\nfunction createLruCache(maxSize, equals) {\n  let entries = [];\n  function get(key) {\n    const cacheIndex = entries.findIndex(entry => equals(key, entry.key));\n    if (cacheIndex > -1) {\n      const entry = entries[cacheIndex];\n      if (cacheIndex > 0) {\n        entries.splice(cacheIndex, 1);\n        entries.unshift(entry);\n      }\n      return entry.value;\n    }\n    return NOT_FOUND;\n  }\n  function put(key, value) {\n    if (get(key) === NOT_FOUND) {\n      entries.unshift({\n        key,\n        value\n      });\n      if (entries.length > maxSize) {\n        entries.pop();\n      }\n    }\n  }\n  function getEntries() {\n    return entries;\n  }\n  function clear() {\n    entries = [];\n  }\n  return {\n    get,\n    put,\n    getEntries,\n    clear\n  };\n}\nvar referenceEqualityCheck = (a, b) => a === b;\nfunction createCacheKeyComparator(equalityCheck) {\n  return function areArgumentsShallowlyEqual(prev, next) {\n    if (prev === null || next === null || prev.length !== next.length) {\n      return false;\n    }\n    const {\n      length\n    } = prev;\n    for (let i = 0; i < length; i++) {\n      if (!equalityCheck(prev[i], next[i])) {\n        return false;\n      }\n    }\n    return true;\n  };\n}\nfunction lruMemoize(func, equalityCheckOrOptions) {\n  const providedOptions = typeof equalityCheckOrOptions === \"object\" ? equalityCheckOrOptions : {\n    equalityCheck: equalityCheckOrOptions\n  };\n  const {\n    equalityCheck = referenceEqualityCheck,\n    maxSize = 1,\n    resultEqualityCheck\n  } = providedOptions;\n  const comparator = createCacheKeyComparator(equalityCheck);\n  let resultsCount = 0;\n  const cache = maxSize <= 1 ? createSingletonCache(comparator) : createLruCache(maxSize, comparator);\n  function memoized() {\n    let value = cache.get(arguments);\n    if (value === NOT_FOUND) {\n      value = func.apply(null, arguments);\n      resultsCount++;\n      if (resultEqualityCheck) {\n        const entries = cache.getEntries();\n        const matchingEntry = entries.find(entry => resultEqualityCheck(entry.value, value));\n        if (matchingEntry) {\n          value = matchingEntry.value;\n          resultsCount !== 0 && resultsCount--;\n        }\n      }\n      cache.put(arguments, value);\n    }\n    return value;\n  }\n  memoized.clearCache = () => {\n    cache.clear();\n    memoized.resetResultsCount();\n  };\n  memoized.resultsCount = () => resultsCount;\n  memoized.resetResultsCount = () => {\n    resultsCount = 0;\n  };\n  return memoized;\n}\n\n// src/autotrackMemoize/autotrackMemoize.ts\nfunction autotrackMemoize(func) {\n  const node = createNode([]);\n  let lastArgs = null;\n  const shallowEqual = createCacheKeyComparator(referenceEqualityCheck);\n  const cache = createCache(() => {\n    const res = func.apply(null, node.proxy);\n    return res;\n  });\n  function memoized() {\n    if (!shallowEqual(lastArgs, arguments)) {\n      updateNode(node, arguments);\n      lastArgs = arguments;\n    }\n    return cache.value;\n  }\n  memoized.clearCache = () => {\n    return cache.clear();\n  };\n  return memoized;\n}\n\n// src/weakMapMemoize.ts\nvar StrongRef = class {\n  constructor(value) {\n    this.value = value;\n  }\n  deref() {\n    return this.value;\n  }\n};\nvar Ref = typeof WeakRef !== \"undefined\" ? WeakRef : StrongRef;\nvar UNTERMINATED = 0;\nvar TERMINATED = 1;\nfunction createCacheNode() {\n  return {\n    s: UNTERMINATED,\n    v: void 0,\n    o: null,\n    p: null\n  };\n}\nfunction weakMapMemoize(func, options = {}) {\n  let fnNode = createCacheNode();\n  const {\n    resultEqualityCheck\n  } = options;\n  let lastResult;\n  let resultsCount = 0;\n  function memoized() {\n    let cacheNode = fnNode;\n    const {\n      length\n    } = arguments;\n    for (let i = 0, l = length; i < l; i++) {\n      const arg = arguments[i];\n      if (typeof arg === \"function\" || typeof arg === \"object\" && arg !== null) {\n        let objectCache = cacheNode.o;\n        if (objectCache === null) {\n          cacheNode.o = objectCache = /* @__PURE__ */new WeakMap();\n        }\n        const objectNode = objectCache.get(arg);\n        if (objectNode === void 0) {\n          cacheNode = createCacheNode();\n          objectCache.set(arg, cacheNode);\n        } else {\n          cacheNode = objectNode;\n        }\n      } else {\n        let primitiveCache = cacheNode.p;\n        if (primitiveCache === null) {\n          cacheNode.p = primitiveCache = /* @__PURE__ */new Map();\n        }\n        const primitiveNode = primitiveCache.get(arg);\n        if (primitiveNode === void 0) {\n          cacheNode = createCacheNode();\n          primitiveCache.set(arg, cacheNode);\n        } else {\n          cacheNode = primitiveNode;\n        }\n      }\n    }\n    const terminatedNode = cacheNode;\n    let result;\n    if (cacheNode.s === TERMINATED) {\n      result = cacheNode.v;\n    } else {\n      result = func.apply(null, arguments);\n      resultsCount++;\n      if (resultEqualityCheck) {\n        const lastResultValue = lastResult?.deref?.() ?? lastResult;\n        if (lastResultValue != null && resultEqualityCheck(lastResultValue, result)) {\n          result = lastResultValue;\n          resultsCount !== 0 && resultsCount--;\n        }\n        const needsWeakRef = typeof result === \"object\" && result !== null || typeof result === \"function\";\n        lastResult = needsWeakRef ? new Ref(result) : result;\n      }\n    }\n    terminatedNode.s = TERMINATED;\n    terminatedNode.v = result;\n    return result;\n  }\n  memoized.clearCache = () => {\n    fnNode = createCacheNode();\n    memoized.resetResultsCount();\n  };\n  memoized.resultsCount = () => resultsCount;\n  memoized.resetResultsCount = () => {\n    resultsCount = 0;\n  };\n  return memoized;\n}\n\n// src/createSelectorCreator.ts\nfunction createSelectorCreator(memoizeOrOptions, ...memoizeOptionsFromArgs) {\n  const createSelectorCreatorOptions = typeof memoizeOrOptions === \"function\" ? {\n    memoize: memoizeOrOptions,\n    memoizeOptions: memoizeOptionsFromArgs\n  } : memoizeOrOptions;\n  const createSelector2 = (...createSelectorArgs) => {\n    let recomputations = 0;\n    let dependencyRecomputations = 0;\n    let lastResult;\n    let directlyPassedOptions = {};\n    let resultFunc = createSelectorArgs.pop();\n    if (typeof resultFunc === \"object\") {\n      directlyPassedOptions = resultFunc;\n      resultFunc = createSelectorArgs.pop();\n    }\n    assertIsFunction(resultFunc, `createSelector expects an output function after the inputs, but received: [${typeof resultFunc}]`);\n    const combinedOptions = {\n      ...createSelectorCreatorOptions,\n      ...directlyPassedOptions\n    };\n    const {\n      memoize,\n      memoizeOptions = [],\n      argsMemoize = weakMapMemoize,\n      argsMemoizeOptions = [],\n      devModeChecks = {}\n    } = combinedOptions;\n    const finalMemoizeOptions = ensureIsArray(memoizeOptions);\n    const finalArgsMemoizeOptions = ensureIsArray(argsMemoizeOptions);\n    const dependencies = getDependencies(createSelectorArgs);\n    const memoizedResultFunc = memoize(function recomputationWrapper() {\n      recomputations++;\n      return resultFunc.apply(null, arguments);\n    }, ...finalMemoizeOptions);\n    let firstRun = true;\n    const selector = argsMemoize(function dependenciesChecker() {\n      dependencyRecomputations++;\n      const inputSelectorResults = collectInputSelectorResults(dependencies, arguments);\n      lastResult = memoizedResultFunc.apply(null, inputSelectorResults);\n      if (process.env.NODE_ENV !== \"production\") {\n        const {\n          identityFunctionCheck,\n          inputStabilityCheck\n        } = getDevModeChecksExecutionInfo(firstRun, devModeChecks);\n        if (identityFunctionCheck.shouldRun) {\n          identityFunctionCheck.run(resultFunc, inputSelectorResults, lastResult);\n        }\n        if (inputStabilityCheck.shouldRun) {\n          const inputSelectorResultsCopy = collectInputSelectorResults(dependencies, arguments);\n          inputStabilityCheck.run({\n            inputSelectorResults,\n            inputSelectorResultsCopy\n          }, {\n            memoize,\n            memoizeOptions: finalMemoizeOptions\n          }, arguments);\n        }\n        if (firstRun) firstRun = false;\n      }\n      return lastResult;\n    }, ...finalArgsMemoizeOptions);\n    return Object.assign(selector, {\n      resultFunc,\n      memoizedResultFunc,\n      dependencies,\n      dependencyRecomputations: () => dependencyRecomputations,\n      resetDependencyRecomputations: () => {\n        dependencyRecomputations = 0;\n      },\n      lastResult: () => lastResult,\n      recomputations: () => recomputations,\n      resetRecomputations: () => {\n        recomputations = 0;\n      },\n      memoize,\n      argsMemoize\n    });\n  };\n  Object.assign(createSelector2, {\n    withTypes: () => createSelector2\n  });\n  return createSelector2;\n}\nvar createSelector = /* @__PURE__ */createSelectorCreator(weakMapMemoize);\n\n// src/createStructuredSelector.ts\nvar createStructuredSelector = Object.assign((inputSelectorsObject, selectorCreator = createSelector) => {\n  assertIsObject(inputSelectorsObject, `createStructuredSelector expects first argument to be an object where each property is a selector, instead received a ${typeof inputSelectorsObject}`);\n  const inputSelectorKeys = Object.keys(inputSelectorsObject);\n  const dependencies = inputSelectorKeys.map(key => inputSelectorsObject[key]);\n  const structuredSelector = selectorCreator(dependencies, (...inputSelectorResults) => {\n    return inputSelectorResults.reduce((composition, value, index) => {\n      composition[inputSelectorKeys[index]] = value;\n      return composition;\n    }, {});\n  });\n  return structuredSelector;\n}, {\n  withTypes: () => createStructuredSelector\n});\nexport { createSelector, createSelectorCreator, createStructuredSelector, lruMemoize, referenceEqualityCheck, setGlobalDevModeChecks, autotrackMemoize as unstable_autotrackMemoize, weakMapMemoize };", "map": {"version": 3, "names": ["runIdentityFunctionCheck", "resultFunc", "inputSelectorsResults", "outputSelectorResult", "length", "isInputSameAsOutput", "emptyObject", "stack", "Error", "e", "console", "warn", "runInputStabilityCheck", "inputSelectorResultsObject", "options", "inputSelectorArgs", "memoize", "memoizeOptions", "inputSelectorResults", "inputSelectorResultsCopy", "createAnEmptyObject", "areInputSelectorResultsEqual", "apply", "arguments", "firstInputs", "secondInputs", "globalDevModeChecks", "inputStabilityCheck", "identityFunctionCheck", "setGlobalDevModeChecks", "dev<PERSON>ode<PERSON><PERSON><PERSON>", "Object", "assign", "NOT_FOUND", "Symbol", "assertIsFunction", "func", "errorMessage", "TypeError", "assertIsObject", "object", "assertIsArrayOfFunctions", "array", "every", "item", "itemTypes", "map", "name", "join", "ensureIsArray", "Array", "isArray", "getDependencies", "createSelectorArgs", "dependencies", "collectInputSelectorResults", "i", "push", "getDevModeChecksExecutionInfo", "firstRun", "shouldRun", "run", "$REVISION", "CURRENT_TRACKER", "Cell", "revision", "_value", "_lastValue", "_isEqual", "tripleEq", "constructor", "initialValue", "isEqual", "value", "add", "newValue", "a", "b", "TrackingCache", "_cachedValue", "_cachedRevision", "_deps", "hits", "fn", "clear", "currentTracker", "Set", "prevTracker", "from", "Math", "max", "d", "getValue", "cell", "setValue", "storage", "createCell", "createCache", "neverEq", "createTag", "dirtyTag", "tag", "consumeCollection", "node", "collectionTag", "dirtyCollection", "REDUX_PROXY_LABEL", "nextId", "proto", "getPrototypeOf", "ObjectTreeNode", "proxy", "Proxy", "objectProxyHandler", "tags", "children", "id", "get", "key", "calculateResult", "childValue", "Reflect", "childNode", "createNode", "res", "ownKeys", "getOwnPropertyDescriptor", "prop", "has", "ArrayTreeNode", "arrayProxyHandler", "updateNode", "oldKeysSize", "newKeysSize", "anyKeysAdded", "_key", "isDifferent", "newChildV<PERSON>ue", "deleteNode", "createSingletonCache", "equals", "entry", "put", "getEntries", "createLruCache", "maxSize", "entries", "cacheIndex", "findIndex", "splice", "unshift", "pop", "referenceEqualityCheck", "createCacheKeyComparator", "equalityCheck", "areArgumentsShallowlyEqual", "prev", "next", "lruMemoize", "equalityCheckOrOptions", "providedOptions", "resultEqualityCheck", "comparator", "resultsCount", "cache", "memoized", "matchingEntry", "find", "clearCache", "resetResultsCount", "autotrackMemoize", "lastArgs", "shallowEqual", "StrongRef", "deref", "Ref", "WeakRef", "UNTERMINATED", "TERMINATED", "createCacheNode", "s", "v", "o", "p", "weakMapMemoize", "fnNode", "lastResult", "cacheNode", "l", "arg", "objectCache", "WeakMap", "objectNode", "set", "primitiveCache", "Map", "primitiveNode", "terminatedNode", "result", "lastResultValue", "needsWeakRef", "createSelectorCreator", "memoizeOrOptions", "memoizeOptionsFromArgs", "createSelectorCreatorOptions", "createSelector2", "createSelector", "recomputations", "dependencyRecomputations", "directlyPassedOptions", "combinedOptions", "argsMemoize", "argsMemoizeOptions", "finalMemoizeOptions", "finalArgsMemoizeOptions", "memoizedResultFunc", "recomputationWrapper", "selector", "dependenciesChecker", "process", "env", "NODE_ENV", "resetDependencyRecomputations", "resetRecomputations", "withTypes", "createStructuredSelector", "inputSelectorsObject", "selectorCreator", "inputSelectorKeys", "keys", "structuredSelector", "reduce", "composition", "index"], "sources": ["C:\\Users\\<USER>\\Documents\\Apps_Creations\\SiahMan_Racing4Speed_WebGame\\siah-man-racing\\node_modules\\reselect\\src\\devModeChecks\\identityFunctionCheck.ts", "C:\\Users\\<USER>\\Documents\\Apps_Creations\\SiahMan_Racing4Speed_WebGame\\siah-man-racing\\node_modules\\reselect\\src\\devModeChecks\\inputStabilityCheck.ts", "C:\\Users\\<USER>\\Documents\\Apps_Creations\\SiahMan_Racing4Speed_WebGame\\siah-man-racing\\node_modules\\reselect\\src\\devModeChecks\\setGlobalDevModeChecks.ts", "C:\\Users\\<USER>\\Documents\\Apps_Creations\\SiahMan_Racing4Speed_WebGame\\siah-man-racing\\node_modules\\reselect\\src\\utils.ts", "C:\\Users\\<USER>\\Documents\\Apps_Creations\\SiahMan_Racing4Speed_WebGame\\siah-man-racing\\node_modules\\reselect\\src\\autotrackMemoize\\autotracking.ts", "C:\\Users\\<USER>\\Documents\\Apps_Creations\\SiahMan_Racing4Speed_WebGame\\siah-man-racing\\node_modules\\reselect\\src\\autotrackMemoize\\tracking.ts", "C:\\Users\\<USER>\\Documents\\Apps_Creations\\SiahMan_Racing4Speed_WebGame\\siah-man-racing\\node_modules\\reselect\\src\\autotrackMemoize\\proxy.ts", "C:\\Users\\<USER>\\Documents\\Apps_Creations\\SiahMan_Racing4Speed_WebGame\\siah-man-racing\\node_modules\\reselect\\src\\lruMemoize.ts", "C:\\Users\\<USER>\\Documents\\Apps_Creations\\SiahMan_Racing4Speed_WebGame\\siah-man-racing\\node_modules\\reselect\\src\\autotrackMemoize\\autotrackMemoize.ts", "C:\\Users\\<USER>\\Documents\\Apps_Creations\\SiahMan_Racing4Speed_WebGame\\siah-man-racing\\node_modules\\reselect\\src\\weakMapMemoize.ts", "C:\\Users\\<USER>\\Documents\\Apps_Creations\\SiahMan_Racing4Speed_WebGame\\siah-man-racing\\node_modules\\reselect\\src\\createSelectorCreator.ts", "C:\\Users\\<USER>\\Documents\\Apps_Creations\\SiahMan_Racing4Speed_WebGame\\siah-man-racing\\node_modules\\reselect\\src\\createStructuredSelector.ts"], "sourcesContent": ["import type { AnyFunction } from '../types'\r\n\r\n/**\r\n * Runs a check to determine if the given result function behaves as an\r\n * identity function. An identity function is one that returns its\r\n * input unchanged, for example, `x => x`. This check helps ensure\r\n * efficient memoization and prevent unnecessary re-renders by encouraging\r\n * proper use of transformation logic in result functions and\r\n * extraction logic in input selectors.\r\n *\r\n * @param resultFunc - The result function to be checked.\r\n * @param inputSelectorsResults - The results of the input selectors.\r\n * @param outputSelectorResult - The result of the output selector.\r\n *\r\n * @see {@link https://reselect.js.org/api/development-only-stability-checks#identityfunctioncheck `identityFunctionCheck`}\r\n *\r\n * @since 5.0.0\r\n * @internal\r\n */\r\nexport const runIdentityFunctionCheck = (\r\n  resultFunc: AnyFunction,\r\n  inputSelectorsResults: unknown[],\r\n  outputSelectorResult: unknown\r\n) => {\r\n  if (\r\n    inputSelectorsResults.length === 1 &&\r\n    inputSelectorsResults[0] === outputSelectorResult\r\n  ) {\r\n    let isInputSameAsOutput = false\r\n    try {\r\n      const emptyObject = {}\r\n      if (resultFunc(emptyObject) === emptyObject) isInputSameAsOutput = true\r\n    } catch {\r\n      // Do nothing\r\n    }\r\n    if (isInputSameAsOutput) {\r\n      let stack: string | undefined = undefined\r\n      try {\r\n        throw new Error()\r\n      } catch (e) {\r\n        // eslint-disable-next-line @typescript-eslint/no-extra-semi, no-extra-semi\r\n        ;({ stack } = e as Error)\r\n      }\r\n      console.warn(\r\n        'The result function returned its own inputs without modification. e.g' +\r\n          '\\n`createSelector([state => state.todos], todos => todos)`' +\r\n          '\\nThis could lead to inefficient memoization and unnecessary re-renders.' +\r\n          '\\nEnsure transformation logic is in the result function, and extraction logic is in the input selectors.',\r\n        { stack }\r\n      )\r\n    }\r\n  }\r\n}\r\n", "import type { CreateSelectorOptions, UnknownMemoizer } from '../types'\r\n\r\n/**\r\n * Runs a stability check to ensure the input selector results remain stable\r\n * when provided with the same arguments. This function is designed to detect\r\n * changes in the output of input selectors, which can impact the performance of memoized selectors.\r\n *\r\n * @param inputSelectorResultsObject - An object containing two arrays: `inputSelectorResults` and `inputSelectorResultsCopy`, representing the results of input selectors.\r\n * @param options - Options object consisting of a `memoize` function and a `memoizeOptions` object.\r\n * @param inputSelectorArgs - List of arguments being passed to the input selectors.\r\n *\r\n * @see {@link https://reselect.js.org/api/development-only-stability-checks/#inputstabilitycheck `inputStabilityCheck`}\r\n *\r\n * @since 5.0.0\r\n * @internal\r\n */\r\nexport const runInputStabilityCheck = (\r\n  inputSelectorResultsObject: {\r\n    inputSelectorResults: unknown[]\r\n    inputSelectorResultsCopy: unknown[]\r\n  },\r\n  options: Required<\r\n    Pick<\r\n      CreateSelectorOptions<UnknownMemoizer, UnknownMemoizer>,\r\n      'memoize' | 'memoizeOptions'\r\n    >\r\n  >,\r\n  inputSelectorArgs: unknown[] | IArguments\r\n) => {\r\n  const { memoize, memoizeOptions } = options\r\n  const { inputSelectorResults, inputSelectorResultsCopy } =\r\n    inputSelectorResultsObject\r\n  const createAnEmptyObject = memoize(() => ({}), ...memoizeOptions)\r\n  // if the memoize method thinks the parameters are equal, these *should* be the same reference\r\n  const areInputSelectorResultsEqual =\r\n    createAnEmptyObject.apply(null, inputSelectorResults) ===\r\n    createAnEmptyObject.apply(null, inputSelectorResultsCopy)\r\n  if (!areInputSelectorResultsEqual) {\r\n    let stack: string | undefined = undefined\r\n    try {\r\n      throw new Error()\r\n    } catch (e) {\r\n      // eslint-disable-next-line @typescript-eslint/no-extra-semi, no-extra-semi\r\n      ;({ stack } = e as Error)\r\n    }\r\n    console.warn(\r\n      'An input selector returned a different result when passed same arguments.' +\r\n        '\\nThis means your output selector will likely run more frequently than intended.' +\r\n        '\\nAvoid returning a new reference inside your input selector, e.g.' +\r\n        '\\n`createSelector([state => state.todos.map(todo => todo.id)], todoIds => todoIds.length)`',\r\n      {\r\n        arguments: inputSelectorArgs,\r\n        firstInputs: inputSelectorResults,\r\n        secondInputs: inputSelectorResultsCopy,\r\n        stack\r\n      }\r\n    )\r\n  }\r\n}\r\n", "import type { DevModeChe<PERSON> } from '../types'\r\n\r\n/**\r\n * Global configuration for development mode checks. This specifies the default\r\n * frequency at which each development mode check should be performed.\r\n *\r\n * @since 5.0.0\r\n * @internal\r\n */\r\nexport const globalDevModeChecks: DevModeChecks = {\r\n  inputStabilityCheck: 'once',\r\n  identityFunctionCheck: 'once'\r\n}\r\n\r\n/**\r\n * Overrides the development mode checks settings for all selectors.\r\n *\r\n * Reselect performs additional checks in development mode to help identify and\r\n * warn about potential issues in selector behavior. This function allows you to\r\n * customize the behavior of these checks across all selectors in your application.\r\n *\r\n * **Note**: This setting can still be overridden per selector inside `createSelector`'s `options` object.\r\n * See {@link https://github.com/reduxjs/reselect#2-per-selector-by-passing-an-identityfunctioncheck-option-directly-to-createselector per-selector-configuration}\r\n * and {@linkcode CreateSelectorOptions.identityFunctionCheck identityFunctionCheck} for more details.\r\n *\r\n * _The development mode checks do not run in production builds._\r\n *\r\n * @param devModeChecks - An object specifying the desired settings for development mode checks. You can provide partial overrides. Unspecified settings will retain their current values.\r\n *\r\n * @example\r\n * ```ts\r\n * import { setGlobalDevModeChecks } from 'reselect'\r\n * import { DevModeChecks } from '../types'\r\n *\r\n * // Run only the first time the selector is called. (default)\r\n * setGlobalDevModeChecks({ inputStabilityCheck: 'once' })\r\n *\r\n * // Run every time the selector is called.\r\n * setGlobalDevModeChecks({ inputStabilityCheck: 'always' })\r\n *\r\n * // Never run the input stability check.\r\n * setGlobalDevModeChecks({ inputStabilityCheck: 'never' })\r\n *\r\n * // Run only the first time the selector is called. (default)\r\n * setGlobalDevModeChecks({ identityFunctionCheck: 'once' })\r\n *\r\n * // Run every time the selector is called.\r\n * setGlobalDevModeChecks({ identityFunctionCheck: 'always' })\r\n *\r\n * // Never run the identity function check.\r\n * setGlobalDevModeChecks({ identityFunctionCheck: 'never' })\r\n * ```\r\n * @see {@link https://reselect.js.org/api/development-only-stability-checks Development-Only Stability Checks}\r\n * @see {@link https://reselect.js.org/api/development-only-stability-checks#1-globally-through-setglobaldevmodechecks global-configuration}\r\n *\r\n * @since 5.0.0\r\n * @public\r\n */\r\nexport const setGlobalDevModeChecks = (\r\n  devModeChecks: Partial<DevModeChecks>\r\n) => {\r\n  Object.assign(globalDevModeChecks, devModeChecks)\r\n}\r\n", "import { runIdentityFunctionCheck } from './devModeChecks/identityFunctionCheck'\r\nimport { runInputStabilityCheck } from './devModeChecks/inputStabilityCheck'\r\nimport { globalDevModeChecks } from './devModeChecks/setGlobalDevModeChecks'\r\n// eslint-disable-next-line @typescript-eslint/consistent-type-imports\r\nimport type {\r\n  <PERSON>Mode<PERSON>hecks,\r\n  Selector,\r\n  SelectorArray,\r\n  DevModeChecksExecutionInfo\r\n} from './types'\r\n\r\nexport const NOT_FOUND = /* @__PURE__ */ Symbol('NOT_FOUND')\r\nexport type NOT_FOUND_TYPE = typeof NOT_FOUND\r\n\r\n/**\r\n * Assert that the provided value is a function. If the assertion fails,\r\n * a `TypeError` is thrown with an optional custom error message.\r\n *\r\n * @param func - The value to be checked.\r\n * @param  errorMessage - An optional custom error message to use if the assertion fails.\r\n * @throws A `TypeError` if the assertion fails.\r\n */\r\nexport function assertIsFunction<FunctionType extends Function>(\r\n  func: unknown,\r\n  errorMessage = `expected a function, instead received ${typeof func}`\r\n): asserts func is FunctionType {\r\n  if (typeof func !== 'function') {\r\n    throw new TypeError(errorMessage)\r\n  }\r\n}\r\n\r\n/**\r\n * Assert that the provided value is an object. If the assertion fails,\r\n * a `TypeError` is thrown with an optional custom error message.\r\n *\r\n * @param object - The value to be checked.\r\n * @param  errorMessage - An optional custom error message to use if the assertion fails.\r\n * @throws A `TypeError` if the assertion fails.\r\n */\r\nexport function assertIsObject<ObjectType extends Record<string, unknown>>(\r\n  object: unknown,\r\n  errorMessage = `expected an object, instead received ${typeof object}`\r\n): asserts object is ObjectType {\r\n  if (typeof object !== 'object') {\r\n    throw new TypeError(errorMessage)\r\n  }\r\n}\r\n\r\n/**\r\n * Assert that the provided array is an array of functions. If the assertion fails,\r\n * a `TypeError` is thrown with an optional custom error message.\r\n *\r\n * @param array - The array to be checked.\r\n * @param  errorMessage - An optional custom error message to use if the assertion fails.\r\n * @throws A `TypeError` if the assertion fails.\r\n */\r\nexport function assertIsArrayOfFunctions<FunctionType extends Function>(\r\n  array: unknown[],\r\n  errorMessage = `expected all items to be functions, instead received the following types: `\r\n): asserts array is FunctionType[] {\r\n  if (\r\n    !array.every((item): item is FunctionType => typeof item === 'function')\r\n  ) {\r\n    const itemTypes = array\r\n      .map(item =>\r\n        typeof item === 'function'\r\n          ? `function ${item.name || 'unnamed'}()`\r\n          : typeof item\r\n      )\r\n      .join(', ')\r\n    throw new TypeError(`${errorMessage}[${itemTypes}]`)\r\n  }\r\n}\r\n\r\n/**\r\n * Ensure that the input is an array. If it's already an array, it's returned as is.\r\n * If it's not an array, it will be wrapped in a new array.\r\n *\r\n * @param item - The item to be checked.\r\n * @returns An array containing the input item. If the input is already an array, it's returned without modification.\r\n */\r\nexport const ensureIsArray = (item: unknown) => {\r\n  return Array.isArray(item) ? item : [item]\r\n}\r\n\r\n/**\r\n * Extracts the \"dependencies\" / \"input selectors\" from the arguments of `createSelector`.\r\n *\r\n * @param createSelectorArgs - Arguments passed to `createSelector` as an array.\r\n * @returns An array of \"input selectors\" / \"dependencies\".\r\n * @throws A `TypeError` if any of the input selectors is not function.\r\n */\r\nexport function getDependencies(createSelectorArgs: unknown[]) {\r\n  const dependencies = Array.isArray(createSelectorArgs[0])\r\n    ? createSelectorArgs[0]\r\n    : createSelectorArgs\r\n\r\n  assertIsArrayOfFunctions<Selector>(\r\n    dependencies,\r\n    `createSelector expects all input-selectors to be functions, but received the following types: `\r\n  )\r\n\r\n  return dependencies as SelectorArray\r\n}\r\n\r\n/**\r\n * Runs each input selector and returns their collective results as an array.\r\n *\r\n * @param dependencies - An array of \"dependencies\" or \"input selectors\".\r\n * @param inputSelectorArgs - An array of arguments being passed to the input selectors.\r\n * @returns An array of input selector results.\r\n */\r\nexport function collectInputSelectorResults(\r\n  dependencies: SelectorArray,\r\n  inputSelectorArgs: unknown[] | IArguments\r\n) {\r\n  const inputSelectorResults = []\r\n  const { length } = dependencies\r\n  for (let i = 0; i < length; i++) {\r\n    // @ts-ignore\r\n    // apply arguments instead of spreading and mutate a local list of params for performance.\r\n    inputSelectorResults.push(dependencies[i].apply(null, inputSelectorArgs))\r\n  }\r\n  return inputSelectorResults\r\n}\r\n\r\n/**\r\n * Retrieves execution information for development mode checks.\r\n *\r\n * @param devModeChecks - Custom Settings for development mode checks. These settings will override the global defaults.\r\n * @param firstRun - Indicates whether it is the first time the selector has run.\r\n * @returns  An object containing the execution information for each development mode check.\r\n */\r\nexport const getDevModeChecksExecutionInfo = (\r\n  firstRun: boolean,\r\n  devModeChecks: Partial<DevModeChecks>\r\n) => {\r\n  const { identityFunctionCheck, inputStabilityCheck } = {\r\n    ...globalDevModeChecks,\r\n    ...devModeChecks\r\n  }\r\n  return {\r\n    identityFunctionCheck: {\r\n      shouldRun:\r\n        identityFunctionCheck === 'always' ||\r\n        (identityFunctionCheck === 'once' && firstRun),\r\n      run: runIdentityFunctionCheck\r\n    },\r\n    inputStabilityCheck: {\r\n      shouldRun:\r\n        inputStabilityCheck === 'always' ||\r\n        (inputStabilityCheck === 'once' && firstRun),\r\n      run: runInputStabilityCheck\r\n    }\r\n  } satisfies DevModeChecksExecutionInfo\r\n}\r\n", "// Original autotracking implementation source:\r\n// - https://gist.github.com/pzuraq/79bf862e0f8cd9521b79c4b6eccdc4f9\r\n// Additional references:\r\n// - https://www.pzuraq.com/blog/how-autotracking-works\r\n// - https://v5.chriskrycho.com/journal/autotracking-elegant-dx-via-cutting-edge-cs/\r\nimport type { EqualityFn } from '../types'\r\nimport { assertIsFunction } from '../utils'\r\n\r\n// The global revision clock. Every time state changes, the clock increments.\r\nexport let $REVISION = 0\r\n\r\n// The current dependency tracker. Whenever we compute a cache, we create a Set\r\n// to track any dependencies that are used while computing. If no cache is\r\n// computing, then the tracker is null.\r\nlet CURRENT_TRACKER: Set<Cell<any> | TrackingCache> | null = null\r\n\r\n// Storage represents a root value in the system - the actual state of our app.\r\nexport class Cell<T> {\r\n  revision = $REVISION\r\n\r\n  _value: T\r\n  _lastValue: T\r\n  _isEqual: EqualityFn = tripleEq\r\n\r\n  constructor(initialValue: T, isEqual: EqualityFn = tripleEq) {\r\n    this._value = this._lastValue = initialValue\r\n    this._isEqual = isEqual\r\n  }\r\n\r\n  // Whenever a storage value is read, it'll add itself to the current tracker if\r\n  // one exists, entangling its state with that cache.\r\n  get value() {\r\n    CURRENT_TRACKER?.add(this)\r\n\r\n    return this._value\r\n  }\r\n\r\n  // Whenever a storage value is updated, we bump the global revision clock,\r\n  // assign the revision for this storage to the new value, _and_ we schedule a\r\n  // rerender. This is important, and it's what makes autotracking  _pull_\r\n  // based. We don't actively tell the caches which depend on the storage that\r\n  // anything has happened. Instead, we recompute the caches when needed.\r\n  set value(newValue) {\r\n    if (this.value === newValue) return\r\n\r\n    this._value = newValue\r\n    this.revision = ++$REVISION\r\n  }\r\n}\r\n\r\nfunction tripleEq(a: unknown, b: unknown) {\r\n  return a === b\r\n}\r\n\r\n// Caches represent derived state in the system. They are ultimately functions\r\n// that are memoized based on what state they use to produce their output,\r\n// meaning they will only rerun IFF a storage value that could affect the output\r\n// has changed. Otherwise, they'll return the cached value.\r\nexport class TrackingCache {\r\n  _cachedValue: any\r\n  _cachedRevision = -1\r\n  _deps: any[] = []\r\n  hits = 0\r\n\r\n  fn: () => any\r\n\r\n  constructor(fn: () => any) {\r\n    this.fn = fn\r\n  }\r\n\r\n  clear() {\r\n    this._cachedValue = undefined\r\n    this._cachedRevision = -1\r\n    this._deps = []\r\n    this.hits = 0\r\n  }\r\n\r\n  get value() {\r\n    // When getting the value for a Cache, first we check all the dependencies of\r\n    // the cache to see what their current revision is. If the current revision is\r\n    // greater than the cached revision, then something has changed.\r\n    if (this.revision > this._cachedRevision) {\r\n      const { fn } = this\r\n\r\n      // We create a new dependency tracker for this cache. As the cache runs\r\n      // its function, any Storage or Cache instances which are used while\r\n      // computing will be added to this tracker. In the end, it will be the\r\n      // full list of dependencies that this Cache depends on.\r\n      const currentTracker = new Set<Cell<any>>()\r\n      const prevTracker = CURRENT_TRACKER\r\n\r\n      CURRENT_TRACKER = currentTracker\r\n\r\n      // try {\r\n      this._cachedValue = fn()\r\n      // } finally {\r\n      CURRENT_TRACKER = prevTracker\r\n      this.hits++\r\n      this._deps = Array.from(currentTracker)\r\n\r\n      // Set the cached revision. This is the current clock count of all the\r\n      // dependencies. If any dependency changes, this number will be less\r\n      // than the new revision.\r\n      this._cachedRevision = this.revision\r\n      // }\r\n    }\r\n\r\n    // If there is a current tracker, it means another Cache is computing and\r\n    // using this one, so we add this one to the tracker.\r\n    CURRENT_TRACKER?.add(this)\r\n\r\n    // Always return the cached value.\r\n    return this._cachedValue\r\n  }\r\n\r\n  get revision() {\r\n    // The current revision is the max of all the dependencies' revisions.\r\n    return Math.max(...this._deps.map(d => d.revision), 0)\r\n  }\r\n}\r\n\r\nexport function getValue<T>(cell: Cell<T>): T {\r\n  if (!(cell instanceof Cell)) {\r\n    console.warn('Not a valid cell! ', cell)\r\n  }\r\n\r\n  return cell.value\r\n}\r\n\r\ntype CellValue<T extends Cell<unknown>> = T extends Cell<infer U> ? U : never\r\n\r\nexport function setValue<T extends Cell<unknown>>(\r\n  storage: T,\r\n  value: CellValue<T>\r\n): void {\r\n  if (!(storage instanceof Cell)) {\r\n    throw new TypeError(\r\n      'setValue must be passed a tracked store created with `createStorage`.'\r\n    )\r\n  }\r\n\r\n  storage.value = storage._lastValue = value\r\n}\r\n\r\nexport function createCell<T = unknown>(\r\n  initialValue: T,\r\n  isEqual: EqualityFn = tripleEq\r\n): Cell<T> {\r\n  return new Cell(initialValue, isEqual)\r\n}\r\n\r\nexport function createCache<T = unknown>(fn: () => T): TrackingCache {\r\n  assertIsFunction(\r\n    fn,\r\n    'the first parameter to `createCache` must be a function'\r\n  )\r\n\r\n  return new TrackingCache(fn)\r\n}\r\n", "import type { Cell } from './autotracking'\r\nimport {\r\n  getValue as consumeTag,\r\n  createCell as createStorage,\r\n  setValue\r\n} from './autotracking'\r\n\r\nexport type Tag = Cell<unknown>\r\n\r\nconst neverEq = (a: any, b: any): boolean => false\r\n\r\nexport function createTag(): Tag {\r\n  return createStorage(null, neverEq)\r\n}\r\nexport { consumeTag }\r\nexport function dirtyTag(tag: Tag, value: any): void {\r\n  setValue(tag, value)\r\n}\r\n\r\nexport interface Node<\r\n  T extends Array<unknown> | Record<string, unknown> =\r\n    | Array<unknown>\r\n    | Record<string, unknown>\r\n> {\r\n  collectionTag: Tag | null\r\n  tag: Tag | null\r\n  tags: Record<string, Tag>\r\n  children: Record<string, Node>\r\n  proxy: T\r\n  value: T\r\n  id: number\r\n}\r\n\r\nexport const consumeCollection = (node: Node): void => {\r\n  let tag = node.collectionTag\r\n\r\n  if (tag === null) {\r\n    tag = node.collectionTag = createTag()\r\n  }\r\n\r\n  consumeTag(tag)\r\n}\r\n\r\nexport const dirtyCollection = (node: Node): void => {\r\n  const tag = node.collectionTag\r\n\r\n  if (tag !== null) {\r\n    dirtyTag(tag, null)\r\n  }\r\n}\r\n", "// Original source:\r\n// - https://github.com/simonihmig/tracked-redux/blob/master/packages/tracked-redux/src/-private/proxy.ts\r\n\r\nimport type { Node, Tag } from './tracking'\r\nimport {\r\n  consumeCollection,\r\n  consumeTag,\r\n  createTag,\r\n  dirtyCollection,\r\n  dirtyTag\r\n} from './tracking'\r\n\r\nexport const REDUX_PROXY_LABEL = Symbol()\r\n\r\nlet nextId = 0\r\n\r\nconst proto = Object.getPrototypeOf({})\r\n\r\nclass ObjectTreeNode<T extends Record<string, unknown>> implements Node<T> {\r\n  proxy: T = new Proxy(this, objectProxyHandler) as unknown as T\r\n  tag = createTag()\r\n  tags = {} as Record<string, Tag>\r\n  children = {} as Record<string, Node>\r\n  collectionTag = null\r\n  id = nextId++\r\n\r\n  constructor(public value: T) {\r\n    this.value = value\r\n    this.tag.value = value\r\n  }\r\n}\r\n\r\nconst objectProxyHandler = {\r\n  get(node: Node, key: string | symbol): unknown {\r\n    function calculateResult() {\r\n      const { value } = node\r\n\r\n      const childValue = Reflect.get(value, key)\r\n\r\n      if (typeof key === 'symbol') {\r\n        return childValue\r\n      }\r\n\r\n      if (key in proto) {\r\n        return childValue\r\n      }\r\n\r\n      if (typeof childValue === 'object' && childValue !== null) {\r\n        let childNode = node.children[key]\r\n\r\n        if (childNode === undefined) {\r\n          childNode = node.children[key] = createNode(childValue)\r\n        }\r\n\r\n        if (childNode.tag) {\r\n          consumeTag(childNode.tag)\r\n        }\r\n\r\n        return childNode.proxy\r\n      } else {\r\n        let tag = node.tags[key]\r\n\r\n        if (tag === undefined) {\r\n          tag = node.tags[key] = createTag()\r\n          tag.value = childValue\r\n        }\r\n\r\n        consumeTag(tag)\r\n\r\n        return childValue\r\n      }\r\n    }\r\n    const res = calculateResult()\r\n    return res\r\n  },\r\n\r\n  ownKeys(node: Node): ArrayLike<string | symbol> {\r\n    consumeCollection(node)\r\n    return Reflect.ownKeys(node.value)\r\n  },\r\n\r\n  getOwnPropertyDescriptor(\r\n    node: Node,\r\n    prop: string | symbol\r\n  ): PropertyDescriptor | undefined {\r\n    return Reflect.getOwnPropertyDescriptor(node.value, prop)\r\n  },\r\n\r\n  has(node: Node, prop: string | symbol): boolean {\r\n    return Reflect.has(node.value, prop)\r\n  }\r\n}\r\n\r\nclass ArrayTreeNode<T extends Array<unknown>> implements Node<T> {\r\n  proxy: T = new Proxy([this], arrayProxyHandler) as unknown as T\r\n  tag = createTag()\r\n  tags = {}\r\n  children = {}\r\n  collectionTag = null\r\n  id = nextId++\r\n\r\n  constructor(public value: T) {\r\n    this.value = value\r\n    this.tag.value = value\r\n  }\r\n}\r\n\r\nconst arrayProxyHandler = {\r\n  get([node]: [Node], key: string | symbol): unknown {\r\n    if (key === 'length') {\r\n      consumeCollection(node)\r\n    }\r\n\r\n    return objectProxyHandler.get(node, key)\r\n  },\r\n\r\n  ownKeys([node]: [Node]): ArrayLike<string | symbol> {\r\n    return objectProxyHandler.ownKeys(node)\r\n  },\r\n\r\n  getOwnPropertyDescriptor(\r\n    [node]: [Node],\r\n    prop: string | symbol\r\n  ): PropertyDescriptor | undefined {\r\n    return objectProxyHandler.getOwnPropertyDescriptor(node, prop)\r\n  },\r\n\r\n  has([node]: [Node], prop: string | symbol): boolean {\r\n    return objectProxyHandler.has(node, prop)\r\n  }\r\n}\r\n\r\nexport function createNode<T extends Array<unknown> | Record<string, unknown>>(\r\n  value: T\r\n): Node<T> {\r\n  if (Array.isArray(value)) {\r\n    return new ArrayTreeNode(value)\r\n  }\r\n\r\n  return new ObjectTreeNode(value) as Node<T>\r\n}\r\n\r\nconst keysMap = new WeakMap<\r\n  Array<unknown> | Record<string, unknown>,\r\n  Set<string>\r\n>()\r\n\r\nexport function updateNode<T extends Array<unknown> | Record<string, unknown>>(\r\n  node: Node<T>,\r\n  newValue: T\r\n): void {\r\n  const { value, tags, children } = node\r\n\r\n  node.value = newValue\r\n\r\n  if (\r\n    Array.isArray(value) &&\r\n    Array.isArray(newValue) &&\r\n    value.length !== newValue.length\r\n  ) {\r\n    dirtyCollection(node)\r\n  } else {\r\n    if (value !== newValue) {\r\n      let oldKeysSize = 0\r\n      let newKeysSize = 0\r\n      let anyKeysAdded = false\r\n\r\n      for (const _key in value) {\r\n        oldKeysSize++\r\n      }\r\n\r\n      for (const key in newValue) {\r\n        newKeysSize++\r\n        if (!(key in value)) {\r\n          anyKeysAdded = true\r\n          break\r\n        }\r\n      }\r\n\r\n      const isDifferent = anyKeysAdded || oldKeysSize !== newKeysSize\r\n\r\n      if (isDifferent) {\r\n        dirtyCollection(node)\r\n      }\r\n    }\r\n  }\r\n\r\n  for (const key in tags) {\r\n    const childValue = (value as Record<string, unknown>)[key]\r\n    const newChildValue = (newValue as Record<string, unknown>)[key]\r\n\r\n    if (childValue !== newChildValue) {\r\n      dirtyCollection(node)\r\n      dirtyTag(tags[key], newChildValue)\r\n    }\r\n\r\n    if (typeof newChildValue === 'object' && newChildValue !== null) {\r\n      delete tags[key]\r\n    }\r\n  }\r\n\r\n  for (const key in children) {\r\n    const childNode = children[key]\r\n    const newChildValue = (newValue as Record<string, unknown>)[key]\r\n\r\n    const childValue = childNode.value\r\n\r\n    if (childValue === newChildValue) {\r\n      continue\r\n    } else if (typeof newChildValue === 'object' && newChildValue !== null) {\r\n      updateNode(childNode, newChildValue as Record<string, unknown>)\r\n    } else {\r\n      deleteNode(childNode)\r\n      delete children[key]\r\n    }\r\n  }\r\n}\r\n\r\nfunction deleteNode(node: Node): void {\r\n  if (node.tag) {\r\n    dirtyTag(node.tag, null)\r\n  }\r\n  dirtyCollection(node)\r\n  for (const key in node.tags) {\r\n    dirtyTag(node.tags[key], null)\r\n  }\r\n  for (const key in node.children) {\r\n    deleteNode(node.children[key])\r\n  }\r\n}\r\n", "import type {\r\n  AnyFunction,\r\n  DefaultMemoize<PERSON><PERSON>s,\r\n  EqualityFn,\r\n  Simplify\r\n} from './types'\r\n\r\nimport type { NOT_FOUND_TYPE } from './utils'\r\nimport { NOT_FOUND } from './utils'\r\n\r\n// Cache implementation based on <PERSON>'s `lru-memoize`:\r\n// https://github.com/erikras/lru-memoize\r\n\r\ninterface Entry {\r\n  key: unknown\r\n  value: unknown\r\n}\r\n\r\ninterface Cache {\r\n  get(key: unknown): unknown | NOT_FOUND_TYPE\r\n  put(key: unknown, value: unknown): void\r\n  getEntries(): Entry[]\r\n  clear(): void\r\n}\r\n\r\nfunction createSingletonCache(equals: EqualityFn): Cache {\r\n  let entry: Entry | undefined\r\n  return {\r\n    get(key: unknown) {\r\n      if (entry && equals(entry.key, key)) {\r\n        return entry.value\r\n      }\r\n\r\n      return NOT_FOUND\r\n    },\r\n\r\n    put(key: unknown, value: unknown) {\r\n      entry = { key, value }\r\n    },\r\n\r\n    getEntries() {\r\n      return entry ? [entry] : []\r\n    },\r\n\r\n    clear() {\r\n      entry = undefined\r\n    }\r\n  }\r\n}\r\n\r\nfunction createLruCache(maxSize: number, equals: EqualityFn): Cache {\r\n  let entries: Entry[] = []\r\n\r\n  function get(key: unknown) {\r\n    const cacheIndex = entries.findIndex(entry => equals(key, entry.key))\r\n\r\n    // We found a cached entry\r\n    if (cacheIndex > -1) {\r\n      const entry = entries[cacheIndex]\r\n\r\n      // Cached entry not at top of cache, move it to the top\r\n      if (cacheIndex > 0) {\r\n        entries.splice(cacheIndex, 1)\r\n        entries.unshift(entry)\r\n      }\r\n\r\n      return entry.value\r\n    }\r\n\r\n    // No entry found in cache, return sentinel\r\n    return NOT_FOUND\r\n  }\r\n\r\n  function put(key: unknown, value: unknown) {\r\n    if (get(key) === NOT_FOUND) {\r\n      // TODO Is unshift slow?\r\n      entries.unshift({ key, value })\r\n      if (entries.length > maxSize) {\r\n        entries.pop()\r\n      }\r\n    }\r\n  }\r\n\r\n  function getEntries() {\r\n    return entries\r\n  }\r\n\r\n  function clear() {\r\n    entries = []\r\n  }\r\n\r\n  return { get, put, getEntries, clear }\r\n}\r\n\r\n/**\r\n * Runs a simple reference equality check.\r\n * What {@linkcode lruMemoize lruMemoize} uses by default.\r\n *\r\n * **Note**: This function was previously known as `defaultEqualityCheck`.\r\n *\r\n * @public\r\n */\r\nexport const referenceEqualityCheck: EqualityFn = (a, b) => a === b\r\n\r\nexport function createCacheKeyComparator(equalityCheck: EqualityFn) {\r\n  return function areArgumentsShallowlyEqual(\r\n    prev: unknown[] | IArguments | null,\r\n    next: unknown[] | IArguments | null\r\n  ): boolean {\r\n    if (prev === null || next === null || prev.length !== next.length) {\r\n      return false\r\n    }\r\n\r\n    // Do this in a for loop (and not a `forEach` or an `every`) so we can determine equality as fast as possible.\r\n    const { length } = prev\r\n    for (let i = 0; i < length; i++) {\r\n      if (!equalityCheck(prev[i], next[i])) {\r\n        return false\r\n      }\r\n    }\r\n\r\n    return true\r\n  }\r\n}\r\n\r\n/**\r\n * Options for configuring the behavior of a function memoized with\r\n * LRU (Least Recently Used) caching.\r\n *\r\n * @template Result - The type of the return value of the memoized function.\r\n *\r\n * @public\r\n */\r\nexport interface LruMemoizeOptions<Result = any> {\r\n  /**\r\n   * Function used to compare the individual arguments of the\r\n   * provided calculation function.\r\n   *\r\n   * @default referenceEqualityCheck\r\n   */\r\n  equalityCheck?: EqualityFn\r\n\r\n  /**\r\n   * If provided, used to compare a newly generated output value against\r\n   * previous values in the cache. If a match is found,\r\n   * the old value is returned. This addresses the common\r\n   * ```ts\r\n   * todos.map(todo => todo.id)\r\n   * ```\r\n   * use case, where an update to another field in the original data causes\r\n   * a recalculation due to changed references, but the output is still\r\n   * effectively the same.\r\n   *\r\n   * @since 4.1.0\r\n   */\r\n  resultEqualityCheck?: EqualityFn<Result>\r\n\r\n  /**\r\n   * The maximum size of the cache used by the selector.\r\n   * A size greater than 1 means the selector will use an\r\n   * LRU (Least Recently Used) cache, allowing for the caching of multiple\r\n   * results based on different sets of arguments.\r\n   *\r\n   * @default 1\r\n   */\r\n  maxSize?: number\r\n}\r\n\r\n/**\r\n * Creates a memoized version of a function with an optional\r\n * LRU (Least Recently Used) cache. The memoized function uses a cache to\r\n * store computed values. Depending on the `maxSize` option, it will use\r\n * either a singleton cache (for a single entry) or an\r\n * LRU cache (for multiple entries).\r\n *\r\n * **Note**: This function was previously known as `defaultMemoize`.\r\n *\r\n * @param func - The function to be memoized.\r\n * @param equalityCheckOrOptions - Either an equality check function or an options object.\r\n * @returns A memoized function with a `.clearCache()` method attached.\r\n *\r\n * @template Func - The type of the function that is memoized.\r\n *\r\n * @see {@link https://reselect.js.org/api/lruMemoize `lruMemoize`}\r\n *\r\n * @public\r\n */\r\nexport function lruMemoize<Func extends AnyFunction>(\r\n  func: Func,\r\n  equalityCheckOrOptions?: EqualityFn | LruMemoizeOptions<ReturnType<Func>>\r\n) {\r\n  const providedOptions =\r\n    typeof equalityCheckOrOptions === 'object'\r\n      ? equalityCheckOrOptions\r\n      : { equalityCheck: equalityCheckOrOptions }\r\n\r\n  const {\r\n    equalityCheck = referenceEqualityCheck,\r\n    maxSize = 1,\r\n    resultEqualityCheck\r\n  } = providedOptions\r\n\r\n  const comparator = createCacheKeyComparator(equalityCheck)\r\n\r\n  let resultsCount = 0\r\n\r\n  const cache =\r\n    maxSize <= 1\r\n      ? createSingletonCache(comparator)\r\n      : createLruCache(maxSize, comparator)\r\n\r\n  function memoized() {\r\n    let value = cache.get(arguments) as ReturnType<Func>\r\n    if (value === NOT_FOUND) {\r\n      // apply arguments instead of spreading for performance.\r\n      // @ts-ignore\r\n      value = func.apply(null, arguments) as ReturnType<Func>\r\n      resultsCount++\r\n\r\n      if (resultEqualityCheck) {\r\n        const entries = cache.getEntries()\r\n        const matchingEntry = entries.find(entry =>\r\n          resultEqualityCheck(entry.value as ReturnType<Func>, value)\r\n        )\r\n\r\n        if (matchingEntry) {\r\n          value = matchingEntry.value as ReturnType<Func>\r\n          resultsCount !== 0 && resultsCount--\r\n        }\r\n      }\r\n\r\n      cache.put(arguments, value)\r\n    }\r\n    return value\r\n  }\r\n\r\n  memoized.clearCache = () => {\r\n    cache.clear()\r\n    memoized.resetResultsCount()\r\n  }\r\n\r\n  memoized.resultsCount = () => resultsCount\r\n\r\n  memoized.resetResultsCount = () => {\r\n    resultsCount = 0\r\n  }\r\n\r\n  return memoized as Func & Simplify<DefaultMemoizeFields>\r\n}\r\n", "import { createNode, updateNode } from './proxy'\r\nimport type { Node } from './tracking'\r\n\r\nimport { createCacheKeyComparator, referenceEqualityCheck } from '../lruMemoize'\r\nimport type { AnyFunction, DefaultMemoizeFields, Simplify } from '../types'\r\nimport { createCache } from './autotracking'\r\n\r\n/**\r\n * Uses an \"auto-tracking\" approach inspired by the work of the Ember Glimmer team.\r\n * It uses a Proxy to wrap arguments and track accesses to nested fields\r\n * in your selector on first read. Later, when the selector is called with\r\n * new arguments, it identifies which accessed fields have changed and\r\n * only recalculates the result if one or more of those accessed fields have changed.\r\n * This allows it to be more precise than the shallow equality checks in `lruMemoize`.\r\n *\r\n * __Design Tradeoffs for `autotrackMemoize`:__\r\n * - Pros:\r\n *    - It is likely to avoid excess calculations and recalculate fewer times than `lruMemoize` will,\r\n *    which may also result in fewer component re-renders.\r\n * - Cons:\r\n *    - It only has a cache size of 1.\r\n *    - It is slower than `lruMemoize`, because it has to do more work. (How much slower is dependent on the number of accessed fields in a selector, number of calls, frequency of input changes, etc)\r\n *    - It can have some unexpected behavior. Because it tracks nested field accesses,\r\n *    cases where you don't access a field will not recalculate properly.\r\n *    For example, a badly-written selector like:\r\n *      ```ts\r\n *      createSelector([state => state.todos], todos => todos)\r\n *      ```\r\n *      that just immediately returns the extracted value will never update, because it doesn't see any field accesses to check.\r\n *\r\n * __Use Cases for `autotrackMemoize`:__\r\n * - It is likely best used for cases where you need to access specific nested fields\r\n * in data, and avoid recalculating if other fields in the same data objects are immutably updated.\r\n *\r\n * @param func - The function to be memoized.\r\n * @returns A memoized function with a `.clearCache()` method attached.\r\n *\r\n * @example\r\n * <caption>Using `createSelector`</caption>\r\n * ```ts\r\n * import { unstable_autotrackMemoize as autotrackMemoize, createSelector } from 'reselect'\r\n *\r\n * const selectTodoIds = createSelector(\r\n *   [(state: RootState) => state.todos],\r\n *   (todos) => todos.map(todo => todo.id),\r\n *   { memoize: autotrackMemoize }\r\n * )\r\n * ```\r\n *\r\n * @example\r\n * <caption>Using `createSelectorCreator`</caption>\r\n * ```ts\r\n * import { unstable_autotrackMemoize as autotrackMemoize, createSelectorCreator } from 'reselect'\r\n *\r\n * const createSelectorAutotrack = createSelectorCreator({ memoize: autotrackMemoize })\r\n *\r\n * const selectTodoIds = createSelectorAutotrack(\r\n *   [(state: RootState) => state.todos],\r\n *   (todos) => todos.map(todo => todo.id)\r\n * )\r\n * ```\r\n *\r\n * @template Func - The type of the function that is memoized.\r\n *\r\n * @see {@link https://reselect.js.org/api/unstable_autotrackMemoize autotrackMemoize}\r\n *\r\n * @since 5.0.0\r\n * @public\r\n * @experimental\r\n */\r\nexport function autotrackMemoize<Func extends AnyFunction>(func: Func) {\r\n  // we reference arguments instead of spreading them for performance reasons\r\n\r\n  const node: Node<Record<string, unknown>> = createNode(\r\n    [] as unknown as Record<string, unknown>\r\n  )\r\n\r\n  let lastArgs: IArguments | null = null\r\n\r\n  const shallowEqual = createCacheKeyComparator(referenceEqualityCheck)\r\n\r\n  const cache = createCache(() => {\r\n    const res = func.apply(null, node.proxy as unknown as any[])\r\n    return res\r\n  })\r\n\r\n  function memoized() {\r\n    if (!shallowEqual(lastArgs, arguments)) {\r\n      updateNode(node, arguments as unknown as Record<string, unknown>)\r\n      lastArgs = arguments\r\n    }\r\n    return cache.value\r\n  }\r\n\r\n  memoized.clearCache = () => {\r\n    return cache.clear()\r\n  }\r\n\r\n  return memoized as Func & Simplify<DefaultMemoizeFields>\r\n}\r\n", "// Original source:\r\n// - https://github.com/facebook/react/blob/0b974418c9a56f6c560298560265dcf4b65784bc/packages/react/src/ReactCache.js\r\n\r\nimport type {\r\n  AnyFunction,\r\n  DefaultMemoizeFields,\r\n  EqualityFn,\r\n  Simplify\r\n} from './types'\r\n\r\nclass StrongRef<T> {\r\n  constructor(private value: T) {}\r\n  deref() {\r\n    return this.value\r\n  }\r\n}\r\n\r\nconst Ref =\r\n  typeof WeakRef !== 'undefined'\r\n    ? WeakRef\r\n    : (StrongRef as unknown as typeof WeakRef)\r\n\r\nconst UNTERMINATED = 0\r\nconst TERMINATED = 1\r\n\r\ninterface UnterminatedCacheNode<T> {\r\n  /**\r\n   * Status, represents whether the cached computation returned a value or threw an error.\r\n   */\r\n  s: 0\r\n  /**\r\n   * Value, either the cached result or an error, depending on status.\r\n   */\r\n  v: void\r\n  /**\r\n   * Object cache, a `WeakMap` where non-primitive arguments are stored.\r\n   */\r\n  o: null | WeakMap<Function | Object, CacheNode<T>>\r\n  /**\r\n   * Primitive cache, a regular Map where primitive arguments are stored.\r\n   */\r\n  p: null | Map<string | number | null | void | symbol | boolean, CacheNode<T>>\r\n}\r\n\r\ninterface TerminatedCacheNode<T> {\r\n  /**\r\n   * Status, represents whether the cached computation returned a value or threw an error.\r\n   */\r\n  s: 1\r\n  /**\r\n   * Value, either the cached result or an error, depending on status.\r\n   */\r\n  v: T\r\n  /**\r\n   * Object cache, a `WeakMap` where non-primitive arguments are stored.\r\n   */\r\n  o: null | WeakMap<Function | Object, CacheNode<T>>\r\n  /**\r\n   * Primitive cache, a regular `Map` where primitive arguments are stored.\r\n   */\r\n  p: null | Map<string | number | null | void | symbol | boolean, CacheNode<T>>\r\n}\r\n\r\ntype CacheNode<T> = TerminatedCacheNode<T> | UnterminatedCacheNode<T>\r\n\r\nfunction createCacheNode<T>(): CacheNode<T> {\r\n  return {\r\n    s: UNTERMINATED,\r\n    v: undefined,\r\n    o: null,\r\n    p: null\r\n  }\r\n}\r\n\r\n/**\r\n * Configuration options for a memoization function utilizing `WeakMap` for\r\n * its caching mechanism.\r\n *\r\n * @template Result - The type of the return value of the memoized function.\r\n *\r\n * @since 5.0.0\r\n * @public\r\n */\r\nexport interface WeakMapMemoizeOptions<Result = any> {\r\n  /**\r\n   * If provided, used to compare a newly generated output value against previous values in the cache.\r\n   * If a match is found, the old value is returned. This addresses the common\r\n   * ```ts\r\n   * todos.map(todo => todo.id)\r\n   * ```\r\n   * use case, where an update to another field in the original data causes a recalculation\r\n   * due to changed references, but the output is still effectively the same.\r\n   *\r\n   * @since 5.0.0\r\n   */\r\n  resultEqualityCheck?: EqualityFn<Result>\r\n}\r\n\r\n/**\r\n * Creates a tree of `WeakMap`-based cache nodes based on the identity of the\r\n * arguments it's been called with (in this case, the extracted values from your input selectors).\r\n * This allows `weakMapMemoize` to have an effectively infinite cache size.\r\n * Cache results will be kept in memory as long as references to the arguments still exist,\r\n * and then cleared out as the arguments are garbage-collected.\r\n *\r\n * __Design Tradeoffs for `weakMapMemoize`:__\r\n * - Pros:\r\n *   - It has an effectively infinite cache size, but you have no control over\r\n *   how long values are kept in cache as it's based on garbage collection and `WeakMap`s.\r\n * - Cons:\r\n *   - There's currently no way to alter the argument comparisons.\r\n *   They're based on strict reference equality.\r\n *   - It's roughly the same speed as `lruMemoize`, although likely a fraction slower.\r\n *\r\n * __Use Cases for `weakMapMemoize`:__\r\n * - This memoizer is likely best used for cases where you need to call the\r\n * same selector instance with many different arguments, such as a single\r\n * selector instance that is used in a list item component and called with\r\n * item IDs like:\r\n *   ```ts\r\n *   useSelector(state => selectSomeData(state, props.category))\r\n *   ```\r\n * @param func - The function to be memoized.\r\n * @returns A memoized function with a `.clearCache()` method attached.\r\n *\r\n * @example\r\n * <caption>Using `createSelector`</caption>\r\n * ```ts\r\n * import { createSelector, weakMapMemoize } from 'reselect'\r\n *\r\n * interface RootState {\r\n *   items: { id: number; category: string; name: string }[]\r\n * }\r\n *\r\n * const selectItemsByCategory = createSelector(\r\n *   [\r\n *     (state: RootState) => state.items,\r\n *     (state: RootState, category: string) => category\r\n *   ],\r\n *   (items, category) => items.filter(item => item.category === category),\r\n *   {\r\n *     memoize: weakMapMemoize,\r\n *     argsMemoize: weakMapMemoize\r\n *   }\r\n * )\r\n * ```\r\n *\r\n * @example\r\n * <caption>Using `createSelectorCreator`</caption>\r\n * ```ts\r\n * import { createSelectorCreator, weakMapMemoize } from 'reselect'\r\n *\r\n * const createSelectorWeakMap = createSelectorCreator({ memoize: weakMapMemoize, argsMemoize: weakMapMemoize })\r\n *\r\n * const selectItemsByCategory = createSelectorWeakMap(\r\n *   [\r\n *     (state: RootState) => state.items,\r\n *     (state: RootState, category: string) => category\r\n *   ],\r\n *   (items, category) => items.filter(item => item.category === category)\r\n * )\r\n * ```\r\n *\r\n * @template Func - The type of the function that is memoized.\r\n *\r\n * @see {@link https://reselect.js.org/api/weakMapMemoize `weakMapMemoize`}\r\n *\r\n * @since 5.0.0\r\n * @public\r\n * @experimental\r\n */\r\nexport function weakMapMemoize<Func extends AnyFunction>(\r\n  func: Func,\r\n  options: WeakMapMemoizeOptions<ReturnType<Func>> = {}\r\n) {\r\n  let fnNode = createCacheNode()\r\n  const { resultEqualityCheck } = options\r\n\r\n  let lastResult: WeakRef<object> | undefined\r\n\r\n  let resultsCount = 0\r\n\r\n  function memoized() {\r\n    let cacheNode = fnNode\r\n    const { length } = arguments\r\n    for (let i = 0, l = length; i < l; i++) {\r\n      const arg = arguments[i]\r\n      if (\r\n        typeof arg === 'function' ||\r\n        (typeof arg === 'object' && arg !== null)\r\n      ) {\r\n        // Objects go into a WeakMap\r\n        let objectCache = cacheNode.o\r\n        if (objectCache === null) {\r\n          cacheNode.o = objectCache = new WeakMap()\r\n        }\r\n        const objectNode = objectCache.get(arg)\r\n        if (objectNode === undefined) {\r\n          cacheNode = createCacheNode()\r\n          objectCache.set(arg, cacheNode)\r\n        } else {\r\n          cacheNode = objectNode\r\n        }\r\n      } else {\r\n        // Primitives go into a regular Map\r\n        let primitiveCache = cacheNode.p\r\n        if (primitiveCache === null) {\r\n          cacheNode.p = primitiveCache = new Map()\r\n        }\r\n        const primitiveNode = primitiveCache.get(arg)\r\n        if (primitiveNode === undefined) {\r\n          cacheNode = createCacheNode()\r\n          primitiveCache.set(arg, cacheNode)\r\n        } else {\r\n          cacheNode = primitiveNode\r\n        }\r\n      }\r\n    }\r\n\r\n    const terminatedNode = cacheNode as unknown as TerminatedCacheNode<any>\r\n\r\n    let result\r\n\r\n    if (cacheNode.s === TERMINATED) {\r\n      result = cacheNode.v\r\n    } else {\r\n      // Allow errors to propagate\r\n      result = func.apply(null, arguments as unknown as any[])\r\n      resultsCount++\r\n\r\n      if (resultEqualityCheck) {\r\n        const lastResultValue = lastResult?.deref?.() ?? lastResult\r\n\r\n        if (\r\n          lastResultValue != null &&\r\n          resultEqualityCheck(lastResultValue as ReturnType<Func>, result)\r\n        ) {\r\n          result = lastResultValue\r\n\r\n          resultsCount !== 0 && resultsCount--\r\n        }\r\n\r\n        const needsWeakRef =\r\n          (typeof result === 'object' && result !== null) ||\r\n          typeof result === 'function'\r\n\r\n        lastResult = needsWeakRef ? new Ref(result) : result\r\n      }\r\n    }\r\n\r\n    terminatedNode.s = TERMINATED\r\n\r\n    terminatedNode.v = result\r\n    return result\r\n  }\r\n\r\n  memoized.clearCache = () => {\r\n    fnNode = createCacheNode()\r\n    memoized.resetResultsCount()\r\n  }\r\n\r\n  memoized.resultsCount = () => resultsCount\r\n\r\n  memoized.resetResultsCount = () => {\r\n    resultsCount = 0\r\n  }\r\n\r\n  return memoized as Func & Simplify<DefaultMemoizeFields>\r\n}\r\n", "import { weakMapMemoize } from './weakMapMemoize'\r\n\r\nimport type {\r\n  <PERSON><PERSON><PERSON>,\r\n  CreateSelectorOptions,\r\n  DropFirstParameter,\r\n  ExtractMemoizerFields,\r\n  GetParamsFromSelectors,\r\n  GetStateFromSelectors,\r\n  InterruptRecursion,\r\n  OutputSelector,\r\n  Selector,\r\n  SelectorArray,\r\n  SetRequired,\r\n  Simplify,\r\n  UnknownMemoizer\r\n} from './types'\r\n\r\nimport {\r\n  assertIsFunction,\r\n  collectInputSelectorResults,\r\n  ensureIsArray,\r\n  getDependencies,\r\n  getDevModeChecksExecutionInfo\r\n} from './utils'\r\n\r\n/**\r\n * An instance of `createSelector`, customized with a given memoize implementation.\r\n *\r\n * @template MemoizeFunction - The type of the memoize function that is used to memoize the `resultFunc` inside `createSelector` (e.g., `lruMemoize` or `weakMapMemoize`).\r\n * @template ArgsMemoizeFunction - The type of the optional memoize function that is used to memoize the arguments passed into the output selector generated by `createSelector` (e.g., `lruMemoize` or `weakMapMemoize`). If none is explicitly provided, `weakMapMemoize` will be used.\r\n * @template StateType - The type of state that the selectors created with this selector creator will operate on.\r\n *\r\n * @public\r\n */\r\nexport interface CreateSelectorFunction<\r\n  MemoizeFunction extends UnknownMemoizer = typeof weakMapMemoize,\r\n  ArgsMemoizeFunction extends UnknownMemoizer = typeof weakMapMemoize,\r\n  StateType = any\r\n> {\r\n  /**\r\n   * Creates a memoized selector function.\r\n   *\r\n   * @param createSelectorArgs - An arbitrary number of input selectors as separate inline arguments and a `combiner` function.\r\n   * @returns A memoized output selector.\r\n   *\r\n   * @template InputSelectors - The type of the input selectors as an array.\r\n   * @template Result - The return type of the `combiner` as well as the output selector.\r\n   * @template OverrideMemoizeFunction - The type of the optional `memoize` function that could be passed into the options object to override the original `memoize` function that was initially passed into `createSelectorCreator`.\r\n   * @template OverrideArgsMemoizeFunction - The type of the optional `argsMemoize` function that could be passed into the options object to override the original `argsMemoize` function that was initially passed into `createSelectorCreator`.\r\n   *\r\n   * @see {@link https://reselect.js.org/api/createselector `createSelector`}\r\n   */\r\n  <InputSelectors extends SelectorArray<StateType>, Result>(\r\n    ...createSelectorArgs: [\r\n      ...inputSelectors: InputSelectors,\r\n      combiner: Combiner<InputSelectors, Result>\r\n    ]\r\n  ): OutputSelector<\r\n    InputSelectors,\r\n    Result,\r\n    MemoizeFunction,\r\n    ArgsMemoizeFunction\r\n  > &\r\n    InterruptRecursion\r\n\r\n  /**\r\n   * Creates a memoized selector function.\r\n   *\r\n   * @param createSelectorArgs - An arbitrary number of input selectors as separate inline arguments, a `combiner` function and an `options` object.\r\n   * @returns A memoized output selector.\r\n   *\r\n   * @template InputSelectors - The type of the input selectors as an array.\r\n   * @template Result - The return type of the `combiner` as well as the output selector.\r\n   * @template OverrideMemoizeFunction - The type of the optional `memoize` function that could be passed into the options object to override the original `memoize` function that was initially passed into `createSelectorCreator`.\r\n   * @template OverrideArgsMemoizeFunction - The type of the optional `argsMemoize` function that could be passed into the options object to override the original `argsMemoize` function that was initially passed into `createSelectorCreator`.\r\n   *\r\n   * @see {@link https://reselect.js.org/api/createselector `createSelector`}\r\n   */\r\n  <\r\n    InputSelectors extends SelectorArray<StateType>,\r\n    Result,\r\n    OverrideMemoizeFunction extends UnknownMemoizer = MemoizeFunction,\r\n    OverrideArgsMemoizeFunction extends UnknownMemoizer = ArgsMemoizeFunction\r\n  >(\r\n    ...createSelectorArgs: [\r\n      ...inputSelectors: InputSelectors,\r\n      combiner: Combiner<InputSelectors, Result>,\r\n      createSelectorOptions: Simplify<\r\n        CreateSelectorOptions<\r\n          MemoizeFunction,\r\n          ArgsMemoizeFunction,\r\n          OverrideMemoizeFunction,\r\n          OverrideArgsMemoizeFunction\r\n        >\r\n      >\r\n    ]\r\n  ): OutputSelector<\r\n    InputSelectors,\r\n    Result,\r\n    OverrideMemoizeFunction,\r\n    OverrideArgsMemoizeFunction\r\n  > &\r\n    InterruptRecursion\r\n\r\n  /**\r\n   * Creates a memoized selector function.\r\n   *\r\n   * @param inputSelectors - An array of input selectors.\r\n   * @param combiner - A function that Combines the input selectors and returns an output selector. Otherwise known as the result function.\r\n   * @param createSelectorOptions - An optional options object that allows for further customization per selector.\r\n   * @returns A memoized output selector.\r\n   *\r\n   * @template InputSelectors - The type of the input selectors array.\r\n   * @template Result - The return type of the `combiner` as well as the output selector.\r\n   * @template OverrideMemoizeFunction - The type of the optional `memoize` function that could be passed into the options object to override the original `memoize` function that was initially passed into `createSelectorCreator`.\r\n   * @template OverrideArgsMemoizeFunction - The type of the optional `argsMemoize` function that could be passed into the options object to override the original `argsMemoize` function that was initially passed into `createSelectorCreator`.\r\n   *\r\n   * @see {@link https://reselect.js.org/api/createselector `createSelector`}\r\n   */\r\n  <\r\n    InputSelectors extends SelectorArray<StateType>,\r\n    Result,\r\n    OverrideMemoizeFunction extends UnknownMemoizer = MemoizeFunction,\r\n    OverrideArgsMemoizeFunction extends UnknownMemoizer = ArgsMemoizeFunction\r\n  >(\r\n    inputSelectors: [...InputSelectors],\r\n    combiner: Combiner<InputSelectors, Result>,\r\n    createSelectorOptions?: Simplify<\r\n      CreateSelectorOptions<\r\n        MemoizeFunction,\r\n        ArgsMemoizeFunction,\r\n        OverrideMemoizeFunction,\r\n        OverrideArgsMemoizeFunction\r\n      >\r\n    >\r\n  ): OutputSelector<\r\n    InputSelectors,\r\n    Result,\r\n    OverrideMemoizeFunction,\r\n    OverrideArgsMemoizeFunction\r\n  > &\r\n    InterruptRecursion\r\n\r\n  /**\r\n   * Creates a \"pre-typed\" version of {@linkcode createSelector createSelector}\r\n   * where the `state` type is predefined.\r\n   *\r\n   * This allows you to set the `state` type once, eliminating the need to\r\n   * specify it with every {@linkcode createSelector createSelector} call.\r\n   *\r\n   * @returns A pre-typed `createSelector` with the state type already defined.\r\n   *\r\n   * @example\r\n   * ```ts\r\n   * import { createSelector } from 'reselect'\r\n   *\r\n   * export interface RootState {\r\n   *   todos: { id: number; completed: boolean }[]\r\n   *   alerts: { id: number; read: boolean }[]\r\n   * }\r\n   *\r\n   * export const createAppSelector = createSelector.withTypes<RootState>()\r\n   *\r\n   * const selectTodoIds = createAppSelector(\r\n   *   [\r\n   *     // Type of `state` is set to `RootState`, no need to manually set the type\r\n   *     state => state.todos\r\n   *   ],\r\n   *   todos => todos.map(({ id }) => id)\r\n   * )\r\n   * ```\r\n   * @template OverrideStateType - The specific type of state used by all selectors created with this selector creator.\r\n   *\r\n   * @see {@link https://reselect.js.org/api/createselector#defining-a-pre-typed-createselector `createSelector.withTypes`}\r\n   *\r\n   * @since 5.1.0\r\n   */\r\n  withTypes: <OverrideStateType extends StateType>() => CreateSelectorFunction<\r\n    MemoizeFunction,\r\n    ArgsMemoizeFunction,\r\n    OverrideStateType\r\n  >\r\n}\r\n\r\n/**\r\n * Creates a selector creator function with the specified memoization function\r\n * and options for customizing memoization behavior.\r\n *\r\n * @param options - An options object containing the `memoize` function responsible for memoizing the `resultFunc` inside `createSelector` (e.g., `lruMemoize` or `weakMapMemoize`). It also provides additional options for customizing memoization. While the `memoize` property is mandatory, the rest are optional.\r\n * @returns A customized `createSelector` function.\r\n *\r\n * @example\r\n * ```ts\r\n * const customCreateSelector = createSelectorCreator({\r\n *   memoize: customMemoize, // Function to be used to memoize `resultFunc`\r\n *   memoizeOptions: [memoizeOption1, memoizeOption2], // Options passed to `customMemoize` as the second argument onwards\r\n *   argsMemoize: customArgsMemoize, // Function to be used to memoize the selector's arguments\r\n *   argsMemoizeOptions: [argsMemoizeOption1, argsMemoizeOption2] // Options passed to `customArgsMemoize` as the second argument onwards\r\n * })\r\n *\r\n * const customSelector = customCreateSelector(\r\n *   [inputSelector1, inputSelector2],\r\n *   resultFunc // `resultFunc` will be passed as the first argument to `customMemoize`\r\n * )\r\n *\r\n * customSelector(\r\n *   ...selectorArgs // Will be memoized by `customArgsMemoize`\r\n * )\r\n * ```\r\n *\r\n * @template MemoizeFunction - The type of the memoize function that is used to memoize the `resultFunc` inside `createSelector` (e.g., `lruMemoize` or `weakMapMemoize`).\r\n * @template ArgsMemoizeFunction - The type of the optional memoize function that is used to memoize the arguments passed into the output selector generated by `createSelector` (e.g., `lruMemoize` or `weakMapMemoize`). If none is explicitly provided, `weakMapMemoize` will be used.\r\n *\r\n * @see {@link https://reselect.js.org/api/createSelectorCreator#using-options-since-500 `createSelectorCreator`}\r\n *\r\n * @since 5.0.0\r\n * @public\r\n */\r\nexport function createSelectorCreator<\r\n  MemoizeFunction extends UnknownMemoizer,\r\n  ArgsMemoizeFunction extends UnknownMemoizer = typeof weakMapMemoize\r\n>(\r\n  options: Simplify<\r\n    SetRequired<\r\n      CreateSelectorOptions<\r\n        typeof weakMapMemoize,\r\n        typeof weakMapMemoize,\r\n        MemoizeFunction,\r\n        ArgsMemoizeFunction\r\n      >,\r\n      'memoize'\r\n    >\r\n  >\r\n): CreateSelectorFunction<MemoizeFunction, ArgsMemoizeFunction>\r\n\r\n/**\r\n * Creates a selector creator function with the specified memoization function\r\n * and options for customizing memoization behavior.\r\n *\r\n * @param memoize - The `memoize` function responsible for memoizing the `resultFunc` inside `createSelector` (e.g., `lruMemoize` or `weakMapMemoize`).\r\n * @param memoizeOptionsFromArgs - Optional configuration options for the memoization function. These options are then passed to the memoize function as the second argument onwards.\r\n * @returns A customized `createSelector` function.\r\n *\r\n * @example\r\n * ```ts\r\n * const customCreateSelector = createSelectorCreator(customMemoize, // Function to be used to memoize `resultFunc`\r\n *   option1, // Will be passed as second argument to `customMemoize`\r\n *   option2, // Will be passed as third argument to `customMemoize`\r\n *   option3 // Will be passed as fourth argument to `customMemoize`\r\n * )\r\n *\r\n * const customSelector = customCreateSelector(\r\n *   [inputSelector1, inputSelector2],\r\n *   resultFunc // `resultFunc` will be passed as the first argument to `customMemoize`\r\n * )\r\n * ```\r\n *\r\n * @template MemoizeFunction - The type of the memoize function that is used to memoize the `resultFunc` inside `createSelector` (e.g., `lruMemoize` or `weakMapMemoize`).\r\n *\r\n * @see {@link https://reselect.js.org/api/createSelectorCreator#using-memoize-and-memoizeoptions `createSelectorCreator`}\r\n *\r\n * @public\r\n */\r\nexport function createSelectorCreator<MemoizeFunction extends UnknownMemoizer>(\r\n  memoize: MemoizeFunction,\r\n  ...memoizeOptionsFromArgs: DropFirstParameter<MemoizeFunction>\r\n): CreateSelectorFunction<MemoizeFunction>\r\n\r\n/**\r\n * Creates a selector creator function with the specified memoization\r\n * function and options for customizing memoization behavior.\r\n *\r\n * @param memoizeOrOptions - Either A `memoize` function or an `options` object containing the `memoize` function.\r\n * @param memoizeOptionsFromArgs - Optional configuration options for the memoization function. These options are then passed to the memoize function as the second argument onwards.\r\n * @returns A customized `createSelector` function.\r\n *\r\n * @template MemoizeFunction - The type of the memoize function that is used to memoize the `resultFunc` inside `createSelector` (e.g., `lruMemoize` or `weakMapMemoize`).\r\n * @template ArgsMemoizeFunction - The type of the optional memoize function that is used to memoize the arguments passed into the output selector generated by `createSelector` (e.g., `lruMemoize` or `weakMapMemoize`). If none is explicitly provided, `weakMapMemoize` will be used.\r\n * @template MemoizeOrOptions - The type of the first argument. It can either be a `memoize` function or an `options` object containing the `memoize` function.\r\n */\r\nexport function createSelectorCreator<\r\n  MemoizeFunction extends UnknownMemoizer,\r\n  ArgsMemoizeFunction extends UnknownMemoizer,\r\n  MemoizeOrOptions extends\r\n    | MemoizeFunction\r\n    | SetRequired<\r\n        CreateSelectorOptions<MemoizeFunction, ArgsMemoizeFunction>,\r\n        'memoize'\r\n      >\r\n>(\r\n  memoizeOrOptions: MemoizeOrOptions,\r\n  ...memoizeOptionsFromArgs: MemoizeOrOptions extends SetRequired<\r\n    CreateSelectorOptions<MemoizeFunction, ArgsMemoizeFunction>,\r\n    'memoize'\r\n  >\r\n    ? never\r\n    : DropFirstParameter<MemoizeFunction>\r\n) {\r\n  /** options initially passed into `createSelectorCreator`. */\r\n  const createSelectorCreatorOptions: SetRequired<\r\n    CreateSelectorOptions<MemoizeFunction, ArgsMemoizeFunction>,\r\n    'memoize'\r\n  > = typeof memoizeOrOptions === 'function'\r\n    ? {\r\n        memoize: memoizeOrOptions as MemoizeFunction,\r\n        memoizeOptions: memoizeOptionsFromArgs\r\n      }\r\n    : memoizeOrOptions\r\n\r\n  const createSelector = <\r\n    InputSelectors extends SelectorArray,\r\n    Result,\r\n    OverrideMemoizeFunction extends UnknownMemoizer = MemoizeFunction,\r\n    OverrideArgsMemoizeFunction extends UnknownMemoizer = ArgsMemoizeFunction\r\n  >(\r\n    ...createSelectorArgs: [\r\n      ...inputSelectors: [...InputSelectors],\r\n      combiner: Combiner<InputSelectors, Result>,\r\n      createSelectorOptions?: CreateSelectorOptions<\r\n        MemoizeFunction,\r\n        ArgsMemoizeFunction,\r\n        OverrideMemoizeFunction,\r\n        OverrideArgsMemoizeFunction\r\n      >\r\n    ]\r\n  ) => {\r\n    let recomputations = 0\r\n    let dependencyRecomputations = 0\r\n    let lastResult: Result\r\n\r\n    // Due to the intricacies of rest params, we can't do an optional arg after `...createSelectorArgs`.\r\n    // So, start by declaring the default value here.\r\n    // (And yes, the words 'memoize' and 'options' appear too many times in this next sequence.)\r\n    let directlyPassedOptions: CreateSelectorOptions<\r\n      MemoizeFunction,\r\n      ArgsMemoizeFunction,\r\n      OverrideMemoizeFunction,\r\n      OverrideArgsMemoizeFunction\r\n    > = {}\r\n\r\n    // Normally, the result func or \"combiner\" is the last arg\r\n    let resultFunc = createSelectorArgs.pop() as\r\n      | Combiner<InputSelectors, Result>\r\n      | CreateSelectorOptions<\r\n          MemoizeFunction,\r\n          ArgsMemoizeFunction,\r\n          OverrideMemoizeFunction,\r\n          OverrideArgsMemoizeFunction\r\n        >\r\n\r\n    // If the result func is actually an _object_, assume it's our options object\r\n    if (typeof resultFunc === 'object') {\r\n      directlyPassedOptions = resultFunc\r\n      // and pop the real result func off\r\n      resultFunc = createSelectorArgs.pop() as Combiner<InputSelectors, Result>\r\n    }\r\n\r\n    assertIsFunction(\r\n      resultFunc,\r\n      `createSelector expects an output function after the inputs, but received: [${typeof resultFunc}]`\r\n    )\r\n\r\n    // Determine which set of options we're using. Prefer options passed directly,\r\n    // but fall back to options given to `createSelectorCreator`.\r\n    const combinedOptions = {\r\n      ...createSelectorCreatorOptions,\r\n      ...directlyPassedOptions\r\n    }\r\n\r\n    const {\r\n      memoize,\r\n      memoizeOptions = [],\r\n      argsMemoize = weakMapMemoize,\r\n      argsMemoizeOptions = [],\r\n      devModeChecks = {}\r\n    } = combinedOptions\r\n\r\n    // Simplifying assumption: it's unlikely that the first options arg of the provided memoizer\r\n    // is an array. In most libs I've looked at, it's an equality function or options object.\r\n    // Based on that, if `memoizeOptions` _is_ an array, we assume it's a full\r\n    // user-provided array of options. Otherwise, it must be just the _first_ arg, and so\r\n    // we wrap it in an array so we can apply it.\r\n    const finalMemoizeOptions = ensureIsArray(memoizeOptions)\r\n    const finalArgsMemoizeOptions = ensureIsArray(argsMemoizeOptions)\r\n    const dependencies = getDependencies(createSelectorArgs) as InputSelectors\r\n\r\n    const memoizedResultFunc = memoize(function recomputationWrapper() {\r\n      recomputations++\r\n      // apply arguments instead of spreading for performance.\r\n      // @ts-ignore\r\n      return (resultFunc as Combiner<InputSelectors, Result>).apply(\r\n        null,\r\n        arguments as unknown as Parameters<Combiner<InputSelectors, Result>>\r\n      )\r\n    }, ...finalMemoizeOptions) as Combiner<InputSelectors, Result> &\r\n      ExtractMemoizerFields<OverrideMemoizeFunction>\r\n\r\n    let firstRun = true\r\n\r\n    // If a selector is called with the exact same arguments we don't need to traverse our dependencies again.\r\n    const selector = argsMemoize(function dependenciesChecker() {\r\n      dependencyRecomputations++\r\n      /** Return values of input selectors which the `resultFunc` takes as arguments. */\r\n      const inputSelectorResults = collectInputSelectorResults(\r\n        dependencies,\r\n        arguments\r\n      )\r\n\r\n      // apply arguments instead of spreading for performance.\r\n      // @ts-ignore\r\n      lastResult = memoizedResultFunc.apply(null, inputSelectorResults)\r\n\r\n      if (process.env.NODE_ENV !== 'production') {\r\n        const { identityFunctionCheck, inputStabilityCheck } =\r\n          getDevModeChecksExecutionInfo(firstRun, devModeChecks)\r\n        if (identityFunctionCheck.shouldRun) {\r\n          identityFunctionCheck.run(\r\n            resultFunc as Combiner<InputSelectors, Result>,\r\n            inputSelectorResults,\r\n            lastResult\r\n          )\r\n        }\r\n\r\n        if (inputStabilityCheck.shouldRun) {\r\n          // make a second copy of the params, to check if we got the same results\r\n          const inputSelectorResultsCopy = collectInputSelectorResults(\r\n            dependencies,\r\n            arguments\r\n          )\r\n\r\n          inputStabilityCheck.run(\r\n            { inputSelectorResults, inputSelectorResultsCopy },\r\n            { memoize, memoizeOptions: finalMemoizeOptions },\r\n            arguments\r\n          )\r\n        }\r\n\r\n        if (firstRun) firstRun = false\r\n      }\r\n\r\n      return lastResult\r\n    }, ...finalArgsMemoizeOptions) as unknown as Selector<\r\n      GetStateFromSelectors<InputSelectors>,\r\n      Result,\r\n      GetParamsFromSelectors<InputSelectors>\r\n    > &\r\n      ExtractMemoizerFields<OverrideArgsMemoizeFunction>\r\n\r\n    return Object.assign(selector, {\r\n      resultFunc,\r\n      memoizedResultFunc,\r\n      dependencies,\r\n      dependencyRecomputations: () => dependencyRecomputations,\r\n      resetDependencyRecomputations: () => {\r\n        dependencyRecomputations = 0\r\n      },\r\n      lastResult: () => lastResult,\r\n      recomputations: () => recomputations,\r\n      resetRecomputations: () => {\r\n        recomputations = 0\r\n      },\r\n      memoize,\r\n      argsMemoize\r\n    }) as OutputSelector<\r\n      InputSelectors,\r\n      Result,\r\n      OverrideMemoizeFunction,\r\n      OverrideArgsMemoizeFunction\r\n    >\r\n  }\r\n\r\n  Object.assign(createSelector, {\r\n    withTypes: () => createSelector\r\n  })\r\n\r\n  return createSelector as CreateSelectorFunction<\r\n    MemoizeFunction,\r\n    ArgsMemoizeFunction\r\n  >\r\n}\r\n\r\n/**\r\n * Accepts one or more \"input selectors\" (either as separate arguments or a single array),\r\n * a single \"result function\" / \"combiner\", and an optional options object, and\r\n * generates a memoized selector function.\r\n *\r\n * @see {@link https://reselect.js.org/api/createSelector `createSelector`}\r\n *\r\n * @public\r\n */\r\nexport const createSelector =\r\n  /* #__PURE__ */ createSelectorCreator(weakMapMemoize)\r\n", "import { createSelector } from './createSelectorCreator'\r\n\r\nimport type { CreateSelectorFunction } from './createSelectorCreator'\r\nimport type {\r\n  InterruptRecursion,\r\n  ObjectValuesToTuple,\r\n  OutputSelector,\r\n  Selector,\r\n  Simplify,\r\n  UnknownMemoizer\r\n} from './types'\r\nimport { assertIsObject } from './utils'\r\nimport type { weakMapMemoize } from './weakMapMemoize'\r\n\r\n/**\r\n * Represents a mapping of selectors to their return types.\r\n *\r\n * @template TObject - An object type where each property is a selector function.\r\n *\r\n * @public\r\n */\r\nexport type SelectorResultsMap<TObject extends SelectorsObject> = {\r\n  [Key in keyof TObject]: ReturnType<TObject[Key]>\r\n}\r\n\r\n/**\r\n * Represents a mapping of selectors for each key in a given root state.\r\n *\r\n * This type is a utility that takes a root state object type and\r\n * generates a corresponding set of selectors. Each selector is associated\r\n * with a key in the root state, allowing for the selection\r\n * of specific parts of the state.\r\n *\r\n * @template RootState - The type of the root state object.\r\n *\r\n * @since 5.0.0\r\n * @public\r\n */\r\nexport type RootStateSelectors<RootState = any> = {\r\n  [Key in keyof RootState]: Selector<RootState, RootState[Key], []>\r\n}\r\n\r\n/**\r\n * @deprecated Please use {@linkcode StructuredSelectorCreator.withTypes createStructuredSelector.withTypes<RootState>()} instead. This type will be removed in the future.\r\n * @template RootState - The type of the root state object.\r\n *\r\n * @since 5.0.0\r\n * @public\r\n */\r\nexport type TypedStructuredSelectorCreator<RootState = any> =\r\n  /**\r\n   * A convenience function that simplifies returning an object\r\n   * made up of selector results.\r\n   *\r\n   * @param inputSelectorsObject - A key value pair consisting of input selectors.\r\n   * @param selectorCreator - A custom selector creator function. It defaults to `createSelector`.\r\n   * @returns A memoized structured selector.\r\n   *\r\n   * @example\r\n   * <caption>Modern Use Case</caption>\r\n   * ```ts\r\n   * import { createSelector, createStructuredSelector } from 'reselect'\r\n   *\r\n   * interface RootState {\r\n   *   todos: {\r\n   *     id: number\r\n   *     completed: boolean\r\n   *     title: string\r\n   *     description: string\r\n   *   }[]\r\n   *   alerts: { id: number; read: boolean }[]\r\n   * }\r\n   *\r\n   * // This:\r\n   * const structuredSelector = createStructuredSelector(\r\n   *   {\r\n   *     todos: (state: RootState) => state.todos,\r\n   *     alerts: (state: RootState) => state.alerts,\r\n   *     todoById: (state: RootState, id: number) => state.todos[id]\r\n   *   },\r\n   *   createSelector\r\n   * )\r\n   *\r\n   * // Is essentially the same as this:\r\n   * const selector = createSelector(\r\n   *   [\r\n   *     (state: RootState) => state.todos,\r\n   *     (state: RootState) => state.alerts,\r\n   *     (state: RootState, id: number) => state.todos[id]\r\n   *   ],\r\n   *   (todos, alerts, todoById) => {\r\n   *     return {\r\n   *       todos,\r\n   *       alerts,\r\n   *       todoById\r\n   *     }\r\n   *   }\r\n   * )\r\n   * ```\r\n   *\r\n   * @example\r\n   * <caption>In your component:</caption>\r\n   * ```tsx\r\n   * import type { RootState } from 'createStructuredSelector/modernUseCase'\r\n   * import { structuredSelector } from 'createStructuredSelector/modernUseCase'\r\n   * import type { FC } from 'react'\r\n   * import { useSelector } from 'react-redux'\r\n   *\r\n   * interface Props {\r\n   *   id: number\r\n   * }\r\n   *\r\n   * const MyComponent: FC<Props> = ({ id }) => {\r\n   *   const { todos, alerts, todoById } = useSelector((state: RootState) =>\r\n   *     structuredSelector(state, id)\r\n   *   )\r\n   *\r\n   *   return (\r\n   *     <div>\r\n   *       Next to do is:\r\n   *       <h2>{todoById.title}</h2>\r\n   *       <p>Description: {todoById.description}</p>\r\n   *       <ul>\r\n   *         <h3>All other to dos:</h3>\r\n   *         {todos.map(todo => (\r\n   *           <li key={todo.id}>{todo.title}</li>\r\n   *         ))}\r\n   *       </ul>\r\n   *     </div>\r\n   *   )\r\n   * }\r\n   * ```\r\n   *\r\n   * @example\r\n   * <caption>Simple Use Case</caption>\r\n   * ```ts\r\n   * const selectA = state => state.a\r\n   * const selectB = state => state.b\r\n   *\r\n   * // The result function in the following selector\r\n   * // is simply building an object from the input selectors\r\n   * const structuredSelector = createSelector(selectA, selectB, (a, b) => ({\r\n   *   a,\r\n   *   b\r\n   * }))\r\n   *\r\n   * const result = structuredSelector({ a: 1, b: 2 }) // will produce { x: 1, y: 2 }\r\n   * ```\r\n   *\r\n   * @template InputSelectorsObject - The shape of the input selectors object.\r\n   * @template MemoizeFunction - The type of the memoize function that is used to create the structured selector. It defaults to `weakMapMemoize`.\r\n   * @template ArgsMemoizeFunction - The type of the of the memoize function that is used to memoize the arguments passed into the generated structured selector. It defaults to `weakMapMemoize`.\r\n   *\r\n   * @see {@link https://reselect.js.org/api/createStructuredSelector `createStructuredSelector`}\r\n   */\r\n  <\r\n    InputSelectorsObject extends RootStateSelectors<RootState> = RootStateSelectors<RootState>,\r\n    MemoizeFunction extends UnknownMemoizer = typeof weakMapMemoize,\r\n    ArgsMemoizeFunction extends UnknownMemoizer = typeof weakMapMemoize\r\n  >(\r\n    inputSelectorsObject: InputSelectorsObject,\r\n    selectorCreator?: CreateSelectorFunction<\r\n      MemoizeFunction,\r\n      ArgsMemoizeFunction\r\n    >\r\n  ) => OutputSelector<\r\n    ObjectValuesToTuple<InputSelectorsObject>,\r\n    Simplify<SelectorResultsMap<InputSelectorsObject>>,\r\n    MemoizeFunction,\r\n    ArgsMemoizeFunction\r\n  > &\r\n    InterruptRecursion\r\n\r\n/**\r\n * Represents an object where each property is a selector function.\r\n *\r\n * @template StateType - The type of state that all the selectors operate on.\r\n *\r\n * @public\r\n */\r\nexport type SelectorsObject<StateType = any> = Record<\r\n  string,\r\n  Selector<StateType>\r\n>\r\n\r\n/**\r\n * It provides a way to create structured selectors.\r\n * The structured selector can take multiple input selectors\r\n * and map their output to an object with specific keys.\r\n *\r\n * @template StateType - The type of state that the structured selectors created with this structured selector creator will operate on.\r\n *\r\n * @see {@link https://reselect.js.org/api/createStructuredSelector `createStructuredSelector`}\r\n *\r\n * @public\r\n */\r\nexport interface StructuredSelectorCreator<StateType = any> {\r\n  /**\r\n   * A convenience function that simplifies returning an object\r\n   * made up of selector results.\r\n   *\r\n   * @param inputSelectorsObject - A key value pair consisting of input selectors.\r\n   * @param selectorCreator - A custom selector creator function. It defaults to `createSelector`.\r\n   * @returns A memoized structured selector.\r\n   *\r\n   * @example\r\n   * <caption>Modern Use Case</caption>\r\n   * ```ts\r\n   * import { createSelector, createStructuredSelector } from 'reselect'\r\n   *\r\n   * interface RootState {\r\n   *   todos: {\r\n   *     id: number\r\n   *     completed: boolean\r\n   *     title: string\r\n   *     description: string\r\n   *   }[]\r\n   *   alerts: { id: number; read: boolean }[]\r\n   * }\r\n   *\r\n   * // This:\r\n   * const structuredSelector = createStructuredSelector(\r\n   *   {\r\n   *     todos: (state: RootState) => state.todos,\r\n   *     alerts: (state: RootState) => state.alerts,\r\n   *     todoById: (state: RootState, id: number) => state.todos[id]\r\n   *   },\r\n   *   createSelector\r\n   * )\r\n   *\r\n   * // Is essentially the same as this:\r\n   * const selector = createSelector(\r\n   *   [\r\n   *     (state: RootState) => state.todos,\r\n   *     (state: RootState) => state.alerts,\r\n   *     (state: RootState, id: number) => state.todos[id]\r\n   *   ],\r\n   *   (todos, alerts, todoById) => {\r\n   *     return {\r\n   *       todos,\r\n   *       alerts,\r\n   *       todoById\r\n   *     }\r\n   *   }\r\n   * )\r\n   * ```\r\n   *\r\n   * @example\r\n   * <caption>In your component:</caption>\r\n   * ```tsx\r\n   * import type { RootState } from 'createStructuredSelector/modernUseCase'\r\n   * import { structuredSelector } from 'createStructuredSelector/modernUseCase'\r\n   * import type { FC } from 'react'\r\n   * import { useSelector } from 'react-redux'\r\n   *\r\n   * interface Props {\r\n   *   id: number\r\n   * }\r\n   *\r\n   * const MyComponent: FC<Props> = ({ id }) => {\r\n   *   const { todos, alerts, todoById } = useSelector((state: RootState) =>\r\n   *     structuredSelector(state, id)\r\n   *   )\r\n   *\r\n   *   return (\r\n   *     <div>\r\n   *       Next to do is:\r\n   *       <h2>{todoById.title}</h2>\r\n   *       <p>Description: {todoById.description}</p>\r\n   *       <ul>\r\n   *         <h3>All other to dos:</h3>\r\n   *         {todos.map(todo => (\r\n   *           <li key={todo.id}>{todo.title}</li>\r\n   *         ))}\r\n   *       </ul>\r\n   *     </div>\r\n   *   )\r\n   * }\r\n   * ```\r\n   *\r\n   * @example\r\n   * <caption>Simple Use Case</caption>\r\n   * ```ts\r\n   * const selectA = state => state.a\r\n   * const selectB = state => state.b\r\n   *\r\n   * // The result function in the following selector\r\n   * // is simply building an object from the input selectors\r\n   * const structuredSelector = createSelector(selectA, selectB, (a, b) => ({\r\n   *   a,\r\n   *   b\r\n   * }))\r\n   *\r\n   * const result = structuredSelector({ a: 1, b: 2 }) // will produce { x: 1, y: 2 }\r\n   * ```\r\n   *\r\n   * @template InputSelectorsObject - The shape of the input selectors object.\r\n   * @template MemoizeFunction - The type of the memoize function that is used to create the structured selector. It defaults to `weakMapMemoize`.\r\n   * @template ArgsMemoizeFunction - The type of the of the memoize function that is used to memoize the arguments passed into the generated structured selector. It defaults to `weakMapMemoize`.\r\n   *\r\n   * @see {@link https://reselect.js.org/api/createStructuredSelector `createStructuredSelector`}\r\n   */\r\n  <\r\n    InputSelectorsObject extends SelectorsObject<StateType>,\r\n    MemoizeFunction extends UnknownMemoizer = typeof weakMapMemoize,\r\n    ArgsMemoizeFunction extends UnknownMemoizer = typeof weakMapMemoize\r\n  >(\r\n    inputSelectorsObject: InputSelectorsObject,\r\n    selectorCreator?: CreateSelectorFunction<\r\n      MemoizeFunction,\r\n      ArgsMemoizeFunction\r\n    >\r\n  ): OutputSelector<\r\n    ObjectValuesToTuple<InputSelectorsObject>,\r\n    Simplify<SelectorResultsMap<InputSelectorsObject>>,\r\n    MemoizeFunction,\r\n    ArgsMemoizeFunction\r\n  > &\r\n    InterruptRecursion\r\n\r\n  /**\r\n   * Creates a \"pre-typed\" version of\r\n   * {@linkcode createStructuredSelector createStructuredSelector}\r\n   * where the `state` type is predefined.\r\n   *\r\n   * This allows you to set the `state` type once, eliminating the need to\r\n   * specify it with every\r\n   * {@linkcode createStructuredSelector createStructuredSelector} call.\r\n   *\r\n   * @returns A pre-typed `createStructuredSelector` with the state type already defined.\r\n   *\r\n   * @example\r\n   * ```ts\r\n   * import { createStructuredSelector } from 'reselect'\r\n   *\r\n   * export interface RootState {\r\n   *   todos: { id: number; completed: boolean }[]\r\n   *   alerts: { id: number; read: boolean }[]\r\n   * }\r\n   *\r\n   * export const createStructuredAppSelector =\r\n   *   createStructuredSelector.withTypes<RootState>()\r\n   *\r\n   * const structuredAppSelector = createStructuredAppSelector({\r\n   *   // Type of `state` is set to `RootState`, no need to manually set the type\r\n   *   todos: state => state.todos,\r\n   *   alerts: state => state.alerts,\r\n   *   todoById: (state, id: number) => state.todos[id]\r\n   * })\r\n   *\r\n   * ```\r\n   * @template OverrideStateType - The specific type of state used by all structured selectors created with this structured selector creator.\r\n   *\r\n   * @see {@link https://reselect.js.org/api/createstructuredselector#defining-a-pre-typed-createstructuredselector `createSelector.withTypes`}\r\n   *\r\n   * @since 5.1.0\r\n   */\r\n  withTypes: <\r\n    OverrideStateType extends StateType\r\n  >() => StructuredSelectorCreator<OverrideStateType>\r\n}\r\n\r\n/**\r\n * A convenience function that simplifies returning an object\r\n * made up of selector results.\r\n *\r\n * @param inputSelectorsObject - A key value pair consisting of input selectors.\r\n * @param selectorCreator - A custom selector creator function. It defaults to `createSelector`.\r\n * @returns A memoized structured selector.\r\n *\r\n * @example\r\n * <caption>Modern Use Case</caption>\r\n * ```ts\r\n * import { createSelector, createStructuredSelector } from 'reselect'\r\n *\r\n * interface RootState {\r\n *   todos: {\r\n *     id: number\r\n *     completed: boolean\r\n *     title: string\r\n *     description: string\r\n *   }[]\r\n *   alerts: { id: number; read: boolean }[]\r\n * }\r\n *\r\n * // This:\r\n * const structuredSelector = createStructuredSelector(\r\n *   {\r\n *     todos: (state: RootState) => state.todos,\r\n *     alerts: (state: RootState) => state.alerts,\r\n *     todoById: (state: RootState, id: number) => state.todos[id]\r\n *   },\r\n *   createSelector\r\n * )\r\n *\r\n * // Is essentially the same as this:\r\n * const selector = createSelector(\r\n *   [\r\n *     (state: RootState) => state.todos,\r\n *     (state: RootState) => state.alerts,\r\n *     (state: RootState, id: number) => state.todos[id]\r\n *   ],\r\n *   (todos, alerts, todoById) => {\r\n *     return {\r\n *       todos,\r\n *       alerts,\r\n *       todoById\r\n *     }\r\n *   }\r\n * )\r\n * ```\r\n *\r\n * @see {@link https://reselect.js.org/api/createStructuredSelector `createStructuredSelector`}\r\n *\r\n * @public\r\n */\r\nexport const createStructuredSelector: StructuredSelectorCreator =\r\n  Object.assign(\r\n    <\r\n      InputSelectorsObject extends SelectorsObject,\r\n      MemoizeFunction extends UnknownMemoizer = typeof weakMapMemoize,\r\n      ArgsMemoizeFunction extends UnknownMemoizer = typeof weakMapMemoize\r\n    >(\r\n      inputSelectorsObject: InputSelectorsObject,\r\n      selectorCreator: CreateSelectorFunction<\r\n        MemoizeFunction,\r\n        ArgsMemoizeFunction\r\n      > = createSelector as CreateSelectorFunction<\r\n        MemoizeFunction,\r\n        ArgsMemoizeFunction\r\n      >\r\n    ) => {\r\n      assertIsObject(\r\n        inputSelectorsObject,\r\n        'createStructuredSelector expects first argument to be an object ' +\r\n          `where each property is a selector, instead received a ${typeof inputSelectorsObject}`\r\n      )\r\n      const inputSelectorKeys = Object.keys(inputSelectorsObject)\r\n      const dependencies = inputSelectorKeys.map(\r\n        key => inputSelectorsObject[key]\r\n      )\r\n      const structuredSelector = selectorCreator(\r\n        dependencies,\r\n        (...inputSelectorResults: any[]) => {\r\n          return inputSelectorResults.reduce((composition, value, index) => {\r\n            composition[inputSelectorKeys[index]] = value\r\n            return composition\r\n          }, {})\r\n        }\r\n      )\r\n      return structuredSelector\r\n    },\r\n    { withTypes: () => createStructuredSelector }\r\n  ) as StructuredSelectorCreator\r\n"], "mappings": ";AAmBO,IAAMA,wBAAA,GAA2BA,CACtCC,UAAA,EACAC,qBAAA,EACAC,oBAAA,KACG;EACH,IACED,qBAAA,CAAsBE,MAAA,KAAW,KACjCF,qBAAA,CAAsB,CAAC,MAAMC,oBAAA,EAC7B;IACA,IAAIE,mBAAA,GAAsB;IAC1B,IAAI;MACF,MAAMC,WAAA,GAAc,CAAC;MACrB,IAAIL,UAAA,CAAWK,WAAW,MAAMA,WAAA,EAAaD,mBAAA,GAAsB;IACrE,QAAE,CAEF;IACA,IAAIA,mBAAA,EAAqB;MACvB,IAAIE,KAAA,GAA4B;MAChC,IAAI;QACF,MAAM,IAAIC,KAAA,CAAM;MAClB,SAASC,CAAA,EAAP;QAEA;QAAC,CAAC;UAAEF;QAAM,IAAIE,CAAA;MAChB;MACAC,OAAA,CAAQC,IAAA,CACN,mTAIA;QAAEJ;MAAM,CACV;IACF;EACF;AACF;;;ACpCO,IAAMK,sBAAA,GAAyBA,CACpCC,0BAAA,EAIAC,OAAA,EAMAC,iBAAA,KACG;EACH,MAAM;IAAEC,OAAA;IAASC;EAAe,IAAIH,OAAA;EACpC,MAAM;IAAEI,oBAAA;IAAsBC;EAAyB,IACrDN,0BAAA;EACF,MAAMO,mBAAA,GAAsBJ,OAAA,CAAQ,OAAO,CAAC,IAAI,GAAGC,cAAc;EAEjE,MAAMI,4BAAA,GACJD,mBAAA,CAAoBE,KAAA,CAAM,MAAMJ,oBAAoB,MACpDE,mBAAA,CAAoBE,KAAA,CAAM,MAAMH,wBAAwB;EAC1D,IAAI,CAACE,4BAAA,EAA8B;IACjC,IAAId,KAAA,GAA4B;IAChC,IAAI;MACF,MAAM,IAAIC,KAAA,CAAM;IAClB,SAASC,CAAA,EAAP;MAEA;MAAC,CAAC;QAAEF;MAAM,IAAIE,CAAA;IAChB;IACAC,OAAA,CAAQC,IAAA,CACN,yTAIA;MACEY,SAAA,EAAWR,iBAAA;MACXS,WAAA,EAAaN,oBAAA;MACbO,YAAA,EAAcN,wBAAA;MACdZ;IACF,CACF;EACF;AACF;;;ACjDO,IAAMmB,mBAAA,GAAqC;EAChDC,mBAAA,EAAqB;EACrBC,qBAAA,EAAuB;AACzB;AA8CO,IAAMC,sBAAA,GACXC,aAAA,IACG;EACHC,MAAA,CAAOC,MAAA,CAAON,mBAAA,EAAqBI,aAAa;AAClD;;;ACnDO,IAAMG,SAAA,GAA4B,eAAAC,MAAA,CAAO,WAAW;AAWpD,SAASC,iBACdC,IAAA,EACAC,YAAA,GAAe,yCAAyC,OAAOD,IAAA,IACjC;EAC9B,IAAI,OAAOA,IAAA,KAAS,YAAY;IAC9B,MAAM,IAAIE,SAAA,CAAUD,YAAY;EAClC;AACF;AAUO,SAASE,eACdC,MAAA,EACAH,YAAA,GAAe,wCAAwC,OAAOG,MAAA,IAChC;EAC9B,IAAI,OAAOA,MAAA,KAAW,UAAU;IAC9B,MAAM,IAAIF,SAAA,CAAUD,YAAY;EAClC;AACF;AAUO,SAASI,yBACdC,KAAA,EACAL,YAAA,GAAe,8EACkB;EACjC,IACE,CAACK,KAAA,CAAMC,KAAA,CAAOC,IAAA,IAA+B,OAAOA,IAAA,KAAS,UAAU,GACvE;IACA,MAAMC,SAAA,GAAYH,KAAA,CACfI,GAAA,CAAIF,IAAA,IACH,OAAOA,IAAA,KAAS,aACZ,YAAYA,IAAA,CAAKG,IAAA,IAAQ,gBACzB,OAAOH,IACb,EACCI,IAAA,CAAK,IAAI;IACZ,MAAM,IAAIV,SAAA,CAAU,GAAGD,YAAA,IAAgBQ,SAAA,GAAY;EACrD;AACF;AASO,IAAMI,aAAA,GAAiBL,IAAA,IAAkB;EAC9C,OAAOM,KAAA,CAAMC,OAAA,CAAQP,IAAI,IAAIA,IAAA,GAAO,CAACA,IAAI;AAC3C;AASO,SAASQ,gBAAgBC,kBAAA,EAA+B;EAC7D,MAAMC,YAAA,GAAeJ,KAAA,CAAMC,OAAA,CAAQE,kBAAA,CAAmB,CAAC,CAAC,IACpDA,kBAAA,CAAmB,CAAC,IACpBA,kBAAA;EAEJZ,wBAAA,CACEa,YAAA,EACA,gGACF;EAEA,OAAOA,YAAA;AACT;AASO,SAASC,4BACdD,YAAA,EACAvC,iBAAA,EACA;EACA,MAAMG,oBAAA,GAAuB,EAAC;EAC9B,MAAM;IAAEd;EAAO,IAAIkD,YAAA;EACnB,SAASE,CAAA,GAAI,GAAGA,CAAA,GAAIpD,MAAA,EAAQoD,CAAA,IAAK;IAG/BtC,oBAAA,CAAqBuC,IAAA,CAAKH,YAAA,CAAaE,CAAC,EAAElC,KAAA,CAAM,MAAMP,iBAAiB,CAAC;EAC1E;EACA,OAAOG,oBAAA;AACT;AASO,IAAMwC,6BAAA,GAAgCA,CAC3CC,QAAA,EACA7B,aAAA,KACG;EACH,MAAM;IAAEF,qBAAA;IAAuBD;EAAoB,IAAI;IACrD,GAAGD,mBAAA;IACH,GAAGI;EACL;EACA,OAAO;IACLF,qBAAA,EAAuB;MACrBgC,SAAA,EACEhC,qBAAA,KAA0B,YACzBA,qBAAA,KAA0B,UAAU+B,QAAA;MACvCE,GAAA,EAAK7D;IACP;IACA2B,mBAAA,EAAqB;MACnBiC,SAAA,EACEjC,mBAAA,KAAwB,YACvBA,mBAAA,KAAwB,UAAUgC,QAAA;MACrCE,GAAA,EAAKjD;IACP;EACF;AACF;;;AClJO,IAAIkD,SAAA,GAAY;AAKvB,IAAIC,eAAA,GAAyD;AAGtD,IAAMC,IAAA,GAAN,MAAc;EACnBC,QAAA,GAAWH,SAAA;EAEXI,MAAA;EACAC,UAAA;EACAC,QAAA,GAAuBC,QAAA;EAEvBC,YAAYC,YAAA,EAAiBC,OAAA,GAAsBH,QAAA,EAAU;IAC3D,KAAKH,MAAA,GAAS,KAAKC,UAAA,GAAaI,YAAA;IAChC,KAAKH,QAAA,GAAWI,OAAA;EAClB;EAAA;EAAA;EAIA,IAAIC,MAAA,EAAQ;IACVV,eAAA,EAAiBW,GAAA,CAAI,IAAI;IAEzB,OAAO,KAAKR,MAAA;EACd;EAAA;EAAA;EAAA;EAAA;EAAA;EAOA,IAAIO,MAAME,QAAA,EAAU;IAClB,IAAI,KAAKF,KAAA,KAAUE,QAAA,EAAU;IAE7B,KAAKT,MAAA,GAASS,QAAA;IACd,KAAKV,QAAA,GAAW,EAAEH,SAAA;EACpB;AACF;AAEA,SAASO,SAASO,CAAA,EAAYC,CAAA,EAAY;EACxC,OAAOD,CAAA,KAAMC,CAAA;AACf;AAMO,IAAMC,aAAA,GAAN,MAAoB;EACzBC,YAAA;EACAC,eAAA,GAAkB;EAClBC,KAAA,GAAe,EAAC;EAChBC,IAAA,GAAO;EAEPC,EAAA;EAEAb,YAAYa,EAAA,EAAe;IACzB,KAAKA,EAAA,GAAKA,EAAA;EACZ;EAEAC,MAAA,EAAQ;IACN,KAAKL,YAAA,GAAe;IACpB,KAAKC,eAAA,GAAkB;IACvB,KAAKC,KAAA,GAAQ,EAAC;IACd,KAAKC,IAAA,GAAO;EACd;EAEA,IAAIT,MAAA,EAAQ;IAIV,IAAI,KAAKR,QAAA,GAAW,KAAKe,eAAA,EAAiB;MACxC,MAAM;QAAEG;MAAG,IAAI;MAMf,MAAME,cAAA,GAAiB,mBAAIC,GAAA,CAAe;MAC1C,MAAMC,WAAA,GAAcxB,eAAA;MAEpBA,eAAA,GAAkBsB,cAAA;MAGlB,KAAKN,YAAA,GAAeI,EAAA,CAAG;MAEvBpB,eAAA,GAAkBwB,WAAA;MAClB,KAAKL,IAAA;MACL,KAAKD,KAAA,GAAQ/B,KAAA,CAAMsC,IAAA,CAAKH,cAAc;MAKtC,KAAKL,eAAA,GAAkB,KAAKf,QAAA;IAE9B;IAIAF,eAAA,EAAiBW,GAAA,CAAI,IAAI;IAGzB,OAAO,KAAKK,YAAA;EACd;EAEA,IAAId,SAAA,EAAW;IAEb,OAAOwB,IAAA,CAAKC,GAAA,CAAI,GAAG,KAAKT,KAAA,CAAMnC,GAAA,CAAI6C,CAAA,IAAKA,CAAA,CAAE1B,QAAQ,GAAG,CAAC;EACvD;AACF;AAEO,SAAS2B,SAAYC,IAAA,EAAkB;EAC5C,IAAI,EAAEA,IAAA,YAAgB7B,IAAA,GAAO;IAC3BtD,OAAA,CAAQC,IAAA,CAAK,sBAAsBkF,IAAI;EACzC;EAEA,OAAOA,IAAA,CAAKpB,KAAA;AACd;AAIO,SAASqB,SACdC,OAAA,EACAtB,KAAA,EACM;EACN,IAAI,EAAEsB,OAAA,YAAmB/B,IAAA,GAAO;IAC9B,MAAM,IAAI1B,SAAA,CACR,uEACF;EACF;EAEAyD,OAAA,CAAQtB,KAAA,GAAQsB,OAAA,CAAQ5B,UAAA,GAAaM,KAAA;AACvC;AAEO,SAASuB,WACdzB,YAAA,EACAC,OAAA,GAAsBH,QAAA,EACb;EACT,OAAO,IAAIL,IAAA,CAAKO,YAAA,EAAcC,OAAO;AACvC;AAEO,SAASyB,YAAyBd,EAAA,EAA4B;EACnEhD,gBAAA,CACEgD,EAAA,EACA,yDACF;EAEA,OAAO,IAAIL,aAAA,CAAcK,EAAE;AAC7B;;;ACrJA,IAAMe,OAAA,GAAUA,CAACtB,CAAA,EAAQC,CAAA,KAAoB;AAEtC,SAASsB,UAAA,EAAiB;EAC/B,OAAOH,UAAA,CAAc,MAAME,OAAO;AACpC;AAEO,SAASE,SAASC,GAAA,EAAU5B,KAAA,EAAkB;EACnDqB,QAAA,CAASO,GAAA,EAAK5B,KAAK;AACrB;AAgBO,IAAM6B,iBAAA,GAAqBC,IAAA,IAAqB;EACrD,IAAIF,GAAA,GAAME,IAAA,CAAKC,aAAA;EAEf,IAAIH,GAAA,KAAQ,MAAM;IAChBA,GAAA,GAAME,IAAA,CAAKC,aAAA,GAAgBL,SAAA,CAAU;EACvC;EAEAP,QAAA,CAAWS,GAAG;AAChB;AAEO,IAAMI,eAAA,GAAmBF,IAAA,IAAqB;EACnD,MAAMF,GAAA,GAAME,IAAA,CAAKC,aAAA;EAEjB,IAAIH,GAAA,KAAQ,MAAM;IAChBD,QAAA,CAASC,GAAA,EAAK,IAAI;EACpB;AACF;;;ACrCO,IAAMK,iBAAA,GAAoBxE,MAAA,CAAO;AAExC,IAAIyE,MAAA,GAAS;AAEb,IAAMC,KAAA,GAAQ7E,MAAA,CAAO8E,cAAA,CAAe,CAAC,CAAC;AAEtC,IAAMC,cAAA,GAAN,MAA2E;EAQzExC,YAAmBG,KAAA,EAAU;IAAV,KAAAA,KAAA,GAAAA,KAAA;IACjB,KAAKA,KAAA,GAAQA,KAAA;IACb,KAAK4B,GAAA,CAAI5B,KAAA,GAAQA,KAAA;EACnB;EAVAsC,KAAA,GAAW,IAAIC,KAAA,CAAM,MAAMC,kBAAkB;EAC7CZ,GAAA,GAAMF,SAAA,CAAU;EAChBe,IAAA,GAAO,CAAC;EACRC,QAAA,GAAW,CAAC;EACZX,aAAA,GAAgB;EAChBY,EAAA,GAAKT,MAAA;AAMP;AAEA,IAAMM,kBAAA,GAAqB;EACzBI,IAAId,IAAA,EAAYe,GAAA,EAA+B;IAC7C,SAASC,gBAAA,EAAkB;MACzB,MAAM;QAAE9C;MAAM,IAAI8B,IAAA;MAElB,MAAMiB,UAAA,GAAaC,OAAA,CAAQJ,GAAA,CAAI5C,KAAA,EAAO6C,GAAG;MAEzC,IAAI,OAAOA,GAAA,KAAQ,UAAU;QAC3B,OAAOE,UAAA;MACT;MAEA,IAAIF,GAAA,IAAOV,KAAA,EAAO;QAChB,OAAOY,UAAA;MACT;MAEA,IAAI,OAAOA,UAAA,KAAe,YAAYA,UAAA,KAAe,MAAM;QACzD,IAAIE,SAAA,GAAYnB,IAAA,CAAKY,QAAA,CAASG,GAAG;QAEjC,IAAII,SAAA,KAAc,QAAW;UAC3BA,SAAA,GAAYnB,IAAA,CAAKY,QAAA,CAASG,GAAG,IAAIK,UAAA,CAAWH,UAAU;QACxD;QAEA,IAAIE,SAAA,CAAUrB,GAAA,EAAK;UACjBT,QAAA,CAAW8B,SAAA,CAAUrB,GAAG;QAC1B;QAEA,OAAOqB,SAAA,CAAUX,KAAA;MACnB,OAAO;QACL,IAAIV,GAAA,GAAME,IAAA,CAAKW,IAAA,CAAKI,GAAG;QAEvB,IAAIjB,GAAA,KAAQ,QAAW;UACrBA,GAAA,GAAME,IAAA,CAAKW,IAAA,CAAKI,GAAG,IAAInB,SAAA,CAAU;UACjCE,GAAA,CAAI5B,KAAA,GAAQ+C,UAAA;QACd;QAEA5B,QAAA,CAAWS,GAAG;QAEd,OAAOmB,UAAA;MACT;IACF;IACA,MAAMI,GAAA,GAAML,eAAA,CAAgB;IAC5B,OAAOK,GAAA;EACT;EAEAC,QAAQtB,IAAA,EAAwC;IAC9CD,iBAAA,CAAkBC,IAAI;IACtB,OAAOkB,OAAA,CAAQI,OAAA,CAAQtB,IAAA,CAAK9B,KAAK;EACnC;EAEAqD,yBACEvB,IAAA,EACAwB,IAAA,EACgC;IAChC,OAAON,OAAA,CAAQK,wBAAA,CAAyBvB,IAAA,CAAK9B,KAAA,EAAOsD,IAAI;EAC1D;EAEAC,IAAIzB,IAAA,EAAYwB,IAAA,EAAgC;IAC9C,OAAON,OAAA,CAAQO,GAAA,CAAIzB,IAAA,CAAK9B,KAAA,EAAOsD,IAAI;EACrC;AACF;AAEA,IAAME,aAAA,GAAN,MAAiE;EAQ/D3D,YAAmBG,KAAA,EAAU;IAAV,KAAAA,KAAA,GAAAA,KAAA;IACjB,KAAKA,KAAA,GAAQA,KAAA;IACb,KAAK4B,GAAA,CAAI5B,KAAA,GAAQA,KAAA;EACnB;EAVAsC,KAAA,GAAW,IAAIC,KAAA,CAAM,CAAC,IAAI,GAAGkB,iBAAiB;EAC9C7B,GAAA,GAAMF,SAAA,CAAU;EAChBe,IAAA,GAAO,CAAC;EACRC,QAAA,GAAW,CAAC;EACZX,aAAA,GAAgB;EAChBY,EAAA,GAAKT,MAAA;AAMP;AAEA,IAAMuB,iBAAA,GAAoB;EACxBb,IAAI,CAACd,IAAI,GAAWe,GAAA,EAA+B;IACjD,IAAIA,GAAA,KAAQ,UAAU;MACpBhB,iBAAA,CAAkBC,IAAI;IACxB;IAEA,OAAOU,kBAAA,CAAmBI,GAAA,CAAId,IAAA,EAAMe,GAAG;EACzC;EAEAO,QAAQ,CAACtB,IAAI,GAAuC;IAClD,OAAOU,kBAAA,CAAmBY,OAAA,CAAQtB,IAAI;EACxC;EAEAuB,yBACE,CAACvB,IAAI,GACLwB,IAAA,EACgC;IAChC,OAAOd,kBAAA,CAAmBa,wBAAA,CAAyBvB,IAAA,EAAMwB,IAAI;EAC/D;EAEAC,IAAI,CAACzB,IAAI,GAAWwB,IAAA,EAAgC;IAClD,OAAOd,kBAAA,CAAmBe,GAAA,CAAIzB,IAAA,EAAMwB,IAAI;EAC1C;AACF;AAEO,SAASJ,WACdlD,KAAA,EACS;EACT,IAAIvB,KAAA,CAAMC,OAAA,CAAQsB,KAAK,GAAG;IACxB,OAAO,IAAIwD,aAAA,CAAcxD,KAAK;EAChC;EAEA,OAAO,IAAIqC,cAAA,CAAerC,KAAK;AACjC;AAOO,SAAS0D,WACd5B,IAAA,EACA5B,QAAA,EACM;EACN,MAAM;IAAEF,KAAA;IAAOyC,IAAA;IAAMC;EAAS,IAAIZ,IAAA;EAElCA,IAAA,CAAK9B,KAAA,GAAQE,QAAA;EAEb,IACEzB,KAAA,CAAMC,OAAA,CAAQsB,KAAK,KACnBvB,KAAA,CAAMC,OAAA,CAAQwB,QAAQ,KACtBF,KAAA,CAAMrE,MAAA,KAAWuE,QAAA,CAASvE,MAAA,EAC1B;IACAqG,eAAA,CAAgBF,IAAI;EACtB,OAAO;IACL,IAAI9B,KAAA,KAAUE,QAAA,EAAU;MACtB,IAAIyD,WAAA,GAAc;MAClB,IAAIC,WAAA,GAAc;MAClB,IAAIC,YAAA,GAAe;MAEnB,WAAWC,IAAA,IAAQ9D,KAAA,EAAO;QACxB2D,WAAA;MACF;MAEA,WAAWd,GAAA,IAAO3C,QAAA,EAAU;QAC1B0D,WAAA;QACA,IAAI,EAAEf,GAAA,IAAO7C,KAAA,GAAQ;UACnB6D,YAAA,GAAe;UACf;QACF;MACF;MAEA,MAAME,WAAA,GAAcF,YAAA,IAAgBF,WAAA,KAAgBC,WAAA;MAEpD,IAAIG,WAAA,EAAa;QACf/B,eAAA,CAAgBF,IAAI;MACtB;IACF;EACF;EAEA,WAAWe,GAAA,IAAOJ,IAAA,EAAM;IACtB,MAAMM,UAAA,GAAc/C,KAAA,CAAkC6C,GAAG;IACzD,MAAMmB,aAAA,GAAiB9D,QAAA,CAAqC2C,GAAG;IAE/D,IAAIE,UAAA,KAAeiB,aAAA,EAAe;MAChChC,eAAA,CAAgBF,IAAI;MACpBH,QAAA,CAASc,IAAA,CAAKI,GAAG,GAAGmB,aAAa;IACnC;IAEA,IAAI,OAAOA,aAAA,KAAkB,YAAYA,aAAA,KAAkB,MAAM;MAC/D,OAAOvB,IAAA,CAAKI,GAAG;IACjB;EACF;EAEA,WAAWA,GAAA,IAAOH,QAAA,EAAU;IAC1B,MAAMO,SAAA,GAAYP,QAAA,CAASG,GAAG;IAC9B,MAAMmB,aAAA,GAAiB9D,QAAA,CAAqC2C,GAAG;IAE/D,MAAME,UAAA,GAAaE,SAAA,CAAUjD,KAAA;IAE7B,IAAI+C,UAAA,KAAeiB,aAAA,EAAe;MAChC;IACF,WAAW,OAAOA,aAAA,KAAkB,YAAYA,aAAA,KAAkB,MAAM;MACtEN,UAAA,CAAWT,SAAA,EAAWe,aAAwC;IAChE,OAAO;MACLC,UAAA,CAAWhB,SAAS;MACpB,OAAOP,QAAA,CAASG,GAAG;IACrB;EACF;AACF;AAEA,SAASoB,WAAWnC,IAAA,EAAkB;EACpC,IAAIA,IAAA,CAAKF,GAAA,EAAK;IACZD,QAAA,CAASG,IAAA,CAAKF,GAAA,EAAK,IAAI;EACzB;EACAI,eAAA,CAAgBF,IAAI;EACpB,WAAWe,GAAA,IAAOf,IAAA,CAAKW,IAAA,EAAM;IAC3Bd,QAAA,CAASG,IAAA,CAAKW,IAAA,CAAKI,GAAG,GAAG,IAAI;EAC/B;EACA,WAAWA,GAAA,IAAOf,IAAA,CAAKY,QAAA,EAAU;IAC/BuB,UAAA,CAAWnC,IAAA,CAAKY,QAAA,CAASG,GAAG,CAAC;EAC/B;AACF;;;AC5MA,SAASqB,qBAAqBC,MAAA,EAA2B;EACvD,IAAIC,KAAA;EACJ,OAAO;IACLxB,IAAIC,GAAA,EAAc;MAChB,IAAIuB,KAAA,IAASD,MAAA,CAAOC,KAAA,CAAMvB,GAAA,EAAKA,GAAG,GAAG;QACnC,OAAOuB,KAAA,CAAMpE,KAAA;MACf;MAEA,OAAOxC,SAAA;IACT;IAEA6G,IAAIxB,GAAA,EAAc7C,KAAA,EAAgB;MAChCoE,KAAA,GAAQ;QAAEvB,GAAA;QAAK7C;MAAM;IACvB;IAEAsE,WAAA,EAAa;MACX,OAAOF,KAAA,GAAQ,CAACA,KAAK,IAAI,EAAC;IAC5B;IAEAzD,MAAA,EAAQ;MACNyD,KAAA,GAAQ;IACV;EACF;AACF;AAEA,SAASG,eAAeC,OAAA,EAAiBL,MAAA,EAA2B;EAClE,IAAIM,OAAA,GAAmB,EAAC;EAExB,SAAS7B,IAAIC,GAAA,EAAc;IACzB,MAAM6B,UAAA,GAAaD,OAAA,CAAQE,SAAA,CAAUP,KAAA,IAASD,MAAA,CAAOtB,GAAA,EAAKuB,KAAA,CAAMvB,GAAG,CAAC;IAGpE,IAAI6B,UAAA,GAAa,IAAI;MACnB,MAAMN,KAAA,GAAQK,OAAA,CAAQC,UAAU;MAGhC,IAAIA,UAAA,GAAa,GAAG;QAClBD,OAAA,CAAQG,MAAA,CAAOF,UAAA,EAAY,CAAC;QAC5BD,OAAA,CAAQI,OAAA,CAAQT,KAAK;MACvB;MAEA,OAAOA,KAAA,CAAMpE,KAAA;IACf;IAGA,OAAOxC,SAAA;EACT;EAEA,SAAS6G,IAAIxB,GAAA,EAAc7C,KAAA,EAAgB;IACzC,IAAI4C,GAAA,CAAIC,GAAG,MAAMrF,SAAA,EAAW;MAE1BiH,OAAA,CAAQI,OAAA,CAAQ;QAAEhC,GAAA;QAAK7C;MAAM,CAAC;MAC9B,IAAIyE,OAAA,CAAQ9I,MAAA,GAAS6I,OAAA,EAAS;QAC5BC,OAAA,CAAQK,GAAA,CAAI;MACd;IACF;EACF;EAEA,SAASR,WAAA,EAAa;IACpB,OAAOG,OAAA;EACT;EAEA,SAAS9D,MAAA,EAAQ;IACf8D,OAAA,GAAU,EAAC;EACb;EAEA,OAAO;IAAE7B,GAAA;IAAKyB,GAAA;IAAKC,UAAA;IAAY3D;EAAM;AACvC;AAUO,IAAMoE,sBAAA,GAAqCA,CAAC5E,CAAA,EAAGC,CAAA,KAAMD,CAAA,KAAMC,CAAA;AAE3D,SAAS4E,yBAAyBC,aAAA,EAA2B;EAClE,OAAO,SAASC,2BACdC,IAAA,EACAC,IAAA,EACS;IACT,IAAID,IAAA,KAAS,QAAQC,IAAA,KAAS,QAAQD,IAAA,CAAKxJ,MAAA,KAAWyJ,IAAA,CAAKzJ,MAAA,EAAQ;MACjE,OAAO;IACT;IAGA,MAAM;MAAEA;IAAO,IAAIwJ,IAAA;IACnB,SAASpG,CAAA,GAAI,GAAGA,CAAA,GAAIpD,MAAA,EAAQoD,CAAA,IAAK;MAC/B,IAAI,CAACkG,aAAA,CAAcE,IAAA,CAAKpG,CAAC,GAAGqG,IAAA,CAAKrG,CAAC,CAAC,GAAG;QACpC,OAAO;MACT;IACF;IAEA,OAAO;EACT;AACF;AAgEO,SAASsG,WACd1H,IAAA,EACA2H,sBAAA,EACA;EACA,MAAMC,eAAA,GACJ,OAAOD,sBAAA,KAA2B,WAC9BA,sBAAA,GACA;IAAEL,aAAA,EAAeK;EAAuB;EAE9C,MAAM;IACJL,aAAA,GAAgBF,sBAAA;IAChBP,OAAA,GAAU;IACVgB;EACF,IAAID,eAAA;EAEJ,MAAME,UAAA,GAAaT,wBAAA,CAAyBC,aAAa;EAEzD,IAAIS,YAAA,GAAe;EAEnB,MAAMC,KAAA,GACJnB,OAAA,IAAW,IACPN,oBAAA,CAAqBuB,UAAU,IAC/BlB,cAAA,CAAeC,OAAA,EAASiB,UAAU;EAExC,SAASG,SAAA,EAAW;IAClB,IAAI5F,KAAA,GAAQ2F,KAAA,CAAM/C,GAAA,CAAI9F,SAAS;IAC/B,IAAIkD,KAAA,KAAUxC,SAAA,EAAW;MAGvBwC,KAAA,GAAQrC,IAAA,CAAKd,KAAA,CAAM,MAAMC,SAAS;MAClC4I,YAAA;MAEA,IAAIF,mBAAA,EAAqB;QACvB,MAAMf,OAAA,GAAUkB,KAAA,CAAMrB,UAAA,CAAW;QACjC,MAAMuB,aAAA,GAAgBpB,OAAA,CAAQqB,IAAA,CAAK1B,KAAA,IACjCoB,mBAAA,CAAoBpB,KAAA,CAAMpE,KAAA,EAA2BA,KAAK,CAC5D;QAEA,IAAI6F,aAAA,EAAe;UACjB7F,KAAA,GAAQ6F,aAAA,CAAc7F,KAAA;UACtB0F,YAAA,KAAiB,KAAKA,YAAA;QACxB;MACF;MAEAC,KAAA,CAAMtB,GAAA,CAAIvH,SAAA,EAAWkD,KAAK;IAC5B;IACA,OAAOA,KAAA;EACT;EAEA4F,QAAA,CAASG,UAAA,GAAa,MAAM;IAC1BJ,KAAA,CAAMhF,KAAA,CAAM;IACZiF,QAAA,CAASI,iBAAA,CAAkB;EAC7B;EAEAJ,QAAA,CAASF,YAAA,GAAe,MAAMA,YAAA;EAE9BE,QAAA,CAASI,iBAAA,GAAoB,MAAM;IACjCN,YAAA,GAAe;EACjB;EAEA,OAAOE,QAAA;AACT;;;AClLO,SAASK,iBAA2CtI,IAAA,EAAY;EAGrE,MAAMmE,IAAA,GAAsCoB,UAAA,CAC1C,EACF;EAEA,IAAIgD,QAAA,GAA8B;EAElC,MAAMC,YAAA,GAAenB,wBAAA,CAAyBD,sBAAsB;EAEpE,MAAMY,KAAA,GAAQnE,WAAA,CAAY,MAAM;IAC9B,MAAM2B,GAAA,GAAMxF,IAAA,CAAKd,KAAA,CAAM,MAAMiF,IAAA,CAAKQ,KAAyB;IAC3D,OAAOa,GAAA;EACT,CAAC;EAED,SAASyC,SAAA,EAAW;IAClB,IAAI,CAACO,YAAA,CAAaD,QAAA,EAAUpJ,SAAS,GAAG;MACtC4G,UAAA,CAAW5B,IAAA,EAAMhF,SAA+C;MAChEoJ,QAAA,GAAWpJ,SAAA;IACb;IACA,OAAO6I,KAAA,CAAM3F,KAAA;EACf;EAEA4F,QAAA,CAASG,UAAA,GAAa,MAAM;IAC1B,OAAOJ,KAAA,CAAMhF,KAAA,CAAM;EACrB;EAEA,OAAOiF,QAAA;AACT;;;ACzFA,IAAMQ,SAAA,GAAN,MAAmB;EACjBvG,YAAoBG,KAAA,EAAU;IAAV,KAAAA,KAAA,GAAAA,KAAA;EAAW;EAC/BqG,MAAA,EAAQ;IACN,OAAO,KAAKrG,KAAA;EACd;AACF;AAEA,IAAMsG,GAAA,GACJ,OAAOC,OAAA,KAAY,cACfA,OAAA,GACCH,SAAA;AAEP,IAAMI,YAAA,GAAe;AACrB,IAAMC,UAAA,GAAa;AA0CnB,SAASC,gBAAA,EAAmC;EAC1C,OAAO;IACLC,CAAA,EAAGH,YAAA;IACHI,CAAA,EAAG;IACHC,CAAA,EAAG;IACHC,CAAA,EAAG;EACL;AACF;AAmGO,SAASC,eACdpJ,IAAA,EACAtB,OAAA,GAAmD,CAAC,GACpD;EACA,IAAI2K,MAAA,GAASN,eAAA,CAAgB;EAC7B,MAAM;IAAElB;EAAoB,IAAInJ,OAAA;EAEhC,IAAI4K,UAAA;EAEJ,IAAIvB,YAAA,GAAe;EAEnB,SAASE,SAAA,EAAW;IAClB,IAAIsB,SAAA,GAAYF,MAAA;IAChB,MAAM;MAAErL;IAAO,IAAImB,SAAA;IACnB,SAASiC,CAAA,GAAI,GAAGoI,CAAA,GAAIxL,MAAA,EAAQoD,CAAA,GAAIoI,CAAA,EAAGpI,CAAA,IAAK;MACtC,MAAMqI,GAAA,GAAMtK,SAAA,CAAUiC,CAAC;MACvB,IACE,OAAOqI,GAAA,KAAQ,cACd,OAAOA,GAAA,KAAQ,YAAYA,GAAA,KAAQ,MACpC;QAEA,IAAIC,WAAA,GAAcH,SAAA,CAAUL,CAAA;QAC5B,IAAIQ,WAAA,KAAgB,MAAM;UACxBH,SAAA,CAAUL,CAAA,GAAIQ,WAAA,GAAc,mBAAIC,OAAA,CAAQ;QAC1C;QACA,MAAMC,UAAA,GAAaF,WAAA,CAAYzE,GAAA,CAAIwE,GAAG;QACtC,IAAIG,UAAA,KAAe,QAAW;UAC5BL,SAAA,GAAYR,eAAA,CAAgB;UAC5BW,WAAA,CAAYG,GAAA,CAAIJ,GAAA,EAAKF,SAAS;QAChC,OAAO;UACLA,SAAA,GAAYK,UAAA;QACd;MACF,OAAO;QAEL,IAAIE,cAAA,GAAiBP,SAAA,CAAUJ,CAAA;QAC/B,IAAIW,cAAA,KAAmB,MAAM;UAC3BP,SAAA,CAAUJ,CAAA,GAAIW,cAAA,GAAiB,mBAAIC,GAAA,CAAI;QACzC;QACA,MAAMC,aAAA,GAAgBF,cAAA,CAAe7E,GAAA,CAAIwE,GAAG;QAC5C,IAAIO,aAAA,KAAkB,QAAW;UAC/BT,SAAA,GAAYR,eAAA,CAAgB;UAC5Be,cAAA,CAAeD,GAAA,CAAIJ,GAAA,EAAKF,SAAS;QACnC,OAAO;UACLA,SAAA,GAAYS,aAAA;QACd;MACF;IACF;IAEA,MAAMC,cAAA,GAAiBV,SAAA;IAEvB,IAAIW,MAAA;IAEJ,IAAIX,SAAA,CAAUP,CAAA,KAAMF,UAAA,EAAY;MAC9BoB,MAAA,GAASX,SAAA,CAAUN,CAAA;IACrB,OAAO;MAELiB,MAAA,GAASlK,IAAA,CAAKd,KAAA,CAAM,MAAMC,SAA6B;MACvD4I,YAAA;MAEA,IAAIF,mBAAA,EAAqB;QACvB,MAAMsC,eAAA,GAAkBb,UAAA,EAAYZ,KAAA,GAAQ,KAAKY,UAAA;QAEjD,IACEa,eAAA,IAAmB,QACnBtC,mBAAA,CAAoBsC,eAAA,EAAqCD,MAAM,GAC/D;UACAA,MAAA,GAASC,eAAA;UAETpC,YAAA,KAAiB,KAAKA,YAAA;QACxB;QAEA,MAAMqC,YAAA,GACH,OAAOF,MAAA,KAAW,YAAYA,MAAA,KAAW,QAC1C,OAAOA,MAAA,KAAW;QAEpBZ,UAAA,GAAac,YAAA,GAAe,IAAIzB,GAAA,CAAIuB,MAAM,IAAIA,MAAA;MAChD;IACF;IAEAD,cAAA,CAAejB,CAAA,GAAIF,UAAA;IAEnBmB,cAAA,CAAehB,CAAA,GAAIiB,MAAA;IACnB,OAAOA,MAAA;EACT;EAEAjC,QAAA,CAASG,UAAA,GAAa,MAAM;IAC1BiB,MAAA,GAASN,eAAA,CAAgB;IACzBd,QAAA,CAASI,iBAAA,CAAkB;EAC7B;EAEAJ,QAAA,CAASF,YAAA,GAAe,MAAMA,YAAA;EAE9BE,QAAA,CAASI,iBAAA,GAAoB,MAAM;IACjCN,YAAA,GAAe;EACjB;EAEA,OAAOE,QAAA;AACT;;;ACaO,SAASoC,sBAUdC,gBAAA,KACGC,sBAAA,EAMH;EAEA,MAAMC,4BAAA,GAGF,OAAOF,gBAAA,KAAqB,aAC5B;IACE1L,OAAA,EAAS0L,gBAAA;IACTzL,cAAA,EAAgB0L;EAClB,IACAD,gBAAA;EAEJ,MAAMG,eAAA,GAAiBC,CAAA,GAMlBzJ,kBAAA,KAUA;IACH,IAAI0J,cAAA,GAAiB;IACrB,IAAIC,wBAAA,GAA2B;IAC/B,IAAItB,UAAA;IAKJ,IAAIuB,qBAAA,GAKA,CAAC;IAGL,IAAIhN,UAAA,GAAaoD,kBAAA,CAAmBkG,GAAA,CAAI;IAUxC,IAAI,OAAOtJ,UAAA,KAAe,UAAU;MAClCgN,qBAAA,GAAwBhN,UAAA;MAExBA,UAAA,GAAaoD,kBAAA,CAAmBkG,GAAA,CAAI;IACtC;IAEApH,gBAAA,CACElC,UAAA,EACA,8EAA8E,OAAOA,UAAA,GACvF;IAIA,MAAMiN,eAAA,GAAkB;MACtB,GAAGN,4BAAA;MACH,GAAGK;IACL;IAEA,MAAM;MACJjM,OAAA;MACAC,cAAA,GAAiB,EAAC;MAClBkM,WAAA,GAAc3B,cAAA;MACd4B,kBAAA,GAAqB,EAAC;MACtBtL,aAAA,GAAgB,CAAC;IACnB,IAAIoL,eAAA;IAOJ,MAAMG,mBAAA,GAAsBpK,aAAA,CAAchC,cAAc;IACxD,MAAMqM,uBAAA,GAA0BrK,aAAA,CAAcmK,kBAAkB;IAChE,MAAM9J,YAAA,GAAeF,eAAA,CAAgBC,kBAAkB;IAEvD,MAAMkK,kBAAA,GAAqBvM,OAAA,CAAQ,SAASwM,qBAAA,EAAuB;MACjET,cAAA;MAGA,OAAQ9M,UAAA,CAAgDqB,KAAA,CACtD,MACAC,SACF;IACF,GAAG,GAAG8L,mBAAmB;IAGzB,IAAI1J,QAAA,GAAW;IAGf,MAAM8J,QAAA,GAAWN,WAAA,CAAY,SAASO,oBAAA,EAAsB;MAC1DV,wBAAA;MAEA,MAAM9L,oBAAA,GAAuBqC,2BAAA,CAC3BD,YAAA,EACA/B,SACF;MAIAmK,UAAA,GAAa6B,kBAAA,CAAmBjM,KAAA,CAAM,MAAMJ,oBAAoB;MAEhE,IAAIyM,OAAA,CAAQC,GAAA,CAAIC,QAAA,KAAa,cAAc;QACzC,MAAM;UAAEjM,qBAAA;UAAuBD;QAAoB,IACjD+B,6BAAA,CAA8BC,QAAA,EAAU7B,aAAa;QACvD,IAAIF,qBAAA,CAAsBgC,SAAA,EAAW;UACnChC,qBAAA,CAAsBiC,GAAA,CACpB5D,UAAA,EACAiB,oBAAA,EACAwK,UACF;QACF;QAEA,IAAI/J,mBAAA,CAAoBiC,SAAA,EAAW;UAEjC,MAAMzC,wBAAA,GAA2BoC,2BAAA,CAC/BD,YAAA,EACA/B,SACF;UAEAI,mBAAA,CAAoBkC,GAAA,CAClB;YAAE3C,oBAAA;YAAsBC;UAAyB,GACjD;YAAEH,OAAA;YAASC,cAAA,EAAgBoM;UAAoB,GAC/C9L,SACF;QACF;QAEA,IAAIoC,QAAA,EAAUA,QAAA,GAAW;MAC3B;MAEA,OAAO+H,UAAA;IACT,GAAG,GAAG4B,uBAAuB;IAO7B,OAAOvL,MAAA,CAAOC,MAAA,CAAOyL,QAAA,EAAU;MAC7BxN,UAAA;MACAsN,kBAAA;MACAjK,YAAA;MACA0J,wBAAA,EAA0BA,CAAA,KAAMA,wBAAA;MAChCc,6BAAA,EAA+BA,CAAA,KAAM;QACnCd,wBAAA,GAA2B;MAC7B;MACAtB,UAAA,EAAYA,CAAA,KAAMA,UAAA;MAClBqB,cAAA,EAAgBA,CAAA,KAAMA,cAAA;MACtBgB,mBAAA,EAAqBA,CAAA,KAAM;QACzBhB,cAAA,GAAiB;MACnB;MACA/L,OAAA;MACAmM;IACF,CAAC;EAMH;EAEApL,MAAA,CAAOC,MAAA,CAAO6K,eAAA,EAAgB;IAC5BmB,SAAA,EAAWA,CAAA,KAAMnB;EACnB,CAAC;EAED,OAAOA,eAAA;AAIT;AAWO,IAAMC,cAAA,GACK,eAAAL,qBAAA,CAAsBjB,cAAc;;;AC5E/C,IAAMyC,wBAAA,GACXlM,MAAA,CAAOC,MAAA,CACL,CAKEkM,oBAAA,EACAC,eAAA,GAGIrB,cAAA,KAID;EACHvK,cAAA,CACE2L,oBAAA,EACA,yHAC2D,OAAOA,oBAAA,EACpE;EACA,MAAME,iBAAA,GAAoBrM,MAAA,CAAOsM,IAAA,CAAKH,oBAAoB;EAC1D,MAAM5K,YAAA,GAAe8K,iBAAA,CAAkBtL,GAAA,CACrCwE,GAAA,IAAO4G,oBAAA,CAAqB5G,GAAG,CACjC;EACA,MAAMgH,kBAAA,GAAqBH,eAAA,CACzB7K,YAAA,EACA,IAAIpC,oBAAA,KAAgC;IAClC,OAAOA,oBAAA,CAAqBqN,MAAA,CAAO,CAACC,WAAA,EAAa/J,KAAA,EAAOgK,KAAA,KAAU;MAChED,WAAA,CAAYJ,iBAAA,CAAkBK,KAAK,CAAC,IAAIhK,KAAA;MACxC,OAAO+J,WAAA;IACT,GAAG,CAAC,CAAC;EACP,CACF;EACA,OAAOF,kBAAA;AACT,GACA;EAAEN,SAAA,EAAWA,CAAA,KAAMC;AAAyB,CAC9C", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}