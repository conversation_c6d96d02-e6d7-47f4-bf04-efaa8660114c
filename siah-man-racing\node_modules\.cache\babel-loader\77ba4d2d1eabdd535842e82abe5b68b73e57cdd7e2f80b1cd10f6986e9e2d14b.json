{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Apps_Creations\\\\SiahMan_Racing4Speed_WebGame\\\\siah-man-racing\\\\src\\\\pages\\\\StoryMode.tsx\",\n  _s = $RefreshSig$();\nimport React, { useEffect, useState } from 'react';\nimport { useNavigate } from 'react-router-dom';\nimport { useDispatch, useSelector } from 'react-redux';\nimport styled, { keyframes } from 'styled-components';\nimport { setCurrentScreen, advanceStory } from '../store/gameSlice';\nimport { advanceChapter, markCutsceneSeen } from '../store/userSlice';\nimport { TTSSystem } from '../game/TTSSystem';\nimport { jsxDEV as _jsxDEV, Fragment as _Fragment } from \"react/jsx-dev-runtime\";\nconst cinematicPan = keyframes`\n  0% { background-position: 0% 50%; }\n  50% { background-position: 100% 50%; }\n  100% { background-position: 0% 50%; }\n`;\nconst StoryContainer = styled.div`\n  width: 100%;\n  height: 100vh;\n  background: linear-gradient(rgba(10, 10, 30, 0.9), rgba(20, 20, 40, 0.9));\n  color: white;\n  display: flex;\n  flex-direction: column;\n  overflow: hidden;\n  position: relative;\n  animation: ${cinematicPan} 90s linear infinite;\n  font-family: 'Exo 2', sans-serif;\n`;\n_c = StoryContainer;\nconst StoryBackground = styled.div`\n  position: absolute;\n  top: 0;\n  left: 0;\n  right: 0;\n  bottom: 0;\n  background: ${props => {\n  switch (props.$chapter) {\n    case 1:\n      return 'linear-gradient(135deg, #1a2a6c, #b21f1f, #fdbb2d)';\n    case 2:\n      return 'linear-gradient(135deg, #0f0c29, #302b63, #24243e)';\n    case 3:\n      return 'linear-gradient(135deg, #2c3e50, #4ca1af)';\n    case 4:\n      return 'linear-gradient(135deg, #8e0e00, #1f1c18)';\n    case 5:\n      return 'linear-gradient(135deg, #000428, #004e92)';\n    default:\n      return 'linear-gradient(135deg, #16222a, #3a6073)';\n  }\n}};\n  opacity: 0.8;\n  z-index: -1;\n`;\n_c2 = StoryBackground;\nconst DialogueBox = styled.div`\n  position: absolute;\n  bottom: 50px;\n  left: 50px;\n  right: 50px;\n  background: rgba(0, 0, 0, 0.8);\n  border-radius: 10px;\n  padding: 20px;\n  box-shadow: 0 0 20px rgba(0, 0, 0, 0.5);\n`;\n_c3 = DialogueBox;\nconst CharacterName = styled.h3`\n  margin: 0 0 10px 0;\n  color: #fdbb2d;\n  font-size: 1.5rem;\n`;\n_c4 = CharacterName;\nconst DialogueText = styled.p`\n  margin: 0;\n  font-size: 1.2rem;\n  line-height: 1.5;\n`;\n_c5 = DialogueText;\nconst CharacterPortrait = styled.div`\n  position: absolute;\n  bottom: 200px;\n  width: 300px;\n  height: 400px;\n  background-color: rgba(0, 0, 0, 0.5);\n  border-radius: 10px;\n  overflow: hidden;\n  \n  ${props => props.$character === 'siah_man' ? 'left: 100px;' : props.$character === 'dr_neal' ? 'left: 100px;' :\n// Assuming Dr. Neal also on left for now\n'right: 100px;'}\n\n  ${props => props.$character === 'siah_man' && `\n    background: url('/assets/Images/Siah_Image.jpeg') no-repeat center center;\n    background-size: cover;\n  `}\n\n  ${props => props.$character === 'dr_neal' && `\n    background: url('/assets/Images/Dr.Neal_SiahsDadImage.jpeg') no-repeat center center;\n    background-size: cover;\n  `}\n\n\n  &:before {\n    content: '';\n    position: absolute;\n    top: 0;\n    left: 0;\n    right: 0;\n    bottom: 0;\n    background: ${props => {\n  switch (props.$character) {\n    case 'siah_man':\n      return 'rgba(0, 0, 0, 0.3)';\n    case 'dr_neal':\n      return 'linear-gradient(to bottom, #1a2a6c, #4ca1af)';\n    case 'max_velocity':\n      return 'linear-gradient(to bottom, #8e0e00, #1f1c18)';\n    default:\n      return 'linear-gradient(to bottom, #16222a, #3a6073)';\n  }\n}};\n    opacity: 0.7;\n    z-index: 1;\n  }\n\n  img {\n    display: block;\n    width: 100%;\n    height: 100%;\n    object-fit: cover;\n    z-index: 2;\n  }\n`;\n_c6 = CharacterPortrait;\nconst ContinueButton = styled.button`\n  position: absolute;\n  bottom: 20px;\n  right: 50px;\n  background: rgba(0, 100, 200, 0.7);\n  color: white;\n  border: none;\n  border-radius: 5px;\n  padding: 10px 20px;\n  cursor: pointer;\n  font-size: 1rem;\n  transition: all 0.2s ease;\n\n  &:hover {\n    background: rgba(0, 150, 250, 0.9);\n  }\n`;\n_c7 = ContinueButton;\nconst BackButton = styled.button`\n  position: absolute;\n  top: 20px;\n  left: 20px;\n  background: rgba(0, 0, 0, 0.5);\n  color: white;\n  border: none;\n  border-radius: 5px;\n  padding: 10px 15px;\n  cursor: pointer;\n  z-index: 10;\n`;\n_c8 = BackButton;\nconst ChapterTitle = styled.h2`\n  position: absolute;\n  top: 50px;\n  left: 0;\n  right: 0;\n  text-align: center;\n  font-size: 2rem;\n  text-transform: uppercase;\n  letter-spacing: 3px;\n  text-shadow: 0 0 10px rgba(0, 0, 0, 0.5);\n`;\n_c9 = ChapterTitle;\nconst storyContent = [[{\n  character: 'dr_neal',\n  name: 'Dr. Neal',\n  text: \"Siah, I've been working on something special in my MAD Laboratory. A formula that could change everything we know about racing.\",\n  audioSrc: '/assets/voices/drneal_story_ch1_line1.mp3'\n}, {\n  character: 'siah_man',\n  name: 'Siah',\n  text: \"Dad, you always say that about your experiments. What makes this one different?\",\n  audioSrc: '/assets/voices/siah_story_ch1_line1.mp3'\n}, {\n  character: 'dr_neal',\n  name: 'Dr. Neal',\n  text: \"This formula creates a connection between driver and vehicle unlike anything we've seen before. It's like... becoming one with the machine.\",\n  audioSrc: '/assets/voices/drneal_story_ch1_line2.mp3'\n}, {\n  character: 'siah_man',\n  name: 'Siah',\n  text: \"Sounds dangerous. Have you tested it?\",\n  audioSrc: '/assets/voices/siah_story_ch1_line2.mp3'\n}, {\n  character: 'dr_neal',\n  name: 'Dr. Neal',\n  text: \"Well, that's why I called you here. I need someone who understands cars on an intuitive level.\",\n  audioSrc: '/assets/voices/drneal_story_ch1_line3.mp3'\n}, {\n  character: 'siah_man',\n  name: 'Siah',\n  text: \"Wait, you want ME to test it? Dad, I'm not sure about this...\",\n  audioSrc: '/assets/voices/siah_story_ch1_line3.mp3'\n}, {\n  character: 'dr_neal',\n  name: 'Dr. Neal',\n  text: \"Trust me, son. This could be revolutionary. With the racing championship coming up, this could be your chance to show everyone what you're capable of.\",\n  audioSrc: '/assets/voices/drneal_story_ch1_line4.mp3'\n}, {\n  character: 'siah_man',\n  name: 'Siah',\n  text: \"...Alright. Let's do it. What's the worst that could happen?\",\n  audioSrc: '/assets/voices/siah_story_ch1_line4.mp3'\n}], [{\n  character: 'dr_neal',\n  name: 'Dr. Neal',\n  text: \"The formula is ready. Are you sure you want to proceed, Siah?\",\n  audioSrc: '/assets/voices/drneal_story_ch2_line1.mp3'\n}, {\n  character: 'siah_man',\n  name: 'Siah',\n  text: \"I'm ready, Dad. Let's see what this 'Car Whisperer' formula can do.\",\n  audioSrc: '/assets/voices/siah_story_ch2_line1.mp3'\n}, {\n  character: 'dr_neal',\n  name: 'Dr. Neal',\n  text: \"Take a deep breath. This might feel... unusual.\",\n  audioSrc: '/assets/voices/drneal_story_ch2_line2.mp3'\n}, {\n  character: 'siah_man',\n  name: 'Siah',\n  text: \"I feel... strange. Like I can hear the engine talking to me. The metal, the gears, they're all... communicating somehow.\",\n  audioSrc: '/assets/voices/siah_story_ch2_line2.mp3'\n}, {\n  character: 'dr_neal',\n  name: 'Dr. Neal',\n  text: \"It's working! The neural pathways are forming. You're becoming the Car Whisperer!\",\n  audioSrc: '/assets/voices/drneal_story_ch2_line3.mp3'\n}, {\n  character: 'siah_man',\n  name: 'Siah',\n  text: \"I understand the vehicle now. Not just how it works, but how it feels. This is incredible!\",\n  audioSrc: '/assets/voices/siah_story_ch2_line3.mp3'\n}, {\n  character: 'dr_neal',\n  name: 'Dr. Neal',\n  text: \"We need to test your abilities. Head to the track and see what you can do with this new connection.\",\n  audioSrc: '/assets/voices/drneal_story_ch2_line4.mp3'\n} // Assuming drneal_story_ch2_line4.mp3 exists\n], [{\n  character: 'max_velocity',\n  name: 'Max Velocity',\n  text: \"Well, well, if it isn't Dr. Neal's kid. Heard you've been making some noise about a new racing technique.\",\n  audioSrc: '/assets/voices/maxvelocity_story_ch3_line1.mp3'\n}, {\n  character: 'siah_man',\n  name: 'Siah',\n  text: \"I'm just here to race, Max. Nothing more.\",\n  audioSrc: '/assets/voices/siah_story_ch3_line1.mp3'\n}, {\n  character: 'max_velocity',\n  name: 'Max Velocity',\n  text: \"Racing is my territory. Your dad's crazy experiments won't change that. How about a little demonstration?\",\n  audioSrc: '/assets/voices/maxvelocity_story_ch3_line2.mp3'\n}, {\n  character: 'siah_man',\n  name: 'Siah',\n  text: \"You're on. One lap around the MAD Lab Circuit.\",\n  audioSrc: '/assets/voices/siah_story_ch3_line2.mp3'\n}, {\n  character: 'max_velocity',\n  name: 'Max Velocity',\n  text: \"When I win, you and your dad stay away from the championship. Deal?\",\n  audioSrc: '/assets/voices/maxvelocity_story_ch3_line3.mp3'\n}, {\n  character: 'siah_man',\n  name: 'Siah',\n  text: \"And when I win, you stop calling my dad crazy. Deal.\",\n  audioSrc: '/assets/voices/siah_story_ch3_line3.mp3'\n}]];\nconst chapterTitles = [\"Chapter 1: The Beginning\", \"Chapter 2: The Transformation\", \"Chapter 3: First Challenge\", \"Chapter 4: Mastering the Power\", \"Chapter 5: The Championship\"];\nconst StoryMode = () => {\n  _s();\n  const navigate = useNavigate();\n  const dispatch = useDispatch();\n  const currentChapter = useSelector(state => state.user.storyProgress.currentChapter);\n  const [dialogueIndex, setDialogueIndex] = useState(0);\n  const [showChapterTitle, setShowChapterTitle] = useState(true);\n  const currentChapterContent = storyContent[currentChapter - 1] || [];\n  const currentDialogue = currentChapterContent[dialogueIndex];\n  useEffect(() => {\n    dispatch(setCurrentScreen('story'));\n    setShowChapterTitle(true); // Ensure chapter title is shown when chapter changes\n    const timer = setTimeout(() => {\n      setShowChapterTitle(false);\n    }, 3000);\n    return () => {\n      clearTimeout(timer);\n      TTSSystem.getInstance().stop();\n    };\n  }, [dispatch, currentChapter]); // Rerun when currentChapter changes\n\n  useEffect(() => {\n    if (!showChapterTitle && currentDialogue) {\n      TTSSystem.getInstance().stop();\n\n      // Use the enhanced voice synchronization\n      TTSSystem.getInstance().playStoryDialogue(currentDialogue.character, currentDialogue.text, currentDialogue.audioSrc, () => {\n        console.log(`Completed dialogue: ${currentDialogue.character} - \"${currentDialogue.text}\"`);\n      });\n    }\n    // No specific cleanup needed here as the main useEffect handles stopping TTS on chapter change\n    // or component unmount. Starting TTS is handled when dialogue becomes available.\n  }, [dialogueIndex, showChapterTitle, currentDialogue]);\n  const handleContinue = () => {\n    if (dialogueIndex < currentChapterContent.length - 1) {\n      setDialogueIndex(dialogueIndex + 1);\n      // TTS for the next line will be triggered by the useEffect above\n    } else {\n      const cutsceneId = `chapter_${currentChapter}_complete`;\n      dispatch(markCutsceneSeen(cutsceneId));\n      if (currentChapter < storyContent.length) {\n        dispatch(advanceChapter());\n        dispatch(advanceStory()); // This updates currentChapter, triggering the first useEffect\n        setDialogueIndex(0); // Reset for the new chapter\n        // setShowChapterTitle(true); // Already handled by the first useEffect\n\n        if (currentChapter === 3) {\n          // Check against the chapter *before* advancing\n          navigate('/race');\n        }\n      } else {\n        navigate('/');\n      }\n    }\n  };\n  return /*#__PURE__*/_jsxDEV(StoryContainer, {\n    children: [/*#__PURE__*/_jsxDEV(StoryBackground, {\n      $chapter: currentChapter\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 274,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(BackButton, {\n      onClick: () => navigate('/'),\n      children: \"Back to Menu\"\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 276,\n      columnNumber: 7\n    }, this), showChapterTitle ? /*#__PURE__*/_jsxDEV(ChapterTitle, {\n      children: chapterTitles[currentChapter - 1] || `Chapter ${currentChapter}`\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 281,\n      columnNumber: 9\n    }, this) : /*#__PURE__*/_jsxDEV(_Fragment, {\n      children: currentDialogue && /*#__PURE__*/_jsxDEV(_Fragment, {\n        children: [/*#__PURE__*/_jsxDEV(CharacterPortrait, {\n          $character: currentDialogue.character\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 286,\n          columnNumber: 15\n        }, this), /*#__PURE__*/_jsxDEV(DialogueBox, {\n          children: [/*#__PURE__*/_jsxDEV(CharacterName, {\n            children: currentDialogue.name\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 288,\n            columnNumber: 17\n          }, this), /*#__PURE__*/_jsxDEV(DialogueText, {\n            children: currentDialogue.text\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 289,\n            columnNumber: 17\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 287,\n          columnNumber: 15\n        }, this), /*#__PURE__*/_jsxDEV(ContinueButton, {\n          onClick: handleContinue,\n          children: \"Continue\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 291,\n          columnNumber: 15\n        }, this)]\n      }, void 0, true)\n    }, void 0, false)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 273,\n    columnNumber: 5\n  }, this);\n};\n_s(StoryMode, \"nfPE1eY8ipC7JAkn+A8Cxz6FUCY=\", false, function () {\n  return [useNavigate, useDispatch, useSelector];\n});\n_c0 = StoryMode;\nexport default StoryMode;\nvar _c, _c2, _c3, _c4, _c5, _c6, _c7, _c8, _c9, _c0;\n$RefreshReg$(_c, \"StoryContainer\");\n$RefreshReg$(_c2, \"StoryBackground\");\n$RefreshReg$(_c3, \"DialogueBox\");\n$RefreshReg$(_c4, \"CharacterName\");\n$RefreshReg$(_c5, \"DialogueText\");\n$RefreshReg$(_c6, \"CharacterPortrait\");\n$RefreshReg$(_c7, \"ContinueButton\");\n$RefreshReg$(_c8, \"BackButton\");\n$RefreshReg$(_c9, \"ChapterTitle\");\n$RefreshReg$(_c0, \"StoryMode\");", "map": {"version": 3, "names": ["React", "useEffect", "useState", "useNavigate", "useDispatch", "useSelector", "styled", "keyframes", "setCurrentScreen", "advanceStory", "advanceChapter", "markCutsceneSeen", "TTSSystem", "jsxDEV", "_jsxDEV", "Fragment", "_Fragment", "cinematicPan", "StoryContainer", "div", "_c", "StoryBackground", "props", "$chapter", "_c2", "DialogueBox", "_c3", "CharacterName", "h3", "_c4", "DialogueText", "p", "_c5", "CharacterPortrait", "$character", "_c6", "Con<PERSON>ue<PERSON><PERSON><PERSON>", "button", "_c7", "BackButton", "_c8", "ChapterTitle", "h2", "_c9", "storyContent", "character", "name", "text", "audioSrc", "chapterTitles", "StoryMode", "_s", "navigate", "dispatch", "currentChapter", "state", "user", "storyProgress", "dialogueIndex", "setDialogueIndex", "showChapterTitle", "setShowChapterTitle", "<PERSON><PERSON><PERSON><PERSON>er<PERSON><PERSON>nt", "currentDialogue", "timer", "setTimeout", "clearTimeout", "getInstance", "stop", "playStoryDialogue", "console", "log", "handleContinue", "length", "cutsceneId", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "onClick", "_c0", "$RefreshReg$"], "sources": ["C:/Users/<USER>/Documents/Apps_Creations/SiahMan_Racing4Speed_WebGame/siah-man-racing/src/pages/StoryMode.tsx"], "sourcesContent": ["import React, { useEffect, useState } from 'react';\nimport { useNavigate } from 'react-router-dom';\nimport { useDispatch, useSelector } from 'react-redux';\nimport styled, { keyframes } from 'styled-components';\nimport { setCurrentScreen, advanceStory } from '../store/gameSlice';\nimport { advanceChapter, markCutsceneSeen } from '../store/userSlice';\nimport { RootState } from '../store';\nimport { TTSSystem } from '../game/TTSSystem';\n\nconst cinematicPan = keyframes`\n  0% { background-position: 0% 50%; }\n  50% { background-position: 100% 50%; }\n  100% { background-position: 0% 50%; }\n`;\n\nconst StoryContainer = styled.div`\n  width: 100%;\n  height: 100vh;\n  background: linear-gradient(rgba(10, 10, 30, 0.9), rgba(20, 20, 40, 0.9));\n  color: white;\n  display: flex;\n  flex-direction: column;\n  overflow: hidden;\n  position: relative;\n  animation: ${cinematicPan} 90s linear infinite;\n  font-family: 'Exo 2', sans-serif;\n`;\n\nconst StoryBackground = styled.div<{ $chapter: number }>`\n  position: absolute;\n  top: 0;\n  left: 0;\n  right: 0;\n  bottom: 0;\n  background: ${props => {\n    switch(props.$chapter) {\n      case 1: return 'linear-gradient(135deg, #1a2a6c, #b21f1f, #fdbb2d)';\n      case 2: return 'linear-gradient(135deg, #0f0c29, #302b63, #24243e)';\n      case 3: return 'linear-gradient(135deg, #2c3e50, #4ca1af)';\n      case 4: return 'linear-gradient(135deg, #8e0e00, #1f1c18)';\n      case 5: return 'linear-gradient(135deg, #000428, #004e92)';\n      default: return 'linear-gradient(135deg, #16222a, #3a6073)';\n    }\n  }};\n  opacity: 0.8;\n  z-index: -1;\n`;\n\nconst DialogueBox = styled.div`\n  position: absolute;\n  bottom: 50px;\n  left: 50px;\n  right: 50px;\n  background: rgba(0, 0, 0, 0.8);\n  border-radius: 10px;\n  padding: 20px;\n  box-shadow: 0 0 20px rgba(0, 0, 0, 0.5);\n`;\n\nconst CharacterName = styled.h3`\n  margin: 0 0 10px 0;\n  color: #fdbb2d;\n  font-size: 1.5rem;\n`;\n\nconst DialogueText = styled.p`\n  margin: 0;\n  font-size: 1.2rem;\n  line-height: 1.5;\n`;\n\nconst CharacterPortrait = styled.div<{ $character: string }>`\n  position: absolute;\n  bottom: 200px;\n  width: 300px;\n  height: 400px;\n  background-color: rgba(0, 0, 0, 0.5);\n  border-radius: 10px;\n  overflow: hidden;\n  \n  ${props => props.$character === 'siah_man' ? 'left: 100px;' : \n             props.$character === 'dr_neal' ? 'left: 100px;' : // Assuming Dr. Neal also on left for now\n             'right: 100px;'}\n\n  ${props => props.$character === 'siah_man' && `\n    background: url('/assets/Images/Siah_Image.jpeg') no-repeat center center;\n    background-size: cover;\n  `}\n\n  ${props => props.$character === 'dr_neal' && `\n    background: url('/assets/Images/Dr.Neal_SiahsDadImage.jpeg') no-repeat center center;\n    background-size: cover;\n  `}\n\n\n  &:before {\n    content: '';\n    position: absolute;\n    top: 0;\n    left: 0;\n    right: 0;\n    bottom: 0;\n    background: ${props => {\n      switch(props.$character) {\n        case 'siah_man': return 'rgba(0, 0, 0, 0.3)';\n        case 'dr_neal': return 'linear-gradient(to bottom, #1a2a6c, #4ca1af)';\n        case 'max_velocity': return 'linear-gradient(to bottom, #8e0e00, #1f1c18)';\n        default: return 'linear-gradient(to bottom, #16222a, #3a6073)';\n      }\n    }};\n    opacity: 0.7;\n    z-index: 1;\n  }\n\n  img {\n    display: block;\n    width: 100%;\n    height: 100%;\n    object-fit: cover;\n    z-index: 2;\n  }\n`;\n\nconst ContinueButton = styled.button`\n  position: absolute;\n  bottom: 20px;\n  right: 50px;\n  background: rgba(0, 100, 200, 0.7);\n  color: white;\n  border: none;\n  border-radius: 5px;\n  padding: 10px 20px;\n  cursor: pointer;\n  font-size: 1rem;\n  transition: all 0.2s ease;\n\n  &:hover {\n    background: rgba(0, 150, 250, 0.9);\n  }\n`;\n\nconst BackButton = styled.button`\n  position: absolute;\n  top: 20px;\n  left: 20px;\n  background: rgba(0, 0, 0, 0.5);\n  color: white;\n  border: none;\n  border-radius: 5px;\n  padding: 10px 15px;\n  cursor: pointer;\n  z-index: 10;\n`;\n\nconst ChapterTitle = styled.h2`\n  position: absolute;\n  top: 50px;\n  left: 0;\n  right: 0;\n  text-align: center;\n  font-size: 2rem;\n  text-transform: uppercase;\n  letter-spacing: 3px;\n  text-shadow: 0 0 10px rgba(0, 0, 0, 0.5);\n`;\n\nconst storyContent = [\n  [\n    { character: 'dr_neal', name: 'Dr. Neal', text: \"Siah, I've been working on something special in my MAD Laboratory. A formula that could change everything we know about racing.\", audioSrc: '/assets/voices/drneal_story_ch1_line1.mp3' },\n    { character: 'siah_man', name: 'Siah', text: \"Dad, you always say that about your experiments. What makes this one different?\", audioSrc: '/assets/voices/siah_story_ch1_line1.mp3' },\n    { character: 'dr_neal', name: 'Dr. Neal', text: \"This formula creates a connection between driver and vehicle unlike anything we've seen before. It's like... becoming one with the machine.\", audioSrc: '/assets/voices/drneal_story_ch1_line2.mp3' },\n    { character: 'siah_man', name: 'Siah', text: \"Sounds dangerous. Have you tested it?\", audioSrc: '/assets/voices/siah_story_ch1_line2.mp3' },\n    { character: 'dr_neal', name: 'Dr. Neal', text: \"Well, that's why I called you here. I need someone who understands cars on an intuitive level.\", audioSrc: '/assets/voices/drneal_story_ch1_line3.mp3' },\n    { character: 'siah_man', name: 'Siah', text: \"Wait, you want ME to test it? Dad, I'm not sure about this...\", audioSrc: '/assets/voices/siah_story_ch1_line3.mp3' },\n    { character: 'dr_neal', name: 'Dr. Neal', text: \"Trust me, son. This could be revolutionary. With the racing championship coming up, this could be your chance to show everyone what you're capable of.\", audioSrc: '/assets/voices/drneal_story_ch1_line4.mp3' },\n    { character: 'siah_man', name: 'Siah', text: \"...Alright. Let's do it. What's the worst that could happen?\", audioSrc: '/assets/voices/siah_story_ch1_line4.mp3' },\n  ],\n  [\n    { character: 'dr_neal', name: 'Dr. Neal', text: \"The formula is ready. Are you sure you want to proceed, Siah?\", audioSrc: '/assets/voices/drneal_story_ch2_line1.mp3' },\n    { character: 'siah_man', name: 'Siah', text: \"I'm ready, Dad. Let's see what this 'Car Whisperer' formula can do.\", audioSrc: '/assets/voices/siah_story_ch2_line1.mp3' },\n    { character: 'dr_neal', name: 'Dr. Neal', text: \"Take a deep breath. This might feel... unusual.\", audioSrc: '/assets/voices/drneal_story_ch2_line2.mp3' },\n    { character: 'siah_man', name: 'Siah', text: \"I feel... strange. Like I can hear the engine talking to me. The metal, the gears, they're all... communicating somehow.\", audioSrc: '/assets/voices/siah_story_ch2_line2.mp3' },\n    { character: 'dr_neal', name: 'Dr. Neal', text: \"It's working! The neural pathways are forming. You're becoming the Car Whisperer!\", audioSrc: '/assets/voices/drneal_story_ch2_line3.mp3' },\n    { character: 'siah_man', name: 'Siah', text: \"I understand the vehicle now. Not just how it works, but how it feels. This is incredible!\", audioSrc: '/assets/voices/siah_story_ch2_line3.mp3' },\n    { character: 'dr_neal', name: 'Dr. Neal', text: \"We need to test your abilities. Head to the track and see what you can do with this new connection.\", audioSrc: '/assets/voices/drneal_story_ch2_line4.mp3' }, // Assuming drneal_story_ch2_line4.mp3 exists\n  ],\n  [\n    { character: 'max_velocity', name: 'Max Velocity', text: \"Well, well, if it isn't Dr. Neal's kid. Heard you've been making some noise about a new racing technique.\", audioSrc: '/assets/voices/maxvelocity_story_ch3_line1.mp3' },\n    { character: 'siah_man', name: 'Siah', text: \"I'm just here to race, Max. Nothing more.\", audioSrc: '/assets/voices/siah_story_ch3_line1.mp3' },\n    { character: 'max_velocity', name: 'Max Velocity', text: \"Racing is my territory. Your dad's crazy experiments won't change that. How about a little demonstration?\", audioSrc: '/assets/voices/maxvelocity_story_ch3_line2.mp3' },\n    { character: 'siah_man', name: 'Siah', text: \"You're on. One lap around the MAD Lab Circuit.\", audioSrc: '/assets/voices/siah_story_ch3_line2.mp3' },\n    { character: 'max_velocity', name: 'Max Velocity', text: \"When I win, you and your dad stay away from the championship. Deal?\", audioSrc: '/assets/voices/maxvelocity_story_ch3_line3.mp3' },\n    { character: 'siah_man', name: 'Siah', text: \"And when I win, you stop calling my dad crazy. Deal.\", audioSrc: '/assets/voices/siah_story_ch3_line3.mp3' },\n  ],\n];\n\nconst chapterTitles = [\n  \"Chapter 1: The Beginning\",\n  \"Chapter 2: The Transformation\",\n  \"Chapter 3: First Challenge\",\n  \"Chapter 4: Mastering the Power\",\n  \"Chapter 5: The Championship\",\n];\n\nconst StoryMode: React.FC = () => {\n  const navigate = useNavigate();\n  const dispatch = useDispatch();\n  \n  const currentChapter = useSelector((state: RootState) => state.user.storyProgress.currentChapter);\n  \n  const [dialogueIndex, setDialogueIndex] = useState(0);\n  const [showChapterTitle, setShowChapterTitle] = useState(true);\n  \n  const currentChapterContent = storyContent[currentChapter - 1] || [];\n  const currentDialogue = currentChapterContent[dialogueIndex];\n  \n  useEffect(() => {\n    dispatch(setCurrentScreen('story'));\n    \n    setShowChapterTitle(true); // Ensure chapter title is shown when chapter changes\n    const timer = setTimeout(() => {\n      setShowChapterTitle(false);\n    }, 3000);\n    \n    return () => {\n      clearTimeout(timer);\n      TTSSystem.getInstance().stop();\n    };\n  }, [dispatch, currentChapter]); // Rerun when currentChapter changes\n\n  useEffect(() => {\n    if (!showChapterTitle && currentDialogue) {\n      TTSSystem.getInstance().stop();\n\n      // Use the enhanced voice synchronization\n      TTSSystem.getInstance().playStoryDialogue(\n        currentDialogue.character,\n        currentDialogue.text,\n        currentDialogue.audioSrc,\n        () => {\n          console.log(`Completed dialogue: ${currentDialogue.character} - \"${currentDialogue.text}\"`);\n        }\n      );\n    }\n    // No specific cleanup needed here as the main useEffect handles stopping TTS on chapter change\n    // or component unmount. Starting TTS is handled when dialogue becomes available.\n  }, [dialogueIndex, showChapterTitle, currentDialogue]);\n\n  const handleContinue = () => {\n    if (dialogueIndex < currentChapterContent.length - 1) {\n      setDialogueIndex(dialogueIndex + 1);\n      // TTS for the next line will be triggered by the useEffect above\n    } else {\n      const cutsceneId = `chapter_${currentChapter}_complete`;\n      dispatch(markCutsceneSeen(cutsceneId));\n      \n      if (currentChapter < storyContent.length) {\n        dispatch(advanceChapter());\n        dispatch(advanceStory()); // This updates currentChapter, triggering the first useEffect\n        setDialogueIndex(0); // Reset for the new chapter\n        // setShowChapterTitle(true); // Already handled by the first useEffect\n        \n        if (currentChapter === 3) { // Check against the chapter *before* advancing\n          navigate('/race');\n        }\n      } else {\n        navigate('/');\n      }\n    }\n  };\n  \n  return (\n    <StoryContainer>\n      <StoryBackground $chapter={currentChapter} />\n      \n      <BackButton onClick={() => navigate('/')}>\n        Back to Menu\n      </BackButton>\n      \n      {showChapterTitle ? (\n        <ChapterTitle>{chapterTitles[currentChapter - 1] || `Chapter ${currentChapter}`}</ChapterTitle>\n      ) : (\n        <>\n          {currentDialogue && (\n            <>\n              <CharacterPortrait $character={currentDialogue.character} />\n              <DialogueBox>\n                <CharacterName>{currentDialogue.name}</CharacterName>\n                <DialogueText>{currentDialogue.text}</DialogueText>\n              </DialogueBox>\n              <ContinueButton onClick={handleContinue}>\n                Continue\n              </ContinueButton>\n            </>\n          )}\n        </>\n      )}\n    </StoryContainer>\n  );\n};\n\nexport default StoryMode;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,SAAS,EAAEC,QAAQ,QAAQ,OAAO;AAClD,SAASC,WAAW,QAAQ,kBAAkB;AAC9C,SAASC,WAAW,EAAEC,WAAW,QAAQ,aAAa;AACtD,OAAOC,MAAM,IAAIC,SAAS,QAAQ,mBAAmB;AACrD,SAASC,gBAAgB,EAAEC,YAAY,QAAQ,oBAAoB;AACnE,SAASC,cAAc,EAAEC,gBAAgB,QAAQ,oBAAoB;AAErE,SAASC,SAAS,QAAQ,mBAAmB;AAAC,SAAAC,MAAA,IAAAC,OAAA,EAAAC,QAAA,IAAAC,SAAA;AAE9C,MAAMC,YAAY,GAAGV,SAAS;AAC9B;AACA;AACA;AACA,CAAC;AAED,MAAMW,cAAc,GAAGZ,MAAM,CAACa,GAAG;AACjC;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,eAAeF,YAAY;AAC3B;AACA,CAAC;AAACG,EAAA,GAXIF,cAAc;AAapB,MAAMG,eAAe,GAAGf,MAAM,CAACa,GAAyB;AACxD;AACA;AACA;AACA;AACA;AACA,gBAAgBG,KAAK,IAAI;EACrB,QAAOA,KAAK,CAACC,QAAQ;IACnB,KAAK,CAAC;MAAE,OAAO,oDAAoD;IACnE,KAAK,CAAC;MAAE,OAAO,oDAAoD;IACnE,KAAK,CAAC;MAAE,OAAO,2CAA2C;IAC1D,KAAK,CAAC;MAAE,OAAO,2CAA2C;IAC1D,KAAK,CAAC;MAAE,OAAO,2CAA2C;IAC1D;MAAS,OAAO,2CAA2C;EAC7D;AACF,CAAC;AACH;AACA;AACA,CAAC;AAACC,GAAA,GAlBIH,eAAe;AAoBrB,MAAMI,WAAW,GAAGnB,MAAM,CAACa,GAAG;AAC9B;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,CAAC;AAACO,GAAA,GATID,WAAW;AAWjB,MAAME,aAAa,GAAGrB,MAAM,CAACsB,EAAE;AAC/B;AACA;AACA;AACA,CAAC;AAACC,GAAA,GAJIF,aAAa;AAMnB,MAAMG,YAAY,GAAGxB,MAAM,CAACyB,CAAC;AAC7B;AACA;AACA;AACA,CAAC;AAACC,GAAA,GAJIF,YAAY;AAMlB,MAAMG,iBAAiB,GAAG3B,MAAM,CAACa,GAA2B;AAC5D;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,IAAIG,KAAK,IAAIA,KAAK,CAACY,UAAU,KAAK,UAAU,GAAG,cAAc,GAChDZ,KAAK,CAACY,UAAU,KAAK,SAAS,GAAG,cAAc;AAAG;AAClD,eAAe;AAC5B;AACA,IAAIZ,KAAK,IAAIA,KAAK,CAACY,UAAU,KAAK,UAAU,IAAI;AAChD;AACA;AACA,GAAG;AACH;AACA,IAAIZ,KAAK,IAAIA,KAAK,CAACY,UAAU,KAAK,SAAS,IAAI;AAC/C;AACA;AACA,GAAG;AACH;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,kBAAkBZ,KAAK,IAAI;EACrB,QAAOA,KAAK,CAACY,UAAU;IACrB,KAAK,UAAU;MAAE,OAAO,oBAAoB;IAC5C,KAAK,SAAS;MAAE,OAAO,8CAA8C;IACrE,KAAK,cAAc;MAAE,OAAO,8CAA8C;IAC1E;MAAS,OAAO,8CAA8C;EAChE;AACF,CAAC;AACL;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,CAAC;AAACC,GAAA,GAlDIF,iBAAiB;AAoDvB,MAAMG,cAAc,GAAG9B,MAAM,CAAC+B,MAAM;AACpC;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,CAAC;AAACC,GAAA,GAhBIF,cAAc;AAkBpB,MAAMG,UAAU,GAAGjC,MAAM,CAAC+B,MAAM;AAChC;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,CAAC;AAACG,GAAA,GAXID,UAAU;AAahB,MAAME,YAAY,GAAGnC,MAAM,CAACoC,EAAE;AAC9B;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,CAAC;AAACC,GAAA,GAVIF,YAAY;AAYlB,MAAMG,YAAY,GAAG,CACnB,CACE;EAAEC,SAAS,EAAE,SAAS;EAAEC,IAAI,EAAE,UAAU;EAAEC,IAAI,EAAE,iIAAiI;EAAEC,QAAQ,EAAE;AAA4C,CAAC,EAC1O;EAAEH,SAAS,EAAE,UAAU;EAAEC,IAAI,EAAE,MAAM;EAAEC,IAAI,EAAE,iFAAiF;EAAEC,QAAQ,EAAE;AAA0C,CAAC,EACrL;EAAEH,SAAS,EAAE,SAAS;EAAEC,IAAI,EAAE,UAAU;EAAEC,IAAI,EAAE,6IAA6I;EAAEC,QAAQ,EAAE;AAA4C,CAAC,EACtP;EAAEH,SAAS,EAAE,UAAU;EAAEC,IAAI,EAAE,MAAM;EAAEC,IAAI,EAAE,uCAAuC;EAAEC,QAAQ,EAAE;AAA0C,CAAC,EAC3I;EAAEH,SAAS,EAAE,SAAS;EAAEC,IAAI,EAAE,UAAU;EAAEC,IAAI,EAAE,gGAAgG;EAAEC,QAAQ,EAAE;AAA4C,CAAC,EACzM;EAAEH,SAAS,EAAE,UAAU;EAAEC,IAAI,EAAE,MAAM;EAAEC,IAAI,EAAE,+DAA+D;EAAEC,QAAQ,EAAE;AAA0C,CAAC,EACnK;EAAEH,SAAS,EAAE,SAAS;EAAEC,IAAI,EAAE,UAAU;EAAEC,IAAI,EAAE,wJAAwJ;EAAEC,QAAQ,EAAE;AAA4C,CAAC,EACjQ;EAAEH,SAAS,EAAE,UAAU;EAAEC,IAAI,EAAE,MAAM;EAAEC,IAAI,EAAE,8DAA8D;EAAEC,QAAQ,EAAE;AAA0C,CAAC,CACnK,EACD,CACE;EAAEH,SAAS,EAAE,SAAS;EAAEC,IAAI,EAAE,UAAU;EAAEC,IAAI,EAAE,+DAA+D;EAAEC,QAAQ,EAAE;AAA4C,CAAC,EACxK;EAAEH,SAAS,EAAE,UAAU;EAAEC,IAAI,EAAE,MAAM;EAAEC,IAAI,EAAE,qEAAqE;EAAEC,QAAQ,EAAE;AAA0C,CAAC,EACzK;EAAEH,SAAS,EAAE,SAAS;EAAEC,IAAI,EAAE,UAAU;EAAEC,IAAI,EAAE,iDAAiD;EAAEC,QAAQ,EAAE;AAA4C,CAAC,EAC1J;EAAEH,SAAS,EAAE,UAAU;EAAEC,IAAI,EAAE,MAAM;EAAEC,IAAI,EAAE,0HAA0H;EAAEC,QAAQ,EAAE;AAA0C,CAAC,EAC9N;EAAEH,SAAS,EAAE,SAAS;EAAEC,IAAI,EAAE,UAAU;EAAEC,IAAI,EAAE,mFAAmF;EAAEC,QAAQ,EAAE;AAA4C,CAAC,EAC5L;EAAEH,SAAS,EAAE,UAAU;EAAEC,IAAI,EAAE,MAAM;EAAEC,IAAI,EAAE,4FAA4F;EAAEC,QAAQ,EAAE;AAA0C,CAAC,EAChM;EAAEH,SAAS,EAAE,SAAS;EAAEC,IAAI,EAAE,UAAU;EAAEC,IAAI,EAAE,qGAAqG;EAAEC,QAAQ,EAAE;AAA4C,CAAC,CAAE;AAAA,CACjN,EACD,CACE;EAAEH,SAAS,EAAE,cAAc;EAAEC,IAAI,EAAE,cAAc;EAAEC,IAAI,EAAE,2GAA2G;EAAEC,QAAQ,EAAE;AAAiD,CAAC,EAClO;EAAEH,SAAS,EAAE,UAAU;EAAEC,IAAI,EAAE,MAAM;EAAEC,IAAI,EAAE,2CAA2C;EAAEC,QAAQ,EAAE;AAA0C,CAAC,EAC/I;EAAEH,SAAS,EAAE,cAAc;EAAEC,IAAI,EAAE,cAAc;EAAEC,IAAI,EAAE,2GAA2G;EAAEC,QAAQ,EAAE;AAAiD,CAAC,EAClO;EAAEH,SAAS,EAAE,UAAU;EAAEC,IAAI,EAAE,MAAM;EAAEC,IAAI,EAAE,gDAAgD;EAAEC,QAAQ,EAAE;AAA0C,CAAC,EACpJ;EAAEH,SAAS,EAAE,cAAc;EAAEC,IAAI,EAAE,cAAc;EAAEC,IAAI,EAAE,qEAAqE;EAAEC,QAAQ,EAAE;AAAiD,CAAC,EAC5L;EAAEH,SAAS,EAAE,UAAU;EAAEC,IAAI,EAAE,MAAM;EAAEC,IAAI,EAAE,sDAAsD;EAAEC,QAAQ,EAAE;AAA0C,CAAC,CAC3J,CACF;AAED,MAAMC,aAAa,GAAG,CACpB,0BAA0B,EAC1B,+BAA+B,EAC/B,4BAA4B,EAC5B,gCAAgC,EAChC,6BAA6B,CAC9B;AAED,MAAMC,SAAmB,GAAGA,CAAA,KAAM;EAAAC,EAAA;EAChC,MAAMC,QAAQ,GAAGjD,WAAW,CAAC,CAAC;EAC9B,MAAMkD,QAAQ,GAAGjD,WAAW,CAAC,CAAC;EAE9B,MAAMkD,cAAc,GAAGjD,WAAW,CAAEkD,KAAgB,IAAKA,KAAK,CAACC,IAAI,CAACC,aAAa,CAACH,cAAc,CAAC;EAEjG,MAAM,CAACI,aAAa,EAAEC,gBAAgB,CAAC,GAAGzD,QAAQ,CAAC,CAAC,CAAC;EACrD,MAAM,CAAC0D,gBAAgB,EAAEC,mBAAmB,CAAC,GAAG3D,QAAQ,CAAC,IAAI,CAAC;EAE9D,MAAM4D,qBAAqB,GAAGlB,YAAY,CAACU,cAAc,GAAG,CAAC,CAAC,IAAI,EAAE;EACpE,MAAMS,eAAe,GAAGD,qBAAqB,CAACJ,aAAa,CAAC;EAE5DzD,SAAS,CAAC,MAAM;IACdoD,QAAQ,CAAC7C,gBAAgB,CAAC,OAAO,CAAC,CAAC;IAEnCqD,mBAAmB,CAAC,IAAI,CAAC,CAAC,CAAC;IAC3B,MAAMG,KAAK,GAAGC,UAAU,CAAC,MAAM;MAC7BJ,mBAAmB,CAAC,KAAK,CAAC;IAC5B,CAAC,EAAE,IAAI,CAAC;IAER,OAAO,MAAM;MACXK,YAAY,CAACF,KAAK,CAAC;MACnBpD,SAAS,CAACuD,WAAW,CAAC,CAAC,CAACC,IAAI,CAAC,CAAC;IAChC,CAAC;EACH,CAAC,EAAE,CAACf,QAAQ,EAAEC,cAAc,CAAC,CAAC,CAAC,CAAC;;EAEhCrD,SAAS,CAAC,MAAM;IACd,IAAI,CAAC2D,gBAAgB,IAAIG,eAAe,EAAE;MACxCnD,SAAS,CAACuD,WAAW,CAAC,CAAC,CAACC,IAAI,CAAC,CAAC;;MAE9B;MACAxD,SAAS,CAACuD,WAAW,CAAC,CAAC,CAACE,iBAAiB,CACvCN,eAAe,CAAClB,SAAS,EACzBkB,eAAe,CAAChB,IAAI,EACpBgB,eAAe,CAACf,QAAQ,EACxB,MAAM;QACJsB,OAAO,CAACC,GAAG,CAAC,uBAAuBR,eAAe,CAAClB,SAAS,OAAOkB,eAAe,CAAChB,IAAI,GAAG,CAAC;MAC7F,CACF,CAAC;IACH;IACA;IACA;EACF,CAAC,EAAE,CAACW,aAAa,EAAEE,gBAAgB,EAAEG,eAAe,CAAC,CAAC;EAEtD,MAAMS,cAAc,GAAGA,CAAA,KAAM;IAC3B,IAAId,aAAa,GAAGI,qBAAqB,CAACW,MAAM,GAAG,CAAC,EAAE;MACpDd,gBAAgB,CAACD,aAAa,GAAG,CAAC,CAAC;MACnC;IACF,CAAC,MAAM;MACL,MAAMgB,UAAU,GAAG,WAAWpB,cAAc,WAAW;MACvDD,QAAQ,CAAC1C,gBAAgB,CAAC+D,UAAU,CAAC,CAAC;MAEtC,IAAIpB,cAAc,GAAGV,YAAY,CAAC6B,MAAM,EAAE;QACxCpB,QAAQ,CAAC3C,cAAc,CAAC,CAAC,CAAC;QAC1B2C,QAAQ,CAAC5C,YAAY,CAAC,CAAC,CAAC,CAAC,CAAC;QAC1BkD,gBAAgB,CAAC,CAAC,CAAC,CAAC,CAAC;QACrB;;QAEA,IAAIL,cAAc,KAAK,CAAC,EAAE;UAAE;UAC1BF,QAAQ,CAAC,OAAO,CAAC;QACnB;MACF,CAAC,MAAM;QACLA,QAAQ,CAAC,GAAG,CAAC;MACf;IACF;EACF,CAAC;EAED,oBACEtC,OAAA,CAACI,cAAc;IAAAyD,QAAA,gBACb7D,OAAA,CAACO,eAAe;MAACE,QAAQ,EAAE+B;IAAe;MAAAsB,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC,eAE7CjE,OAAA,CAACyB,UAAU;MAACyC,OAAO,EAAEA,CAAA,KAAM5B,QAAQ,CAAC,GAAG,CAAE;MAAAuB,QAAA,EAAC;IAE1C;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAY,CAAC,EAEZnB,gBAAgB,gBACf9C,OAAA,CAAC2B,YAAY;MAAAkC,QAAA,EAAE1B,aAAa,CAACK,cAAc,GAAG,CAAC,CAAC,IAAI,WAAWA,cAAc;IAAE;MAAAsB,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAe,CAAC,gBAE/FjE,OAAA,CAAAE,SAAA;MAAA2D,QAAA,EACGZ,eAAe,iBACdjD,OAAA,CAAAE,SAAA;QAAA2D,QAAA,gBACE7D,OAAA,CAACmB,iBAAiB;UAACC,UAAU,EAAE6B,eAAe,CAAClB;QAAU;UAAA+B,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC,eAC5DjE,OAAA,CAACW,WAAW;UAAAkD,QAAA,gBACV7D,OAAA,CAACa,aAAa;YAAAgD,QAAA,EAAEZ,eAAe,CAACjB;UAAI;YAAA8B,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAgB,CAAC,eACrDjE,OAAA,CAACgB,YAAY;YAAA6C,QAAA,EAAEZ,eAAe,CAAChB;UAAI;YAAA6B,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAe,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACxC,CAAC,eACdjE,OAAA,CAACsB,cAAc;UAAC4C,OAAO,EAAER,cAAe;UAAAG,QAAA,EAAC;QAEzC;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAgB,CAAC;MAAA,eACjB;IACH,gBACD,CACH;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACa,CAAC;AAErB,CAAC;AAAC5B,EAAA,CA/FID,SAAmB;EAAA,QACN/C,WAAW,EACXC,WAAW,EAELC,WAAW;AAAA;AAAA4E,GAAA,GAJ9B/B,SAAmB;AAiGzB,eAAeA,SAAS;AAAC,IAAA9B,EAAA,EAAAI,GAAA,EAAAE,GAAA,EAAAG,GAAA,EAAAG,GAAA,EAAAG,GAAA,EAAAG,GAAA,EAAAE,GAAA,EAAAG,GAAA,EAAAsC,GAAA;AAAAC,YAAA,CAAA9D,EAAA;AAAA8D,YAAA,CAAA1D,GAAA;AAAA0D,YAAA,CAAAxD,GAAA;AAAAwD,YAAA,CAAArD,GAAA;AAAAqD,YAAA,CAAAlD,GAAA;AAAAkD,YAAA,CAAA/C,GAAA;AAAA+C,YAAA,CAAA5C,GAAA;AAAA4C,YAAA,CAAA1C,GAAA;AAAA0C,YAAA,CAAAvC,GAAA;AAAAuC,YAAA,CAAAD,GAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}