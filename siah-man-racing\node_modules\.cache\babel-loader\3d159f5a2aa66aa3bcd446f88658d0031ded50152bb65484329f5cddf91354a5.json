{"ast": null, "code": "var __defProp = Object.defineProperty;\nvar __defNormalProp = (obj, key, value) => key in obj ? __defProp(obj, key, {\n  enumerable: true,\n  configurable: true,\n  writable: true,\n  value\n}) : obj[key] = value;\nvar __publicField = (obj, key, value) => {\n  __defNormalProp(obj, typeof key !== \"symbol\" ? key + \"\" : key, value);\n  return value;\n};\nconst VRButton = /* @__PURE__ */(() => {\n  const _VRButton = class {\n    static createButton(renderer, sessionInit = {}) {\n      const button = document.createElement(\"button\");\n      function showEnterVR() {\n        let currentSession = null;\n        async function onSessionStarted(session) {\n          session.addEventListener(\"end\", onSessionEnded);\n          await renderer.xr.setSession(session);\n          button.textContent = \"EXIT VR\";\n          currentSession = session;\n        }\n        function onSessionEnded() {\n          currentSession.removeEventListener(\"end\", onSessionEnded);\n          button.textContent = \"ENTER VR\";\n          currentSession = null;\n        }\n        button.style.display = \"\";\n        button.style.cursor = \"pointer\";\n        button.style.left = \"calc(50% - 50px)\";\n        button.style.width = \"100px\";\n        button.textContent = \"ENTER VR\";\n        button.onmouseenter = () => {\n          button.style.opacity = \"1.0\";\n        };\n        button.onmouseleave = () => {\n          button.style.opacity = \"0.5\";\n        };\n        button.onclick = () => {\n          var _a;\n          if (currentSession === null) {\n            const optionalFeatures = [sessionInit.optionalFeatures, \"local-floor\", \"bounded-floor\", \"hand-tracking\"].flat().filter(Boolean);\n            (_a = navigator.xr) == null ? void 0 : _a.requestSession(\"immersive-vr\", {\n              ...sessionInit,\n              optionalFeatures\n            }).then(onSessionStarted);\n          } else {\n            currentSession.end();\n          }\n        };\n      }\n      function disableButton() {\n        button.style.display = \"\";\n        button.style.cursor = \"auto\";\n        button.style.left = \"calc(50% - 75px)\";\n        button.style.width = \"150px\";\n        button.onmouseenter = null;\n        button.onmouseleave = null;\n        button.onclick = null;\n      }\n      function showWebXRNotFound() {\n        disableButton();\n        button.textContent = \"VR NOT SUPPORTED\";\n      }\n      function stylizeElement(element) {\n        element.style.position = \"absolute\";\n        element.style.bottom = \"20px\";\n        element.style.padding = \"12px 6px\";\n        element.style.border = \"1px solid #fff\";\n        element.style.borderRadius = \"4px\";\n        element.style.background = \"rgba(0,0,0,0.1)\";\n        element.style.color = \"#fff\";\n        element.style.font = \"normal 13px sans-serif\";\n        element.style.textAlign = \"center\";\n        element.style.opacity = \"0.5\";\n        element.style.outline = \"none\";\n        element.style.zIndex = \"999\";\n      }\n      if (\"xr\" in navigator) {\n        stylizeElement(button);\n        button.id = \"VRButton\";\n        button.style.display = \"none\";\n        navigator.xr.isSessionSupported(\"immersive-vr\").then(supported => {\n          supported ? showEnterVR() : showWebXRNotFound();\n          if (supported && _VRButton.xrSessionIsGranted) {\n            button.click();\n          }\n        });\n        return button;\n      } else {\n        const message = document.createElement(\"a\");\n        if (window.isSecureContext === false) {\n          message.href = document.location.href.replace(/^http:/, \"https:\");\n          message.innerHTML = \"WEBXR NEEDS HTTPS\";\n        } else {\n          message.href = \"https://immersiveweb.dev/\";\n          message.innerHTML = \"WEBXR NOT AVAILABLE\";\n        }\n        message.style.left = \"calc(50% - 90px)\";\n        message.style.width = \"180px\";\n        message.style.textDecoration = \"none\";\n        stylizeElement(message);\n        return message;\n      }\n    }\n    static registerSessionGrantedListener() {\n      if (typeof navigator !== \"undefined\" && \"xr\" in navigator) {\n        navigator.xr.addEventListener(\"sessiongranted\", () => {\n          _VRButton.xrSessionIsGranted = true;\n        });\n      }\n    }\n  };\n  let VRButton2 = _VRButton;\n  __publicField(VRButton2, \"xrSessionIsGranted\", false);\n  VRButton2.registerSessionGrantedListener();\n  return VRButton2;\n})();\nexport { VRButton };", "map": {"version": 3, "names": ["VRButton", "_VRButton", "createButton", "renderer", "sessionInit", "button", "document", "createElement", "showEnterVR", "currentSession", "onSessionStarted", "session", "addEventListener", "onSessionEnded", "xr", "setSession", "textContent", "removeEventListener", "style", "display", "cursor", "left", "width", "onmouseenter", "opacity", "onmouseleave", "onclick", "_a", "optionalFeatures", "flat", "filter", "Boolean", "navigator", "requestSession", "then", "end", "disable<PERSON><PERSON><PERSON>", "showWebXRNotFound", "stylizeElement", "element", "position", "bottom", "padding", "border", "borderRadius", "background", "color", "font", "textAlign", "outline", "zIndex", "id", "isSessionSupported", "supported", "xrSessionIsGranted", "click", "message", "window", "isSecureContext", "href", "location", "replace", "innerHTML", "textDecoration", "registerSessionGrantedListener", "VRButton2", "__publicField"], "sources": ["C:\\Users\\<USER>\\Documents\\Apps_Creations\\SiahMan_Racing4Speed_WebGame\\siah-man-racing\\node_modules\\src\\webxr\\VRButton.js"], "sourcesContent": ["const VRButton = /* @__PURE__ */ (() => {\n  class VRButton {\n    static createButton(renderer, sessionInit = {}) {\n      const button = document.createElement('button')\n\n      function showEnterVR(/*device*/) {\n        let currentSession = null\n\n        async function onSessionStarted(session) {\n          session.addEventListener('end', onSessionEnded)\n\n          await renderer.xr.setSession(session)\n          button.textContent = 'EXIT VR'\n\n          currentSession = session\n        }\n\n        function onSessionEnded(/*event*/) {\n          currentSession.removeEventListener('end', onSessionEnded)\n\n          button.textContent = 'ENTER VR'\n\n          currentSession = null\n        }\n\n        //\n\n        button.style.display = ''\n\n        button.style.cursor = 'pointer'\n        button.style.left = 'calc(50% - 50px)'\n        button.style.width = '100px'\n\n        button.textContent = 'ENTER VR'\n\n        button.onmouseenter = () => {\n          button.style.opacity = '1.0'\n        }\n\n        button.onmouseleave = () => {\n          button.style.opacity = '0.5'\n        }\n\n        button.onclick = () => {\n          if (currentSession === null) {\n            // WebXR's requestReferenceSpace only works if the corresponding feature\n            // was requested at session creation time. For simplicity, just ask for\n            // the interesting ones as optional features, but be aware that the\n            // requestReferenceSpace call will fail if it turns out to be unavailable.\n            // ('local' is always available for immersive sessions and doesn't need to\n            // be requested separately.)\n\n            const optionalFeatures = [sessionInit.optionalFeatures, 'local-floor', 'bounded-floor', 'hand-tracking']\n              .flat()\n              .filter(Boolean)\n\n            navigator.xr?.requestSession('immersive-vr', { ...sessionInit, optionalFeatures }).then(onSessionStarted)\n          } else {\n            currentSession.end()\n          }\n        }\n      }\n\n      function disableButton() {\n        button.style.display = ''\n\n        button.style.cursor = 'auto'\n        button.style.left = 'calc(50% - 75px)'\n        button.style.width = '150px'\n\n        button.onmouseenter = null\n        button.onmouseleave = null\n\n        button.onclick = null\n      }\n\n      function showWebXRNotFound() {\n        disableButton()\n\n        button.textContent = 'VR NOT SUPPORTED'\n      }\n\n      function stylizeElement(element) {\n        element.style.position = 'absolute'\n        element.style.bottom = '20px'\n        element.style.padding = '12px 6px'\n        element.style.border = '1px solid #fff'\n        element.style.borderRadius = '4px'\n        element.style.background = 'rgba(0,0,0,0.1)'\n        element.style.color = '#fff'\n        element.style.font = 'normal 13px sans-serif'\n        element.style.textAlign = 'center'\n        element.style.opacity = '0.5'\n        element.style.outline = 'none'\n        element.style.zIndex = '999'\n      }\n\n      if ('xr' in navigator) {\n        stylizeElement(button)\n        button.id = 'VRButton'\n        button.style.display = 'none'\n\n        // Query for session mode\n        navigator.xr.isSessionSupported('immersive-vr').then((supported) => {\n          supported ? showEnterVR() : showWebXRNotFound()\n\n          if (supported && VRButton.xrSessionIsGranted) {\n            button.click()\n          }\n        })\n\n        return button\n      } else {\n        const message = document.createElement('a')\n\n        if (window.isSecureContext === false) {\n          message.href = document.location.href.replace(/^http:/, 'https:')\n          message.innerHTML = 'WEBXR NEEDS HTTPS' // TODO Improve message\n        } else {\n          message.href = 'https://immersiveweb.dev/'\n          message.innerHTML = 'WEBXR NOT AVAILABLE'\n        }\n\n        message.style.left = 'calc(50% - 90px)'\n        message.style.width = '180px'\n        message.style.textDecoration = 'none'\n\n        stylizeElement(message)\n\n        return message\n      }\n    }\n\n    static xrSessionIsGranted = false\n\n    static registerSessionGrantedListener() {\n      if (typeof navigator !== 'undefined' && 'xr' in navigator) {\n        navigator.xr.addEventListener('sessiongranted', () => {\n          VRButton.xrSessionIsGranted = true\n        })\n      }\n    }\n  }\n\n  VRButton.registerSessionGrantedListener()\n\n  return VRButton\n})()\n\nexport { VRButton }\n"], "mappings": ";;;;;;;;;;;AAAK,MAACA,QAAA,GAA4B,sBAAM;EACtC,MAAMC,SAAA,GAAN,MAAe;IACb,OAAOC,aAAaC,QAAA,EAAUC,WAAA,GAAc,IAAI;MAC9C,MAAMC,MAAA,GAASC,QAAA,CAASC,aAAA,CAAc,QAAQ;MAE9C,SAASC,YAAA,EAAwB;QAC/B,IAAIC,cAAA,GAAiB;QAErB,eAAeC,iBAAiBC,OAAA,EAAS;UACvCA,OAAA,CAAQC,gBAAA,CAAiB,OAAOC,cAAc;UAE9C,MAAMV,QAAA,CAASW,EAAA,CAAGC,UAAA,CAAWJ,OAAO;UACpCN,MAAA,CAAOW,WAAA,GAAc;UAErBP,cAAA,GAAiBE,OAAA;QAClB;QAED,SAASE,eAAA,EAA0B;UACjCJ,cAAA,CAAeQ,mBAAA,CAAoB,OAAOJ,cAAc;UAExDR,MAAA,CAAOW,WAAA,GAAc;UAErBP,cAAA,GAAiB;QAClB;QAIDJ,MAAA,CAAOa,KAAA,CAAMC,OAAA,GAAU;QAEvBd,MAAA,CAAOa,KAAA,CAAME,MAAA,GAAS;QACtBf,MAAA,CAAOa,KAAA,CAAMG,IAAA,GAAO;QACpBhB,MAAA,CAAOa,KAAA,CAAMI,KAAA,GAAQ;QAErBjB,MAAA,CAAOW,WAAA,GAAc;QAErBX,MAAA,CAAOkB,YAAA,GAAe,MAAM;UAC1BlB,MAAA,CAAOa,KAAA,CAAMM,OAAA,GAAU;QACxB;QAEDnB,MAAA,CAAOoB,YAAA,GAAe,MAAM;UAC1BpB,MAAA,CAAOa,KAAA,CAAMM,OAAA,GAAU;QACxB;QAEDnB,MAAA,CAAOqB,OAAA,GAAU,MAAM;UA3C1B,IAAAC,EAAA;UA4CK,IAAIlB,cAAA,KAAmB,MAAM;YAQ3B,MAAMmB,gBAAA,GAAmB,CAACxB,WAAA,CAAYwB,gBAAA,EAAkB,eAAe,iBAAiB,eAAe,EACpGC,IAAA,CAAM,EACNC,MAAA,CAAOC,OAAO;YAEjB,CAAAJ,EAAA,GAAAK,SAAA,CAAUlB,EAAA,KAAV,gBAAAa,EAAA,CAAcM,cAAA,CAAe,gBAAgB;cAAE,GAAG7B,WAAA;cAAawB;YAAkB,GAAEM,IAAA,CAAKxB,gBAAA;UACpG,OAAiB;YACLD,cAAA,CAAe0B,GAAA,CAAK;UACrB;QACF;MACF;MAED,SAASC,cAAA,EAAgB;QACvB/B,MAAA,CAAOa,KAAA,CAAMC,OAAA,GAAU;QAEvBd,MAAA,CAAOa,KAAA,CAAME,MAAA,GAAS;QACtBf,MAAA,CAAOa,KAAA,CAAMG,IAAA,GAAO;QACpBhB,MAAA,CAAOa,KAAA,CAAMI,KAAA,GAAQ;QAErBjB,MAAA,CAAOkB,YAAA,GAAe;QACtBlB,MAAA,CAAOoB,YAAA,GAAe;QAEtBpB,MAAA,CAAOqB,OAAA,GAAU;MAClB;MAED,SAASW,kBAAA,EAAoB;QAC3BD,aAAA,CAAe;QAEf/B,MAAA,CAAOW,WAAA,GAAc;MACtB;MAED,SAASsB,eAAeC,OAAA,EAAS;QAC/BA,OAAA,CAAQrB,KAAA,CAAMsB,QAAA,GAAW;QACzBD,OAAA,CAAQrB,KAAA,CAAMuB,MAAA,GAAS;QACvBF,OAAA,CAAQrB,KAAA,CAAMwB,OAAA,GAAU;QACxBH,OAAA,CAAQrB,KAAA,CAAMyB,MAAA,GAAS;QACvBJ,OAAA,CAAQrB,KAAA,CAAM0B,YAAA,GAAe;QAC7BL,OAAA,CAAQrB,KAAA,CAAM2B,UAAA,GAAa;QAC3BN,OAAA,CAAQrB,KAAA,CAAM4B,KAAA,GAAQ;QACtBP,OAAA,CAAQrB,KAAA,CAAM6B,IAAA,GAAO;QACrBR,OAAA,CAAQrB,KAAA,CAAM8B,SAAA,GAAY;QAC1BT,OAAA,CAAQrB,KAAA,CAAMM,OAAA,GAAU;QACxBe,OAAA,CAAQrB,KAAA,CAAM+B,OAAA,GAAU;QACxBV,OAAA,CAAQrB,KAAA,CAAMgC,MAAA,GAAS;MACxB;MAED,IAAI,QAAQlB,SAAA,EAAW;QACrBM,cAAA,CAAejC,MAAM;QACrBA,MAAA,CAAO8C,EAAA,GAAK;QACZ9C,MAAA,CAAOa,KAAA,CAAMC,OAAA,GAAU;QAGvBa,SAAA,CAAUlB,EAAA,CAAGsC,kBAAA,CAAmB,cAAc,EAAElB,IAAA,CAAMmB,SAAA,IAAc;UAClEA,SAAA,GAAY7C,WAAA,CAAa,IAAG6B,iBAAA,CAAmB;UAE/C,IAAIgB,SAAA,IAAapD,SAAA,CAASqD,kBAAA,EAAoB;YAC5CjD,MAAA,CAAOkD,KAAA,CAAO;UACf;QACX,CAAS;QAED,OAAOlD,MAAA;MACf,OAAa;QACL,MAAMmD,OAAA,GAAUlD,QAAA,CAASC,aAAA,CAAc,GAAG;QAE1C,IAAIkD,MAAA,CAAOC,eAAA,KAAoB,OAAO;UACpCF,OAAA,CAAQG,IAAA,GAAOrD,QAAA,CAASsD,QAAA,CAASD,IAAA,CAAKE,OAAA,CAAQ,UAAU,QAAQ;UAChEL,OAAA,CAAQM,SAAA,GAAY;QAC9B,OAAe;UACLN,OAAA,CAAQG,IAAA,GAAO;UACfH,OAAA,CAAQM,SAAA,GAAY;QACrB;QAEDN,OAAA,CAAQtC,KAAA,CAAMG,IAAA,GAAO;QACrBmC,OAAA,CAAQtC,KAAA,CAAMI,KAAA,GAAQ;QACtBkC,OAAA,CAAQtC,KAAA,CAAM6C,cAAA,GAAiB;QAE/BzB,cAAA,CAAekB,OAAO;QAEtB,OAAOA,OAAA;MACR;IACF;IAID,OAAOQ,+BAAA,EAAiC;MACtC,IAAI,OAAOhC,SAAA,KAAc,eAAe,QAAQA,SAAA,EAAW;QACzDA,SAAA,CAAUlB,EAAA,CAAGF,gBAAA,CAAiB,kBAAkB,MAAM;UACpDX,SAAA,CAASqD,kBAAA,GAAqB;QACxC,CAAS;MACF;IACF;EACF;EA7ID,IAAMW,SAAA,GAANhE,SAAA;EAoIEiE,aAAA,CApIID,SAAA,EAoIG,sBAAqB;EAW9BA,SAAA,CAASD,8BAAA,CAAgC;EAEzC,OAAOC,SAAA;AACT,GAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}