import React, { useEffect, useState, useCallback } from 'react';
import { AudioSystem } from '../game/AudioSystem';
import { useSelector } from 'react-redux';
import { RootState } from '../store';
import styled from 'styled-components';

// Styled components
const AudioControlsContainer = styled.div`
  position: fixed;
  bottom: 15px;
  right: 15px;
  display: flex;
  gap: 10px;
  z-index: 100;
`;

const AudioButton = styled.button<{ $active: boolean }>`
  width: 40px;
  height: 40px;
  border-radius: 50%;
  background: ${props => props.$active 
    ? 'linear-gradient(145deg, #0088ff, #00aaff)'
    : 'rgba(0, 0, 0, 0.5)'
  };
  border: 2px solid ${props => props.$active ? '#ffffff' : 'rgba(255, 255, 255, 0.3)'};
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  transition: all 0.3s ease;
  
  &:hover {
    transform: scale(1.1);
    box-shadow: 0 0 15px rgba(0, 170, 255, 0.5);
  }
  
  svg {
    width: 20px;
    height: 20px;
    fill: white;
  }
`;

const MusicIcon = () => (
  <svg viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
    <path d="M9 18V5l12-2v13" />
    <circle cx="6" cy="18" r="3" />
    <circle cx="18" cy="16" r="3" />
  </svg>
);

const MusicMuteIcon = () => (
  <svg viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
    <path d="M9 18V5l12-2v13" />
    <circle cx="6" cy="18" r="3" />
    <circle cx="18" cy="16" r="3" />
    <line x1="3" y1="3" x2="21" y2="21" stroke="white" strokeWidth="2" />
  </svg>
);

const SoundIcon = () => (
  <svg viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
    <path d="M12 4L8 8H4v8h4l4 4V4z" />
    <path d="M16 8a5 5 0 0 1 0 8M19 5a10 10 0 0 1 0 14" />
  </svg>
);

const SoundMuteIcon = () => (
  <svg viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
    <path d="M12 4L8 8H4v8h4l4 4V4z" />
    <line x1="17" y1="7" x2="17" y2="17" stroke="white" strokeWidth="2" />
    <line x1="21" y1="11" x2="21" y2="13" stroke="white" strokeWidth="2" />
    <line x1="3" y1="3" x2="21" y2="21" stroke="white" strokeWidth="2" />
  </svg>
);

const AudioController: React.FC = () => {
  const [isMusicMuted, setIsMusicMuted] = useState(false);
  const [isSfxMuted, setIsSfxMuted] = useState(false);
  const audioSystem = AudioSystem.getInstance();
  const { currentScreen } = useSelector((state: RootState) => state.game);
  
  // Load sound assets
  useEffect(() => {
    const loadRaceAudio = async () => {
      try {
        // Load music tracks
        await audioSystem.loadMusicTrack({
          id: 'menu_music',
          name: 'Menu Theme',
          src: '/assets/sounds/music/menu_theme.mp3',
          intensity: 'low',
          loop: true,
          volume: 0.6,
          category: 'menu'
        });
        
        await audioSystem.loadMusicTrack({
          id: 'race_music_intro',
          name: 'Race Intro',
          src: '/assets/sounds/music/race_intro.mp3',
          intensity: 'medium',
          loop: false,
          volume: 0.7,
          category: 'race'
        });
        
        await audioSystem.loadMusicTrack({
          id: 'race_music',
          name: 'Race Theme',
          src: '/assets/sounds/music/race_theme.mp3',
          intensity: 'high',
          loop: true,
          volume: 0.7,
          category: 'race'
        });
        
        await audioSystem.loadMusicTrack({
          id: 'victory_music',
          name: 'Victory Theme',
          src: '/assets/sounds/music/victory.mp3',
          intensity: 'medium',
          loop: false,
          volume: 0.6,
          category: 'race'
        });
        
        // Load sound effects
        await audioSystem.loadSoundEffect({
          id: 'ui_select',
          name: 'UI Select',
          src: '/assets/sounds/sfx/ui_select.mp3',
          volume: 0.5,
          loop: false
        });

        await audioSystem.loadSoundEffect({
          id: 'engine_idle',
          name: 'Engine Idle',
          src: '/assets/sounds/sfx/engine_idle.mp3',
          volume: 0.4,
          loop: true
        });

        await audioSystem.loadSoundEffect({
          id: 'tire_skid',
          name: 'Tire Skid',
          src: '/assets/sounds/sfx/tire_skid.mp3',
          volume: 0.5,
          loop: false
        });
        
        await audioSystem.loadSoundEffect({
          id: 'brake_skid',
          name: 'Brake Skid',
          src: '/assets/sounds/sfx/brake_skid.mp3',
          volume: 0.5
        });
        
        await audioSystem.loadSoundEffect({
          id: 'nitro_boost',
          name: 'Nitro Boost',
          src: '/assets/sounds/sfx/nitro_boost.mp3',
          volume: 0.7
        });
        
        await audioSystem.loadSoundEffect({
          id: 'collision',
          name: 'Collision',
          src: '/assets/sounds/sfx/collision.mp3',
          volume: 0.6
        });
        
        await audioSystem.loadSoundEffect({
          id: 'checkpoint',
          name: 'Checkpoint',
          src: '/assets/sounds/sfx/checkpoint.mp3',
          volume: 0.5
        });
        
        await audioSystem.loadSoundEffect({
          id: 'race_start_countdown',
          name: 'Race Start Countdown',
          src: '/assets/sounds/sfx/race_countdown.mp3',
          volume: 0.8
        });
        
        await audioSystem.loadSoundEffect({
          id: 'race_start',
          name: 'Race Start',
          src: '/assets/sounds/sfx/race_start.mp3',
          volume: 0.8
        });
        
        await audioSystem.loadSoundEffect({
          id: 'race_complete',
          name: 'Race Complete',
          src: '/assets/sounds/sfx/race_complete.mp3',
          volume: 0.8
        });
      } catch (error) {
        console.error('Failed to load audio assets:', error);
      }
    };
    
    loadRaceAudio();
  }, []);
  
  // Handle screen transitions for music changes
  useEffect(() => {
    // Use the appropriate music based on the current screen
    const screenMusicMap: Record<string, string> = {
      'main-menu': 'menu_music',
      'game-mode': 'menu_music',
      'customize': 'menu_music',
      'lab': 'menu_music',
      'race': 'race_music_intro',
      'story': 'menu_music'
    };
    
    const musicId = screenMusicMap[currentScreen];
    
    if (musicId && !isMusicMuted) {
      audioSystem.fadeOutCurrentMusic(1000);
      setTimeout(() => {
        audioSystem.playMusic(musicId);
      }, 1000);
    }
    
    // Clean up function
    return () => {
      // No need to stop music here, as we're using crossfade between screens
    };
  }, [currentScreen, isMusicMuted]);
  
  // Toggle music mute state
  const toggleMusic = useCallback(() => {
    const newMuteState = !isMusicMuted;
    setIsMusicMuted(newMuteState);
    
    if (newMuteState) {
      audioSystem.muteMusic();
    } else {
      audioSystem.unmuteMusic();
      
      // Resume music based on current screen
      const screenMusicMap: Record<string, string> = {
        'main-menu': 'menu_music',
        'game-mode': 'menu_music',
        'customize': 'customization_music',
        'lab': 'lab_music',
        'racing': 'race_music_intro',
        'story': 'story_music'
      };
      
      const musicId = screenMusicMap[currentScreen];
      if (musicId) {
        audioSystem.playMusic(musicId, 1000);
      }
    }
  }, [isMusicMuted, audioSystem, currentScreen]);
  
  // Toggle sound effects mute state
  const toggleSfx = useCallback(() => {
    const newMuteState = !isSfxMuted;
    setIsSfxMuted(newMuteState);
    
    if (newMuteState) {
      audioSystem.muteSoundEffects();
      // Play a muted UI sound to verify it's muted
      audioSystem.playSoundEffect('ui_click');
    } else {
      audioSystem.unmuteSoundEffects();
      // Play a UI sound to verify it's unmuted
      audioSystem.playSoundEffect('ui_click');
    }
  }, [isSfxMuted, audioSystem]);
  
  return (
    <AudioControlsContainer>
      <AudioButton 
        $active={!isMusicMuted} 
        onClick={toggleMusic}
        aria-label={isMusicMuted ? "Unmute Music" : "Mute Music"}
      >
        {isMusicMuted ? <MusicMuteIcon /> : <MusicIcon />}
      </AudioButton>
      
      <AudioButton 
        $active={!isSfxMuted} 
        onClick={toggleSfx}
        aria-label={isSfxMuted ? "Unmute Sound Effects" : "Mute Sound Effects"}
      >
        {isSfxMuted ? <SoundMuteIcon /> : <SoundIcon />}
      </AudioButton>
    </AudioControlsContainer>
  );
};

export default AudioController;
