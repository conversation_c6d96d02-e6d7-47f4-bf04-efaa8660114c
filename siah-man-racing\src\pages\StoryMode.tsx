import React, { useEffect, useState } from 'react';
import { useNavigate } from 'react-router-dom';
import { useDispatch, useSelector } from 'react-redux';
import styled, { keyframes } from 'styled-components';
import { setCurrentScreen, advanceStory } from '../store/gameSlice';
import { advanceChapter, markCutsceneSeen } from '../store/userSlice';
import { RootState } from '../store';
import { TTSSystem } from '../game/TTSSystem';

const cinematicPan = keyframes`
  0% { background-position: 0% 50%; }
  50% { background-position: 100% 50%; }
  100% { background-position: 0% 50%; }
`;

const StoryContainer = styled.div`
  width: 100%;
  height: 100vh;
  background: linear-gradient(rgba(10, 10, 30, 0.9), rgba(20, 20, 40, 0.9));
  color: white;
  display: flex;
  flex-direction: column;
  overflow: hidden;
  position: relative;
  animation: ${cinematicPan} 90s linear infinite;
  font-family: 'Exo 2', sans-serif;
`;

const StoryBackground = styled.div<{ $chapter: number }>`
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: ${props => {
    switch(props.$chapter) {
      case 1: return 'linear-gradient(135deg, #1a2a6c, #b21f1f, #fdbb2d)';
      case 2: return 'linear-gradient(135deg, #0f0c29, #302b63, #24243e)';
      case 3: return 'linear-gradient(135deg, #2c3e50, #4ca1af)';
      case 4: return 'linear-gradient(135deg, #8e0e00, #1f1c18)';
      case 5: return 'linear-gradient(135deg, #000428, #004e92)';
      default: return 'linear-gradient(135deg, #16222a, #3a6073)';
    }
  }};
  opacity: 0.8;
  z-index: -1;
`;

const DialogueBox = styled.div`
  position: absolute;
  bottom: 50px;
  left: 50px;
  right: 50px;
  background: rgba(0, 0, 0, 0.8);
  border-radius: 10px;
  padding: 20px;
  box-shadow: 0 0 20px rgba(0, 0, 0, 0.5);
`;

const CharacterName = styled.h3`
  margin: 0 0 10px 0;
  color: #fdbb2d;
  font-size: 1.5rem;
`;

const DialogueText = styled.p`
  margin: 0;
  font-size: 1.2rem;
  line-height: 1.5;
`;

const CharacterPortrait = styled.div<{ $character: string }>`
  position: absolute;
  bottom: 200px;
  width: 300px;
  height: 400px;
  background-color: rgba(0, 0, 0, 0.5);
  border-radius: 10px;
  overflow: hidden;
  
  ${props => props.$character === 'siah_man' ? 'left: 100px;' : 
             props.$character === 'dr_neal' ? 'left: 100px;' : // Assuming Dr. Neal also on left for now
             'right: 100px;'}

  ${props => props.$character === 'siah_man' && `
    background: url('/assets/Images/Siah_Image.jpeg') no-repeat center center;
    background-size: cover;
  `}

  ${props => props.$character === 'dr_neal' && `
    background: url('/assets/Images/Dr.Neal_SiahsDadImage.jpeg') no-repeat center center;
    background-size: cover;
  `}


  &:before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: ${props => {
      switch(props.$character) {
        case 'siah_man': return 'rgba(0, 0, 0, 0.3)';
        case 'dr_neal': return 'linear-gradient(to bottom, #1a2a6c, #4ca1af)';
        case 'max_velocity': return 'linear-gradient(to bottom, #8e0e00, #1f1c18)';
        default: return 'linear-gradient(to bottom, #16222a, #3a6073)';
      }
    }};
    opacity: 0.7;
    z-index: 1;
  }

  img {
    display: block;
    width: 100%;
    height: 100%;
    object-fit: cover;
    z-index: 2;
  }
`;

const ContinueButton = styled.button`
  position: absolute;
  bottom: 20px;
  right: 50px;
  background: rgba(0, 100, 200, 0.7);
  color: white;
  border: none;
  border-radius: 5px;
  padding: 10px 20px;
  cursor: pointer;
  font-size: 1rem;
  transition: all 0.2s ease;

  &:hover {
    background: rgba(0, 150, 250, 0.9);
  }
`;

const BackButton = styled.button`
  position: absolute;
  top: 20px;
  left: 20px;
  background: rgba(0, 0, 0, 0.5);
  color: white;
  border: none;
  border-radius: 5px;
  padding: 10px 15px;
  cursor: pointer;
  z-index: 10;
`;

const ChapterTitle = styled.h2`
  position: absolute;
  top: 50px;
  left: 0;
  right: 0;
  text-align: center;
  font-size: 2rem;
  text-transform: uppercase;
  letter-spacing: 3px;
  text-shadow: 0 0 10px rgba(0, 0, 0, 0.5);
`;

const storyContent = [
  [
    { character: 'dr_neal', name: 'Dr. Neal', text: "Siah, I've been working on something special in my MAD Laboratory. A formula that could change everything we know about racing.", audioSrc: '/assets/voices/drneal_story_ch1_line1.mp3' },
    { character: 'siah_man', name: 'Siah', text: "Dad, you always say that about your experiments. What makes this one different?", audioSrc: '/assets/voices/siah_story_ch1_line1.mp3' },
    { character: 'dr_neal', name: 'Dr. Neal', text: "This formula creates a connection between driver and vehicle unlike anything we've seen before. It's like... becoming one with the machine.", audioSrc: '/assets/voices/drneal_story_ch1_line2.mp3' },
    { character: 'siah_man', name: 'Siah', text: "Sounds dangerous. Have you tested it?", audioSrc: '/assets/voices/siah_story_ch1_line2.mp3' },
    { character: 'dr_neal', name: 'Dr. Neal', text: "Well, that's why I called you here. I need someone who understands cars on an intuitive level.", audioSrc: '/assets/voices/drneal_story_ch1_line3.mp3' },
    { character: 'siah_man', name: 'Siah', text: "Wait, you want ME to test it? Dad, I'm not sure about this...", audioSrc: '/assets/voices/siah_story_ch1_line3.mp3' },
    { character: 'dr_neal', name: 'Dr. Neal', text: "Trust me, son. This could be revolutionary. With the racing championship coming up, this could be your chance to show everyone what you're capable of.", audioSrc: '/assets/voices/drneal_story_ch1_line4.mp3' },
    { character: 'siah_man', name: 'Siah', text: "...Alright. Let's do it. What's the worst that could happen?", audioSrc: '/assets/voices/siah_story_ch1_line4.mp3' },
  ],
  [
    { character: 'dr_neal', name: 'Dr. Neal', text: "The formula is ready. Are you sure you want to proceed, Siah?", audioSrc: '/assets/voices/drneal_story_ch2_line1.mp3' },
    { character: 'siah_man', name: 'Siah', text: "I'm ready, Dad. Let's see what this 'Car Whisperer' formula can do.", audioSrc: '/assets/voices/siah_story_ch2_line1.mp3' },
    { character: 'dr_neal', name: 'Dr. Neal', text: "Take a deep breath. This might feel... unusual.", audioSrc: '/assets/voices/drneal_story_ch2_line2.mp3' },
    { character: 'siah_man', name: 'Siah', text: "I feel... strange. Like I can hear the engine talking to me. The metal, the gears, they're all... communicating somehow.", audioSrc: '/assets/voices/siah_story_ch2_line2.mp3' },
    { character: 'dr_neal', name: 'Dr. Neal', text: "It's working! The neural pathways are forming. You're becoming the Car Whisperer!", audioSrc: '/assets/voices/drneal_story_ch2_line3.mp3' },
    { character: 'siah_man', name: 'Siah', text: "I understand the vehicle now. Not just how it works, but how it feels. This is incredible!", audioSrc: '/assets/voices/siah_story_ch2_line3.mp3' },
    { character: 'dr_neal', name: 'Dr. Neal', text: "We need to test your abilities. Head to the track and see what you can do with this new connection.", audioSrc: '/assets/voices/drneal_story_ch2_line4.mp3' }, // Assuming drneal_story_ch2_line4.mp3 exists
  ],
  [
    { character: 'max_velocity', name: 'Max Velocity', text: "Well, well, if it isn't Dr. Neal's kid. Heard you've been making some noise about a new racing technique.", audioSrc: '/assets/voices/maxvelocity_story_ch3_line1.mp3' },
    { character: 'siah_man', name: 'Siah', text: "I'm just here to race, Max. Nothing more.", audioSrc: '/assets/voices/siah_story_ch3_line1.mp3' },
    { character: 'max_velocity', name: 'Max Velocity', text: "Racing is my territory. Your dad's crazy experiments won't change that. How about a little demonstration?", audioSrc: '/assets/voices/maxvelocity_story_ch3_line2.mp3' },
    { character: 'siah_man', name: 'Siah', text: "You're on. One lap around the MAD Lab Circuit.", audioSrc: '/assets/voices/siah_story_ch3_line2.mp3' },
    { character: 'max_velocity', name: 'Max Velocity', text: "When I win, you and your dad stay away from the championship. Deal?", audioSrc: '/assets/voices/maxvelocity_story_ch3_line3.mp3' },
    { character: 'siah_man', name: 'Siah', text: "And when I win, you stop calling my dad crazy. Deal.", audioSrc: '/assets/voices/siah_story_ch3_line3.mp3' },
  ],
];

const chapterTitles = [
  "Chapter 1: The Beginning",
  "Chapter 2: The Transformation",
  "Chapter 3: First Challenge",
  "Chapter 4: Mastering the Power",
  "Chapter 5: The Championship",
];

const StoryMode: React.FC = () => {
  const navigate = useNavigate();
  const dispatch = useDispatch();
  
  const currentChapter = useSelector((state: RootState) => state.user.storyProgress.currentChapter);
  
  const [dialogueIndex, setDialogueIndex] = useState(0);
  const [showChapterTitle, setShowChapterTitle] = useState(true);
  
  const currentChapterContent = storyContent[currentChapter - 1] || [];
  const currentDialogue = currentChapterContent[dialogueIndex];
  
  useEffect(() => {
    dispatch(setCurrentScreen('story'));
    
    setShowChapterTitle(true); // Ensure chapter title is shown when chapter changes
    const timer = setTimeout(() => {
      setShowChapterTitle(false);
    }, 3000);
    
    return () => {
      clearTimeout(timer);
      TTSSystem.getInstance().stop();
    };
  }, [dispatch, currentChapter]); // Rerun when currentChapter changes

  useEffect(() => {
    if (!showChapterTitle && currentDialogue) {
      TTSSystem.getInstance().stop();

      // Use the enhanced voice synchronization
      TTSSystem.getInstance().playStoryDialogue(
        currentDialogue.character,
        currentDialogue.text,
        currentDialogue.audioSrc,
        () => {
          console.log(`Completed dialogue: ${currentDialogue.character} - "${currentDialogue.text}"`);
        }
      );
    }
    // No specific cleanup needed here as the main useEffect handles stopping TTS on chapter change
    // or component unmount. Starting TTS is handled when dialogue becomes available.
  }, [dialogueIndex, showChapterTitle, currentDialogue]);

  const handleContinue = () => {
    if (dialogueIndex < currentChapterContent.length - 1) {
      setDialogueIndex(dialogueIndex + 1);
      // TTS for the next line will be triggered by the useEffect above
    } else {
      const cutsceneId = `chapter_${currentChapter}_complete`;
      dispatch(markCutsceneSeen(cutsceneId));
      
      if (currentChapter < storyContent.length) {
        dispatch(advanceChapter());
        dispatch(advanceStory()); // This updates currentChapter, triggering the first useEffect
        setDialogueIndex(0); // Reset for the new chapter
        // setShowChapterTitle(true); // Already handled by the first useEffect
        
        if (currentChapter === 3) { // Check against the chapter *before* advancing
          navigate('/race');
        }
      } else {
        navigate('/');
      }
    }
  };
  
  return (
    <StoryContainer>
      <StoryBackground $chapter={currentChapter} />
      
      <BackButton onClick={() => navigate('/')}>
        Back to Menu
      </BackButton>
      
      {showChapterTitle ? (
        <ChapterTitle>{chapterTitles[currentChapter - 1] || `Chapter ${currentChapter}`}</ChapterTitle>
      ) : (
        <>
          {currentDialogue && (
            <>
              <CharacterPortrait $character={currentDialogue.character} />
              <DialogueBox>
                <CharacterName>{currentDialogue.name}</CharacterName>
                <DialogueText>{currentDialogue.text}</DialogueText>
              </DialogueBox>
              <ContinueButton onClick={handleContinue}>
                Continue
              </ContinueButton>
            </>
          )}
        </>
      )}
    </StoryContainer>
  );
};

export default StoryMode;
