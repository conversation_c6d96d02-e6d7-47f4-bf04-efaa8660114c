{"ast": null, "code": "import { Vector3 } from 'three';\nconst temp = /* @__PURE__ */new Vector3();\nconst temp1 = /* @__PURE__ */new Vector3();\nexport function closestPointToPoint(bvh, point, target = {}, minThreshold = 0, maxThreshold = Infinity) {\n  // early out if under minThreshold\n  // skip checking if over maxThreshold\n  // set minThreshold = maxThreshold to quickly check if a point is within a threshold\n  // returns Infinity if no value found\n  const minThresholdSq = minThreshold * minThreshold;\n  const maxThresholdSq = maxThreshold * maxThreshold;\n  let closestDistanceSq = Infinity;\n  let closestDistanceTriIndex = null;\n  bvh.shapecast({\n    boundsTraverseOrder: box => {\n      temp.copy(point).clamp(box.min, box.max);\n      return temp.distanceToSquared(point);\n    },\n    intersectsBounds: (box, isLeaf, score) => {\n      return score < closestDistanceSq && score < maxThresholdSq;\n    },\n    intersectsTriangle: (tri, triIndex) => {\n      tri.closestPointToPoint(point, temp);\n      const distSq = point.distanceToSquared(temp);\n      if (distSq < closestDistanceSq) {\n        temp1.copy(temp);\n        closestDistanceSq = distSq;\n        closestDistanceTriIndex = triIndex;\n      }\n      if (distSq < minThresholdSq) {\n        return true;\n      } else {\n        return false;\n      }\n    }\n  });\n  if (closestDistanceSq === Infinity) return null;\n  const closestDistance = Math.sqrt(closestDistanceSq);\n  if (!target.point) target.point = temp1.clone();else target.point.copy(temp1);\n  target.distance = closestDistance, target.faceIndex = closestDistanceTriIndex;\n  return target;\n}", "map": {"version": 3, "names": ["Vector3", "temp", "temp1", "closestPointToPoint", "bvh", "point", "target", "min<PERSON><PERSON><PERSON><PERSON>", "max<PERSON><PERSON><PERSON><PERSON>", "Infinity", "minThresholdSq", "maxThresholdSq", "closestDistanceSq", "closestDistanceTriIndex", "shapecast", "boundsTraverseOrder", "box", "copy", "clamp", "min", "max", "distanceToSquared", "intersectsBounds", "<PERSON><PERSON><PERSON><PERSON>", "score", "intersectsTriangle", "tri", "triIndex", "distSq", "closestDistance", "Math", "sqrt", "clone", "distance", "faceIndex"], "sources": ["C:/Users/<USER>/Documents/Apps_Creations/SiahMan_Racing4Speed_WebGame/siah-man-racing/node_modules/three-mesh-bvh/src/core/cast/closestPointToPoint.js"], "sourcesContent": ["import { Vector3 } from 'three';\n\nconst temp = /* @__PURE__ */ new Vector3();\nconst temp1 = /* @__PURE__ */ new Vector3();\n\nexport function closestPointToPoint(\n\tbvh,\n\tpoint,\n\ttarget = { },\n\tminThreshold = 0,\n\tmaxThreshold = Infinity,\n) {\n\n\t// early out if under minThreshold\n\t// skip checking if over maxThreshold\n\t// set minThreshold = maxThreshold to quickly check if a point is within a threshold\n\t// returns Infinity if no value found\n\tconst minThresholdSq = minThreshold * minThreshold;\n\tconst maxThresholdSq = maxThreshold * maxThreshold;\n\tlet closestDistanceSq = Infinity;\n\tlet closestDistanceTriIndex = null;\n\tbvh.shapecast(\n\n\t\t{\n\n\t\t\tboundsTraverseOrder: box => {\n\n\t\t\t\ttemp.copy( point ).clamp( box.min, box.max );\n\t\t\t\treturn temp.distanceToSquared( point );\n\n\t\t\t},\n\n\t\t\tintersectsBounds: ( box, isLeaf, score ) => {\n\n\t\t\t\treturn score < closestDistanceSq && score < maxThresholdSq;\n\n\t\t\t},\n\n\t\t\tintersectsTriangle: ( tri, triIndex ) => {\n\n\t\t\t\ttri.closestPointToPoint( point, temp );\n\t\t\t\tconst distSq = point.distanceToSquared( temp );\n\t\t\t\tif ( distSq < closestDistanceSq ) {\n\n\t\t\t\t\ttemp1.copy( temp );\n\t\t\t\t\tclosestDistanceSq = distSq;\n\t\t\t\t\tclosestDistanceTriIndex = triIndex;\n\n\t\t\t\t}\n\n\t\t\t\tif ( distSq < minThresholdSq ) {\n\n\t\t\t\t\treturn true;\n\n\t\t\t\t} else {\n\n\t\t\t\t\treturn false;\n\n\t\t\t\t}\n\n\t\t\t},\n\n\t\t}\n\n\t);\n\n\tif ( closestDistanceSq === Infinity ) return null;\n\n\tconst closestDistance = Math.sqrt( closestDistanceSq );\n\n\tif ( ! target.point ) target.point = temp1.clone();\n\telse target.point.copy( temp1 );\n\ttarget.distance = closestDistance,\n\ttarget.faceIndex = closestDistanceTriIndex;\n\n\treturn target;\n\n}\n"], "mappings": "AAAA,SAASA,OAAO,QAAQ,OAAO;AAE/B,MAAMC,IAAI,GAAG,eAAgB,IAAID,OAAO,CAAC,CAAC;AAC1C,MAAME,KAAK,GAAG,eAAgB,IAAIF,OAAO,CAAC,CAAC;AAE3C,OAAO,SAASG,mBAAmBA,CAClCC,GAAG,EACHC,KAAK,EACLC,MAAM,GAAG,CAAE,CAAC,EACZC,YAAY,GAAG,CAAC,EAChBC,YAAY,GAAGC,QAAQ,EACtB;EAED;EACA;EACA;EACA;EACA,MAAMC,cAAc,GAAGH,YAAY,GAAGA,YAAY;EAClD,MAAMI,cAAc,GAAGH,YAAY,GAAGA,YAAY;EAClD,IAAII,iBAAiB,GAAGH,QAAQ;EAChC,IAAII,uBAAuB,GAAG,IAAI;EAClCT,GAAG,CAACU,SAAS,CAEZ;IAECC,mBAAmB,EAAEC,GAAG,IAAI;MAE3Bf,IAAI,CAACgB,IAAI,CAAEZ,KAAM,CAAC,CAACa,KAAK,CAAEF,GAAG,CAACG,GAAG,EAAEH,GAAG,CAACI,GAAI,CAAC;MAC5C,OAAOnB,IAAI,CAACoB,iBAAiB,CAAEhB,KAAM,CAAC;IAEvC,CAAC;IAEDiB,gBAAgB,EAAEA,CAAEN,GAAG,EAAEO,MAAM,EAAEC,KAAK,KAAM;MAE3C,OAAOA,KAAK,GAAGZ,iBAAiB,IAAIY,KAAK,GAAGb,cAAc;IAE3D,CAAC;IAEDc,kBAAkB,EAAEA,CAAEC,GAAG,EAAEC,QAAQ,KAAM;MAExCD,GAAG,CAACvB,mBAAmB,CAAEE,KAAK,EAAEJ,IAAK,CAAC;MACtC,MAAM2B,MAAM,GAAGvB,KAAK,CAACgB,iBAAiB,CAAEpB,IAAK,CAAC;MAC9C,IAAK2B,MAAM,GAAGhB,iBAAiB,EAAG;QAEjCV,KAAK,CAACe,IAAI,CAAEhB,IAAK,CAAC;QAClBW,iBAAiB,GAAGgB,MAAM;QAC1Bf,uBAAuB,GAAGc,QAAQ;MAEnC;MAEA,IAAKC,MAAM,GAAGlB,cAAc,EAAG;QAE9B,OAAO,IAAI;MAEZ,CAAC,MAAM;QAEN,OAAO,KAAK;MAEb;IAED;EAED,CAED,CAAC;EAED,IAAKE,iBAAiB,KAAKH,QAAQ,EAAG,OAAO,IAAI;EAEjD,MAAMoB,eAAe,GAAGC,IAAI,CAACC,IAAI,CAAEnB,iBAAkB,CAAC;EAEtD,IAAK,CAAEN,MAAM,CAACD,KAAK,EAAGC,MAAM,CAACD,KAAK,GAAGH,KAAK,CAAC8B,KAAK,CAAC,CAAC,CAAC,KAC9C1B,MAAM,CAACD,KAAK,CAACY,IAAI,CAAEf,KAAM,CAAC;EAC/BI,MAAM,CAAC2B,QAAQ,GAAGJ,eAAe,EACjCvB,MAAM,CAAC4B,SAAS,GAAGrB,uBAAuB;EAE1C,OAAOP,MAAM;AAEd", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}