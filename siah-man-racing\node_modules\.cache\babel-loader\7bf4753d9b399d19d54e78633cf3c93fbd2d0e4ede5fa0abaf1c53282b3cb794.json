{"ast": null, "code": "function SDFGenerator() {\n  var exports = function (exports) {\n    /**\n     * Find the point on a quadratic bezier curve at t where t is in the range [0, 1]\n     */\n    function pointOnQuadraticBezier(x0, y0, x1, y1, x2, y2, t, pointOut) {\n      var t2 = 1 - t;\n      pointOut.x = t2 * t2 * x0 + 2 * t2 * t * x1 + t * t * x2;\n      pointOut.y = t2 * t2 * y0 + 2 * t2 * t * y1 + t * t * y2;\n    }\n\n    /**\n     * Find the point on a cubic bezier curve at t where t is in the range [0, 1]\n     */\n    function pointOnCubicBezier(x0, y0, x1, y1, x2, y2, x3, y3, t, pointOut) {\n      var t2 = 1 - t;\n      pointOut.x = t2 * t2 * t2 * x0 + 3 * t2 * t2 * t * x1 + 3 * t2 * t * t * x2 + t * t * t * x3;\n      pointOut.y = t2 * t2 * t2 * y0 + 3 * t2 * t2 * t * y1 + 3 * t2 * t * t * y2 + t * t * t * y3;\n    }\n\n    /**\n     * Parse a path string into its constituent line/curve commands, invoking a callback for each.\n     * @param {string} pathString - An SVG-like path string to parse; should only contain commands: M/L/Q/C/Z\n     * @param {function(\n     *   command: 'L'|'Q'|'C',\n     *   startX: number,\n     *   startY: number,\n     *   endX: number,\n     *   endY: number,\n     *   ctrl1X?: number,\n     *   ctrl1Y?: number,\n     *   ctrl2X?: number,\n     *   ctrl2Y?: number\n     * )} commandCallback - A callback function that will be called once for each parsed path command, passing the\n     *                      command identifier (only L/Q/C commands) and its numeric arguments.\n     */\n    function forEachPathCommand(pathString, commandCallback) {\n      var segmentRE = /([MLQCZ])([^MLQCZ]*)/g;\n      var match, firstX, firstY, prevX, prevY;\n      while (match = segmentRE.exec(pathString)) {\n        var args = match[2].replace(/^\\s*|\\s*$/g, '').split(/[,\\s]+/).map(function (v) {\n          return parseFloat(v);\n        });\n        switch (match[1]) {\n          case 'M':\n            prevX = firstX = args[0];\n            prevY = firstY = args[1];\n            break;\n          case 'L':\n            if (args[0] !== prevX || args[1] !== prevY) {\n              // yup, some fonts have zero-length line commands\n              commandCallback('L', prevX, prevY, prevX = args[0], prevY = args[1]);\n            }\n            break;\n          case 'Q':\n            {\n              commandCallback('Q', prevX, prevY, prevX = args[2], prevY = args[3], args[0], args[1]);\n              break;\n            }\n          case 'C':\n            {\n              commandCallback('C', prevX, prevY, prevX = args[4], prevY = args[5], args[0], args[1], args[2], args[3]);\n              break;\n            }\n          case 'Z':\n            if (prevX !== firstX || prevY !== firstY) {\n              commandCallback('L', prevX, prevY, firstX, firstY);\n            }\n            break;\n        }\n      }\n    }\n\n    /**\n     * Convert a path string to a series of straight line segments\n     * @param {string} pathString - An SVG-like path string to parse; should only contain commands: M/L/Q/C/Z\n     * @param {function(x1:number, y1:number, x2:number, y2:number)} segmentCallback - A callback\n     *        function that will be called once for every line segment\n     * @param {number} [curvePoints] - How many straight line segments to use when approximating a\n     *        bezier curve in the path. Defaults to 16.\n     */\n    function pathToLineSegments(pathString, segmentCallback, curvePoints) {\n      if (curvePoints === void 0) curvePoints = 16;\n      var tempPoint = {\n        x: 0,\n        y: 0\n      };\n      forEachPathCommand(pathString, function (command, startX, startY, endX, endY, ctrl1X, ctrl1Y, ctrl2X, ctrl2Y) {\n        switch (command) {\n          case 'L':\n            segmentCallback(startX, startY, endX, endY);\n            break;\n          case 'Q':\n            {\n              var prevCurveX = startX;\n              var prevCurveY = startY;\n              for (var i = 1; i < curvePoints; i++) {\n                pointOnQuadraticBezier(startX, startY, ctrl1X, ctrl1Y, endX, endY, i / (curvePoints - 1), tempPoint);\n                segmentCallback(prevCurveX, prevCurveY, tempPoint.x, tempPoint.y);\n                prevCurveX = tempPoint.x;\n                prevCurveY = tempPoint.y;\n              }\n              break;\n            }\n          case 'C':\n            {\n              var prevCurveX$1 = startX;\n              var prevCurveY$1 = startY;\n              for (var i$1 = 1; i$1 < curvePoints; i$1++) {\n                pointOnCubicBezier(startX, startY, ctrl1X, ctrl1Y, ctrl2X, ctrl2Y, endX, endY, i$1 / (curvePoints - 1), tempPoint);\n                segmentCallback(prevCurveX$1, prevCurveY$1, tempPoint.x, tempPoint.y);\n                prevCurveX$1 = tempPoint.x;\n                prevCurveY$1 = tempPoint.y;\n              }\n              break;\n            }\n        }\n      });\n    }\n    var viewportQuadVertex = \"precision highp float;attribute vec2 aUV;varying vec2 vUV;void main(){vUV=aUV;gl_Position=vec4(mix(vec2(-1.0),vec2(1.0),aUV),0.0,1.0);}\";\n    var copyTexFragment = \"precision highp float;uniform sampler2D tex;varying vec2 vUV;void main(){gl_FragColor=texture2D(tex,vUV);}\";\n    var cache = new WeakMap();\n    var glContextParams = {\n      premultipliedAlpha: false,\n      preserveDrawingBuffer: true,\n      antialias: false,\n      depth: false\n    };\n\n    /**\n     * This is a little helper library for WebGL. It assists with state management for a GL context.\n     * It's pretty tightly wrapped to the needs of this package, not very general-purpose.\n     *\n     * @param { WebGLRenderingContext | HTMLCanvasElement | OffscreenCanvas } glOrCanvas - the GL context to wrap\n     * @param { ({gl, getExtension, withProgram, withTexture, withTextureFramebuffer, handleContextLoss}) => void } callback\n     */\n    function withWebGLContext(glOrCanvas, callback) {\n      var gl = glOrCanvas.getContext ? glOrCanvas.getContext('webgl', glContextParams) : glOrCanvas;\n      var wrapper = cache.get(gl);\n      if (!wrapper) {\n        var isWebGL2 = typeof WebGL2RenderingContext !== 'undefined' && gl instanceof WebGL2RenderingContext;\n        var extensions = {};\n        var programs = {};\n        var textures = {};\n        var textureUnit = -1;\n        var framebufferStack = [];\n        gl.canvas.addEventListener('webglcontextlost', function (e) {\n          handleContextLoss();\n          e.preventDefault();\n        }, false);\n        function getExtension(name) {\n          var ext = extensions[name];\n          if (!ext) {\n            ext = extensions[name] = gl.getExtension(name);\n            if (!ext) {\n              throw new Error(name + \" not supported\");\n            }\n          }\n          return ext;\n        }\n        function compileShader(src, type) {\n          var shader = gl.createShader(type);\n          gl.shaderSource(shader, src);\n          gl.compileShader(shader);\n          // const status = gl.getShaderParameter(shader, gl.COMPILE_STATUS)\n          // if (!status && !gl.isContextLost()) {\n          //   throw new Error(gl.getShaderInfoLog(shader).trim())\n          // }\n          return shader;\n        }\n        function withProgram(name, vert, frag, func) {\n          if (!programs[name]) {\n            var attributes = {};\n            var uniforms = {};\n            var program = gl.createProgram();\n            gl.attachShader(program, compileShader(vert, gl.VERTEX_SHADER));\n            gl.attachShader(program, compileShader(frag, gl.FRAGMENT_SHADER));\n            gl.linkProgram(program);\n            programs[name] = {\n              program: program,\n              transaction: function transaction(func) {\n                gl.useProgram(program);\n                func({\n                  setUniform: function setUniform(type, name) {\n                    var values = [],\n                      len = arguments.length - 2;\n                    while (len-- > 0) values[len] = arguments[len + 2];\n                    var uniformLoc = uniforms[name] || (uniforms[name] = gl.getUniformLocation(program, name));\n                    gl[\"uniform\" + type].apply(gl, [uniformLoc].concat(values));\n                  },\n                  setAttribute: function setAttribute(name, size, usage, instancingDivisor, data) {\n                    var attr = attributes[name];\n                    if (!attr) {\n                      attr = attributes[name] = {\n                        buf: gl.createBuffer(),\n                        // TODO should we destroy our buffers?\n                        loc: gl.getAttribLocation(program, name),\n                        data: null\n                      };\n                    }\n                    gl.bindBuffer(gl.ARRAY_BUFFER, attr.buf);\n                    gl.vertexAttribPointer(attr.loc, size, gl.FLOAT, false, 0, 0);\n                    gl.enableVertexAttribArray(attr.loc);\n                    if (isWebGL2) {\n                      gl.vertexAttribDivisor(attr.loc, instancingDivisor);\n                    } else {\n                      getExtension('ANGLE_instanced_arrays').vertexAttribDivisorANGLE(attr.loc, instancingDivisor);\n                    }\n                    if (data !== attr.data) {\n                      gl.bufferData(gl.ARRAY_BUFFER, data, usage);\n                      attr.data = data;\n                    }\n                  }\n                });\n              }\n            };\n          }\n          programs[name].transaction(func);\n        }\n        function withTexture(name, func) {\n          textureUnit++;\n          try {\n            gl.activeTexture(gl.TEXTURE0 + textureUnit);\n            var texture = textures[name];\n            if (!texture) {\n              texture = textures[name] = gl.createTexture();\n              gl.bindTexture(gl.TEXTURE_2D, texture);\n              gl.texParameteri(gl.TEXTURE_2D, gl.TEXTURE_MIN_FILTER, gl.NEAREST);\n              gl.texParameteri(gl.TEXTURE_2D, gl.TEXTURE_MAG_FILTER, gl.NEAREST);\n            }\n            gl.bindTexture(gl.TEXTURE_2D, texture);\n            func(texture, textureUnit);\n          } finally {\n            textureUnit--;\n          }\n        }\n        function withTextureFramebuffer(texture, textureUnit, func) {\n          var framebuffer = gl.createFramebuffer();\n          framebufferStack.push(framebuffer);\n          gl.bindFramebuffer(gl.FRAMEBUFFER, framebuffer);\n          gl.activeTexture(gl.TEXTURE0 + textureUnit);\n          gl.bindTexture(gl.TEXTURE_2D, texture);\n          gl.framebufferTexture2D(gl.FRAMEBUFFER, gl.COLOR_ATTACHMENT0, gl.TEXTURE_2D, texture, 0);\n          try {\n            func(framebuffer);\n          } finally {\n            gl.deleteFramebuffer(framebuffer);\n            gl.bindFramebuffer(gl.FRAMEBUFFER, framebufferStack[--framebufferStack.length - 1] || null);\n          }\n        }\n        function handleContextLoss() {\n          extensions = {};\n          programs = {};\n          textures = {};\n          textureUnit = -1;\n          framebufferStack.length = 0;\n        }\n        cache.set(gl, wrapper = {\n          gl: gl,\n          isWebGL2: isWebGL2,\n          getExtension: getExtension,\n          withProgram: withProgram,\n          withTexture: withTexture,\n          withTextureFramebuffer: withTextureFramebuffer,\n          handleContextLoss: handleContextLoss\n        });\n      }\n      callback(wrapper);\n    }\n    function renderImageData(glOrCanvas, imageData, x, y, width, height, channels, framebuffer) {\n      if (channels === void 0) channels = 15;\n      if (framebuffer === void 0) framebuffer = null;\n      withWebGLContext(glOrCanvas, function (ref) {\n        var gl = ref.gl;\n        var withProgram = ref.withProgram;\n        var withTexture = ref.withTexture;\n        withTexture('copy', function (tex, texUnit) {\n          gl.texImage2D(gl.TEXTURE_2D, 0, gl.RGBA, width, height, 0, gl.RGBA, gl.UNSIGNED_BYTE, imageData);\n          withProgram('copy', viewportQuadVertex, copyTexFragment, function (ref) {\n            var setUniform = ref.setUniform;\n            var setAttribute = ref.setAttribute;\n            setAttribute('aUV', 2, gl.STATIC_DRAW, 0, new Float32Array([0, 0, 2, 0, 0, 2]));\n            setUniform('1i', 'image', texUnit);\n            gl.bindFramebuffer(gl.FRAMEBUFFER, framebuffer || null);\n            gl.disable(gl.BLEND);\n            gl.colorMask(channels & 8, channels & 4, channels & 2, channels & 1);\n            gl.viewport(x, y, width, height);\n            gl.scissor(x, y, width, height);\n            gl.drawArrays(gl.TRIANGLES, 0, 3);\n          });\n        });\n      });\n    }\n\n    /**\n     * Resizing a canvas clears its contents; this utility copies the previous contents over.\n     * @param canvas\n     * @param newWidth\n     * @param newHeight\n     */\n    function resizeWebGLCanvasWithoutClearing(canvas, newWidth, newHeight) {\n      var width = canvas.width;\n      var height = canvas.height;\n      withWebGLContext(canvas, function (ref) {\n        var gl = ref.gl;\n        var data = new Uint8Array(width * height * 4);\n        gl.readPixels(0, 0, width, height, gl.RGBA, gl.UNSIGNED_BYTE, data);\n        canvas.width = newWidth;\n        canvas.height = newHeight;\n        renderImageData(gl, data, 0, 0, width, height);\n      });\n    }\n    var webglUtils = /*#__PURE__*/Object.freeze({\n      __proto__: null,\n      withWebGLContext: withWebGLContext,\n      renderImageData: renderImageData,\n      resizeWebGLCanvasWithoutClearing: resizeWebGLCanvasWithoutClearing\n    });\n    function generate$2(sdfWidth, sdfHeight, path, viewBox, maxDistance, sdfExponent) {\n      if (sdfExponent === void 0) sdfExponent = 1;\n      var textureData = new Uint8Array(sdfWidth * sdfHeight);\n      var viewBoxWidth = viewBox[2] - viewBox[0];\n      var viewBoxHeight = viewBox[3] - viewBox[1];\n\n      // Decompose all paths into straight line segments and add them to an index\n      var segments = [];\n      pathToLineSegments(path, function (x1, y1, x2, y2) {\n        segments.push({\n          x1: x1,\n          y1: y1,\n          x2: x2,\n          y2: y2,\n          minX: Math.min(x1, x2),\n          minY: Math.min(y1, y2),\n          maxX: Math.max(x1, x2),\n          maxY: Math.max(y1, y2)\n        });\n      });\n\n      // Sort segments by maxX, this will let us short-circuit some loops below\n      segments.sort(function (a, b) {\n        return a.maxX - b.maxX;\n      });\n\n      // For each target SDF texel, find the distance from its center to its nearest line segment,\n      // map that distance to an alpha value, and write that alpha to the texel\n      for (var sdfX = 0; sdfX < sdfWidth; sdfX++) {\n        for (var sdfY = 0; sdfY < sdfHeight; sdfY++) {\n          var signedDist = findNearestSignedDistance(viewBox[0] + viewBoxWidth * (sdfX + 0.5) / sdfWidth, viewBox[1] + viewBoxHeight * (sdfY + 0.5) / sdfHeight);\n\n          // Use an exponential scale to ensure the texels very near the glyph path have adequate\n          // precision, while allowing the distance field to cover the entire texture, given that\n          // there are only 8 bits available. Formula visualized: https://www.desmos.com/calculator/uiaq5aqiam\n          var alpha = Math.pow(1 - Math.abs(signedDist) / maxDistance, sdfExponent) / 2;\n          if (signedDist < 0) {\n            alpha = 1 - alpha;\n          }\n          alpha = Math.max(0, Math.min(255, Math.round(alpha * 255))); //clamp\n          textureData[sdfY * sdfWidth + sdfX] = alpha;\n        }\n      }\n      return textureData;\n\n      /**\n       * For a given x/y, search the index for the closest line segment and return\n       * its signed distance. Negative = inside, positive = outside, zero = on edge\n       * @param x\n       * @param y\n       * @returns {number}\n       */\n      function findNearestSignedDistance(x, y) {\n        var closestDistSq = Infinity;\n        var closestDist = Infinity;\n        for (var i = segments.length; i--;) {\n          var seg = segments[i];\n          if (seg.maxX + closestDist <= x) {\n            break;\n          } //sorting by maxX means no more can be closer, so we can short-circuit\n          if (x + closestDist > seg.minX && y - closestDist < seg.maxY && y + closestDist > seg.minY) {\n            var distSq = absSquareDistanceToLineSegment(x, y, seg.x1, seg.y1, seg.x2, seg.y2);\n            if (distSq < closestDistSq) {\n              closestDistSq = distSq;\n              closestDist = Math.sqrt(closestDistSq);\n            }\n          }\n        }\n\n        // Flip to negative distance if inside the poly\n        if (isPointInPoly(x, y)) {\n          closestDist = -closestDist;\n        }\n        return closestDist;\n      }\n\n      /**\n       * Determine whether the given point lies inside or outside the glyph. Uses a simple\n       * winding-number ray casting algorithm using a ray pointing east from the point.\n       */\n      function isPointInPoly(x, y) {\n        var winding = 0;\n        for (var i = segments.length; i--;) {\n          var seg = segments[i];\n          if (seg.maxX <= x) {\n            break;\n          } //sorting by maxX means no more can cross, so we can short-circuit\n          var intersects = seg.y1 > y !== seg.y2 > y && x < (seg.x2 - seg.x1) * (y - seg.y1) / (seg.y2 - seg.y1) + seg.x1;\n          if (intersects) {\n            winding += seg.y1 < seg.y2 ? 1 : -1;\n          }\n        }\n        return winding !== 0;\n      }\n    }\n    function generateIntoCanvas$2(sdfWidth, sdfHeight, path, viewBox, maxDistance, sdfExponent, canvas, x, y, channel) {\n      if (sdfExponent === void 0) sdfExponent = 1;\n      if (x === void 0) x = 0;\n      if (y === void 0) y = 0;\n      if (channel === void 0) channel = 0;\n      generateIntoFramebuffer$1(sdfWidth, sdfHeight, path, viewBox, maxDistance, sdfExponent, canvas, null, x, y, channel);\n    }\n    function generateIntoFramebuffer$1(sdfWidth, sdfHeight, path, viewBox, maxDistance, sdfExponent, glOrCanvas, framebuffer, x, y, channel) {\n      if (sdfExponent === void 0) sdfExponent = 1;\n      if (x === void 0) x = 0;\n      if (y === void 0) y = 0;\n      if (channel === void 0) channel = 0;\n      var data = generate$2(sdfWidth, sdfHeight, path, viewBox, maxDistance, sdfExponent);\n      // Expand single-channel data to rbga\n      var rgbaData = new Uint8Array(data.length * 4);\n      for (var i = 0; i < data.length; i++) {\n        rgbaData[i * 4 + channel] = data[i];\n      }\n      renderImageData(glOrCanvas, rgbaData, x, y, sdfWidth, sdfHeight, 1 << 3 - channel, framebuffer);\n    }\n\n    /**\n     * Find the absolute distance from a point to a line segment at closest approach\n     */\n    function absSquareDistanceToLineSegment(x, y, lineX0, lineY0, lineX1, lineY1) {\n      var ldx = lineX1 - lineX0;\n      var ldy = lineY1 - lineY0;\n      var lengthSq = ldx * ldx + ldy * ldy;\n      var t = lengthSq ? Math.max(0, Math.min(1, ((x - lineX0) * ldx + (y - lineY0) * ldy) / lengthSq)) : 0;\n      var dx = x - (lineX0 + t * ldx);\n      var dy = y - (lineY0 + t * ldy);\n      return dx * dx + dy * dy;\n    }\n    var javascript = /*#__PURE__*/Object.freeze({\n      __proto__: null,\n      generate: generate$2,\n      generateIntoCanvas: generateIntoCanvas$2,\n      generateIntoFramebuffer: generateIntoFramebuffer$1\n    });\n    var mainVertex = \"precision highp float;uniform vec4 uGlyphBounds;attribute vec2 aUV;attribute vec4 aLineSegment;varying vec4 vLineSegment;varying vec2 vGlyphXY;void main(){vLineSegment=aLineSegment;vGlyphXY=mix(uGlyphBounds.xy,uGlyphBounds.zw,aUV);gl_Position=vec4(mix(vec2(-1.0),vec2(1.0),aUV),0.0,1.0);}\";\n    var mainFragment = \"precision highp float;uniform vec4 uGlyphBounds;uniform float uMaxDistance;uniform float uExponent;varying vec4 vLineSegment;varying vec2 vGlyphXY;float absDistToSegment(vec2 point,vec2 lineA,vec2 lineB){vec2 lineDir=lineB-lineA;float lenSq=dot(lineDir,lineDir);float t=lenSq==0.0 ? 0.0 : clamp(dot(point-lineA,lineDir)/lenSq,0.0,1.0);vec2 linePt=lineA+t*lineDir;return distance(point,linePt);}void main(){vec4 seg=vLineSegment;vec2 p=vGlyphXY;float dist=absDistToSegment(p,seg.xy,seg.zw);float val=pow(1.0-clamp(dist/uMaxDistance,0.0,1.0),uExponent)*0.5;bool crossing=(seg.y>p.y!=seg.w>p.y)&&(p.x<(seg.z-seg.x)*(p.y-seg.y)/(seg.w-seg.y)+seg.x);bool crossingUp=crossing&&vLineSegment.y<vLineSegment.w;gl_FragColor=vec4(crossingUp ? 1.0/255.0 : 0.0,crossing&&!crossingUp ? 1.0/255.0 : 0.0,0.0,val);}\";\n    var postFragment = \"precision highp float;uniform sampler2D tex;varying vec2 vUV;void main(){vec4 color=texture2D(tex,vUV);bool inside=color.r!=color.g;float val=inside ? 1.0-color.a : color.a;gl_FragColor=vec4(val);}\";\n\n    // Single triangle covering viewport\n    var viewportUVs = new Float32Array([0, 0, 2, 0, 0, 2]);\n    var implicitContext = null;\n    var isTestingSupport = false;\n    var NULL_OBJECT = {};\n    var supportByCanvas = new WeakMap(); // canvas -> bool\n\n    function validateSupport(glOrCanvas) {\n      if (!isTestingSupport && !isSupported(glOrCanvas)) {\n        throw new Error('WebGL generation not supported');\n      }\n    }\n    function generate$1(sdfWidth, sdfHeight, path, viewBox, maxDistance, sdfExponent, glOrCanvas) {\n      if (sdfExponent === void 0) sdfExponent = 1;\n      if (glOrCanvas === void 0) glOrCanvas = null;\n      if (!glOrCanvas) {\n        glOrCanvas = implicitContext;\n        if (!glOrCanvas) {\n          var canvas = typeof OffscreenCanvas === 'function' ? new OffscreenCanvas(1, 1) : typeof document !== 'undefined' ? document.createElement('canvas') : null;\n          if (!canvas) {\n            throw new Error('OffscreenCanvas or DOM canvas not supported');\n          }\n          glOrCanvas = implicitContext = canvas.getContext('webgl', {\n            depth: false\n          });\n        }\n      }\n      validateSupport(glOrCanvas);\n      var rgbaData = new Uint8Array(sdfWidth * sdfHeight * 4); //not Uint8ClampedArray, cuz Safari\n\n      // Render into a background texture framebuffer\n      withWebGLContext(glOrCanvas, function (ref) {\n        var gl = ref.gl;\n        var withTexture = ref.withTexture;\n        var withTextureFramebuffer = ref.withTextureFramebuffer;\n        withTexture('readable', function (texture, textureUnit) {\n          gl.texImage2D(gl.TEXTURE_2D, 0, gl.RGBA, sdfWidth, sdfHeight, 0, gl.RGBA, gl.UNSIGNED_BYTE, null);\n          withTextureFramebuffer(texture, textureUnit, function (framebuffer) {\n            generateIntoFramebuffer(sdfWidth, sdfHeight, path, viewBox, maxDistance, sdfExponent, gl, framebuffer, 0, 0, 0 // red channel\n            );\n            gl.readPixels(0, 0, sdfWidth, sdfHeight, gl.RGBA, gl.UNSIGNED_BYTE, rgbaData);\n          });\n        });\n      });\n\n      // Throw away all but the red channel\n      var data = new Uint8Array(sdfWidth * sdfHeight);\n      for (var i = 0, j = 0; i < rgbaData.length; i += 4) {\n        data[j++] = rgbaData[i];\n      }\n      return data;\n    }\n    function generateIntoCanvas$1(sdfWidth, sdfHeight, path, viewBox, maxDistance, sdfExponent, canvas, x, y, channel) {\n      if (sdfExponent === void 0) sdfExponent = 1;\n      if (x === void 0) x = 0;\n      if (y === void 0) y = 0;\n      if (channel === void 0) channel = 0;\n      generateIntoFramebuffer(sdfWidth, sdfHeight, path, viewBox, maxDistance, sdfExponent, canvas, null, x, y, channel);\n    }\n    function generateIntoFramebuffer(sdfWidth, sdfHeight, path, viewBox, maxDistance, sdfExponent, glOrCanvas, framebuffer, x, y, channel) {\n      if (sdfExponent === void 0) sdfExponent = 1;\n      if (x === void 0) x = 0;\n      if (y === void 0) y = 0;\n      if (channel === void 0) channel = 0;\n\n      // Verify support\n      validateSupport(glOrCanvas);\n\n      // Compute path segments\n      var lineSegmentCoords = [];\n      pathToLineSegments(path, function (x1, y1, x2, y2) {\n        lineSegmentCoords.push(x1, y1, x2, y2);\n      });\n      lineSegmentCoords = new Float32Array(lineSegmentCoords);\n      withWebGLContext(glOrCanvas, function (ref) {\n        var gl = ref.gl;\n        var isWebGL2 = ref.isWebGL2;\n        var getExtension = ref.getExtension;\n        var withProgram = ref.withProgram;\n        var withTexture = ref.withTexture;\n        var withTextureFramebuffer = ref.withTextureFramebuffer;\n        var handleContextLoss = ref.handleContextLoss;\n        withTexture('rawDistances', function (intermediateTexture, intermediateTextureUnit) {\n          if (sdfWidth !== intermediateTexture._lastWidth || sdfHeight !== intermediateTexture._lastHeight) {\n            gl.texImage2D(gl.TEXTURE_2D, 0, gl.RGBA, intermediateTexture._lastWidth = sdfWidth, intermediateTexture._lastHeight = sdfHeight, 0, gl.RGBA, gl.UNSIGNED_BYTE, null);\n          }\n\n          // Unsigned distance pass\n          withProgram('main', mainVertex, mainFragment, function (ref) {\n            var setAttribute = ref.setAttribute;\n            var setUniform = ref.setUniform;\n\n            // Init extensions\n            var instancingExtension = !isWebGL2 && getExtension('ANGLE_instanced_arrays');\n            var blendMinMaxExtension = !isWebGL2 && getExtension('EXT_blend_minmax');\n\n            // Init/update attributes\n            setAttribute('aUV', 2, gl.STATIC_DRAW, 0, viewportUVs);\n            setAttribute('aLineSegment', 4, gl.DYNAMIC_DRAW, 1, lineSegmentCoords);\n\n            // Init/update uniforms\n            setUniform.apply(void 0, ['4f', 'uGlyphBounds'].concat(viewBox));\n            setUniform('1f', 'uMaxDistance', maxDistance);\n            setUniform('1f', 'uExponent', sdfExponent);\n\n            // Render initial unsigned distance / winding number info to a texture\n            withTextureFramebuffer(intermediateTexture, intermediateTextureUnit, function (framebuffer) {\n              gl.enable(gl.BLEND);\n              gl.colorMask(true, true, true, true);\n              gl.viewport(0, 0, sdfWidth, sdfHeight);\n              gl.scissor(0, 0, sdfWidth, sdfHeight);\n              gl.blendFunc(gl.ONE, gl.ONE);\n              // Red+Green channels are incremented (FUNC_ADD) for segment-ray crossings to give a \"winding number\".\n              // Alpha holds the closest (MAX) unsigned distance.\n              gl.blendEquationSeparate(gl.FUNC_ADD, isWebGL2 ? gl.MAX : blendMinMaxExtension.MAX_EXT);\n              gl.clear(gl.COLOR_BUFFER_BIT);\n              if (isWebGL2) {\n                gl.drawArraysInstanced(gl.TRIANGLES, 0, 3, lineSegmentCoords.length / 4);\n              } else {\n                instancingExtension.drawArraysInstancedANGLE(gl.TRIANGLES, 0, 3, lineSegmentCoords.length / 4);\n              }\n              // Debug\n              // const debug = new Uint8Array(sdfWidth * sdfHeight * 4)\n              // gl.readPixels(0, 0, sdfWidth, sdfHeight, gl.RGBA, gl.UNSIGNED_BYTE, debug)\n              // console.log('intermediate texture data: ', debug)\n            });\n          });\n\n          // Use the data stored in the texture to apply inside/outside and write to the output framebuffer rect+channel.\n          withProgram('post', viewportQuadVertex, postFragment, function (program) {\n            program.setAttribute('aUV', 2, gl.STATIC_DRAW, 0, viewportUVs);\n            program.setUniform('1i', 'tex', intermediateTextureUnit);\n            gl.bindFramebuffer(gl.FRAMEBUFFER, framebuffer);\n            gl.disable(gl.BLEND);\n            gl.colorMask(channel === 0, channel === 1, channel === 2, channel === 3);\n            gl.viewport(x, y, sdfWidth, sdfHeight);\n            gl.scissor(x, y, sdfWidth, sdfHeight);\n            gl.drawArrays(gl.TRIANGLES, 0, 3);\n          });\n        });\n\n        // Handle context loss occurring during any of the above calls\n        if (gl.isContextLost()) {\n          handleContextLoss();\n          throw new Error('webgl context lost');\n        }\n      });\n    }\n    function isSupported(glOrCanvas) {\n      var key = !glOrCanvas || glOrCanvas === implicitContext ? NULL_OBJECT : glOrCanvas.canvas || glOrCanvas;\n      var supported = supportByCanvas.get(key);\n      if (supported === undefined) {\n        isTestingSupport = true;\n        var failReason = null;\n        try {\n          // Since we can't detect all failure modes up front, let's just do a trial run of a\n          // simple path and compare what we get back to the correct expected result. This will\n          // also serve to prime the shader compilation.\n          var expectedResult = [97, 106, 97, 61, 99, 137, 118, 80, 80, 118, 137, 99, 61, 97, 106, 97];\n          var testResult = generate$1(4, 4, 'M8,8L16,8L24,24L16,24Z', [0, 0, 32, 32], 24, 1, glOrCanvas);\n          supported = testResult && expectedResult.length === testResult.length && testResult.every(function (val, i) {\n            return val === expectedResult[i];\n          });\n          if (!supported) {\n            failReason = 'bad trial run results';\n            console.info(expectedResult, testResult);\n          }\n        } catch (err) {\n          // TODO if it threw due to webgl context loss, should we maybe leave isSupported as null and try again later?\n          supported = false;\n          failReason = err.message;\n        }\n        if (failReason) {\n          console.warn('WebGL SDF generation not supported:', failReason);\n        }\n        isTestingSupport = false;\n        supportByCanvas.set(key, supported);\n      }\n      return supported;\n    }\n    var webgl = /*#__PURE__*/Object.freeze({\n      __proto__: null,\n      generate: generate$1,\n      generateIntoCanvas: generateIntoCanvas$1,\n      generateIntoFramebuffer: generateIntoFramebuffer,\n      isSupported: isSupported\n    });\n\n    /**\n     * Generate an SDF texture image for a 2D path.\n     *\n     * @param {number} sdfWidth - width of the SDF output image in pixels.\n     * @param {number} sdfHeight - height of the SDF output image in pixels.\n     * @param {string} path - an SVG-like path string describing the glyph; should only contain commands: M/L/Q/C/Z.\n     * @param {number[]} viewBox - [minX, minY, maxX, maxY] in font units aligning with the texture's edges.\n     * @param {number} maxDistance - the maximum distance from the glyph path in font units that will be encoded; defaults\n     *        to half the maximum viewBox dimension.\n     * @param {number} [sdfExponent] - specifies an exponent for encoding the SDF's distance values; higher exponents\n     *        will give greater precision nearer the glyph's path.\n     * @return {Uint8Array}\n     */\n    function generate(sdfWidth, sdfHeight, path, viewBox, maxDistance, sdfExponent) {\n      if (maxDistance === void 0) maxDistance = Math.max(viewBox[2] - viewBox[0], viewBox[3] - viewBox[1]) / 2;\n      if (sdfExponent === void 0) sdfExponent = 1;\n      try {\n        return generate$1.apply(webgl, arguments);\n      } catch (e) {\n        console.info('WebGL SDF generation failed, falling back to JS', e);\n        return generate$2.apply(javascript, arguments);\n      }\n    }\n\n    /**\n     * Generate an SDF texture image for a 2D path, inserting the result into a WebGL `canvas` at a given x/y position\n     * and color channel. This is generally much faster than calling `generate` because it does not require reading pixels\n     * back from the GPU->CPU -- the `canvas` can be used directly as a WebGL texture image, so it all stays on the GPU.\n     *\n     * @param {number} sdfWidth - width of the SDF output image in pixels.\n     * @param {number} sdfHeight - height of the SDF output image in pixels.\n     * @param {string} path - an SVG-like path string describing the glyph; should only contain commands: M/L/Q/C/Z.\n     * @param {number[]} viewBox - [minX, minY, maxX, maxY] in font units aligning with the texture's edges.\n     * @param {number} maxDistance - the maximum distance from the glyph path in font units that will be encoded; defaults\n     *        to half the maximum viewBox dimension.\n     * @param {number} [sdfExponent] - specifies an exponent for encoding the SDF's distance values; higher exponents\n     *        will give greater precision nearer the glyph's path.\n     * @param {HTMLCanvasElement|OffscreenCanvas} canvas - a WebGL-enabled canvas into which the SDF will be rendered.\n     *        Only the relevant rect/channel will be modified, the rest will be preserved. To avoid unpredictable results\n     *        due to shared GL context state, this canvas should be dedicated to use by this library alone.\n     * @param {number} x - the x position at which to render the SDF.\n     * @param {number} y - the y position at which to render the SDF.\n     * @param {number} channel - the color channel index (0-4) into which the SDF will be rendered.\n     * @return {Uint8Array}\n     */\n    function generateIntoCanvas(sdfWidth, sdfHeight, path, viewBox, maxDistance, sdfExponent, canvas, x, y, channel) {\n      if (maxDistance === void 0) maxDistance = Math.max(viewBox[2] - viewBox[0], viewBox[3] - viewBox[1]) / 2;\n      if (sdfExponent === void 0) sdfExponent = 1;\n      if (x === void 0) x = 0;\n      if (y === void 0) y = 0;\n      if (channel === void 0) channel = 0;\n      try {\n        return generateIntoCanvas$1.apply(webgl, arguments);\n      } catch (e) {\n        console.info('WebGL SDF generation failed, falling back to JS', e);\n        return generateIntoCanvas$2.apply(javascript, arguments);\n      }\n    }\n    exports.forEachPathCommand = forEachPathCommand;\n    exports.generate = generate;\n    exports.generateIntoCanvas = generateIntoCanvas;\n    exports.javascript = javascript;\n    exports.pathToLineSegments = pathToLineSegments;\n    exports.webgl = webgl;\n    exports.webglUtils = webglUtils;\n    Object.defineProperty(exports, '__esModule', {\n      value: true\n    });\n    return exports;\n  }({});\n  return exports;\n}\nexport { SDFGenerator as default };", "map": {"version": 3, "names": ["SDFGenerator", "exports", "pointOnQuadraticBezier", "x0", "y0", "x1", "y1", "x2", "y2", "t", "pointOut", "t2", "x", "y", "pointOnCubicBezier", "x3", "y3", "forEachPathCommand", "pathString", "commandCallback", "segmentRE", "match", "firstX", "firstY", "prevX", "prevY", "exec", "args", "replace", "split", "map", "v", "parseFloat", "pathToLineSegments", "segmentCallback", "curvePoints", "tempPoint", "command", "startX", "startY", "endX", "endY", "ctrl1X", "ctrl1Y", "ctrl2X", "ctrl2Y", "prevCurveX", "prevCurveY", "i", "prevCurveX$1", "prevCurveY$1", "i$1", "viewportQuadVertex", "copyTexFragment", "cache", "WeakMap", "glContextParams", "premultipliedAlpha", "preserveDrawingBuffer", "antialias", "depth", "withWebGLContext", "glOrCanvas", "callback", "gl", "getContext", "wrapper", "get", "isWebGL2", "WebGL2RenderingContext", "extensions", "programs", "textures", "textureUnit", "framebufferStack", "canvas", "addEventListener", "e", "handleContextLoss", "preventDefault", "getExtension", "name", "ext", "Error", "compileShader", "src", "type", "shader", "createShader", "shaderSource", "withProgram", "vert", "frag", "func", "attributes", "uniforms", "program", "createProgram", "<PERSON><PERSON><PERSON><PERSON>", "VERTEX_SHADER", "FRAGMENT_SHADER", "linkProgram", "transaction", "useProgram", "setUniform", "values", "len", "arguments", "length", "uniformLoc", "getUniformLocation", "apply", "concat", "setAttribute", "size", "usage", "instancingDivisor", "data", "attr", "buf", "createBuffer", "loc", "getAttribLocation", "<PERSON><PERSON><PERSON><PERSON>", "ARRAY_BUFFER", "vertexAttribPointer", "FLOAT", "enableVertexAttribArray", "vertexAttribDivisor", "vertexAttribDivisorANGLE", "bufferData", "withTexture", "activeTexture", "TEXTURE0", "texture", "createTexture", "bindTexture", "TEXTURE_2D", "texParameteri", "TEXTURE_MIN_FILTER", "NEAREST", "TEXTURE_MAG_FILTER", "withTextureFramebuffer", "framebuffer", "createFramebuffer", "push", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "FRAMEBUFFER", "framebufferTexture2D", "COLOR_ATTACHMENT0", "deleteFramebuffer", "set", "renderImageData", "imageData", "width", "height", "channels", "ref", "tex", "texUnit", "texImage2D", "RGBA", "UNSIGNED_BYTE", "STATIC_DRAW", "Float32Array", "disable", "BLEND", "colorMask", "viewport", "scissor", "drawArrays", "TRIANGLES", "resizeWebGLCanvasWithoutClearing", "newWidth", "newHeight", "Uint8Array", "readPixels", "webglUtils", "Object", "freeze", "__proto__", "generate$2", "sdfWidth", "sdfHeight", "path", "viewBox", "maxDistance", "sdfExponent", "textureData", "viewBoxWidth", "viewBoxHeight", "segments", "minX", "Math", "min", "minY", "maxX", "max", "maxY", "sort", "a", "b", "sdfX", "sdfY", "signedDist", "findNearestSignedDistance", "alpha", "pow", "abs", "round", "closestDistSq", "Infinity", "closestDist", "seg", "distSq", "absSquareDistanceToLineSegment", "sqrt", "isPointInPoly", "winding", "intersects", "generateIntoCanvas$2", "channel", "generateIntoFramebuffer$1", "rgbaData", "lineX0", "lineY0", "lineX1", "lineY1", "ldx", "ldy", "lengthSq", "dx", "dy", "javascript", "generate", "generateIntoCanvas", "generateIntoFramebuffer", "mainVertex", "mainFragment", "postFragment", "viewportUVs", "implicitContext", "isTestingSupport", "NULL_OBJECT", "supportByCanvas", "validateSupport", "isSupported", "generate$1", "OffscreenCanvas", "document", "createElement", "j", "generateIntoCanvas$1", "lineSegmentCoords", "intermediateTexture", "intermediateTextureUnit", "_lastWidth", "_lastHeight", "instancingExtension", "blendMinMaxExtension", "DYNAMIC_DRAW", "enable", "blendFunc", "ONE", "blendEquationSeparate", "FUNC_ADD", "MAX", "MAX_EXT", "clear", "COLOR_BUFFER_BIT", "drawArraysInstanced", "drawArraysInstancedANGLE", "isContextLost", "key", "supported", "undefined", "failReason", "expectedResult", "testResult", "every", "val", "console", "info", "err", "message", "warn", "webgl", "defineProperty", "value", "default"], "sources": ["C:/Users/<USER>/Documents/Apps_Creations/SiahMan_Racing4Speed_WebGame/siah-man-racing/node_modules/webgl-sdf-generator/dist/webgl-sdf-generator.mjs"], "sourcesContent": ["function SDFGenerator() {\nvar exports = (function (exports) {\n\n  /**\n   * Find the point on a quadratic bezier curve at t where t is in the range [0, 1]\n   */\n  function pointOnQuadraticBezier (x0, y0, x1, y1, x2, y2, t, pointOut) {\n    var t2 = 1 - t;\n    pointOut.x = t2 * t2 * x0 + 2 * t2 * t * x1 + t * t * x2;\n    pointOut.y = t2 * t2 * y0 + 2 * t2 * t * y1 + t * t * y2;\n  }\n\n  /**\n   * Find the point on a cubic bezier curve at t where t is in the range [0, 1]\n   */\n  function pointOnCubicBezier (x0, y0, x1, y1, x2, y2, x3, y3, t, pointOut) {\n    var t2 = 1 - t;\n    pointOut.x = t2 * t2 * t2 * x0 + 3 * t2 * t2 * t * x1 + 3 * t2 * t * t * x2 + t * t * t * x3;\n    pointOut.y = t2 * t2 * t2 * y0 + 3 * t2 * t2 * t * y1 + 3 * t2 * t * t * y2 + t * t * t * y3;\n  }\n\n  /**\n   * Parse a path string into its constituent line/curve commands, invoking a callback for each.\n   * @param {string} pathString - An SVG-like path string to parse; should only contain commands: M/L/Q/C/Z\n   * @param {function(\n   *   command: 'L'|'Q'|'C',\n   *   startX: number,\n   *   startY: number,\n   *   endX: number,\n   *   endY: number,\n   *   ctrl1X?: number,\n   *   ctrl1Y?: number,\n   *   ctrl2X?: number,\n   *   ctrl2Y?: number\n   * )} commandCallback - A callback function that will be called once for each parsed path command, passing the\n   *                      command identifier (only L/Q/C commands) and its numeric arguments.\n   */\n  function forEachPathCommand(pathString, commandCallback) {\n    var segmentRE = /([MLQCZ])([^MLQCZ]*)/g;\n    var match, firstX, firstY, prevX, prevY;\n    while ((match = segmentRE.exec(pathString))) {\n      var args = match[2]\n        .replace(/^\\s*|\\s*$/g, '')\n        .split(/[,\\s]+/)\n        .map(function (v) { return parseFloat(v); });\n      switch (match[1]) {\n        case 'M':\n          prevX = firstX = args[0];\n          prevY = firstY = args[1];\n          break\n        case 'L':\n          if (args[0] !== prevX || args[1] !== prevY) { // yup, some fonts have zero-length line commands\n            commandCallback('L', prevX, prevY, (prevX = args[0]), (prevY = args[1]));\n          }\n          break\n        case 'Q': {\n          commandCallback('Q', prevX, prevY, (prevX = args[2]), (prevY = args[3]), args[0], args[1]);\n          break\n        }\n        case 'C': {\n          commandCallback('C', prevX, prevY, (prevX = args[4]), (prevY = args[5]), args[0], args[1], args[2], args[3]);\n          break\n        }\n        case 'Z':\n          if (prevX !== firstX || prevY !== firstY) {\n            commandCallback('L', prevX, prevY, firstX, firstY);\n          }\n          break\n      }\n    }\n  }\n\n  /**\n   * Convert a path string to a series of straight line segments\n   * @param {string} pathString - An SVG-like path string to parse; should only contain commands: M/L/Q/C/Z\n   * @param {function(x1:number, y1:number, x2:number, y2:number)} segmentCallback - A callback\n   *        function that will be called once for every line segment\n   * @param {number} [curvePoints] - How many straight line segments to use when approximating a\n   *        bezier curve in the path. Defaults to 16.\n   */\n  function pathToLineSegments (pathString, segmentCallback, curvePoints) {\n    if ( curvePoints === void 0 ) curvePoints = 16;\n\n    var tempPoint = { x: 0, y: 0 };\n    forEachPathCommand(pathString, function (command, startX, startY, endX, endY, ctrl1X, ctrl1Y, ctrl2X, ctrl2Y) {\n      switch (command) {\n        case 'L':\n          segmentCallback(startX, startY, endX, endY);\n          break\n        case 'Q': {\n          var prevCurveX = startX;\n          var prevCurveY = startY;\n          for (var i = 1; i < curvePoints; i++) {\n            pointOnQuadraticBezier(\n              startX, startY,\n              ctrl1X, ctrl1Y,\n              endX, endY,\n              i / (curvePoints - 1),\n              tempPoint\n            );\n            segmentCallback(prevCurveX, prevCurveY, tempPoint.x, tempPoint.y);\n            prevCurveX = tempPoint.x;\n            prevCurveY = tempPoint.y;\n          }\n          break\n        }\n        case 'C': {\n          var prevCurveX$1 = startX;\n          var prevCurveY$1 = startY;\n          for (var i$1 = 1; i$1 < curvePoints; i$1++) {\n            pointOnCubicBezier(\n              startX, startY,\n              ctrl1X, ctrl1Y,\n              ctrl2X, ctrl2Y,\n              endX, endY,\n              i$1 / (curvePoints - 1),\n              tempPoint\n            );\n            segmentCallback(prevCurveX$1, prevCurveY$1, tempPoint.x, tempPoint.y);\n            prevCurveX$1 = tempPoint.x;\n            prevCurveY$1 = tempPoint.y;\n          }\n          break\n        }\n      }\n    });\n  }\n\n  var viewportQuadVertex = \"precision highp float;attribute vec2 aUV;varying vec2 vUV;void main(){vUV=aUV;gl_Position=vec4(mix(vec2(-1.0),vec2(1.0),aUV),0.0,1.0);}\";\n\n  var copyTexFragment = \"precision highp float;uniform sampler2D tex;varying vec2 vUV;void main(){gl_FragColor=texture2D(tex,vUV);}\";\n\n  var cache = new WeakMap();\n\n  var glContextParams = {\n    premultipliedAlpha: false,\n    preserveDrawingBuffer: true,\n    antialias: false,\n    depth: false,\n  };\n\n  /**\n   * This is a little helper library for WebGL. It assists with state management for a GL context.\n   * It's pretty tightly wrapped to the needs of this package, not very general-purpose.\n   *\n   * @param { WebGLRenderingContext | HTMLCanvasElement | OffscreenCanvas } glOrCanvas - the GL context to wrap\n   * @param { ({gl, getExtension, withProgram, withTexture, withTextureFramebuffer, handleContextLoss}) => void } callback\n   */\n  function withWebGLContext (glOrCanvas, callback) {\n    var gl = glOrCanvas.getContext ? glOrCanvas.getContext('webgl', glContextParams) : glOrCanvas;\n    var wrapper = cache.get(gl);\n    if (!wrapper) {\n      var isWebGL2 = typeof WebGL2RenderingContext !== 'undefined' && gl instanceof WebGL2RenderingContext;\n      var extensions = {};\n      var programs = {};\n      var textures = {};\n      var textureUnit = -1;\n      var framebufferStack = [];\n\n      gl.canvas.addEventListener('webglcontextlost', function (e) {\n        handleContextLoss();\n        e.preventDefault();\n      }, false);\n\n      function getExtension (name) {\n        var ext = extensions[name];\n        if (!ext) {\n          ext = extensions[name] = gl.getExtension(name);\n          if (!ext) {\n            throw new Error((name + \" not supported\"))\n          }\n        }\n        return ext\n      }\n\n      function compileShader (src, type) {\n        var shader = gl.createShader(type);\n        gl.shaderSource(shader, src);\n        gl.compileShader(shader);\n        // const status = gl.getShaderParameter(shader, gl.COMPILE_STATUS)\n        // if (!status && !gl.isContextLost()) {\n        //   throw new Error(gl.getShaderInfoLog(shader).trim())\n        // }\n        return shader\n      }\n\n      function withProgram (name, vert, frag, func) {\n        if (!programs[name]) {\n          var attributes = {};\n          var uniforms = {};\n          var program = gl.createProgram();\n          gl.attachShader(program, compileShader(vert, gl.VERTEX_SHADER));\n          gl.attachShader(program, compileShader(frag, gl.FRAGMENT_SHADER));\n          gl.linkProgram(program);\n\n          programs[name] = {\n            program: program,\n            transaction: function transaction (func) {\n              gl.useProgram(program);\n              func({\n                setUniform: function setUniform (type, name) {\n                  var values = [], len = arguments.length - 2;\n                  while ( len-- > 0 ) values[ len ] = arguments[ len + 2 ];\n\n                  var uniformLoc = uniforms[name] || (uniforms[name] = gl.getUniformLocation(program, name));\n                  gl[(\"uniform\" + type)].apply(gl, [ uniformLoc ].concat( values ));\n                },\n\n                setAttribute: function setAttribute (name, size, usage, instancingDivisor, data) {\n                  var attr = attributes[name];\n                  if (!attr) {\n                    attr = attributes[name] = {\n                      buf: gl.createBuffer(), // TODO should we destroy our buffers?\n                      loc: gl.getAttribLocation(program, name),\n                      data: null\n                    };\n                  }\n                  gl.bindBuffer(gl.ARRAY_BUFFER, attr.buf);\n                  gl.vertexAttribPointer(attr.loc, size, gl.FLOAT, false, 0, 0);\n                  gl.enableVertexAttribArray(attr.loc);\n                  if (isWebGL2) {\n                    gl.vertexAttribDivisor(attr.loc, instancingDivisor);\n                  } else {\n                    getExtension('ANGLE_instanced_arrays').vertexAttribDivisorANGLE(attr.loc, instancingDivisor);\n                  }\n                  if (data !== attr.data) {\n                    gl.bufferData(gl.ARRAY_BUFFER, data, usage);\n                    attr.data = data;\n                  }\n                }\n              });\n            }\n          };\n        }\n\n        programs[name].transaction(func);\n      }\n\n      function withTexture (name, func) {\n        textureUnit++;\n        try {\n          gl.activeTexture(gl.TEXTURE0 + textureUnit);\n          var texture = textures[name];\n          if (!texture) {\n            texture = textures[name] = gl.createTexture();\n            gl.bindTexture(gl.TEXTURE_2D, texture);\n            gl.texParameteri(gl.TEXTURE_2D, gl.TEXTURE_MIN_FILTER, gl.NEAREST);\n            gl.texParameteri(gl.TEXTURE_2D, gl.TEXTURE_MAG_FILTER, gl.NEAREST);\n          }\n          gl.bindTexture(gl.TEXTURE_2D, texture);\n          func(texture, textureUnit);\n        } finally {\n          textureUnit--;\n        }\n      }\n\n      function withTextureFramebuffer (texture, textureUnit, func) {\n        var framebuffer = gl.createFramebuffer();\n        framebufferStack.push(framebuffer);\n        gl.bindFramebuffer(gl.FRAMEBUFFER, framebuffer);\n        gl.activeTexture(gl.TEXTURE0 + textureUnit);\n        gl.bindTexture(gl.TEXTURE_2D, texture);\n        gl.framebufferTexture2D(gl.FRAMEBUFFER, gl.COLOR_ATTACHMENT0, gl.TEXTURE_2D, texture, 0);\n        try {\n          func(framebuffer);\n        } finally {\n          gl.deleteFramebuffer(framebuffer);\n          gl.bindFramebuffer(gl.FRAMEBUFFER, framebufferStack[--framebufferStack.length - 1] || null);\n        }\n      }\n\n      function handleContextLoss () {\n        extensions = {};\n        programs = {};\n        textures = {};\n        textureUnit = -1;\n        framebufferStack.length = 0;\n      }\n\n      cache.set(gl, wrapper = {\n        gl: gl,\n        isWebGL2: isWebGL2,\n        getExtension: getExtension,\n        withProgram: withProgram,\n        withTexture: withTexture,\n        withTextureFramebuffer: withTextureFramebuffer,\n        handleContextLoss: handleContextLoss,\n      });\n    }\n    callback(wrapper);\n  }\n\n\n  function renderImageData(glOrCanvas, imageData, x, y, width, height, channels, framebuffer) {\n    if ( channels === void 0 ) channels = 15;\n    if ( framebuffer === void 0 ) framebuffer = null;\n\n    withWebGLContext(glOrCanvas, function (ref) {\n      var gl = ref.gl;\n      var withProgram = ref.withProgram;\n      var withTexture = ref.withTexture;\n\n      withTexture('copy', function (tex, texUnit) {\n        gl.texImage2D(gl.TEXTURE_2D, 0, gl.RGBA, width, height, 0, gl.RGBA, gl.UNSIGNED_BYTE, imageData);\n        withProgram('copy', viewportQuadVertex, copyTexFragment, function (ref) {\n          var setUniform = ref.setUniform;\n          var setAttribute = ref.setAttribute;\n\n          setAttribute('aUV', 2, gl.STATIC_DRAW, 0, new Float32Array([0, 0, 2, 0, 0, 2]));\n          setUniform('1i', 'image', texUnit);\n          gl.bindFramebuffer(gl.FRAMEBUFFER, framebuffer || null);\n          gl.disable(gl.BLEND);\n          gl.colorMask(channels & 8, channels & 4, channels & 2, channels & 1);\n          gl.viewport(x, y, width, height);\n          gl.scissor(x, y, width, height);\n          gl.drawArrays(gl.TRIANGLES, 0, 3);\n        });\n      });\n    });\n  }\n\n  /**\n   * Resizing a canvas clears its contents; this utility copies the previous contents over.\n   * @param canvas\n   * @param newWidth\n   * @param newHeight\n   */\n  function resizeWebGLCanvasWithoutClearing(canvas, newWidth, newHeight) {\n    var width = canvas.width;\n    var height = canvas.height;\n    withWebGLContext(canvas, function (ref) {\n      var gl = ref.gl;\n\n      var data = new Uint8Array(width * height * 4);\n      gl.readPixels(0, 0, width, height, gl.RGBA, gl.UNSIGNED_BYTE, data);\n      canvas.width = newWidth;\n      canvas.height = newHeight;\n      renderImageData(gl, data, 0, 0, width, height);\n    });\n  }\n\n  var webglUtils = /*#__PURE__*/Object.freeze({\n    __proto__: null,\n    withWebGLContext: withWebGLContext,\n    renderImageData: renderImageData,\n    resizeWebGLCanvasWithoutClearing: resizeWebGLCanvasWithoutClearing\n  });\n\n  function generate$2 (sdfWidth, sdfHeight, path, viewBox, maxDistance, sdfExponent) {\n    if ( sdfExponent === void 0 ) sdfExponent = 1;\n\n    var textureData = new Uint8Array(sdfWidth * sdfHeight);\n\n    var viewBoxWidth = viewBox[2] - viewBox[0];\n    var viewBoxHeight = viewBox[3] - viewBox[1];\n\n    // Decompose all paths into straight line segments and add them to an index\n    var segments = [];\n    pathToLineSegments(path, function (x1, y1, x2, y2) {\n      segments.push({\n        x1: x1, y1: y1, x2: x2, y2: y2,\n        minX: Math.min(x1, x2),\n        minY: Math.min(y1, y2),\n        maxX: Math.max(x1, x2),\n        maxY: Math.max(y1, y2)\n      });\n    });\n\n    // Sort segments by maxX, this will let us short-circuit some loops below\n    segments.sort(function (a, b) { return a.maxX - b.maxX; });\n\n    // For each target SDF texel, find the distance from its center to its nearest line segment,\n    // map that distance to an alpha value, and write that alpha to the texel\n    for (var sdfX = 0; sdfX < sdfWidth; sdfX++) {\n      for (var sdfY = 0; sdfY < sdfHeight; sdfY++) {\n        var signedDist = findNearestSignedDistance(\n          viewBox[0] + viewBoxWidth * (sdfX + 0.5) / sdfWidth,\n          viewBox[1] + viewBoxHeight * (sdfY + 0.5) / sdfHeight\n        );\n\n        // Use an exponential scale to ensure the texels very near the glyph path have adequate\n        // precision, while allowing the distance field to cover the entire texture, given that\n        // there are only 8 bits available. Formula visualized: https://www.desmos.com/calculator/uiaq5aqiam\n        var alpha = Math.pow((1 - Math.abs(signedDist) / maxDistance), sdfExponent) / 2;\n        if (signedDist < 0) {\n          alpha = 1 - alpha;\n        }\n\n        alpha = Math.max(0, Math.min(255, Math.round(alpha * 255))); //clamp\n        textureData[sdfY * sdfWidth + sdfX] = alpha;\n      }\n    }\n\n    return textureData\n\n    /**\n     * For a given x/y, search the index for the closest line segment and return\n     * its signed distance. Negative = inside, positive = outside, zero = on edge\n     * @param x\n     * @param y\n     * @returns {number}\n     */\n    function findNearestSignedDistance (x, y) {\n      var closestDistSq = Infinity;\n      var closestDist = Infinity;\n\n      for (var i = segments.length; i--;) {\n        var seg = segments[i];\n        if (seg.maxX + closestDist <= x) { break } //sorting by maxX means no more can be closer, so we can short-circuit\n        if (x + closestDist > seg.minX && y - closestDist < seg.maxY && y + closestDist > seg.minY) {\n          var distSq = absSquareDistanceToLineSegment(x, y, seg.x1, seg.y1, seg.x2, seg.y2);\n          if (distSq < closestDistSq) {\n            closestDistSq = distSq;\n            closestDist = Math.sqrt(closestDistSq);\n          }\n        }\n      }\n\n      // Flip to negative distance if inside the poly\n      if (isPointInPoly(x, y)) {\n        closestDist = -closestDist;\n      }\n      return closestDist\n    }\n\n    /**\n     * Determine whether the given point lies inside or outside the glyph. Uses a simple\n     * winding-number ray casting algorithm using a ray pointing east from the point.\n     */\n    function isPointInPoly (x, y) {\n      var winding = 0;\n      for (var i = segments.length; i--;) {\n        var seg = segments[i];\n        if (seg.maxX <= x) { break } //sorting by maxX means no more can cross, so we can short-circuit\n        var intersects = ((seg.y1 > y) !== (seg.y2 > y)) && (x < (seg.x2 - seg.x1) * (y - seg.y1) / (seg.y2 - seg.y1) + seg.x1);\n        if (intersects) {\n          winding += seg.y1 < seg.y2 ? 1 : -1;\n        }\n      }\n      return winding !== 0\n    }\n  }\n\n  function generateIntoCanvas$2(sdfWidth, sdfHeight, path, viewBox, maxDistance, sdfExponent, canvas, x, y, channel) {\n    if ( sdfExponent === void 0 ) sdfExponent = 1;\n    if ( x === void 0 ) x = 0;\n    if ( y === void 0 ) y = 0;\n    if ( channel === void 0 ) channel = 0;\n\n    generateIntoFramebuffer$1(sdfWidth, sdfHeight, path, viewBox, maxDistance, sdfExponent, canvas, null, x, y, channel);\n  }\n\n  function generateIntoFramebuffer$1 (sdfWidth, sdfHeight, path, viewBox, maxDistance, sdfExponent, glOrCanvas, framebuffer, x, y, channel) {\n    if ( sdfExponent === void 0 ) sdfExponent = 1;\n    if ( x === void 0 ) x = 0;\n    if ( y === void 0 ) y = 0;\n    if ( channel === void 0 ) channel = 0;\n\n    var data = generate$2(sdfWidth, sdfHeight, path, viewBox, maxDistance, sdfExponent);\n    // Expand single-channel data to rbga\n    var rgbaData = new Uint8Array(data.length * 4);\n    for (var i = 0; i < data.length; i++) {\n      rgbaData[i * 4 + channel] = data[i];\n    }\n    renderImageData(glOrCanvas, rgbaData, x, y, sdfWidth, sdfHeight, 1 << (3 - channel), framebuffer);\n  }\n\n  /**\n   * Find the absolute distance from a point to a line segment at closest approach\n   */\n  function absSquareDistanceToLineSegment (x, y, lineX0, lineY0, lineX1, lineY1) {\n    var ldx = lineX1 - lineX0;\n    var ldy = lineY1 - lineY0;\n    var lengthSq = ldx * ldx + ldy * ldy;\n    var t = lengthSq ? Math.max(0, Math.min(1, ((x - lineX0) * ldx + (y - lineY0) * ldy) / lengthSq)) : 0;\n    var dx = x - (lineX0 + t * ldx);\n    var dy = y - (lineY0 + t * ldy);\n    return dx * dx + dy * dy\n  }\n\n  var javascript = /*#__PURE__*/Object.freeze({\n    __proto__: null,\n    generate: generate$2,\n    generateIntoCanvas: generateIntoCanvas$2,\n    generateIntoFramebuffer: generateIntoFramebuffer$1\n  });\n\n  var mainVertex = \"precision highp float;uniform vec4 uGlyphBounds;attribute vec2 aUV;attribute vec4 aLineSegment;varying vec4 vLineSegment;varying vec2 vGlyphXY;void main(){vLineSegment=aLineSegment;vGlyphXY=mix(uGlyphBounds.xy,uGlyphBounds.zw,aUV);gl_Position=vec4(mix(vec2(-1.0),vec2(1.0),aUV),0.0,1.0);}\";\n\n  var mainFragment = \"precision highp float;uniform vec4 uGlyphBounds;uniform float uMaxDistance;uniform float uExponent;varying vec4 vLineSegment;varying vec2 vGlyphXY;float absDistToSegment(vec2 point,vec2 lineA,vec2 lineB){vec2 lineDir=lineB-lineA;float lenSq=dot(lineDir,lineDir);float t=lenSq==0.0 ? 0.0 : clamp(dot(point-lineA,lineDir)/lenSq,0.0,1.0);vec2 linePt=lineA+t*lineDir;return distance(point,linePt);}void main(){vec4 seg=vLineSegment;vec2 p=vGlyphXY;float dist=absDistToSegment(p,seg.xy,seg.zw);float val=pow(1.0-clamp(dist/uMaxDistance,0.0,1.0),uExponent)*0.5;bool crossing=(seg.y>p.y!=seg.w>p.y)&&(p.x<(seg.z-seg.x)*(p.y-seg.y)/(seg.w-seg.y)+seg.x);bool crossingUp=crossing&&vLineSegment.y<vLineSegment.w;gl_FragColor=vec4(crossingUp ? 1.0/255.0 : 0.0,crossing&&!crossingUp ? 1.0/255.0 : 0.0,0.0,val);}\";\n\n  var postFragment = \"precision highp float;uniform sampler2D tex;varying vec2 vUV;void main(){vec4 color=texture2D(tex,vUV);bool inside=color.r!=color.g;float val=inside ? 1.0-color.a : color.a;gl_FragColor=vec4(val);}\";\n\n  // Single triangle covering viewport\n  var viewportUVs = new Float32Array([0, 0, 2, 0, 0, 2]);\n\n  var implicitContext = null;\n  var isTestingSupport = false;\n  var NULL_OBJECT = {};\n  var supportByCanvas = new WeakMap(); // canvas -> bool\n\n  function validateSupport (glOrCanvas) {\n    if (!isTestingSupport && !isSupported(glOrCanvas)) {\n      throw new Error('WebGL generation not supported')\n    }\n  }\n\n  function generate$1 (sdfWidth, sdfHeight, path, viewBox, maxDistance, sdfExponent, glOrCanvas) {\n    if ( sdfExponent === void 0 ) sdfExponent = 1;\n    if ( glOrCanvas === void 0 ) glOrCanvas = null;\n\n    if (!glOrCanvas) {\n      glOrCanvas = implicitContext;\n      if (!glOrCanvas) {\n        var canvas = typeof OffscreenCanvas === 'function'\n          ? new OffscreenCanvas(1, 1)\n          : typeof document !== 'undefined'\n            ? document.createElement('canvas')\n            : null;\n        if (!canvas) {\n          throw new Error('OffscreenCanvas or DOM canvas not supported')\n        }\n        glOrCanvas = implicitContext = canvas.getContext('webgl', { depth: false });\n      }\n    }\n\n    validateSupport(glOrCanvas);\n\n    var rgbaData = new Uint8Array(sdfWidth * sdfHeight * 4); //not Uint8ClampedArray, cuz Safari\n\n    // Render into a background texture framebuffer\n    withWebGLContext(glOrCanvas, function (ref) {\n      var gl = ref.gl;\n      var withTexture = ref.withTexture;\n      var withTextureFramebuffer = ref.withTextureFramebuffer;\n\n      withTexture('readable', function (texture, textureUnit) {\n        gl.texImage2D(gl.TEXTURE_2D, 0, gl.RGBA, sdfWidth, sdfHeight, 0, gl.RGBA, gl.UNSIGNED_BYTE, null);\n\n        withTextureFramebuffer(texture, textureUnit, function (framebuffer) {\n          generateIntoFramebuffer(\n            sdfWidth,\n            sdfHeight,\n            path,\n            viewBox,\n            maxDistance,\n            sdfExponent,\n            gl,\n            framebuffer,\n            0,\n            0,\n            0 // red channel\n          );\n          gl.readPixels(0, 0, sdfWidth, sdfHeight, gl.RGBA, gl.UNSIGNED_BYTE, rgbaData);\n        });\n      });\n    });\n\n    // Throw away all but the red channel\n    var data = new Uint8Array(sdfWidth * sdfHeight);\n    for (var i = 0, j = 0; i < rgbaData.length; i += 4) {\n      data[j++] = rgbaData[i];\n    }\n\n    return data\n  }\n\n  function generateIntoCanvas$1(sdfWidth, sdfHeight, path, viewBox, maxDistance, sdfExponent, canvas, x, y, channel) {\n    if ( sdfExponent === void 0 ) sdfExponent = 1;\n    if ( x === void 0 ) x = 0;\n    if ( y === void 0 ) y = 0;\n    if ( channel === void 0 ) channel = 0;\n\n    generateIntoFramebuffer(sdfWidth, sdfHeight, path, viewBox, maxDistance, sdfExponent, canvas, null, x, y, channel);\n  }\n\n  function generateIntoFramebuffer (sdfWidth, sdfHeight, path, viewBox, maxDistance, sdfExponent, glOrCanvas, framebuffer, x, y, channel) {\n    if ( sdfExponent === void 0 ) sdfExponent = 1;\n    if ( x === void 0 ) x = 0;\n    if ( y === void 0 ) y = 0;\n    if ( channel === void 0 ) channel = 0;\n\n    // Verify support\n    validateSupport(glOrCanvas);\n\n    // Compute path segments\n    var lineSegmentCoords = [];\n    pathToLineSegments(path, function (x1, y1, x2, y2) {\n      lineSegmentCoords.push(x1, y1, x2, y2);\n    });\n    lineSegmentCoords = new Float32Array(lineSegmentCoords);\n\n    withWebGLContext(glOrCanvas, function (ref) {\n      var gl = ref.gl;\n      var isWebGL2 = ref.isWebGL2;\n      var getExtension = ref.getExtension;\n      var withProgram = ref.withProgram;\n      var withTexture = ref.withTexture;\n      var withTextureFramebuffer = ref.withTextureFramebuffer;\n      var handleContextLoss = ref.handleContextLoss;\n\n      withTexture('rawDistances', function (intermediateTexture, intermediateTextureUnit) {\n        if (sdfWidth !== intermediateTexture._lastWidth || sdfHeight !== intermediateTexture._lastHeight) {\n          gl.texImage2D(\n            gl.TEXTURE_2D, 0, gl.RGBA,\n            intermediateTexture._lastWidth = sdfWidth,\n            intermediateTexture._lastHeight = sdfHeight,\n            0, gl.RGBA, gl.UNSIGNED_BYTE, null\n          );\n        }\n\n        // Unsigned distance pass\n        withProgram('main', mainVertex, mainFragment, function (ref) {\n          var setAttribute = ref.setAttribute;\n          var setUniform = ref.setUniform;\n\n          // Init extensions\n          var instancingExtension = !isWebGL2 && getExtension('ANGLE_instanced_arrays');\n          var blendMinMaxExtension = !isWebGL2 && getExtension('EXT_blend_minmax');\n\n          // Init/update attributes\n          setAttribute('aUV', 2, gl.STATIC_DRAW, 0, viewportUVs);\n          setAttribute('aLineSegment', 4, gl.DYNAMIC_DRAW, 1, lineSegmentCoords);\n\n          // Init/update uniforms\n          setUniform.apply(void 0, [ '4f', 'uGlyphBounds' ].concat( viewBox ));\n          setUniform('1f', 'uMaxDistance', maxDistance);\n          setUniform('1f', 'uExponent', sdfExponent);\n\n          // Render initial unsigned distance / winding number info to a texture\n          withTextureFramebuffer(intermediateTexture, intermediateTextureUnit, function (framebuffer) {\n            gl.enable(gl.BLEND);\n            gl.colorMask(true, true, true, true);\n            gl.viewport(0, 0, sdfWidth, sdfHeight);\n            gl.scissor(0, 0, sdfWidth, sdfHeight);\n            gl.blendFunc(gl.ONE, gl.ONE);\n            // Red+Green channels are incremented (FUNC_ADD) for segment-ray crossings to give a \"winding number\".\n            // Alpha holds the closest (MAX) unsigned distance.\n            gl.blendEquationSeparate(gl.FUNC_ADD, isWebGL2 ? gl.MAX : blendMinMaxExtension.MAX_EXT);\n            gl.clear(gl.COLOR_BUFFER_BIT);\n            if (isWebGL2) {\n              gl.drawArraysInstanced(gl.TRIANGLES, 0, 3, lineSegmentCoords.length / 4);\n            } else {\n              instancingExtension.drawArraysInstancedANGLE(gl.TRIANGLES, 0, 3, lineSegmentCoords.length / 4);\n            }\n            // Debug\n            // const debug = new Uint8Array(sdfWidth * sdfHeight * 4)\n            // gl.readPixels(0, 0, sdfWidth, sdfHeight, gl.RGBA, gl.UNSIGNED_BYTE, debug)\n            // console.log('intermediate texture data: ', debug)\n          });\n        });\n\n        // Use the data stored in the texture to apply inside/outside and write to the output framebuffer rect+channel.\n        withProgram('post', viewportQuadVertex, postFragment, function (program) {\n          program.setAttribute('aUV', 2, gl.STATIC_DRAW, 0, viewportUVs);\n          program.setUniform('1i', 'tex', intermediateTextureUnit);\n          gl.bindFramebuffer(gl.FRAMEBUFFER, framebuffer);\n          gl.disable(gl.BLEND);\n          gl.colorMask(channel === 0, channel === 1, channel === 2, channel === 3);\n          gl.viewport(x, y, sdfWidth, sdfHeight);\n          gl.scissor(x, y, sdfWidth, sdfHeight);\n          gl.drawArrays(gl.TRIANGLES, 0, 3);\n        });\n      });\n\n      // Handle context loss occurring during any of the above calls\n      if (gl.isContextLost()) {\n        handleContextLoss();\n        throw new Error('webgl context lost')\n      }\n    });\n  }\n\n  function isSupported (glOrCanvas) {\n    var key = (!glOrCanvas || glOrCanvas === implicitContext) ? NULL_OBJECT : (glOrCanvas.canvas || glOrCanvas);\n    var supported = supportByCanvas.get(key);\n    if (supported === undefined) {\n      isTestingSupport = true;\n      var failReason = null;\n      try {\n        // Since we can't detect all failure modes up front, let's just do a trial run of a\n        // simple path and compare what we get back to the correct expected result. This will\n        // also serve to prime the shader compilation.\n        var expectedResult = [\n          97, 106, 97, 61,\n          99, 137, 118, 80,\n          80, 118, 137, 99,\n          61, 97, 106, 97\n        ];\n        var testResult = generate$1(\n          4,\n          4,\n          'M8,8L16,8L24,24L16,24Z',\n          [0, 0, 32, 32],\n          24,\n          1,\n          glOrCanvas\n        );\n        supported = testResult && expectedResult.length === testResult.length &&\n          testResult.every(function (val, i) { return val === expectedResult[i]; });\n        if (!supported) {\n          failReason = 'bad trial run results';\n          console.info(expectedResult, testResult);\n        }\n      } catch (err) {\n        // TODO if it threw due to webgl context loss, should we maybe leave isSupported as null and try again later?\n        supported = false;\n        failReason = err.message;\n      }\n      if (failReason) {\n        console.warn('WebGL SDF generation not supported:', failReason);\n      }\n      isTestingSupport = false;\n      supportByCanvas.set(key, supported);\n    }\n    return supported\n  }\n\n  var webgl = /*#__PURE__*/Object.freeze({\n    __proto__: null,\n    generate: generate$1,\n    generateIntoCanvas: generateIntoCanvas$1,\n    generateIntoFramebuffer: generateIntoFramebuffer,\n    isSupported: isSupported\n  });\n\n  /**\n   * Generate an SDF texture image for a 2D path.\n   *\n   * @param {number} sdfWidth - width of the SDF output image in pixels.\n   * @param {number} sdfHeight - height of the SDF output image in pixels.\n   * @param {string} path - an SVG-like path string describing the glyph; should only contain commands: M/L/Q/C/Z.\n   * @param {number[]} viewBox - [minX, minY, maxX, maxY] in font units aligning with the texture's edges.\n   * @param {number} maxDistance - the maximum distance from the glyph path in font units that will be encoded; defaults\n   *        to half the maximum viewBox dimension.\n   * @param {number} [sdfExponent] - specifies an exponent for encoding the SDF's distance values; higher exponents\n   *        will give greater precision nearer the glyph's path.\n   * @return {Uint8Array}\n   */\n  function generate(\n    sdfWidth,\n    sdfHeight,\n    path,\n    viewBox,\n    maxDistance,\n    sdfExponent\n  ) {\n    if ( maxDistance === void 0 ) maxDistance = Math.max(viewBox[2] - viewBox[0], viewBox[3] - viewBox[1]) / 2;\n    if ( sdfExponent === void 0 ) sdfExponent = 1;\n\n    try {\n      return generate$1.apply(webgl, arguments)\n    } catch(e) {\n      console.info('WebGL SDF generation failed, falling back to JS', e);\n      return generate$2.apply(javascript, arguments)\n    }\n  }\n\n  /**\n   * Generate an SDF texture image for a 2D path, inserting the result into a WebGL `canvas` at a given x/y position\n   * and color channel. This is generally much faster than calling `generate` because it does not require reading pixels\n   * back from the GPU->CPU -- the `canvas` can be used directly as a WebGL texture image, so it all stays on the GPU.\n   *\n   * @param {number} sdfWidth - width of the SDF output image in pixels.\n   * @param {number} sdfHeight - height of the SDF output image in pixels.\n   * @param {string} path - an SVG-like path string describing the glyph; should only contain commands: M/L/Q/C/Z.\n   * @param {number[]} viewBox - [minX, minY, maxX, maxY] in font units aligning with the texture's edges.\n   * @param {number} maxDistance - the maximum distance from the glyph path in font units that will be encoded; defaults\n   *        to half the maximum viewBox dimension.\n   * @param {number} [sdfExponent] - specifies an exponent for encoding the SDF's distance values; higher exponents\n   *        will give greater precision nearer the glyph's path.\n   * @param {HTMLCanvasElement|OffscreenCanvas} canvas - a WebGL-enabled canvas into which the SDF will be rendered.\n   *        Only the relevant rect/channel will be modified, the rest will be preserved. To avoid unpredictable results\n   *        due to shared GL context state, this canvas should be dedicated to use by this library alone.\n   * @param {number} x - the x position at which to render the SDF.\n   * @param {number} y - the y position at which to render the SDF.\n   * @param {number} channel - the color channel index (0-4) into which the SDF will be rendered.\n   * @return {Uint8Array}\n   */\n  function generateIntoCanvas(\n    sdfWidth,\n    sdfHeight,\n    path,\n    viewBox,\n    maxDistance,\n    sdfExponent,\n    canvas,\n    x,\n    y,\n    channel\n  ) {\n    if ( maxDistance === void 0 ) maxDistance = Math.max(viewBox[2] - viewBox[0], viewBox[3] - viewBox[1]) / 2;\n    if ( sdfExponent === void 0 ) sdfExponent = 1;\n    if ( x === void 0 ) x = 0;\n    if ( y === void 0 ) y = 0;\n    if ( channel === void 0 ) channel = 0;\n\n    try {\n      return generateIntoCanvas$1.apply(webgl, arguments)\n    } catch(e) {\n      console.info('WebGL SDF generation failed, falling back to JS', e);\n      return generateIntoCanvas$2.apply(javascript, arguments)\n    }\n  }\n\n  exports.forEachPathCommand = forEachPathCommand;\n  exports.generate = generate;\n  exports.generateIntoCanvas = generateIntoCanvas;\n  exports.javascript = javascript;\n  exports.pathToLineSegments = pathToLineSegments;\n  exports.webgl = webgl;\n  exports.webglUtils = webglUtils;\n\n  Object.defineProperty(exports, '__esModule', { value: true });\n\n  return exports;\n\n}({}));\nreturn exports\n}\n\nexport { SDFGenerator as default };\n"], "mappings": "AAAA,SAASA,YAAYA,CAAA,EAAG;EACxB,IAAIC,OAAO,GAAI,UAAUA,OAAO,EAAE;IAEhC;AACF;AACA;IACE,SAASC,sBAAsBA,CAAEC,EAAE,EAAEC,EAAE,EAAEC,EAAE,EAAEC,EAAE,EAAEC,EAAE,EAAEC,EAAE,EAAEC,CAAC,EAAEC,QAAQ,EAAE;MACpE,IAAIC,EAAE,GAAG,CAAC,GAAGF,CAAC;MACdC,QAAQ,CAACE,CAAC,GAAGD,EAAE,GAAGA,EAAE,GAAGR,EAAE,GAAG,CAAC,GAAGQ,EAAE,GAAGF,CAAC,GAAGJ,EAAE,GAAGI,CAAC,GAAGA,CAAC,GAAGF,EAAE;MACxDG,QAAQ,CAACG,CAAC,GAAGF,EAAE,GAAGA,EAAE,GAAGP,EAAE,GAAG,CAAC,GAAGO,EAAE,GAAGF,CAAC,GAAGH,EAAE,GAAGG,CAAC,GAAGA,CAAC,GAAGD,EAAE;IAC1D;;IAEA;AACF;AACA;IACE,SAASM,kBAAkBA,CAAEX,EAAE,EAAEC,EAAE,EAAEC,EAAE,EAAEC,EAAE,EAAEC,EAAE,EAAEC,EAAE,EAAEO,EAAE,EAAEC,EAAE,EAAEP,CAAC,EAAEC,QAAQ,EAAE;MACxE,IAAIC,EAAE,GAAG,CAAC,GAAGF,CAAC;MACdC,QAAQ,CAACE,CAAC,GAAGD,EAAE,GAAGA,EAAE,GAAGA,EAAE,GAAGR,EAAE,GAAG,CAAC,GAAGQ,EAAE,GAAGA,EAAE,GAAGF,CAAC,GAAGJ,EAAE,GAAG,CAAC,GAAGM,EAAE,GAAGF,CAAC,GAAGA,CAAC,GAAGF,EAAE,GAAGE,CAAC,GAAGA,CAAC,GAAGA,CAAC,GAAGM,EAAE;MAC5FL,QAAQ,CAACG,CAAC,GAAGF,EAAE,GAAGA,EAAE,GAAGA,EAAE,GAAGP,EAAE,GAAG,CAAC,GAAGO,EAAE,GAAGA,EAAE,GAAGF,CAAC,GAAGH,EAAE,GAAG,CAAC,GAAGK,EAAE,GAAGF,CAAC,GAAGA,CAAC,GAAGD,EAAE,GAAGC,CAAC,GAAGA,CAAC,GAAGA,CAAC,GAAGO,EAAE;IAC9F;;IAEA;AACF;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;IACE,SAASC,kBAAkBA,CAACC,UAAU,EAAEC,eAAe,EAAE;MACvD,IAAIC,SAAS,GAAG,uBAAuB;MACvC,IAAIC,KAAK,EAAEC,MAAM,EAAEC,MAAM,EAAEC,KAAK,EAAEC,KAAK;MACvC,OAAQJ,KAAK,GAAGD,SAAS,CAACM,IAAI,CAACR,UAAU,CAAC,EAAG;QAC3C,IAAIS,IAAI,GAAGN,KAAK,CAAC,CAAC,CAAC,CAChBO,OAAO,CAAC,YAAY,EAAE,EAAE,CAAC,CACzBC,KAAK,CAAC,QAAQ,CAAC,CACfC,GAAG,CAAC,UAAUC,CAAC,EAAE;UAAE,OAAOC,UAAU,CAACD,CAAC,CAAC;QAAE,CAAC,CAAC;QAC9C,QAAQV,KAAK,CAAC,CAAC,CAAC;UACd,KAAK,GAAG;YACNG,KAAK,GAAGF,MAAM,GAAGK,IAAI,CAAC,CAAC,CAAC;YACxBF,KAAK,GAAGF,MAAM,GAAGI,IAAI,CAAC,CAAC,CAAC;YACxB;UACF,KAAK,GAAG;YACN,IAAIA,IAAI,CAAC,CAAC,CAAC,KAAKH,KAAK,IAAIG,IAAI,CAAC,CAAC,CAAC,KAAKF,KAAK,EAAE;cAAE;cAC5CN,eAAe,CAAC,GAAG,EAAEK,KAAK,EAAEC,KAAK,EAAGD,KAAK,GAAGG,IAAI,CAAC,CAAC,CAAC,EAAIF,KAAK,GAAGE,IAAI,CAAC,CAAC,CAAE,CAAC;YAC1E;YACA;UACF,KAAK,GAAG;YAAE;cACRR,eAAe,CAAC,GAAG,EAAEK,KAAK,EAAEC,KAAK,EAAGD,KAAK,GAAGG,IAAI,CAAC,CAAC,CAAC,EAAIF,KAAK,GAAGE,IAAI,CAAC,CAAC,CAAC,EAAGA,IAAI,CAAC,CAAC,CAAC,EAAEA,IAAI,CAAC,CAAC,CAAC,CAAC;cAC1F;YACF;UACA,KAAK,GAAG;YAAE;cACRR,eAAe,CAAC,GAAG,EAAEK,KAAK,EAAEC,KAAK,EAAGD,KAAK,GAAGG,IAAI,CAAC,CAAC,CAAC,EAAIF,KAAK,GAAGE,IAAI,CAAC,CAAC,CAAC,EAAGA,IAAI,CAAC,CAAC,CAAC,EAAEA,IAAI,CAAC,CAAC,CAAC,EAAEA,IAAI,CAAC,CAAC,CAAC,EAAEA,IAAI,CAAC,CAAC,CAAC,CAAC;cAC5G;YACF;UACA,KAAK,GAAG;YACN,IAAIH,KAAK,KAAKF,MAAM,IAAIG,KAAK,KAAKF,MAAM,EAAE;cACxCJ,eAAe,CAAC,GAAG,EAAEK,KAAK,EAAEC,KAAK,EAAEH,MAAM,EAAEC,MAAM,CAAC;YACpD;YACA;QACJ;MACF;IACF;;IAEA;AACF;AACA;AACA;AACA;AACA;AACA;AACA;IACE,SAASU,kBAAkBA,CAAEf,UAAU,EAAEgB,eAAe,EAAEC,WAAW,EAAE;MACrE,IAAKA,WAAW,KAAK,KAAK,CAAC,EAAGA,WAAW,GAAG,EAAE;MAE9C,IAAIC,SAAS,GAAG;QAAExB,CAAC,EAAE,CAAC;QAAEC,CAAC,EAAE;MAAE,CAAC;MAC9BI,kBAAkB,CAACC,UAAU,EAAE,UAAUmB,OAAO,EAAEC,MAAM,EAAEC,MAAM,EAAEC,IAAI,EAAEC,IAAI,EAAEC,MAAM,EAAEC,MAAM,EAAEC,MAAM,EAAEC,MAAM,EAAE;QAC5G,QAAQR,OAAO;UACb,KAAK,GAAG;YACNH,eAAe,CAACI,MAAM,EAAEC,MAAM,EAAEC,IAAI,EAAEC,IAAI,CAAC;YAC3C;UACF,KAAK,GAAG;YAAE;cACR,IAAIK,UAAU,GAAGR,MAAM;cACvB,IAAIS,UAAU,GAAGR,MAAM;cACvB,KAAK,IAAIS,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGb,WAAW,EAAEa,CAAC,EAAE,EAAE;gBACpC9C,sBAAsB,CACpBoC,MAAM,EAAEC,MAAM,EACdG,MAAM,EAAEC,MAAM,EACdH,IAAI,EAAEC,IAAI,EACVO,CAAC,IAAIb,WAAW,GAAG,CAAC,CAAC,EACrBC,SACF,CAAC;gBACDF,eAAe,CAACY,UAAU,EAAEC,UAAU,EAAEX,SAAS,CAACxB,CAAC,EAAEwB,SAAS,CAACvB,CAAC,CAAC;gBACjEiC,UAAU,GAAGV,SAAS,CAACxB,CAAC;gBACxBmC,UAAU,GAAGX,SAAS,CAACvB,CAAC;cAC1B;cACA;YACF;UACA,KAAK,GAAG;YAAE;cACR,IAAIoC,YAAY,GAAGX,MAAM;cACzB,IAAIY,YAAY,GAAGX,MAAM;cACzB,KAAK,IAAIY,GAAG,GAAG,CAAC,EAAEA,GAAG,GAAGhB,WAAW,EAAEgB,GAAG,EAAE,EAAE;gBAC1CrC,kBAAkB,CAChBwB,MAAM,EAAEC,MAAM,EACdG,MAAM,EAAEC,MAAM,EACdC,MAAM,EAAEC,MAAM,EACdL,IAAI,EAAEC,IAAI,EACVU,GAAG,IAAIhB,WAAW,GAAG,CAAC,CAAC,EACvBC,SACF,CAAC;gBACDF,eAAe,CAACe,YAAY,EAAEC,YAAY,EAAEd,SAAS,CAACxB,CAAC,EAAEwB,SAAS,CAACvB,CAAC,CAAC;gBACrEoC,YAAY,GAAGb,SAAS,CAACxB,CAAC;gBAC1BsC,YAAY,GAAGd,SAAS,CAACvB,CAAC;cAC5B;cACA;YACF;QACF;MACF,CAAC,CAAC;IACJ;IAEA,IAAIuC,kBAAkB,GAAG,yIAAyI;IAElK,IAAIC,eAAe,GAAG,4GAA4G;IAElI,IAAIC,KAAK,GAAG,IAAIC,OAAO,CAAC,CAAC;IAEzB,IAAIC,eAAe,GAAG;MACpBC,kBAAkB,EAAE,KAAK;MACzBC,qBAAqB,EAAE,IAAI;MAC3BC,SAAS,EAAE,KAAK;MAChBC,KAAK,EAAE;IACT,CAAC;;IAED;AACF;AACA;AACA;AACA;AACA;AACA;IACE,SAASC,gBAAgBA,CAAEC,UAAU,EAAEC,QAAQ,EAAE;MAC/C,IAAIC,EAAE,GAAGF,UAAU,CAACG,UAAU,GAAGH,UAAU,CAACG,UAAU,CAAC,OAAO,EAAET,eAAe,CAAC,GAAGM,UAAU;MAC7F,IAAII,OAAO,GAAGZ,KAAK,CAACa,GAAG,CAACH,EAAE,CAAC;MAC3B,IAAI,CAACE,OAAO,EAAE;QACZ,IAAIE,QAAQ,GAAG,OAAOC,sBAAsB,KAAK,WAAW,IAAIL,EAAE,YAAYK,sBAAsB;QACpG,IAAIC,UAAU,GAAG,CAAC,CAAC;QACnB,IAAIC,QAAQ,GAAG,CAAC,CAAC;QACjB,IAAIC,QAAQ,GAAG,CAAC,CAAC;QACjB,IAAIC,WAAW,GAAG,CAAC,CAAC;QACpB,IAAIC,gBAAgB,GAAG,EAAE;QAEzBV,EAAE,CAACW,MAAM,CAACC,gBAAgB,CAAC,kBAAkB,EAAE,UAAUC,CAAC,EAAE;UAC1DC,iBAAiB,CAAC,CAAC;UACnBD,CAAC,CAACE,cAAc,CAAC,CAAC;QACpB,CAAC,EAAE,KAAK,CAAC;QAET,SAASC,YAAYA,CAAEC,IAAI,EAAE;UAC3B,IAAIC,GAAG,GAAGZ,UAAU,CAACW,IAAI,CAAC;UAC1B,IAAI,CAACC,GAAG,EAAE;YACRA,GAAG,GAAGZ,UAAU,CAACW,IAAI,CAAC,GAAGjB,EAAE,CAACgB,YAAY,CAACC,IAAI,CAAC;YAC9C,IAAI,CAACC,GAAG,EAAE;cACR,MAAM,IAAIC,KAAK,CAAEF,IAAI,GAAG,gBAAiB,CAAC;YAC5C;UACF;UACA,OAAOC,GAAG;QACZ;QAEA,SAASE,aAAaA,CAAEC,GAAG,EAAEC,IAAI,EAAE;UACjC,IAAIC,MAAM,GAAGvB,EAAE,CAACwB,YAAY,CAACF,IAAI,CAAC;UAClCtB,EAAE,CAACyB,YAAY,CAACF,MAAM,EAAEF,GAAG,CAAC;UAC5BrB,EAAE,CAACoB,aAAa,CAACG,MAAM,CAAC;UACxB;UACA;UACA;UACA;UACA,OAAOA,MAAM;QACf;QAEA,SAASG,WAAWA,CAAET,IAAI,EAAEU,IAAI,EAAEC,IAAI,EAAEC,IAAI,EAAE;UAC5C,IAAI,CAACtB,QAAQ,CAACU,IAAI,CAAC,EAAE;YACnB,IAAIa,UAAU,GAAG,CAAC,CAAC;YACnB,IAAIC,QAAQ,GAAG,CAAC,CAAC;YACjB,IAAIC,OAAO,GAAGhC,EAAE,CAACiC,aAAa,CAAC,CAAC;YAChCjC,EAAE,CAACkC,YAAY,CAACF,OAAO,EAAEZ,aAAa,CAACO,IAAI,EAAE3B,EAAE,CAACmC,aAAa,CAAC,CAAC;YAC/DnC,EAAE,CAACkC,YAAY,CAACF,OAAO,EAAEZ,aAAa,CAACQ,IAAI,EAAE5B,EAAE,CAACoC,eAAe,CAAC,CAAC;YACjEpC,EAAE,CAACqC,WAAW,CAACL,OAAO,CAAC;YAEvBzB,QAAQ,CAACU,IAAI,CAAC,GAAG;cACfe,OAAO,EAAEA,OAAO;cAChBM,WAAW,EAAE,SAASA,WAAWA,CAAET,IAAI,EAAE;gBACvC7B,EAAE,CAACuC,UAAU,CAACP,OAAO,CAAC;gBACtBH,IAAI,CAAC;kBACHW,UAAU,EAAE,SAASA,UAAUA,CAAElB,IAAI,EAAEL,IAAI,EAAE;oBAC3C,IAAIwB,MAAM,GAAG,EAAE;sBAAEC,GAAG,GAAGC,SAAS,CAACC,MAAM,GAAG,CAAC;oBAC3C,OAAQF,GAAG,EAAE,GAAG,CAAC,EAAGD,MAAM,CAAEC,GAAG,CAAE,GAAGC,SAAS,CAAED,GAAG,GAAG,CAAC,CAAE;oBAExD,IAAIG,UAAU,GAAGd,QAAQ,CAACd,IAAI,CAAC,KAAKc,QAAQ,CAACd,IAAI,CAAC,GAAGjB,EAAE,CAAC8C,kBAAkB,CAACd,OAAO,EAAEf,IAAI,CAAC,CAAC;oBAC1FjB,EAAE,CAAE,SAAS,GAAGsB,IAAI,CAAE,CAACyB,KAAK,CAAC/C,EAAE,EAAE,CAAE6C,UAAU,CAAE,CAACG,MAAM,CAAEP,MAAO,CAAC,CAAC;kBACnE,CAAC;kBAEDQ,YAAY,EAAE,SAASA,YAAYA,CAAEhC,IAAI,EAAEiC,IAAI,EAAEC,KAAK,EAAEC,iBAAiB,EAAEC,IAAI,EAAE;oBAC/E,IAAIC,IAAI,GAAGxB,UAAU,CAACb,IAAI,CAAC;oBAC3B,IAAI,CAACqC,IAAI,EAAE;sBACTA,IAAI,GAAGxB,UAAU,CAACb,IAAI,CAAC,GAAG;wBACxBsC,GAAG,EAAEvD,EAAE,CAACwD,YAAY,CAAC,CAAC;wBAAE;wBACxBC,GAAG,EAAEzD,EAAE,CAAC0D,iBAAiB,CAAC1B,OAAO,EAAEf,IAAI,CAAC;wBACxCoC,IAAI,EAAE;sBACR,CAAC;oBACH;oBACArD,EAAE,CAAC2D,UAAU,CAAC3D,EAAE,CAAC4D,YAAY,EAAEN,IAAI,CAACC,GAAG,CAAC;oBACxCvD,EAAE,CAAC6D,mBAAmB,CAACP,IAAI,CAACG,GAAG,EAAEP,IAAI,EAAElD,EAAE,CAAC8D,KAAK,EAAE,KAAK,EAAE,CAAC,EAAE,CAAC,CAAC;oBAC7D9D,EAAE,CAAC+D,uBAAuB,CAACT,IAAI,CAACG,GAAG,CAAC;oBACpC,IAAIrD,QAAQ,EAAE;sBACZJ,EAAE,CAACgE,mBAAmB,CAACV,IAAI,CAACG,GAAG,EAAEL,iBAAiB,CAAC;oBACrD,CAAC,MAAM;sBACLpC,YAAY,CAAC,wBAAwB,CAAC,CAACiD,wBAAwB,CAACX,IAAI,CAACG,GAAG,EAAEL,iBAAiB,CAAC;oBAC9F;oBACA,IAAIC,IAAI,KAAKC,IAAI,CAACD,IAAI,EAAE;sBACtBrD,EAAE,CAACkE,UAAU,CAAClE,EAAE,CAAC4D,YAAY,EAAEP,IAAI,EAAEF,KAAK,CAAC;sBAC3CG,IAAI,CAACD,IAAI,GAAGA,IAAI;oBAClB;kBACF;gBACF,CAAC,CAAC;cACJ;YACF,CAAC;UACH;UAEA9C,QAAQ,CAACU,IAAI,CAAC,CAACqB,WAAW,CAACT,IAAI,CAAC;QAClC;QAEA,SAASsC,WAAWA,CAAElD,IAAI,EAAEY,IAAI,EAAE;UAChCpB,WAAW,EAAE;UACb,IAAI;YACFT,EAAE,CAACoE,aAAa,CAACpE,EAAE,CAACqE,QAAQ,GAAG5D,WAAW,CAAC;YAC3C,IAAI6D,OAAO,GAAG9D,QAAQ,CAACS,IAAI,CAAC;YAC5B,IAAI,CAACqD,OAAO,EAAE;cACZA,OAAO,GAAG9D,QAAQ,CAACS,IAAI,CAAC,GAAGjB,EAAE,CAACuE,aAAa,CAAC,CAAC;cAC7CvE,EAAE,CAACwE,WAAW,CAACxE,EAAE,CAACyE,UAAU,EAAEH,OAAO,CAAC;cACtCtE,EAAE,CAAC0E,aAAa,CAAC1E,EAAE,CAACyE,UAAU,EAAEzE,EAAE,CAAC2E,kBAAkB,EAAE3E,EAAE,CAAC4E,OAAO,CAAC;cAClE5E,EAAE,CAAC0E,aAAa,CAAC1E,EAAE,CAACyE,UAAU,EAAEzE,EAAE,CAAC6E,kBAAkB,EAAE7E,EAAE,CAAC4E,OAAO,CAAC;YACpE;YACA5E,EAAE,CAACwE,WAAW,CAACxE,EAAE,CAACyE,UAAU,EAAEH,OAAO,CAAC;YACtCzC,IAAI,CAACyC,OAAO,EAAE7D,WAAW,CAAC;UAC5B,CAAC,SAAS;YACRA,WAAW,EAAE;UACf;QACF;QAEA,SAASqE,sBAAsBA,CAAER,OAAO,EAAE7D,WAAW,EAAEoB,IAAI,EAAE;UAC3D,IAAIkD,WAAW,GAAG/E,EAAE,CAACgF,iBAAiB,CAAC,CAAC;UACxCtE,gBAAgB,CAACuE,IAAI,CAACF,WAAW,CAAC;UAClC/E,EAAE,CAACkF,eAAe,CAAClF,EAAE,CAACmF,WAAW,EAAEJ,WAAW,CAAC;UAC/C/E,EAAE,CAACoE,aAAa,CAACpE,EAAE,CAACqE,QAAQ,GAAG5D,WAAW,CAAC;UAC3CT,EAAE,CAACwE,WAAW,CAACxE,EAAE,CAACyE,UAAU,EAAEH,OAAO,CAAC;UACtCtE,EAAE,CAACoF,oBAAoB,CAACpF,EAAE,CAACmF,WAAW,EAAEnF,EAAE,CAACqF,iBAAiB,EAAErF,EAAE,CAACyE,UAAU,EAAEH,OAAO,EAAE,CAAC,CAAC;UACxF,IAAI;YACFzC,IAAI,CAACkD,WAAW,CAAC;UACnB,CAAC,SAAS;YACR/E,EAAE,CAACsF,iBAAiB,CAACP,WAAW,CAAC;YACjC/E,EAAE,CAACkF,eAAe,CAAClF,EAAE,CAACmF,WAAW,EAAEzE,gBAAgB,CAAC,EAAEA,gBAAgB,CAACkC,MAAM,GAAG,CAAC,CAAC,IAAI,IAAI,CAAC;UAC7F;QACF;QAEA,SAAS9B,iBAAiBA,CAAA,EAAI;UAC5BR,UAAU,GAAG,CAAC,CAAC;UACfC,QAAQ,GAAG,CAAC,CAAC;UACbC,QAAQ,GAAG,CAAC,CAAC;UACbC,WAAW,GAAG,CAAC,CAAC;UAChBC,gBAAgB,CAACkC,MAAM,GAAG,CAAC;QAC7B;QAEAtD,KAAK,CAACiG,GAAG,CAACvF,EAAE,EAAEE,OAAO,GAAG;UACtBF,EAAE,EAAEA,EAAE;UACNI,QAAQ,EAAEA,QAAQ;UAClBY,YAAY,EAAEA,YAAY;UAC1BU,WAAW,EAAEA,WAAW;UACxByC,WAAW,EAAEA,WAAW;UACxBW,sBAAsB,EAAEA,sBAAsB;UAC9ChE,iBAAiB,EAAEA;QACrB,CAAC,CAAC;MACJ;MACAf,QAAQ,CAACG,OAAO,CAAC;IACnB;IAGA,SAASsF,eAAeA,CAAC1F,UAAU,EAAE2F,SAAS,EAAE7I,CAAC,EAAEC,CAAC,EAAE6I,KAAK,EAAEC,MAAM,EAAEC,QAAQ,EAAEb,WAAW,EAAE;MAC1F,IAAKa,QAAQ,KAAK,KAAK,CAAC,EAAGA,QAAQ,GAAG,EAAE;MACxC,IAAKb,WAAW,KAAK,KAAK,CAAC,EAAGA,WAAW,GAAG,IAAI;MAEhDlF,gBAAgB,CAACC,UAAU,EAAE,UAAU+F,GAAG,EAAE;QAC1C,IAAI7F,EAAE,GAAG6F,GAAG,CAAC7F,EAAE;QACf,IAAI0B,WAAW,GAAGmE,GAAG,CAACnE,WAAW;QACjC,IAAIyC,WAAW,GAAG0B,GAAG,CAAC1B,WAAW;QAEjCA,WAAW,CAAC,MAAM,EAAE,UAAU2B,GAAG,EAAEC,OAAO,EAAE;UAC1C/F,EAAE,CAACgG,UAAU,CAAChG,EAAE,CAACyE,UAAU,EAAE,CAAC,EAAEzE,EAAE,CAACiG,IAAI,EAAEP,KAAK,EAAEC,MAAM,EAAE,CAAC,EAAE3F,EAAE,CAACiG,IAAI,EAAEjG,EAAE,CAACkG,aAAa,EAAET,SAAS,CAAC;UAChG/D,WAAW,CAAC,MAAM,EAAEtC,kBAAkB,EAAEC,eAAe,EAAE,UAAUwG,GAAG,EAAE;YACtE,IAAIrD,UAAU,GAAGqD,GAAG,CAACrD,UAAU;YAC/B,IAAIS,YAAY,GAAG4C,GAAG,CAAC5C,YAAY;YAEnCA,YAAY,CAAC,KAAK,EAAE,CAAC,EAAEjD,EAAE,CAACmG,WAAW,EAAE,CAAC,EAAE,IAAIC,YAAY,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;YAC/E5D,UAAU,CAAC,IAAI,EAAE,OAAO,EAAEuD,OAAO,CAAC;YAClC/F,EAAE,CAACkF,eAAe,CAAClF,EAAE,CAACmF,WAAW,EAAEJ,WAAW,IAAI,IAAI,CAAC;YACvD/E,EAAE,CAACqG,OAAO,CAACrG,EAAE,CAACsG,KAAK,CAAC;YACpBtG,EAAE,CAACuG,SAAS,CAACX,QAAQ,GAAG,CAAC,EAAEA,QAAQ,GAAG,CAAC,EAAEA,QAAQ,GAAG,CAAC,EAAEA,QAAQ,GAAG,CAAC,CAAC;YACpE5F,EAAE,CAACwG,QAAQ,CAAC5J,CAAC,EAAEC,CAAC,EAAE6I,KAAK,EAAEC,MAAM,CAAC;YAChC3F,EAAE,CAACyG,OAAO,CAAC7J,CAAC,EAAEC,CAAC,EAAE6I,KAAK,EAAEC,MAAM,CAAC;YAC/B3F,EAAE,CAAC0G,UAAU,CAAC1G,EAAE,CAAC2G,SAAS,EAAE,CAAC,EAAE,CAAC,CAAC;UACnC,CAAC,CAAC;QACJ,CAAC,CAAC;MACJ,CAAC,CAAC;IACJ;;IAEA;AACF;AACA;AACA;AACA;AACA;IACE,SAASC,gCAAgCA,CAACjG,MAAM,EAAEkG,QAAQ,EAAEC,SAAS,EAAE;MACrE,IAAIpB,KAAK,GAAG/E,MAAM,CAAC+E,KAAK;MACxB,IAAIC,MAAM,GAAGhF,MAAM,CAACgF,MAAM;MAC1B9F,gBAAgB,CAACc,MAAM,EAAE,UAAUkF,GAAG,EAAE;QACtC,IAAI7F,EAAE,GAAG6F,GAAG,CAAC7F,EAAE;QAEf,IAAIqD,IAAI,GAAG,IAAI0D,UAAU,CAACrB,KAAK,GAAGC,MAAM,GAAG,CAAC,CAAC;QAC7C3F,EAAE,CAACgH,UAAU,CAAC,CAAC,EAAE,CAAC,EAAEtB,KAAK,EAAEC,MAAM,EAAE3F,EAAE,CAACiG,IAAI,EAAEjG,EAAE,CAACkG,aAAa,EAAE7C,IAAI,CAAC;QACnE1C,MAAM,CAAC+E,KAAK,GAAGmB,QAAQ;QACvBlG,MAAM,CAACgF,MAAM,GAAGmB,SAAS;QACzBtB,eAAe,CAACxF,EAAE,EAAEqD,IAAI,EAAE,CAAC,EAAE,CAAC,EAAEqC,KAAK,EAAEC,MAAM,CAAC;MAChD,CAAC,CAAC;IACJ;IAEA,IAAIsB,UAAU,GAAG,aAAaC,MAAM,CAACC,MAAM,CAAC;MAC1CC,SAAS,EAAE,IAAI;MACfvH,gBAAgB,EAAEA,gBAAgB;MAClC2F,eAAe,EAAEA,eAAe;MAChCoB,gCAAgC,EAAEA;IACpC,CAAC,CAAC;IAEF,SAASS,UAAUA,CAAEC,QAAQ,EAAEC,SAAS,EAAEC,IAAI,EAAEC,OAAO,EAAEC,WAAW,EAAEC,WAAW,EAAE;MACjF,IAAKA,WAAW,KAAK,KAAK,CAAC,EAAGA,WAAW,GAAG,CAAC;MAE7C,IAAIC,WAAW,GAAG,IAAIb,UAAU,CAACO,QAAQ,GAAGC,SAAS,CAAC;MAEtD,IAAIM,YAAY,GAAGJ,OAAO,CAAC,CAAC,CAAC,GAAGA,OAAO,CAAC,CAAC,CAAC;MAC1C,IAAIK,aAAa,GAAGL,OAAO,CAAC,CAAC,CAAC,GAAGA,OAAO,CAAC,CAAC,CAAC;;MAE3C;MACA,IAAIM,QAAQ,GAAG,EAAE;MACjB9J,kBAAkB,CAACuJ,IAAI,EAAE,UAAUnL,EAAE,EAAEC,EAAE,EAAEC,EAAE,EAAEC,EAAE,EAAE;QACjDuL,QAAQ,CAAC9C,IAAI,CAAC;UACZ5I,EAAE,EAAEA,EAAE;UAAEC,EAAE,EAAEA,EAAE;UAAEC,EAAE,EAAEA,EAAE;UAAEC,EAAE,EAAEA,EAAE;UAC9BwL,IAAI,EAAEC,IAAI,CAACC,GAAG,CAAC7L,EAAE,EAAEE,EAAE,CAAC;UACtB4L,IAAI,EAAEF,IAAI,CAACC,GAAG,CAAC5L,EAAE,EAAEE,EAAE,CAAC;UACtB4L,IAAI,EAAEH,IAAI,CAACI,GAAG,CAAChM,EAAE,EAAEE,EAAE,CAAC;UACtB+L,IAAI,EAAEL,IAAI,CAACI,GAAG,CAAC/L,EAAE,EAAEE,EAAE;QACvB,CAAC,CAAC;MACJ,CAAC,CAAC;;MAEF;MACAuL,QAAQ,CAACQ,IAAI,CAAC,UAAUC,CAAC,EAAEC,CAAC,EAAE;QAAE,OAAOD,CAAC,CAACJ,IAAI,GAAGK,CAAC,CAACL,IAAI;MAAE,CAAC,CAAC;;MAE1D;MACA;MACA,KAAK,IAAIM,IAAI,GAAG,CAAC,EAAEA,IAAI,GAAGpB,QAAQ,EAAEoB,IAAI,EAAE,EAAE;QAC1C,KAAK,IAAIC,IAAI,GAAG,CAAC,EAAEA,IAAI,GAAGpB,SAAS,EAAEoB,IAAI,EAAE,EAAE;UAC3C,IAAIC,UAAU,GAAGC,yBAAyB,CACxCpB,OAAO,CAAC,CAAC,CAAC,GAAGI,YAAY,IAAIa,IAAI,GAAG,GAAG,CAAC,GAAGpB,QAAQ,EACnDG,OAAO,CAAC,CAAC,CAAC,GAAGK,aAAa,IAAIa,IAAI,GAAG,GAAG,CAAC,GAAGpB,SAC9C,CAAC;;UAED;UACA;UACA;UACA,IAAIuB,KAAK,GAAGb,IAAI,CAACc,GAAG,CAAE,CAAC,GAAGd,IAAI,CAACe,GAAG,CAACJ,UAAU,CAAC,GAAGlB,WAAW,EAAGC,WAAW,CAAC,GAAG,CAAC;UAC/E,IAAIiB,UAAU,GAAG,CAAC,EAAE;YAClBE,KAAK,GAAG,CAAC,GAAGA,KAAK;UACnB;UAEAA,KAAK,GAAGb,IAAI,CAACI,GAAG,CAAC,CAAC,EAAEJ,IAAI,CAACC,GAAG,CAAC,GAAG,EAAED,IAAI,CAACgB,KAAK,CAACH,KAAK,GAAG,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC;UAC7DlB,WAAW,CAACe,IAAI,GAAGrB,QAAQ,GAAGoB,IAAI,CAAC,GAAGI,KAAK;QAC7C;MACF;MAEA,OAAOlB,WAAW;;MAElB;AACJ;AACA;AACA;AACA;AACA;AACA;MACI,SAASiB,yBAAyBA,CAAEjM,CAAC,EAAEC,CAAC,EAAE;QACxC,IAAIqM,aAAa,GAAGC,QAAQ;QAC5B,IAAIC,WAAW,GAAGD,QAAQ;QAE1B,KAAK,IAAInK,CAAC,GAAG+I,QAAQ,CAACnF,MAAM,EAAE5D,CAAC,EAAE,GAAG;UAClC,IAAIqK,GAAG,GAAGtB,QAAQ,CAAC/I,CAAC,CAAC;UACrB,IAAIqK,GAAG,CAACjB,IAAI,GAAGgB,WAAW,IAAIxM,CAAC,EAAE;YAAE;UAAM,CAAC,CAAC;UAC3C,IAAIA,CAAC,GAAGwM,WAAW,GAAGC,GAAG,CAACrB,IAAI,IAAInL,CAAC,GAAGuM,WAAW,GAAGC,GAAG,CAACf,IAAI,IAAIzL,CAAC,GAAGuM,WAAW,GAAGC,GAAG,CAAClB,IAAI,EAAE;YAC1F,IAAImB,MAAM,GAAGC,8BAA8B,CAAC3M,CAAC,EAAEC,CAAC,EAAEwM,GAAG,CAAChN,EAAE,EAAEgN,GAAG,CAAC/M,EAAE,EAAE+M,GAAG,CAAC9M,EAAE,EAAE8M,GAAG,CAAC7M,EAAE,CAAC;YACjF,IAAI8M,MAAM,GAAGJ,aAAa,EAAE;cAC1BA,aAAa,GAAGI,MAAM;cACtBF,WAAW,GAAGnB,IAAI,CAACuB,IAAI,CAACN,aAAa,CAAC;YACxC;UACF;QACF;;QAEA;QACA,IAAIO,aAAa,CAAC7M,CAAC,EAAEC,CAAC,CAAC,EAAE;UACvBuM,WAAW,GAAG,CAACA,WAAW;QAC5B;QACA,OAAOA,WAAW;MACpB;;MAEA;AACJ;AACA;AACA;MACI,SAASK,aAAaA,CAAE7M,CAAC,EAAEC,CAAC,EAAE;QAC5B,IAAI6M,OAAO,GAAG,CAAC;QACf,KAAK,IAAI1K,CAAC,GAAG+I,QAAQ,CAACnF,MAAM,EAAE5D,CAAC,EAAE,GAAG;UAClC,IAAIqK,GAAG,GAAGtB,QAAQ,CAAC/I,CAAC,CAAC;UACrB,IAAIqK,GAAG,CAACjB,IAAI,IAAIxL,CAAC,EAAE;YAAE;UAAM,CAAC,CAAC;UAC7B,IAAI+M,UAAU,GAAKN,GAAG,CAAC/M,EAAE,GAAGO,CAAC,KAAOwM,GAAG,CAAC7M,EAAE,GAAGK,CAAE,IAAMD,CAAC,GAAG,CAACyM,GAAG,CAAC9M,EAAE,GAAG8M,GAAG,CAAChN,EAAE,KAAKQ,CAAC,GAAGwM,GAAG,CAAC/M,EAAE,CAAC,IAAI+M,GAAG,CAAC7M,EAAE,GAAG6M,GAAG,CAAC/M,EAAE,CAAC,GAAG+M,GAAG,CAAChN,EAAG;UACvH,IAAIsN,UAAU,EAAE;YACdD,OAAO,IAAIL,GAAG,CAAC/M,EAAE,GAAG+M,GAAG,CAAC7M,EAAE,GAAG,CAAC,GAAG,CAAC,CAAC;UACrC;QACF;QACA,OAAOkN,OAAO,KAAK,CAAC;MACtB;IACF;IAEA,SAASE,oBAAoBA,CAACtC,QAAQ,EAAEC,SAAS,EAAEC,IAAI,EAAEC,OAAO,EAAEC,WAAW,EAAEC,WAAW,EAAEhH,MAAM,EAAE/D,CAAC,EAAEC,CAAC,EAAEgN,OAAO,EAAE;MACjH,IAAKlC,WAAW,KAAK,KAAK,CAAC,EAAGA,WAAW,GAAG,CAAC;MAC7C,IAAK/K,CAAC,KAAK,KAAK,CAAC,EAAGA,CAAC,GAAG,CAAC;MACzB,IAAKC,CAAC,KAAK,KAAK,CAAC,EAAGA,CAAC,GAAG,CAAC;MACzB,IAAKgN,OAAO,KAAK,KAAK,CAAC,EAAGA,OAAO,GAAG,CAAC;MAErCC,yBAAyB,CAACxC,QAAQ,EAAEC,SAAS,EAAEC,IAAI,EAAEC,OAAO,EAAEC,WAAW,EAAEC,WAAW,EAAEhH,MAAM,EAAE,IAAI,EAAE/D,CAAC,EAAEC,CAAC,EAAEgN,OAAO,CAAC;IACtH;IAEA,SAASC,yBAAyBA,CAAExC,QAAQ,EAAEC,SAAS,EAAEC,IAAI,EAAEC,OAAO,EAAEC,WAAW,EAAEC,WAAW,EAAE7H,UAAU,EAAEiF,WAAW,EAAEnI,CAAC,EAAEC,CAAC,EAAEgN,OAAO,EAAE;MACxI,IAAKlC,WAAW,KAAK,KAAK,CAAC,EAAGA,WAAW,GAAG,CAAC;MAC7C,IAAK/K,CAAC,KAAK,KAAK,CAAC,EAAGA,CAAC,GAAG,CAAC;MACzB,IAAKC,CAAC,KAAK,KAAK,CAAC,EAAGA,CAAC,GAAG,CAAC;MACzB,IAAKgN,OAAO,KAAK,KAAK,CAAC,EAAGA,OAAO,GAAG,CAAC;MAErC,IAAIxG,IAAI,GAAGgE,UAAU,CAACC,QAAQ,EAAEC,SAAS,EAAEC,IAAI,EAAEC,OAAO,EAAEC,WAAW,EAAEC,WAAW,CAAC;MACnF;MACA,IAAIoC,QAAQ,GAAG,IAAIhD,UAAU,CAAC1D,IAAI,CAACT,MAAM,GAAG,CAAC,CAAC;MAC9C,KAAK,IAAI5D,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGqE,IAAI,CAACT,MAAM,EAAE5D,CAAC,EAAE,EAAE;QACpC+K,QAAQ,CAAC/K,CAAC,GAAG,CAAC,GAAG6K,OAAO,CAAC,GAAGxG,IAAI,CAACrE,CAAC,CAAC;MACrC;MACAwG,eAAe,CAAC1F,UAAU,EAAEiK,QAAQ,EAAEnN,CAAC,EAAEC,CAAC,EAAEyK,QAAQ,EAAEC,SAAS,EAAE,CAAC,IAAK,CAAC,GAAGsC,OAAQ,EAAE9E,WAAW,CAAC;IACnG;;IAEA;AACF;AACA;IACE,SAASwE,8BAA8BA,CAAE3M,CAAC,EAAEC,CAAC,EAAEmN,MAAM,EAAEC,MAAM,EAAEC,MAAM,EAAEC,MAAM,EAAE;MAC7E,IAAIC,GAAG,GAAGF,MAAM,GAAGF,MAAM;MACzB,IAAIK,GAAG,GAAGF,MAAM,GAAGF,MAAM;MACzB,IAAIK,QAAQ,GAAGF,GAAG,GAAGA,GAAG,GAAGC,GAAG,GAAGA,GAAG;MACpC,IAAI5N,CAAC,GAAG6N,QAAQ,GAAGrC,IAAI,CAACI,GAAG,CAAC,CAAC,EAAEJ,IAAI,CAACC,GAAG,CAAC,CAAC,EAAE,CAAC,CAACtL,CAAC,GAAGoN,MAAM,IAAII,GAAG,GAAG,CAACvN,CAAC,GAAGoN,MAAM,IAAII,GAAG,IAAIC,QAAQ,CAAC,CAAC,GAAG,CAAC;MACrG,IAAIC,EAAE,GAAG3N,CAAC,IAAIoN,MAAM,GAAGvN,CAAC,GAAG2N,GAAG,CAAC;MAC/B,IAAII,EAAE,GAAG3N,CAAC,IAAIoN,MAAM,GAAGxN,CAAC,GAAG4N,GAAG,CAAC;MAC/B,OAAOE,EAAE,GAAGA,EAAE,GAAGC,EAAE,GAAGA,EAAE;IAC1B;IAEA,IAAIC,UAAU,GAAG,aAAavD,MAAM,CAACC,MAAM,CAAC;MAC1CC,SAAS,EAAE,IAAI;MACfsD,QAAQ,EAAErD,UAAU;MACpBsD,kBAAkB,EAAEf,oBAAoB;MACxCgB,uBAAuB,EAAEd;IAC3B,CAAC,CAAC;IAEF,IAAIe,UAAU,GAAG,kSAAkS;IAEnT,IAAIC,YAAY,GAAG,gyBAAgyB;IAEnzB,IAAIC,YAAY,GAAG,uMAAuM;;IAE1N;IACA,IAAIC,WAAW,GAAG,IAAI5E,YAAY,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC;IAEtD,IAAI6E,eAAe,GAAG,IAAI;IAC1B,IAAIC,gBAAgB,GAAG,KAAK;IAC5B,IAAIC,WAAW,GAAG,CAAC,CAAC;IACpB,IAAIC,eAAe,GAAG,IAAI7L,OAAO,CAAC,CAAC,CAAC,CAAC;;IAErC,SAAS8L,eAAeA,CAAEvL,UAAU,EAAE;MACpC,IAAI,CAACoL,gBAAgB,IAAI,CAACI,WAAW,CAACxL,UAAU,CAAC,EAAE;QACjD,MAAM,IAAIqB,KAAK,CAAC,gCAAgC,CAAC;MACnD;IACF;IAEA,SAASoK,UAAUA,CAAEjE,QAAQ,EAAEC,SAAS,EAAEC,IAAI,EAAEC,OAAO,EAAEC,WAAW,EAAEC,WAAW,EAAE7H,UAAU,EAAE;MAC7F,IAAK6H,WAAW,KAAK,KAAK,CAAC,EAAGA,WAAW,GAAG,CAAC;MAC7C,IAAK7H,UAAU,KAAK,KAAK,CAAC,EAAGA,UAAU,GAAG,IAAI;MAE9C,IAAI,CAACA,UAAU,EAAE;QACfA,UAAU,GAAGmL,eAAe;QAC5B,IAAI,CAACnL,UAAU,EAAE;UACf,IAAIa,MAAM,GAAG,OAAO6K,eAAe,KAAK,UAAU,GAC9C,IAAIA,eAAe,CAAC,CAAC,EAAE,CAAC,CAAC,GACzB,OAAOC,QAAQ,KAAK,WAAW,GAC7BA,QAAQ,CAACC,aAAa,CAAC,QAAQ,CAAC,GAChC,IAAI;UACV,IAAI,CAAC/K,MAAM,EAAE;YACX,MAAM,IAAIQ,KAAK,CAAC,6CAA6C,CAAC;UAChE;UACArB,UAAU,GAAGmL,eAAe,GAAGtK,MAAM,CAACV,UAAU,CAAC,OAAO,EAAE;YAAEL,KAAK,EAAE;UAAM,CAAC,CAAC;QAC7E;MACF;MAEAyL,eAAe,CAACvL,UAAU,CAAC;MAE3B,IAAIiK,QAAQ,GAAG,IAAIhD,UAAU,CAACO,QAAQ,GAAGC,SAAS,GAAG,CAAC,CAAC,CAAC,CAAC;;MAEzD;MACA1H,gBAAgB,CAACC,UAAU,EAAE,UAAU+F,GAAG,EAAE;QAC1C,IAAI7F,EAAE,GAAG6F,GAAG,CAAC7F,EAAE;QACf,IAAImE,WAAW,GAAG0B,GAAG,CAAC1B,WAAW;QACjC,IAAIW,sBAAsB,GAAGe,GAAG,CAACf,sBAAsB;QAEvDX,WAAW,CAAC,UAAU,EAAE,UAAUG,OAAO,EAAE7D,WAAW,EAAE;UACtDT,EAAE,CAACgG,UAAU,CAAChG,EAAE,CAACyE,UAAU,EAAE,CAAC,EAAEzE,EAAE,CAACiG,IAAI,EAAEqB,QAAQ,EAAEC,SAAS,EAAE,CAAC,EAAEvH,EAAE,CAACiG,IAAI,EAAEjG,EAAE,CAACkG,aAAa,EAAE,IAAI,CAAC;UAEjGpB,sBAAsB,CAACR,OAAO,EAAE7D,WAAW,EAAE,UAAUsE,WAAW,EAAE;YAClE6F,uBAAuB,CACrBtD,QAAQ,EACRC,SAAS,EACTC,IAAI,EACJC,OAAO,EACPC,WAAW,EACXC,WAAW,EACX3H,EAAE,EACF+E,WAAW,EACX,CAAC,EACD,CAAC,EACD,CAAC,CAAC;YACJ,CAAC;YACD/E,EAAE,CAACgH,UAAU,CAAC,CAAC,EAAE,CAAC,EAAEM,QAAQ,EAAEC,SAAS,EAAEvH,EAAE,CAACiG,IAAI,EAAEjG,EAAE,CAACkG,aAAa,EAAE6D,QAAQ,CAAC;UAC/E,CAAC,CAAC;QACJ,CAAC,CAAC;MACJ,CAAC,CAAC;;MAEF;MACA,IAAI1G,IAAI,GAAG,IAAI0D,UAAU,CAACO,QAAQ,GAAGC,SAAS,CAAC;MAC/C,KAAK,IAAIvI,CAAC,GAAG,CAAC,EAAE2M,CAAC,GAAG,CAAC,EAAE3M,CAAC,GAAG+K,QAAQ,CAACnH,MAAM,EAAE5D,CAAC,IAAI,CAAC,EAAE;QAClDqE,IAAI,CAACsI,CAAC,EAAE,CAAC,GAAG5B,QAAQ,CAAC/K,CAAC,CAAC;MACzB;MAEA,OAAOqE,IAAI;IACb;IAEA,SAASuI,oBAAoBA,CAACtE,QAAQ,EAAEC,SAAS,EAAEC,IAAI,EAAEC,OAAO,EAAEC,WAAW,EAAEC,WAAW,EAAEhH,MAAM,EAAE/D,CAAC,EAAEC,CAAC,EAAEgN,OAAO,EAAE;MACjH,IAAKlC,WAAW,KAAK,KAAK,CAAC,EAAGA,WAAW,GAAG,CAAC;MAC7C,IAAK/K,CAAC,KAAK,KAAK,CAAC,EAAGA,CAAC,GAAG,CAAC;MACzB,IAAKC,CAAC,KAAK,KAAK,CAAC,EAAGA,CAAC,GAAG,CAAC;MACzB,IAAKgN,OAAO,KAAK,KAAK,CAAC,EAAGA,OAAO,GAAG,CAAC;MAErCe,uBAAuB,CAACtD,QAAQ,EAAEC,SAAS,EAAEC,IAAI,EAAEC,OAAO,EAAEC,WAAW,EAAEC,WAAW,EAAEhH,MAAM,EAAE,IAAI,EAAE/D,CAAC,EAAEC,CAAC,EAAEgN,OAAO,CAAC;IACpH;IAEA,SAASe,uBAAuBA,CAAEtD,QAAQ,EAAEC,SAAS,EAAEC,IAAI,EAAEC,OAAO,EAAEC,WAAW,EAAEC,WAAW,EAAE7H,UAAU,EAAEiF,WAAW,EAAEnI,CAAC,EAAEC,CAAC,EAAEgN,OAAO,EAAE;MACtI,IAAKlC,WAAW,KAAK,KAAK,CAAC,EAAGA,WAAW,GAAG,CAAC;MAC7C,IAAK/K,CAAC,KAAK,KAAK,CAAC,EAAGA,CAAC,GAAG,CAAC;MACzB,IAAKC,CAAC,KAAK,KAAK,CAAC,EAAGA,CAAC,GAAG,CAAC;MACzB,IAAKgN,OAAO,KAAK,KAAK,CAAC,EAAGA,OAAO,GAAG,CAAC;;MAErC;MACAwB,eAAe,CAACvL,UAAU,CAAC;;MAE3B;MACA,IAAI+L,iBAAiB,GAAG,EAAE;MAC1B5N,kBAAkB,CAACuJ,IAAI,EAAE,UAAUnL,EAAE,EAAEC,EAAE,EAAEC,EAAE,EAAEC,EAAE,EAAE;QACjDqP,iBAAiB,CAAC5G,IAAI,CAAC5I,EAAE,EAAEC,EAAE,EAAEC,EAAE,EAAEC,EAAE,CAAC;MACxC,CAAC,CAAC;MACFqP,iBAAiB,GAAG,IAAIzF,YAAY,CAACyF,iBAAiB,CAAC;MAEvDhM,gBAAgB,CAACC,UAAU,EAAE,UAAU+F,GAAG,EAAE;QAC1C,IAAI7F,EAAE,GAAG6F,GAAG,CAAC7F,EAAE;QACf,IAAII,QAAQ,GAAGyF,GAAG,CAACzF,QAAQ;QAC3B,IAAIY,YAAY,GAAG6E,GAAG,CAAC7E,YAAY;QACnC,IAAIU,WAAW,GAAGmE,GAAG,CAACnE,WAAW;QACjC,IAAIyC,WAAW,GAAG0B,GAAG,CAAC1B,WAAW;QACjC,IAAIW,sBAAsB,GAAGe,GAAG,CAACf,sBAAsB;QACvD,IAAIhE,iBAAiB,GAAG+E,GAAG,CAAC/E,iBAAiB;QAE7CqD,WAAW,CAAC,cAAc,EAAE,UAAU2H,mBAAmB,EAAEC,uBAAuB,EAAE;UAClF,IAAIzE,QAAQ,KAAKwE,mBAAmB,CAACE,UAAU,IAAIzE,SAAS,KAAKuE,mBAAmB,CAACG,WAAW,EAAE;YAChGjM,EAAE,CAACgG,UAAU,CACXhG,EAAE,CAACyE,UAAU,EAAE,CAAC,EAAEzE,EAAE,CAACiG,IAAI,EACzB6F,mBAAmB,CAACE,UAAU,GAAG1E,QAAQ,EACzCwE,mBAAmB,CAACG,WAAW,GAAG1E,SAAS,EAC3C,CAAC,EAAEvH,EAAE,CAACiG,IAAI,EAAEjG,EAAE,CAACkG,aAAa,EAAE,IAChC,CAAC;UACH;;UAEA;UACAxE,WAAW,CAAC,MAAM,EAAEmJ,UAAU,EAAEC,YAAY,EAAE,UAAUjF,GAAG,EAAE;YAC3D,IAAI5C,YAAY,GAAG4C,GAAG,CAAC5C,YAAY;YACnC,IAAIT,UAAU,GAAGqD,GAAG,CAACrD,UAAU;;YAE/B;YACA,IAAI0J,mBAAmB,GAAG,CAAC9L,QAAQ,IAAIY,YAAY,CAAC,wBAAwB,CAAC;YAC7E,IAAImL,oBAAoB,GAAG,CAAC/L,QAAQ,IAAIY,YAAY,CAAC,kBAAkB,CAAC;;YAExE;YACAiC,YAAY,CAAC,KAAK,EAAE,CAAC,EAAEjD,EAAE,CAACmG,WAAW,EAAE,CAAC,EAAE6E,WAAW,CAAC;YACtD/H,YAAY,CAAC,cAAc,EAAE,CAAC,EAAEjD,EAAE,CAACoM,YAAY,EAAE,CAAC,EAAEP,iBAAiB,CAAC;;YAEtE;YACArJ,UAAU,CAACO,KAAK,CAAC,KAAK,CAAC,EAAE,CAAE,IAAI,EAAE,cAAc,CAAE,CAACC,MAAM,CAAEyE,OAAQ,CAAC,CAAC;YACpEjF,UAAU,CAAC,IAAI,EAAE,cAAc,EAAEkF,WAAW,CAAC;YAC7ClF,UAAU,CAAC,IAAI,EAAE,WAAW,EAAEmF,WAAW,CAAC;;YAE1C;YACA7C,sBAAsB,CAACgH,mBAAmB,EAAEC,uBAAuB,EAAE,UAAUhH,WAAW,EAAE;cAC1F/E,EAAE,CAACqM,MAAM,CAACrM,EAAE,CAACsG,KAAK,CAAC;cACnBtG,EAAE,CAACuG,SAAS,CAAC,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,CAAC;cACpCvG,EAAE,CAACwG,QAAQ,CAAC,CAAC,EAAE,CAAC,EAAEc,QAAQ,EAAEC,SAAS,CAAC;cACtCvH,EAAE,CAACyG,OAAO,CAAC,CAAC,EAAE,CAAC,EAAEa,QAAQ,EAAEC,SAAS,CAAC;cACrCvH,EAAE,CAACsM,SAAS,CAACtM,EAAE,CAACuM,GAAG,EAAEvM,EAAE,CAACuM,GAAG,CAAC;cAC5B;cACA;cACAvM,EAAE,CAACwM,qBAAqB,CAACxM,EAAE,CAACyM,QAAQ,EAAErM,QAAQ,GAAGJ,EAAE,CAAC0M,GAAG,GAAGP,oBAAoB,CAACQ,OAAO,CAAC;cACvF3M,EAAE,CAAC4M,KAAK,CAAC5M,EAAE,CAAC6M,gBAAgB,CAAC;cAC7B,IAAIzM,QAAQ,EAAE;gBACZJ,EAAE,CAAC8M,mBAAmB,CAAC9M,EAAE,CAAC2G,SAAS,EAAE,CAAC,EAAE,CAAC,EAAEkF,iBAAiB,CAACjJ,MAAM,GAAG,CAAC,CAAC;cAC1E,CAAC,MAAM;gBACLsJ,mBAAmB,CAACa,wBAAwB,CAAC/M,EAAE,CAAC2G,SAAS,EAAE,CAAC,EAAE,CAAC,EAAEkF,iBAAiB,CAACjJ,MAAM,GAAG,CAAC,CAAC;cAChG;cACA;cACA;cACA;cACA;YACF,CAAC,CAAC;UACJ,CAAC,CAAC;;UAEF;UACAlB,WAAW,CAAC,MAAM,EAAEtC,kBAAkB,EAAE2L,YAAY,EAAE,UAAU/I,OAAO,EAAE;YACvEA,OAAO,CAACiB,YAAY,CAAC,KAAK,EAAE,CAAC,EAAEjD,EAAE,CAACmG,WAAW,EAAE,CAAC,EAAE6E,WAAW,CAAC;YAC9DhJ,OAAO,CAACQ,UAAU,CAAC,IAAI,EAAE,KAAK,EAAEuJ,uBAAuB,CAAC;YACxD/L,EAAE,CAACkF,eAAe,CAAClF,EAAE,CAACmF,WAAW,EAAEJ,WAAW,CAAC;YAC/C/E,EAAE,CAACqG,OAAO,CAACrG,EAAE,CAACsG,KAAK,CAAC;YACpBtG,EAAE,CAACuG,SAAS,CAACsD,OAAO,KAAK,CAAC,EAAEA,OAAO,KAAK,CAAC,EAAEA,OAAO,KAAK,CAAC,EAAEA,OAAO,KAAK,CAAC,CAAC;YACxE7J,EAAE,CAACwG,QAAQ,CAAC5J,CAAC,EAAEC,CAAC,EAAEyK,QAAQ,EAAEC,SAAS,CAAC;YACtCvH,EAAE,CAACyG,OAAO,CAAC7J,CAAC,EAAEC,CAAC,EAAEyK,QAAQ,EAAEC,SAAS,CAAC;YACrCvH,EAAE,CAAC0G,UAAU,CAAC1G,EAAE,CAAC2G,SAAS,EAAE,CAAC,EAAE,CAAC,CAAC;UACnC,CAAC,CAAC;QACJ,CAAC,CAAC;;QAEF;QACA,IAAI3G,EAAE,CAACgN,aAAa,CAAC,CAAC,EAAE;UACtBlM,iBAAiB,CAAC,CAAC;UACnB,MAAM,IAAIK,KAAK,CAAC,oBAAoB,CAAC;QACvC;MACF,CAAC,CAAC;IACJ;IAEA,SAASmK,WAAWA,CAAExL,UAAU,EAAE;MAChC,IAAImN,GAAG,GAAI,CAACnN,UAAU,IAAIA,UAAU,KAAKmL,eAAe,GAAIE,WAAW,GAAIrL,UAAU,CAACa,MAAM,IAAIb,UAAW;MAC3G,IAAIoN,SAAS,GAAG9B,eAAe,CAACjL,GAAG,CAAC8M,GAAG,CAAC;MACxC,IAAIC,SAAS,KAAKC,SAAS,EAAE;QAC3BjC,gBAAgB,GAAG,IAAI;QACvB,IAAIkC,UAAU,GAAG,IAAI;QACrB,IAAI;UACF;UACA;UACA;UACA,IAAIC,cAAc,GAAG,CACnB,EAAE,EAAE,GAAG,EAAE,EAAE,EAAE,EAAE,EACf,EAAE,EAAE,GAAG,EAAE,GAAG,EAAE,EAAE,EAChB,EAAE,EAAE,GAAG,EAAE,GAAG,EAAE,EAAE,EAChB,EAAE,EAAE,EAAE,EAAE,GAAG,EAAE,EAAE,CAChB;UACD,IAAIC,UAAU,GAAG/B,UAAU,CACzB,CAAC,EACD,CAAC,EACD,wBAAwB,EACxB,CAAC,CAAC,EAAE,CAAC,EAAE,EAAE,EAAE,EAAE,CAAC,EACd,EAAE,EACF,CAAC,EACDzL,UACF,CAAC;UACDoN,SAAS,GAAGI,UAAU,IAAID,cAAc,CAACzK,MAAM,KAAK0K,UAAU,CAAC1K,MAAM,IACnE0K,UAAU,CAACC,KAAK,CAAC,UAAUC,GAAG,EAAExO,CAAC,EAAE;YAAE,OAAOwO,GAAG,KAAKH,cAAc,CAACrO,CAAC,CAAC;UAAE,CAAC,CAAC;UAC3E,IAAI,CAACkO,SAAS,EAAE;YACdE,UAAU,GAAG,uBAAuB;YACpCK,OAAO,CAACC,IAAI,CAACL,cAAc,EAAEC,UAAU,CAAC;UAC1C;QACF,CAAC,CAAC,OAAOK,GAAG,EAAE;UACZ;UACAT,SAAS,GAAG,KAAK;UACjBE,UAAU,GAAGO,GAAG,CAACC,OAAO;QAC1B;QACA,IAAIR,UAAU,EAAE;UACdK,OAAO,CAACI,IAAI,CAAC,qCAAqC,EAAET,UAAU,CAAC;QACjE;QACAlC,gBAAgB,GAAG,KAAK;QACxBE,eAAe,CAAC7F,GAAG,CAAC0H,GAAG,EAAEC,SAAS,CAAC;MACrC;MACA,OAAOA,SAAS;IAClB;IAEA,IAAIY,KAAK,GAAG,aAAa5G,MAAM,CAACC,MAAM,CAAC;MACrCC,SAAS,EAAE,IAAI;MACfsD,QAAQ,EAAEa,UAAU;MACpBZ,kBAAkB,EAAEiB,oBAAoB;MACxChB,uBAAuB,EAAEA,uBAAuB;MAChDU,WAAW,EAAEA;IACf,CAAC,CAAC;;IAEF;AACF;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;IACE,SAASZ,QAAQA,CACfpD,QAAQ,EACRC,SAAS,EACTC,IAAI,EACJC,OAAO,EACPC,WAAW,EACXC,WAAW,EACX;MACA,IAAKD,WAAW,KAAK,KAAK,CAAC,EAAGA,WAAW,GAAGO,IAAI,CAACI,GAAG,CAACZ,OAAO,CAAC,CAAC,CAAC,GAAGA,OAAO,CAAC,CAAC,CAAC,EAAEA,OAAO,CAAC,CAAC,CAAC,GAAGA,OAAO,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC;MAC1G,IAAKE,WAAW,KAAK,KAAK,CAAC,EAAGA,WAAW,GAAG,CAAC;MAE7C,IAAI;QACF,OAAO4D,UAAU,CAACxI,KAAK,CAAC+K,KAAK,EAAEnL,SAAS,CAAC;MAC3C,CAAC,CAAC,OAAM9B,CAAC,EAAE;QACT4M,OAAO,CAACC,IAAI,CAAC,iDAAiD,EAAE7M,CAAC,CAAC;QAClE,OAAOwG,UAAU,CAACtE,KAAK,CAAC0H,UAAU,EAAE9H,SAAS,CAAC;MAChD;IACF;;IAEA;AACF;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;IACE,SAASgI,kBAAkBA,CACzBrD,QAAQ,EACRC,SAAS,EACTC,IAAI,EACJC,OAAO,EACPC,WAAW,EACXC,WAAW,EACXhH,MAAM,EACN/D,CAAC,EACDC,CAAC,EACDgN,OAAO,EACP;MACA,IAAKnC,WAAW,KAAK,KAAK,CAAC,EAAGA,WAAW,GAAGO,IAAI,CAACI,GAAG,CAACZ,OAAO,CAAC,CAAC,CAAC,GAAGA,OAAO,CAAC,CAAC,CAAC,EAAEA,OAAO,CAAC,CAAC,CAAC,GAAGA,OAAO,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC;MAC1G,IAAKE,WAAW,KAAK,KAAK,CAAC,EAAGA,WAAW,GAAG,CAAC;MAC7C,IAAK/K,CAAC,KAAK,KAAK,CAAC,EAAGA,CAAC,GAAG,CAAC;MACzB,IAAKC,CAAC,KAAK,KAAK,CAAC,EAAGA,CAAC,GAAG,CAAC;MACzB,IAAKgN,OAAO,KAAK,KAAK,CAAC,EAAGA,OAAO,GAAG,CAAC;MAErC,IAAI;QACF,OAAO+B,oBAAoB,CAAC7I,KAAK,CAAC+K,KAAK,EAAEnL,SAAS,CAAC;MACrD,CAAC,CAAC,OAAM9B,CAAC,EAAE;QACT4M,OAAO,CAACC,IAAI,CAAC,iDAAiD,EAAE7M,CAAC,CAAC;QAClE,OAAO+I,oBAAoB,CAAC7G,KAAK,CAAC0H,UAAU,EAAE9H,SAAS,CAAC;MAC1D;IACF;IAEA1G,OAAO,CAACgB,kBAAkB,GAAGA,kBAAkB;IAC/ChB,OAAO,CAACyO,QAAQ,GAAGA,QAAQ;IAC3BzO,OAAO,CAAC0O,kBAAkB,GAAGA,kBAAkB;IAC/C1O,OAAO,CAACwO,UAAU,GAAGA,UAAU;IAC/BxO,OAAO,CAACgC,kBAAkB,GAAGA,kBAAkB;IAC/ChC,OAAO,CAAC6R,KAAK,GAAGA,KAAK;IACrB7R,OAAO,CAACgL,UAAU,GAAGA,UAAU;IAE/BC,MAAM,CAAC6G,cAAc,CAAC9R,OAAO,EAAE,YAAY,EAAE;MAAE+R,KAAK,EAAE;IAAK,CAAC,CAAC;IAE7D,OAAO/R,OAAO;EAEhB,CAAC,CAAC,CAAC,CAAC,CAAE;EACN,OAAOA,OAAO;AACd;AAEA,SAASD,YAAY,IAAIiS,OAAO", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}