// Asset Loader for Super Siah Man Racing
import { AudioSystem, TrackInfo, SoundEffect } from './AudioSystem';
import { store } from '../store'; // Import the Redux store
import { setUiAssets } from '../store/gameSlice'; // Import the action to set UI assets

// Define asset types
export interface VehicleAsset {
  id: string;
  name: string;
  model: string;
  textures: string[];
  thumbnail: string;
}

export interface TrackAsset {
  id: string;
  name: string;
  background: string;
  thumbnail: string;
  difficulty: 'easy' | 'medium' | 'hard';
}

export interface CharacterAsset {
  id: string;
  name: string;
  portrait: string;
}

export interface UIAsset {
  id: string;
  path: string;
}

// Asset Loader class
export class AssetLoader {
  private static instance: AssetLoader;
  
  // Asset collections
  private vehicles: Map<string, VehicleAsset>;
  private tracks: Map<string, TrackAsset>;
  private characters: Map<string, CharacterAsset>;
  private uiAssets: Map<string, UIAsset>;
  
  // Loading state
  private loadingProgress: number;
  private totalAssets: number;
  private loadedAssets: number;
  private isLoading: boolean;
  private loadingError: string | null;
  
  // Audio system reference
  private audioSystem: AudioSystem;
  
  private constructor() {
    this.vehicles = new Map();
    this.tracks = new Map();
    this.characters = new Map();
    this.uiAssets = new Map();
    
    this.loadingProgress = 0;
    this.totalAssets = 0;
    this.loadedAssets = 0;
    this.isLoading = false;
    this.loadingError = null;
    
    this.audioSystem = AudioSystem.getInstance();
  }
  
  public static getInstance(): AssetLoader {
    if (!AssetLoader.instance) {
      AssetLoader.instance = new AssetLoader();
    }
    return AssetLoader.instance;
  }
  
  // Initialize asset loading
  public async initializeAssets(
    onProgress: (progress: number) => void,
    onComplete: () => void,
    onError: (error: string) => void
  ): Promise<void> {
    if (this.isLoading) return;
    
    this.isLoading = true;
    this.loadingProgress = 0;
    this.loadedAssets = 0;
    this.loadingError = null;
    
    try {
      // Define assets to load
      const vehicleAssets = this.getVehicleAssets();
      const trackAssets = this.getTrackAssets();
      const characterAssets = this.getCharacterAssets();
      const uiAssets = this.getUIAssets();
      const musicTracks = this.getMusicTracks();
      const soundEffects = this.getSoundEffects();
      
      // Calculate total assets
      this.totalAssets = 
        vehicleAssets.length + 
        trackAssets.length + 
        characterAssets.length + 
        uiAssets.length +
        musicTracks.length +
        soundEffects.length;
      
      // Load vehicle assets
      for (const vehicle of vehicleAssets) {
        await this.loadVehicleAsset(vehicle);
        this.updateProgress(onProgress);
      }
      
      // Load track assets
      for (const track of trackAssets) {
        await this.loadTrackAsset(track);
        this.updateProgress(onProgress);
      }
      
      // Load character assets
      for (const character of characterAssets) {
        await this.loadCharacterAsset(character);
        this.updateProgress(onProgress);
      }
      
      // Load UI assets
      for (const ui of uiAssets) {
        await this.loadUIAsset(ui);
        this.updateProgress(onProgress);
      }
      
      // Load music tracks
      for (const track of musicTracks) {
        await this.audioSystem.loadMusicTrack(track);
        this.updateProgress(onProgress);
      }
      
      // Load sound effects
      for (const effect of soundEffects) {
        await this.audioSystem.loadSoundEffect(effect);
        this.updateProgress(onProgress);
      }

      // After all UI assets are loaded (metadata stored), dispatch them to Redux
      const loadedUiAssetPaths: Record<string, string> = {};
      this.uiAssets.forEach(asset => {
        loadedUiAssetPaths[asset.id] = asset.path;
      });
      store.dispatch(setUiAssets(loadedUiAssetPaths));
      
      this.isLoading = false;
      onComplete();
    } catch (error) {
      this.isLoading = false;
      this.loadingError = error instanceof Error ? error.message : String(error);
      onError(this.loadingError);
    }
  }
  
  // Update loading progress
  private updateProgress(onProgress: (progress: number) => void): void {
    this.loadedAssets++;
    this.loadingProgress = this.loadedAssets / this.totalAssets;
    onProgress(this.loadingProgress);
  }
  
  // Load a vehicle asset
  private async loadVehicleAsset(vehicle: VehicleAsset): Promise<void> {
    // In a real implementation, this would load the 3D model and textures
    // For now, we'll just store the metadata
    this.vehicles.set(vehicle.id, vehicle);
  }
  
  // Load a track asset
  private async loadTrackAsset(track: TrackAsset): Promise<void> {
    // In a real implementation, this would load the track data
    // For now, we'll just store the metadata
    this.tracks.set(track.id, track);
  }
  
  // Load a character asset
  private async loadCharacterAsset(character: CharacterAsset): Promise<void> {
    // In a real implementation, this would load the character portrait
    // For now, we'll just store the metadata
    this.characters.set(character.id, character);
  }
  
  // Load a UI asset
  private async loadUIAsset(ui: UIAsset): Promise<void> {
    // In a real implementation, this would load the UI image
    // For now, we'll just store the metadata
    this.uiAssets.set(ui.id, ui);
  }
  
  // Get vehicle assets
  private getVehicleAssets(): VehicleAsset[] {
    return [
      {
        id: 'car_black',
        name: 'Racing Car Black',
        model: '/assets/StoreAssets/Azerilo/HQ Racing Car Model No.1203/Mesh/car 1203.fbx',
        textures: ['/assets/StoreAssets/Azerilo/HQ Racing Car Model No.1203/Materials/BodyMat black.mat'],
        thumbnail: '/assets/StoreAssets/Azerilo/HQ Racing Car Model No.1203/Materials/Textures/Dash1.tga' // Using a texture as placeholder thumbnail
      },
      {
        id: 'car_blue',
        name: 'Racing Car Blue',
        model: '/assets/StoreAssets/Azerilo/HQ Racing Car Model No.1203/Mesh/car 1203.fbx',
        textures: ['/assets/StoreAssets/Azerilo/HQ Racing Car Model No.1203/Materials/BodyMat blue.mat'],
        thumbnail: '/assets/StoreAssets/Azerilo/HQ Racing Car Model No.1203/Materials/Textures/Dash1.tga' // Using a texture as placeholder thumbnail
      },
      {
        id: 'car_green',
        name: 'Racing Car Green',
        model: '/assets/StoreAssets/Azerilo/HQ Racing Car Model No.1203/Mesh/car 1203.fbx',
        textures: ['/assets/StoreAssets/Azerilo/HQ Racing Car Model No.1203/Materials/BodyMat green.mat'],
        thumbnail: '/assets/StoreAssets/Azerilo/HQ Racing Car Model No.1203/Materials/Textures/Dash1.tga' // Using a texture as placeholder thumbnail
      },
      {
        id: 'car_white',
        name: 'Racing Car White',
        model: '/assets/StoreAssets/Azerilo/HQ Racing Car Model No.1203/Mesh/car 1203.fbx',
        textures: ['/assets/StoreAssets/Azerilo/HQ Racing Car Model No.1203/Materials/BodyMat white.mat'],
        thumbnail: '/assets/StoreAssets/Azerilo/HQ Racing Car Model No.1203/Materials/Textures/Dash1.tga' // Using a texture as placeholder thumbnail
      },
      {
        id: 'car_yellow',
        name: 'Racing Car Yellow',
        model: '/assets/StoreAssets/Azerilo/HQ Racing Car Model No.1203/Mesh/car 1203.fbx',
        textures: ['/assets/StoreAssets/Azerilo/HQ Racing Car Model No.1203/Materials/BodyMat yellow.mat'],
        thumbnail: '/assets/StoreAssets/Azerilo/HQ Racing Car Model No.1203/Materials/Textures/Dash1.tga' // Using a texture as placeholder thumbnail
      }
    ];
  }
  
  // Get track assets
  private getTrackAssets(): TrackAsset[] {
    return [
      {
        id: 'race_track_lake',
        name: 'Race Track Lake',
        background: '/assets/race-track-lake/Textures/asphalt.png', // Using a texture as placeholder background
        thumbnail: '/assets/race-track-lake/Textures/asphalt.png', // Using the same texture as placeholder thumbnail
        difficulty: 'medium' // Assuming medium difficulty for now
      }
    ];
  }
  
  // Get character assets
  private getCharacterAssets(): CharacterAsset[] {
    return [
      {
        id: 'beniamin_idle',
        name: 'Beniamin',
        portrait: '/assets/Sprites/idles/Beniamin bez idle.png'
      },
      {
        id: 'beniamin_angry',
        name: 'Beniamin Angry',
        portrait: '/assets/Sprites/Angries/Beniamin bez wściekły.png'
      },
       {
        id: 'beniamin_talk',
        name: 'Beniamin Talking',
        portrait: '/assets/Sprites/talk/Beniamin bez talk.png'
      },
      {
        id: 'natan_idle',
        name: 'Natan',
        portrait: '/assets/Sprites/idles/Natan bez idle.png'
      },
      {
        id: 'natan_angry',
        name: 'Natan Angry',
        portrait: '/assets/Sprites/Angries/Natan bez wściekły.png'
      },
       {
        id: 'natan_talk',
        name: 'Natan Talking',
        portrait: '/assets/Sprites/talk/Natan bez talk.png'
      },
       {
        id: 'paulina_idle',
        name: 'Paulina',
        portrait: '/assets/Sprites/idles/Paulina bez idle.png'
      },
      {
        id: 'paulina_angry',
        name: 'Paulina Angry',
        portrait: '/assets/Sprites/Angries/Paulina bez wściekła.png'
      },
       {
        id: 'paulina_talk',
        name: 'Paulina Talking',
        portrait: '/assets/Sprites/talk/Paulina bez talk.png'
      },
       {
        id: 'werka_idle',
        name: 'Werka',
        portrait: '/assets/Sprites/idles/Werka bez idle.png'
      },
      {
        id: 'werka_angry',
        name: 'Werka Angry',
        portrait: '/assets/Sprites/Angries/Werka bez wściekła.png'
      },
      {
        id: 'werka_talk',
        name: 'Werka Talking',
        portrait: '/assets/Sprites/talk/Werka bez talk.png'
      }
    ];
  }
  
  // Get UI assets
  private getUIAssets(): UIAsset[] {
    return [
      {
        id: 'ui_visual_novel',
        path: '/assets/Sprites/UI/UIVisualNovel.png'
      },
      {
        id: 'button_arrow_up',
        path: '/assets/Images/ButtonArrowUpSprite.png'
      },
       {
        id: 'gold_gradient',
        path: '/assets/Images/GoldGradient.asset'
      },
       {
        id: 'skidmarks_tex',
        path: '/assets/Images/SkidmarksTex.psd'
      },
       {
        id: 'tachometer_image',
        path: '/assets/Images/Tahometer.png'
      },
       {
        id: 'tachometer_arrow',
        path: '/assets/Images/TahometerArrow.png'
      },
       {
        id: 'tachometer_inside',
        path: '/assets/Images/TahometerInside.png'
      }
      // Add other relevant UI assets as needed from the transferred folders
    ];
  }
  
  // Get music tracks
  private getMusicTracks(): TrackInfo[] {
    return [
      {
        id: 'atl_ride',
        name: 'ATL Ride by Treal',
        src: '/assets/sounds/ATL RIDE BY TREAL_041133.mp3',
        intensity: 'medium', // Assuming a medium intensity for now
        loop: true,
        volume: 0.6,
        category: 'race' // Categorizing as race music
      },
      {
        id: 'big_balling',
        name: 'Big Balling Instrumental',
        src: '/assets/sounds/BIG BALLING INSTRUMENTAL_041206.mp3',
        intensity: 'high', // Assuming a high intensity
        loop: true,
        volume: 0.6,
        category: 'race'
      },
      {
        id: 'epiclations_book1',
        name: 'Epiclations The Book 1',
        src: '/assets/sounds/EPICLATIONS THE BOOK 1_034752.mp3',
        intensity: 'low', // Assuming a low intensity
        loop: true,
        volume: 0.6,
        category: 'menu' // Maybe better for a menu? Let's put some in menu category
      },
      {
        id: 'home_skillet',
        name: 'Home Skillet Instrumental by Treal',
        src: '/assets/sounds/HOME SKILLET INSTRUMENTAL BY TREAL_041033.mp3',
        intensity: 'medium',
        loop: true,
        volume: 0.6,
        category: 'race'
      },
      {
        id: 'you_want_me_back',
        name: 'You Want Me Back Instrumental Treal',
        src: '/assets/sounds/YOU WANT ME BACK INSTRUMENTAL TREAL_041115.mp3',
        intensity: 'medium',
        loop: true,
        volume: 0.6,
        category: 'race'
      }
    ];
  }
  
  // Get sound effects
  private getSoundEffects(): SoundEffect[] {
    return [
      // Keeping some general sound effect ideas, but prioritizing the actual transferred files
      {
        id: 'button_click',
        name: 'Button Click',
        src: '/assets/sounds/button_click.mp3', // Assuming a default button click sound remains
        volume: 0.5,
        loop: false
      },
       {
        id: 'ability_activate',
        name: 'Ability Activate',
        src: '/assets/sounds/ability_activate.mp3', // Assuming a default ability sound remains
        volume: 0.7,
        loop: false
      },
      {
        id: 'water_splash',
        name: 'Big Water Splash',
        src: '/assets/sounds/Big Water Splash Sound Effect.mp3',
        volume: 0.7,
        loop: false
      },
      {
        id: 'car_collision',
        name: 'Car Collision',
        src: '/assets/sounds/CarColision.wav',
        volume: 0.8,
        loop: false
      },
       {
        id: 'car_sound_general',
        name: 'General Car Sound',
        src: '/assets/sounds/CarSound.wav',
        volume: 0.6,
        loop: true // Assuming this is a looping background car sound
      },
       {
        id: 'engine_high_acc',
        name: 'Engine High Acceleration',
        src: '/assets/sounds/high ACC.wav',
        volume: 0.7,
        loop: false
      },
       {
        id: 'engine_high_deacc',
        name: 'Engine High Deacceleration',
        src: '/assets/sounds/high Deacc.wav',
        volume: 0.7,
        loop: false
      },
       {
        id: 'engine_low',
        name: 'Engine Low Sound',
        src: '/assets/sounds/low.wav',
        volume: 0.5,
        loop: true // Assuming this is a looping low engine sound
      },
       {
        id: 'track_collision',
        name: 'Track Collision',
        src: '/assets/sounds/TrackColision.wav',
        volume: 0.7,
        loop: false
      },

      // Enhanced arcade sound effects for fun gameplay!
      {
        id: 'speed_boost',
        name: 'Speed Boost Activation',
        src: '/assets/sounds/high ACC.wav', // Reusing existing sound
        volume: 0.8,
        loop: false
      },
      {
        id: 'jump',
        name: 'Jump Ramp',
        src: '/assets/sounds/Big Water Splash Sound Effect.mp3', // Creative reuse!
        volume: 0.6,
        loop: false
      },
      {
        id: 'coin_collect',
        name: 'Coin Collection',
        src: '/assets/sounds/high ACC.wav', // Will be pitch-shifted
        volume: 0.5,
        loop: false
      },
      {
        id: 'power_up',
        name: 'Power-up Pickup',
        src: '/assets/sounds/CarSound.wav', // Will be modified
        volume: 0.7,
        loop: false
      },
      {
        id: 'collision',
        name: 'Collision Sound',
        src: '/assets/sounds/CarColision.wav',
        volume: 0.8,
        loop: false
      },
      {
        id: 'air_time_bonus',
        name: 'Air Time Bonus',
        src: '/assets/sounds/high Deacc.wav',
        volume: 0.6,
        loop: false
      },
      {
        id: 'perfect_drift',
        name: 'Perfect Drift',
        src: '/assets/sounds/low.wav',
        volume: 0.7,
        loop: false
      },
      {
        id: 'nitro_boost',
        name: 'Nitro Boost',
        src: '/assets/sounds/high ACC.wav',
        volume: 0.9,
        loop: false
      },
      {
        id: 'tire_skid',
        name: 'Tire Skidding',
        src: '/assets/sounds/TrackColision.wav',
        volume: 0.6,
        loop: true
      },
      {
        id: 'brake_skid',
        name: 'Brake Skidding',
        src: '/assets/sounds/TrackColision.wav',
        volume: 0.7,
        loop: false
      }
      // Note: We're creatively reusing existing sounds with different volumes and pitch modifications
      // In a full production, these would be custom-designed arcade sound effects
    ];
  }
  
  // Get a vehicle asset by ID
  public getVehicle(id: string): VehicleAsset | undefined {
    return this.vehicles.get(id);
  }
  
  // Get all vehicle assets
  public getAllVehicles(): VehicleAsset[] {
    return Array.from(this.vehicles.values());
  }
  
  // Get a track asset by ID
  public getTrack(id: string): TrackAsset | undefined {
    return this.tracks.get(id);
  }
  
  // Get all track assets
  public getAllTracks(): TrackAsset[] {
    return Array.from(this.tracks.values());
  }
  
  // Get a character asset by ID
  public getCharacter(id: string): CharacterAsset | undefined {
    return this.characters.get(id);
  }
  
  // Get a UI asset by ID
  public getUIAsset(id: string): UIAsset | undefined {
    return this.uiAssets.get(id);
  }
  
  // Get loading progress
  public getLoadingProgress(): number {
    return this.loadingProgress;
  }
  
  // Check if assets are loading
  public isAssetLoading(): boolean {
    return this.isLoading;
  }
  
  // Get loading error
  public getLoadingError(): string | null {
    return this.loadingError;
  }
}
