{"ast": null, "code": "import { createSlice } from '@reduxjs/toolkit';\nconst initialState = {\n  vehicles: {\n    'rainbow_racer': {\n      id: 'rainbow_racer',\n      name: '🌈 Rainbow Racer',\n      type: 'sports',\n      topSpeed: 200,\n      acceleration: 9.0,\n      handling: 8.5,\n      braking: 8.0,\n      price: 0,\n      // Free starter car!\n      unlocked: true,\n      colors: ['#ff0000', '#ff8000', '#ffff00', '#00ff00', '#0080ff', '#8000ff'],\n      upgrades: {\n        engine: 0,\n        transmission: 0,\n        suspension: 0,\n        tires: 0,\n        nitro: 0\n      },\n      boostRechargeMax: 100,\n      boostRechargeRate: 1.5,\n      boostRechargeDelay: 2000,\n      boostRechargeRemaining: 100\n    },\n    'candy_cruiser': {\n      id: 'candy_cruiser',\n      name: '🍭 Candy Cruiser',\n      type: 'sports',\n      topSpeed: 180,\n      acceleration: 8.0,\n      handling: 9.5,\n      braking: 7.5,\n      price: 5000,\n      unlocked: false,\n      colors: ['#ff69b4', '#ffc0cb', '#ff1493', '#da70d6'],\n      description: 'Sweet as candy and twice as fast!',\n      emoji: '🍭',\n      upgrades: {\n        engine: 0,\n        transmission: 0,\n        suspension: 0,\n        tires: 0,\n        nitro: 0\n      },\n      boostRechargeMax: 90,\n      boostRechargeRate: 1.3,\n      boostRechargeDelay: 2200,\n      boostRechargeRemaining: 90\n    },\n    'muscle_car': {\n      id: 'muscle_car',\n      name: 'Turbo Whisperer',\n      type: 'muscle',\n      topSpeed: 200,\n      acceleration: 9.5,\n      handling: 7.0,\n      braking: 6.5,\n      price: 35000,\n      unlocked: false,\n      colors: ['#ff0000', '#0000ff', '#ffffff', '#000000'],\n      upgrades: {\n        engine: 0,\n        transmission: 0,\n        suspension: 0,\n        tires: 0,\n        nitro: 0\n      },\n      boostRechargeMax: 120,\n      boostRechargeRate: 1.8,\n      boostRechargeDelay: 1800,\n      boostRechargeRemaining: 120\n    },\n    'rally_car': {\n      id: 'rally_car',\n      name: 'MAD Prototype',\n      type: 'rally',\n      topSpeed: 180,\n      acceleration: 8.0,\n      handling: 9.5,\n      braking: 9.0,\n      price: 40000,\n      unlocked: false,\n      colors: ['#ff0000', '#0000ff', '#ffffff', '#000000'],\n      upgrades: {\n        engine: 0,\n        transmission: 0,\n        suspension: 0,\n        tires: 0,\n        nitro: 0\n      },\n      boostRechargeMax: 110,\n      boostRechargeRate: 1.6,\n      boostRechargeDelay: 1900,\n      boostRechargeRemaining: 110\n    },\n    'supercar': {\n      id: 'supercar',\n      name: 'Velocity Vortex',\n      type: 'supercar',\n      topSpeed: 250,\n      acceleration: 10.0,\n      handling: 8.5,\n      braking: 9.0,\n      price: 100000,\n      unlocked: false,\n      colors: ['#ff0000', '#0000ff', '#ffffff', '#000000'],\n      upgrades: {\n        engine: 0,\n        transmission: 0,\n        suspension: 0,\n        tires: 0,\n        nitro: 0\n      },\n      boostRechargeMax: 150,\n      boostRechargeRate: 2.2,\n      boostRechargeDelay: 1500,\n      boostRechargeRemaining: 150\n    },\n    'concept_car': {\n      id: 'concept_car',\n      name: 'Quantum Racer',\n      type: 'concept',\n      topSpeed: 280,\n      acceleration: 10.0,\n      handling: 10.0,\n      braking: 10.0,\n      price: 200000,\n      unlocked: false,\n      colors: ['#ff0000', '#0000ff', '#ffffff', '#000000'],\n      upgrades: {\n        engine: 0,\n        transmission: 0,\n        suspension: 0,\n        tires: 0,\n        nitro: 0\n      }\n    }\n  },\n  currentVehicleId: 'rainbow_racer',\n  unlockedVehicles: ['rainbow_racer'],\n  // Start with the free rainbow racer unlocked\n  availableParts: [{\n    id: 'body1',\n    name: 'Standard Body',\n    type: 'body',\n    unlocked: true,\n    stats: {\n      speed: 0,\n      acceleration: 0,\n      handling: 0,\n      braking: 0\n    },\n    price: 0,\n    image: 'standard_body.png'\n  }, {\n    id: 'body2',\n    name: 'Aerodynamic Body',\n    type: 'body',\n    unlocked: false,\n    stats: {\n      speed: 5,\n      acceleration: 2,\n      handling: 0,\n      braking: 0\n    },\n    price: 1000,\n    image: 'aero_body.png'\n  }, {\n    id: 'wheels1',\n    name: 'Standard Wheels',\n    type: 'wheels',\n    unlocked: true,\n    stats: {\n      speed: 0,\n      acceleration: 0,\n      handling: 0,\n      braking: 0\n    },\n    price: 0,\n    image: 'standard_wheels.png'\n  }, {\n    id: 'wheels2',\n    name: 'Performance Wheels',\n    type: 'wheels',\n    unlocked: false,\n    stats: {\n      speed: 0,\n      acceleration: 3,\n      handling: 5,\n      braking: 3\n    },\n    price: 800,\n    image: 'performance_wheels.png'\n  }, {\n    id: 'engine1',\n    name: 'Standard Engine',\n    type: 'engine',\n    unlocked: true,\n    stats: {\n      speed: 0,\n      acceleration: 0,\n      handling: 0,\n      braking: 0\n    },\n    price: 0,\n    image: 'standard_engine.png'\n  }, {\n    id: 'engine2',\n    name: 'Turbo Engine',\n    type: 'engine',\n    unlocked: false,\n    stats: {\n      speed: 10,\n      acceleration: 8,\n      handling: 0,\n      braking: 0\n    },\n    price: 2000,\n    image: 'turbo_engine.png'\n  }],\n  equippedParts: {\n    'sports_car': {\n      body: 'body1',\n      wheels: 'wheels1',\n      spoiler: null,\n      engine: 'engine1',\n      transmission: null,\n      suspension: null,\n      tires: null,\n      nitro: null,\n      headlights: null,\n      exhaust: null,\n      decals: []\n    },\n    'muscle_car': {\n      body: 'body1',\n      wheels: 'wheels1',\n      spoiler: null,\n      engine: 'engine1',\n      transmission: null,\n      suspension: null,\n      tires: null,\n      nitro: null,\n      headlights: null,\n      exhaust: null,\n      decals: []\n    },\n    'rally_car': {\n      body: 'body1',\n      wheels: 'wheels1',\n      spoiler: null,\n      engine: 'engine1',\n      transmission: null,\n      suspension: null,\n      tires: null,\n      nitro: null,\n      headlights: null,\n      exhaust: null,\n      decals: []\n    }\n  },\n  availableColors: [{\n    id: 'color1',\n    name: 'Classic Red',\n    primary: '#ff0000',\n    secondary: '#990000',\n    detail: '#ffffff',\n    unlocked: true,\n    price: 0\n  }, {\n    id: 'color2',\n    name: 'Electric Blue',\n    primary: '#0066ff',\n    secondary: '#003399',\n    detail: '#ffffff',\n    unlocked: true,\n    price: 0\n  }, {\n    id: 'color3',\n    name: 'Mystic Purple',\n    primary: '#9900cc',\n    secondary: '#660099',\n    detail: '#ffcc00',\n    unlocked: false,\n    price: 500\n  }],\n  selectedColors: {\n    vehicle1: {\n      colorId: 'color1'\n    }\n  },\n  savedConfigurations: [],\n  // Performance stats cache\n  cachedStats: {}\n};\nexport const vehicleSlice = createSlice({\n  name: 'vehicle',\n  initialState,\n  reducers: {\n    setCurrentVehicle: (state, action) => {\n      state.currentVehicleId = action.payload;\n    },\n    unlockVehicle: (state, action) => {\n      if (state.vehicles[action.payload]) {\n        state.vehicles[action.payload].unlocked = true;\n      }\n    },\n    unlockPart: (state, action) => {\n      const part = state.availableParts.find(p => p.id === action.payload);\n      if (part) {\n        part.unlocked = true;\n      }\n    },\n    equipPart: (state, action) => {\n      const part = state.availableParts.find(p => p.id === action.payload.partId);\n      if (part && part.unlocked) {\n        // Check if the part is compatible with this vehicle type\n        const vehicle = state.vehicles[action.payload.vehicleId];\n        if (vehicle && (!part.vehicleTypes || part.vehicleTypes.includes(vehicle.type))) {\n          // Initialize parts object if it doesn't exist\n          if (!state.equippedParts[action.payload.vehicleId]) {\n            state.equippedParts[action.payload.vehicleId] = {\n              body: null,\n              wheels: null,\n              spoiler: null,\n              engine: null,\n              transmission: null,\n              suspension: null,\n              tires: null,\n              nitro: null,\n              headlights: null,\n              exhaust: null,\n              decals: []\n            };\n          }\n\n          // Handle decals differently (can have multiple)\n          if (part.type === 'decal') {\n            if (!state.equippedParts[action.payload.vehicleId].decals.includes(part.id)) {\n              state.equippedParts[action.payload.vehicleId].decals.push(part.id);\n            }\n          } else {\n            state.equippedParts[action.payload.vehicleId][part.type] = part.id;\n          }\n\n          // Update vehicle upgrades if it's an upgrade part\n          if (['engine', 'transmission', 'suspension', 'tires', 'nitro'].includes(part.type)) {\n            const upgradeLevel = part.stats.speed + part.stats.acceleration + part.stats.handling + part.stats.braking;\n            const normalizedLevel = Math.floor(upgradeLevel / 10);\n\n            // Update the upgrade level in the vehicle\n            if (vehicle.upgrades) {\n              vehicle.upgrades[part.type] = normalizedLevel;\n            }\n          }\n\n          // Recalculate and cache the vehicle stats\n          calculateVehicleStats(state, action.payload.vehicleId);\n        }\n      }\n    },\n    removePart: (state, action) => {\n      if (state.equippedParts[action.payload.vehicleId]) {\n        if (action.payload.partType === 'decals' && action.payload.partId) {\n          state.equippedParts[action.payload.vehicleId].decals = state.equippedParts[action.payload.vehicleId].decals.filter(id => id !== action.payload.partId);\n        } else if (action.payload.partType !== 'decals') {\n          state.equippedParts[action.payload.vehicleId][action.payload.partType] = null;\n\n          // Reset upgrade level if it's an upgrade part\n          if (['engine', 'transmission', 'suspension', 'tires', 'nitro'].includes(action.payload.partType)) {\n            const vehicle = state.vehicles[action.payload.vehicleId];\n            if (vehicle && vehicle.upgrades) {\n              vehicle.upgrades[action.payload.partType] = 0;\n            }\n          }\n        }\n\n        // Recalculate and cache the vehicle stats\n        calculateVehicleStats(state, action.payload.vehicleId);\n      }\n    },\n    selectColor: (state, action) => {\n      const color = state.availableColors.find(c => c.id === action.payload.colorId);\n      if (color && color.unlocked) {\n        state.selectedColors[action.payload.vehicleId] = {\n          colorId: color.id\n        };\n      }\n    },\n    unlockColor: (state, action) => {\n      const color = state.availableColors.find(c => c.id === action.payload);\n      if (color) {\n        color.unlocked = true;\n      }\n    },\n    saveConfiguration: (state, action) => {\n      var _state$selectedColors;\n      const vehicleId = state.currentVehicleId;\n      const parts = state.equippedParts[vehicleId] || {\n        body: null,\n        wheels: null,\n        spoiler: null,\n        engine: null,\n        transmission: null,\n        suspension: null,\n        tires: null,\n        nitro: null,\n        headlights: null,\n        exhaust: null,\n        decals: []\n      };\n      const colorId = ((_state$selectedColors = state.selectedColors[vehicleId]) === null || _state$selectedColors === void 0 ? void 0 : _state$selectedColors.colorId) || state.availableColors[0].id;\n      const configId = `config_${Date.now()}`;\n      state.savedConfigurations.push({\n        id: configId,\n        name: action.payload.name,\n        vehicleId,\n        parts: {\n          ...parts\n        },\n        colorId\n      });\n    },\n    loadConfiguration: (state, action) => {\n      const config = state.savedConfigurations.find(c => c.id === action.payload);\n      if (config) {\n        state.currentVehicleId = config.vehicleId;\n        state.equippedParts[config.vehicleId] = {\n          ...config.parts\n        };\n        state.selectedColors[config.vehicleId] = {\n          colorId: config.colorId\n        };\n\n        // Recalculate and cache the vehicle stats\n        calculateVehicleStats(state, config.vehicleId);\n      }\n    },\n    deleteConfiguration: (state, action) => {\n      state.savedConfigurations = state.savedConfigurations.filter(c => c.id !== action.payload);\n    },\n    upgradeVehicle: (state, action) => {\n      const {\n        vehicleId,\n        upgradeType,\n        level\n      } = action.payload;\n      const vehicle = state.vehicles[vehicleId];\n      if (vehicle && vehicle.upgrades) {\n        vehicle.upgrades[upgradeType] = level;\n\n        // Recalculate and cache the vehicle stats\n        calculateVehicleStats(state, vehicleId);\n      }\n    },\n    calculateAllVehicleStats: state => {\n      // Calculate stats for all vehicles\n      Object.keys(state.vehicles).forEach(vehicleId => {\n        calculateVehicleStats(state, vehicleId);\n      });\n    }\n  }\n});\n\n// Helper function to calculate vehicle stats based on base stats and equipped parts\nconst calculateVehicleStats = (state, vehicleId) => {\n  const vehicle = state.vehicles[vehicleId];\n  const parts = state.equippedParts[vehicleId];\n  if (!vehicle || !parts) return;\n\n  // Start with base stats\n  let speed = vehicle.topSpeed;\n  let acceleration = vehicle.acceleration * 10; // Scale to 0-100\n  let handling = vehicle.handling * 10;\n  let braking = vehicle.braking * 10;\n\n  // Add stats from equipped parts\n  Object.entries(parts).forEach(([partType, partId]) => {\n    if (partId && partType !== 'decals') {\n      const part = state.availableParts.find(p => p.id === partId);\n      if (part) {\n        speed += part.stats.speed;\n        acceleration += part.stats.acceleration;\n        handling += part.stats.handling;\n        braking += part.stats.braking;\n      }\n    }\n  });\n\n  // Add stats from upgrades\n  if (vehicle.upgrades) {\n    speed += vehicle.upgrades.engine * 10;\n    acceleration += vehicle.upgrades.transmission * 5;\n    handling += vehicle.upgrades.suspension * 5;\n    braking += vehicle.upgrades.tires * 5;\n\n    // Nitro adds to both speed and acceleration\n    speed += vehicle.upgrades.nitro * 5;\n    acceleration += vehicle.upgrades.nitro * 5;\n  }\n\n  // Cache the calculated stats\n  state.cachedStats[vehicleId] = {\n    speed,\n    acceleration,\n    handling,\n    braking\n  };\n};\nexport const {\n  setCurrentVehicle,\n  unlockVehicle,\n  unlockPart,\n  equipPart,\n  removePart,\n  selectColor,\n  unlockColor,\n  saveConfiguration,\n  loadConfiguration,\n  deleteConfiguration,\n  upgradeVehicle,\n  calculateAllVehicleStats\n} = vehicleSlice.actions;\nexport default vehicleSlice.reducer;", "map": {"version": 3, "names": ["createSlice", "initialState", "vehicles", "id", "name", "type", "topSpeed", "acceleration", "handling", "braking", "price", "unlocked", "colors", "upgrades", "engine", "transmission", "suspension", "tires", "nitro", "boostRechargeMax", "boostRechargeRate", "boostRechargeDelay", "boostRechargeRemaining", "description", "emoji", "currentVehicleId", "unlockedVehicles", "availableParts", "stats", "speed", "image", "equippedParts", "body", "wheels", "spoiler", "headlights", "exhaust", "decals", "availableColors", "primary", "secondary", "detail", "selectedColors", "vehicle1", "colorId", "savedConfigurations", "cachedStats", "vehicleSlice", "reducers", "setCurrentVehicle", "state", "action", "payload", "unlockVehicle", "unlockPart", "part", "find", "p", "equipPart", "partId", "vehicle", "vehicleId", "vehicleTypes", "includes", "push", "upgradeLevel", "normalizedLevel", "Math", "floor", "calculateVehicleStats", "remove<PERSON>art", "partType", "filter", "selectColor", "color", "c", "unlockColor", "saveConfiguration", "_state$selectedColors", "parts", "configId", "Date", "now", "loadConfiguration", "config", "deleteConfiguration", "upgradeVehicle", "upgradeType", "level", "calculateAllVehicleStats", "Object", "keys", "for<PERSON>ach", "entries", "actions", "reducer"], "sources": ["C:/Users/<USER>/Documents/Apps_Creations/SiahMan_Racing4Speed_WebGame/siah-man-racing/src/store/vehicleSlice.ts"], "sourcesContent": ["import { createSlice, PayloadAction } from '@reduxjs/toolkit';\nimport { Vehicle, VehicleType, VehicleUpgrades } from '../types/GameTypes';\n\ninterface VehiclePart {\n  id: string;\n  name: string;\n  type: 'body' | 'wheels' | 'spoiler' | 'engine' | 'transmission' | 'suspension' | 'tires' | 'nitro' | 'decal' | 'headlights' | 'exhaust' | 'horn' | 'antenna' | 'stickers';\n  unlocked: boolean;\n  stats: {\n    speed: number;\n    acceleration: number;\n    handling: number;\n    braking: number;\n    fun?: number; // New fun factor stat!\n  };\n  price: number;\n  image: string;\n  vehicleTypes?: VehicleType[];\n  description?: string;\n  rarity?: 'common' | 'rare' | 'epic' | 'legendary';\n  specialEffect?: string; // Special visual or sound effects\n  emoji?: string; // Fun emoji representation\n}\n\ninterface VehicleColor {\n  id: string;\n  name: string;\n  primary: string;\n  secondary: string;\n  detail: string;\n  unlocked: boolean;\n  price: number;\n}\n\ninterface VehicleState {\n  // Available vehicles with their base properties\n  vehicles: Record<string, Vehicle>;\n\n  // Currently selected vehicle\n  currentVehicleId: string;\n\n  // Unlocked vehicles list\n  unlockedVehicles: string[];\n  \n  // Available parts for customization\n  availableParts: VehiclePart[];\n  \n  // Equipped parts for each vehicle\n  equippedParts: {\n    [vehicleId: string]: {\n      body: string | null;\n      wheels: string | null;\n      spoiler: string | null;\n      engine: string | null;\n      transmission: string | null;\n      suspension: string | null;\n      tires: string | null;\n      nitro: string | null;\n      headlights: string | null;\n      exhaust: string | null;\n      decals: string[];\n    };\n  };\n  \n  // Available colors for vehicles\n  availableColors: VehicleColor[];\n  \n  // Selected colors for each vehicle\n  selectedColors: {\n    [vehicleId: string]: {\n      colorId: string;\n    };\n  };\n  \n  // Saved vehicle configurations\n  savedConfigurations: {\n    id: string;\n    name: string;\n    vehicleId: string;\n    parts: {\n      body: string | null;\n      wheels: string | null;\n      spoiler: string | null;\n      engine: string | null;\n      transmission: string | null;\n      suspension: string | null;\n      tires: string | null;\n      nitro: string | null;\n      headlights: string | null;\n      exhaust: string | null;\n      decals: string[];\n    };\n    colorId: string;\n  }[];\n  \n  // Performance stats cache for quick access\n  cachedStats: {\n    [vehicleId: string]: {\n      speed: number;\n      acceleration: number;\n      handling: number;\n      braking: number;\n    };\n  };\n}\n\nconst initialState: VehicleState = {\n  vehicles: {\n    'rainbow_racer': {\n      id: 'rainbow_racer',\n      name: '🌈 Rainbow Racer',\n      type: 'sports',\n      topSpeed: 200,\n      acceleration: 9.0,\n      handling: 8.5,\n      braking: 8.0,\n      price: 0, // Free starter car!\n      unlocked: true,\n      colors: ['#ff0000', '#ff8000', '#ffff00', '#00ff00', '#0080ff', '#8000ff'],\n      upgrades: {\n        engine: 0,\n        transmission: 0,\n        suspension: 0,\n        tires: 0,\n        nitro: 0\n      },\n      boostRechargeMax: 100,\n      boostRechargeRate: 1.5,\n      boostRechargeDelay: 2000,\n      boostRechargeRemaining: 100\n    },\n    'candy_cruiser': {\n      id: 'candy_cruiser',\n      name: '🍭 Candy Cruiser',\n      type: 'sports',\n      topSpeed: 180,\n      acceleration: 8.0,\n      handling: 9.5,\n      braking: 7.5,\n      price: 5000,\n      unlocked: false,\n      colors: ['#ff69b4', '#ffc0cb', '#ff1493', '#da70d6'],\n      description: 'Sweet as candy and twice as fast!',\n      emoji: '🍭',\n      upgrades: {\n        engine: 0,\n        transmission: 0,\n        suspension: 0,\n        tires: 0,\n        nitro: 0\n      },\n      boostRechargeMax: 90,\n      boostRechargeRate: 1.3,\n      boostRechargeDelay: 2200,\n      boostRechargeRemaining: 90\n    },\n    'muscle_car': {\n      id: 'muscle_car',\n      name: 'Turbo Whisperer',\n      type: 'muscle',\n      topSpeed: 200,\n      acceleration: 9.5,\n      handling: 7.0,\n      braking: 6.5,\n      price: 35000,\n      unlocked: false,\n      colors: ['#ff0000', '#0000ff', '#ffffff', '#000000'],\n      upgrades: {\n        engine: 0,\n        transmission: 0,\n        suspension: 0,\n        tires: 0,\n        nitro: 0\n      },\n      boostRechargeMax: 120,\n      boostRechargeRate: 1.8,\n      boostRechargeDelay: 1800,\n      boostRechargeRemaining: 120\n    },\n    'rally_car': {\n      id: 'rally_car',\n      name: 'MAD Prototype',\n      type: 'rally',\n      topSpeed: 180,\n      acceleration: 8.0,\n      handling: 9.5,\n      braking: 9.0,\n      price: 40000,\n      unlocked: false,\n      colors: ['#ff0000', '#0000ff', '#ffffff', '#000000'],\n      upgrades: {\n        engine: 0,\n        transmission: 0,\n        suspension: 0,\n        tires: 0,\n        nitro: 0\n      },\n      boostRechargeMax: 110,\n      boostRechargeRate: 1.6,\n      boostRechargeDelay: 1900,\n      boostRechargeRemaining: 110\n    },\n    'supercar': {\n      id: 'supercar',\n      name: 'Velocity Vortex',\n      type: 'supercar',\n      topSpeed: 250,\n      acceleration: 10.0,\n      handling: 8.5,\n      braking: 9.0,\n      price: 100000,\n      unlocked: false,\n      colors: ['#ff0000', '#0000ff', '#ffffff', '#000000'],\n      upgrades: {\n        engine: 0,\n        transmission: 0,\n        suspension: 0,\n        tires: 0,\n        nitro: 0\n      },\n      boostRechargeMax: 150,\n      boostRechargeRate: 2.2,\n      boostRechargeDelay: 1500,\n      boostRechargeRemaining: 150\n    },\n    'concept_car': {\n      id: 'concept_car',\n      name: 'Quantum Racer',\n      type: 'concept',\n      topSpeed: 280,\n      acceleration: 10.0,\n      handling: 10.0,\n      braking: 10.0,\n      price: 200000,\n      unlocked: false,\n      colors: ['#ff0000', '#0000ff', '#ffffff', '#000000'],\n      upgrades: {\n        engine: 0,\n        transmission: 0,\n        suspension: 0,\n        tires: 0,\n        nitro: 0\n      }\n    }\n  },\n  currentVehicleId: 'rainbow_racer',\n  unlockedVehicles: ['rainbow_racer'], // Start with the free rainbow racer unlocked\n  availableParts: [\n    {\n      id: 'body1',\n      name: 'Standard Body',\n      type: 'body',\n      unlocked: true,\n      stats: {\n        speed: 0,\n        acceleration: 0,\n        handling: 0,\n        braking: 0,\n      },\n      price: 0,\n      image: 'standard_body.png',\n    },\n    {\n      id: 'body2',\n      name: 'Aerodynamic Body',\n      type: 'body',\n      unlocked: false,\n      stats: {\n        speed: 5,\n        acceleration: 2,\n        handling: 0,\n        braking: 0,\n      },\n      price: 1000,\n      image: 'aero_body.png',\n    },\n    {\n      id: 'wheels1',\n      name: 'Standard Wheels',\n      type: 'wheels',\n      unlocked: true,\n      stats: {\n        speed: 0,\n        acceleration: 0,\n        handling: 0,\n        braking: 0,\n      },\n      price: 0,\n      image: 'standard_wheels.png',\n    },\n    {\n      id: 'wheels2',\n      name: 'Performance Wheels',\n      type: 'wheels',\n      unlocked: false,\n      stats: {\n        speed: 0,\n        acceleration: 3,\n        handling: 5,\n        braking: 3,\n      },\n      price: 800,\n      image: 'performance_wheels.png',\n    },\n    {\n      id: 'engine1',\n      name: 'Standard Engine',\n      type: 'engine',\n      unlocked: true,\n      stats: {\n        speed: 0,\n        acceleration: 0,\n        handling: 0,\n        braking: 0,\n      },\n      price: 0,\n      image: 'standard_engine.png',\n    },\n    {\n      id: 'engine2',\n      name: 'Turbo Engine',\n      type: 'engine',\n      unlocked: false,\n      stats: {\n        speed: 10,\n        acceleration: 8,\n        handling: 0,\n        braking: 0,\n      },\n      price: 2000,\n      image: 'turbo_engine.png',\n    },\n  ],\n  equippedParts: {\n    'sports_car': {\n      body: 'body1',\n      wheels: 'wheels1',\n      spoiler: null,\n      engine: 'engine1',\n      transmission: null,\n      suspension: null,\n      tires: null,\n      nitro: null,\n      headlights: null,\n      exhaust: null,\n      decals: [],\n    },\n    'muscle_car': {\n      body: 'body1',\n      wheels: 'wheels1',\n      spoiler: null,\n      engine: 'engine1',\n      transmission: null,\n      suspension: null,\n      tires: null,\n      nitro: null,\n      headlights: null,\n      exhaust: null,\n      decals: [],\n    },\n    'rally_car': {\n      body: 'body1',\n      wheels: 'wheels1',\n      spoiler: null,\n      engine: 'engine1',\n      transmission: null,\n      suspension: null,\n      tires: null,\n      nitro: null,\n      headlights: null,\n      exhaust: null,\n      decals: [],\n    },\n  },\n  availableColors: [\n    {\n      id: 'color1',\n      name: 'Classic Red',\n      primary: '#ff0000',\n      secondary: '#990000',\n      detail: '#ffffff',\n      unlocked: true,\n      price: 0,\n    },\n    {\n      id: 'color2',\n      name: 'Electric Blue',\n      primary: '#0066ff',\n      secondary: '#003399',\n      detail: '#ffffff',\n      unlocked: true,\n      price: 0,\n    },\n    {\n      id: 'color3',\n      name: 'Mystic Purple',\n      primary: '#9900cc',\n      secondary: '#660099',\n      detail: '#ffcc00',\n      unlocked: false,\n      price: 500,\n    },\n  ],\n  selectedColors: {\n    vehicle1: {\n      colorId: 'color1',\n    },\n  },\n  savedConfigurations: [],\n  // Performance stats cache\n  cachedStats: {}\n};\n\nexport const vehicleSlice = createSlice({\n  name: 'vehicle',\n  initialState,\n  reducers: {\n    setCurrentVehicle: (state, action: PayloadAction<string>) => {\n      state.currentVehicleId = action.payload;\n    },\n    unlockVehicle: (state, action: PayloadAction<string>) => {\n      if (state.vehicles[action.payload]) {\n        state.vehicles[action.payload].unlocked = true;\n      }\n    },\n    unlockPart: (state, action: PayloadAction<string>) => {\n      const part = state.availableParts.find(p => p.id === action.payload);\n      if (part) {\n        part.unlocked = true;\n      }\n    },\n    equipPart: (state, action: PayloadAction<{ vehicleId: string; partId: string }>) => {\n      const part = state.availableParts.find(p => p.id === action.payload.partId);\n      if (part && part.unlocked) {\n        // Check if the part is compatible with this vehicle type\n        const vehicle = state.vehicles[action.payload.vehicleId];\n        if (vehicle && (!part.vehicleTypes || part.vehicleTypes.includes(vehicle.type))) {\n          // Initialize parts object if it doesn't exist\n          if (!state.equippedParts[action.payload.vehicleId]) {\n            state.equippedParts[action.payload.vehicleId] = {\n              body: null,\n              wheels: null,\n              spoiler: null,\n              engine: null,\n              transmission: null,\n              suspension: null,\n              tires: null,\n              nitro: null,\n              headlights: null,\n              exhaust: null,\n              decals: [],\n            };\n          }\n\n          // Handle decals differently (can have multiple)\n          if (part.type === 'decal') {\n            if (!state.equippedParts[action.payload.vehicleId].decals.includes(part.id)) {\n              state.equippedParts[action.payload.vehicleId].decals.push(part.id);\n            }\n          } else {\n            (state.equippedParts[action.payload.vehicleId] as any)[part.type] = part.id;\n          }\n          \n          // Update vehicle upgrades if it's an upgrade part\n          if (['engine', 'transmission', 'suspension', 'tires', 'nitro'].includes(part.type)) {\n            const upgradeLevel = part.stats.speed + part.stats.acceleration + \n                               part.stats.handling + part.stats.braking;\n            const normalizedLevel = Math.floor(upgradeLevel / 10);\n            \n            // Update the upgrade level in the vehicle\n            if (vehicle.upgrades) {\n              (vehicle.upgrades as any)[part.type] = normalizedLevel;\n            }\n          }\n          \n          // Recalculate and cache the vehicle stats\n          calculateVehicleStats(state, action.payload.vehicleId);\n        }\n      }\n    },\n    removePart: (state, action: PayloadAction<{ vehicleId: string; partType: string; partId?: string }>) => {\n      if (state.equippedParts[action.payload.vehicleId]) {\n        if (action.payload.partType === 'decals' && action.payload.partId) {\n          state.equippedParts[action.payload.vehicleId].decals = \n            state.equippedParts[action.payload.vehicleId].decals.filter(id => id !== action.payload.partId);\n        } else if (action.payload.partType !== 'decals') {\n          (state.equippedParts[action.payload.vehicleId] as any)[action.payload.partType] = null;\n          \n          // Reset upgrade level if it's an upgrade part\n          if (['engine', 'transmission', 'suspension', 'tires', 'nitro'].includes(action.payload.partType)) {\n            const vehicle = state.vehicles[action.payload.vehicleId];\n            if (vehicle && vehicle.upgrades) {\n              (vehicle.upgrades as any)[action.payload.partType] = 0;\n            }\n          }\n        }\n        \n        // Recalculate and cache the vehicle stats\n        calculateVehicleStats(state, action.payload.vehicleId);\n      }\n    },\n    selectColor: (state, action: PayloadAction<{ vehicleId: string; colorId: string }>) => {\n      const color = state.availableColors.find(c => c.id === action.payload.colorId);\n      if (color && color.unlocked) {\n        state.selectedColors[action.payload.vehicleId] = {\n          colorId: color.id,\n        };\n      }\n    },\n    unlockColor: (state, action: PayloadAction<string>) => {\n      const color = state.availableColors.find(c => c.id === action.payload);\n      if (color) {\n        color.unlocked = true;\n      }\n    },\n    saveConfiguration: (state, action: PayloadAction<{ name: string }>) => {\n      const vehicleId = state.currentVehicleId;\n      const parts = state.equippedParts[vehicleId] || {\n        body: null,\n        wheels: null,\n        spoiler: null,\n        engine: null,\n        transmission: null,\n        suspension: null,\n        tires: null,\n        nitro: null,\n        headlights: null,\n        exhaust: null,\n        decals: [],\n      };\n      const colorId = state.selectedColors[vehicleId]?.colorId || state.availableColors[0].id;\n      \n      const configId = `config_${Date.now()}`;\n      state.savedConfigurations.push({\n        id: configId,\n        name: action.payload.name,\n        vehicleId,\n        parts: { ...parts },\n        colorId,\n      });\n    },\n    loadConfiguration: (state, action: PayloadAction<string>) => {\n      const config = state.savedConfigurations.find(c => c.id === action.payload);\n      if (config) {\n        state.currentVehicleId = config.vehicleId;\n        state.equippedParts[config.vehicleId] = { ...config.parts };\n        state.selectedColors[config.vehicleId] = {\n          colorId: config.colorId,\n        };\n        \n        // Recalculate and cache the vehicle stats\n        calculateVehicleStats(state, config.vehicleId);\n      }\n    },\n    deleteConfiguration: (state, action: PayloadAction<string>) => {\n      state.savedConfigurations = state.savedConfigurations.filter(c => c.id !== action.payload);\n    },\n    \n    upgradeVehicle: (state, action: PayloadAction<{ vehicleId: string; upgradeType: keyof VehicleUpgrades; level: number }>) => {\n      const { vehicleId, upgradeType, level } = action.payload;\n      const vehicle = state.vehicles[vehicleId];\n      \n      if (vehicle && vehicle.upgrades) {\n        vehicle.upgrades[upgradeType] = level;\n        \n        // Recalculate and cache the vehicle stats\n        calculateVehicleStats(state, vehicleId);\n      }\n    },\n    \n    calculateAllVehicleStats: (state) => {\n      // Calculate stats for all vehicles\n      Object.keys(state.vehicles).forEach(vehicleId => {\n        calculateVehicleStats(state, vehicleId);\n      });\n    }\n  }\n});\n\n// Helper function to calculate vehicle stats based on base stats and equipped parts\nconst calculateVehicleStats = (state: VehicleState, vehicleId: string) => {\n  const vehicle = state.vehicles[vehicleId];\n  const parts = state.equippedParts[vehicleId];\n  \n  if (!vehicle || !parts) return;\n  \n  // Start with base stats\n  let speed = vehicle.topSpeed;\n  let acceleration = vehicle.acceleration * 10; // Scale to 0-100\n  let handling = vehicle.handling * 10;\n  let braking = vehicle.braking * 10;\n  \n  // Add stats from equipped parts\n  Object.entries(parts).forEach(([partType, partId]) => {\n    if (partId && partType !== 'decals') {\n      const part = state.availableParts.find(p => p.id === partId);\n      if (part) {\n        speed += part.stats.speed;\n        acceleration += part.stats.acceleration;\n        handling += part.stats.handling;\n        braking += part.stats.braking;\n      }\n    }\n  });\n  \n  // Add stats from upgrades\n  if (vehicle.upgrades) {\n    speed += vehicle.upgrades.engine * 10;\n    acceleration += vehicle.upgrades.transmission * 5;\n    handling += vehicle.upgrades.suspension * 5;\n    braking += vehicle.upgrades.tires * 5;\n    \n    // Nitro adds to both speed and acceleration\n    speed += vehicle.upgrades.nitro * 5;\n    acceleration += vehicle.upgrades.nitro * 5;\n  }\n  \n  // Cache the calculated stats\n  state.cachedStats[vehicleId] = {\n    speed,\n    acceleration,\n    handling,\n    braking\n  };\n};\n\nexport const {\n  setCurrentVehicle,\n  unlockVehicle,\n  unlockPart,\n  equipPart,\n  removePart,\n  selectColor,\n  unlockColor,\n  saveConfiguration,\n  loadConfiguration,\n  deleteConfiguration,\n  upgradeVehicle,\n  calculateAllVehicleStats,\n} = vehicleSlice.actions;\n\nexport default vehicleSlice.reducer;\n"], "mappings": "AAAA,SAASA,WAAW,QAAuB,kBAAkB;AA0G7D,MAAMC,YAA0B,GAAG;EACjCC,QAAQ,EAAE;IACR,eAAe,EAAE;MACfC,EAAE,EAAE,eAAe;MACnBC,IAAI,EAAE,kBAAkB;MACxBC,IAAI,EAAE,QAAQ;MACdC,QAAQ,EAAE,GAAG;MACbC,YAAY,EAAE,GAAG;MACjBC,QAAQ,EAAE,GAAG;MACbC,OAAO,EAAE,GAAG;MACZC,KAAK,EAAE,CAAC;MAAE;MACVC,QAAQ,EAAE,IAAI;MACdC,MAAM,EAAE,CAAC,SAAS,EAAE,SAAS,EAAE,SAAS,EAAE,SAAS,EAAE,SAAS,EAAE,SAAS,CAAC;MAC1EC,QAAQ,EAAE;QACRC,MAAM,EAAE,CAAC;QACTC,YAAY,EAAE,CAAC;QACfC,UAAU,EAAE,CAAC;QACbC,KAAK,EAAE,CAAC;QACRC,KAAK,EAAE;MACT,CAAC;MACDC,gBAAgB,EAAE,GAAG;MACrBC,iBAAiB,EAAE,GAAG;MACtBC,kBAAkB,EAAE,IAAI;MACxBC,sBAAsB,EAAE;IAC1B,CAAC;IACD,eAAe,EAAE;MACfnB,EAAE,EAAE,eAAe;MACnBC,IAAI,EAAE,kBAAkB;MACxBC,IAAI,EAAE,QAAQ;MACdC,QAAQ,EAAE,GAAG;MACbC,YAAY,EAAE,GAAG;MACjBC,QAAQ,EAAE,GAAG;MACbC,OAAO,EAAE,GAAG;MACZC,KAAK,EAAE,IAAI;MACXC,QAAQ,EAAE,KAAK;MACfC,MAAM,EAAE,CAAC,SAAS,EAAE,SAAS,EAAE,SAAS,EAAE,SAAS,CAAC;MACpDW,WAAW,EAAE,mCAAmC;MAChDC,KAAK,EAAE,IAAI;MACXX,QAAQ,EAAE;QACRC,MAAM,EAAE,CAAC;QACTC,YAAY,EAAE,CAAC;QACfC,UAAU,EAAE,CAAC;QACbC,KAAK,EAAE,CAAC;QACRC,KAAK,EAAE;MACT,CAAC;MACDC,gBAAgB,EAAE,EAAE;MACpBC,iBAAiB,EAAE,GAAG;MACtBC,kBAAkB,EAAE,IAAI;MACxBC,sBAAsB,EAAE;IAC1B,CAAC;IACD,YAAY,EAAE;MACZnB,EAAE,EAAE,YAAY;MAChBC,IAAI,EAAE,iBAAiB;MACvBC,IAAI,EAAE,QAAQ;MACdC,QAAQ,EAAE,GAAG;MACbC,YAAY,EAAE,GAAG;MACjBC,QAAQ,EAAE,GAAG;MACbC,OAAO,EAAE,GAAG;MACZC,KAAK,EAAE,KAAK;MACZC,QAAQ,EAAE,KAAK;MACfC,MAAM,EAAE,CAAC,SAAS,EAAE,SAAS,EAAE,SAAS,EAAE,SAAS,CAAC;MACpDC,QAAQ,EAAE;QACRC,MAAM,EAAE,CAAC;QACTC,YAAY,EAAE,CAAC;QACfC,UAAU,EAAE,CAAC;QACbC,KAAK,EAAE,CAAC;QACRC,KAAK,EAAE;MACT,CAAC;MACDC,gBAAgB,EAAE,GAAG;MACrBC,iBAAiB,EAAE,GAAG;MACtBC,kBAAkB,EAAE,IAAI;MACxBC,sBAAsB,EAAE;IAC1B,CAAC;IACD,WAAW,EAAE;MACXnB,EAAE,EAAE,WAAW;MACfC,IAAI,EAAE,eAAe;MACrBC,IAAI,EAAE,OAAO;MACbC,QAAQ,EAAE,GAAG;MACbC,YAAY,EAAE,GAAG;MACjBC,QAAQ,EAAE,GAAG;MACbC,OAAO,EAAE,GAAG;MACZC,KAAK,EAAE,KAAK;MACZC,QAAQ,EAAE,KAAK;MACfC,MAAM,EAAE,CAAC,SAAS,EAAE,SAAS,EAAE,SAAS,EAAE,SAAS,CAAC;MACpDC,QAAQ,EAAE;QACRC,MAAM,EAAE,CAAC;QACTC,YAAY,EAAE,CAAC;QACfC,UAAU,EAAE,CAAC;QACbC,KAAK,EAAE,CAAC;QACRC,KAAK,EAAE;MACT,CAAC;MACDC,gBAAgB,EAAE,GAAG;MACrBC,iBAAiB,EAAE,GAAG;MACtBC,kBAAkB,EAAE,IAAI;MACxBC,sBAAsB,EAAE;IAC1B,CAAC;IACD,UAAU,EAAE;MACVnB,EAAE,EAAE,UAAU;MACdC,IAAI,EAAE,iBAAiB;MACvBC,IAAI,EAAE,UAAU;MAChBC,QAAQ,EAAE,GAAG;MACbC,YAAY,EAAE,IAAI;MAClBC,QAAQ,EAAE,GAAG;MACbC,OAAO,EAAE,GAAG;MACZC,KAAK,EAAE,MAAM;MACbC,QAAQ,EAAE,KAAK;MACfC,MAAM,EAAE,CAAC,SAAS,EAAE,SAAS,EAAE,SAAS,EAAE,SAAS,CAAC;MACpDC,QAAQ,EAAE;QACRC,MAAM,EAAE,CAAC;QACTC,YAAY,EAAE,CAAC;QACfC,UAAU,EAAE,CAAC;QACbC,KAAK,EAAE,CAAC;QACRC,KAAK,EAAE;MACT,CAAC;MACDC,gBAAgB,EAAE,GAAG;MACrBC,iBAAiB,EAAE,GAAG;MACtBC,kBAAkB,EAAE,IAAI;MACxBC,sBAAsB,EAAE;IAC1B,CAAC;IACD,aAAa,EAAE;MACbnB,EAAE,EAAE,aAAa;MACjBC,IAAI,EAAE,eAAe;MACrBC,IAAI,EAAE,SAAS;MACfC,QAAQ,EAAE,GAAG;MACbC,YAAY,EAAE,IAAI;MAClBC,QAAQ,EAAE,IAAI;MACdC,OAAO,EAAE,IAAI;MACbC,KAAK,EAAE,MAAM;MACbC,QAAQ,EAAE,KAAK;MACfC,MAAM,EAAE,CAAC,SAAS,EAAE,SAAS,EAAE,SAAS,EAAE,SAAS,CAAC;MACpDC,QAAQ,EAAE;QACRC,MAAM,EAAE,CAAC;QACTC,YAAY,EAAE,CAAC;QACfC,UAAU,EAAE,CAAC;QACbC,KAAK,EAAE,CAAC;QACRC,KAAK,EAAE;MACT;IACF;EACF,CAAC;EACDO,gBAAgB,EAAE,eAAe;EACjCC,gBAAgB,EAAE,CAAC,eAAe,CAAC;EAAE;EACrCC,cAAc,EAAE,CACd;IACExB,EAAE,EAAE,OAAO;IACXC,IAAI,EAAE,eAAe;IACrBC,IAAI,EAAE,MAAM;IACZM,QAAQ,EAAE,IAAI;IACdiB,KAAK,EAAE;MACLC,KAAK,EAAE,CAAC;MACRtB,YAAY,EAAE,CAAC;MACfC,QAAQ,EAAE,CAAC;MACXC,OAAO,EAAE;IACX,CAAC;IACDC,KAAK,EAAE,CAAC;IACRoB,KAAK,EAAE;EACT,CAAC,EACD;IACE3B,EAAE,EAAE,OAAO;IACXC,IAAI,EAAE,kBAAkB;IACxBC,IAAI,EAAE,MAAM;IACZM,QAAQ,EAAE,KAAK;IACfiB,KAAK,EAAE;MACLC,KAAK,EAAE,CAAC;MACRtB,YAAY,EAAE,CAAC;MACfC,QAAQ,EAAE,CAAC;MACXC,OAAO,EAAE;IACX,CAAC;IACDC,KAAK,EAAE,IAAI;IACXoB,KAAK,EAAE;EACT,CAAC,EACD;IACE3B,EAAE,EAAE,SAAS;IACbC,IAAI,EAAE,iBAAiB;IACvBC,IAAI,EAAE,QAAQ;IACdM,QAAQ,EAAE,IAAI;IACdiB,KAAK,EAAE;MACLC,KAAK,EAAE,CAAC;MACRtB,YAAY,EAAE,CAAC;MACfC,QAAQ,EAAE,CAAC;MACXC,OAAO,EAAE;IACX,CAAC;IACDC,KAAK,EAAE,CAAC;IACRoB,KAAK,EAAE;EACT,CAAC,EACD;IACE3B,EAAE,EAAE,SAAS;IACbC,IAAI,EAAE,oBAAoB;IAC1BC,IAAI,EAAE,QAAQ;IACdM,QAAQ,EAAE,KAAK;IACfiB,KAAK,EAAE;MACLC,KAAK,EAAE,CAAC;MACRtB,YAAY,EAAE,CAAC;MACfC,QAAQ,EAAE,CAAC;MACXC,OAAO,EAAE;IACX,CAAC;IACDC,KAAK,EAAE,GAAG;IACVoB,KAAK,EAAE;EACT,CAAC,EACD;IACE3B,EAAE,EAAE,SAAS;IACbC,IAAI,EAAE,iBAAiB;IACvBC,IAAI,EAAE,QAAQ;IACdM,QAAQ,EAAE,IAAI;IACdiB,KAAK,EAAE;MACLC,KAAK,EAAE,CAAC;MACRtB,YAAY,EAAE,CAAC;MACfC,QAAQ,EAAE,CAAC;MACXC,OAAO,EAAE;IACX,CAAC;IACDC,KAAK,EAAE,CAAC;IACRoB,KAAK,EAAE;EACT,CAAC,EACD;IACE3B,EAAE,EAAE,SAAS;IACbC,IAAI,EAAE,cAAc;IACpBC,IAAI,EAAE,QAAQ;IACdM,QAAQ,EAAE,KAAK;IACfiB,KAAK,EAAE;MACLC,KAAK,EAAE,EAAE;MACTtB,YAAY,EAAE,CAAC;MACfC,QAAQ,EAAE,CAAC;MACXC,OAAO,EAAE;IACX,CAAC;IACDC,KAAK,EAAE,IAAI;IACXoB,KAAK,EAAE;EACT,CAAC,CACF;EACDC,aAAa,EAAE;IACb,YAAY,EAAE;MACZC,IAAI,EAAE,OAAO;MACbC,MAAM,EAAE,SAAS;MACjBC,OAAO,EAAE,IAAI;MACbpB,MAAM,EAAE,SAAS;MACjBC,YAAY,EAAE,IAAI;MAClBC,UAAU,EAAE,IAAI;MAChBC,KAAK,EAAE,IAAI;MACXC,KAAK,EAAE,IAAI;MACXiB,UAAU,EAAE,IAAI;MAChBC,OAAO,EAAE,IAAI;MACbC,MAAM,EAAE;IACV,CAAC;IACD,YAAY,EAAE;MACZL,IAAI,EAAE,OAAO;MACbC,MAAM,EAAE,SAAS;MACjBC,OAAO,EAAE,IAAI;MACbpB,MAAM,EAAE,SAAS;MACjBC,YAAY,EAAE,IAAI;MAClBC,UAAU,EAAE,IAAI;MAChBC,KAAK,EAAE,IAAI;MACXC,KAAK,EAAE,IAAI;MACXiB,UAAU,EAAE,IAAI;MAChBC,OAAO,EAAE,IAAI;MACbC,MAAM,EAAE;IACV,CAAC;IACD,WAAW,EAAE;MACXL,IAAI,EAAE,OAAO;MACbC,MAAM,EAAE,SAAS;MACjBC,OAAO,EAAE,IAAI;MACbpB,MAAM,EAAE,SAAS;MACjBC,YAAY,EAAE,IAAI;MAClBC,UAAU,EAAE,IAAI;MAChBC,KAAK,EAAE,IAAI;MACXC,KAAK,EAAE,IAAI;MACXiB,UAAU,EAAE,IAAI;MAChBC,OAAO,EAAE,IAAI;MACbC,MAAM,EAAE;IACV;EACF,CAAC;EACDC,eAAe,EAAE,CACf;IACEnC,EAAE,EAAE,QAAQ;IACZC,IAAI,EAAE,aAAa;IACnBmC,OAAO,EAAE,SAAS;IAClBC,SAAS,EAAE,SAAS;IACpBC,MAAM,EAAE,SAAS;IACjB9B,QAAQ,EAAE,IAAI;IACdD,KAAK,EAAE;EACT,CAAC,EACD;IACEP,EAAE,EAAE,QAAQ;IACZC,IAAI,EAAE,eAAe;IACrBmC,OAAO,EAAE,SAAS;IAClBC,SAAS,EAAE,SAAS;IACpBC,MAAM,EAAE,SAAS;IACjB9B,QAAQ,EAAE,IAAI;IACdD,KAAK,EAAE;EACT,CAAC,EACD;IACEP,EAAE,EAAE,QAAQ;IACZC,IAAI,EAAE,eAAe;IACrBmC,OAAO,EAAE,SAAS;IAClBC,SAAS,EAAE,SAAS;IACpBC,MAAM,EAAE,SAAS;IACjB9B,QAAQ,EAAE,KAAK;IACfD,KAAK,EAAE;EACT,CAAC,CACF;EACDgC,cAAc,EAAE;IACdC,QAAQ,EAAE;MACRC,OAAO,EAAE;IACX;EACF,CAAC;EACDC,mBAAmB,EAAE,EAAE;EACvB;EACAC,WAAW,EAAE,CAAC;AAChB,CAAC;AAED,OAAO,MAAMC,YAAY,GAAG/C,WAAW,CAAC;EACtCI,IAAI,EAAE,SAAS;EACfH,YAAY;EACZ+C,QAAQ,EAAE;IACRC,iBAAiB,EAAEA,CAACC,KAAK,EAAEC,MAA6B,KAAK;MAC3DD,KAAK,CAACzB,gBAAgB,GAAG0B,MAAM,CAACC,OAAO;IACzC,CAAC;IACDC,aAAa,EAAEA,CAACH,KAAK,EAAEC,MAA6B,KAAK;MACvD,IAAID,KAAK,CAAChD,QAAQ,CAACiD,MAAM,CAACC,OAAO,CAAC,EAAE;QAClCF,KAAK,CAAChD,QAAQ,CAACiD,MAAM,CAACC,OAAO,CAAC,CAACzC,QAAQ,GAAG,IAAI;MAChD;IACF,CAAC;IACD2C,UAAU,EAAEA,CAACJ,KAAK,EAAEC,MAA6B,KAAK;MACpD,MAAMI,IAAI,GAAGL,KAAK,CAACvB,cAAc,CAAC6B,IAAI,CAACC,CAAC,IAAIA,CAAC,CAACtD,EAAE,KAAKgD,MAAM,CAACC,OAAO,CAAC;MACpE,IAAIG,IAAI,EAAE;QACRA,IAAI,CAAC5C,QAAQ,GAAG,IAAI;MACtB;IACF,CAAC;IACD+C,SAAS,EAAEA,CAACR,KAAK,EAAEC,MAA4D,KAAK;MAClF,MAAMI,IAAI,GAAGL,KAAK,CAACvB,cAAc,CAAC6B,IAAI,CAACC,CAAC,IAAIA,CAAC,CAACtD,EAAE,KAAKgD,MAAM,CAACC,OAAO,CAACO,MAAM,CAAC;MAC3E,IAAIJ,IAAI,IAAIA,IAAI,CAAC5C,QAAQ,EAAE;QACzB;QACA,MAAMiD,OAAO,GAAGV,KAAK,CAAChD,QAAQ,CAACiD,MAAM,CAACC,OAAO,CAACS,SAAS,CAAC;QACxD,IAAID,OAAO,KAAK,CAACL,IAAI,CAACO,YAAY,IAAIP,IAAI,CAACO,YAAY,CAACC,QAAQ,CAACH,OAAO,CAACvD,IAAI,CAAC,CAAC,EAAE;UAC/E;UACA,IAAI,CAAC6C,KAAK,CAACnB,aAAa,CAACoB,MAAM,CAACC,OAAO,CAACS,SAAS,CAAC,EAAE;YAClDX,KAAK,CAACnB,aAAa,CAACoB,MAAM,CAACC,OAAO,CAACS,SAAS,CAAC,GAAG;cAC9C7B,IAAI,EAAE,IAAI;cACVC,MAAM,EAAE,IAAI;cACZC,OAAO,EAAE,IAAI;cACbpB,MAAM,EAAE,IAAI;cACZC,YAAY,EAAE,IAAI;cAClBC,UAAU,EAAE,IAAI;cAChBC,KAAK,EAAE,IAAI;cACXC,KAAK,EAAE,IAAI;cACXiB,UAAU,EAAE,IAAI;cAChBC,OAAO,EAAE,IAAI;cACbC,MAAM,EAAE;YACV,CAAC;UACH;;UAEA;UACA,IAAIkB,IAAI,CAAClD,IAAI,KAAK,OAAO,EAAE;YACzB,IAAI,CAAC6C,KAAK,CAACnB,aAAa,CAACoB,MAAM,CAACC,OAAO,CAACS,SAAS,CAAC,CAACxB,MAAM,CAAC0B,QAAQ,CAACR,IAAI,CAACpD,EAAE,CAAC,EAAE;cAC3E+C,KAAK,CAACnB,aAAa,CAACoB,MAAM,CAACC,OAAO,CAACS,SAAS,CAAC,CAACxB,MAAM,CAAC2B,IAAI,CAACT,IAAI,CAACpD,EAAE,CAAC;YACpE;UACF,CAAC,MAAM;YACJ+C,KAAK,CAACnB,aAAa,CAACoB,MAAM,CAACC,OAAO,CAACS,SAAS,CAAC,CAASN,IAAI,CAAClD,IAAI,CAAC,GAAGkD,IAAI,CAACpD,EAAE;UAC7E;;UAEA;UACA,IAAI,CAAC,QAAQ,EAAE,cAAc,EAAE,YAAY,EAAE,OAAO,EAAE,OAAO,CAAC,CAAC4D,QAAQ,CAACR,IAAI,CAAClD,IAAI,CAAC,EAAE;YAClF,MAAM4D,YAAY,GAAGV,IAAI,CAAC3B,KAAK,CAACC,KAAK,GAAG0B,IAAI,CAAC3B,KAAK,CAACrB,YAAY,GAC5CgD,IAAI,CAAC3B,KAAK,CAACpB,QAAQ,GAAG+C,IAAI,CAAC3B,KAAK,CAACnB,OAAO;YAC3D,MAAMyD,eAAe,GAAGC,IAAI,CAACC,KAAK,CAACH,YAAY,GAAG,EAAE,CAAC;;YAErD;YACA,IAAIL,OAAO,CAAC/C,QAAQ,EAAE;cACnB+C,OAAO,CAAC/C,QAAQ,CAAS0C,IAAI,CAAClD,IAAI,CAAC,GAAG6D,eAAe;YACxD;UACF;;UAEA;UACAG,qBAAqB,CAACnB,KAAK,EAAEC,MAAM,CAACC,OAAO,CAACS,SAAS,CAAC;QACxD;MACF;IACF,CAAC;IACDS,UAAU,EAAEA,CAACpB,KAAK,EAAEC,MAA+E,KAAK;MACtG,IAAID,KAAK,CAACnB,aAAa,CAACoB,MAAM,CAACC,OAAO,CAACS,SAAS,CAAC,EAAE;QACjD,IAAIV,MAAM,CAACC,OAAO,CAACmB,QAAQ,KAAK,QAAQ,IAAIpB,MAAM,CAACC,OAAO,CAACO,MAAM,EAAE;UACjET,KAAK,CAACnB,aAAa,CAACoB,MAAM,CAACC,OAAO,CAACS,SAAS,CAAC,CAACxB,MAAM,GAClDa,KAAK,CAACnB,aAAa,CAACoB,MAAM,CAACC,OAAO,CAACS,SAAS,CAAC,CAACxB,MAAM,CAACmC,MAAM,CAACrE,EAAE,IAAIA,EAAE,KAAKgD,MAAM,CAACC,OAAO,CAACO,MAAM,CAAC;QACnG,CAAC,MAAM,IAAIR,MAAM,CAACC,OAAO,CAACmB,QAAQ,KAAK,QAAQ,EAAE;UAC9CrB,KAAK,CAACnB,aAAa,CAACoB,MAAM,CAACC,OAAO,CAACS,SAAS,CAAC,CAASV,MAAM,CAACC,OAAO,CAACmB,QAAQ,CAAC,GAAG,IAAI;;UAEtF;UACA,IAAI,CAAC,QAAQ,EAAE,cAAc,EAAE,YAAY,EAAE,OAAO,EAAE,OAAO,CAAC,CAACR,QAAQ,CAACZ,MAAM,CAACC,OAAO,CAACmB,QAAQ,CAAC,EAAE;YAChG,MAAMX,OAAO,GAAGV,KAAK,CAAChD,QAAQ,CAACiD,MAAM,CAACC,OAAO,CAACS,SAAS,CAAC;YACxD,IAAID,OAAO,IAAIA,OAAO,CAAC/C,QAAQ,EAAE;cAC9B+C,OAAO,CAAC/C,QAAQ,CAASsC,MAAM,CAACC,OAAO,CAACmB,QAAQ,CAAC,GAAG,CAAC;YACxD;UACF;QACF;;QAEA;QACAF,qBAAqB,CAACnB,KAAK,EAAEC,MAAM,CAACC,OAAO,CAACS,SAAS,CAAC;MACxD;IACF,CAAC;IACDY,WAAW,EAAEA,CAACvB,KAAK,EAAEC,MAA6D,KAAK;MACrF,MAAMuB,KAAK,GAAGxB,KAAK,CAACZ,eAAe,CAACkB,IAAI,CAACmB,CAAC,IAAIA,CAAC,CAACxE,EAAE,KAAKgD,MAAM,CAACC,OAAO,CAACR,OAAO,CAAC;MAC9E,IAAI8B,KAAK,IAAIA,KAAK,CAAC/D,QAAQ,EAAE;QAC3BuC,KAAK,CAACR,cAAc,CAACS,MAAM,CAACC,OAAO,CAACS,SAAS,CAAC,GAAG;UAC/CjB,OAAO,EAAE8B,KAAK,CAACvE;QACjB,CAAC;MACH;IACF,CAAC;IACDyE,WAAW,EAAEA,CAAC1B,KAAK,EAAEC,MAA6B,KAAK;MACrD,MAAMuB,KAAK,GAAGxB,KAAK,CAACZ,eAAe,CAACkB,IAAI,CAACmB,CAAC,IAAIA,CAAC,CAACxE,EAAE,KAAKgD,MAAM,CAACC,OAAO,CAAC;MACtE,IAAIsB,KAAK,EAAE;QACTA,KAAK,CAAC/D,QAAQ,GAAG,IAAI;MACvB;IACF,CAAC;IACDkE,iBAAiB,EAAEA,CAAC3B,KAAK,EAAEC,MAAuC,KAAK;MAAA,IAAA2B,qBAAA;MACrE,MAAMjB,SAAS,GAAGX,KAAK,CAACzB,gBAAgB;MACxC,MAAMsD,KAAK,GAAG7B,KAAK,CAACnB,aAAa,CAAC8B,SAAS,CAAC,IAAI;QAC9C7B,IAAI,EAAE,IAAI;QACVC,MAAM,EAAE,IAAI;QACZC,OAAO,EAAE,IAAI;QACbpB,MAAM,EAAE,IAAI;QACZC,YAAY,EAAE,IAAI;QAClBC,UAAU,EAAE,IAAI;QAChBC,KAAK,EAAE,IAAI;QACXC,KAAK,EAAE,IAAI;QACXiB,UAAU,EAAE,IAAI;QAChBC,OAAO,EAAE,IAAI;QACbC,MAAM,EAAE;MACV,CAAC;MACD,MAAMO,OAAO,GAAG,EAAAkC,qBAAA,GAAA5B,KAAK,CAACR,cAAc,CAACmB,SAAS,CAAC,cAAAiB,qBAAA,uBAA/BA,qBAAA,CAAiClC,OAAO,KAAIM,KAAK,CAACZ,eAAe,CAAC,CAAC,CAAC,CAACnC,EAAE;MAEvF,MAAM6E,QAAQ,GAAG,UAAUC,IAAI,CAACC,GAAG,CAAC,CAAC,EAAE;MACvChC,KAAK,CAACL,mBAAmB,CAACmB,IAAI,CAAC;QAC7B7D,EAAE,EAAE6E,QAAQ;QACZ5E,IAAI,EAAE+C,MAAM,CAACC,OAAO,CAAChD,IAAI;QACzByD,SAAS;QACTkB,KAAK,EAAE;UAAE,GAAGA;QAAM,CAAC;QACnBnC;MACF,CAAC,CAAC;IACJ,CAAC;IACDuC,iBAAiB,EAAEA,CAACjC,KAAK,EAAEC,MAA6B,KAAK;MAC3D,MAAMiC,MAAM,GAAGlC,KAAK,CAACL,mBAAmB,CAACW,IAAI,CAACmB,CAAC,IAAIA,CAAC,CAACxE,EAAE,KAAKgD,MAAM,CAACC,OAAO,CAAC;MAC3E,IAAIgC,MAAM,EAAE;QACVlC,KAAK,CAACzB,gBAAgB,GAAG2D,MAAM,CAACvB,SAAS;QACzCX,KAAK,CAACnB,aAAa,CAACqD,MAAM,CAACvB,SAAS,CAAC,GAAG;UAAE,GAAGuB,MAAM,CAACL;QAAM,CAAC;QAC3D7B,KAAK,CAACR,cAAc,CAAC0C,MAAM,CAACvB,SAAS,CAAC,GAAG;UACvCjB,OAAO,EAAEwC,MAAM,CAACxC;QAClB,CAAC;;QAED;QACAyB,qBAAqB,CAACnB,KAAK,EAAEkC,MAAM,CAACvB,SAAS,CAAC;MAChD;IACF,CAAC;IACDwB,mBAAmB,EAAEA,CAACnC,KAAK,EAAEC,MAA6B,KAAK;MAC7DD,KAAK,CAACL,mBAAmB,GAAGK,KAAK,CAACL,mBAAmB,CAAC2B,MAAM,CAACG,CAAC,IAAIA,CAAC,CAACxE,EAAE,KAAKgD,MAAM,CAACC,OAAO,CAAC;IAC5F,CAAC;IAEDkC,cAAc,EAAEA,CAACpC,KAAK,EAAEC,MAA+F,KAAK;MAC1H,MAAM;QAAEU,SAAS;QAAE0B,WAAW;QAAEC;MAAM,CAAC,GAAGrC,MAAM,CAACC,OAAO;MACxD,MAAMQ,OAAO,GAAGV,KAAK,CAAChD,QAAQ,CAAC2D,SAAS,CAAC;MAEzC,IAAID,OAAO,IAAIA,OAAO,CAAC/C,QAAQ,EAAE;QAC/B+C,OAAO,CAAC/C,QAAQ,CAAC0E,WAAW,CAAC,GAAGC,KAAK;;QAErC;QACAnB,qBAAqB,CAACnB,KAAK,EAAEW,SAAS,CAAC;MACzC;IACF,CAAC;IAED4B,wBAAwB,EAAGvC,KAAK,IAAK;MACnC;MACAwC,MAAM,CAACC,IAAI,CAACzC,KAAK,CAAChD,QAAQ,CAAC,CAAC0F,OAAO,CAAC/B,SAAS,IAAI;QAC/CQ,qBAAqB,CAACnB,KAAK,EAAEW,SAAS,CAAC;MACzC,CAAC,CAAC;IACJ;EACF;AACF,CAAC,CAAC;;AAEF;AACA,MAAMQ,qBAAqB,GAAGA,CAACnB,KAAmB,EAAEW,SAAiB,KAAK;EACxE,MAAMD,OAAO,GAAGV,KAAK,CAAChD,QAAQ,CAAC2D,SAAS,CAAC;EACzC,MAAMkB,KAAK,GAAG7B,KAAK,CAACnB,aAAa,CAAC8B,SAAS,CAAC;EAE5C,IAAI,CAACD,OAAO,IAAI,CAACmB,KAAK,EAAE;;EAExB;EACA,IAAIlD,KAAK,GAAG+B,OAAO,CAACtD,QAAQ;EAC5B,IAAIC,YAAY,GAAGqD,OAAO,CAACrD,YAAY,GAAG,EAAE,CAAC,CAAC;EAC9C,IAAIC,QAAQ,GAAGoD,OAAO,CAACpD,QAAQ,GAAG,EAAE;EACpC,IAAIC,OAAO,GAAGmD,OAAO,CAACnD,OAAO,GAAG,EAAE;;EAElC;EACAiF,MAAM,CAACG,OAAO,CAACd,KAAK,CAAC,CAACa,OAAO,CAAC,CAAC,CAACrB,QAAQ,EAAEZ,MAAM,CAAC,KAAK;IACpD,IAAIA,MAAM,IAAIY,QAAQ,KAAK,QAAQ,EAAE;MACnC,MAAMhB,IAAI,GAAGL,KAAK,CAACvB,cAAc,CAAC6B,IAAI,CAACC,CAAC,IAAIA,CAAC,CAACtD,EAAE,KAAKwD,MAAM,CAAC;MAC5D,IAAIJ,IAAI,EAAE;QACR1B,KAAK,IAAI0B,IAAI,CAAC3B,KAAK,CAACC,KAAK;QACzBtB,YAAY,IAAIgD,IAAI,CAAC3B,KAAK,CAACrB,YAAY;QACvCC,QAAQ,IAAI+C,IAAI,CAAC3B,KAAK,CAACpB,QAAQ;QAC/BC,OAAO,IAAI8C,IAAI,CAAC3B,KAAK,CAACnB,OAAO;MAC/B;IACF;EACF,CAAC,CAAC;;EAEF;EACA,IAAImD,OAAO,CAAC/C,QAAQ,EAAE;IACpBgB,KAAK,IAAI+B,OAAO,CAAC/C,QAAQ,CAACC,MAAM,GAAG,EAAE;IACrCP,YAAY,IAAIqD,OAAO,CAAC/C,QAAQ,CAACE,YAAY,GAAG,CAAC;IACjDP,QAAQ,IAAIoD,OAAO,CAAC/C,QAAQ,CAACG,UAAU,GAAG,CAAC;IAC3CP,OAAO,IAAImD,OAAO,CAAC/C,QAAQ,CAACI,KAAK,GAAG,CAAC;;IAErC;IACAY,KAAK,IAAI+B,OAAO,CAAC/C,QAAQ,CAACK,KAAK,GAAG,CAAC;IACnCX,YAAY,IAAIqD,OAAO,CAAC/C,QAAQ,CAACK,KAAK,GAAG,CAAC;EAC5C;;EAEA;EACAgC,KAAK,CAACJ,WAAW,CAACe,SAAS,CAAC,GAAG;IAC7BhC,KAAK;IACLtB,YAAY;IACZC,QAAQ;IACRC;EACF,CAAC;AACH,CAAC;AAED,OAAO,MAAM;EACXwC,iBAAiB;EACjBI,aAAa;EACbC,UAAU;EACVI,SAAS;EACTY,UAAU;EACVG,WAAW;EACXG,WAAW;EACXC,iBAAiB;EACjBM,iBAAiB;EACjBE,mBAAmB;EACnBC,cAAc;EACdG;AACF,CAAC,GAAG1C,YAAY,CAAC+C,OAAO;AAExB,eAAe/C,YAAY,CAACgD,OAAO", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}