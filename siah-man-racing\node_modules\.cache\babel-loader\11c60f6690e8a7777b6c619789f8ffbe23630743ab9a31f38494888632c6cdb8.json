{"ast": null, "code": "var __defProp = Object.defineProperty;\nvar __defNormalProp = (obj, key, value) => key in obj ? __defProp(obj, key, {\n  enumerable: true,\n  configurable: true,\n  writable: true,\n  value\n}) : obj[key] = value;\nvar __publicField = (obj, key, value) => {\n  __defNormalProp(obj, typeof key !== \"symbol\" ? key + \"\" : key, value);\n  return value;\n};\nimport { Object3D, Raycaster, Vector3, Quaternion, Euler, Matrix4, MeshBasicMaterial, DoubleSide, LineBasicMaterial, CylinderGeometry, BoxGeometry, BufferGeometry, Float32BufferAttribute, Mesh, Line, OctahedronGeometry, PlaneGeometry, TorusGeometry, SphereGeometry, Color } from \"three\";\nclass TransformControls extends Object3D {\n  constructor(camera, domElement) {\n    super();\n    __publicField(this, \"isTransformControls\", true);\n    __publicField(this, \"visible\", false);\n    __publicField(this, \"domElement\");\n    __publicField(this, \"raycaster\", new Raycaster());\n    __publicField(this, \"gizmo\");\n    __publicField(this, \"plane\");\n    __publicField(this, \"tempVector\", new Vector3());\n    __publicField(this, \"tempVector2\", new Vector3());\n    __publicField(this, \"tempQuaternion\", new Quaternion());\n    __publicField(this, \"unit\", {\n      X: new Vector3(1, 0, 0),\n      Y: new Vector3(0, 1, 0),\n      Z: new Vector3(0, 0, 1)\n    });\n    __publicField(this, \"pointStart\", new Vector3());\n    __publicField(this, \"pointEnd\", new Vector3());\n    __publicField(this, \"offset\", new Vector3());\n    __publicField(this, \"rotationAxis\", new Vector3());\n    __publicField(this, \"startNorm\", new Vector3());\n    __publicField(this, \"endNorm\", new Vector3());\n    __publicField(this, \"rotationAngle\", 0);\n    __publicField(this, \"cameraPosition\", new Vector3());\n    __publicField(this, \"cameraQuaternion\", new Quaternion());\n    __publicField(this, \"cameraScale\", new Vector3());\n    __publicField(this, \"parentPosition\", new Vector3());\n    __publicField(this, \"parentQuaternion\", new Quaternion());\n    __publicField(this, \"parentQuaternionInv\", new Quaternion());\n    __publicField(this, \"parentScale\", new Vector3());\n    __publicField(this, \"worldPositionStart\", new Vector3());\n    __publicField(this, \"worldQuaternionStart\", new Quaternion());\n    __publicField(this, \"worldScaleStart\", new Vector3());\n    __publicField(this, \"worldPosition\", new Vector3());\n    __publicField(this, \"worldQuaternion\", new Quaternion());\n    __publicField(this, \"worldQuaternionInv\", new Quaternion());\n    __publicField(this, \"worldScale\", new Vector3());\n    __publicField(this, \"eye\", new Vector3());\n    __publicField(this, \"positionStart\", new Vector3());\n    __publicField(this, \"quaternionStart\", new Quaternion());\n    __publicField(this, \"scaleStart\", new Vector3());\n    __publicField(this, \"camera\");\n    __publicField(this, \"object\");\n    __publicField(this, \"enabled\", true);\n    __publicField(this, \"axis\", null);\n    __publicField(this, \"mode\", \"translate\");\n    __publicField(this, \"translationSnap\", null);\n    __publicField(this, \"rotationSnap\", null);\n    __publicField(this, \"scaleSnap\", null);\n    __publicField(this, \"space\", \"world\");\n    __publicField(this, \"size\", 1);\n    __publicField(this, \"dragging\", false);\n    __publicField(this, \"showX\", true);\n    __publicField(this, \"showY\", true);\n    __publicField(this, \"showZ\", true);\n    // events\n    __publicField(this, \"changeEvent\", {\n      type: \"change\"\n    });\n    __publicField(this, \"mouseDownEvent\", {\n      type: \"mouseDown\",\n      mode: this.mode\n    });\n    __publicField(this, \"mouseUpEvent\", {\n      type: \"mouseUp\",\n      mode: this.mode\n    });\n    __publicField(this, \"objectChangeEvent\", {\n      type: \"objectChange\"\n    });\n    __publicField(this, \"intersectObjectWithRay\", (object, raycaster, includeInvisible) => {\n      const allIntersections = raycaster.intersectObject(object, true);\n      for (let i = 0; i < allIntersections.length; i++) {\n        if (allIntersections[i].object.visible || includeInvisible) {\n          return allIntersections[i];\n        }\n      }\n      return false;\n    });\n    // Set current object\n    __publicField(this, \"attach\", object => {\n      this.object = object;\n      this.visible = true;\n      return this;\n    });\n    // Detatch from object\n    __publicField(this, \"detach\", () => {\n      this.object = void 0;\n      this.visible = false;\n      this.axis = null;\n      return this;\n    });\n    // Reset\n    __publicField(this, \"reset\", () => {\n      if (!this.enabled) return this;\n      if (this.dragging) {\n        if (this.object !== void 0) {\n          this.object.position.copy(this.positionStart);\n          this.object.quaternion.copy(this.quaternionStart);\n          this.object.scale.copy(this.scaleStart);\n          this.dispatchEvent(this.changeEvent);\n          this.dispatchEvent(this.objectChangeEvent);\n          this.pointStart.copy(this.pointEnd);\n        }\n      }\n      return this;\n    });\n    __publicField(this, \"updateMatrixWorld\", () => {\n      if (this.object !== void 0) {\n        this.object.updateMatrixWorld();\n        if (this.object.parent === null) {\n          console.error(\"TransformControls: The attached 3D object must be a part of the scene graph.\");\n        } else {\n          this.object.parent.matrixWorld.decompose(this.parentPosition, this.parentQuaternion, this.parentScale);\n        }\n        this.object.matrixWorld.decompose(this.worldPosition, this.worldQuaternion, this.worldScale);\n        this.parentQuaternionInv.copy(this.parentQuaternion).invert();\n        this.worldQuaternionInv.copy(this.worldQuaternion).invert();\n      }\n      this.camera.updateMatrixWorld();\n      this.camera.matrixWorld.decompose(this.cameraPosition, this.cameraQuaternion, this.cameraScale);\n      this.eye.copy(this.cameraPosition).sub(this.worldPosition).normalize();\n      super.updateMatrixWorld();\n    });\n    __publicField(this, \"pointerHover\", pointer => {\n      if (this.object === void 0 || this.dragging === true) return;\n      this.raycaster.setFromCamera(pointer, this.camera);\n      const intersect = this.intersectObjectWithRay(this.gizmo.picker[this.mode], this.raycaster);\n      if (intersect) {\n        this.axis = intersect.object.name;\n      } else {\n        this.axis = null;\n      }\n    });\n    __publicField(this, \"pointerDown\", pointer => {\n      if (this.object === void 0 || this.dragging === true || pointer.button !== 0) return;\n      if (this.axis !== null) {\n        this.raycaster.setFromCamera(pointer, this.camera);\n        const planeIntersect = this.intersectObjectWithRay(this.plane, this.raycaster, true);\n        if (planeIntersect) {\n          let space = this.space;\n          if (this.mode === \"scale\") {\n            space = \"local\";\n          } else if (this.axis === \"E\" || this.axis === \"XYZE\" || this.axis === \"XYZ\") {\n            space = \"world\";\n          }\n          if (space === \"local\" && this.mode === \"rotate\") {\n            const snap = this.rotationSnap;\n            if (this.axis === \"X\" && snap) this.object.rotation.x = Math.round(this.object.rotation.x / snap) * snap;\n            if (this.axis === \"Y\" && snap) this.object.rotation.y = Math.round(this.object.rotation.y / snap) * snap;\n            if (this.axis === \"Z\" && snap) this.object.rotation.z = Math.round(this.object.rotation.z / snap) * snap;\n          }\n          this.object.updateMatrixWorld();\n          if (this.object.parent) {\n            this.object.parent.updateMatrixWorld();\n          }\n          this.positionStart.copy(this.object.position);\n          this.quaternionStart.copy(this.object.quaternion);\n          this.scaleStart.copy(this.object.scale);\n          this.object.matrixWorld.decompose(this.worldPositionStart, this.worldQuaternionStart, this.worldScaleStart);\n          this.pointStart.copy(planeIntersect.point).sub(this.worldPositionStart);\n        }\n        this.dragging = true;\n        this.mouseDownEvent.mode = this.mode;\n        this.dispatchEvent(this.mouseDownEvent);\n      }\n    });\n    __publicField(this, \"pointerMove\", pointer => {\n      const axis = this.axis;\n      const mode = this.mode;\n      const object = this.object;\n      let space = this.space;\n      if (mode === \"scale\") {\n        space = \"local\";\n      } else if (axis === \"E\" || axis === \"XYZE\" || axis === \"XYZ\") {\n        space = \"world\";\n      }\n      if (object === void 0 || axis === null || this.dragging === false || pointer.button !== -1) return;\n      this.raycaster.setFromCamera(pointer, this.camera);\n      const planeIntersect = this.intersectObjectWithRay(this.plane, this.raycaster, true);\n      if (!planeIntersect) return;\n      this.pointEnd.copy(planeIntersect.point).sub(this.worldPositionStart);\n      if (mode === \"translate\") {\n        this.offset.copy(this.pointEnd).sub(this.pointStart);\n        if (space === \"local\" && axis !== \"XYZ\") {\n          this.offset.applyQuaternion(this.worldQuaternionInv);\n        }\n        if (axis.indexOf(\"X\") === -1) this.offset.x = 0;\n        if (axis.indexOf(\"Y\") === -1) this.offset.y = 0;\n        if (axis.indexOf(\"Z\") === -1) this.offset.z = 0;\n        if (space === \"local\" && axis !== \"XYZ\") {\n          this.offset.applyQuaternion(this.quaternionStart).divide(this.parentScale);\n        } else {\n          this.offset.applyQuaternion(this.parentQuaternionInv).divide(this.parentScale);\n        }\n        object.position.copy(this.offset).add(this.positionStart);\n        if (this.translationSnap) {\n          if (space === \"local\") {\n            object.position.applyQuaternion(this.tempQuaternion.copy(this.quaternionStart).invert());\n            if (axis.search(\"X\") !== -1) {\n              object.position.x = Math.round(object.position.x / this.translationSnap) * this.translationSnap;\n            }\n            if (axis.search(\"Y\") !== -1) {\n              object.position.y = Math.round(object.position.y / this.translationSnap) * this.translationSnap;\n            }\n            if (axis.search(\"Z\") !== -1) {\n              object.position.z = Math.round(object.position.z / this.translationSnap) * this.translationSnap;\n            }\n            object.position.applyQuaternion(this.quaternionStart);\n          }\n          if (space === \"world\") {\n            if (object.parent) {\n              object.position.add(this.tempVector.setFromMatrixPosition(object.parent.matrixWorld));\n            }\n            if (axis.search(\"X\") !== -1) {\n              object.position.x = Math.round(object.position.x / this.translationSnap) * this.translationSnap;\n            }\n            if (axis.search(\"Y\") !== -1) {\n              object.position.y = Math.round(object.position.y / this.translationSnap) * this.translationSnap;\n            }\n            if (axis.search(\"Z\") !== -1) {\n              object.position.z = Math.round(object.position.z / this.translationSnap) * this.translationSnap;\n            }\n            if (object.parent) {\n              object.position.sub(this.tempVector.setFromMatrixPosition(object.parent.matrixWorld));\n            }\n          }\n        }\n      } else if (mode === \"scale\") {\n        if (axis.search(\"XYZ\") !== -1) {\n          let d = this.pointEnd.length() / this.pointStart.length();\n          if (this.pointEnd.dot(this.pointStart) < 0) d *= -1;\n          this.tempVector2.set(d, d, d);\n        } else {\n          this.tempVector.copy(this.pointStart);\n          this.tempVector2.copy(this.pointEnd);\n          this.tempVector.applyQuaternion(this.worldQuaternionInv);\n          this.tempVector2.applyQuaternion(this.worldQuaternionInv);\n          this.tempVector2.divide(this.tempVector);\n          if (axis.search(\"X\") === -1) {\n            this.tempVector2.x = 1;\n          }\n          if (axis.search(\"Y\") === -1) {\n            this.tempVector2.y = 1;\n          }\n          if (axis.search(\"Z\") === -1) {\n            this.tempVector2.z = 1;\n          }\n        }\n        object.scale.copy(this.scaleStart).multiply(this.tempVector2);\n        if (this.scaleSnap && this.object) {\n          if (axis.search(\"X\") !== -1) {\n            this.object.scale.x = Math.round(object.scale.x / this.scaleSnap) * this.scaleSnap || this.scaleSnap;\n          }\n          if (axis.search(\"Y\") !== -1) {\n            object.scale.y = Math.round(object.scale.y / this.scaleSnap) * this.scaleSnap || this.scaleSnap;\n          }\n          if (axis.search(\"Z\") !== -1) {\n            object.scale.z = Math.round(object.scale.z / this.scaleSnap) * this.scaleSnap || this.scaleSnap;\n          }\n        }\n      } else if (mode === \"rotate\") {\n        this.offset.copy(this.pointEnd).sub(this.pointStart);\n        const ROTATION_SPEED = 20 / this.worldPosition.distanceTo(this.tempVector.setFromMatrixPosition(this.camera.matrixWorld));\n        if (axis === \"E\") {\n          this.rotationAxis.copy(this.eye);\n          this.rotationAngle = this.pointEnd.angleTo(this.pointStart);\n          this.startNorm.copy(this.pointStart).normalize();\n          this.endNorm.copy(this.pointEnd).normalize();\n          this.rotationAngle *= this.endNorm.cross(this.startNorm).dot(this.eye) < 0 ? 1 : -1;\n        } else if (axis === \"XYZE\") {\n          this.rotationAxis.copy(this.offset).cross(this.eye).normalize();\n          this.rotationAngle = this.offset.dot(this.tempVector.copy(this.rotationAxis).cross(this.eye)) * ROTATION_SPEED;\n        } else if (axis === \"X\" || axis === \"Y\" || axis === \"Z\") {\n          this.rotationAxis.copy(this.unit[axis]);\n          this.tempVector.copy(this.unit[axis]);\n          if (space === \"local\") {\n            this.tempVector.applyQuaternion(this.worldQuaternion);\n          }\n          this.rotationAngle = this.offset.dot(this.tempVector.cross(this.eye).normalize()) * ROTATION_SPEED;\n        }\n        if (this.rotationSnap) {\n          this.rotationAngle = Math.round(this.rotationAngle / this.rotationSnap) * this.rotationSnap;\n        }\n        if (space === \"local\" && axis !== \"E\" && axis !== \"XYZE\") {\n          object.quaternion.copy(this.quaternionStart);\n          object.quaternion.multiply(this.tempQuaternion.setFromAxisAngle(this.rotationAxis, this.rotationAngle)).normalize();\n        } else {\n          this.rotationAxis.applyQuaternion(this.parentQuaternionInv);\n          object.quaternion.copy(this.tempQuaternion.setFromAxisAngle(this.rotationAxis, this.rotationAngle));\n          object.quaternion.multiply(this.quaternionStart).normalize();\n        }\n      }\n      this.dispatchEvent(this.changeEvent);\n      this.dispatchEvent(this.objectChangeEvent);\n    });\n    __publicField(this, \"pointerUp\", pointer => {\n      if (pointer.button !== 0) return;\n      if (this.dragging && this.axis !== null) {\n        this.mouseUpEvent.mode = this.mode;\n        this.dispatchEvent(this.mouseUpEvent);\n      }\n      this.dragging = false;\n      this.axis = null;\n    });\n    __publicField(this, \"getPointer\", event => {\n      var _a;\n      if (this.domElement && ((_a = this.domElement.ownerDocument) == null ? void 0 : _a.pointerLockElement)) {\n        return {\n          x: 0,\n          y: 0,\n          button: event.button\n        };\n      } else {\n        const pointer = event.changedTouches ? event.changedTouches[0] : event;\n        const rect = this.domElement.getBoundingClientRect();\n        return {\n          x: (pointer.clientX - rect.left) / rect.width * 2 - 1,\n          y: -(pointer.clientY - rect.top) / rect.height * 2 + 1,\n          button: event.button\n        };\n      }\n    });\n    __publicField(this, \"onPointerHover\", event => {\n      if (!this.enabled) return;\n      switch (event.pointerType) {\n        case \"mouse\":\n        case \"pen\":\n          this.pointerHover(this.getPointer(event));\n          break;\n      }\n    });\n    __publicField(this, \"onPointerDown\", event => {\n      if (!this.enabled || !this.domElement) return;\n      this.domElement.style.touchAction = \"none\";\n      this.domElement.ownerDocument.addEventListener(\"pointermove\", this.onPointerMove);\n      this.pointerHover(this.getPointer(event));\n      this.pointerDown(this.getPointer(event));\n    });\n    __publicField(this, \"onPointerMove\", event => {\n      if (!this.enabled) return;\n      this.pointerMove(this.getPointer(event));\n    });\n    __publicField(this, \"onPointerUp\", event => {\n      if (!this.enabled || !this.domElement) return;\n      this.domElement.style.touchAction = \"\";\n      this.domElement.ownerDocument.removeEventListener(\"pointermove\", this.onPointerMove);\n      this.pointerUp(this.getPointer(event));\n    });\n    __publicField(this, \"getMode\", () => this.mode);\n    __publicField(this, \"setMode\", mode => {\n      this.mode = mode;\n    });\n    __publicField(this, \"setTranslationSnap\", translationSnap => {\n      this.translationSnap = translationSnap;\n    });\n    __publicField(this, \"setRotationSnap\", rotationSnap => {\n      this.rotationSnap = rotationSnap;\n    });\n    __publicField(this, \"setScaleSnap\", scaleSnap => {\n      this.scaleSnap = scaleSnap;\n    });\n    __publicField(this, \"setSize\", size => {\n      this.size = size;\n    });\n    __publicField(this, \"setSpace\", space => {\n      this.space = space;\n    });\n    __publicField(this, \"update\", () => {\n      console.warn(\"THREE.TransformControls: update function has no more functionality and therefore has been deprecated.\");\n    });\n    __publicField(this, \"connect\", domElement => {\n      if (domElement === document) {\n        console.error('THREE.OrbitControls: \"document\" should not be used as the target \"domElement\". Please use \"renderer.domElement\" instead.');\n      }\n      this.domElement = domElement;\n      this.domElement.addEventListener(\"pointerdown\", this.onPointerDown);\n      this.domElement.addEventListener(\"pointermove\", this.onPointerHover);\n      this.domElement.ownerDocument.addEventListener(\"pointerup\", this.onPointerUp);\n    });\n    __publicField(this, \"dispose\", () => {\n      var _a, _b, _c, _d, _e, _f;\n      (_a = this.domElement) == null ? void 0 : _a.removeEventListener(\"pointerdown\", this.onPointerDown);\n      (_b = this.domElement) == null ? void 0 : _b.removeEventListener(\"pointermove\", this.onPointerHover);\n      (_d = (_c = this.domElement) == null ? void 0 : _c.ownerDocument) == null ? void 0 : _d.removeEventListener(\"pointermove\", this.onPointerMove);\n      (_f = (_e = this.domElement) == null ? void 0 : _e.ownerDocument) == null ? void 0 : _f.removeEventListener(\"pointerup\", this.onPointerUp);\n      this.traverse(child => {\n        const mesh = child;\n        if (mesh.geometry) {\n          mesh.geometry.dispose();\n        }\n        if (mesh.material) {\n          mesh.material.dispose();\n        }\n      });\n    });\n    this.domElement = domElement;\n    this.camera = camera;\n    this.gizmo = new TransformControlsGizmo();\n    this.add(this.gizmo);\n    this.plane = new TransformControlsPlane();\n    this.add(this.plane);\n    const defineProperty = (propName, defaultValue) => {\n      let propValue = defaultValue;\n      Object.defineProperty(this, propName, {\n        get: function () {\n          return propValue !== void 0 ? propValue : defaultValue;\n        },\n        set: function (value) {\n          if (propValue !== value) {\n            propValue = value;\n            this.plane[propName] = value;\n            this.gizmo[propName] = value;\n            this.dispatchEvent({\n              type: propName + \"-changed\",\n              value\n            });\n            this.dispatchEvent(this.changeEvent);\n          }\n        }\n      });\n      this[propName] = defaultValue;\n      this.plane[propName] = defaultValue;\n      this.gizmo[propName] = defaultValue;\n    };\n    defineProperty(\"camera\", this.camera);\n    defineProperty(\"object\", this.object);\n    defineProperty(\"enabled\", this.enabled);\n    defineProperty(\"axis\", this.axis);\n    defineProperty(\"mode\", this.mode);\n    defineProperty(\"translationSnap\", this.translationSnap);\n    defineProperty(\"rotationSnap\", this.rotationSnap);\n    defineProperty(\"scaleSnap\", this.scaleSnap);\n    defineProperty(\"space\", this.space);\n    defineProperty(\"size\", this.size);\n    defineProperty(\"dragging\", this.dragging);\n    defineProperty(\"showX\", this.showX);\n    defineProperty(\"showY\", this.showY);\n    defineProperty(\"showZ\", this.showZ);\n    defineProperty(\"worldPosition\", this.worldPosition);\n    defineProperty(\"worldPositionStart\", this.worldPositionStart);\n    defineProperty(\"worldQuaternion\", this.worldQuaternion);\n    defineProperty(\"worldQuaternionStart\", this.worldQuaternionStart);\n    defineProperty(\"cameraPosition\", this.cameraPosition);\n    defineProperty(\"cameraQuaternion\", this.cameraQuaternion);\n    defineProperty(\"pointStart\", this.pointStart);\n    defineProperty(\"pointEnd\", this.pointEnd);\n    defineProperty(\"rotationAxis\", this.rotationAxis);\n    defineProperty(\"rotationAngle\", this.rotationAngle);\n    defineProperty(\"eye\", this.eye);\n    if (domElement !== void 0) this.connect(domElement);\n  }\n}\nclass TransformControlsGizmo extends Object3D {\n  constructor() {\n    super();\n    __publicField(this, \"isTransformControlsGizmo\", true);\n    __publicField(this, \"type\", \"TransformControlsGizmo\");\n    __publicField(this, \"tempVector\", new Vector3(0, 0, 0));\n    __publicField(this, \"tempEuler\", new Euler());\n    __publicField(this, \"alignVector\", new Vector3(0, 1, 0));\n    __publicField(this, \"zeroVector\", new Vector3(0, 0, 0));\n    __publicField(this, \"lookAtMatrix\", new Matrix4());\n    __publicField(this, \"tempQuaternion\", new Quaternion());\n    __publicField(this, \"tempQuaternion2\", new Quaternion());\n    __publicField(this, \"identityQuaternion\", new Quaternion());\n    __publicField(this, \"unitX\", new Vector3(1, 0, 0));\n    __publicField(this, \"unitY\", new Vector3(0, 1, 0));\n    __publicField(this, \"unitZ\", new Vector3(0, 0, 1));\n    __publicField(this, \"gizmo\");\n    __publicField(this, \"picker\");\n    __publicField(this, \"helper\");\n    // these are set from parent class TransformControls\n    __publicField(this, \"rotationAxis\", new Vector3());\n    __publicField(this, \"cameraPosition\", new Vector3());\n    __publicField(this, \"worldPositionStart\", new Vector3());\n    __publicField(this, \"worldQuaternionStart\", new Quaternion());\n    __publicField(this, \"worldPosition\", new Vector3());\n    __publicField(this, \"worldQuaternion\", new Quaternion());\n    __publicField(this, \"eye\", new Vector3());\n    __publicField(this, \"camera\", null);\n    __publicField(this, \"enabled\", true);\n    __publicField(this, \"axis\", null);\n    __publicField(this, \"mode\", \"translate\");\n    __publicField(this, \"space\", \"world\");\n    __publicField(this, \"size\", 1);\n    __publicField(this, \"dragging\", false);\n    __publicField(this, \"showX\", true);\n    __publicField(this, \"showY\", true);\n    __publicField(this, \"showZ\", true);\n    // updateMatrixWorld will update transformations and appearance of individual handles\n    __publicField(this, \"updateMatrixWorld\", () => {\n      let space = this.space;\n      if (this.mode === \"scale\") {\n        space = \"local\";\n      }\n      const quaternion = space === \"local\" ? this.worldQuaternion : this.identityQuaternion;\n      this.gizmo[\"translate\"].visible = this.mode === \"translate\";\n      this.gizmo[\"rotate\"].visible = this.mode === \"rotate\";\n      this.gizmo[\"scale\"].visible = this.mode === \"scale\";\n      this.helper[\"translate\"].visible = this.mode === \"translate\";\n      this.helper[\"rotate\"].visible = this.mode === \"rotate\";\n      this.helper[\"scale\"].visible = this.mode === \"scale\";\n      let handles = [];\n      handles = handles.concat(this.picker[this.mode].children);\n      handles = handles.concat(this.gizmo[this.mode].children);\n      handles = handles.concat(this.helper[this.mode].children);\n      for (let i = 0; i < handles.length; i++) {\n        const handle = handles[i];\n        handle.visible = true;\n        handle.rotation.set(0, 0, 0);\n        handle.position.copy(this.worldPosition);\n        let factor;\n        if (this.camera.isOrthographicCamera) {\n          factor = (this.camera.top - this.camera.bottom) / this.camera.zoom;\n        } else {\n          factor = this.worldPosition.distanceTo(this.cameraPosition) * Math.min(1.9 * Math.tan(Math.PI * this.camera.fov / 360) / this.camera.zoom, 7);\n        }\n        handle.scale.set(1, 1, 1).multiplyScalar(factor * this.size / 7);\n        if (handle.tag === \"helper\") {\n          handle.visible = false;\n          if (handle.name === \"AXIS\") {\n            handle.position.copy(this.worldPositionStart);\n            handle.visible = !!this.axis;\n            if (this.axis === \"X\") {\n              this.tempQuaternion.setFromEuler(this.tempEuler.set(0, 0, 0));\n              handle.quaternion.copy(quaternion).multiply(this.tempQuaternion);\n              if (Math.abs(this.alignVector.copy(this.unitX).applyQuaternion(quaternion).dot(this.eye)) > 0.9) {\n                handle.visible = false;\n              }\n            }\n            if (this.axis === \"Y\") {\n              this.tempQuaternion.setFromEuler(this.tempEuler.set(0, 0, Math.PI / 2));\n              handle.quaternion.copy(quaternion).multiply(this.tempQuaternion);\n              if (Math.abs(this.alignVector.copy(this.unitY).applyQuaternion(quaternion).dot(this.eye)) > 0.9) {\n                handle.visible = false;\n              }\n            }\n            if (this.axis === \"Z\") {\n              this.tempQuaternion.setFromEuler(this.tempEuler.set(0, Math.PI / 2, 0));\n              handle.quaternion.copy(quaternion).multiply(this.tempQuaternion);\n              if (Math.abs(this.alignVector.copy(this.unitZ).applyQuaternion(quaternion).dot(this.eye)) > 0.9) {\n                handle.visible = false;\n              }\n            }\n            if (this.axis === \"XYZE\") {\n              this.tempQuaternion.setFromEuler(this.tempEuler.set(0, Math.PI / 2, 0));\n              this.alignVector.copy(this.rotationAxis);\n              handle.quaternion.setFromRotationMatrix(this.lookAtMatrix.lookAt(this.zeroVector, this.alignVector, this.unitY));\n              handle.quaternion.multiply(this.tempQuaternion);\n              handle.visible = this.dragging;\n            }\n            if (this.axis === \"E\") {\n              handle.visible = false;\n            }\n          } else if (handle.name === \"START\") {\n            handle.position.copy(this.worldPositionStart);\n            handle.visible = this.dragging;\n          } else if (handle.name === \"END\") {\n            handle.position.copy(this.worldPosition);\n            handle.visible = this.dragging;\n          } else if (handle.name === \"DELTA\") {\n            handle.position.copy(this.worldPositionStart);\n            handle.quaternion.copy(this.worldQuaternionStart);\n            this.tempVector.set(1e-10, 1e-10, 1e-10).add(this.worldPositionStart).sub(this.worldPosition).multiplyScalar(-1);\n            this.tempVector.applyQuaternion(this.worldQuaternionStart.clone().invert());\n            handle.scale.copy(this.tempVector);\n            handle.visible = this.dragging;\n          } else {\n            handle.quaternion.copy(quaternion);\n            if (this.dragging) {\n              handle.position.copy(this.worldPositionStart);\n            } else {\n              handle.position.copy(this.worldPosition);\n            }\n            if (this.axis) {\n              handle.visible = this.axis.search(handle.name) !== -1;\n            }\n          }\n          continue;\n        }\n        handle.quaternion.copy(quaternion);\n        if (this.mode === \"translate\" || this.mode === \"scale\") {\n          const AXIS_HIDE_TRESHOLD = 0.99;\n          const PLANE_HIDE_TRESHOLD = 0.2;\n          const AXIS_FLIP_TRESHOLD = 0;\n          if (handle.name === \"X\" || handle.name === \"XYZX\") {\n            if (Math.abs(this.alignVector.copy(this.unitX).applyQuaternion(quaternion).dot(this.eye)) > AXIS_HIDE_TRESHOLD) {\n              handle.scale.set(1e-10, 1e-10, 1e-10);\n              handle.visible = false;\n            }\n          }\n          if (handle.name === \"Y\" || handle.name === \"XYZY\") {\n            if (Math.abs(this.alignVector.copy(this.unitY).applyQuaternion(quaternion).dot(this.eye)) > AXIS_HIDE_TRESHOLD) {\n              handle.scale.set(1e-10, 1e-10, 1e-10);\n              handle.visible = false;\n            }\n          }\n          if (handle.name === \"Z\" || handle.name === \"XYZZ\") {\n            if (Math.abs(this.alignVector.copy(this.unitZ).applyQuaternion(quaternion).dot(this.eye)) > AXIS_HIDE_TRESHOLD) {\n              handle.scale.set(1e-10, 1e-10, 1e-10);\n              handle.visible = false;\n            }\n          }\n          if (handle.name === \"XY\") {\n            if (Math.abs(this.alignVector.copy(this.unitZ).applyQuaternion(quaternion).dot(this.eye)) < PLANE_HIDE_TRESHOLD) {\n              handle.scale.set(1e-10, 1e-10, 1e-10);\n              handle.visible = false;\n            }\n          }\n          if (handle.name === \"YZ\") {\n            if (Math.abs(this.alignVector.copy(this.unitX).applyQuaternion(quaternion).dot(this.eye)) < PLANE_HIDE_TRESHOLD) {\n              handle.scale.set(1e-10, 1e-10, 1e-10);\n              handle.visible = false;\n            }\n          }\n          if (handle.name === \"XZ\") {\n            if (Math.abs(this.alignVector.copy(this.unitY).applyQuaternion(quaternion).dot(this.eye)) < PLANE_HIDE_TRESHOLD) {\n              handle.scale.set(1e-10, 1e-10, 1e-10);\n              handle.visible = false;\n            }\n          }\n          if (handle.name.search(\"X\") !== -1) {\n            if (this.alignVector.copy(this.unitX).applyQuaternion(quaternion).dot(this.eye) < AXIS_FLIP_TRESHOLD) {\n              if (handle.tag === \"fwd\") {\n                handle.visible = false;\n              } else {\n                handle.scale.x *= -1;\n              }\n            } else if (handle.tag === \"bwd\") {\n              handle.visible = false;\n            }\n          }\n          if (handle.name.search(\"Y\") !== -1) {\n            if (this.alignVector.copy(this.unitY).applyQuaternion(quaternion).dot(this.eye) < AXIS_FLIP_TRESHOLD) {\n              if (handle.tag === \"fwd\") {\n                handle.visible = false;\n              } else {\n                handle.scale.y *= -1;\n              }\n            } else if (handle.tag === \"bwd\") {\n              handle.visible = false;\n            }\n          }\n          if (handle.name.search(\"Z\") !== -1) {\n            if (this.alignVector.copy(this.unitZ).applyQuaternion(quaternion).dot(this.eye) < AXIS_FLIP_TRESHOLD) {\n              if (handle.tag === \"fwd\") {\n                handle.visible = false;\n              } else {\n                handle.scale.z *= -1;\n              }\n            } else if (handle.tag === \"bwd\") {\n              handle.visible = false;\n            }\n          }\n        } else if (this.mode === \"rotate\") {\n          this.tempQuaternion2.copy(quaternion);\n          this.alignVector.copy(this.eye).applyQuaternion(this.tempQuaternion.copy(quaternion).invert());\n          if (handle.name.search(\"E\") !== -1) {\n            handle.quaternion.setFromRotationMatrix(this.lookAtMatrix.lookAt(this.eye, this.zeroVector, this.unitY));\n          }\n          if (handle.name === \"X\") {\n            this.tempQuaternion.setFromAxisAngle(this.unitX, Math.atan2(-this.alignVector.y, this.alignVector.z));\n            this.tempQuaternion.multiplyQuaternions(this.tempQuaternion2, this.tempQuaternion);\n            handle.quaternion.copy(this.tempQuaternion);\n          }\n          if (handle.name === \"Y\") {\n            this.tempQuaternion.setFromAxisAngle(this.unitY, Math.atan2(this.alignVector.x, this.alignVector.z));\n            this.tempQuaternion.multiplyQuaternions(this.tempQuaternion2, this.tempQuaternion);\n            handle.quaternion.copy(this.tempQuaternion);\n          }\n          if (handle.name === \"Z\") {\n            this.tempQuaternion.setFromAxisAngle(this.unitZ, Math.atan2(this.alignVector.y, this.alignVector.x));\n            this.tempQuaternion.multiplyQuaternions(this.tempQuaternion2, this.tempQuaternion);\n            handle.quaternion.copy(this.tempQuaternion);\n          }\n        }\n        handle.visible = handle.visible && (handle.name.indexOf(\"X\") === -1 || this.showX);\n        handle.visible = handle.visible && (handle.name.indexOf(\"Y\") === -1 || this.showY);\n        handle.visible = handle.visible && (handle.name.indexOf(\"Z\") === -1 || this.showZ);\n        handle.visible = handle.visible && (handle.name.indexOf(\"E\") === -1 || this.showX && this.showY && this.showZ);\n        handle.material.tempOpacity = handle.material.tempOpacity || handle.material.opacity;\n        handle.material.tempColor = handle.material.tempColor || handle.material.color.clone();\n        handle.material.color.copy(handle.material.tempColor);\n        handle.material.opacity = handle.material.tempOpacity;\n        if (!this.enabled) {\n          handle.material.opacity *= 0.5;\n          handle.material.color.lerp(new Color(1, 1, 1), 0.5);\n        } else if (this.axis) {\n          if (handle.name === this.axis) {\n            handle.material.opacity = 1;\n            handle.material.color.lerp(new Color(1, 1, 1), 0.5);\n          } else if (this.axis.split(\"\").some(function (a) {\n            return handle.name === a;\n          })) {\n            handle.material.opacity = 1;\n            handle.material.color.lerp(new Color(1, 1, 1), 0.5);\n          } else {\n            handle.material.opacity *= 0.25;\n            handle.material.color.lerp(new Color(1, 1, 1), 0.5);\n          }\n        }\n      }\n      super.updateMatrixWorld();\n    });\n    const gizmoMaterial = new MeshBasicMaterial({\n      depthTest: false,\n      depthWrite: false,\n      transparent: true,\n      side: DoubleSide,\n      fog: false,\n      toneMapped: false\n    });\n    const gizmoLineMaterial = new LineBasicMaterial({\n      depthTest: false,\n      depthWrite: false,\n      transparent: true,\n      linewidth: 1,\n      fog: false,\n      toneMapped: false\n    });\n    const matInvisible = gizmoMaterial.clone();\n    matInvisible.opacity = 0.15;\n    const matHelper = gizmoMaterial.clone();\n    matHelper.opacity = 0.33;\n    const matRed = gizmoMaterial.clone();\n    matRed.color.set(16711680);\n    const matGreen = gizmoMaterial.clone();\n    matGreen.color.set(65280);\n    const matBlue = gizmoMaterial.clone();\n    matBlue.color.set(255);\n    const matWhiteTransparent = gizmoMaterial.clone();\n    matWhiteTransparent.opacity = 0.25;\n    const matYellowTransparent = matWhiteTransparent.clone();\n    matYellowTransparent.color.set(16776960);\n    const matCyanTransparent = matWhiteTransparent.clone();\n    matCyanTransparent.color.set(65535);\n    const matMagentaTransparent = matWhiteTransparent.clone();\n    matMagentaTransparent.color.set(16711935);\n    const matYellow = gizmoMaterial.clone();\n    matYellow.color.set(16776960);\n    const matLineRed = gizmoLineMaterial.clone();\n    matLineRed.color.set(16711680);\n    const matLineGreen = gizmoLineMaterial.clone();\n    matLineGreen.color.set(65280);\n    const matLineBlue = gizmoLineMaterial.clone();\n    matLineBlue.color.set(255);\n    const matLineCyan = gizmoLineMaterial.clone();\n    matLineCyan.color.set(65535);\n    const matLineMagenta = gizmoLineMaterial.clone();\n    matLineMagenta.color.set(16711935);\n    const matLineYellow = gizmoLineMaterial.clone();\n    matLineYellow.color.set(16776960);\n    const matLineGray = gizmoLineMaterial.clone();\n    matLineGray.color.set(7895160);\n    const matLineYellowTransparent = matLineYellow.clone();\n    matLineYellowTransparent.opacity = 0.25;\n    const arrowGeometry = new CylinderGeometry(0, 0.05, 0.2, 12, 1, false);\n    const scaleHandleGeometry = new BoxGeometry(0.125, 0.125, 0.125);\n    const lineGeometry = new BufferGeometry();\n    lineGeometry.setAttribute(\"position\", new Float32BufferAttribute([0, 0, 0, 1, 0, 0], 3));\n    const CircleGeometry = (radius, arc) => {\n      const geometry = new BufferGeometry();\n      const vertices = [];\n      for (let i = 0; i <= 64 * arc; ++i) {\n        vertices.push(0, Math.cos(i / 32 * Math.PI) * radius, Math.sin(i / 32 * Math.PI) * radius);\n      }\n      geometry.setAttribute(\"position\", new Float32BufferAttribute(vertices, 3));\n      return geometry;\n    };\n    const TranslateHelperGeometry = () => {\n      const geometry = new BufferGeometry();\n      geometry.setAttribute(\"position\", new Float32BufferAttribute([0, 0, 0, 1, 1, 1], 3));\n      return geometry;\n    };\n    const gizmoTranslate = {\n      X: [[new Mesh(arrowGeometry, matRed), [1, 0, 0], [0, 0, -Math.PI / 2], null, \"fwd\"], [new Mesh(arrowGeometry, matRed), [1, 0, 0], [0, 0, Math.PI / 2], null, \"bwd\"], [new Line(lineGeometry, matLineRed)]],\n      Y: [[new Mesh(arrowGeometry, matGreen), [0, 1, 0], null, null, \"fwd\"], [new Mesh(arrowGeometry, matGreen), [0, 1, 0], [Math.PI, 0, 0], null, \"bwd\"], [new Line(lineGeometry, matLineGreen), null, [0, 0, Math.PI / 2]]],\n      Z: [[new Mesh(arrowGeometry, matBlue), [0, 0, 1], [Math.PI / 2, 0, 0], null, \"fwd\"], [new Mesh(arrowGeometry, matBlue), [0, 0, 1], [-Math.PI / 2, 0, 0], null, \"bwd\"], [new Line(lineGeometry, matLineBlue), null, [0, -Math.PI / 2, 0]]],\n      XYZ: [[new Mesh(new OctahedronGeometry(0.1, 0), matWhiteTransparent.clone()), [0, 0, 0], [0, 0, 0]]],\n      XY: [[new Mesh(new PlaneGeometry(0.295, 0.295), matYellowTransparent.clone()), [0.15, 0.15, 0]], [new Line(lineGeometry, matLineYellow), [0.18, 0.3, 0], null, [0.125, 1, 1]], [new Line(lineGeometry, matLineYellow), [0.3, 0.18, 0], [0, 0, Math.PI / 2], [0.125, 1, 1]]],\n      YZ: [[new Mesh(new PlaneGeometry(0.295, 0.295), matCyanTransparent.clone()), [0, 0.15, 0.15], [0, Math.PI / 2, 0]], [new Line(lineGeometry, matLineCyan), [0, 0.18, 0.3], [0, 0, Math.PI / 2], [0.125, 1, 1]], [new Line(lineGeometry, matLineCyan), [0, 0.3, 0.18], [0, -Math.PI / 2, 0], [0.125, 1, 1]]],\n      XZ: [[new Mesh(new PlaneGeometry(0.295, 0.295), matMagentaTransparent.clone()), [0.15, 0, 0.15], [-Math.PI / 2, 0, 0]], [new Line(lineGeometry, matLineMagenta), [0.18, 0, 0.3], null, [0.125, 1, 1]], [new Line(lineGeometry, matLineMagenta), [0.3, 0, 0.18], [0, -Math.PI / 2, 0], [0.125, 1, 1]]]\n    };\n    const pickerTranslate = {\n      X: [[new Mesh(new CylinderGeometry(0.2, 0, 1, 4, 1, false), matInvisible), [0.6, 0, 0], [0, 0, -Math.PI / 2]]],\n      Y: [[new Mesh(new CylinderGeometry(0.2, 0, 1, 4, 1, false), matInvisible), [0, 0.6, 0]]],\n      Z: [[new Mesh(new CylinderGeometry(0.2, 0, 1, 4, 1, false), matInvisible), [0, 0, 0.6], [Math.PI / 2, 0, 0]]],\n      XYZ: [[new Mesh(new OctahedronGeometry(0.2, 0), matInvisible)]],\n      XY: [[new Mesh(new PlaneGeometry(0.4, 0.4), matInvisible), [0.2, 0.2, 0]]],\n      YZ: [[new Mesh(new PlaneGeometry(0.4, 0.4), matInvisible), [0, 0.2, 0.2], [0, Math.PI / 2, 0]]],\n      XZ: [[new Mesh(new PlaneGeometry(0.4, 0.4), matInvisible), [0.2, 0, 0.2], [-Math.PI / 2, 0, 0]]]\n    };\n    const helperTranslate = {\n      START: [[new Mesh(new OctahedronGeometry(0.01, 2), matHelper), null, null, null, \"helper\"]],\n      END: [[new Mesh(new OctahedronGeometry(0.01, 2), matHelper), null, null, null, \"helper\"]],\n      DELTA: [[new Line(TranslateHelperGeometry(), matHelper), null, null, null, \"helper\"]],\n      X: [[new Line(lineGeometry, matHelper.clone()), [-1e3, 0, 0], null, [1e6, 1, 1], \"helper\"]],\n      Y: [[new Line(lineGeometry, matHelper.clone()), [0, -1e3, 0], [0, 0, Math.PI / 2], [1e6, 1, 1], \"helper\"]],\n      Z: [[new Line(lineGeometry, matHelper.clone()), [0, 0, -1e3], [0, -Math.PI / 2, 0], [1e6, 1, 1], \"helper\"]]\n    };\n    const gizmoRotate = {\n      X: [[new Line(CircleGeometry(1, 0.5), matLineRed)], [new Mesh(new OctahedronGeometry(0.04, 0), matRed), [0, 0, 0.99], null, [1, 3, 1]]],\n      Y: [[new Line(CircleGeometry(1, 0.5), matLineGreen), null, [0, 0, -Math.PI / 2]], [new Mesh(new OctahedronGeometry(0.04, 0), matGreen), [0, 0, 0.99], null, [3, 1, 1]]],\n      Z: [[new Line(CircleGeometry(1, 0.5), matLineBlue), null, [0, Math.PI / 2, 0]], [new Mesh(new OctahedronGeometry(0.04, 0), matBlue), [0.99, 0, 0], null, [1, 3, 1]]],\n      E: [[new Line(CircleGeometry(1.25, 1), matLineYellowTransparent), null, [0, Math.PI / 2, 0]], [new Mesh(new CylinderGeometry(0.03, 0, 0.15, 4, 1, false), matLineYellowTransparent), [1.17, 0, 0], [0, 0, -Math.PI / 2], [1, 1, 1e-3]], [new Mesh(new CylinderGeometry(0.03, 0, 0.15, 4, 1, false), matLineYellowTransparent), [-1.17, 0, 0], [0, 0, Math.PI / 2], [1, 1, 1e-3]], [new Mesh(new CylinderGeometry(0.03, 0, 0.15, 4, 1, false), matLineYellowTransparent), [0, -1.17, 0], [Math.PI, 0, 0], [1, 1, 1e-3]], [new Mesh(new CylinderGeometry(0.03, 0, 0.15, 4, 1, false), matLineYellowTransparent), [0, 1.17, 0], [0, 0, 0], [1, 1, 1e-3]]],\n      XYZE: [[new Line(CircleGeometry(1, 1), matLineGray), null, [0, Math.PI / 2, 0]]]\n    };\n    const helperRotate = {\n      AXIS: [[new Line(lineGeometry, matHelper.clone()), [-1e3, 0, 0], null, [1e6, 1, 1], \"helper\"]]\n    };\n    const pickerRotate = {\n      X: [[new Mesh(new TorusGeometry(1, 0.1, 4, 24), matInvisible), [0, 0, 0], [0, -Math.PI / 2, -Math.PI / 2]]],\n      Y: [[new Mesh(new TorusGeometry(1, 0.1, 4, 24), matInvisible), [0, 0, 0], [Math.PI / 2, 0, 0]]],\n      Z: [[new Mesh(new TorusGeometry(1, 0.1, 4, 24), matInvisible), [0, 0, 0], [0, 0, -Math.PI / 2]]],\n      E: [[new Mesh(new TorusGeometry(1.25, 0.1, 2, 24), matInvisible)]],\n      XYZE: [[new Mesh(new SphereGeometry(0.7, 10, 8), matInvisible)]]\n    };\n    const gizmoScale = {\n      X: [[new Mesh(scaleHandleGeometry, matRed), [0.8, 0, 0], [0, 0, -Math.PI / 2]], [new Line(lineGeometry, matLineRed), null, null, [0.8, 1, 1]]],\n      Y: [[new Mesh(scaleHandleGeometry, matGreen), [0, 0.8, 0]], [new Line(lineGeometry, matLineGreen), null, [0, 0, Math.PI / 2], [0.8, 1, 1]]],\n      Z: [[new Mesh(scaleHandleGeometry, matBlue), [0, 0, 0.8], [Math.PI / 2, 0, 0]], [new Line(lineGeometry, matLineBlue), null, [0, -Math.PI / 2, 0], [0.8, 1, 1]]],\n      XY: [[new Mesh(scaleHandleGeometry, matYellowTransparent), [0.85, 0.85, 0], null, [2, 2, 0.2]], [new Line(lineGeometry, matLineYellow), [0.855, 0.98, 0], null, [0.125, 1, 1]], [new Line(lineGeometry, matLineYellow), [0.98, 0.855, 0], [0, 0, Math.PI / 2], [0.125, 1, 1]]],\n      YZ: [[new Mesh(scaleHandleGeometry, matCyanTransparent), [0, 0.85, 0.85], null, [0.2, 2, 2]], [new Line(lineGeometry, matLineCyan), [0, 0.855, 0.98], [0, 0, Math.PI / 2], [0.125, 1, 1]], [new Line(lineGeometry, matLineCyan), [0, 0.98, 0.855], [0, -Math.PI / 2, 0], [0.125, 1, 1]]],\n      XZ: [[new Mesh(scaleHandleGeometry, matMagentaTransparent), [0.85, 0, 0.85], null, [2, 0.2, 2]], [new Line(lineGeometry, matLineMagenta), [0.855, 0, 0.98], null, [0.125, 1, 1]], [new Line(lineGeometry, matLineMagenta), [0.98, 0, 0.855], [0, -Math.PI / 2, 0], [0.125, 1, 1]]],\n      XYZX: [[new Mesh(new BoxGeometry(0.125, 0.125, 0.125), matWhiteTransparent.clone()), [1.1, 0, 0]]],\n      XYZY: [[new Mesh(new BoxGeometry(0.125, 0.125, 0.125), matWhiteTransparent.clone()), [0, 1.1, 0]]],\n      XYZZ: [[new Mesh(new BoxGeometry(0.125, 0.125, 0.125), matWhiteTransparent.clone()), [0, 0, 1.1]]]\n    };\n    const pickerScale = {\n      X: [[new Mesh(new CylinderGeometry(0.2, 0, 0.8, 4, 1, false), matInvisible), [0.5, 0, 0], [0, 0, -Math.PI / 2]]],\n      Y: [[new Mesh(new CylinderGeometry(0.2, 0, 0.8, 4, 1, false), matInvisible), [0, 0.5, 0]]],\n      Z: [[new Mesh(new CylinderGeometry(0.2, 0, 0.8, 4, 1, false), matInvisible), [0, 0, 0.5], [Math.PI / 2, 0, 0]]],\n      XY: [[new Mesh(scaleHandleGeometry, matInvisible), [0.85, 0.85, 0], null, [3, 3, 0.2]]],\n      YZ: [[new Mesh(scaleHandleGeometry, matInvisible), [0, 0.85, 0.85], null, [0.2, 3, 3]]],\n      XZ: [[new Mesh(scaleHandleGeometry, matInvisible), [0.85, 0, 0.85], null, [3, 0.2, 3]]],\n      XYZX: [[new Mesh(new BoxGeometry(0.2, 0.2, 0.2), matInvisible), [1.1, 0, 0]]],\n      XYZY: [[new Mesh(new BoxGeometry(0.2, 0.2, 0.2), matInvisible), [0, 1.1, 0]]],\n      XYZZ: [[new Mesh(new BoxGeometry(0.2, 0.2, 0.2), matInvisible), [0, 0, 1.1]]]\n    };\n    const helperScale = {\n      X: [[new Line(lineGeometry, matHelper.clone()), [-1e3, 0, 0], null, [1e6, 1, 1], \"helper\"]],\n      Y: [[new Line(lineGeometry, matHelper.clone()), [0, -1e3, 0], [0, 0, Math.PI / 2], [1e6, 1, 1], \"helper\"]],\n      Z: [[new Line(lineGeometry, matHelper.clone()), [0, 0, -1e3], [0, -Math.PI / 2, 0], [1e6, 1, 1], \"helper\"]]\n    };\n    const setupGizmo = gizmoMap => {\n      const gizmo = new Object3D();\n      for (let name in gizmoMap) {\n        for (let i = gizmoMap[name].length; i--;) {\n          const object = gizmoMap[name][i][0].clone();\n          const position = gizmoMap[name][i][1];\n          const rotation = gizmoMap[name][i][2];\n          const scale = gizmoMap[name][i][3];\n          const tag = gizmoMap[name][i][4];\n          object.name = name;\n          object.tag = tag;\n          if (position) {\n            object.position.set(position[0], position[1], position[2]);\n          }\n          if (rotation) {\n            object.rotation.set(rotation[0], rotation[1], rotation[2]);\n          }\n          if (scale) {\n            object.scale.set(scale[0], scale[1], scale[2]);\n          }\n          object.updateMatrix();\n          const tempGeometry = object.geometry.clone();\n          tempGeometry.applyMatrix4(object.matrix);\n          object.geometry = tempGeometry;\n          object.renderOrder = Infinity;\n          object.position.set(0, 0, 0);\n          object.rotation.set(0, 0, 0);\n          object.scale.set(1, 1, 1);\n          gizmo.add(object);\n        }\n      }\n      return gizmo;\n    };\n    this.gizmo = {};\n    this.picker = {};\n    this.helper = {};\n    this.add(this.gizmo[\"translate\"] = setupGizmo(gizmoTranslate));\n    this.add(this.gizmo[\"rotate\"] = setupGizmo(gizmoRotate));\n    this.add(this.gizmo[\"scale\"] = setupGizmo(gizmoScale));\n    this.add(this.picker[\"translate\"] = setupGizmo(pickerTranslate));\n    this.add(this.picker[\"rotate\"] = setupGizmo(pickerRotate));\n    this.add(this.picker[\"scale\"] = setupGizmo(pickerScale));\n    this.add(this.helper[\"translate\"] = setupGizmo(helperTranslate));\n    this.add(this.helper[\"rotate\"] = setupGizmo(helperRotate));\n    this.add(this.helper[\"scale\"] = setupGizmo(helperScale));\n    this.picker[\"translate\"].visible = false;\n    this.picker[\"rotate\"].visible = false;\n    this.picker[\"scale\"].visible = false;\n  }\n}\nclass TransformControlsPlane extends Mesh {\n  constructor() {\n    super(new PlaneGeometry(1e5, 1e5, 2, 2), new MeshBasicMaterial({\n      visible: false,\n      wireframe: true,\n      side: DoubleSide,\n      transparent: true,\n      opacity: 0.1,\n      toneMapped: false\n    }));\n    __publicField(this, \"isTransformControlsPlane\", true);\n    __publicField(this, \"type\", \"TransformControlsPlane\");\n    __publicField(this, \"unitX\", new Vector3(1, 0, 0));\n    __publicField(this, \"unitY\", new Vector3(0, 1, 0));\n    __publicField(this, \"unitZ\", new Vector3(0, 0, 1));\n    __publicField(this, \"tempVector\", new Vector3());\n    __publicField(this, \"dirVector\", new Vector3());\n    __publicField(this, \"alignVector\", new Vector3());\n    __publicField(this, \"tempMatrix\", new Matrix4());\n    __publicField(this, \"identityQuaternion\", new Quaternion());\n    // these are set from parent class TransformControls\n    __publicField(this, \"cameraQuaternion\", new Quaternion());\n    __publicField(this, \"worldPosition\", new Vector3());\n    __publicField(this, \"worldQuaternion\", new Quaternion());\n    __publicField(this, \"eye\", new Vector3());\n    __publicField(this, \"axis\", null);\n    __publicField(this, \"mode\", \"translate\");\n    __publicField(this, \"space\", \"world\");\n    __publicField(this, \"updateMatrixWorld\", () => {\n      let space = this.space;\n      this.position.copy(this.worldPosition);\n      if (this.mode === \"scale\") space = \"local\";\n      this.unitX.set(1, 0, 0).applyQuaternion(space === \"local\" ? this.worldQuaternion : this.identityQuaternion);\n      this.unitY.set(0, 1, 0).applyQuaternion(space === \"local\" ? this.worldQuaternion : this.identityQuaternion);\n      this.unitZ.set(0, 0, 1).applyQuaternion(space === \"local\" ? this.worldQuaternion : this.identityQuaternion);\n      this.alignVector.copy(this.unitY);\n      switch (this.mode) {\n        case \"translate\":\n        case \"scale\":\n          switch (this.axis) {\n            case \"X\":\n              this.alignVector.copy(this.eye).cross(this.unitX);\n              this.dirVector.copy(this.unitX).cross(this.alignVector);\n              break;\n            case \"Y\":\n              this.alignVector.copy(this.eye).cross(this.unitY);\n              this.dirVector.copy(this.unitY).cross(this.alignVector);\n              break;\n            case \"Z\":\n              this.alignVector.copy(this.eye).cross(this.unitZ);\n              this.dirVector.copy(this.unitZ).cross(this.alignVector);\n              break;\n            case \"XY\":\n              this.dirVector.copy(this.unitZ);\n              break;\n            case \"YZ\":\n              this.dirVector.copy(this.unitX);\n              break;\n            case \"XZ\":\n              this.alignVector.copy(this.unitZ);\n              this.dirVector.copy(this.unitY);\n              break;\n            case \"XYZ\":\n            case \"E\":\n              this.dirVector.set(0, 0, 0);\n              break;\n          }\n          break;\n        case \"rotate\":\n        default:\n          this.dirVector.set(0, 0, 0);\n      }\n      if (this.dirVector.length() === 0) {\n        this.quaternion.copy(this.cameraQuaternion);\n      } else {\n        this.tempMatrix.lookAt(this.tempVector.set(0, 0, 0), this.dirVector, this.alignVector);\n        this.quaternion.setFromRotationMatrix(this.tempMatrix);\n      }\n      super.updateMatrixWorld();\n    });\n  }\n}\nexport { TransformControls, TransformControlsGizmo, TransformControlsPlane };", "map": {"version": 3, "names": ["TransformControls", "Object3D", "constructor", "camera", "dom<PERSON>lement", "__publicField", "Raycaster", "Vector3", "Quaternion", "X", "Y", "Z", "type", "mode", "object", "raycaster", "includeInvisible", "allIntersections", "intersectObject", "i", "length", "visible", "axis", "enabled", "dragging", "position", "copy", "positionStart", "quaternion", "quaternionStart", "scale", "scaleStart", "dispatchEvent", "changeEvent", "objectChangeEvent", "pointStart", "pointEnd", "updateMatrixWorld", "parent", "console", "error", "matrixWorld", "decompose", "parentPosition", "parentQuaternion", "parentScale", "worldPosition", "worldQuaternion", "worldScale", "parentQuaternionInv", "invert", "worldQuaternionInv", "cameraPosition", "cameraQuaternion", "cameraScale", "eye", "sub", "normalize", "pointer", "setFromCamera", "intersect", "intersectObjectWithRay", "gizmo", "picker", "name", "button", "planeIntersect", "plane", "space", "snap", "rotationSnap", "rotation", "x", "Math", "round", "y", "z", "worldPositionStart", "worldQuaternionStart", "worldScaleStart", "point", "mouseDownEvent", "offset", "applyQuaternion", "indexOf", "divide", "add", "translationSnap", "tempQuaternion", "search", "tempVector", "setFromMatrixPosition", "d", "dot", "tempVector2", "set", "multiply", "scaleSnap", "ROTATION_SPEED", "distanceTo", "rotationAxis", "rotationAngle", "angleTo", "startNorm", "endNorm", "cross", "unit", "setFromAxisAngle", "mouseUpEvent", "event", "_a", "ownerDocument", "pointerLockElement", "changedTouches", "rect", "getBoundingClientRect", "clientX", "left", "width", "clientY", "top", "height", "pointerType", "pointerHover", "getPointer", "style", "touchAction", "addEventListener", "onPointerMove", "pointerDown", "pointer<PERSON><PERSON>", "removeEventListener", "pointerUp", "size", "warn", "document", "onPointerDown", "onPointerHover", "onPointerUp", "_b", "_d", "_c", "_f", "_e", "traverse", "child", "mesh", "geometry", "dispose", "material", "TransformControlsGizmo", "TransformControlsPlane", "defineProperty", "propName", "defaultValue", "propValue", "Object", "get", "value", "showX", "showY", "showZ", "connect", "<PERSON>uler", "Matrix4", "identityQuaternion", "helper", "handles", "concat", "children", "handle", "factor", "isOrthographicCamera", "bottom", "zoom", "min", "tan", "PI", "fov", "multiplyScalar", "tag", "setFromEuler", "tempEuler", "abs", "alignVector", "unitX", "unitY", "unitZ", "setFromRotationMatrix", "lookAtMatrix", "lookAt", "zeroVector", "clone", "AXIS_HIDE_TRESHOLD", "PLANE_HIDE_TRESHOLD", "AXIS_FLIP_TRESHOLD", "tempQuaternion2", "atan2", "multiplyQuaternions", "tempOpacity", "opacity", "tempColor", "color", "lerp", "Color", "split", "some", "a", "gizmoMaterial", "MeshBasicMaterial", "depthTest", "depthWrite", "transparent", "side", "DoubleSide", "fog", "toneMapped", "gizmoLineMaterial", "LineBasicMaterial", "linewidth", "matInvisible", "<PERSON><PERSON><PERSON><PERSON>", "matRed", "<PERSON><PERSON><PERSON>", "matBlue", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "matMagentaTransparent", "<PERSON><PERSON><PERSON><PERSON>", "matLineRed", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "matLineBlue", "mat<PERSON>ine<PERSON>yan", "matLineMagenta", "mat<PERSON><PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "matLineYellowTransparent", "arrowGeometry", "CylinderGeometry", "scaleHandleGeometry", "BoxGeometry", "lineGeometry", "BufferGeometry", "setAttribute", "Float32BufferAttribute", "CircleGeometry", "radius", "arc", "vertices", "push", "cos", "sin", "TranslateHelperGeometry", "gizmoTranslate", "<PERSON><PERSON>", "Line", "XYZ", "OctahedronGeometry", "XY", "PlaneGeometry", "YZ", "XZ", "pickerTranslate", "helperTranslate", "START", "END", "DELTA", "gizmoRotate", "E", "XYZE", "helperRotate", "AXIS", "pickerRotate", "TorusGeometry", "SphereGeometry", "gizmoScale", "XYZX", "XYZY", "XYZZ", "pickerScale", "helperScale", "setupGizmo", "gizmoMap", "updateMatrix", "tempGeometry", "applyMatrix4", "matrix", "renderOrder", "Infinity", "wireframe", "dirVector", "tempMatrix"], "sources": ["C:\\Users\\<USER>\\Documents\\Apps_Creations\\SiahMan_Racing4Speed_WebGame\\siah-man-racing\\node_modules\\src\\controls\\TransformControls.ts"], "sourcesContent": ["import {\n  BoxGeometry,\n  BufferGeometry,\n  Color,\n  CylinderGeometry,\n  DoubleSide,\n  Euler,\n  Float32BufferAttribute,\n  Line,\n  LineBasicMaterial,\n  Material,\n  Matrix4,\n  Mesh,\n  MeshBasicMaterial,\n  Object3D,\n  OctahedronGeometry,\n  OrthographicCamera,\n  PerspectiveCamera,\n  PlaneGeometry,\n  Quaternion,\n  Raycaster,\n  SphereGeometry,\n  Intersection,\n  TorusGeometry,\n  Vector3,\n  Camera,\n  Vector2,\n} from 'three'\n\nexport interface TransformControlsPointerObject {\n  x: number\n  y: number\n  button: number\n}\n\nclass TransformControls<TCamera extends Camera = Camera> extends Object3D {\n  public readonly isTransformControls = true\n\n  public visible = false\n\n  private domElement: HTMLElement | undefined\n\n  private raycaster = new Raycaster()\n\n  private gizmo: TransformControlsGizmo\n  private plane: TransformControlsPlane\n\n  private tempVector = new Vector3()\n  private tempVector2 = new Vector3()\n  private tempQuaternion = new Quaternion()\n  private unit = {\n    X: new Vector3(1, 0, 0),\n    Y: new Vector3(0, 1, 0),\n    Z: new Vector3(0, 0, 1),\n  }\n\n  private pointStart = new Vector3()\n  private pointEnd = new Vector3()\n  private offset = new Vector3()\n  private rotationAxis = new Vector3()\n  private startNorm = new Vector3()\n  private endNorm = new Vector3()\n  private rotationAngle = 0\n\n  private cameraPosition = new Vector3()\n  private cameraQuaternion = new Quaternion()\n  private cameraScale = new Vector3()\n\n  private parentPosition = new Vector3()\n  private parentQuaternion = new Quaternion()\n  private parentQuaternionInv = new Quaternion()\n  private parentScale = new Vector3()\n\n  private worldPositionStart = new Vector3()\n  private worldQuaternionStart = new Quaternion()\n  private worldScaleStart = new Vector3()\n\n  private worldPosition = new Vector3()\n  private worldQuaternion = new Quaternion()\n  private worldQuaternionInv = new Quaternion()\n  private worldScale = new Vector3()\n\n  private eye = new Vector3()\n\n  private positionStart = new Vector3()\n  private quaternionStart = new Quaternion()\n  private scaleStart = new Vector3()\n\n  private camera: TCamera\n  private object: Object3D | undefined\n  private enabled = true\n  private axis: string | null = null\n  private mode: 'translate' | 'rotate' | 'scale' = 'translate'\n  private translationSnap: number | null = null\n  private rotationSnap: number | null = null\n  private scaleSnap: number | null = null\n  private space = 'world'\n  private size = 1\n  private dragging = false\n  private showX = true\n  private showY = true\n  private showZ = true\n\n  // events\n  private changeEvent = { type: 'change' }\n  private mouseDownEvent = { type: 'mouseDown', mode: this.mode }\n  private mouseUpEvent = { type: 'mouseUp', mode: this.mode }\n  private objectChangeEvent = { type: 'objectChange' }\n\n  constructor(camera: TCamera, domElement: HTMLElement | undefined) {\n    super()\n\n    this.domElement = domElement\n    this.camera = camera\n\n    this.gizmo = new TransformControlsGizmo()\n    this.add(this.gizmo)\n\n    this.plane = new TransformControlsPlane()\n    this.add(this.plane)\n\n    // Defined getter, setter and store for a property\n    const defineProperty = <TValue>(propName: string, defaultValue: TValue): void => {\n      let propValue = defaultValue\n\n      Object.defineProperty(this, propName, {\n        get: function () {\n          return propValue !== undefined ? propValue : defaultValue\n        },\n\n        set: function (value) {\n          if (propValue !== value) {\n            propValue = value\n            this.plane[propName] = value\n            this.gizmo[propName] = value\n\n            this.dispatchEvent({ type: propName + '-changed', value: value })\n            this.dispatchEvent(this.changeEvent)\n          }\n        },\n      })\n\n      //@ts-ignore\n      this[propName] = defaultValue\n      // @ts-ignore\n      this.plane[propName] = defaultValue\n      // @ts-ignore\n      this.gizmo[propName] = defaultValue\n    }\n\n    defineProperty('camera', this.camera)\n    defineProperty('object', this.object)\n    defineProperty('enabled', this.enabled)\n    defineProperty('axis', this.axis)\n    defineProperty('mode', this.mode)\n    defineProperty('translationSnap', this.translationSnap)\n    defineProperty('rotationSnap', this.rotationSnap)\n    defineProperty('scaleSnap', this.scaleSnap)\n    defineProperty('space', this.space)\n    defineProperty('size', this.size)\n    defineProperty('dragging', this.dragging)\n    defineProperty('showX', this.showX)\n    defineProperty('showY', this.showY)\n    defineProperty('showZ', this.showZ)\n    defineProperty('worldPosition', this.worldPosition)\n    defineProperty('worldPositionStart', this.worldPositionStart)\n    defineProperty('worldQuaternion', this.worldQuaternion)\n    defineProperty('worldQuaternionStart', this.worldQuaternionStart)\n    defineProperty('cameraPosition', this.cameraPosition)\n    defineProperty('cameraQuaternion', this.cameraQuaternion)\n    defineProperty('pointStart', this.pointStart)\n    defineProperty('pointEnd', this.pointEnd)\n    defineProperty('rotationAxis', this.rotationAxis)\n    defineProperty('rotationAngle', this.rotationAngle)\n    defineProperty('eye', this.eye)\n\n    // connect events\n    if (domElement !== undefined) this.connect(domElement)\n  }\n\n  private intersectObjectWithRay = (\n    object: Object3D,\n    raycaster: Raycaster,\n    includeInvisible?: boolean,\n  ): false | Intersection => {\n    const allIntersections = raycaster.intersectObject(object, true)\n\n    for (let i = 0; i < allIntersections.length; i++) {\n      if (allIntersections[i].object.visible || includeInvisible) {\n        return allIntersections[i]\n      }\n    }\n\n    return false\n  }\n\n  // Set current object\n  public attach = (object: Object3D): this => {\n    this.object = object\n    this.visible = true\n\n    return this\n  }\n\n  // Detatch from object\n  public detach = (): this => {\n    this.object = undefined\n    this.visible = false\n    this.axis = null\n\n    return this\n  }\n\n  // Reset\n  public reset = (): this => {\n    if (!this.enabled) return this\n\n    if (this.dragging) {\n      if (this.object !== undefined) {\n        this.object.position.copy(this.positionStart)\n        this.object.quaternion.copy(this.quaternionStart)\n        this.object.scale.copy(this.scaleStart)\n        // @ts-ignore\n        this.dispatchEvent(this.changeEvent)\n        // @ts-ignore\n        this.dispatchEvent(this.objectChangeEvent)\n        this.pointStart.copy(this.pointEnd)\n      }\n    }\n\n    return this\n  }\n\n  public updateMatrixWorld = (): void => {\n    if (this.object !== undefined) {\n      this.object.updateMatrixWorld()\n\n      if (this.object.parent === null) {\n        console.error('TransformControls: The attached 3D object must be a part of the scene graph.')\n      } else {\n        this.object.parent.matrixWorld.decompose(this.parentPosition, this.parentQuaternion, this.parentScale)\n      }\n\n      this.object.matrixWorld.decompose(this.worldPosition, this.worldQuaternion, this.worldScale)\n\n      this.parentQuaternionInv.copy(this.parentQuaternion).invert()\n      this.worldQuaternionInv.copy(this.worldQuaternion).invert()\n    }\n\n    this.camera.updateMatrixWorld()\n    this.camera.matrixWorld.decompose(this.cameraPosition, this.cameraQuaternion, this.cameraScale)\n\n    this.eye.copy(this.cameraPosition).sub(this.worldPosition).normalize()\n\n    super.updateMatrixWorld()\n  }\n\n  private pointerHover = (pointer: TransformControlsPointerObject): void => {\n    if (this.object === undefined || this.dragging === true) return\n\n    this.raycaster.setFromCamera((pointer as unknown) as Vector2, this.camera)\n\n    const intersect = this.intersectObjectWithRay(this.gizmo.picker[this.mode], this.raycaster)\n\n    if (intersect) {\n      this.axis = intersect.object.name\n    } else {\n      this.axis = null\n    }\n  }\n\n  private pointerDown = (pointer: TransformControlsPointerObject): void => {\n    if (this.object === undefined || this.dragging === true || pointer.button !== 0) return\n\n    if (this.axis !== null) {\n      this.raycaster.setFromCamera((pointer as unknown) as Vector2, this.camera)\n\n      const planeIntersect = this.intersectObjectWithRay(this.plane, this.raycaster, true)\n\n      if (planeIntersect) {\n        let space = this.space\n\n        if (this.mode === 'scale') {\n          space = 'local'\n        } else if (this.axis === 'E' || this.axis === 'XYZE' || this.axis === 'XYZ') {\n          space = 'world'\n        }\n\n        if (space === 'local' && this.mode === 'rotate') {\n          const snap = this.rotationSnap\n\n          if (this.axis === 'X' && snap) this.object.rotation.x = Math.round(this.object.rotation.x / snap) * snap\n          if (this.axis === 'Y' && snap) this.object.rotation.y = Math.round(this.object.rotation.y / snap) * snap\n          if (this.axis === 'Z' && snap) this.object.rotation.z = Math.round(this.object.rotation.z / snap) * snap\n        }\n\n        this.object.updateMatrixWorld()\n\n        if (this.object.parent) {\n          this.object.parent.updateMatrixWorld()\n        }\n\n        this.positionStart.copy(this.object.position)\n        this.quaternionStart.copy(this.object.quaternion)\n        this.scaleStart.copy(this.object.scale)\n\n        this.object.matrixWorld.decompose(this.worldPositionStart, this.worldQuaternionStart, this.worldScaleStart)\n\n        this.pointStart.copy(planeIntersect.point).sub(this.worldPositionStart)\n      }\n\n      this.dragging = true\n      this.mouseDownEvent.mode = this.mode\n      // @ts-ignore\n      this.dispatchEvent(this.mouseDownEvent)\n    }\n  }\n\n  private pointerMove = (pointer: TransformControlsPointerObject): void => {\n    const axis = this.axis\n    const mode = this.mode\n    const object = this.object\n    let space = this.space\n\n    if (mode === 'scale') {\n      space = 'local'\n    } else if (axis === 'E' || axis === 'XYZE' || axis === 'XYZ') {\n      space = 'world'\n    }\n\n    if (object === undefined || axis === null || this.dragging === false || pointer.button !== -1) return\n\n    this.raycaster.setFromCamera((pointer as unknown) as Vector2, this.camera)\n\n    const planeIntersect = this.intersectObjectWithRay(this.plane, this.raycaster, true)\n\n    if (!planeIntersect) return\n\n    this.pointEnd.copy(planeIntersect.point).sub(this.worldPositionStart)\n\n    if (mode === 'translate') {\n      // Apply translate\n\n      this.offset.copy(this.pointEnd).sub(this.pointStart)\n\n      if (space === 'local' && axis !== 'XYZ') {\n        this.offset.applyQuaternion(this.worldQuaternionInv)\n      }\n\n      if (axis.indexOf('X') === -1) this.offset.x = 0\n      if (axis.indexOf('Y') === -1) this.offset.y = 0\n      if (axis.indexOf('Z') === -1) this.offset.z = 0\n\n      if (space === 'local' && axis !== 'XYZ') {\n        this.offset.applyQuaternion(this.quaternionStart).divide(this.parentScale)\n      } else {\n        this.offset.applyQuaternion(this.parentQuaternionInv).divide(this.parentScale)\n      }\n\n      object.position.copy(this.offset).add(this.positionStart)\n\n      // Apply translation snap\n\n      if (this.translationSnap) {\n        if (space === 'local') {\n          object.position.applyQuaternion(this.tempQuaternion.copy(this.quaternionStart).invert())\n\n          if (axis.search('X') !== -1) {\n            object.position.x = Math.round(object.position.x / this.translationSnap) * this.translationSnap\n          }\n\n          if (axis.search('Y') !== -1) {\n            object.position.y = Math.round(object.position.y / this.translationSnap) * this.translationSnap\n          }\n\n          if (axis.search('Z') !== -1) {\n            object.position.z = Math.round(object.position.z / this.translationSnap) * this.translationSnap\n          }\n\n          object.position.applyQuaternion(this.quaternionStart)\n        }\n\n        if (space === 'world') {\n          if (object.parent) {\n            object.position.add(this.tempVector.setFromMatrixPosition(object.parent.matrixWorld))\n          }\n\n          if (axis.search('X') !== -1) {\n            object.position.x = Math.round(object.position.x / this.translationSnap) * this.translationSnap\n          }\n\n          if (axis.search('Y') !== -1) {\n            object.position.y = Math.round(object.position.y / this.translationSnap) * this.translationSnap\n          }\n\n          if (axis.search('Z') !== -1) {\n            object.position.z = Math.round(object.position.z / this.translationSnap) * this.translationSnap\n          }\n\n          if (object.parent) {\n            object.position.sub(this.tempVector.setFromMatrixPosition(object.parent.matrixWorld))\n          }\n        }\n      }\n    } else if (mode === 'scale') {\n      if (axis.search('XYZ') !== -1) {\n        let d = this.pointEnd.length() / this.pointStart.length()\n\n        if (this.pointEnd.dot(this.pointStart) < 0) d *= -1\n\n        this.tempVector2.set(d, d, d)\n      } else {\n        this.tempVector.copy(this.pointStart)\n        this.tempVector2.copy(this.pointEnd)\n\n        this.tempVector.applyQuaternion(this.worldQuaternionInv)\n        this.tempVector2.applyQuaternion(this.worldQuaternionInv)\n\n        this.tempVector2.divide(this.tempVector)\n\n        if (axis.search('X') === -1) {\n          this.tempVector2.x = 1\n        }\n\n        if (axis.search('Y') === -1) {\n          this.tempVector2.y = 1\n        }\n\n        if (axis.search('Z') === -1) {\n          this.tempVector2.z = 1\n        }\n      }\n\n      // Apply scale\n\n      object.scale.copy(this.scaleStart).multiply(this.tempVector2)\n\n      if (this.scaleSnap && this.object) {\n        if (axis.search('X') !== -1) {\n          this.object.scale.x = Math.round(object.scale.x / this.scaleSnap) * this.scaleSnap || this.scaleSnap\n        }\n\n        if (axis.search('Y') !== -1) {\n          object.scale.y = Math.round(object.scale.y / this.scaleSnap) * this.scaleSnap || this.scaleSnap\n        }\n\n        if (axis.search('Z') !== -1) {\n          object.scale.z = Math.round(object.scale.z / this.scaleSnap) * this.scaleSnap || this.scaleSnap\n        }\n      }\n    } else if (mode === 'rotate') {\n      this.offset.copy(this.pointEnd).sub(this.pointStart)\n\n      const ROTATION_SPEED =\n        20 / this.worldPosition.distanceTo(this.tempVector.setFromMatrixPosition(this.camera.matrixWorld))\n\n      if (axis === 'E') {\n        this.rotationAxis.copy(this.eye)\n        this.rotationAngle = this.pointEnd.angleTo(this.pointStart)\n\n        this.startNorm.copy(this.pointStart).normalize()\n        this.endNorm.copy(this.pointEnd).normalize()\n\n        this.rotationAngle *= this.endNorm.cross(this.startNorm).dot(this.eye) < 0 ? 1 : -1\n      } else if (axis === 'XYZE') {\n        this.rotationAxis.copy(this.offset).cross(this.eye).normalize()\n        this.rotationAngle = this.offset.dot(this.tempVector.copy(this.rotationAxis).cross(this.eye)) * ROTATION_SPEED\n      } else if (axis === 'X' || axis === 'Y' || axis === 'Z') {\n        this.rotationAxis.copy(this.unit[axis])\n\n        this.tempVector.copy(this.unit[axis])\n\n        if (space === 'local') {\n          this.tempVector.applyQuaternion(this.worldQuaternion)\n        }\n\n        this.rotationAngle = this.offset.dot(this.tempVector.cross(this.eye).normalize()) * ROTATION_SPEED\n      }\n\n      // Apply rotation snap\n\n      if (this.rotationSnap) {\n        this.rotationAngle = Math.round(this.rotationAngle / this.rotationSnap) * this.rotationSnap\n      }\n\n      // Apply rotate\n      if (space === 'local' && axis !== 'E' && axis !== 'XYZE') {\n        object.quaternion.copy(this.quaternionStart)\n        object.quaternion\n          .multiply(this.tempQuaternion.setFromAxisAngle(this.rotationAxis, this.rotationAngle))\n          .normalize()\n      } else {\n        this.rotationAxis.applyQuaternion(this.parentQuaternionInv)\n        object.quaternion.copy(this.tempQuaternion.setFromAxisAngle(this.rotationAxis, this.rotationAngle))\n        object.quaternion.multiply(this.quaternionStart).normalize()\n      }\n    }\n\n    // @ts-ignore\n    this.dispatchEvent(this.changeEvent)\n    // @ts-ignore\n    this.dispatchEvent(this.objectChangeEvent)\n  }\n\n  private pointerUp = (pointer: TransformControlsPointerObject): void => {\n    if (pointer.button !== 0) return\n\n    if (this.dragging && this.axis !== null) {\n      this.mouseUpEvent.mode = this.mode\n      // @ts-ignore\n      this.dispatchEvent(this.mouseUpEvent)\n    }\n\n    this.dragging = false\n    this.axis = null\n  }\n\n  private getPointer = (event: Event): TransformControlsPointerObject => {\n    if (this.domElement && this.domElement.ownerDocument?.pointerLockElement) {\n      return {\n        x: 0,\n        y: 0,\n        button: (event as MouseEvent).button,\n      }\n    } else {\n      const pointer = (event as TouchEvent).changedTouches\n        ? (event as TouchEvent).changedTouches[0]\n        : (event as MouseEvent)\n\n      const rect = this.domElement!.getBoundingClientRect()\n\n      return {\n        x: ((pointer.clientX - rect.left) / rect.width) * 2 - 1,\n        y: (-(pointer.clientY - rect.top) / rect.height) * 2 + 1,\n        button: (event as MouseEvent).button,\n      }\n    }\n  }\n\n  private onPointerHover = (event: Event): void => {\n    if (!this.enabled) return\n\n    switch ((event as PointerEvent).pointerType) {\n      case 'mouse':\n      case 'pen':\n        this.pointerHover(this.getPointer(event))\n        break\n    }\n  }\n\n  private onPointerDown = (event: Event): void => {\n    if (!this.enabled || !this.domElement) return\n\n    this.domElement.style.touchAction = 'none' // disable touch scroll\n    this.domElement.ownerDocument.addEventListener('pointermove', this.onPointerMove)\n    this.pointerHover(this.getPointer(event))\n    this.pointerDown(this.getPointer(event))\n  }\n\n  private onPointerMove = (event: Event): void => {\n    if (!this.enabled) return\n\n    this.pointerMove(this.getPointer(event))\n  }\n\n  private onPointerUp = (event: Event): void => {\n    if (!this.enabled || !this.domElement) return\n\n    this.domElement.style.touchAction! = ''\n    this.domElement.ownerDocument.removeEventListener('pointermove', this.onPointerMove)\n\n    this.pointerUp(this.getPointer(event))\n  }\n\n  public getMode = (): TransformControls['mode'] => this.mode\n\n  public setMode = (mode: TransformControls['mode']): void => {\n    this.mode = mode\n  }\n\n  public setTranslationSnap = (translationSnap: number): void => {\n    this.translationSnap = translationSnap\n  }\n\n  public setRotationSnap = (rotationSnap: number): void => {\n    this.rotationSnap = rotationSnap\n  }\n\n  public setScaleSnap = (scaleSnap: number): void => {\n    this.scaleSnap = scaleSnap\n  }\n\n  public setSize = (size: number): void => {\n    this.size = size\n  }\n\n  public setSpace = (space: string): void => {\n    this.space = space\n  }\n\n  public update = (): void => {\n    console.warn(\n      'THREE.TransformControls: update function has no more functionality and therefore has been deprecated.',\n    )\n  }\n\n  public connect = (domElement: HTMLElement): void => {\n    if ((domElement as any) === document) {\n      console.error(\n        'THREE.OrbitControls: \"document\" should not be used as the target \"domElement\". Please use \"renderer.domElement\" instead.',\n      )\n    }\n    this.domElement = domElement\n\n    this.domElement.addEventListener('pointerdown', this.onPointerDown)\n    this.domElement.addEventListener('pointermove', this.onPointerHover)\n    this.domElement.ownerDocument.addEventListener('pointerup', this.onPointerUp)\n  }\n\n  public dispose = (): void => {\n    this.domElement?.removeEventListener('pointerdown', this.onPointerDown)\n    this.domElement?.removeEventListener('pointermove', this.onPointerHover)\n    this.domElement?.ownerDocument?.removeEventListener('pointermove', this.onPointerMove)\n    this.domElement?.ownerDocument?.removeEventListener('pointerup', this.onPointerUp)\n\n    this.traverse((child) => {\n      const mesh = child as Mesh<BufferGeometry, Material>\n      if (mesh.geometry) {\n        mesh.geometry.dispose()\n      }\n      if (mesh.material) {\n        mesh.material.dispose()\n      }\n    })\n  }\n}\n\ntype TransformControlsGizmoPrivateGizmos = {\n  ['translate']: Object3D\n  ['scale']: Object3D\n  ['rotate']: Object3D\n  ['visible']: boolean\n}\n\nclass TransformControlsGizmo extends Object3D {\n  private isTransformControlsGizmo = true\n  public type = 'TransformControlsGizmo'\n\n  private tempVector = new Vector3(0, 0, 0)\n  private tempEuler = new Euler()\n  private alignVector = new Vector3(0, 1, 0)\n  private zeroVector = new Vector3(0, 0, 0)\n  private lookAtMatrix = new Matrix4()\n  private tempQuaternion = new Quaternion()\n  private tempQuaternion2 = new Quaternion()\n  private identityQuaternion = new Quaternion()\n\n  private unitX = new Vector3(1, 0, 0)\n  private unitY = new Vector3(0, 1, 0)\n  private unitZ = new Vector3(0, 0, 1)\n\n  private gizmo: TransformControlsGizmoPrivateGizmos\n  public picker: TransformControlsGizmoPrivateGizmos\n  private helper: TransformControlsGizmoPrivateGizmos\n\n  // these are set from parent class TransformControls\n  private rotationAxis = new Vector3()\n\n  private cameraPosition = new Vector3()\n\n  private worldPositionStart = new Vector3()\n  private worldQuaternionStart = new Quaternion()\n\n  private worldPosition = new Vector3()\n  private worldQuaternion = new Quaternion()\n\n  private eye = new Vector3()\n\n  private camera: PerspectiveCamera | OrthographicCamera = null!\n  private enabled = true\n  private axis: string | null = null\n  private mode: 'translate' | 'rotate' | 'scale' = 'translate'\n  private space = 'world'\n  private size = 1\n  private dragging = false\n  private showX = true\n  private showY = true\n  private showZ = true\n\n  constructor() {\n    super()\n\n    const gizmoMaterial = new MeshBasicMaterial({\n      depthTest: false,\n      depthWrite: false,\n      transparent: true,\n      side: DoubleSide,\n      fog: false,\n      toneMapped: false,\n    })\n\n    const gizmoLineMaterial = new LineBasicMaterial({\n      depthTest: false,\n      depthWrite: false,\n      transparent: true,\n      linewidth: 1,\n      fog: false,\n      toneMapped: false,\n    })\n\n    // Make unique material for each axis/color\n\n    const matInvisible = gizmoMaterial.clone()\n    matInvisible.opacity = 0.15\n\n    const matHelper = gizmoMaterial.clone()\n    matHelper.opacity = 0.33\n\n    const matRed = gizmoMaterial.clone() as MeshBasicMaterial\n    matRed.color.set(0xff0000)\n\n    const matGreen = gizmoMaterial.clone() as MeshBasicMaterial\n    matGreen.color.set(0x00ff00)\n\n    const matBlue = gizmoMaterial.clone() as MeshBasicMaterial\n    matBlue.color.set(0x0000ff)\n\n    const matWhiteTransparent = gizmoMaterial.clone() as MeshBasicMaterial\n    matWhiteTransparent.opacity = 0.25\n\n    const matYellowTransparent = matWhiteTransparent.clone() as MeshBasicMaterial\n    matYellowTransparent.color.set(0xffff00)\n\n    const matCyanTransparent = matWhiteTransparent.clone() as MeshBasicMaterial\n    matCyanTransparent.color.set(0x00ffff)\n\n    const matMagentaTransparent = matWhiteTransparent.clone() as MeshBasicMaterial\n    matMagentaTransparent.color.set(0xff00ff)\n\n    const matYellow = gizmoMaterial.clone() as MeshBasicMaterial\n    matYellow.color.set(0xffff00)\n\n    const matLineRed = gizmoLineMaterial.clone() as LineBasicMaterial\n    matLineRed.color.set(0xff0000)\n\n    const matLineGreen = gizmoLineMaterial.clone() as LineBasicMaterial\n    matLineGreen.color.set(0x00ff00)\n\n    const matLineBlue = gizmoLineMaterial.clone() as LineBasicMaterial\n    matLineBlue.color.set(0x0000ff)\n\n    const matLineCyan = gizmoLineMaterial.clone() as LineBasicMaterial\n    matLineCyan.color.set(0x00ffff)\n\n    const matLineMagenta = gizmoLineMaterial.clone() as LineBasicMaterial\n    matLineMagenta.color.set(0xff00ff)\n\n    const matLineYellow = gizmoLineMaterial.clone() as LineBasicMaterial\n    matLineYellow.color.set(0xffff00)\n\n    const matLineGray = gizmoLineMaterial.clone() as LineBasicMaterial\n    matLineGray.color.set(0x787878)\n\n    const matLineYellowTransparent = matLineYellow.clone() as LineBasicMaterial\n    matLineYellowTransparent.opacity = 0.25\n\n    // reusable geometry\n\n    const arrowGeometry = new CylinderGeometry(0, 0.05, 0.2, 12, 1, false)\n\n    const scaleHandleGeometry = new BoxGeometry(0.125, 0.125, 0.125)\n\n    const lineGeometry = new BufferGeometry()\n    lineGeometry.setAttribute('position', new Float32BufferAttribute([0, 0, 0, 1, 0, 0], 3))\n\n    const CircleGeometry = (radius: number, arc: number): BufferGeometry => {\n      const geometry = new BufferGeometry()\n      const vertices = []\n\n      for (let i = 0; i <= 64 * arc; ++i) {\n        vertices.push(0, Math.cos((i / 32) * Math.PI) * radius, Math.sin((i / 32) * Math.PI) * radius)\n      }\n\n      geometry.setAttribute('position', new Float32BufferAttribute(vertices, 3))\n\n      return geometry\n    }\n\n    // Special geometry for transform helper. If scaled with position vector it spans from [0,0,0] to position\n\n    const TranslateHelperGeometry = (): BufferGeometry => {\n      const geometry = new BufferGeometry()\n\n      geometry.setAttribute('position', new Float32BufferAttribute([0, 0, 0, 1, 1, 1], 3))\n\n      return geometry\n    }\n\n    // Gizmo definitions - custom hierarchy definitions for setupGizmo() function\n\n    const gizmoTranslate = {\n      X: [\n        [new Mesh(arrowGeometry, matRed), [1, 0, 0], [0, 0, -Math.PI / 2], null, 'fwd'],\n        [new Mesh(arrowGeometry, matRed), [1, 0, 0], [0, 0, Math.PI / 2], null, 'bwd'],\n        [new Line(lineGeometry, matLineRed)],\n      ],\n      Y: [\n        [new Mesh(arrowGeometry, matGreen), [0, 1, 0], null, null, 'fwd'],\n        [new Mesh(arrowGeometry, matGreen), [0, 1, 0], [Math.PI, 0, 0], null, 'bwd'],\n        [new Line(lineGeometry, matLineGreen), null, [0, 0, Math.PI / 2]],\n      ],\n      Z: [\n        [new Mesh(arrowGeometry, matBlue), [0, 0, 1], [Math.PI / 2, 0, 0], null, 'fwd'],\n        [new Mesh(arrowGeometry, matBlue), [0, 0, 1], [-Math.PI / 2, 0, 0], null, 'bwd'],\n        [new Line(lineGeometry, matLineBlue), null, [0, -Math.PI / 2, 0]],\n      ],\n      XYZ: [[new Mesh(new OctahedronGeometry(0.1, 0), matWhiteTransparent.clone()), [0, 0, 0], [0, 0, 0]]],\n      XY: [\n        [new Mesh(new PlaneGeometry(0.295, 0.295), matYellowTransparent.clone()), [0.15, 0.15, 0]],\n        [new Line(lineGeometry, matLineYellow), [0.18, 0.3, 0], null, [0.125, 1, 1]],\n        [new Line(lineGeometry, matLineYellow), [0.3, 0.18, 0], [0, 0, Math.PI / 2], [0.125, 1, 1]],\n      ],\n      YZ: [\n        [new Mesh(new PlaneGeometry(0.295, 0.295), matCyanTransparent.clone()), [0, 0.15, 0.15], [0, Math.PI / 2, 0]],\n        [new Line(lineGeometry, matLineCyan), [0, 0.18, 0.3], [0, 0, Math.PI / 2], [0.125, 1, 1]],\n        [new Line(lineGeometry, matLineCyan), [0, 0.3, 0.18], [0, -Math.PI / 2, 0], [0.125, 1, 1]],\n      ],\n      XZ: [\n        [\n          new Mesh(new PlaneGeometry(0.295, 0.295), matMagentaTransparent.clone()),\n          [0.15, 0, 0.15],\n          [-Math.PI / 2, 0, 0],\n        ],\n        [new Line(lineGeometry, matLineMagenta), [0.18, 0, 0.3], null, [0.125, 1, 1]],\n        [new Line(lineGeometry, matLineMagenta), [0.3, 0, 0.18], [0, -Math.PI / 2, 0], [0.125, 1, 1]],\n      ],\n    }\n\n    const pickerTranslate = {\n      X: [[new Mesh(new CylinderGeometry(0.2, 0, 1, 4, 1, false), matInvisible), [0.6, 0, 0], [0, 0, -Math.PI / 2]]],\n      Y: [[new Mesh(new CylinderGeometry(0.2, 0, 1, 4, 1, false), matInvisible), [0, 0.6, 0]]],\n      Z: [[new Mesh(new CylinderGeometry(0.2, 0, 1, 4, 1, false), matInvisible), [0, 0, 0.6], [Math.PI / 2, 0, 0]]],\n      XYZ: [[new Mesh(new OctahedronGeometry(0.2, 0), matInvisible)]],\n      XY: [[new Mesh(new PlaneGeometry(0.4, 0.4), matInvisible), [0.2, 0.2, 0]]],\n      YZ: [[new Mesh(new PlaneGeometry(0.4, 0.4), matInvisible), [0, 0.2, 0.2], [0, Math.PI / 2, 0]]],\n      XZ: [[new Mesh(new PlaneGeometry(0.4, 0.4), matInvisible), [0.2, 0, 0.2], [-Math.PI / 2, 0, 0]]],\n    }\n\n    const helperTranslate = {\n      START: [[new Mesh(new OctahedronGeometry(0.01, 2), matHelper), null, null, null, 'helper']],\n      END: [[new Mesh(new OctahedronGeometry(0.01, 2), matHelper), null, null, null, 'helper']],\n      DELTA: [[new Line(TranslateHelperGeometry(), matHelper), null, null, null, 'helper']],\n      X: [[new Line(lineGeometry, matHelper.clone()), [-1e3, 0, 0], null, [1e6, 1, 1], 'helper']],\n      Y: [[new Line(lineGeometry, matHelper.clone()), [0, -1e3, 0], [0, 0, Math.PI / 2], [1e6, 1, 1], 'helper']],\n      Z: [[new Line(lineGeometry, matHelper.clone()), [0, 0, -1e3], [0, -Math.PI / 2, 0], [1e6, 1, 1], 'helper']],\n    }\n\n    const gizmoRotate = {\n      X: [\n        [new Line(CircleGeometry(1, 0.5), matLineRed)],\n        [new Mesh(new OctahedronGeometry(0.04, 0), matRed), [0, 0, 0.99], null, [1, 3, 1]],\n      ],\n      Y: [\n        [new Line(CircleGeometry(1, 0.5), matLineGreen), null, [0, 0, -Math.PI / 2]],\n        [new Mesh(new OctahedronGeometry(0.04, 0), matGreen), [0, 0, 0.99], null, [3, 1, 1]],\n      ],\n      Z: [\n        [new Line(CircleGeometry(1, 0.5), matLineBlue), null, [0, Math.PI / 2, 0]],\n        [new Mesh(new OctahedronGeometry(0.04, 0), matBlue), [0.99, 0, 0], null, [1, 3, 1]],\n      ],\n      E: [\n        [new Line(CircleGeometry(1.25, 1), matLineYellowTransparent), null, [0, Math.PI / 2, 0]],\n        [\n          new Mesh(new CylinderGeometry(0.03, 0, 0.15, 4, 1, false), matLineYellowTransparent),\n          [1.17, 0, 0],\n          [0, 0, -Math.PI / 2],\n          [1, 1, 0.001],\n        ],\n        [\n          new Mesh(new CylinderGeometry(0.03, 0, 0.15, 4, 1, false), matLineYellowTransparent),\n          [-1.17, 0, 0],\n          [0, 0, Math.PI / 2],\n          [1, 1, 0.001],\n        ],\n        [\n          new Mesh(new CylinderGeometry(0.03, 0, 0.15, 4, 1, false), matLineYellowTransparent),\n          [0, -1.17, 0],\n          [Math.PI, 0, 0],\n          [1, 1, 0.001],\n        ],\n        [\n          new Mesh(new CylinderGeometry(0.03, 0, 0.15, 4, 1, false), matLineYellowTransparent),\n          [0, 1.17, 0],\n          [0, 0, 0],\n          [1, 1, 0.001],\n        ],\n      ],\n      XYZE: [[new Line(CircleGeometry(1, 1), matLineGray), null, [0, Math.PI / 2, 0]]],\n    }\n\n    const helperRotate = {\n      AXIS: [[new Line(lineGeometry, matHelper.clone()), [-1e3, 0, 0], null, [1e6, 1, 1], 'helper']],\n    }\n\n    const pickerRotate = {\n      X: [[new Mesh(new TorusGeometry(1, 0.1, 4, 24), matInvisible), [0, 0, 0], [0, -Math.PI / 2, -Math.PI / 2]]],\n      Y: [[new Mesh(new TorusGeometry(1, 0.1, 4, 24), matInvisible), [0, 0, 0], [Math.PI / 2, 0, 0]]],\n      Z: [[new Mesh(new TorusGeometry(1, 0.1, 4, 24), matInvisible), [0, 0, 0], [0, 0, -Math.PI / 2]]],\n      E: [[new Mesh(new TorusGeometry(1.25, 0.1, 2, 24), matInvisible)]],\n      XYZE: [[new Mesh(new SphereGeometry(0.7, 10, 8), matInvisible)]],\n    }\n\n    const gizmoScale = {\n      X: [\n        [new Mesh(scaleHandleGeometry, matRed), [0.8, 0, 0], [0, 0, -Math.PI / 2]],\n        [new Line(lineGeometry, matLineRed), null, null, [0.8, 1, 1]],\n      ],\n      Y: [\n        [new Mesh(scaleHandleGeometry, matGreen), [0, 0.8, 0]],\n        [new Line(lineGeometry, matLineGreen), null, [0, 0, Math.PI / 2], [0.8, 1, 1]],\n      ],\n      Z: [\n        [new Mesh(scaleHandleGeometry, matBlue), [0, 0, 0.8], [Math.PI / 2, 0, 0]],\n        [new Line(lineGeometry, matLineBlue), null, [0, -Math.PI / 2, 0], [0.8, 1, 1]],\n      ],\n      XY: [\n        [new Mesh(scaleHandleGeometry, matYellowTransparent), [0.85, 0.85, 0], null, [2, 2, 0.2]],\n        [new Line(lineGeometry, matLineYellow), [0.855, 0.98, 0], null, [0.125, 1, 1]],\n        [new Line(lineGeometry, matLineYellow), [0.98, 0.855, 0], [0, 0, Math.PI / 2], [0.125, 1, 1]],\n      ],\n      YZ: [\n        [new Mesh(scaleHandleGeometry, matCyanTransparent), [0, 0.85, 0.85], null, [0.2, 2, 2]],\n        [new Line(lineGeometry, matLineCyan), [0, 0.855, 0.98], [0, 0, Math.PI / 2], [0.125, 1, 1]],\n        [new Line(lineGeometry, matLineCyan), [0, 0.98, 0.855], [0, -Math.PI / 2, 0], [0.125, 1, 1]],\n      ],\n      XZ: [\n        [new Mesh(scaleHandleGeometry, matMagentaTransparent), [0.85, 0, 0.85], null, [2, 0.2, 2]],\n        [new Line(lineGeometry, matLineMagenta), [0.855, 0, 0.98], null, [0.125, 1, 1]],\n        [new Line(lineGeometry, matLineMagenta), [0.98, 0, 0.855], [0, -Math.PI / 2, 0], [0.125, 1, 1]],\n      ],\n      XYZX: [[new Mesh(new BoxGeometry(0.125, 0.125, 0.125), matWhiteTransparent.clone()), [1.1, 0, 0]]],\n      XYZY: [[new Mesh(new BoxGeometry(0.125, 0.125, 0.125), matWhiteTransparent.clone()), [0, 1.1, 0]]],\n      XYZZ: [[new Mesh(new BoxGeometry(0.125, 0.125, 0.125), matWhiteTransparent.clone()), [0, 0, 1.1]]],\n    }\n\n    const pickerScale = {\n      X: [[new Mesh(new CylinderGeometry(0.2, 0, 0.8, 4, 1, false), matInvisible), [0.5, 0, 0], [0, 0, -Math.PI / 2]]],\n      Y: [[new Mesh(new CylinderGeometry(0.2, 0, 0.8, 4, 1, false), matInvisible), [0, 0.5, 0]]],\n      Z: [[new Mesh(new CylinderGeometry(0.2, 0, 0.8, 4, 1, false), matInvisible), [0, 0, 0.5], [Math.PI / 2, 0, 0]]],\n      XY: [[new Mesh(scaleHandleGeometry, matInvisible), [0.85, 0.85, 0], null, [3, 3, 0.2]]],\n      YZ: [[new Mesh(scaleHandleGeometry, matInvisible), [0, 0.85, 0.85], null, [0.2, 3, 3]]],\n      XZ: [[new Mesh(scaleHandleGeometry, matInvisible), [0.85, 0, 0.85], null, [3, 0.2, 3]]],\n      XYZX: [[new Mesh(new BoxGeometry(0.2, 0.2, 0.2), matInvisible), [1.1, 0, 0]]],\n      XYZY: [[new Mesh(new BoxGeometry(0.2, 0.2, 0.2), matInvisible), [0, 1.1, 0]]],\n      XYZZ: [[new Mesh(new BoxGeometry(0.2, 0.2, 0.2), matInvisible), [0, 0, 1.1]]],\n    }\n\n    const helperScale = {\n      X: [[new Line(lineGeometry, matHelper.clone()), [-1e3, 0, 0], null, [1e6, 1, 1], 'helper']],\n      Y: [[new Line(lineGeometry, matHelper.clone()), [0, -1e3, 0], [0, 0, Math.PI / 2], [1e6, 1, 1], 'helper']],\n      Z: [[new Line(lineGeometry, matHelper.clone()), [0, 0, -1e3], [0, -Math.PI / 2, 0], [1e6, 1, 1], 'helper']],\n    }\n\n    // Creates an Object3D with gizmos described in custom hierarchy definition.\n    // this is nearly impossible to Type so i'm leaving it\n    const setupGizmo = (gizmoMap: any): Object3D => {\n      const gizmo = new Object3D()\n\n      for (let name in gizmoMap) {\n        for (let i = gizmoMap[name].length; i--; ) {\n          const object = gizmoMap[name][i][0].clone() as Mesh\n          const position = gizmoMap[name][i][1]\n          const rotation = gizmoMap[name][i][2]\n          const scale = gizmoMap[name][i][3]\n          const tag = gizmoMap[name][i][4]\n\n          // name and tag properties are essential for picking and updating logic.\n          object.name = name\n          // @ts-ignore\n          object.tag = tag\n\n          if (position) {\n            object.position.set(position[0], position[1], position[2])\n          }\n\n          if (rotation) {\n            object.rotation.set(rotation[0], rotation[1], rotation[2])\n          }\n\n          if (scale) {\n            object.scale.set(scale[0], scale[1], scale[2])\n          }\n\n          object.updateMatrix()\n\n          const tempGeometry = object.geometry.clone()\n          tempGeometry.applyMatrix4(object.matrix)\n          object.geometry = tempGeometry\n          object.renderOrder = Infinity\n\n          object.position.set(0, 0, 0)\n          object.rotation.set(0, 0, 0)\n          object.scale.set(1, 1, 1)\n\n          gizmo.add(object)\n        }\n      }\n\n      return gizmo\n    }\n\n    this.gizmo = {} as TransformControlsGizmoPrivateGizmos\n    this.picker = {} as TransformControlsGizmoPrivateGizmos\n    this.helper = {} as TransformControlsGizmoPrivateGizmos\n\n    this.add((this.gizmo['translate'] = setupGizmo(gizmoTranslate)))\n    this.add((this.gizmo['rotate'] = setupGizmo(gizmoRotate)))\n    this.add((this.gizmo['scale'] = setupGizmo(gizmoScale)))\n    this.add((this.picker['translate'] = setupGizmo(pickerTranslate)))\n    this.add((this.picker['rotate'] = setupGizmo(pickerRotate)))\n    this.add((this.picker['scale'] = setupGizmo(pickerScale)))\n    this.add((this.helper['translate'] = setupGizmo(helperTranslate)))\n    this.add((this.helper['rotate'] = setupGizmo(helperRotate)))\n    this.add((this.helper['scale'] = setupGizmo(helperScale)))\n\n    // Pickers should be hidden always\n\n    this.picker['translate'].visible = false\n    this.picker['rotate'].visible = false\n    this.picker['scale'].visible = false\n  }\n\n  // updateMatrixWorld will update transformations and appearance of individual handles\n  public updateMatrixWorld = (): void => {\n    let space = this.space\n\n    if (this.mode === 'scale') {\n      space = 'local' // scale always oriented to local rotation\n    }\n\n    const quaternion = space === 'local' ? this.worldQuaternion : this.identityQuaternion\n\n    // Show only gizmos for current transform mode\n\n    this.gizmo['translate'].visible = this.mode === 'translate'\n    this.gizmo['rotate'].visible = this.mode === 'rotate'\n    this.gizmo['scale'].visible = this.mode === 'scale'\n\n    this.helper['translate'].visible = this.mode === 'translate'\n    this.helper['rotate'].visible = this.mode === 'rotate'\n    this.helper['scale'].visible = this.mode === 'scale'\n\n    let handles: Array<Object3D & { tag?: string }> = []\n    handles = handles.concat(this.picker[this.mode].children)\n    handles = handles.concat(this.gizmo[this.mode].children)\n    handles = handles.concat(this.helper[this.mode].children)\n\n    for (let i = 0; i < handles.length; i++) {\n      const handle = handles[i]\n\n      // hide aligned to camera\n\n      handle.visible = true\n      handle.rotation.set(0, 0, 0)\n      handle.position.copy(this.worldPosition)\n\n      let factor\n\n      if ((this.camera as OrthographicCamera).isOrthographicCamera) {\n        factor =\n          ((this.camera as OrthographicCamera).top - (this.camera as OrthographicCamera).bottom) /\n          (this.camera as OrthographicCamera).zoom\n      } else {\n        factor =\n          this.worldPosition.distanceTo(this.cameraPosition) *\n          Math.min((1.9 * Math.tan((Math.PI * (this.camera as PerspectiveCamera).fov) / 360)) / this.camera.zoom, 7)\n      }\n\n      handle.scale.set(1, 1, 1).multiplyScalar((factor * this.size) / 7)\n\n      // TODO: simplify helpers and consider decoupling from gizmo\n\n      if (handle.tag === 'helper') {\n        handle.visible = false\n\n        if (handle.name === 'AXIS') {\n          handle.position.copy(this.worldPositionStart)\n          handle.visible = !!this.axis\n\n          if (this.axis === 'X') {\n            this.tempQuaternion.setFromEuler(this.tempEuler.set(0, 0, 0))\n            handle.quaternion.copy(quaternion).multiply(this.tempQuaternion)\n\n            if (Math.abs(this.alignVector.copy(this.unitX).applyQuaternion(quaternion).dot(this.eye)) > 0.9) {\n              handle.visible = false\n            }\n          }\n\n          if (this.axis === 'Y') {\n            this.tempQuaternion.setFromEuler(this.tempEuler.set(0, 0, Math.PI / 2))\n            handle.quaternion.copy(quaternion).multiply(this.tempQuaternion)\n\n            if (Math.abs(this.alignVector.copy(this.unitY).applyQuaternion(quaternion).dot(this.eye)) > 0.9) {\n              handle.visible = false\n            }\n          }\n\n          if (this.axis === 'Z') {\n            this.tempQuaternion.setFromEuler(this.tempEuler.set(0, Math.PI / 2, 0))\n            handle.quaternion.copy(quaternion).multiply(this.tempQuaternion)\n\n            if (Math.abs(this.alignVector.copy(this.unitZ).applyQuaternion(quaternion).dot(this.eye)) > 0.9) {\n              handle.visible = false\n            }\n          }\n\n          if (this.axis === 'XYZE') {\n            this.tempQuaternion.setFromEuler(this.tempEuler.set(0, Math.PI / 2, 0))\n            this.alignVector.copy(this.rotationAxis)\n            handle.quaternion.setFromRotationMatrix(\n              this.lookAtMatrix.lookAt(this.zeroVector, this.alignVector, this.unitY),\n            )\n            handle.quaternion.multiply(this.tempQuaternion)\n            handle.visible = this.dragging\n          }\n\n          if (this.axis === 'E') {\n            handle.visible = false\n          }\n        } else if (handle.name === 'START') {\n          handle.position.copy(this.worldPositionStart)\n          handle.visible = this.dragging\n        } else if (handle.name === 'END') {\n          handle.position.copy(this.worldPosition)\n          handle.visible = this.dragging\n        } else if (handle.name === 'DELTA') {\n          handle.position.copy(this.worldPositionStart)\n          handle.quaternion.copy(this.worldQuaternionStart)\n          this.tempVector\n            .set(1e-10, 1e-10, 1e-10)\n            .add(this.worldPositionStart)\n            .sub(this.worldPosition)\n            .multiplyScalar(-1)\n          this.tempVector.applyQuaternion(this.worldQuaternionStart.clone().invert())\n          handle.scale.copy(this.tempVector)\n          handle.visible = this.dragging\n        } else {\n          handle.quaternion.copy(quaternion)\n\n          if (this.dragging) {\n            handle.position.copy(this.worldPositionStart)\n          } else {\n            handle.position.copy(this.worldPosition)\n          }\n\n          if (this.axis) {\n            handle.visible = this.axis.search(handle.name) !== -1\n          }\n        }\n\n        // If updating helper, skip rest of the loop\n        continue\n      }\n\n      // Align handles to current local or world rotation\n\n      handle.quaternion.copy(quaternion)\n\n      if (this.mode === 'translate' || this.mode === 'scale') {\n        // Hide translate and scale axis facing the camera\n\n        const AXIS_HIDE_TRESHOLD = 0.99\n        const PLANE_HIDE_TRESHOLD = 0.2\n        const AXIS_FLIP_TRESHOLD = 0.0\n\n        if (handle.name === 'X' || handle.name === 'XYZX') {\n          if (\n            Math.abs(this.alignVector.copy(this.unitX).applyQuaternion(quaternion).dot(this.eye)) > AXIS_HIDE_TRESHOLD\n          ) {\n            handle.scale.set(1e-10, 1e-10, 1e-10)\n            handle.visible = false\n          }\n        }\n\n        if (handle.name === 'Y' || handle.name === 'XYZY') {\n          if (\n            Math.abs(this.alignVector.copy(this.unitY).applyQuaternion(quaternion).dot(this.eye)) > AXIS_HIDE_TRESHOLD\n          ) {\n            handle.scale.set(1e-10, 1e-10, 1e-10)\n            handle.visible = false\n          }\n        }\n\n        if (handle.name === 'Z' || handle.name === 'XYZZ') {\n          if (\n            Math.abs(this.alignVector.copy(this.unitZ).applyQuaternion(quaternion).dot(this.eye)) > AXIS_HIDE_TRESHOLD\n          ) {\n            handle.scale.set(1e-10, 1e-10, 1e-10)\n            handle.visible = false\n          }\n        }\n\n        if (handle.name === 'XY') {\n          if (\n            Math.abs(this.alignVector.copy(this.unitZ).applyQuaternion(quaternion).dot(this.eye)) < PLANE_HIDE_TRESHOLD\n          ) {\n            handle.scale.set(1e-10, 1e-10, 1e-10)\n            handle.visible = false\n          }\n        }\n\n        if (handle.name === 'YZ') {\n          if (\n            Math.abs(this.alignVector.copy(this.unitX).applyQuaternion(quaternion).dot(this.eye)) < PLANE_HIDE_TRESHOLD\n          ) {\n            handle.scale.set(1e-10, 1e-10, 1e-10)\n            handle.visible = false\n          }\n        }\n\n        if (handle.name === 'XZ') {\n          if (\n            Math.abs(this.alignVector.copy(this.unitY).applyQuaternion(quaternion).dot(this.eye)) < PLANE_HIDE_TRESHOLD\n          ) {\n            handle.scale.set(1e-10, 1e-10, 1e-10)\n            handle.visible = false\n          }\n        }\n\n        // Flip translate and scale axis ocluded behind another axis\n\n        if (handle.name.search('X') !== -1) {\n          if (this.alignVector.copy(this.unitX).applyQuaternion(quaternion).dot(this.eye) < AXIS_FLIP_TRESHOLD) {\n            if (handle.tag === 'fwd') {\n              handle.visible = false\n            } else {\n              handle.scale.x *= -1\n            }\n          } else if (handle.tag === 'bwd') {\n            handle.visible = false\n          }\n        }\n\n        if (handle.name.search('Y') !== -1) {\n          if (this.alignVector.copy(this.unitY).applyQuaternion(quaternion).dot(this.eye) < AXIS_FLIP_TRESHOLD) {\n            if (handle.tag === 'fwd') {\n              handle.visible = false\n            } else {\n              handle.scale.y *= -1\n            }\n          } else if (handle.tag === 'bwd') {\n            handle.visible = false\n          }\n        }\n\n        if (handle.name.search('Z') !== -1) {\n          if (this.alignVector.copy(this.unitZ).applyQuaternion(quaternion).dot(this.eye) < AXIS_FLIP_TRESHOLD) {\n            if (handle.tag === 'fwd') {\n              handle.visible = false\n            } else {\n              handle.scale.z *= -1\n            }\n          } else if (handle.tag === 'bwd') {\n            handle.visible = false\n          }\n        }\n      } else if (this.mode === 'rotate') {\n        // Align handles to current local or world rotation\n\n        this.tempQuaternion2.copy(quaternion)\n        this.alignVector.copy(this.eye).applyQuaternion(this.tempQuaternion.copy(quaternion).invert())\n\n        if (handle.name.search('E') !== -1) {\n          handle.quaternion.setFromRotationMatrix(this.lookAtMatrix.lookAt(this.eye, this.zeroVector, this.unitY))\n        }\n\n        if (handle.name === 'X') {\n          this.tempQuaternion.setFromAxisAngle(this.unitX, Math.atan2(-this.alignVector.y, this.alignVector.z))\n          this.tempQuaternion.multiplyQuaternions(this.tempQuaternion2, this.tempQuaternion)\n          handle.quaternion.copy(this.tempQuaternion)\n        }\n\n        if (handle.name === 'Y') {\n          this.tempQuaternion.setFromAxisAngle(this.unitY, Math.atan2(this.alignVector.x, this.alignVector.z))\n          this.tempQuaternion.multiplyQuaternions(this.tempQuaternion2, this.tempQuaternion)\n          handle.quaternion.copy(this.tempQuaternion)\n        }\n\n        if (handle.name === 'Z') {\n          this.tempQuaternion.setFromAxisAngle(this.unitZ, Math.atan2(this.alignVector.y, this.alignVector.x))\n          this.tempQuaternion.multiplyQuaternions(this.tempQuaternion2, this.tempQuaternion)\n          handle.quaternion.copy(this.tempQuaternion)\n        }\n      }\n\n      // Hide disabled axes\n      handle.visible = handle.visible && (handle.name.indexOf('X') === -1 || this.showX)\n      handle.visible = handle.visible && (handle.name.indexOf('Y') === -1 || this.showY)\n      handle.visible = handle.visible && (handle.name.indexOf('Z') === -1 || this.showZ)\n      handle.visible = handle.visible && (handle.name.indexOf('E') === -1 || (this.showX && this.showY && this.showZ))\n\n      // highlight selected axis\n\n      //@ts-ignore\n      handle.material.tempOpacity = handle.material.tempOpacity || handle.material.opacity\n      //@ts-ignore\n      handle.material.tempColor = handle.material.tempColor || handle.material.color.clone()\n      //@ts-ignore\n      handle.material.color.copy(handle.material.tempColor)\n      //@ts-ignore\n      handle.material.opacity = handle.material.tempOpacity\n\n      if (!this.enabled) {\n        //@ts-ignore\n        handle.material.opacity *= 0.5\n        //@ts-ignore\n        handle.material.color.lerp(new Color(1, 1, 1), 0.5)\n      } else if (this.axis) {\n        if (handle.name === this.axis) {\n          //@ts-ignore\n          handle.material.opacity = 1.0\n          //@ts-ignore\n          handle.material.color.lerp(new Color(1, 1, 1), 0.5)\n        } else if (\n          this.axis.split('').some(function (a) {\n            return handle.name === a\n          })\n        ) {\n          //@ts-ignore\n          handle.material.opacity = 1.0\n          //@ts-ignore\n          handle.material.color.lerp(new Color(1, 1, 1), 0.5)\n        } else {\n          //@ts-ignore\n          handle.material.opacity *= 0.25\n          //@ts-ignore\n          handle.material.color.lerp(new Color(1, 1, 1), 0.5)\n        }\n      }\n    }\n\n    super.updateMatrixWorld()\n  }\n}\n\nclass TransformControlsPlane extends Mesh<PlaneGeometry, MeshBasicMaterial> {\n  private isTransformControlsPlane = true\n  public type = 'TransformControlsPlane'\n\n  constructor() {\n    super(\n      new PlaneGeometry(100000, 100000, 2, 2),\n      new MeshBasicMaterial({\n        visible: false,\n        wireframe: true,\n        side: DoubleSide,\n        transparent: true,\n        opacity: 0.1,\n        toneMapped: false,\n      }),\n    )\n  }\n\n  private unitX = new Vector3(1, 0, 0)\n  private unitY = new Vector3(0, 1, 0)\n  private unitZ = new Vector3(0, 0, 1)\n\n  private tempVector = new Vector3()\n  private dirVector = new Vector3()\n  private alignVector = new Vector3()\n  private tempMatrix = new Matrix4()\n  private identityQuaternion = new Quaternion()\n\n  // these are set from parent class TransformControls\n  private cameraQuaternion = new Quaternion()\n\n  private worldPosition = new Vector3()\n  private worldQuaternion = new Quaternion()\n\n  private eye = new Vector3()\n\n  private axis: string | null = null\n  private mode: 'translate' | 'rotate' | 'scale' = 'translate'\n  private space = 'world'\n\n  public updateMatrixWorld = (): void => {\n    let space = this.space\n\n    this.position.copy(this.worldPosition)\n\n    if (this.mode === 'scale') space = 'local' // scale always oriented to local rotation\n\n    this.unitX.set(1, 0, 0).applyQuaternion(space === 'local' ? this.worldQuaternion : this.identityQuaternion)\n    this.unitY.set(0, 1, 0).applyQuaternion(space === 'local' ? this.worldQuaternion : this.identityQuaternion)\n    this.unitZ.set(0, 0, 1).applyQuaternion(space === 'local' ? this.worldQuaternion : this.identityQuaternion)\n\n    // Align the plane for current transform mode, axis and space.\n\n    this.alignVector.copy(this.unitY)\n\n    switch (this.mode) {\n      case 'translate':\n      case 'scale':\n        switch (this.axis) {\n          case 'X':\n            this.alignVector.copy(this.eye).cross(this.unitX)\n            this.dirVector.copy(this.unitX).cross(this.alignVector)\n            break\n          case 'Y':\n            this.alignVector.copy(this.eye).cross(this.unitY)\n            this.dirVector.copy(this.unitY).cross(this.alignVector)\n            break\n          case 'Z':\n            this.alignVector.copy(this.eye).cross(this.unitZ)\n            this.dirVector.copy(this.unitZ).cross(this.alignVector)\n            break\n          case 'XY':\n            this.dirVector.copy(this.unitZ)\n            break\n          case 'YZ':\n            this.dirVector.copy(this.unitX)\n            break\n          case 'XZ':\n            this.alignVector.copy(this.unitZ)\n            this.dirVector.copy(this.unitY)\n            break\n          case 'XYZ':\n          case 'E':\n            this.dirVector.set(0, 0, 0)\n            break\n        }\n\n        break\n      case 'rotate':\n      default:\n        // special case for rotate\n        this.dirVector.set(0, 0, 0)\n    }\n\n    if (this.dirVector.length() === 0) {\n      // If in rotate mode, make the plane parallel to camera\n      this.quaternion.copy(this.cameraQuaternion)\n    } else {\n      this.tempMatrix.lookAt(this.tempVector.set(0, 0, 0), this.dirVector, this.alignVector)\n\n      this.quaternion.setFromRotationMatrix(this.tempMatrix)\n    }\n\n    super.updateMatrixWorld()\n  }\n}\n\nexport { TransformControls, TransformControlsGizmo, TransformControlsPlane }\n"], "mappings": ";;;;;;;;;;;;AAmCA,MAAMA,iBAAA,SAA2DC,QAAA,CAAS;EA0ExEC,YAAYC,MAAA,EAAiBC,UAAA,EAAqC;IAC1D;IA1EQC,aAAA,8BAAsB;IAE/BA,aAAA,kBAAU;IAETA,aAAA;IAEAA,aAAA,oBAAY,IAAIC,SAAA;IAEhBD,aAAA;IACAA,aAAA;IAEAA,aAAA,qBAAa,IAAIE,OAAA;IACjBF,aAAA,sBAAc,IAAIE,OAAA;IAClBF,aAAA,yBAAiB,IAAIG,UAAA;IACrBH,aAAA,eAAO;MACbI,CAAA,EAAG,IAAIF,OAAA,CAAQ,GAAG,GAAG,CAAC;MACtBG,CAAA,EAAG,IAAIH,OAAA,CAAQ,GAAG,GAAG,CAAC;MACtBI,CAAA,EAAG,IAAIJ,OAAA,CAAQ,GAAG,GAAG,CAAC;IAAA;IAGhBF,aAAA,qBAAa,IAAIE,OAAA;IACjBF,aAAA,mBAAW,IAAIE,OAAA;IACfF,aAAA,iBAAS,IAAIE,OAAA;IACbF,aAAA,uBAAe,IAAIE,OAAA;IACnBF,aAAA,oBAAY,IAAIE,OAAA;IAChBF,aAAA,kBAAU,IAAIE,OAAA;IACdF,aAAA,wBAAgB;IAEhBA,aAAA,yBAAiB,IAAIE,OAAA;IACrBF,aAAA,2BAAmB,IAAIG,UAAA;IACvBH,aAAA,sBAAc,IAAIE,OAAA;IAElBF,aAAA,yBAAiB,IAAIE,OAAA;IACrBF,aAAA,2BAAmB,IAAIG,UAAA;IACvBH,aAAA,8BAAsB,IAAIG,UAAA;IAC1BH,aAAA,sBAAc,IAAIE,OAAA;IAElBF,aAAA,6BAAqB,IAAIE,OAAA;IACzBF,aAAA,+BAAuB,IAAIG,UAAA;IAC3BH,aAAA,0BAAkB,IAAIE,OAAA;IAEtBF,aAAA,wBAAgB,IAAIE,OAAA;IACpBF,aAAA,0BAAkB,IAAIG,UAAA;IACtBH,aAAA,6BAAqB,IAAIG,UAAA;IACzBH,aAAA,qBAAa,IAAIE,OAAA;IAEjBF,aAAA,cAAM,IAAIE,OAAA;IAEVF,aAAA,wBAAgB,IAAIE,OAAA;IACpBF,aAAA,0BAAkB,IAAIG,UAAA;IACtBH,aAAA,qBAAa,IAAIE,OAAA;IAEjBF,aAAA;IACAA,aAAA;IACAA,aAAA,kBAAU;IACVA,aAAA,eAAsB;IACtBA,aAAA,eAAyC;IACzCA,aAAA,0BAAiC;IACjCA,aAAA,uBAA8B;IAC9BA,aAAA,oBAA2B;IAC3BA,aAAA,gBAAQ;IACRA,aAAA,eAAO;IACPA,aAAA,mBAAW;IACXA,aAAA,gBAAQ;IACRA,aAAA,gBAAQ;IACRA,aAAA,gBAAQ;IAGR;IAAAA,aAAA,sBAAc;MAAEO,IAAA,EAAM;IAAA;IACtBP,aAAA,yBAAiB;MAAEO,IAAA,EAAM;MAAaC,IAAA,EAAM,KAAKA;IAAA;IACjDR,aAAA,uBAAe;MAAEO,IAAA,EAAM;MAAWC,IAAA,EAAM,KAAKA;IAAA;IAC7CR,aAAA,4BAAoB;MAAEO,IAAA,EAAM;IAAA;IAyE5BP,aAAA,iCAAyB,CAC/BS,MAAA,EACAC,SAAA,EACAC,gBAAA,KACyB;MACzB,MAAMC,gBAAA,GAAmBF,SAAA,CAAUG,eAAA,CAAgBJ,MAAA,EAAQ,IAAI;MAE/D,SAASK,CAAA,GAAI,GAAGA,CAAA,GAAIF,gBAAA,CAAiBG,MAAA,EAAQD,CAAA,IAAK;QAChD,IAAIF,gBAAA,CAAiBE,CAAC,EAAEL,MAAA,CAAOO,OAAA,IAAWL,gBAAA,EAAkB;UAC1D,OAAOC,gBAAA,CAAiBE,CAAC;QAC3B;MACF;MAEO;IAAA;IAIF;IAAAd,aAAA,iBAAUS,MAAA,IAA2B;MAC1C,KAAKA,MAAA,GAASA,MAAA;MACd,KAAKO,OAAA,GAAU;MAER;IAAA;IAIF;IAAAhB,aAAA,iBAAS,MAAY;MAC1B,KAAKS,MAAA,GAAS;MACd,KAAKO,OAAA,GAAU;MACf,KAAKC,IAAA,GAAO;MAEL;IAAA;IAIF;IAAAjB,aAAA,gBAAQ,MAAY;MACzB,IAAI,CAAC,KAAKkB,OAAA,EAAgB;MAE1B,IAAI,KAAKC,QAAA,EAAU;QACb,SAAKV,MAAA,KAAW,QAAW;UAC7B,KAAKA,MAAA,CAAOW,QAAA,CAASC,IAAA,CAAK,KAAKC,aAAa;UAC5C,KAAKb,MAAA,CAAOc,UAAA,CAAWF,IAAA,CAAK,KAAKG,eAAe;UAChD,KAAKf,MAAA,CAAOgB,KAAA,CAAMJ,IAAA,CAAK,KAAKK,UAAU;UAEjC,KAAAC,aAAA,CAAc,KAAKC,WAAW;UAE9B,KAAAD,aAAA,CAAc,KAAKE,iBAAiB;UACpC,KAAAC,UAAA,CAAWT,IAAA,CAAK,KAAKU,QAAQ;QACpC;MACF;MAEO;IAAA;IAGF/B,aAAA,4BAAoB,MAAY;MACjC,SAAKS,MAAA,KAAW,QAAW;QAC7B,KAAKA,MAAA,CAAOuB,iBAAA;QAER,SAAKvB,MAAA,CAAOwB,MAAA,KAAW,MAAM;UAC/BC,OAAA,CAAQC,KAAA,CAAM,8EAA8E;QAAA,OACvF;UACA,KAAA1B,MAAA,CAAOwB,MAAA,CAAOG,WAAA,CAAYC,SAAA,CAAU,KAAKC,cAAA,EAAgB,KAAKC,gBAAA,EAAkB,KAAKC,WAAW;QACvG;QAEK,KAAA/B,MAAA,CAAO2B,WAAA,CAAYC,SAAA,CAAU,KAAKI,aAAA,EAAe,KAAKC,eAAA,EAAiB,KAAKC,UAAU;QAE3F,KAAKC,mBAAA,CAAoBvB,IAAA,CAAK,KAAKkB,gBAAgB,EAAEM,MAAA;QACrD,KAAKC,kBAAA,CAAmBzB,IAAA,CAAK,KAAKqB,eAAe,EAAEG,MAAA;MACrD;MAEA,KAAK/C,MAAA,CAAOkC,iBAAA;MACP,KAAAlC,MAAA,CAAOsC,WAAA,CAAYC,SAAA,CAAU,KAAKU,cAAA,EAAgB,KAAKC,gBAAA,EAAkB,KAAKC,WAAW;MAEzF,KAAAC,GAAA,CAAI7B,IAAA,CAAK,KAAK0B,cAAc,EAAEI,GAAA,CAAI,KAAKV,aAAa,EAAEW,SAAA;MAE3D,MAAMpB,iBAAA,CAAkB;IAAA;IAGlBhC,aAAA,uBAAgBqD,OAAA,IAAkD;MACxE,IAAI,KAAK5C,MAAA,KAAW,UAAa,KAAKU,QAAA,KAAa,MAAM;MAEzD,KAAKT,SAAA,CAAU4C,aAAA,CAAeD,OAAA,EAAgC,KAAKvD,MAAM;MAEnE,MAAAyD,SAAA,GAAY,KAAKC,sBAAA,CAAuB,KAAKC,KAAA,CAAMC,MAAA,CAAO,KAAKlD,IAAI,GAAG,KAAKE,SAAS;MAE1F,IAAI6C,SAAA,EAAW;QACR,KAAAtC,IAAA,GAAOsC,SAAA,CAAU9C,MAAA,CAAOkD,IAAA;MAAA,OACxB;QACL,KAAK1C,IAAA,GAAO;MACd;IAAA;IAGMjB,aAAA,sBAAeqD,OAAA,IAAkD;MACvE,IAAI,KAAK5C,MAAA,KAAW,UAAa,KAAKU,QAAA,KAAa,QAAQkC,OAAA,CAAQO,MAAA,KAAW,GAAG;MAE7E,SAAK3C,IAAA,KAAS,MAAM;QACtB,KAAKP,SAAA,CAAU4C,aAAA,CAAeD,OAAA,EAAgC,KAAKvD,MAAM;QAEzE,MAAM+D,cAAA,GAAiB,KAAKL,sBAAA,CAAuB,KAAKM,KAAA,EAAO,KAAKpD,SAAA,EAAW,IAAI;QAEnF,IAAImD,cAAA,EAAgB;UAClB,IAAIE,KAAA,GAAQ,KAAKA,KAAA;UAEb,SAAKvD,IAAA,KAAS,SAAS;YACjBuD,KAAA;UAAA,WACC,KAAK9C,IAAA,KAAS,OAAO,KAAKA,IAAA,KAAS,UAAU,KAAKA,IAAA,KAAS,OAAO;YACnE8C,KAAA;UACV;UAEA,IAAIA,KAAA,KAAU,WAAW,KAAKvD,IAAA,KAAS,UAAU;YAC/C,MAAMwD,IAAA,GAAO,KAAKC,YAAA;YAEd,SAAKhD,IAAA,KAAS,OAAO+C,IAAA,EAAW,KAAAvD,MAAA,CAAOyD,QAAA,CAASC,CAAA,GAAIC,IAAA,CAAKC,KAAA,CAAM,KAAK5D,MAAA,CAAOyD,QAAA,CAASC,CAAA,GAAIH,IAAI,IAAIA,IAAA;YAChG,SAAK/C,IAAA,KAAS,OAAO+C,IAAA,EAAW,KAAAvD,MAAA,CAAOyD,QAAA,CAASI,CAAA,GAAIF,IAAA,CAAKC,KAAA,CAAM,KAAK5D,MAAA,CAAOyD,QAAA,CAASI,CAAA,GAAIN,IAAI,IAAIA,IAAA;YAChG,SAAK/C,IAAA,KAAS,OAAO+C,IAAA,EAAW,KAAAvD,MAAA,CAAOyD,QAAA,CAASK,CAAA,GAAIH,IAAA,CAAKC,KAAA,CAAM,KAAK5D,MAAA,CAAOyD,QAAA,CAASK,CAAA,GAAIP,IAAI,IAAIA,IAAA;UACtG;UAEA,KAAKvD,MAAA,CAAOuB,iBAAA;UAER,SAAKvB,MAAA,CAAOwB,MAAA,EAAQ;YACjB,KAAAxB,MAAA,CAAOwB,MAAA,CAAOD,iBAAA;UACrB;UAEA,KAAKV,aAAA,CAAcD,IAAA,CAAK,KAAKZ,MAAA,CAAOW,QAAQ;UAC5C,KAAKI,eAAA,CAAgBH,IAAA,CAAK,KAAKZ,MAAA,CAAOc,UAAU;UAChD,KAAKG,UAAA,CAAWL,IAAA,CAAK,KAAKZ,MAAA,CAAOgB,KAAK;UAEjC,KAAAhB,MAAA,CAAO2B,WAAA,CAAYC,SAAA,CAAU,KAAKmC,kBAAA,EAAoB,KAAKC,oBAAA,EAAsB,KAAKC,eAAe;UAE1G,KAAK5C,UAAA,CAAWT,IAAA,CAAKwC,cAAA,CAAec,KAAK,EAAExB,GAAA,CAAI,KAAKqB,kBAAkB;QACxE;QAEA,KAAKrD,QAAA,GAAW;QACX,KAAAyD,cAAA,CAAepE,IAAA,GAAO,KAAKA,IAAA;QAE3B,KAAAmB,aAAA,CAAc,KAAKiD,cAAc;MACxC;IAAA;IAGM5E,aAAA,sBAAeqD,OAAA,IAAkD;MACvE,MAAMpC,IAAA,GAAO,KAAKA,IAAA;MAClB,MAAMT,IAAA,GAAO,KAAKA,IAAA;MAClB,MAAMC,MAAA,GAAS,KAAKA,MAAA;MACpB,IAAIsD,KAAA,GAAQ,KAAKA,KAAA;MAEjB,IAAIvD,IAAA,KAAS,SAAS;QACZuD,KAAA;MAAA,WACC9C,IAAA,KAAS,OAAOA,IAAA,KAAS,UAAUA,IAAA,KAAS,OAAO;QACpD8C,KAAA;MACV;MAEI,IAAAtD,MAAA,KAAW,UAAaQ,IAAA,KAAS,QAAQ,KAAKE,QAAA,KAAa,SAASkC,OAAA,CAAQO,MAAA,KAAW,IAAI;MAE/F,KAAKlD,SAAA,CAAU4C,aAAA,CAAeD,OAAA,EAAgC,KAAKvD,MAAM;MAEzE,MAAM+D,cAAA,GAAiB,KAAKL,sBAAA,CAAuB,KAAKM,KAAA,EAAO,KAAKpD,SAAA,EAAW,IAAI;MAEnF,IAAI,CAACmD,cAAA,EAAgB;MAErB,KAAK9B,QAAA,CAASV,IAAA,CAAKwC,cAAA,CAAec,KAAK,EAAExB,GAAA,CAAI,KAAKqB,kBAAkB;MAEpE,IAAIhE,IAAA,KAAS,aAAa;QAGxB,KAAKqE,MAAA,CAAOxD,IAAA,CAAK,KAAKU,QAAQ,EAAEoB,GAAA,CAAI,KAAKrB,UAAU;QAE/C,IAAAiC,KAAA,KAAU,WAAW9C,IAAA,KAAS,OAAO;UAClC,KAAA4D,MAAA,CAAOC,eAAA,CAAgB,KAAKhC,kBAAkB;QACrD;QAEI,IAAA7B,IAAA,CAAK8D,OAAA,CAAQ,GAAG,MAAM,IAAI,KAAKF,MAAA,CAAOV,CAAA,GAAI;QAC1C,IAAAlD,IAAA,CAAK8D,OAAA,CAAQ,GAAG,MAAM,IAAI,KAAKF,MAAA,CAAOP,CAAA,GAAI;QAC1C,IAAArD,IAAA,CAAK8D,OAAA,CAAQ,GAAG,MAAM,IAAI,KAAKF,MAAA,CAAON,CAAA,GAAI;QAE1C,IAAAR,KAAA,KAAU,WAAW9C,IAAA,KAAS,OAAO;UACvC,KAAK4D,MAAA,CAAOC,eAAA,CAAgB,KAAKtD,eAAe,EAAEwD,MAAA,CAAO,KAAKxC,WAAW;QAAA,OACpE;UACL,KAAKqC,MAAA,CAAOC,eAAA,CAAgB,KAAKlC,mBAAmB,EAAEoC,MAAA,CAAO,KAAKxC,WAAW;QAC/E;QAEA/B,MAAA,CAAOW,QAAA,CAASC,IAAA,CAAK,KAAKwD,MAAM,EAAEI,GAAA,CAAI,KAAK3D,aAAa;QAIxD,IAAI,KAAK4D,eAAA,EAAiB;UACxB,IAAInB,KAAA,KAAU,SAAS;YACdtD,MAAA,CAAAW,QAAA,CAAS0D,eAAA,CAAgB,KAAKK,cAAA,CAAe9D,IAAA,CAAK,KAAKG,eAAe,EAAEqB,MAAA,EAAQ;YAEvF,IAAI5B,IAAA,CAAKmE,MAAA,CAAO,GAAG,MAAM,IAAI;cACpB3E,MAAA,CAAAW,QAAA,CAAS+C,CAAA,GAAIC,IAAA,CAAKC,KAAA,CAAM5D,MAAA,CAAOW,QAAA,CAAS+C,CAAA,GAAI,KAAKe,eAAe,IAAI,KAAKA,eAAA;YAClF;YAEA,IAAIjE,IAAA,CAAKmE,MAAA,CAAO,GAAG,MAAM,IAAI;cACpB3E,MAAA,CAAAW,QAAA,CAASkD,CAAA,GAAIF,IAAA,CAAKC,KAAA,CAAM5D,MAAA,CAAOW,QAAA,CAASkD,CAAA,GAAI,KAAKY,eAAe,IAAI,KAAKA,eAAA;YAClF;YAEA,IAAIjE,IAAA,CAAKmE,MAAA,CAAO,GAAG,MAAM,IAAI;cACpB3E,MAAA,CAAAW,QAAA,CAASmD,CAAA,GAAIH,IAAA,CAAKC,KAAA,CAAM5D,MAAA,CAAOW,QAAA,CAASmD,CAAA,GAAI,KAAKW,eAAe,IAAI,KAAKA,eAAA;YAClF;YAEOzE,MAAA,CAAAW,QAAA,CAAS0D,eAAA,CAAgB,KAAKtD,eAAe;UACtD;UAEA,IAAIuC,KAAA,KAAU,SAAS;YACrB,IAAItD,MAAA,CAAOwB,MAAA,EAAQ;cACVxB,MAAA,CAAAW,QAAA,CAAS6D,GAAA,CAAI,KAAKI,UAAA,CAAWC,qBAAA,CAAsB7E,MAAA,CAAOwB,MAAA,CAAOG,WAAW,CAAC;YACtF;YAEA,IAAInB,IAAA,CAAKmE,MAAA,CAAO,GAAG,MAAM,IAAI;cACpB3E,MAAA,CAAAW,QAAA,CAAS+C,CAAA,GAAIC,IAAA,CAAKC,KAAA,CAAM5D,MAAA,CAAOW,QAAA,CAAS+C,CAAA,GAAI,KAAKe,eAAe,IAAI,KAAKA,eAAA;YAClF;YAEA,IAAIjE,IAAA,CAAKmE,MAAA,CAAO,GAAG,MAAM,IAAI;cACpB3E,MAAA,CAAAW,QAAA,CAASkD,CAAA,GAAIF,IAAA,CAAKC,KAAA,CAAM5D,MAAA,CAAOW,QAAA,CAASkD,CAAA,GAAI,KAAKY,eAAe,IAAI,KAAKA,eAAA;YAClF;YAEA,IAAIjE,IAAA,CAAKmE,MAAA,CAAO,GAAG,MAAM,IAAI;cACpB3E,MAAA,CAAAW,QAAA,CAASmD,CAAA,GAAIH,IAAA,CAAKC,KAAA,CAAM5D,MAAA,CAAOW,QAAA,CAASmD,CAAA,GAAI,KAAKW,eAAe,IAAI,KAAKA,eAAA;YAClF;YAEA,IAAIzE,MAAA,CAAOwB,MAAA,EAAQ;cACVxB,MAAA,CAAAW,QAAA,CAAS+B,GAAA,CAAI,KAAKkC,UAAA,CAAWC,qBAAA,CAAsB7E,MAAA,CAAOwB,MAAA,CAAOG,WAAW,CAAC;YACtF;UACF;QACF;MAAA,WACS5B,IAAA,KAAS,SAAS;QAC3B,IAAIS,IAAA,CAAKmE,MAAA,CAAO,KAAK,MAAM,IAAI;UAC7B,IAAIG,CAAA,GAAI,KAAKxD,QAAA,CAAShB,MAAA,CAAW,SAAKe,UAAA,CAAWf,MAAA;UAEjD,IAAI,KAAKgB,QAAA,CAASyD,GAAA,CAAI,KAAK1D,UAAU,IAAI,GAAQyD,CAAA;UAEjD,KAAKE,WAAA,CAAYC,GAAA,CAAIH,CAAA,EAAGA,CAAA,EAAGA,CAAC;QAAA,OACvB;UACA,KAAAF,UAAA,CAAWhE,IAAA,CAAK,KAAKS,UAAU;UAC/B,KAAA2D,WAAA,CAAYpE,IAAA,CAAK,KAAKU,QAAQ;UAE9B,KAAAsD,UAAA,CAAWP,eAAA,CAAgB,KAAKhC,kBAAkB;UAClD,KAAA2C,WAAA,CAAYX,eAAA,CAAgB,KAAKhC,kBAAkB;UAEnD,KAAA2C,WAAA,CAAYT,MAAA,CAAO,KAAKK,UAAU;UAEvC,IAAIpE,IAAA,CAAKmE,MAAA,CAAO,GAAG,MAAM,IAAI;YAC3B,KAAKK,WAAA,CAAYtB,CAAA,GAAI;UACvB;UAEA,IAAIlD,IAAA,CAAKmE,MAAA,CAAO,GAAG,MAAM,IAAI;YAC3B,KAAKK,WAAA,CAAYnB,CAAA,GAAI;UACvB;UAEA,IAAIrD,IAAA,CAAKmE,MAAA,CAAO,GAAG,MAAM,IAAI;YAC3B,KAAKK,WAAA,CAAYlB,CAAA,GAAI;UACvB;QACF;QAIA9D,MAAA,CAAOgB,KAAA,CAAMJ,IAAA,CAAK,KAAKK,UAAU,EAAEiE,QAAA,CAAS,KAAKF,WAAW;QAExD,SAAKG,SAAA,IAAa,KAAKnF,MAAA,EAAQ;UACjC,IAAIQ,IAAA,CAAKmE,MAAA,CAAO,GAAG,MAAM,IAAI;YAC3B,KAAK3E,MAAA,CAAOgB,KAAA,CAAM0C,CAAA,GAAIC,IAAA,CAAKC,KAAA,CAAM5D,MAAA,CAAOgB,KAAA,CAAM0C,CAAA,GAAI,KAAKyB,SAAS,IAAI,KAAKA,SAAA,IAAa,KAAKA,SAAA;UAC7F;UAEA,IAAI3E,IAAA,CAAKmE,MAAA,CAAO,GAAG,MAAM,IAAI;YAC3B3E,MAAA,CAAOgB,KAAA,CAAM6C,CAAA,GAAIF,IAAA,CAAKC,KAAA,CAAM5D,MAAA,CAAOgB,KAAA,CAAM6C,CAAA,GAAI,KAAKsB,SAAS,IAAI,KAAKA,SAAA,IAAa,KAAKA,SAAA;UACxF;UAEA,IAAI3E,IAAA,CAAKmE,MAAA,CAAO,GAAG,MAAM,IAAI;YAC3B3E,MAAA,CAAOgB,KAAA,CAAM8C,CAAA,GAAIH,IAAA,CAAKC,KAAA,CAAM5D,MAAA,CAAOgB,KAAA,CAAM8C,CAAA,GAAI,KAAKqB,SAAS,IAAI,KAAKA,SAAA,IAAa,KAAKA,SAAA;UACxF;QACF;MAAA,WACSpF,IAAA,KAAS,UAAU;QAC5B,KAAKqE,MAAA,CAAOxD,IAAA,CAAK,KAAKU,QAAQ,EAAEoB,GAAA,CAAI,KAAKrB,UAAU;QAE7C,MAAA+D,cAAA,GACJ,KAAK,KAAKpD,aAAA,CAAcqD,UAAA,CAAW,KAAKT,UAAA,CAAWC,qBAAA,CAAsB,KAAKxF,MAAA,CAAOsC,WAAW,CAAC;QAEnG,IAAInB,IAAA,KAAS,KAAK;UACX,KAAA8E,YAAA,CAAa1E,IAAA,CAAK,KAAK6B,GAAG;UAC/B,KAAK8C,aAAA,GAAgB,KAAKjE,QAAA,CAASkE,OAAA,CAAQ,KAAKnE,UAAU;UAE1D,KAAKoE,SAAA,CAAU7E,IAAA,CAAK,KAAKS,UAAU,EAAEsB,SAAA;UACrC,KAAK+C,OAAA,CAAQ9E,IAAA,CAAK,KAAKU,QAAQ,EAAEqB,SAAA;UAEjC,KAAK4C,aAAA,IAAiB,KAAKG,OAAA,CAAQC,KAAA,CAAM,KAAKF,SAAS,EAAEV,GAAA,CAAI,KAAKtC,GAAG,IAAI,IAAI,IAAI;QAAA,WACxEjC,IAAA,KAAS,QAAQ;UACrB,KAAA8E,YAAA,CAAa1E,IAAA,CAAK,KAAKwD,MAAM,EAAEuB,KAAA,CAAM,KAAKlD,GAAG,EAAEE,SAAA;UACpD,KAAK4C,aAAA,GAAgB,KAAKnB,MAAA,CAAOW,GAAA,CAAI,KAAKH,UAAA,CAAWhE,IAAA,CAAK,KAAK0E,YAAY,EAAEK,KAAA,CAAM,KAAKlD,GAAG,CAAC,IAAI2C,cAAA;QAAA,WACvF5E,IAAA,KAAS,OAAOA,IAAA,KAAS,OAAOA,IAAA,KAAS,KAAK;UACvD,KAAK8E,YAAA,CAAa1E,IAAA,CAAK,KAAKgF,IAAA,CAAKpF,IAAI,CAAC;UAEtC,KAAKoE,UAAA,CAAWhE,IAAA,CAAK,KAAKgF,IAAA,CAAKpF,IAAI,CAAC;UAEpC,IAAI8C,KAAA,KAAU,SAAS;YAChB,KAAAsB,UAAA,CAAWP,eAAA,CAAgB,KAAKpC,eAAe;UACtD;UAEA,KAAKsD,aAAA,GAAgB,KAAKnB,MAAA,CAAOW,GAAA,CAAI,KAAKH,UAAA,CAAWe,KAAA,CAAM,KAAKlD,GAAG,EAAEE,SAAA,CAAW,KAAIyC,cAAA;QACtF;QAIA,IAAI,KAAK5B,YAAA,EAAc;UAChB,KAAA+B,aAAA,GAAgB5B,IAAA,CAAKC,KAAA,CAAM,KAAK2B,aAAA,GAAgB,KAAK/B,YAAY,IAAI,KAAKA,YAAA;QACjF;QAGA,IAAIF,KAAA,KAAU,WAAW9C,IAAA,KAAS,OAAOA,IAAA,KAAS,QAAQ;UACjDR,MAAA,CAAAc,UAAA,CAAWF,IAAA,CAAK,KAAKG,eAAe;UACpCf,MAAA,CAAAc,UAAA,CACJoE,QAAA,CAAS,KAAKR,cAAA,CAAemB,gBAAA,CAAiB,KAAKP,YAAA,EAAc,KAAKC,aAAa,CAAC,EACpF5C,SAAA,CAAU;QAAA,OACR;UACA,KAAA2C,YAAA,CAAajB,eAAA,CAAgB,KAAKlC,mBAAmB;UACnDnC,MAAA,CAAAc,UAAA,CAAWF,IAAA,CAAK,KAAK8D,cAAA,CAAemB,gBAAA,CAAiB,KAAKP,YAAA,EAAc,KAAKC,aAAa,CAAC;UAClGvF,MAAA,CAAOc,UAAA,CAAWoE,QAAA,CAAS,KAAKnE,eAAe,EAAE4B,SAAA;QACnD;MACF;MAGK,KAAAzB,aAAA,CAAc,KAAKC,WAAW;MAE9B,KAAAD,aAAA,CAAc,KAAKE,iBAAiB;IAAA;IAGnC7B,aAAA,oBAAaqD,OAAA,IAAkD;MACrE,IAAIA,OAAA,CAAQO,MAAA,KAAW,GAAG;MAE1B,IAAI,KAAKzC,QAAA,IAAY,KAAKF,IAAA,KAAS,MAAM;QAClC,KAAAsF,YAAA,CAAa/F,IAAA,GAAO,KAAKA,IAAA;QAEzB,KAAAmB,aAAA,CAAc,KAAK4E,YAAY;MACtC;MAEA,KAAKpF,QAAA,GAAW;MAChB,KAAKF,IAAA,GAAO;IAAA;IAGNjB,aAAA,qBAAcwG,KAAA,IAAiD;;MACrE,IAAI,KAAKzG,UAAA,MAAc0G,EAAA,QAAK1G,UAAA,CAAW2G,aAAA,KAAhB,gBAAAD,EAAA,CAA+BE,kBAAA,GAAoB;QACjE;UACLxC,CAAA,EAAG;UACHG,CAAA,EAAG;UACHV,MAAA,EAAS4C,KAAA,CAAqB5C;QAAA;MAChC,OACK;QACL,MAAMP,OAAA,GAAWmD,KAAA,CAAqBI,cAAA,GACjCJ,KAAA,CAAqBI,cAAA,CAAe,CAAC,IACrCJ,KAAA;QAEC,MAAAK,IAAA,GAAO,KAAK9G,UAAA,CAAY+G,qBAAA,CAAsB;QAE7C;UACL3C,CAAA,GAAKd,OAAA,CAAQ0D,OAAA,GAAUF,IAAA,CAAKG,IAAA,IAAQH,IAAA,CAAKI,KAAA,GAAS,IAAI;UACtD3C,CAAA,EAAI,EAAEjB,OAAA,CAAQ6D,OAAA,GAAUL,IAAA,CAAKM,GAAA,IAAON,IAAA,CAAKO,MAAA,GAAU,IAAI;UACvDxD,MAAA,EAAS4C,KAAA,CAAqB5C;QAAA;MAElC;IAAA;IAGM5D,aAAA,yBAAkBwG,KAAA,IAAuB;MAC/C,IAAI,CAAC,KAAKtF,OAAA,EAAS;MAEnB,QAASsF,KAAA,CAAuBa,WAAA;QAC9B,KAAK;QACL,KAAK;UACH,KAAKC,YAAA,CAAa,KAAKC,UAAA,CAAWf,KAAK,CAAC;UACxC;MACJ;IAAA;IAGMxG,aAAA,wBAAiBwG,KAAA,IAAuB;MAC9C,IAAI,CAAC,KAAKtF,OAAA,IAAW,CAAC,KAAKnB,UAAA,EAAY;MAElC,KAAAA,UAAA,CAAWyH,KAAA,CAAMC,WAAA,GAAc;MACpC,KAAK1H,UAAA,CAAW2G,aAAA,CAAcgB,gBAAA,CAAiB,eAAe,KAAKC,aAAa;MAChF,KAAKL,YAAA,CAAa,KAAKC,UAAA,CAAWf,KAAK,CAAC;MACxC,KAAKoB,WAAA,CAAY,KAAKL,UAAA,CAAWf,KAAK,CAAC;IAAA;IAGjCxG,aAAA,wBAAiBwG,KAAA,IAAuB;MAC9C,IAAI,CAAC,KAAKtF,OAAA,EAAS;MAEnB,KAAK2G,WAAA,CAAY,KAAKN,UAAA,CAAWf,KAAK,CAAC;IAAA;IAGjCxG,aAAA,sBAAewG,KAAA,IAAuB;MAC5C,IAAI,CAAC,KAAKtF,OAAA,IAAW,CAAC,KAAKnB,UAAA,EAAY;MAElC,KAAAA,UAAA,CAAWyH,KAAA,CAAMC,WAAA,GAAe;MACrC,KAAK1H,UAAA,CAAW2G,aAAA,CAAcoB,mBAAA,CAAoB,eAAe,KAAKH,aAAa;MAEnF,KAAKI,SAAA,CAAU,KAAKR,UAAA,CAAWf,KAAK,CAAC;IAAA;IAGhCxG,aAAA,kBAAU,MAAiC,KAAKQ,IAAA;IAEhDR,aAAA,kBAAWQ,IAAA,IAA0C;MAC1D,KAAKA,IAAA,GAAOA,IAAA;IAAA;IAGPR,aAAA,6BAAsBkF,eAAA,IAAkC;MAC7D,KAAKA,eAAA,GAAkBA,eAAA;IAAA;IAGlBlF,aAAA,0BAAmBiE,YAAA,IAA+B;MACvD,KAAKA,YAAA,GAAeA,YAAA;IAAA;IAGfjE,aAAA,uBAAgB4F,SAAA,IAA4B;MACjD,KAAKA,SAAA,GAAYA,SAAA;IAAA;IAGZ5F,aAAA,kBAAWgI,IAAA,IAAuB;MACvC,KAAKA,IAAA,GAAOA,IAAA;IAAA;IAGPhI,aAAA,mBAAY+D,KAAA,IAAwB;MACzC,KAAKA,KAAA,GAAQA,KAAA;IAAA;IAGR/D,aAAA,iBAAS,MAAY;MAClBkC,OAAA,CAAA+F,IAAA,CACN;IACF;IAGKjI,aAAA,kBAAWD,UAAA,IAAkC;MAClD,IAAKA,UAAA,KAAuBmI,QAAA,EAAU;QAC5BhG,OAAA,CAAAC,KAAA,CACN;MAEJ;MACA,KAAKpC,UAAA,GAAaA,UAAA;MAElB,KAAKA,UAAA,CAAW2H,gBAAA,CAAiB,eAAe,KAAKS,aAAa;MAClE,KAAKpI,UAAA,CAAW2H,gBAAA,CAAiB,eAAe,KAAKU,cAAc;MACnE,KAAKrI,UAAA,CAAW2G,aAAA,CAAcgB,gBAAA,CAAiB,aAAa,KAAKW,WAAW;IAAA;IAGvErI,aAAA,kBAAU,MAAY;;MAC3B,CAAAyG,EAAA,QAAK1G,UAAA,KAAL,gBAAA0G,EAAA,CAAiBqB,mBAAA,CAAoB,eAAe,KAAKK,aAAA;MACzD,CAAAG,EAAA,QAAKvI,UAAA,KAAL,gBAAAuI,EAAA,CAAiBR,mBAAA,CAAoB,eAAe,KAAKM,cAAA;MACzD,CAAAG,EAAA,IAAAC,EAAA,QAAKzI,UAAA,KAAL,gBAAAyI,EAAA,CAAiB9B,aAAA,KAAjB,gBAAA6B,EAAA,CAAgCT,mBAAA,CAAoB,eAAe,KAAKH,aAAA;MACxE,CAAAc,EAAA,IAAAC,EAAA,QAAK3I,UAAA,KAAL,gBAAA2I,EAAA,CAAiBhC,aAAA,KAAjB,gBAAA+B,EAAA,CAAgCX,mBAAA,CAAoB,aAAa,KAAKO,WAAA;MAEjE,KAAAM,QAAA,CAAUC,KAAA,IAAU;QACvB,MAAMC,IAAA,GAAOD,KAAA;QACb,IAAIC,IAAA,CAAKC,QAAA,EAAU;UACjBD,IAAA,CAAKC,QAAA,CAASC,OAAA;QAChB;QACA,IAAIF,IAAA,CAAKG,QAAA,EAAU;UACjBH,IAAA,CAAKG,QAAA,CAASD,OAAA;QAChB;MAAA,CACD;IAAA;IAzgBD,KAAKhJ,UAAA,GAAaA,UAAA;IAClB,KAAKD,MAAA,GAASA,MAAA;IAET,KAAA2D,KAAA,GAAQ,IAAIwF,sBAAA;IACZ,KAAAhE,GAAA,CAAI,KAAKxB,KAAK;IAEd,KAAAK,KAAA,GAAQ,IAAIoF,sBAAA;IACZ,KAAAjE,GAAA,CAAI,KAAKnB,KAAK;IAGb,MAAAqF,cAAA,GAAiBA,CAASC,QAAA,EAAkBC,YAAA,KAA+B;MAC/E,IAAIC,SAAA,GAAYD,YAAA;MAETE,MAAA,CAAAJ,cAAA,CAAe,MAAMC,QAAA,EAAU;QACpCI,GAAA,EAAK,SAAAA,CAAA,EAAY;UACR,OAAAF,SAAA,KAAc,SAAYA,SAAA,GAAYD,YAAA;QAC/C;QAEA3D,GAAA,EAAK,SAAAA,CAAU+D,KAAA,EAAO;UACpB,IAAIH,SAAA,KAAcG,KAAA,EAAO;YACXH,SAAA,GAAAG,KAAA;YACP,KAAA3F,KAAA,CAAMsF,QAAQ,IAAIK,KAAA;YAClB,KAAAhG,KAAA,CAAM2F,QAAQ,IAAIK,KAAA;YAEvB,KAAK9H,aAAA,CAAc;cAAEpB,IAAA,EAAM6I,QAAA,GAAW;cAAYK;YAAA,CAAc;YAC3D,KAAA9H,aAAA,CAAc,KAAKC,WAAW;UACrC;QACF;MAAA,CACD;MAGD,KAAKwH,QAAQ,IAAIC,YAAA;MAEZ,KAAAvF,KAAA,CAAMsF,QAAQ,IAAIC,YAAA;MAElB,KAAA5F,KAAA,CAAM2F,QAAQ,IAAIC,YAAA;IAAA;IAGVF,cAAA,WAAU,KAAKrJ,MAAM;IACrBqJ,cAAA,WAAU,KAAK1I,MAAM;IACrB0I,cAAA,YAAW,KAAKjI,OAAO;IACvBiI,cAAA,SAAQ,KAAKlI,IAAI;IACjBkI,cAAA,SAAQ,KAAK3I,IAAI;IACjB2I,cAAA,oBAAmB,KAAKjE,eAAe;IACvCiE,cAAA,iBAAgB,KAAKlF,YAAY;IACjCkF,cAAA,cAAa,KAAKvD,SAAS;IAC3BuD,cAAA,UAAS,KAAKpF,KAAK;IACnBoF,cAAA,SAAQ,KAAKnB,IAAI;IACjBmB,cAAA,aAAY,KAAKhI,QAAQ;IACzBgI,cAAA,UAAS,KAAKO,KAAK;IACnBP,cAAA,UAAS,KAAKQ,KAAK;IACnBR,cAAA,UAAS,KAAKS,KAAK;IACnBT,cAAA,kBAAiB,KAAK1G,aAAa;IACnC0G,cAAA,uBAAsB,KAAK3E,kBAAkB;IAC7C2E,cAAA,oBAAmB,KAAKzG,eAAe;IACvCyG,cAAA,yBAAwB,KAAK1E,oBAAoB;IACjD0E,cAAA,mBAAkB,KAAKpG,cAAc;IACrCoG,cAAA,qBAAoB,KAAKnG,gBAAgB;IACzCmG,cAAA,eAAc,KAAKrH,UAAU;IAC7BqH,cAAA,aAAY,KAAKpH,QAAQ;IACzBoH,cAAA,iBAAgB,KAAKpD,YAAY;IACjCoD,cAAA,kBAAiB,KAAKnD,aAAa;IACnCmD,cAAA,QAAO,KAAKjG,GAAG;IAG9B,IAAInD,UAAA,KAAe,QAAW,KAAK8J,OAAA,CAAQ9J,UAAU;EACvD;AAycF;AASA,MAAMkJ,sBAAA,SAA+BrJ,QAAA,CAAS;EA6C5CC,YAAA,EAAc;IACN;IA7CAG,aAAA,mCAA2B;IAC5BA,aAAA,eAAO;IAENA,aAAA,qBAAa,IAAIE,OAAA,CAAQ,GAAG,GAAG,CAAC;IAChCF,aAAA,oBAAY,IAAI8J,KAAA;IAChB9J,aAAA,sBAAc,IAAIE,OAAA,CAAQ,GAAG,GAAG,CAAC;IACjCF,aAAA,qBAAa,IAAIE,OAAA,CAAQ,GAAG,GAAG,CAAC;IAChCF,aAAA,uBAAe,IAAI+J,OAAA;IACnB/J,aAAA,yBAAiB,IAAIG,UAAA;IACrBH,aAAA,0BAAkB,IAAIG,UAAA;IACtBH,aAAA,6BAAqB,IAAIG,UAAA;IAEzBH,aAAA,gBAAQ,IAAIE,OAAA,CAAQ,GAAG,GAAG,CAAC;IAC3BF,aAAA,gBAAQ,IAAIE,OAAA,CAAQ,GAAG,GAAG,CAAC;IAC3BF,aAAA,gBAAQ,IAAIE,OAAA,CAAQ,GAAG,GAAG,CAAC;IAE3BF,aAAA;IACDA,aAAA;IACCA,aAAA;IAGA;IAAAA,aAAA,uBAAe,IAAIE,OAAA;IAEnBF,aAAA,yBAAiB,IAAIE,OAAA;IAErBF,aAAA,6BAAqB,IAAIE,OAAA;IACzBF,aAAA,+BAAuB,IAAIG,UAAA;IAE3BH,aAAA,wBAAgB,IAAIE,OAAA;IACpBF,aAAA,0BAAkB,IAAIG,UAAA;IAEtBH,aAAA,cAAM,IAAIE,OAAA;IAEVF,aAAA,iBAAiD;IACjDA,aAAA,kBAAU;IACVA,aAAA,eAAsB;IACtBA,aAAA,eAAyC;IACzCA,aAAA,gBAAQ;IACRA,aAAA,eAAO;IACPA,aAAA,mBAAW;IACXA,aAAA,gBAAQ;IACRA,aAAA,gBAAQ;IACRA,aAAA,gBAAQ;IA0VT;IAAAA,aAAA,4BAAoB,MAAY;MACrC,IAAI+D,KAAA,GAAQ,KAAKA,KAAA;MAEb,SAAKvD,IAAA,KAAS,SAAS;QACjBuD,KAAA;MACV;MAEA,MAAMxC,UAAA,GAAawC,KAAA,KAAU,UAAU,KAAKrB,eAAA,GAAkB,KAAKsH,kBAAA;MAInE,KAAKvG,KAAA,CAAM,WAAW,EAAEzC,OAAA,GAAU,KAAKR,IAAA,KAAS;MAChD,KAAKiD,KAAA,CAAM,QAAQ,EAAEzC,OAAA,GAAU,KAAKR,IAAA,KAAS;MAC7C,KAAKiD,KAAA,CAAM,OAAO,EAAEzC,OAAA,GAAU,KAAKR,IAAA,KAAS;MAE5C,KAAKyJ,MAAA,CAAO,WAAW,EAAEjJ,OAAA,GAAU,KAAKR,IAAA,KAAS;MACjD,KAAKyJ,MAAA,CAAO,QAAQ,EAAEjJ,OAAA,GAAU,KAAKR,IAAA,KAAS;MAC9C,KAAKyJ,MAAA,CAAO,OAAO,EAAEjJ,OAAA,GAAU,KAAKR,IAAA,KAAS;MAE7C,IAAI0J,OAAA,GAA8C;MAClDA,OAAA,GAAUA,OAAA,CAAQC,MAAA,CAAO,KAAKzG,MAAA,CAAO,KAAKlD,IAAI,EAAE4J,QAAQ;MACxDF,OAAA,GAAUA,OAAA,CAAQC,MAAA,CAAO,KAAK1G,KAAA,CAAM,KAAKjD,IAAI,EAAE4J,QAAQ;MACvDF,OAAA,GAAUA,OAAA,CAAQC,MAAA,CAAO,KAAKF,MAAA,CAAO,KAAKzJ,IAAI,EAAE4J,QAAQ;MAExD,SAAStJ,CAAA,GAAI,GAAGA,CAAA,GAAIoJ,OAAA,CAAQnJ,MAAA,EAAQD,CAAA,IAAK;QACjC,MAAAuJ,MAAA,GAASH,OAAA,CAAQpJ,CAAC;QAIxBuJ,MAAA,CAAOrJ,OAAA,GAAU;QACjBqJ,MAAA,CAAOnG,QAAA,CAASwB,GAAA,CAAI,GAAG,GAAG,CAAC;QACpB2E,MAAA,CAAAjJ,QAAA,CAASC,IAAA,CAAK,KAAKoB,aAAa;QAEnC,IAAA6H,MAAA;QAEC,SAAKxK,MAAA,CAA8ByK,oBAAA,EAAsB;UAC5DD,MAAA,IACI,KAAKxK,MAAA,CAA8BqH,GAAA,GAAO,KAAKrH,MAAA,CAA8B0K,MAAA,IAC9E,KAAK1K,MAAA,CAA8B2K,IAAA;QAAA,OACjC;UAEHH,MAAA,QAAK7H,aAAA,CAAcqD,UAAA,CAAW,KAAK/C,cAAc,IACjDqB,IAAA,CAAKsG,GAAA,CAAK,MAAMtG,IAAA,CAAKuG,GAAA,CAAKvG,IAAA,CAAKwG,EAAA,GAAM,KAAK9K,MAAA,CAA6B+K,GAAA,GAAO,GAAG,IAAK,KAAK/K,MAAA,CAAO2K,IAAA,EAAM,CAAC;QAC7G;QAEOJ,MAAA,CAAA5I,KAAA,CAAMiE,GAAA,CAAI,GAAG,GAAG,CAAC,EAAEoF,cAAA,CAAgBR,MAAA,GAAS,KAAKtC,IAAA,GAAQ,CAAC;QAI7D,IAAAqC,MAAA,CAAOU,GAAA,KAAQ,UAAU;UAC3BV,MAAA,CAAOrJ,OAAA,GAAU;UAEb,IAAAqJ,MAAA,CAAO1G,IAAA,KAAS,QAAQ;YACnB0G,MAAA,CAAAjJ,QAAA,CAASC,IAAA,CAAK,KAAKmD,kBAAkB;YACrC6F,MAAA,CAAArJ,OAAA,GAAU,CAAC,CAAC,KAAKC,IAAA;YAEpB,SAAKA,IAAA,KAAS,KAAK;cAChB,KAAAkE,cAAA,CAAe6F,YAAA,CAAa,KAAKC,SAAA,CAAUvF,GAAA,CAAI,GAAG,GAAG,CAAC,CAAC;cAC5D2E,MAAA,CAAO9I,UAAA,CAAWF,IAAA,CAAKE,UAAU,EAAEoE,QAAA,CAAS,KAAKR,cAAc;cAE/D,IAAIf,IAAA,CAAK8G,GAAA,CAAI,KAAKC,WAAA,CAAY9J,IAAA,CAAK,KAAK+J,KAAK,EAAEtG,eAAA,CAAgBvD,UAAU,EAAEiE,GAAA,CAAI,KAAKtC,GAAG,CAAC,IAAI,KAAK;gBAC/FmH,MAAA,CAAOrJ,OAAA,GAAU;cACnB;YACF;YAEI,SAAKC,IAAA,KAAS,KAAK;cAChB,KAAAkE,cAAA,CAAe6F,YAAA,CAAa,KAAKC,SAAA,CAAUvF,GAAA,CAAI,GAAG,GAAGtB,IAAA,CAAKwG,EAAA,GAAK,CAAC,CAAC;cACtEP,MAAA,CAAO9I,UAAA,CAAWF,IAAA,CAAKE,UAAU,EAAEoE,QAAA,CAAS,KAAKR,cAAc;cAE/D,IAAIf,IAAA,CAAK8G,GAAA,CAAI,KAAKC,WAAA,CAAY9J,IAAA,CAAK,KAAKgK,KAAK,EAAEvG,eAAA,CAAgBvD,UAAU,EAAEiE,GAAA,CAAI,KAAKtC,GAAG,CAAC,IAAI,KAAK;gBAC/FmH,MAAA,CAAOrJ,OAAA,GAAU;cACnB;YACF;YAEI,SAAKC,IAAA,KAAS,KAAK;cAChB,KAAAkE,cAAA,CAAe6F,YAAA,CAAa,KAAKC,SAAA,CAAUvF,GAAA,CAAI,GAAGtB,IAAA,CAAKwG,EAAA,GAAK,GAAG,CAAC,CAAC;cACtEP,MAAA,CAAO9I,UAAA,CAAWF,IAAA,CAAKE,UAAU,EAAEoE,QAAA,CAAS,KAAKR,cAAc;cAE/D,IAAIf,IAAA,CAAK8G,GAAA,CAAI,KAAKC,WAAA,CAAY9J,IAAA,CAAK,KAAKiK,KAAK,EAAExG,eAAA,CAAgBvD,UAAU,EAAEiE,GAAA,CAAI,KAAKtC,GAAG,CAAC,IAAI,KAAK;gBAC/FmH,MAAA,CAAOrJ,OAAA,GAAU;cACnB;YACF;YAEI,SAAKC,IAAA,KAAS,QAAQ;cACnB,KAAAkE,cAAA,CAAe6F,YAAA,CAAa,KAAKC,SAAA,CAAUvF,GAAA,CAAI,GAAGtB,IAAA,CAAKwG,EAAA,GAAK,GAAG,CAAC,CAAC;cACjE,KAAAO,WAAA,CAAY9J,IAAA,CAAK,KAAK0E,YAAY;cACvCsE,MAAA,CAAO9I,UAAA,CAAWgK,qBAAA,CAChB,KAAKC,YAAA,CAAaC,MAAA,CAAO,KAAKC,UAAA,EAAY,KAAKP,WAAA,EAAa,KAAKE,KAAK;cAEjEhB,MAAA,CAAA9I,UAAA,CAAWoE,QAAA,CAAS,KAAKR,cAAc;cAC9CkF,MAAA,CAAOrJ,OAAA,GAAU,KAAKG,QAAA;YACxB;YAEI,SAAKF,IAAA,KAAS,KAAK;cACrBoJ,MAAA,CAAOrJ,OAAA,GAAU;YACnB;UAAA,WACSqJ,MAAA,CAAO1G,IAAA,KAAS,SAAS;YAC3B0G,MAAA,CAAAjJ,QAAA,CAASC,IAAA,CAAK,KAAKmD,kBAAkB;YAC5C6F,MAAA,CAAOrJ,OAAA,GAAU,KAAKG,QAAA;UAAA,WACbkJ,MAAA,CAAO1G,IAAA,KAAS,OAAO;YACzB0G,MAAA,CAAAjJ,QAAA,CAASC,IAAA,CAAK,KAAKoB,aAAa;YACvC4H,MAAA,CAAOrJ,OAAA,GAAU,KAAKG,QAAA;UAAA,WACbkJ,MAAA,CAAO1G,IAAA,KAAS,SAAS;YAC3B0G,MAAA,CAAAjJ,QAAA,CAASC,IAAA,CAAK,KAAKmD,kBAAkB;YACrC6F,MAAA,CAAA9I,UAAA,CAAWF,IAAA,CAAK,KAAKoD,oBAAoB;YAChD,KAAKY,UAAA,CACFK,GAAA,CAAI,OAAO,OAAO,KAAK,EACvBT,GAAA,CAAI,KAAKT,kBAAkB,EAC3BrB,GAAA,CAAI,KAAKV,aAAa,EACtBqI,cAAA,CAAe,EAAE;YACpB,KAAKzF,UAAA,CAAWP,eAAA,CAAgB,KAAKL,oBAAA,CAAqBkH,KAAA,CAAM,EAAE9I,MAAA,EAAQ;YACnEwH,MAAA,CAAA5I,KAAA,CAAMJ,IAAA,CAAK,KAAKgE,UAAU;YACjCgF,MAAA,CAAOrJ,OAAA,GAAU,KAAKG,QAAA;UAAA,OACjB;YACEkJ,MAAA,CAAA9I,UAAA,CAAWF,IAAA,CAAKE,UAAU;YAEjC,IAAI,KAAKJ,QAAA,EAAU;cACVkJ,MAAA,CAAAjJ,QAAA,CAASC,IAAA,CAAK,KAAKmD,kBAAkB;YAAA,OACvC;cACE6F,MAAA,CAAAjJ,QAAA,CAASC,IAAA,CAAK,KAAKoB,aAAa;YACzC;YAEA,IAAI,KAAKxB,IAAA,EAAM;cACboJ,MAAA,CAAOrJ,OAAA,GAAU,KAAKC,IAAA,CAAKmE,MAAA,CAAOiF,MAAA,CAAO1G,IAAI,MAAM;YACrD;UACF;UAGA;QACF;QAIO0G,MAAA,CAAA9I,UAAA,CAAWF,IAAA,CAAKE,UAAU;QAEjC,IAAI,KAAKf,IAAA,KAAS,eAAe,KAAKA,IAAA,KAAS,SAAS;UAGtD,MAAMoL,kBAAA,GAAqB;UAC3B,MAAMC,mBAAA,GAAsB;UAC5B,MAAMC,kBAAA,GAAqB;UAE3B,IAAIzB,MAAA,CAAO1G,IAAA,KAAS,OAAO0G,MAAA,CAAO1G,IAAA,KAAS,QAAQ;YACjD,IACES,IAAA,CAAK8G,GAAA,CAAI,KAAKC,WAAA,CAAY9J,IAAA,CAAK,KAAK+J,KAAK,EAAEtG,eAAA,CAAgBvD,UAAU,EAAEiE,GAAA,CAAI,KAAKtC,GAAG,CAAC,IAAI0I,kBAAA,EACxF;cACAvB,MAAA,CAAO5I,KAAA,CAAMiE,GAAA,CAAI,OAAO,OAAO,KAAK;cACpC2E,MAAA,CAAOrJ,OAAA,GAAU;YACnB;UACF;UAEA,IAAIqJ,MAAA,CAAO1G,IAAA,KAAS,OAAO0G,MAAA,CAAO1G,IAAA,KAAS,QAAQ;YACjD,IACES,IAAA,CAAK8G,GAAA,CAAI,KAAKC,WAAA,CAAY9J,IAAA,CAAK,KAAKgK,KAAK,EAAEvG,eAAA,CAAgBvD,UAAU,EAAEiE,GAAA,CAAI,KAAKtC,GAAG,CAAC,IAAI0I,kBAAA,EACxF;cACAvB,MAAA,CAAO5I,KAAA,CAAMiE,GAAA,CAAI,OAAO,OAAO,KAAK;cACpC2E,MAAA,CAAOrJ,OAAA,GAAU;YACnB;UACF;UAEA,IAAIqJ,MAAA,CAAO1G,IAAA,KAAS,OAAO0G,MAAA,CAAO1G,IAAA,KAAS,QAAQ;YACjD,IACES,IAAA,CAAK8G,GAAA,CAAI,KAAKC,WAAA,CAAY9J,IAAA,CAAK,KAAKiK,KAAK,EAAExG,eAAA,CAAgBvD,UAAU,EAAEiE,GAAA,CAAI,KAAKtC,GAAG,CAAC,IAAI0I,kBAAA,EACxF;cACAvB,MAAA,CAAO5I,KAAA,CAAMiE,GAAA,CAAI,OAAO,OAAO,KAAK;cACpC2E,MAAA,CAAOrJ,OAAA,GAAU;YACnB;UACF;UAEI,IAAAqJ,MAAA,CAAO1G,IAAA,KAAS,MAAM;YACxB,IACES,IAAA,CAAK8G,GAAA,CAAI,KAAKC,WAAA,CAAY9J,IAAA,CAAK,KAAKiK,KAAK,EAAExG,eAAA,CAAgBvD,UAAU,EAAEiE,GAAA,CAAI,KAAKtC,GAAG,CAAC,IAAI2I,mBAAA,EACxF;cACAxB,MAAA,CAAO5I,KAAA,CAAMiE,GAAA,CAAI,OAAO,OAAO,KAAK;cACpC2E,MAAA,CAAOrJ,OAAA,GAAU;YACnB;UACF;UAEI,IAAAqJ,MAAA,CAAO1G,IAAA,KAAS,MAAM;YACxB,IACES,IAAA,CAAK8G,GAAA,CAAI,KAAKC,WAAA,CAAY9J,IAAA,CAAK,KAAK+J,KAAK,EAAEtG,eAAA,CAAgBvD,UAAU,EAAEiE,GAAA,CAAI,KAAKtC,GAAG,CAAC,IAAI2I,mBAAA,EACxF;cACAxB,MAAA,CAAO5I,KAAA,CAAMiE,GAAA,CAAI,OAAO,OAAO,KAAK;cACpC2E,MAAA,CAAOrJ,OAAA,GAAU;YACnB;UACF;UAEI,IAAAqJ,MAAA,CAAO1G,IAAA,KAAS,MAAM;YACxB,IACES,IAAA,CAAK8G,GAAA,CAAI,KAAKC,WAAA,CAAY9J,IAAA,CAAK,KAAKgK,KAAK,EAAEvG,eAAA,CAAgBvD,UAAU,EAAEiE,GAAA,CAAI,KAAKtC,GAAG,CAAC,IAAI2I,mBAAA,EACxF;cACAxB,MAAA,CAAO5I,KAAA,CAAMiE,GAAA,CAAI,OAAO,OAAO,KAAK;cACpC2E,MAAA,CAAOrJ,OAAA,GAAU;YACnB;UACF;UAIA,IAAIqJ,MAAA,CAAO1G,IAAA,CAAKyB,MAAA,CAAO,GAAG,MAAM,IAAI;YAClC,IAAI,KAAK+F,WAAA,CAAY9J,IAAA,CAAK,KAAK+J,KAAK,EAAEtG,eAAA,CAAgBvD,UAAU,EAAEiE,GAAA,CAAI,KAAKtC,GAAG,IAAI4I,kBAAA,EAAoB;cAChG,IAAAzB,MAAA,CAAOU,GAAA,KAAQ,OAAO;gBACxBV,MAAA,CAAOrJ,OAAA,GAAU;cAAA,OACZ;gBACLqJ,MAAA,CAAO5I,KAAA,CAAM0C,CAAA,IAAK;cACpB;YAAA,WACSkG,MAAA,CAAOU,GAAA,KAAQ,OAAO;cAC/BV,MAAA,CAAOrJ,OAAA,GAAU;YACnB;UACF;UAEA,IAAIqJ,MAAA,CAAO1G,IAAA,CAAKyB,MAAA,CAAO,GAAG,MAAM,IAAI;YAClC,IAAI,KAAK+F,WAAA,CAAY9J,IAAA,CAAK,KAAKgK,KAAK,EAAEvG,eAAA,CAAgBvD,UAAU,EAAEiE,GAAA,CAAI,KAAKtC,GAAG,IAAI4I,kBAAA,EAAoB;cAChG,IAAAzB,MAAA,CAAOU,GAAA,KAAQ,OAAO;gBACxBV,MAAA,CAAOrJ,OAAA,GAAU;cAAA,OACZ;gBACLqJ,MAAA,CAAO5I,KAAA,CAAM6C,CAAA,IAAK;cACpB;YAAA,WACS+F,MAAA,CAAOU,GAAA,KAAQ,OAAO;cAC/BV,MAAA,CAAOrJ,OAAA,GAAU;YACnB;UACF;UAEA,IAAIqJ,MAAA,CAAO1G,IAAA,CAAKyB,MAAA,CAAO,GAAG,MAAM,IAAI;YAClC,IAAI,KAAK+F,WAAA,CAAY9J,IAAA,CAAK,KAAKiK,KAAK,EAAExG,eAAA,CAAgBvD,UAAU,EAAEiE,GAAA,CAAI,KAAKtC,GAAG,IAAI4I,kBAAA,EAAoB;cAChG,IAAAzB,MAAA,CAAOU,GAAA,KAAQ,OAAO;gBACxBV,MAAA,CAAOrJ,OAAA,GAAU;cAAA,OACZ;gBACLqJ,MAAA,CAAO5I,KAAA,CAAM8C,CAAA,IAAK;cACpB;YAAA,WACS8F,MAAA,CAAOU,GAAA,KAAQ,OAAO;cAC/BV,MAAA,CAAOrJ,OAAA,GAAU;YACnB;UACF;QAAA,WACS,KAAKR,IAAA,KAAS,UAAU;UAG5B,KAAAuL,eAAA,CAAgB1K,IAAA,CAAKE,UAAU;UACpC,KAAK4J,WAAA,CAAY9J,IAAA,CAAK,KAAK6B,GAAG,EAAE4B,eAAA,CAAgB,KAAKK,cAAA,CAAe9D,IAAA,CAAKE,UAAU,EAAEsB,MAAA,CAAQ;UAE7F,IAAIwH,MAAA,CAAO1G,IAAA,CAAKyB,MAAA,CAAO,GAAG,MAAM,IAAI;YAC3BiF,MAAA,CAAA9I,UAAA,CAAWgK,qBAAA,CAAsB,KAAKC,YAAA,CAAaC,MAAA,CAAO,KAAKvI,GAAA,EAAK,KAAKwI,UAAA,EAAY,KAAKL,KAAK,CAAC;UACzG;UAEI,IAAAhB,MAAA,CAAO1G,IAAA,KAAS,KAAK;YACvB,KAAKwB,cAAA,CAAemB,gBAAA,CAAiB,KAAK8E,KAAA,EAAOhH,IAAA,CAAK4H,KAAA,CAAM,CAAC,KAAKb,WAAA,CAAY7G,CAAA,EAAG,KAAK6G,WAAA,CAAY5G,CAAC,CAAC;YACpG,KAAKY,cAAA,CAAe8G,mBAAA,CAAoB,KAAKF,eAAA,EAAiB,KAAK5G,cAAc;YAC1EkF,MAAA,CAAA9I,UAAA,CAAWF,IAAA,CAAK,KAAK8D,cAAc;UAC5C;UAEI,IAAAkF,MAAA,CAAO1G,IAAA,KAAS,KAAK;YACvB,KAAKwB,cAAA,CAAemB,gBAAA,CAAiB,KAAK+E,KAAA,EAAOjH,IAAA,CAAK4H,KAAA,CAAM,KAAKb,WAAA,CAAYhH,CAAA,EAAG,KAAKgH,WAAA,CAAY5G,CAAC,CAAC;YACnG,KAAKY,cAAA,CAAe8G,mBAAA,CAAoB,KAAKF,eAAA,EAAiB,KAAK5G,cAAc;YAC1EkF,MAAA,CAAA9I,UAAA,CAAWF,IAAA,CAAK,KAAK8D,cAAc;UAC5C;UAEI,IAAAkF,MAAA,CAAO1G,IAAA,KAAS,KAAK;YACvB,KAAKwB,cAAA,CAAemB,gBAAA,CAAiB,KAAKgF,KAAA,EAAOlH,IAAA,CAAK4H,KAAA,CAAM,KAAKb,WAAA,CAAY7G,CAAA,EAAG,KAAK6G,WAAA,CAAYhH,CAAC,CAAC;YACnG,KAAKgB,cAAA,CAAe8G,mBAAA,CAAoB,KAAKF,eAAA,EAAiB,KAAK5G,cAAc;YAC1EkF,MAAA,CAAA9I,UAAA,CAAWF,IAAA,CAAK,KAAK8D,cAAc;UAC5C;QACF;QAGOkF,MAAA,CAAArJ,OAAA,GAAUqJ,MAAA,CAAOrJ,OAAA,KAAYqJ,MAAA,CAAO1G,IAAA,CAAKoB,OAAA,CAAQ,GAAG,MAAM,MAAM,KAAK2E,KAAA;QACrEW,MAAA,CAAArJ,OAAA,GAAUqJ,MAAA,CAAOrJ,OAAA,KAAYqJ,MAAA,CAAO1G,IAAA,CAAKoB,OAAA,CAAQ,GAAG,MAAM,MAAM,KAAK4E,KAAA;QACrEU,MAAA,CAAArJ,OAAA,GAAUqJ,MAAA,CAAOrJ,OAAA,KAAYqJ,MAAA,CAAO1G,IAAA,CAAKoB,OAAA,CAAQ,GAAG,MAAM,MAAM,KAAK6E,KAAA;QAC5ES,MAAA,CAAOrJ,OAAA,GAAUqJ,MAAA,CAAOrJ,OAAA,KAAYqJ,MAAA,CAAO1G,IAAA,CAAKoB,OAAA,CAAQ,GAAG,MAAM,MAAO,KAAK2E,KAAA,IAAS,KAAKC,KAAA,IAAS,KAAKC,KAAA;QAKzGS,MAAA,CAAOrB,QAAA,CAASkD,WAAA,GAAc7B,MAAA,CAAOrB,QAAA,CAASkD,WAAA,IAAe7B,MAAA,CAAOrB,QAAA,CAASmD,OAAA;QAEtE9B,MAAA,CAAArB,QAAA,CAASoD,SAAA,GAAY/B,MAAA,CAAOrB,QAAA,CAASoD,SAAA,IAAa/B,MAAA,CAAOrB,QAAA,CAASqD,KAAA,CAAMV,KAAA;QAE/EtB,MAAA,CAAOrB,QAAA,CAASqD,KAAA,CAAMhL,IAAA,CAAKgJ,MAAA,CAAOrB,QAAA,CAASoD,SAAS;QAE7C/B,MAAA,CAAArB,QAAA,CAASmD,OAAA,GAAU9B,MAAA,CAAOrB,QAAA,CAASkD,WAAA;QAEtC,KAAC,KAAKhL,OAAA,EAAS;UAEjBmJ,MAAA,CAAOrB,QAAA,CAASmD,OAAA,IAAW;UAEpB9B,MAAA,CAAArB,QAAA,CAASqD,KAAA,CAAMC,IAAA,CAAK,IAAIC,KAAA,CAAM,GAAG,GAAG,CAAC,GAAG,GAAG;QAAA,WACzC,KAAKtL,IAAA,EAAM;UAChB,IAAAoJ,MAAA,CAAO1G,IAAA,KAAS,KAAK1C,IAAA,EAAM;YAE7BoJ,MAAA,CAAOrB,QAAA,CAASmD,OAAA,GAAU;YAEnB9B,MAAA,CAAArB,QAAA,CAASqD,KAAA,CAAMC,IAAA,CAAK,IAAIC,KAAA,CAAM,GAAG,GAAG,CAAC,GAAG,GAAG;UAAA,WAElD,KAAKtL,IAAA,CAAKuL,KAAA,CAAM,EAAE,EAAEC,IAAA,CAAK,UAAUC,CAAA,EAAG;YACpC,OAAOrC,MAAA,CAAO1G,IAAA,KAAS+I,CAAA;UAAA,CACxB,GACD;YAEArC,MAAA,CAAOrB,QAAA,CAASmD,OAAA,GAAU;YAEnB9B,MAAA,CAAArB,QAAA,CAASqD,KAAA,CAAMC,IAAA,CAAK,IAAIC,KAAA,CAAM,GAAG,GAAG,CAAC,GAAG,GAAG;UAAA,OAC7C;YAELlC,MAAA,CAAOrB,QAAA,CAASmD,OAAA,IAAW;YAEpB9B,MAAA,CAAArB,QAAA,CAASqD,KAAA,CAAMC,IAAA,CAAK,IAAIC,KAAA,CAAM,GAAG,GAAG,CAAC,GAAG,GAAG;UACpD;QACF;MACF;MAEA,MAAMvK,iBAAA,CAAkB;IAAA;IAzoBlB,MAAA2K,aAAA,GAAgB,IAAIC,iBAAA,CAAkB;MAC1CC,SAAA,EAAW;MACXC,UAAA,EAAY;MACZC,WAAA,EAAa;MACbC,IAAA,EAAMC,UAAA;MACNC,GAAA,EAAK;MACLC,UAAA,EAAY;IAAA,CACb;IAEK,MAAAC,iBAAA,GAAoB,IAAIC,iBAAA,CAAkB;MAC9CR,SAAA,EAAW;MACXC,UAAA,EAAY;MACZC,WAAA,EAAa;MACbO,SAAA,EAAW;MACXJ,GAAA,EAAK;MACLC,UAAA,EAAY;IAAA,CACb;IAIK,MAAAI,YAAA,GAAeZ,aAAA,CAAchB,KAAA;IACnC4B,YAAA,CAAapB,OAAA,GAAU;IAEjB,MAAAqB,SAAA,GAAYb,aAAA,CAAchB,KAAA;IAChC6B,SAAA,CAAUrB,OAAA,GAAU;IAEd,MAAAsB,MAAA,GAASd,aAAA,CAAchB,KAAA;IACtB8B,MAAA,CAAApB,KAAA,CAAM3G,GAAA,CAAI,QAAQ;IAEnB,MAAAgI,QAAA,GAAWf,aAAA,CAAchB,KAAA;IACtB+B,QAAA,CAAArB,KAAA,CAAM3G,GAAA,CAAI,KAAQ;IAErB,MAAAiI,OAAA,GAAUhB,aAAA,CAAchB,KAAA;IACtBgC,OAAA,CAAAtB,KAAA,CAAM3G,GAAA,CAAI,GAAQ;IAEpB,MAAAkI,mBAAA,GAAsBjB,aAAA,CAAchB,KAAA;IAC1CiC,mBAAA,CAAoBzB,OAAA,GAAU;IAExB,MAAA0B,oBAAA,GAAuBD,mBAAA,CAAoBjC,KAAA;IAC5BkC,oBAAA,CAAAxB,KAAA,CAAM3G,GAAA,CAAI,QAAQ;IAEjC,MAAAoI,kBAAA,GAAqBF,mBAAA,CAAoBjC,KAAA;IAC5BmC,kBAAA,CAAAzB,KAAA,CAAM3G,GAAA,CAAI,KAAQ;IAE/B,MAAAqI,qBAAA,GAAwBH,mBAAA,CAAoBjC,KAAA;IAC5BoC,qBAAA,CAAA1B,KAAA,CAAM3G,GAAA,CAAI,QAAQ;IAElC,MAAAsI,SAAA,GAAYrB,aAAA,CAAchB,KAAA;IACtBqC,SAAA,CAAA3B,KAAA,CAAM3G,GAAA,CAAI,QAAQ;IAEtB,MAAAuI,UAAA,GAAab,iBAAA,CAAkBzB,KAAA;IAC1BsC,UAAA,CAAA5B,KAAA,CAAM3G,GAAA,CAAI,QAAQ;IAEvB,MAAAwI,YAAA,GAAed,iBAAA,CAAkBzB,KAAA;IAC1BuC,YAAA,CAAA7B,KAAA,CAAM3G,GAAA,CAAI,KAAQ;IAEzB,MAAAyI,WAAA,GAAcf,iBAAA,CAAkBzB,KAAA;IAC1BwC,WAAA,CAAA9B,KAAA,CAAM3G,GAAA,CAAI,GAAQ;IAExB,MAAA0I,WAAA,GAAchB,iBAAA,CAAkBzB,KAAA;IAC1ByC,WAAA,CAAA/B,KAAA,CAAM3G,GAAA,CAAI,KAAQ;IAExB,MAAA2I,cAAA,GAAiBjB,iBAAA,CAAkBzB,KAAA;IAC1B0C,cAAA,CAAAhC,KAAA,CAAM3G,GAAA,CAAI,QAAQ;IAE3B,MAAA4I,aAAA,GAAgBlB,iBAAA,CAAkBzB,KAAA;IAC1B2C,aAAA,CAAAjC,KAAA,CAAM3G,GAAA,CAAI,QAAQ;IAE1B,MAAA6I,WAAA,GAAcnB,iBAAA,CAAkBzB,KAAA;IAC1B4C,WAAA,CAAAlC,KAAA,CAAM3G,GAAA,CAAI,OAAQ;IAExB,MAAA8I,wBAAA,GAA2BF,aAAA,CAAc3C,KAAA;IAC/C6C,wBAAA,CAAyBrC,OAAA,GAAU;IAI7B,MAAAsC,aAAA,GAAgB,IAAIC,gBAAA,CAAiB,GAAG,MAAM,KAAK,IAAI,GAAG,KAAK;IAErE,MAAMC,mBAAA,GAAsB,IAAIC,WAAA,CAAY,OAAO,OAAO,KAAK;IAEzD,MAAAC,YAAA,GAAe,IAAIC,cAAA;IACzBD,YAAA,CAAaE,YAAA,CAAa,YAAY,IAAIC,sBAAA,CAAuB,CAAC,GAAG,GAAG,GAAG,GAAG,GAAG,CAAC,GAAG,CAAC,CAAC;IAEjF,MAAAC,cAAA,GAAiBA,CAACC,MAAA,EAAgBC,GAAA,KAAgC;MAChE,MAAArG,QAAA,GAAW,IAAIgG,cAAA;MACrB,MAAMM,QAAA,GAAW;MAEjB,SAAStO,CAAA,GAAI,GAAGA,CAAA,IAAK,KAAKqO,GAAA,EAAK,EAAErO,CAAA,EAAG;QAClCsO,QAAA,CAASC,IAAA,CAAK,GAAGjL,IAAA,CAAKkL,GAAA,CAAKxO,CAAA,GAAI,KAAMsD,IAAA,CAAKwG,EAAE,IAAIsE,MAAA,EAAQ9K,IAAA,CAAKmL,GAAA,CAAKzO,CAAA,GAAI,KAAMsD,IAAA,CAAKwG,EAAE,IAAIsE,MAAM;MAC/F;MAEApG,QAAA,CAASiG,YAAA,CAAa,YAAY,IAAIC,sBAAA,CAAuBI,QAAA,EAAU,CAAC,CAAC;MAElE,OAAAtG,QAAA;IAAA;IAKT,MAAM0G,uBAAA,GAA0BA,CAAA,KAAsB;MAC9C,MAAA1G,QAAA,GAAW,IAAIgG,cAAA;MAErBhG,QAAA,CAASiG,YAAA,CAAa,YAAY,IAAIC,sBAAA,CAAuB,CAAC,GAAG,GAAG,GAAG,GAAG,GAAG,CAAC,GAAG,CAAC,CAAC;MAE5E,OAAAlG,QAAA;IAAA;IAKT,MAAM2G,cAAA,GAAiB;MACrBrP,CAAA,EAAG,CACD,CAAC,IAAIsP,IAAA,CAAKjB,aAAA,EAAehB,MAAM,GAAG,CAAC,GAAG,GAAG,CAAC,GAAG,CAAC,GAAG,GAAG,CAACrJ,IAAA,CAAKwG,EAAA,GAAK,CAAC,GAAG,MAAM,KAAK,GAC9E,CAAC,IAAI8E,IAAA,CAAKjB,aAAA,EAAehB,MAAM,GAAG,CAAC,GAAG,GAAG,CAAC,GAAG,CAAC,GAAG,GAAGrJ,IAAA,CAAKwG,EAAA,GAAK,CAAC,GAAG,MAAM,KAAK,GAC7E,CAAC,IAAI+E,IAAA,CAAKd,YAAA,EAAcZ,UAAU,CAAC,EACrC;MACA5N,CAAA,EAAG,CACD,CAAC,IAAIqP,IAAA,CAAKjB,aAAA,EAAef,QAAQ,GAAG,CAAC,GAAG,GAAG,CAAC,GAAG,MAAM,MAAM,KAAK,GAChE,CAAC,IAAIgC,IAAA,CAAKjB,aAAA,EAAef,QAAQ,GAAG,CAAC,GAAG,GAAG,CAAC,GAAG,CAACtJ,IAAA,CAAKwG,EAAA,EAAI,GAAG,CAAC,GAAG,MAAM,KAAK,GAC3E,CAAC,IAAI+E,IAAA,CAAKd,YAAA,EAAcX,YAAY,GAAG,MAAM,CAAC,GAAG,GAAG9J,IAAA,CAAKwG,EAAA,GAAK,CAAC,CAAC,EAClE;MACAtK,CAAA,EAAG,CACD,CAAC,IAAIoP,IAAA,CAAKjB,aAAA,EAAed,OAAO,GAAG,CAAC,GAAG,GAAG,CAAC,GAAG,CAACvJ,IAAA,CAAKwG,EAAA,GAAK,GAAG,GAAG,CAAC,GAAG,MAAM,KAAK,GAC9E,CAAC,IAAI8E,IAAA,CAAKjB,aAAA,EAAed,OAAO,GAAG,CAAC,GAAG,GAAG,CAAC,GAAG,CAAC,CAACvJ,IAAA,CAAKwG,EAAA,GAAK,GAAG,GAAG,CAAC,GAAG,MAAM,KAAK,GAC/E,CAAC,IAAI+E,IAAA,CAAKd,YAAA,EAAcV,WAAW,GAAG,MAAM,CAAC,GAAG,CAAC/J,IAAA,CAAKwG,EAAA,GAAK,GAAG,CAAC,CAAC,EAClE;MACAgF,GAAA,EAAK,CAAC,CAAC,IAAIF,IAAA,CAAK,IAAIG,kBAAA,CAAmB,KAAK,CAAC,GAAGjC,mBAAA,CAAoBjC,KAAA,CAAO,IAAG,CAAC,GAAG,GAAG,CAAC,GAAG,CAAC,GAAG,GAAG,CAAC,CAAC,CAAC;MACnGmE,EAAA,EAAI,CACF,CAAC,IAAIJ,IAAA,CAAK,IAAIK,aAAA,CAAc,OAAO,KAAK,GAAGlC,oBAAA,CAAqBlC,KAAA,CAAO,IAAG,CAAC,MAAM,MAAM,CAAC,CAAC,GACzF,CAAC,IAAIgE,IAAA,CAAKd,YAAA,EAAcP,aAAa,GAAG,CAAC,MAAM,KAAK,CAAC,GAAG,MAAM,CAAC,OAAO,GAAG,CAAC,CAAC,GAC3E,CAAC,IAAIqB,IAAA,CAAKd,YAAA,EAAcP,aAAa,GAAG,CAAC,KAAK,MAAM,CAAC,GAAG,CAAC,GAAG,GAAGlK,IAAA,CAAKwG,EAAA,GAAK,CAAC,GAAG,CAAC,OAAO,GAAG,CAAC,CAAC,EAC5F;MACAoF,EAAA,EAAI,CACF,CAAC,IAAIN,IAAA,CAAK,IAAIK,aAAA,CAAc,OAAO,KAAK,GAAGjC,kBAAA,CAAmBnC,KAAA,EAAO,GAAG,CAAC,GAAG,MAAM,IAAI,GAAG,CAAC,GAAGvH,IAAA,CAAKwG,EAAA,GAAK,GAAG,CAAC,CAAC,GAC5G,CAAC,IAAI+E,IAAA,CAAKd,YAAA,EAAcT,WAAW,GAAG,CAAC,GAAG,MAAM,GAAG,GAAG,CAAC,GAAG,GAAGhK,IAAA,CAAKwG,EAAA,GAAK,CAAC,GAAG,CAAC,OAAO,GAAG,CAAC,CAAC,GACxF,CAAC,IAAI+E,IAAA,CAAKd,YAAA,EAAcT,WAAW,GAAG,CAAC,GAAG,KAAK,IAAI,GAAG,CAAC,GAAG,CAAChK,IAAA,CAAKwG,EAAA,GAAK,GAAG,CAAC,GAAG,CAAC,OAAO,GAAG,CAAC,CAAC,EAC3F;MACAqF,EAAA,EAAI,CACF,CACE,IAAIP,IAAA,CAAK,IAAIK,aAAA,CAAc,OAAO,KAAK,GAAGhC,qBAAA,CAAsBpC,KAAA,EAAO,GACvE,CAAC,MAAM,GAAG,IAAI,GACd,CAAC,CAACvH,IAAA,CAAKwG,EAAA,GAAK,GAAG,GAAG,CAAC,EACrB,EACA,CAAC,IAAI+E,IAAA,CAAKd,YAAA,EAAcR,cAAc,GAAG,CAAC,MAAM,GAAG,GAAG,GAAG,MAAM,CAAC,OAAO,GAAG,CAAC,CAAC,GAC5E,CAAC,IAAIsB,IAAA,CAAKd,YAAA,EAAcR,cAAc,GAAG,CAAC,KAAK,GAAG,IAAI,GAAG,CAAC,GAAG,CAACjK,IAAA,CAAKwG,EAAA,GAAK,GAAG,CAAC,GAAG,CAAC,OAAO,GAAG,CAAC,CAAC;IAC9F;IAGF,MAAMsF,eAAA,GAAkB;MACtB9P,CAAA,EAAG,CAAC,CAAC,IAAIsP,IAAA,CAAK,IAAIhB,gBAAA,CAAiB,KAAK,GAAG,GAAG,GAAG,GAAG,KAAK,GAAGnB,YAAY,GAAG,CAAC,KAAK,GAAG,CAAC,GAAG,CAAC,GAAG,GAAG,CAACnJ,IAAA,CAAKwG,EAAA,GAAK,CAAC,CAAC,CAAC;MAC7GvK,CAAA,EAAG,CAAC,CAAC,IAAIqP,IAAA,CAAK,IAAIhB,gBAAA,CAAiB,KAAK,GAAG,GAAG,GAAG,GAAG,KAAK,GAAGnB,YAAY,GAAG,CAAC,GAAG,KAAK,CAAC,CAAC,CAAC;MACvFjN,CAAA,EAAG,CAAC,CAAC,IAAIoP,IAAA,CAAK,IAAIhB,gBAAA,CAAiB,KAAK,GAAG,GAAG,GAAG,GAAG,KAAK,GAAGnB,YAAY,GAAG,CAAC,GAAG,GAAG,GAAG,GAAG,CAACnJ,IAAA,CAAKwG,EAAA,GAAK,GAAG,GAAG,CAAC,CAAC,CAAC;MAC5GgF,GAAA,EAAK,CAAC,CAAC,IAAIF,IAAA,CAAK,IAAIG,kBAAA,CAAmB,KAAK,CAAC,GAAGtC,YAAY,CAAC,CAAC;MAC9DuC,EAAA,EAAI,CAAC,CAAC,IAAIJ,IAAA,CAAK,IAAIK,aAAA,CAAc,KAAK,GAAG,GAAGxC,YAAY,GAAG,CAAC,KAAK,KAAK,CAAC,CAAC,CAAC;MACzEyC,EAAA,EAAI,CAAC,CAAC,IAAIN,IAAA,CAAK,IAAIK,aAAA,CAAc,KAAK,GAAG,GAAGxC,YAAY,GAAG,CAAC,GAAG,KAAK,GAAG,GAAG,CAAC,GAAGnJ,IAAA,CAAKwG,EAAA,GAAK,GAAG,CAAC,CAAC,CAAC;MAC9FqF,EAAA,EAAI,CAAC,CAAC,IAAIP,IAAA,CAAK,IAAIK,aAAA,CAAc,KAAK,GAAG,GAAGxC,YAAY,GAAG,CAAC,KAAK,GAAG,GAAG,GAAG,CAAC,CAACnJ,IAAA,CAAKwG,EAAA,GAAK,GAAG,GAAG,CAAC,CAAC,CAAC;IAAA;IAGjG,MAAMuF,eAAA,GAAkB;MACtBC,KAAA,EAAO,CAAC,CAAC,IAAIV,IAAA,CAAK,IAAIG,kBAAA,CAAmB,MAAM,CAAC,GAAGrC,SAAS,GAAG,MAAM,MAAM,MAAM,QAAQ,CAAC;MAC1F6C,GAAA,EAAK,CAAC,CAAC,IAAIX,IAAA,CAAK,IAAIG,kBAAA,CAAmB,MAAM,CAAC,GAAGrC,SAAS,GAAG,MAAM,MAAM,MAAM,QAAQ,CAAC;MACxF8C,KAAA,EAAO,CAAC,CAAC,IAAIX,IAAA,CAAKH,uBAAA,CAA2B,GAAAhC,SAAS,GAAG,MAAM,MAAM,MAAM,QAAQ,CAAC;MACpFpN,CAAA,EAAG,CAAC,CAAC,IAAIuP,IAAA,CAAKd,YAAA,EAAcrB,SAAA,CAAU7B,KAAA,CAAM,CAAC,GAAG,CAAC,MAAM,GAAG,CAAC,GAAG,MAAM,CAAC,KAAK,GAAG,CAAC,GAAG,QAAQ,CAAC;MAC1FtL,CAAA,EAAG,CAAC,CAAC,IAAIsP,IAAA,CAAKd,YAAA,EAAcrB,SAAA,CAAU7B,KAAA,EAAO,GAAG,CAAC,GAAG,MAAM,CAAC,GAAG,CAAC,GAAG,GAAGvH,IAAA,CAAKwG,EAAA,GAAK,CAAC,GAAG,CAAC,KAAK,GAAG,CAAC,GAAG,QAAQ,CAAC;MACzGtK,CAAA,EAAG,CAAC,CAAC,IAAIqP,IAAA,CAAKd,YAAA,EAAcrB,SAAA,CAAU7B,KAAA,EAAO,GAAG,CAAC,GAAG,GAAG,IAAI,GAAG,CAAC,GAAG,CAACvH,IAAA,CAAKwG,EAAA,GAAK,GAAG,CAAC,GAAG,CAAC,KAAK,GAAG,CAAC,GAAG,QAAQ,CAAC;IAAA;IAG5G,MAAM2F,WAAA,GAAc;MAClBnQ,CAAA,EAAG,CACD,CAAC,IAAIuP,IAAA,CAAKV,cAAA,CAAe,GAAG,GAAG,GAAGhB,UAAU,CAAC,GAC7C,CAAC,IAAIyB,IAAA,CAAK,IAAIG,kBAAA,CAAmB,MAAM,CAAC,GAAGpC,MAAM,GAAG,CAAC,GAAG,GAAG,IAAI,GAAG,MAAM,CAAC,GAAG,GAAG,CAAC,CAAC,EACnF;MACApN,CAAA,EAAG,CACD,CAAC,IAAIsP,IAAA,CAAKV,cAAA,CAAe,GAAG,GAAG,GAAGf,YAAY,GAAG,MAAM,CAAC,GAAG,GAAG,CAAC9J,IAAA,CAAKwG,EAAA,GAAK,CAAC,CAAC,GAC3E,CAAC,IAAI8E,IAAA,CAAK,IAAIG,kBAAA,CAAmB,MAAM,CAAC,GAAGnC,QAAQ,GAAG,CAAC,GAAG,GAAG,IAAI,GAAG,MAAM,CAAC,GAAG,GAAG,CAAC,CAAC,EACrF;MACApN,CAAA,EAAG,CACD,CAAC,IAAIqP,IAAA,CAAKV,cAAA,CAAe,GAAG,GAAG,GAAGd,WAAW,GAAG,MAAM,CAAC,GAAG/J,IAAA,CAAKwG,EAAA,GAAK,GAAG,CAAC,CAAC,GACzE,CAAC,IAAI8E,IAAA,CAAK,IAAIG,kBAAA,CAAmB,MAAM,CAAC,GAAGlC,OAAO,GAAG,CAAC,MAAM,GAAG,CAAC,GAAG,MAAM,CAAC,GAAG,GAAG,CAAC,CAAC,EACpF;MACA6C,CAAA,EAAG,CACD,CAAC,IAAIb,IAAA,CAAKV,cAAA,CAAe,MAAM,CAAC,GAAGT,wBAAwB,GAAG,MAAM,CAAC,GAAGpK,IAAA,CAAKwG,EAAA,GAAK,GAAG,CAAC,CAAC,GACvF,CACE,IAAI8E,IAAA,CAAK,IAAIhB,gBAAA,CAAiB,MAAM,GAAG,MAAM,GAAG,GAAG,KAAK,GAAGF,wBAAwB,GACnF,CAAC,MAAM,GAAG,CAAC,GACX,CAAC,GAAG,GAAG,CAACpK,IAAA,CAAKwG,EAAA,GAAK,CAAC,GACnB,CAAC,GAAG,GAAG,IAAK,EACd,EACA,CACE,IAAI8E,IAAA,CAAK,IAAIhB,gBAAA,CAAiB,MAAM,GAAG,MAAM,GAAG,GAAG,KAAK,GAAGF,wBAAwB,GACnF,CAAC,OAAO,GAAG,CAAC,GACZ,CAAC,GAAG,GAAGpK,IAAA,CAAKwG,EAAA,GAAK,CAAC,GAClB,CAAC,GAAG,GAAG,IAAK,EACd,EACA,CACE,IAAI8E,IAAA,CAAK,IAAIhB,gBAAA,CAAiB,MAAM,GAAG,MAAM,GAAG,GAAG,KAAK,GAAGF,wBAAwB,GACnF,CAAC,GAAG,OAAO,CAAC,GACZ,CAACpK,IAAA,CAAKwG,EAAA,EAAI,GAAG,CAAC,GACd,CAAC,GAAG,GAAG,IAAK,EACd,EACA,CACE,IAAI8E,IAAA,CAAK,IAAIhB,gBAAA,CAAiB,MAAM,GAAG,MAAM,GAAG,GAAG,KAAK,GAAGF,wBAAwB,GACnF,CAAC,GAAG,MAAM,CAAC,GACX,CAAC,GAAG,GAAG,CAAC,GACR,CAAC,GAAG,GAAG,IAAK,EACd,CACF;MACAiC,IAAA,EAAM,CAAC,CAAC,IAAId,IAAA,CAAKV,cAAA,CAAe,GAAG,CAAC,GAAGV,WAAW,GAAG,MAAM,CAAC,GAAGnK,IAAA,CAAKwG,EAAA,GAAK,GAAG,CAAC,CAAC,CAAC;IAAA;IAGjF,MAAM8F,YAAA,GAAe;MACnBC,IAAA,EAAM,CAAC,CAAC,IAAIhB,IAAA,CAAKd,YAAA,EAAcrB,SAAA,CAAU7B,KAAA,CAAM,CAAC,GAAG,CAAC,MAAM,GAAG,CAAC,GAAG,MAAM,CAAC,KAAK,GAAG,CAAC,GAAG,QAAQ,CAAC;IAAA;IAG/F,MAAMiF,YAAA,GAAe;MACnBxQ,CAAA,EAAG,CAAC,CAAC,IAAIsP,IAAA,CAAK,IAAImB,aAAA,CAAc,GAAG,KAAK,GAAG,EAAE,GAAGtD,YAAY,GAAG,CAAC,GAAG,GAAG,CAAC,GAAG,CAAC,GAAG,CAACnJ,IAAA,CAAKwG,EAAA,GAAK,GAAG,CAACxG,IAAA,CAAKwG,EAAA,GAAK,CAAC,CAAC,CAAC;MAC1GvK,CAAA,EAAG,CAAC,CAAC,IAAIqP,IAAA,CAAK,IAAImB,aAAA,CAAc,GAAG,KAAK,GAAG,EAAE,GAAGtD,YAAY,GAAG,CAAC,GAAG,GAAG,CAAC,GAAG,CAACnJ,IAAA,CAAKwG,EAAA,GAAK,GAAG,GAAG,CAAC,CAAC,CAAC;MAC9FtK,CAAA,EAAG,CAAC,CAAC,IAAIoP,IAAA,CAAK,IAAImB,aAAA,CAAc,GAAG,KAAK,GAAG,EAAE,GAAGtD,YAAY,GAAG,CAAC,GAAG,GAAG,CAAC,GAAG,CAAC,GAAG,GAAG,CAACnJ,IAAA,CAAKwG,EAAA,GAAK,CAAC,CAAC,CAAC;MAC/F4F,CAAA,EAAG,CAAC,CAAC,IAAId,IAAA,CAAK,IAAImB,aAAA,CAAc,MAAM,KAAK,GAAG,EAAE,GAAGtD,YAAY,CAAC,CAAC;MACjEkD,IAAA,EAAM,CAAC,CAAC,IAAIf,IAAA,CAAK,IAAIoB,cAAA,CAAe,KAAK,IAAI,CAAC,GAAGvD,YAAY,CAAC,CAAC;IAAA;IAGjE,MAAMwD,UAAA,GAAa;MACjB3Q,CAAA,EAAG,CACD,CAAC,IAAIsP,IAAA,CAAKf,mBAAA,EAAqBlB,MAAM,GAAG,CAAC,KAAK,GAAG,CAAC,GAAG,CAAC,GAAG,GAAG,CAACrJ,IAAA,CAAKwG,EAAA,GAAK,CAAC,CAAC,GACzE,CAAC,IAAI+E,IAAA,CAAKd,YAAA,EAAcZ,UAAU,GAAG,MAAM,MAAM,CAAC,KAAK,GAAG,CAAC,CAAC,EAC9D;MACA5N,CAAA,EAAG,CACD,CAAC,IAAIqP,IAAA,CAAKf,mBAAA,EAAqBjB,QAAQ,GAAG,CAAC,GAAG,KAAK,CAAC,CAAC,GACrD,CAAC,IAAIiC,IAAA,CAAKd,YAAA,EAAcX,YAAY,GAAG,MAAM,CAAC,GAAG,GAAG9J,IAAA,CAAKwG,EAAA,GAAK,CAAC,GAAG,CAAC,KAAK,GAAG,CAAC,CAAC,EAC/E;MACAtK,CAAA,EAAG,CACD,CAAC,IAAIoP,IAAA,CAAKf,mBAAA,EAAqBhB,OAAO,GAAG,CAAC,GAAG,GAAG,GAAG,GAAG,CAACvJ,IAAA,CAAKwG,EAAA,GAAK,GAAG,GAAG,CAAC,CAAC,GACzE,CAAC,IAAI+E,IAAA,CAAKd,YAAA,EAAcV,WAAW,GAAG,MAAM,CAAC,GAAG,CAAC/J,IAAA,CAAKwG,EAAA,GAAK,GAAG,CAAC,GAAG,CAAC,KAAK,GAAG,CAAC,CAAC,EAC/E;MACAkF,EAAA,EAAI,CACF,CAAC,IAAIJ,IAAA,CAAKf,mBAAA,EAAqBd,oBAAoB,GAAG,CAAC,MAAM,MAAM,CAAC,GAAG,MAAM,CAAC,GAAG,GAAG,GAAG,CAAC,GACxF,CAAC,IAAI8B,IAAA,CAAKd,YAAA,EAAcP,aAAa,GAAG,CAAC,OAAO,MAAM,CAAC,GAAG,MAAM,CAAC,OAAO,GAAG,CAAC,CAAC,GAC7E,CAAC,IAAIqB,IAAA,CAAKd,YAAA,EAAcP,aAAa,GAAG,CAAC,MAAM,OAAO,CAAC,GAAG,CAAC,GAAG,GAAGlK,IAAA,CAAKwG,EAAA,GAAK,CAAC,GAAG,CAAC,OAAO,GAAG,CAAC,CAAC,EAC9F;MACAoF,EAAA,EAAI,CACF,CAAC,IAAIN,IAAA,CAAKf,mBAAA,EAAqBb,kBAAkB,GAAG,CAAC,GAAG,MAAM,IAAI,GAAG,MAAM,CAAC,KAAK,GAAG,CAAC,CAAC,GACtF,CAAC,IAAI6B,IAAA,CAAKd,YAAA,EAAcT,WAAW,GAAG,CAAC,GAAG,OAAO,IAAI,GAAG,CAAC,GAAG,GAAGhK,IAAA,CAAKwG,EAAA,GAAK,CAAC,GAAG,CAAC,OAAO,GAAG,CAAC,CAAC,GAC1F,CAAC,IAAI+E,IAAA,CAAKd,YAAA,EAAcT,WAAW,GAAG,CAAC,GAAG,MAAM,KAAK,GAAG,CAAC,GAAG,CAAChK,IAAA,CAAKwG,EAAA,GAAK,GAAG,CAAC,GAAG,CAAC,OAAO,GAAG,CAAC,CAAC,EAC7F;MACAqF,EAAA,EAAI,CACF,CAAC,IAAIP,IAAA,CAAKf,mBAAA,EAAqBZ,qBAAqB,GAAG,CAAC,MAAM,GAAG,IAAI,GAAG,MAAM,CAAC,GAAG,KAAK,CAAC,CAAC,GACzF,CAAC,IAAI4B,IAAA,CAAKd,YAAA,EAAcR,cAAc,GAAG,CAAC,OAAO,GAAG,IAAI,GAAG,MAAM,CAAC,OAAO,GAAG,CAAC,CAAC,GAC9E,CAAC,IAAIsB,IAAA,CAAKd,YAAA,EAAcR,cAAc,GAAG,CAAC,MAAM,GAAG,KAAK,GAAG,CAAC,GAAG,CAACjK,IAAA,CAAKwG,EAAA,GAAK,GAAG,CAAC,GAAG,CAAC,OAAO,GAAG,CAAC,CAAC,EAChG;MACAoG,IAAA,EAAM,CAAC,CAAC,IAAItB,IAAA,CAAK,IAAId,WAAA,CAAY,OAAO,OAAO,KAAK,GAAGhB,mBAAA,CAAoBjC,KAAA,EAAO,GAAG,CAAC,KAAK,GAAG,CAAC,CAAC,CAAC;MACjGsF,IAAA,EAAM,CAAC,CAAC,IAAIvB,IAAA,CAAK,IAAId,WAAA,CAAY,OAAO,OAAO,KAAK,GAAGhB,mBAAA,CAAoBjC,KAAA,EAAO,GAAG,CAAC,GAAG,KAAK,CAAC,CAAC,CAAC;MACjGuF,IAAA,EAAM,CAAC,CAAC,IAAIxB,IAAA,CAAK,IAAId,WAAA,CAAY,OAAO,OAAO,KAAK,GAAGhB,mBAAA,CAAoBjC,KAAA,EAAO,GAAG,CAAC,GAAG,GAAG,GAAG,CAAC,CAAC;IAAA;IAGnG,MAAMwF,WAAA,GAAc;MAClB/Q,CAAA,EAAG,CAAC,CAAC,IAAIsP,IAAA,CAAK,IAAIhB,gBAAA,CAAiB,KAAK,GAAG,KAAK,GAAG,GAAG,KAAK,GAAGnB,YAAY,GAAG,CAAC,KAAK,GAAG,CAAC,GAAG,CAAC,GAAG,GAAG,CAACnJ,IAAA,CAAKwG,EAAA,GAAK,CAAC,CAAC,CAAC;MAC/GvK,CAAA,EAAG,CAAC,CAAC,IAAIqP,IAAA,CAAK,IAAIhB,gBAAA,CAAiB,KAAK,GAAG,KAAK,GAAG,GAAG,KAAK,GAAGnB,YAAY,GAAG,CAAC,GAAG,KAAK,CAAC,CAAC,CAAC;MACzFjN,CAAA,EAAG,CAAC,CAAC,IAAIoP,IAAA,CAAK,IAAIhB,gBAAA,CAAiB,KAAK,GAAG,KAAK,GAAG,GAAG,KAAK,GAAGnB,YAAY,GAAG,CAAC,GAAG,GAAG,GAAG,GAAG,CAACnJ,IAAA,CAAKwG,EAAA,GAAK,GAAG,GAAG,CAAC,CAAC,CAAC;MAC9GkF,EAAA,EAAI,CAAC,CAAC,IAAIJ,IAAA,CAAKf,mBAAA,EAAqBpB,YAAY,GAAG,CAAC,MAAM,MAAM,CAAC,GAAG,MAAM,CAAC,GAAG,GAAG,GAAG,CAAC,CAAC;MACtFyC,EAAA,EAAI,CAAC,CAAC,IAAIN,IAAA,CAAKf,mBAAA,EAAqBpB,YAAY,GAAG,CAAC,GAAG,MAAM,IAAI,GAAG,MAAM,CAAC,KAAK,GAAG,CAAC,CAAC,CAAC;MACtF0C,EAAA,EAAI,CAAC,CAAC,IAAIP,IAAA,CAAKf,mBAAA,EAAqBpB,YAAY,GAAG,CAAC,MAAM,GAAG,IAAI,GAAG,MAAM,CAAC,GAAG,KAAK,CAAC,CAAC,CAAC;MACtFyD,IAAA,EAAM,CAAC,CAAC,IAAItB,IAAA,CAAK,IAAId,WAAA,CAAY,KAAK,KAAK,GAAG,GAAGrB,YAAY,GAAG,CAAC,KAAK,GAAG,CAAC,CAAC,CAAC;MAC5E0D,IAAA,EAAM,CAAC,CAAC,IAAIvB,IAAA,CAAK,IAAId,WAAA,CAAY,KAAK,KAAK,GAAG,GAAGrB,YAAY,GAAG,CAAC,GAAG,KAAK,CAAC,CAAC,CAAC;MAC5E2D,IAAA,EAAM,CAAC,CAAC,IAAIxB,IAAA,CAAK,IAAId,WAAA,CAAY,KAAK,KAAK,GAAG,GAAGrB,YAAY,GAAG,CAAC,GAAG,GAAG,GAAG,CAAC,CAAC;IAAA;IAG9E,MAAM6D,WAAA,GAAc;MAClBhR,CAAA,EAAG,CAAC,CAAC,IAAIuP,IAAA,CAAKd,YAAA,EAAcrB,SAAA,CAAU7B,KAAA,CAAM,CAAC,GAAG,CAAC,MAAM,GAAG,CAAC,GAAG,MAAM,CAAC,KAAK,GAAG,CAAC,GAAG,QAAQ,CAAC;MAC1FtL,CAAA,EAAG,CAAC,CAAC,IAAIsP,IAAA,CAAKd,YAAA,EAAcrB,SAAA,CAAU7B,KAAA,EAAO,GAAG,CAAC,GAAG,MAAM,CAAC,GAAG,CAAC,GAAG,GAAGvH,IAAA,CAAKwG,EAAA,GAAK,CAAC,GAAG,CAAC,KAAK,GAAG,CAAC,GAAG,QAAQ,CAAC;MACzGtK,CAAA,EAAG,CAAC,CAAC,IAAIqP,IAAA,CAAKd,YAAA,EAAcrB,SAAA,CAAU7B,KAAA,EAAO,GAAG,CAAC,GAAG,GAAG,IAAI,GAAG,CAAC,GAAG,CAACvH,IAAA,CAAKwG,EAAA,GAAK,GAAG,CAAC,GAAG,CAAC,KAAK,GAAG,CAAC,GAAG,QAAQ,CAAC;IAAA;IAKtG,MAAAyG,UAAA,GAAcC,QAAA,IAA4B;MACxC,MAAA7N,KAAA,GAAQ,IAAI7D,QAAA;MAElB,SAAS+D,IAAA,IAAQ2N,QAAA,EAAU;QACzB,SAASxQ,CAAA,GAAIwQ,QAAA,CAAS3N,IAAI,EAAE5C,MAAA,EAAQD,CAAA,KAAO;UACnC,MAAAL,MAAA,GAAS6Q,QAAA,CAAS3N,IAAI,EAAE7C,CAAC,EAAE,CAAC,EAAE6K,KAAA;UACpC,MAAMvK,QAAA,GAAWkQ,QAAA,CAAS3N,IAAI,EAAE7C,CAAC,EAAE,CAAC;UACpC,MAAMoD,QAAA,GAAWoN,QAAA,CAAS3N,IAAI,EAAE7C,CAAC,EAAE,CAAC;UACpC,MAAMW,KAAA,GAAQ6P,QAAA,CAAS3N,IAAI,EAAE7C,CAAC,EAAE,CAAC;UACjC,MAAMiK,GAAA,GAAMuG,QAAA,CAAS3N,IAAI,EAAE7C,CAAC,EAAE,CAAC;UAG/BL,MAAA,CAAOkD,IAAA,GAAOA,IAAA;UAEdlD,MAAA,CAAOsK,GAAA,GAAMA,GAAA;UAEb,IAAI3J,QAAA,EAAU;YACLX,MAAA,CAAAW,QAAA,CAASsE,GAAA,CAAItE,QAAA,CAAS,CAAC,GAAGA,QAAA,CAAS,CAAC,GAAGA,QAAA,CAAS,CAAC,CAAC;UAC3D;UAEA,IAAI8C,QAAA,EAAU;YACLzD,MAAA,CAAAyD,QAAA,CAASwB,GAAA,CAAIxB,QAAA,CAAS,CAAC,GAAGA,QAAA,CAAS,CAAC,GAAGA,QAAA,CAAS,CAAC,CAAC;UAC3D;UAEA,IAAIzC,KAAA,EAAO;YACFhB,MAAA,CAAAgB,KAAA,CAAMiE,GAAA,CAAIjE,KAAA,CAAM,CAAC,GAAGA,KAAA,CAAM,CAAC,GAAGA,KAAA,CAAM,CAAC,CAAC;UAC/C;UAEAhB,MAAA,CAAO8Q,YAAA,CAAa;UAEd,MAAAC,YAAA,GAAe/Q,MAAA,CAAOqI,QAAA,CAAS6C,KAAA,CAAM;UAC9B6F,YAAA,CAAAC,YAAA,CAAahR,MAAA,CAAOiR,MAAM;UACvCjR,MAAA,CAAOqI,QAAA,GAAW0I,YAAA;UAClB/Q,MAAA,CAAOkR,WAAA,GAAcC,QAAA;UAErBnR,MAAA,CAAOW,QAAA,CAASsE,GAAA,CAAI,GAAG,GAAG,CAAC;UAC3BjF,MAAA,CAAOyD,QAAA,CAASwB,GAAA,CAAI,GAAG,GAAG,CAAC;UAC3BjF,MAAA,CAAOgB,KAAA,CAAMiE,GAAA,CAAI,GAAG,GAAG,CAAC;UAExBjC,KAAA,CAAMwB,GAAA,CAAIxE,MAAM;QAClB;MACF;MAEO,OAAAgD,KAAA;IAAA;IAGT,KAAKA,KAAA,GAAQ;IACb,KAAKC,MAAA,GAAS;IACd,KAAKuG,MAAA,GAAS;IAEd,KAAKhF,GAAA,CAAK,KAAKxB,KAAA,CAAM,WAAW,IAAI4N,UAAA,CAAW5B,cAAc,CAAE;IAC/D,KAAKxK,GAAA,CAAK,KAAKxB,KAAA,CAAM,QAAQ,IAAI4N,UAAA,CAAWd,WAAW,CAAE;IACzD,KAAKtL,GAAA,CAAK,KAAKxB,KAAA,CAAM,OAAO,IAAI4N,UAAA,CAAWN,UAAU,CAAE;IACvD,KAAK9L,GAAA,CAAK,KAAKvB,MAAA,CAAO,WAAW,IAAI2N,UAAA,CAAWnB,eAAe,CAAE;IACjE,KAAKjL,GAAA,CAAK,KAAKvB,MAAA,CAAO,QAAQ,IAAI2N,UAAA,CAAWT,YAAY,CAAE;IAC3D,KAAK3L,GAAA,CAAK,KAAKvB,MAAA,CAAO,OAAO,IAAI2N,UAAA,CAAWF,WAAW,CAAE;IACzD,KAAKlM,GAAA,CAAK,KAAKgF,MAAA,CAAO,WAAW,IAAIoH,UAAA,CAAWlB,eAAe,CAAE;IACjE,KAAKlL,GAAA,CAAK,KAAKgF,MAAA,CAAO,QAAQ,IAAIoH,UAAA,CAAWX,YAAY,CAAE;IAC3D,KAAKzL,GAAA,CAAK,KAAKgF,MAAA,CAAO,OAAO,IAAIoH,UAAA,CAAWD,WAAW,CAAE;IAIpD,KAAA1N,MAAA,CAAO,WAAW,EAAE1C,OAAA,GAAU;IAC9B,KAAA0C,MAAA,CAAO,QAAQ,EAAE1C,OAAA,GAAU;IAC3B,KAAA0C,MAAA,CAAO,OAAO,EAAE1C,OAAA,GAAU;EACjC;AAyTF;AAEA,MAAMkI,sBAAA,SAA+BwG,IAAA,CAAuC;EAI1E7P,YAAA,EAAc;IACZ,MACE,IAAIkQ,aAAA,CAAc,KAAQ,KAAQ,GAAG,CAAC,GACtC,IAAInD,iBAAA,CAAkB;MACpB5L,OAAA,EAAS;MACT6Q,SAAA,EAAW;MACX7E,IAAA,EAAMC,UAAA;MACNF,WAAA,EAAa;MACbZ,OAAA,EAAS;MACTgB,UAAA,EAAY;IAAA,CACb;IAbGnN,aAAA,mCAA2B;IAC5BA,aAAA,eAAO;IAgBNA,aAAA,gBAAQ,IAAIE,OAAA,CAAQ,GAAG,GAAG,CAAC;IAC3BF,aAAA,gBAAQ,IAAIE,OAAA,CAAQ,GAAG,GAAG,CAAC;IAC3BF,aAAA,gBAAQ,IAAIE,OAAA,CAAQ,GAAG,GAAG,CAAC;IAE3BF,aAAA,qBAAa,IAAIE,OAAA;IACjBF,aAAA,oBAAY,IAAIE,OAAA;IAChBF,aAAA,sBAAc,IAAIE,OAAA;IAClBF,aAAA,qBAAa,IAAI+J,OAAA;IACjB/J,aAAA,6BAAqB,IAAIG,UAAA;IAGzB;IAAAH,aAAA,2BAAmB,IAAIG,UAAA;IAEvBH,aAAA,wBAAgB,IAAIE,OAAA;IACpBF,aAAA,0BAAkB,IAAIG,UAAA;IAEtBH,aAAA,cAAM,IAAIE,OAAA;IAEVF,aAAA,eAAsB;IACtBA,aAAA,eAAyC;IACzCA,aAAA,gBAAQ;IAETA,aAAA,4BAAoB,MAAY;MACrC,IAAI+D,KAAA,GAAQ,KAAKA,KAAA;MAEZ,KAAA3C,QAAA,CAASC,IAAA,CAAK,KAAKoB,aAAa;MAErC,IAAI,KAAKjC,IAAA,KAAS,SAAiBuD,KAAA;MAEnC,KAAKqH,KAAA,CAAM1F,GAAA,CAAI,GAAG,GAAG,CAAC,EAAEZ,eAAA,CAAgBf,KAAA,KAAU,UAAU,KAAKrB,eAAA,GAAkB,KAAKsH,kBAAkB;MAC1G,KAAKqB,KAAA,CAAM3F,GAAA,CAAI,GAAG,GAAG,CAAC,EAAEZ,eAAA,CAAgBf,KAAA,KAAU,UAAU,KAAKrB,eAAA,GAAkB,KAAKsH,kBAAkB;MAC1G,KAAKsB,KAAA,CAAM5F,GAAA,CAAI,GAAG,GAAG,CAAC,EAAEZ,eAAA,CAAgBf,KAAA,KAAU,UAAU,KAAKrB,eAAA,GAAkB,KAAKsH,kBAAkB;MAIrG,KAAAmB,WAAA,CAAY9J,IAAA,CAAK,KAAKgK,KAAK;MAEhC,QAAQ,KAAK7K,IAAA;QACX,KAAK;QACL,KAAK;UACH,QAAQ,KAAKS,IAAA;YACX,KAAK;cACH,KAAKkK,WAAA,CAAY9J,IAAA,CAAK,KAAK6B,GAAG,EAAEkD,KAAA,CAAM,KAAKgF,KAAK;cAChD,KAAK0G,SAAA,CAAUzQ,IAAA,CAAK,KAAK+J,KAAK,EAAEhF,KAAA,CAAM,KAAK+E,WAAW;cACtD;YACF,KAAK;cACH,KAAKA,WAAA,CAAY9J,IAAA,CAAK,KAAK6B,GAAG,EAAEkD,KAAA,CAAM,KAAKiF,KAAK;cAChD,KAAKyG,SAAA,CAAUzQ,IAAA,CAAK,KAAKgK,KAAK,EAAEjF,KAAA,CAAM,KAAK+E,WAAW;cACtD;YACF,KAAK;cACH,KAAKA,WAAA,CAAY9J,IAAA,CAAK,KAAK6B,GAAG,EAAEkD,KAAA,CAAM,KAAKkF,KAAK;cAChD,KAAKwG,SAAA,CAAUzQ,IAAA,CAAK,KAAKiK,KAAK,EAAElF,KAAA,CAAM,KAAK+E,WAAW;cACtD;YACF,KAAK;cACE,KAAA2G,SAAA,CAAUzQ,IAAA,CAAK,KAAKiK,KAAK;cAC9B;YACF,KAAK;cACE,KAAAwG,SAAA,CAAUzQ,IAAA,CAAK,KAAK+J,KAAK;cAC9B;YACF,KAAK;cACE,KAAAD,WAAA,CAAY9J,IAAA,CAAK,KAAKiK,KAAK;cAC3B,KAAAwG,SAAA,CAAUzQ,IAAA,CAAK,KAAKgK,KAAK;cAC9B;YACF,KAAK;YACL,KAAK;cACH,KAAKyG,SAAA,CAAUpM,GAAA,CAAI,GAAG,GAAG,CAAC;cAC1B;UACJ;UAEA;QACF,KAAK;QACL;UAEE,KAAKoM,SAAA,CAAUpM,GAAA,CAAI,GAAG,GAAG,CAAC;MAC9B;MAEA,IAAI,KAAKoM,SAAA,CAAU/Q,MAAA,CAAO,MAAM,GAAG;QAE5B,KAAAQ,UAAA,CAAWF,IAAA,CAAK,KAAK2B,gBAAgB;MAAA,OACrC;QACL,KAAK+O,UAAA,CAAWtG,MAAA,CAAO,KAAKpG,UAAA,CAAWK,GAAA,CAAI,GAAG,GAAG,CAAC,GAAG,KAAKoM,SAAA,EAAW,KAAK3G,WAAW;QAEhF,KAAA5J,UAAA,CAAWgK,qBAAA,CAAsB,KAAKwG,UAAU;MACvD;MAEA,MAAM/P,iBAAA,CAAkB;IAAA;EAvF1B;AAyFF", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}