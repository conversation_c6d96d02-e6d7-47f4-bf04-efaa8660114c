{"ast": null, "code": "function decodeText(array) {\n  if (typeof TextDecoder !== \"undefined\") {\n    return new TextDecoder().decode(array);\n  }\n  let s = \"\";\n  for (let i = 0, il = array.length; i < il; i++) {\n    s += String.fromCharCode(array[i]);\n  }\n  try {\n    return decodeURIComponent(escape(s));\n  } catch (e) {\n    return s;\n  }\n}\nexport { decodeText };", "map": {"version": 3, "names": ["decodeText", "array", "TextDecoder", "decode", "s", "i", "il", "length", "String", "fromCharCode", "decodeURIComponent", "escape", "e"], "sources": ["C:\\Users\\<USER>\\Documents\\Apps_Creations\\SiahMan_Racing4Speed_WebGame\\siah-man-racing\\node_modules\\src\\_polyfill\\LoaderUtils.js"], "sourcesContent": ["export function decodeText(array) {\n  if (typeof TextDecoder !== 'undefined') {\n    return new TextDecoder().decode(array)\n  }\n\n  // Avoid the String.fromCharCode.apply(null, array) shortcut, which\n  // throws a \"maximum call stack size exceeded\" error for large arrays.\n\n  let s = ''\n\n  for (let i = 0, il = array.length; i < il; i++) {\n    // Implicitly assumes little-endian.\n    s += String.fromCharCode(array[i])\n  }\n\n  try {\n    // merges multi-byte utf-8 characters.\n\n    return decodeURIComponent(escape(s))\n  } catch (e) {\n    // see https://github.com/mrdoob/three.js/issues/16358\n\n    return s\n  }\n}\n"], "mappings": "AAAO,SAASA,WAAWC,KAAA,EAAO;EAChC,IAAI,OAAOC,WAAA,KAAgB,aAAa;IACtC,OAAO,IAAIA,WAAA,CAAW,EAAGC,MAAA,CAAOF,KAAK;EACtC;EAKD,IAAIG,CAAA,GAAI;EAER,SAASC,CAAA,GAAI,GAAGC,EAAA,GAAKL,KAAA,CAAMM,MAAA,EAAQF,CAAA,GAAIC,EAAA,EAAID,CAAA,IAAK;IAE9CD,CAAA,IAAKI,MAAA,CAAOC,YAAA,CAAaR,KAAA,CAAMI,CAAC,CAAC;EAClC;EAED,IAAI;IAGF,OAAOK,kBAAA,CAAmBC,MAAA,CAAOP,CAAC,CAAC;EACpC,SAAQQ,CAAA,EAAP;IAGA,OAAOR,CAAA;EACR;AACH", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}