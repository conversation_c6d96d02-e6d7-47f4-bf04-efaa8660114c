{"ast": null, "code": "var __defProp = Object.defineProperty;\nvar __defNormalProp = (obj, key, value) => key in obj ? __defProp(obj, key, {\n  enumerable: true,\n  configurable: true,\n  writable: true,\n  value\n}) : obj[key] = value;\nvar __publicField = (obj, key, value) => {\n  __defNormalProp(obj, typeof key !== \"symbol\" ? key + \"\" : key, value);\n  return value;\n};\nimport { Pass } from \"./Pass.js\";\nclass MaskPass extends Pass {\n  constructor(scene, camera) {\n    super();\n    __publicField(this, \"scene\");\n    __publicField(this, \"camera\");\n    __publicField(this, \"inverse\");\n    this.scene = scene;\n    this.camera = camera;\n    this.clear = true;\n    this.needsSwap = false;\n    this.inverse = false;\n  }\n  render(renderer, writeBuffer, readBuffer) {\n    const context = renderer.getContext();\n    const state = renderer.state;\n    state.buffers.color.setMask(false);\n    state.buffers.depth.setMask(false);\n    state.buffers.color.setLocked(true);\n    state.buffers.depth.setLocked(true);\n    let writeValue, clearValue;\n    if (this.inverse) {\n      writeValue = 0;\n      clearValue = 1;\n    } else {\n      writeValue = 1;\n      clearValue = 0;\n    }\n    state.buffers.stencil.setTest(true);\n    state.buffers.stencil.setOp(context.REPLACE, context.REPLACE, context.REPLACE);\n    state.buffers.stencil.setFunc(context.ALWAYS, writeValue, 4294967295);\n    state.buffers.stencil.setClear(clearValue);\n    state.buffers.stencil.setLocked(true);\n    renderer.setRenderTarget(readBuffer);\n    if (this.clear) renderer.clear();\n    renderer.render(this.scene, this.camera);\n    renderer.setRenderTarget(writeBuffer);\n    if (this.clear) renderer.clear();\n    renderer.render(this.scene, this.camera);\n    state.buffers.color.setLocked(false);\n    state.buffers.depth.setLocked(false);\n    state.buffers.stencil.setLocked(false);\n    state.buffers.stencil.setFunc(context.EQUAL, 1, 4294967295);\n    state.buffers.stencil.setOp(context.KEEP, context.KEEP, context.KEEP);\n    state.buffers.stencil.setLocked(true);\n  }\n}\nclass ClearMaskPass extends Pass {\n  constructor() {\n    super();\n    this.needsSwap = false;\n  }\n  render(renderer) {\n    renderer.state.buffers.stencil.setLocked(false);\n    renderer.state.buffers.stencil.setTest(false);\n  }\n}\nexport { ClearMaskPass, MaskPass };", "map": {"version": 3, "names": ["<PERSON><PERSON><PERSON>", "Pass", "constructor", "scene", "camera", "__publicField", "clear", "needsSwap", "inverse", "render", "renderer", "writeBuffer", "readBuffer", "context", "getContext", "state", "buffers", "color", "setMask", "depth", "setLocked", "writeValue", "clearValue", "stencil", "setTest", "setOp", "REPLACE", "setFunc", "ALWAYS", "setClear", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "EQUAL", "KEEP", "ClearMaskPass"], "sources": ["C:\\Users\\<USER>\\Documents\\Apps_Creations\\SiahMan_Racing4Speed_WebGame\\siah-man-racing\\node_modules\\src\\postprocessing\\MaskPass.ts"], "sourcesContent": ["import { Camera, Scene, WebGLRenderer, WebGLRenderTarget } from 'three'\nimport { Pass } from './Pass'\n\nclass MaskPass extends Pass {\n  public scene: Scene\n  public camera: Camera\n  public inverse: boolean\n\n  constructor(scene: Scene, camera: Camera) {\n    super()\n\n    this.scene = scene\n    this.camera = camera\n\n    this.clear = true\n    this.needsSwap = false\n\n    this.inverse = false\n  }\n\n  public render(\n    renderer: WebGLRenderer,\n    writeBuffer: WebGLRenderTarget,\n    readBuffer: WebGLRenderTarget /*, deltaTime, maskActive */,\n  ): void {\n    const context = renderer.getContext()\n    const state = renderer.state\n\n    // don't update color or depth\n\n    state.buffers.color.setMask(false)\n    state.buffers.depth.setMask(false)\n\n    // lock buffers\n\n    state.buffers.color.setLocked(true)\n    state.buffers.depth.setLocked(true)\n\n    // set up stencil\n\n    let writeValue, clearValue\n\n    if (this.inverse) {\n      writeValue = 0\n      clearValue = 1\n    } else {\n      writeValue = 1\n      clearValue = 0\n    }\n\n    state.buffers.stencil.setTest(true)\n    state.buffers.stencil.setOp(context.REPLACE, context.REPLACE, context.REPLACE)\n    state.buffers.stencil.setFunc(context.ALWAYS, writeValue, 0xffffffff)\n    state.buffers.stencil.setClear(clearValue)\n    state.buffers.stencil.setLocked(true)\n\n    // draw into the stencil buffer\n\n    renderer.setRenderTarget(readBuffer)\n    if (this.clear) renderer.clear()\n    renderer.render(this.scene, this.camera)\n\n    renderer.setRenderTarget(writeBuffer)\n    if (this.clear) renderer.clear()\n    renderer.render(this.scene, this.camera)\n\n    // unlock color and depth buffer for subsequent rendering\n\n    state.buffers.color.setLocked(false)\n    state.buffers.depth.setLocked(false)\n\n    // only render where stencil is set to 1\n\n    state.buffers.stencil.setLocked(false)\n    state.buffers.stencil.setFunc(context.EQUAL, 1, 0xffffffff) // draw if == 1\n    state.buffers.stencil.setOp(context.KEEP, context.KEEP, context.KEEP)\n    state.buffers.stencil.setLocked(true)\n  }\n}\n\nclass ClearMaskPass extends Pass {\n  constructor() {\n    super()\n    this.needsSwap = false\n  }\n\n  public render(renderer: WebGLRenderer /*, writeBuffer, readBuffer, deltaTime, maskActive */): void {\n    renderer.state.buffers.stencil.setLocked(false)\n    renderer.state.buffers.stencil.setTest(false)\n  }\n}\n\nexport { MaskPass, ClearMaskPass }\n"], "mappings": ";;;;;;;;;;;;AAGA,MAAMA,QAAA,SAAiBC,IAAA,CAAK;EAK1BC,YAAYC,KAAA,EAAcC,MAAA,EAAgB;IAClC;IALDC,aAAA;IACAA,aAAA;IACAA,aAAA;IAKL,KAAKF,KAAA,GAAQA,KAAA;IACb,KAAKC,MAAA,GAASA,MAAA;IAEd,KAAKE,KAAA,GAAQ;IACb,KAAKC,SAAA,GAAY;IAEjB,KAAKC,OAAA,GAAU;EACjB;EAEOC,OACLC,QAAA,EACAC,WAAA,EACAC,UAAA,EACM;IACA,MAAAC,OAAA,GAAUH,QAAA,CAASI,UAAA;IACzB,MAAMC,KAAA,GAAQL,QAAA,CAASK,KAAA;IAIjBA,KAAA,CAAAC,OAAA,CAAQC,KAAA,CAAMC,OAAA,CAAQ,KAAK;IAC3BH,KAAA,CAAAC,OAAA,CAAQG,KAAA,CAAMD,OAAA,CAAQ,KAAK;IAI3BH,KAAA,CAAAC,OAAA,CAAQC,KAAA,CAAMG,SAAA,CAAU,IAAI;IAC5BL,KAAA,CAAAC,OAAA,CAAQG,KAAA,CAAMC,SAAA,CAAU,IAAI;IAIlC,IAAIC,UAAA,EAAYC,UAAA;IAEhB,IAAI,KAAKd,OAAA,EAAS;MACHa,UAAA;MACAC,UAAA;IAAA,OACR;MACQD,UAAA;MACAC,UAAA;IACf;IAEMP,KAAA,CAAAC,OAAA,CAAQO,OAAA,CAAQC,OAAA,CAAQ,IAAI;IAC5BT,KAAA,CAAAC,OAAA,CAAQO,OAAA,CAAQE,KAAA,CAAMZ,OAAA,CAAQa,OAAA,EAASb,OAAA,CAAQa,OAAA,EAASb,OAAA,CAAQa,OAAO;IAC7EX,KAAA,CAAMC,OAAA,CAAQO,OAAA,CAAQI,OAAA,CAAQd,OAAA,CAAQe,MAAA,EAAQP,UAAA,EAAY,UAAU;IAC9DN,KAAA,CAAAC,OAAA,CAAQO,OAAA,CAAQM,QAAA,CAASP,UAAU;IACnCP,KAAA,CAAAC,OAAA,CAAQO,OAAA,CAAQH,SAAA,CAAU,IAAI;IAIpCV,QAAA,CAASoB,eAAA,CAAgBlB,UAAU;IACnC,IAAI,KAAKN,KAAA,EAAOI,QAAA,CAASJ,KAAA,CAAM;IAC/BI,QAAA,CAASD,MAAA,CAAO,KAAKN,KAAA,EAAO,KAAKC,MAAM;IAEvCM,QAAA,CAASoB,eAAA,CAAgBnB,WAAW;IACpC,IAAI,KAAKL,KAAA,EAAOI,QAAA,CAASJ,KAAA,CAAM;IAC/BI,QAAA,CAASD,MAAA,CAAO,KAAKN,KAAA,EAAO,KAAKC,MAAM;IAIjCW,KAAA,CAAAC,OAAA,CAAQC,KAAA,CAAMG,SAAA,CAAU,KAAK;IAC7BL,KAAA,CAAAC,OAAA,CAAQG,KAAA,CAAMC,SAAA,CAAU,KAAK;IAI7BL,KAAA,CAAAC,OAAA,CAAQO,OAAA,CAAQH,SAAA,CAAU,KAAK;IACrCL,KAAA,CAAMC,OAAA,CAAQO,OAAA,CAAQI,OAAA,CAAQd,OAAA,CAAQkB,KAAA,EAAO,GAAG,UAAU;IACpDhB,KAAA,CAAAC,OAAA,CAAQO,OAAA,CAAQE,KAAA,CAAMZ,OAAA,CAAQmB,IAAA,EAAMnB,OAAA,CAAQmB,IAAA,EAAMnB,OAAA,CAAQmB,IAAI;IAC9DjB,KAAA,CAAAC,OAAA,CAAQO,OAAA,CAAQH,SAAA,CAAU,IAAI;EACtC;AACF;AAEA,MAAMa,aAAA,SAAsBhC,IAAA,CAAK;EAC/BC,YAAA,EAAc;IACN;IACN,KAAKK,SAAA,GAAY;EACnB;EAEOE,OAAOC,QAAA,EAAqF;IACjGA,QAAA,CAASK,KAAA,CAAMC,OAAA,CAAQO,OAAA,CAAQH,SAAA,CAAU,KAAK;IAC9CV,QAAA,CAASK,KAAA,CAAMC,OAAA,CAAQO,OAAA,CAAQC,OAAA,CAAQ,KAAK;EAC9C;AACF", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}