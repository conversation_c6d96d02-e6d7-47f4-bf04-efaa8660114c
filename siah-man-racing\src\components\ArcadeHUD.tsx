import React, { useEffect, useState } from 'react';
import { useSelector } from 'react-redux';
import styled, { keyframes, css } from 'styled-components';
import { RootState } from '../store';

// Exciting animations for a 7-year-old!
const bounce = keyframes`
  0%, 20%, 50%, 80%, 100% { transform: translateY(0); }
  40% { transform: translateY(-10px); }
  60% { transform: translateY(-5px); }
`;

const glow = keyframes`
  0%, 100% { box-shadow: 0 0 5px #00ff00; }
  50% { box-shadow: 0 0 20px #00ff00, 0 0 30px #00ff00; }
`;

const rainbow = keyframes`
  0% { color: #ff0000; }
  16% { color: #ff8000; }
  33% { color: #ffff00; }
  50% { color: #00ff00; }
  66% { color: #0080ff; }
  83% { color: #8000ff; }
  100% { color: #ff0000; }
`;

const pulse = keyframes`
  0%, 100% { transform: scale(1); }
  50% { transform: scale(1.1); }
`;

// Styled components for the arcade HUD
const HUDContainer = styled.div`
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  pointer-events: none;
  z-index: 1000;
  font-family: 'Arial Black', Arial, sans-serif;
`;

const TopBar = styled.div`
  position: absolute;
  top: 20px;
  left: 20px;
  right: 20px;
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
`;

const ScoreDisplay = styled.div<{ $isGlowing?: boolean }>`
  background: linear-gradient(45deg, #ff6b00, #ffd700);
  border: 3px solid #fff;
  border-radius: 15px;
  padding: 10px 20px;
  color: #fff;
  font-size: 24px;
  font-weight: bold;
  text-shadow: 2px 2px 4px rgba(0,0,0,0.8);
  animation: ${props => props.$isGlowing ? glow : 'none'} 1s ease-in-out infinite;
  
  .score-label {
    font-size: 14px;
    opacity: 0.9;
  }
  
  .score-value {
    font-size: 28px;
    animation: ${props => props.$isGlowing ? rainbow : 'none'} 2s linear infinite;
  }
`;

const ComboDisplay = styled.div<{ $multiplier: number }>`
  background: linear-gradient(45deg, #ff00ff, #00ffff);
  border: 3px solid #fff;
  border-radius: 15px;
  padding: 8px 16px;
  color: #fff;
  font-size: 18px;
  font-weight: bold;
  text-shadow: 2px 2px 4px rgba(0,0,0,0.8);
  animation: ${props => props.$multiplier > 1 ? bounce : 'none'} 0.6s ease-in-out infinite;
  
  ${props => props.$multiplier > 3 && css`
    animation: ${pulse} 0.3s ease-in-out infinite, ${rainbow} 1s linear infinite;
  `}
`;

const PowerUpBar = styled.div`
  position: absolute;
  top: 100px;
  right: 20px;
  display: flex;
  flex-direction: column;
  gap: 10px;
`;

const PowerUpIcon = styled.div<{ $type: string; $timeLeft: number }>`
  width: 60px;
  height: 60px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 24px;
  font-weight: bold;
  border: 3px solid #fff;
  position: relative;
  animation: ${glow} 1s ease-in-out infinite;
  
  ${props => {
    switch (props.$type) {
      case 'invincibility':
        return css`background: linear-gradient(45deg, #ffd700, #ffff00);`;
      case 'mega_speed':
        return css`background: linear-gradient(45deg, #ff0000, #ff8000);`;
      case 'coin_magnet':
        return css`background: linear-gradient(45deg, #00ff00, #80ff00);`;
      case 'double_points':
        return css`background: linear-gradient(45deg, #8000ff, #ff00ff);`;
      default:
        return css`background: linear-gradient(45deg, #666, #999);`;
    }
  }}
  
  &::after {
    content: '';
    position: absolute;
    top: -3px;
    left: -3px;
    right: -3px;
    bottom: -3px;
    border-radius: 50%;
    background: conic-gradient(
      transparent 0deg,
      transparent ${props => (1 - props.$timeLeft) * 360}deg,
      rgba(255,255,255,0.8) ${props => (1 - props.$timeLeft) * 360}deg,
      rgba(255,255,255,0.8) 360deg
    );
    z-index: -1;
  }
`;

const BottomBar = styled.div`
  position: absolute;
  bottom: 20px;
  left: 20px;
  right: 20px;
  display: flex;
  justify-content: space-between;
  align-items: flex-end;
`;

const CoinsDisplay = styled.div`
  background: linear-gradient(45deg, #ffd700, #ffff80);
  border: 3px solid #fff;
  border-radius: 15px;
  padding: 8px 16px;
  color: #000;
  font-size: 20px;
  font-weight: bold;
  display: flex;
  align-items: center;
  gap: 8px;
  
  .coin-icon {
    font-size: 24px;
    animation: ${bounce} 1s ease-in-out infinite;
  }
`;

const StatusEffects = styled.div`
  display: flex;
  gap: 10px;
  align-items: center;
`;

const StatusEffect = styled.div<{ $type: string }>`
  padding: 6px 12px;
  border-radius: 20px;
  font-size: 14px;
  font-weight: bold;
  color: #fff;
  text-shadow: 1px 1px 2px rgba(0,0,0,0.8);
  animation: ${pulse} 1s ease-in-out infinite;
  
  ${props => {
    switch (props.$type) {
      case 'jumping':
        return css`background: linear-gradient(45deg, #00aaff, #0080ff);`;
      case 'drifting':
        return css`background: linear-gradient(45deg, #ff6600, #ff8800);`;
      case 'nitro':
        return css`background: linear-gradient(45deg, #ff0000, #ff4400);`;
      case 'speed_boost':
        return css`background: linear-gradient(45deg, #ff00aa, #ff44cc);`;
      default:
        return css`background: linear-gradient(45deg, #666, #888);`;
    }
  }}
`;

const FloatingText = styled.div<{ $x: number; $y: number; $type: string }>`
  position: absolute;
  left: ${props => props.$x}px;
  top: ${props => props.$y}px;
  font-size: 20px;
  font-weight: bold;
  color: #fff;
  text-shadow: 2px 2px 4px rgba(0,0,0,0.8);
  animation: ${bounce} 0.5s ease-out, fadeOut 2s ease-out forwards;
  pointer-events: none;
  z-index: 1001;
  
  ${props => {
    switch (props.$type) {
      case 'score':
        return css`color: #ffd700; animation: ${bounce} 0.5s ease-out, ${rainbow} 2s linear infinite;`;
      case 'combo':
        return css`color: #ff00ff; font-size: 24px;`;
      case 'perfect':
        return css`color: #00ff00; font-size: 28px; animation: ${pulse} 0.3s ease-out infinite;`;
      default:
        return css`color: #fff;`;
    }
  }}
  
  @keyframes fadeOut {
    0% { opacity: 1; transform: translateY(0); }
    100% { opacity: 0; transform: translateY(-50px); }
  }
`;

interface ArcadeHUDProps {
  visible?: boolean;
}

const ArcadeHUD: React.FC<ArcadeHUDProps> = ({ visible = true }) => {
  const { vehicleState, raceState } = useSelector((state: RootState) => state.game);
  const [floatingTexts, setFloatingTexts] = useState<Array<{
    id: number;
    x: number;
    y: number;
    text: string;
    type: string;
  }>>([]);
  const [lastScore, setLastScore] = useState(0);

  // Add floating text when score increases
  useEffect(() => {
    if (vehicleState.totalScore && vehicleState.totalScore > lastScore) {
      const scoreDiff = vehicleState.totalScore - lastScore;
      if (scoreDiff > 0) {
        const newText = {
          id: Date.now(),
          x: Math.random() * 200 + 100,
          y: Math.random() * 100 + 200,
          text: `+${scoreDiff}`,
          type: scoreDiff > 100 ? 'perfect' : scoreDiff > 50 ? 'combo' : 'score'
        };
        
        setFloatingTexts(prev => [...prev, newText]);
        
        // Remove after animation
        setTimeout(() => {
          setFloatingTexts(prev => prev.filter(t => t.id !== newText.id));
        }, 2000);
      }
      setLastScore(vehicleState.totalScore);
    }
  }, [vehicleState.totalScore, lastScore]);

  if (!visible || !raceState.isRaceStarted) return null;

  const now = Date.now();
  const activePowerUps = [
    vehicleState.invincibilityUntil && now < vehicleState.invincibilityUntil && {
      type: 'invincibility',
      timeLeft: (vehicleState.invincibilityUntil - now) / 5000,
      icon: '⭐'
    },
    vehicleState.speedBoostUntil && now < vehicleState.speedBoostUntil && {
      type: 'mega_speed',
      timeLeft: (vehicleState.speedBoostUntil - now) / 3000,
      icon: '🚀'
    },
    vehicleState.coinMagnetUntil && now < vehicleState.coinMagnetUntil && {
      type: 'coin_magnet',
      timeLeft: (vehicleState.coinMagnetUntil - now) / 8000,
      icon: '🧲'
    },
    vehicleState.doublePointsUntil && now < vehicleState.doublePointsUntil && {
      type: 'double_points',
      timeLeft: (vehicleState.doublePointsUntil - now) / 10000,
      icon: '2️⃣'
    }
  ].filter(Boolean);

  const activeEffects = [
    vehicleState.isJumping && { type: 'jumping', text: 'AIRBORNE!' },
    vehicleState.isDrifting && { type: 'drifting', text: 'DRIFTING!' },
    vehicleState.isNitroActive && { type: 'nitro', text: 'NITRO!' },
    now < (vehicleState.speedBoostUntil || 0) && { type: 'speed_boost', text: 'SPEED BOOST!' }
  ].filter(Boolean);

  return (
    <HUDContainer>
      <TopBar>
        <ScoreDisplay $isGlowing={!!(vehicleState.comboMultiplier && vehicleState.comboMultiplier > 2)}>
          <div className="score-label">SCORE</div>
          <div className="score-value">{vehicleState.totalScore?.toLocaleString() || 0}</div>
        </ScoreDisplay>
        
        {vehicleState.comboMultiplier && vehicleState.comboMultiplier > 1 && (
          <ComboDisplay $multiplier={vehicleState.comboMultiplier}>
            COMBO x{vehicleState.comboMultiplier.toFixed(1)}
          </ComboDisplay>
        )}
      </TopBar>

      <PowerUpBar>
        {activePowerUps.map((powerUp: any, index) => (
          <PowerUpIcon
            key={powerUp.type}
            $type={powerUp.type}
            $timeLeft={powerUp.timeLeft}
          >
            {powerUp.icon}
          </PowerUpIcon>
        ))}
      </PowerUpBar>

      <BottomBar>
        <CoinsDisplay>
          <span className="coin-icon">🪙</span>
          {vehicleState.coinsCollected || 0}
        </CoinsDisplay>
        
        <StatusEffects>
          {activeEffects.map((effect: any, index) => (
            <StatusEffect key={effect.type} $type={effect.type}>
              {effect.text}
            </StatusEffect>
          ))}
        </StatusEffects>
      </BottomBar>

      {/* Floating score texts */}
      {floatingTexts.map(text => (
        <FloatingText
          key={text.id}
          $x={text.x}
          $y={text.y}
          $type={text.type}
        >
          {text.text}
        </FloatingText>
      ))}
    </HUDContainer>
  );
};

export default ArcadeHUD;
