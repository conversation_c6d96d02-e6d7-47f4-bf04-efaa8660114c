{"ast": null, "code": "import { Vector3, <PERSON>ur<PERSON> } from \"three\";\nimport { ParametricGeometry } from \"./ParametricGeometry.js\";\nclass TubeGeometry extends ParametricGeometry {\n  constructor(path, segments = 64, radius = 1, segmentsRadius = 8, closed = false) {\n    const numpoints = segments + 1;\n    const frames = path.computeFrenetFrames(segments, closed),\n      tangents = frames.tangents,\n      normals = frames.normals,\n      binormals = frames.binormals;\n    const position = new Vector3();\n    function ParametricTube(u, v, target) {\n      v *= 2 * Math.PI;\n      const i = Math.floor(u * (numpoints - 1));\n      path.getPointAt(u, position);\n      const normal = normals[i];\n      const binormal = binormals[i];\n      const cx = -radius * Math.cos(v);\n      const cy = radius * Math.sin(v);\n      position.x += cx * normal.x + cy * binormal.x;\n      position.y += cx * normal.y + cy * binormal.y;\n      position.z += cx * normal.z + cy * binormal.z;\n      target.copy(position);\n    }\n    super(ParametricTube, segments, segmentsRadius);\n    this.tangents = tangents;\n    this.normals = normals;\n    this.binormals = binormals;\n    this.path = path;\n    this.segments = segments;\n    this.radius = radius;\n    this.segmentsRadius = segmentsRadius;\n    this.closed = closed;\n  }\n}\nconst ParametricGeometries = {\n  klein: function (v, u, target) {\n    u *= Math.PI;\n    v *= 2 * Math.PI;\n    u = u * 2;\n    let x, z;\n    if (u < Math.PI) {\n      x = 3 * Math.cos(u) * (1 + Math.sin(u)) + 2 * (1 - Math.cos(u) / 2) * Math.cos(u) * Math.cos(v);\n      z = -8 * Math.sin(u) - 2 * (1 - Math.cos(u) / 2) * Math.sin(u) * Math.cos(v);\n    } else {\n      x = 3 * Math.cos(u) * (1 + Math.sin(u)) + 2 * (1 - Math.cos(u) / 2) * Math.cos(v + Math.PI);\n      z = -8 * Math.sin(u);\n    }\n    const y = -2 * (1 - Math.cos(u) / 2) * Math.sin(v);\n    target.set(x, y, z);\n  },\n  plane: function (width, height) {\n    return function (u, v, target) {\n      const x = u * width;\n      const y = 0;\n      const z = v * height;\n      target.set(x, y, z);\n    };\n  },\n  mobius: function (u, t, target) {\n    u = u - 0.5;\n    const v = 2 * Math.PI * t;\n    const a = 2;\n    const x = Math.cos(v) * (a + u * Math.cos(v / 2));\n    const y = Math.sin(v) * (a + u * Math.cos(v / 2));\n    const z = u * Math.sin(v / 2);\n    target.set(x, y, z);\n  },\n  mobius3d: function (u, t, target) {\n    u *= Math.PI;\n    t *= 2 * Math.PI;\n    u = u * 2;\n    const phi = u / 2;\n    const major = 2.25,\n      a = 0.125,\n      b = 0.65;\n    let x = a * Math.cos(t) * Math.cos(phi) - b * Math.sin(t) * Math.sin(phi);\n    const z = a * Math.cos(t) * Math.sin(phi) + b * Math.sin(t) * Math.cos(phi);\n    const y = (major + x) * Math.sin(u);\n    x = (major + x) * Math.cos(u);\n    target.set(x, y, z);\n  },\n  TubeGeometry,\n  TorusKnotGeometry: class TorusKnotGeometry extends TubeGeometry {\n    constructor(radius = 200, tube = 40, segmentsT = 64, segmentsR = 8, p = 2, q = 3) {\n      class TorusKnotCurve extends Curve {\n        getPoint(t, optionalTarget = new Vector3()) {\n          const point = optionalTarget;\n          t *= Math.PI * 2;\n          const r = 0.5;\n          const x = (1 + r * Math.cos(q * t)) * Math.cos(p * t);\n          const y = (1 + r * Math.cos(q * t)) * Math.sin(p * t);\n          const z = r * Math.sin(q * t);\n          return point.set(x, y, z).multiplyScalar(radius);\n        }\n      }\n      const segments = segmentsT;\n      const radiusSegments = segmentsR;\n      const extrudePath = new TorusKnotCurve();\n      super(extrudePath, segments, tube, radiusSegments, true, false);\n      this.radius = radius;\n      this.tube = tube;\n      this.segmentsT = segmentsT;\n      this.segmentsR = segmentsR;\n      this.p = p;\n      this.q = q;\n    }\n  },\n  SphereGeometry: class SphereGeometry extends ParametricGeometry {\n    constructor(size, u, v) {\n      function sphere(u2, v2, target) {\n        u2 *= Math.PI;\n        v2 *= 2 * Math.PI;\n        const x = size * Math.sin(u2) * Math.cos(v2);\n        const y = size * Math.sin(u2) * Math.sin(v2);\n        const z = size * Math.cos(u2);\n        target.set(x, y, z);\n      }\n      super(sphere, u, v);\n    }\n  },\n  PlaneGeometry: class PlaneGeometry extends ParametricGeometry {\n    constructor(width, depth, segmentsWidth, segmentsDepth) {\n      function plane(u, v, target) {\n        const x = u * width;\n        const y = 0;\n        const z = v * depth;\n        target.set(x, y, z);\n      }\n      super(plane, segmentsWidth, segmentsDepth);\n    }\n  }\n};\nexport { ParametricGeometries };", "map": {"version": 3, "names": ["TubeGeometry", "ParametricGeometry", "constructor", "path", "segments", "radius", "segmentsRadius", "closed", "numpoints", "frames", "computeFrenetFrames", "tangents", "normals", "binormals", "position", "Vector3", "ParametricTube", "u", "v", "target", "Math", "PI", "i", "floor", "getPointAt", "normal", "binormal", "cx", "cos", "cy", "sin", "x", "y", "z", "copy", "ParametricGeometries", "klein", "set", "plane", "width", "height", "mobius", "t", "a", "mobius3d", "phi", "major", "b", "TorusKnotGeometry", "tube", "segmentsT", "segmentsR", "p", "q", "TorusKnotCurve", "Curve", "getPoint", "optionalTarget", "point", "r", "multiplyScalar", "radiusSegments", "extrude<PERSON><PERSON>", "SphereGeometry", "size", "sphere", "u2", "v2", "PlaneGeometry", "depth", "segmentsWidth", "segmentsDepth"], "sources": ["C:\\Users\\<USER>\\Documents\\Apps_Creations\\SiahMan_Racing4Speed_WebGame\\siah-man-racing\\node_modules\\src\\geometries\\ParametricGeometries.js"], "sourcesContent": ["import { <PERSON>ur<PERSON>, Vector3 } from 'three'\nimport { ParametricGeometry } from './ParametricGeometry'\n\nclass TubeGeometry extends ParametricGeometry {\n  constructor(path, segments = 64, radius = 1, segmentsRadius = 8, closed = false) {\n    const numpoints = segments + 1\n\n    const frames = path.computeFrenetFrames(segments, closed),\n      tangents = frames.tangents,\n      normals = frames.normals,\n      binormals = frames.binormals\n\n    const position = new Vector3()\n\n    function ParametricTube(u, v, target) {\n      v *= 2 * Math.PI\n\n      const i = Math.floor(u * (numpoints - 1))\n\n      path.getPointAt(u, position)\n\n      const normal = normals[i]\n      const binormal = binormals[i]\n\n      const cx = -radius * Math.cos(v) // TODO: Hack: Negating it so it faces outside.\n      const cy = radius * Math.sin(v)\n\n      position.x += cx * normal.x + cy * binormal.x\n      position.y += cx * normal.y + cy * binormal.y\n      position.z += cx * normal.z + cy * binormal.z\n\n      target.copy(position)\n    }\n\n    super(ParametricTube, segments, segmentsRadius)\n\n    // proxy internals\n\n    this.tangents = tangents\n    this.normals = normals\n    this.binormals = binormals\n\n    this.path = path\n    this.segments = segments\n    this.radius = radius\n    this.segmentsRadius = segmentsRadius\n    this.closed = closed\n  }\n}\n\n/**\n * Experimental primitive geometry creation using Surface Parametric equations\n */\nconst ParametricGeometries = {\n  klein: function (v, u, target) {\n    u *= Math.PI\n    v *= 2 * Math.PI\n\n    u = u * 2\n    let x, z\n    if (u < Math.PI) {\n      x = 3 * Math.cos(u) * (1 + Math.sin(u)) + 2 * (1 - Math.cos(u) / 2) * Math.cos(u) * Math.cos(v)\n      z = -8 * Math.sin(u) - 2 * (1 - Math.cos(u) / 2) * Math.sin(u) * Math.cos(v)\n    } else {\n      x = 3 * Math.cos(u) * (1 + Math.sin(u)) + 2 * (1 - Math.cos(u) / 2) * Math.cos(v + Math.PI)\n      z = -8 * Math.sin(u)\n    }\n\n    const y = -2 * (1 - Math.cos(u) / 2) * Math.sin(v)\n\n    target.set(x, y, z)\n  },\n\n  plane: function (width, height) {\n    return function (u, v, target) {\n      const x = u * width\n      const y = 0\n      const z = v * height\n\n      target.set(x, y, z)\n    }\n  },\n\n  mobius: function (u, t, target) {\n    // flat mobius strip\n    // http://www.wolframalpha.com/input/?i=M%C3%B6bius+strip+parametric+equations&lk=1&a=ClashPrefs_*Surface.MoebiusStrip.SurfaceProperty.ParametricEquations-\n    u = u - 0.5\n    const v = 2 * Math.PI * t\n\n    const a = 2\n\n    const x = Math.cos(v) * (a + u * Math.cos(v / 2))\n    const y = Math.sin(v) * (a + u * Math.cos(v / 2))\n    const z = u * Math.sin(v / 2)\n\n    target.set(x, y, z)\n  },\n\n  mobius3d: function (u, t, target) {\n    // volumetric mobius strip\n\n    u *= Math.PI\n    t *= 2 * Math.PI\n\n    u = u * 2\n    const phi = u / 2\n    const major = 2.25,\n      a = 0.125,\n      b = 0.65\n\n    let x = a * Math.cos(t) * Math.cos(phi) - b * Math.sin(t) * Math.sin(phi)\n    const z = a * Math.cos(t) * Math.sin(phi) + b * Math.sin(t) * Math.cos(phi)\n    const y = (major + x) * Math.sin(u)\n    x = (major + x) * Math.cos(u)\n\n    target.set(x, y, z)\n  },\n  TubeGeometry,\n  TorusKnotGeometry: class TorusKnotGeometry extends TubeGeometry {\n    constructor(radius = 200, tube = 40, segmentsT = 64, segmentsR = 8, p = 2, q = 3) {\n      class TorusKnotCurve extends Curve {\n        getPoint(t, optionalTarget = new Vector3()) {\n          const point = optionalTarget\n\n          t *= Math.PI * 2\n\n          const r = 0.5\n\n          const x = (1 + r * Math.cos(q * t)) * Math.cos(p * t)\n          const y = (1 + r * Math.cos(q * t)) * Math.sin(p * t)\n          const z = r * Math.sin(q * t)\n\n          return point.set(x, y, z).multiplyScalar(radius)\n        }\n      }\n\n      const segments = segmentsT\n      const radiusSegments = segmentsR\n      const extrudePath = new TorusKnotCurve()\n\n      super(extrudePath, segments, tube, radiusSegments, true, false)\n\n      this.radius = radius\n      this.tube = tube\n      this.segmentsT = segmentsT\n      this.segmentsR = segmentsR\n      this.p = p\n      this.q = q\n    }\n  },\n  SphereGeometry: class SphereGeometry extends ParametricGeometry {\n    constructor(size, u, v) {\n      function sphere(u, v, target) {\n        u *= Math.PI\n        v *= 2 * Math.PI\n\n        const x = size * Math.sin(u) * Math.cos(v)\n        const y = size * Math.sin(u) * Math.sin(v)\n        const z = size * Math.cos(u)\n\n        target.set(x, y, z)\n      }\n\n      super(sphere, u, v)\n    }\n  },\n  PlaneGeometry: class PlaneGeometry extends ParametricGeometry {\n    constructor(width, depth, segmentsWidth, segmentsDepth) {\n      function plane(u, v, target) {\n        const x = u * width\n        const y = 0\n        const z = v * depth\n\n        target.set(x, y, z)\n      }\n\n      super(plane, segmentsWidth, segmentsDepth)\n    }\n  },\n}\n\nexport { ParametricGeometries }\n"], "mappings": ";;AAGA,MAAMA,YAAA,SAAqBC,kBAAA,CAAmB;EAC5CC,YAAYC,IAAA,EAAMC,QAAA,GAAW,IAAIC,MAAA,GAAS,GAAGC,cAAA,GAAiB,GAAGC,MAAA,GAAS,OAAO;IAC/E,MAAMC,SAAA,GAAYJ,QAAA,GAAW;IAE7B,MAAMK,MAAA,GAASN,IAAA,CAAKO,mBAAA,CAAoBN,QAAA,EAAUG,MAAM;MACtDI,QAAA,GAAWF,MAAA,CAAOE,QAAA;MAClBC,OAAA,GAAUH,MAAA,CAAOG,OAAA;MACjBC,SAAA,GAAYJ,MAAA,CAAOI,SAAA;IAErB,MAAMC,QAAA,GAAW,IAAIC,OAAA,CAAS;IAE9B,SAASC,eAAeC,CAAA,EAAGC,CAAA,EAAGC,MAAA,EAAQ;MACpCD,CAAA,IAAK,IAAIE,IAAA,CAAKC,EAAA;MAEd,MAAMC,CAAA,GAAIF,IAAA,CAAKG,KAAA,CAAMN,CAAA,IAAKT,SAAA,GAAY,EAAE;MAExCL,IAAA,CAAKqB,UAAA,CAAWP,CAAA,EAAGH,QAAQ;MAE3B,MAAMW,MAAA,GAASb,OAAA,CAAQU,CAAC;MACxB,MAAMI,QAAA,GAAWb,SAAA,CAAUS,CAAC;MAE5B,MAAMK,EAAA,GAAK,CAACtB,MAAA,GAASe,IAAA,CAAKQ,GAAA,CAAIV,CAAC;MAC/B,MAAMW,EAAA,GAAKxB,MAAA,GAASe,IAAA,CAAKU,GAAA,CAAIZ,CAAC;MAE9BJ,QAAA,CAASiB,CAAA,IAAKJ,EAAA,GAAKF,MAAA,CAAOM,CAAA,GAAIF,EAAA,GAAKH,QAAA,CAASK,CAAA;MAC5CjB,QAAA,CAASkB,CAAA,IAAKL,EAAA,GAAKF,MAAA,CAAOO,CAAA,GAAIH,EAAA,GAAKH,QAAA,CAASM,CAAA;MAC5ClB,QAAA,CAASmB,CAAA,IAAKN,EAAA,GAAKF,MAAA,CAAOQ,CAAA,GAAIJ,EAAA,GAAKH,QAAA,CAASO,CAAA;MAE5Cd,MAAA,CAAOe,IAAA,CAAKpB,QAAQ;IACrB;IAED,MAAME,cAAA,EAAgBZ,QAAA,EAAUE,cAAc;IAI9C,KAAKK,QAAA,GAAWA,QAAA;IAChB,KAAKC,OAAA,GAAUA,OAAA;IACf,KAAKC,SAAA,GAAYA,SAAA;IAEjB,KAAKV,IAAA,GAAOA,IAAA;IACZ,KAAKC,QAAA,GAAWA,QAAA;IAChB,KAAKC,MAAA,GAASA,MAAA;IACd,KAAKC,cAAA,GAAiBA,cAAA;IACtB,KAAKC,MAAA,GAASA,MAAA;EACf;AACH;AAKK,MAAC4B,oBAAA,GAAuB;EAC3BC,KAAA,EAAO,SAAAA,CAAUlB,CAAA,EAAGD,CAAA,EAAGE,MAAA,EAAQ;IAC7BF,CAAA,IAAKG,IAAA,CAAKC,EAAA;IACVH,CAAA,IAAK,IAAIE,IAAA,CAAKC,EAAA;IAEdJ,CAAA,GAAIA,CAAA,GAAI;IACR,IAAIc,CAAA,EAAGE,CAAA;IACP,IAAIhB,CAAA,GAAIG,IAAA,CAAKC,EAAA,EAAI;MACfU,CAAA,GAAI,IAAIX,IAAA,CAAKQ,GAAA,CAAIX,CAAC,KAAK,IAAIG,IAAA,CAAKU,GAAA,CAAIb,CAAC,KAAK,KAAK,IAAIG,IAAA,CAAKQ,GAAA,CAAIX,CAAC,IAAI,KAAKG,IAAA,CAAKQ,GAAA,CAAIX,CAAC,IAAIG,IAAA,CAAKQ,GAAA,CAAIV,CAAC;MAC9Fe,CAAA,GAAI,KAAKb,IAAA,CAAKU,GAAA,CAAIb,CAAC,IAAI,KAAK,IAAIG,IAAA,CAAKQ,GAAA,CAAIX,CAAC,IAAI,KAAKG,IAAA,CAAKU,GAAA,CAAIb,CAAC,IAAIG,IAAA,CAAKQ,GAAA,CAAIV,CAAC;IACjF,OAAW;MACLa,CAAA,GAAI,IAAIX,IAAA,CAAKQ,GAAA,CAAIX,CAAC,KAAK,IAAIG,IAAA,CAAKU,GAAA,CAAIb,CAAC,KAAK,KAAK,IAAIG,IAAA,CAAKQ,GAAA,CAAIX,CAAC,IAAI,KAAKG,IAAA,CAAKQ,GAAA,CAAIV,CAAA,GAAIE,IAAA,CAAKC,EAAE;MAC1FY,CAAA,GAAI,KAAKb,IAAA,CAAKU,GAAA,CAAIb,CAAC;IACpB;IAED,MAAMe,CAAA,GAAI,MAAM,IAAIZ,IAAA,CAAKQ,GAAA,CAAIX,CAAC,IAAI,KAAKG,IAAA,CAAKU,GAAA,CAAIZ,CAAC;IAEjDC,MAAA,CAAOkB,GAAA,CAAIN,CAAA,EAAGC,CAAA,EAAGC,CAAC;EACnB;EAEDK,KAAA,EAAO,SAAAA,CAAUC,KAAA,EAAOC,MAAA,EAAQ;IAC9B,OAAO,UAAUvB,CAAA,EAAGC,CAAA,EAAGC,MAAA,EAAQ;MAC7B,MAAMY,CAAA,GAAId,CAAA,GAAIsB,KAAA;MACd,MAAMP,CAAA,GAAI;MACV,MAAMC,CAAA,GAAIf,CAAA,GAAIsB,MAAA;MAEdrB,MAAA,CAAOkB,GAAA,CAAIN,CAAA,EAAGC,CAAA,EAAGC,CAAC;IACnB;EACF;EAEDQ,MAAA,EAAQ,SAAAA,CAAUxB,CAAA,EAAGyB,CAAA,EAAGvB,MAAA,EAAQ;IAG9BF,CAAA,GAAIA,CAAA,GAAI;IACR,MAAMC,CAAA,GAAI,IAAIE,IAAA,CAAKC,EAAA,GAAKqB,CAAA;IAExB,MAAMC,CAAA,GAAI;IAEV,MAAMZ,CAAA,GAAIX,IAAA,CAAKQ,GAAA,CAAIV,CAAC,KAAKyB,CAAA,GAAI1B,CAAA,GAAIG,IAAA,CAAKQ,GAAA,CAAIV,CAAA,GAAI,CAAC;IAC/C,MAAMc,CAAA,GAAIZ,IAAA,CAAKU,GAAA,CAAIZ,CAAC,KAAKyB,CAAA,GAAI1B,CAAA,GAAIG,IAAA,CAAKQ,GAAA,CAAIV,CAAA,GAAI,CAAC;IAC/C,MAAMe,CAAA,GAAIhB,CAAA,GAAIG,IAAA,CAAKU,GAAA,CAAIZ,CAAA,GAAI,CAAC;IAE5BC,MAAA,CAAOkB,GAAA,CAAIN,CAAA,EAAGC,CAAA,EAAGC,CAAC;EACnB;EAEDW,QAAA,EAAU,SAAAA,CAAU3B,CAAA,EAAGyB,CAAA,EAAGvB,MAAA,EAAQ;IAGhCF,CAAA,IAAKG,IAAA,CAAKC,EAAA;IACVqB,CAAA,IAAK,IAAItB,IAAA,CAAKC,EAAA;IAEdJ,CAAA,GAAIA,CAAA,GAAI;IACR,MAAM4B,GAAA,GAAM5B,CAAA,GAAI;IAChB,MAAM6B,KAAA,GAAQ;MACZH,CAAA,GAAI;MACJI,CAAA,GAAI;IAEN,IAAIhB,CAAA,GAAIY,CAAA,GAAIvB,IAAA,CAAKQ,GAAA,CAAIc,CAAC,IAAItB,IAAA,CAAKQ,GAAA,CAAIiB,GAAG,IAAIE,CAAA,GAAI3B,IAAA,CAAKU,GAAA,CAAIY,CAAC,IAAItB,IAAA,CAAKU,GAAA,CAAIe,GAAG;IACxE,MAAMZ,CAAA,GAAIU,CAAA,GAAIvB,IAAA,CAAKQ,GAAA,CAAIc,CAAC,IAAItB,IAAA,CAAKU,GAAA,CAAIe,GAAG,IAAIE,CAAA,GAAI3B,IAAA,CAAKU,GAAA,CAAIY,CAAC,IAAItB,IAAA,CAAKQ,GAAA,CAAIiB,GAAG;IAC1E,MAAMb,CAAA,IAAKc,KAAA,GAAQf,CAAA,IAAKX,IAAA,CAAKU,GAAA,CAAIb,CAAC;IAClCc,CAAA,IAAKe,KAAA,GAAQf,CAAA,IAAKX,IAAA,CAAKQ,GAAA,CAAIX,CAAC;IAE5BE,MAAA,CAAOkB,GAAA,CAAIN,CAAA,EAAGC,CAAA,EAAGC,CAAC;EACnB;EACDjC,YAAA;EACAgD,iBAAA,EAAmB,MAAMA,iBAAA,SAA0BhD,YAAA,CAAa;IAC9DE,YAAYG,MAAA,GAAS,KAAK4C,IAAA,GAAO,IAAIC,SAAA,GAAY,IAAIC,SAAA,GAAY,GAAGC,CAAA,GAAI,GAAGC,CAAA,GAAI,GAAG;MAChF,MAAMC,cAAA,SAAuBC,KAAA,CAAM;QACjCC,SAASd,CAAA,EAAGe,cAAA,GAAiB,IAAI1C,OAAA,CAAO,GAAI;UAC1C,MAAM2C,KAAA,GAAQD,cAAA;UAEdf,CAAA,IAAKtB,IAAA,CAAKC,EAAA,GAAK;UAEf,MAAMsC,CAAA,GAAI;UAEV,MAAM5B,CAAA,IAAK,IAAI4B,CAAA,GAAIvC,IAAA,CAAKQ,GAAA,CAAIyB,CAAA,GAAIX,CAAC,KAAKtB,IAAA,CAAKQ,GAAA,CAAIwB,CAAA,GAAIV,CAAC;UACpD,MAAMV,CAAA,IAAK,IAAI2B,CAAA,GAAIvC,IAAA,CAAKQ,GAAA,CAAIyB,CAAA,GAAIX,CAAC,KAAKtB,IAAA,CAAKU,GAAA,CAAIsB,CAAA,GAAIV,CAAC;UACpD,MAAMT,CAAA,GAAI0B,CAAA,GAAIvC,IAAA,CAAKU,GAAA,CAAIuB,CAAA,GAAIX,CAAC;UAE5B,OAAOgB,KAAA,CAAMrB,GAAA,CAAIN,CAAA,EAAGC,CAAA,EAAGC,CAAC,EAAE2B,cAAA,CAAevD,MAAM;QAChD;MACF;MAED,MAAMD,QAAA,GAAW8C,SAAA;MACjB,MAAMW,cAAA,GAAiBV,SAAA;MACvB,MAAMW,WAAA,GAAc,IAAIR,cAAA,CAAgB;MAExC,MAAMQ,WAAA,EAAa1D,QAAA,EAAU6C,IAAA,EAAMY,cAAA,EAAgB,MAAM,KAAK;MAE9D,KAAKxD,MAAA,GAASA,MAAA;MACd,KAAK4C,IAAA,GAAOA,IAAA;MACZ,KAAKC,SAAA,GAAYA,SAAA;MACjB,KAAKC,SAAA,GAAYA,SAAA;MACjB,KAAKC,CAAA,GAAIA,CAAA;MACT,KAAKC,CAAA,GAAIA,CAAA;IACV;EACF;EACDU,cAAA,EAAgB,MAAMA,cAAA,SAAuB9D,kBAAA,CAAmB;IAC9DC,YAAY8D,IAAA,EAAM/C,CAAA,EAAGC,CAAA,EAAG;MACtB,SAAS+C,OAAOC,EAAA,EAAGC,EAAA,EAAGhD,MAAA,EAAQ;QAC5B+C,EAAA,IAAK9C,IAAA,CAAKC,EAAA;QACV8C,EAAA,IAAK,IAAI/C,IAAA,CAAKC,EAAA;QAEd,MAAMU,CAAA,GAAIiC,IAAA,GAAO5C,IAAA,CAAKU,GAAA,CAAIoC,EAAC,IAAI9C,IAAA,CAAKQ,GAAA,CAAIuC,EAAC;QACzC,MAAMnC,CAAA,GAAIgC,IAAA,GAAO5C,IAAA,CAAKU,GAAA,CAAIoC,EAAC,IAAI9C,IAAA,CAAKU,GAAA,CAAIqC,EAAC;QACzC,MAAMlC,CAAA,GAAI+B,IAAA,GAAO5C,IAAA,CAAKQ,GAAA,CAAIsC,EAAC;QAE3B/C,MAAA,CAAOkB,GAAA,CAAIN,CAAA,EAAGC,CAAA,EAAGC,CAAC;MACnB;MAED,MAAMgC,MAAA,EAAQhD,CAAA,EAAGC,CAAC;IACnB;EACF;EACDkD,aAAA,EAAe,MAAMA,aAAA,SAAsBnE,kBAAA,CAAmB;IAC5DC,YAAYqC,KAAA,EAAO8B,KAAA,EAAOC,aAAA,EAAeC,aAAA,EAAe;MACtD,SAASjC,MAAMrB,CAAA,EAAGC,CAAA,EAAGC,MAAA,EAAQ;QAC3B,MAAMY,CAAA,GAAId,CAAA,GAAIsB,KAAA;QACd,MAAMP,CAAA,GAAI;QACV,MAAMC,CAAA,GAAIf,CAAA,GAAImD,KAAA;QAEdlD,MAAA,CAAOkB,GAAA,CAAIN,CAAA,EAAGC,CAAA,EAAGC,CAAC;MACnB;MAED,MAAMK,KAAA,EAAOgC,aAAA,EAAeC,aAAa;IAC1C;EACF;AACH", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}