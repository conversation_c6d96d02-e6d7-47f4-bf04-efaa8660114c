{"ast": null, "code": "import * as React from 'react';\nimport * as THREE from 'three';\nimport { useThree, useFrame } from '@react-three/fiber';\nvar AnimationState = /*#__PURE__*/function (AnimationState) {\n  AnimationState[AnimationState[\"NONE\"] = 0] = \"NONE\";\n  AnimationState[AnimationState[\"START\"] = 1] = \"START\";\n  AnimationState[AnimationState[\"ACTIVE\"] = 2] = \"ACTIVE\";\n  return AnimationState;\n}(AnimationState || {});\nconst isOrthographic = def => def && def.isOrthographicCamera;\nconst isBox3 = def => def && def.isBox3;\nconst interpolateFuncDefault = t => {\n  // Imitates the previously used THREE.MathUtils.damp\n  return 1 - Math.exp(-5 * t) + 0.007 * t;\n};\nconst context = /*#__PURE__*/React.createContext(null);\nfunction Bounds({\n  children,\n  maxDuration = 1.0,\n  margin = 1.2,\n  observe,\n  fit,\n  clip,\n  interpolateFunc = interpolateFuncDefault,\n  onFit\n}) {\n  const ref = React.useRef(null);\n  const {\n    camera,\n    size,\n    invalidate\n  } = useThree();\n  const controls = useThree(state => state.controls);\n  const onFitRef = React.useRef(onFit);\n  onFitRef.current = onFit;\n  const origin = React.useRef({\n    camPos: new THREE.Vector3(),\n    camRot: new THREE.Quaternion(),\n    camZoom: 1\n  });\n  const goal = React.useRef({\n    camPos: undefined,\n    camRot: undefined,\n    camZoom: undefined,\n    camUp: undefined,\n    target: undefined\n  });\n  const animationState = React.useRef(AnimationState.NONE);\n  const t = React.useRef(0); // represent animation state from 0 to 1\n\n  const [box] = React.useState(() => new THREE.Box3());\n  const api = React.useMemo(() => {\n    function getSize() {\n      const boxSize = box.getSize(new THREE.Vector3());\n      const center = box.getCenter(new THREE.Vector3());\n      const maxSize = Math.max(boxSize.x, boxSize.y, boxSize.z);\n      const fitHeightDistance = isOrthographic(camera) ? maxSize * 4 : maxSize / (2 * Math.atan(Math.PI * camera.fov / 360));\n      const fitWidthDistance = isOrthographic(camera) ? maxSize * 4 : fitHeightDistance / camera.aspect;\n      const distance = margin * Math.max(fitHeightDistance, fitWidthDistance);\n      return {\n        box,\n        size: boxSize,\n        center,\n        distance\n      };\n    }\n    return {\n      getSize,\n      refresh(object) {\n        if (isBox3(object)) box.copy(object);else {\n          const target = object || ref.current;\n          if (!target) return this;\n          target.updateWorldMatrix(true, true);\n          box.setFromObject(target);\n        }\n        if (box.isEmpty()) {\n          const max = camera.position.length() || 10;\n          box.setFromCenterAndSize(new THREE.Vector3(), new THREE.Vector3(max, max, max));\n        }\n        origin.current.camPos.copy(camera.position);\n        origin.current.camRot.copy(camera.quaternion);\n        isOrthographic(camera) && (origin.current.camZoom = camera.zoom);\n        goal.current.camPos = undefined;\n        goal.current.camRot = undefined;\n        goal.current.camZoom = undefined;\n        goal.current.camUp = undefined;\n        goal.current.target = undefined;\n        return this;\n      },\n      reset() {\n        const {\n          center,\n          distance\n        } = getSize();\n        const direction = camera.position.clone().sub(center).normalize();\n        goal.current.camPos = center.clone().addScaledVector(direction, distance);\n        goal.current.target = center.clone();\n        const mCamRot = new THREE.Matrix4().lookAt(goal.current.camPos, goal.current.target, camera.up);\n        goal.current.camRot = new THREE.Quaternion().setFromRotationMatrix(mCamRot);\n        animationState.current = AnimationState.START;\n        t.current = 0;\n        return this;\n      },\n      moveTo(position) {\n        goal.current.camPos = Array.isArray(position) ? new THREE.Vector3(...position) : position.clone();\n        animationState.current = AnimationState.START;\n        t.current = 0;\n        return this;\n      },\n      lookAt({\n        target,\n        up\n      }) {\n        goal.current.target = Array.isArray(target) ? new THREE.Vector3(...target) : target.clone();\n        if (up) {\n          goal.current.camUp = Array.isArray(up) ? new THREE.Vector3(...up) : up.clone();\n        } else {\n          goal.current.camUp = camera.up.clone();\n        }\n        const mCamRot = new THREE.Matrix4().lookAt(goal.current.camPos || camera.position, goal.current.target, goal.current.camUp);\n        goal.current.camRot = new THREE.Quaternion().setFromRotationMatrix(mCamRot);\n        animationState.current = AnimationState.START;\n        t.current = 0;\n        return this;\n      },\n      /**\n       * @deprecated Use moveTo and lookAt instead\n       */\n      to({\n        position,\n        target\n      }) {\n        return this.moveTo(position).lookAt({\n          target\n        });\n      },\n      fit() {\n        if (!isOrthographic(camera)) {\n          // For non-orthographic cameras, fit should behave exactly like reset\n          return this.reset();\n        }\n\n        // For orthographic cameras, fit should only modify the zoom value\n        let maxHeight = 0,\n          maxWidth = 0;\n        const vertices = [new THREE.Vector3(box.min.x, box.min.y, box.min.z), new THREE.Vector3(box.min.x, box.max.y, box.min.z), new THREE.Vector3(box.min.x, box.min.y, box.max.z), new THREE.Vector3(box.min.x, box.max.y, box.max.z), new THREE.Vector3(box.max.x, box.max.y, box.max.z), new THREE.Vector3(box.max.x, box.max.y, box.min.z), new THREE.Vector3(box.max.x, box.min.y, box.max.z), new THREE.Vector3(box.max.x, box.min.y, box.min.z)];\n\n        // Transform the center and each corner to camera space\n        const pos = goal.current.camPos || camera.position;\n        const target = goal.current.target || (controls == null ? void 0 : controls.target);\n        const up = goal.current.camUp || camera.up;\n        const mCamWInv = target ? new THREE.Matrix4().lookAt(pos, target, up).setPosition(pos).invert() : camera.matrixWorldInverse;\n        for (const v of vertices) {\n          v.applyMatrix4(mCamWInv);\n          maxHeight = Math.max(maxHeight, Math.abs(v.y));\n          maxWidth = Math.max(maxWidth, Math.abs(v.x));\n        }\n        maxHeight *= 2;\n        maxWidth *= 2;\n        const zoomForHeight = (camera.top - camera.bottom) / maxHeight;\n        const zoomForWidth = (camera.right - camera.left) / maxWidth;\n        goal.current.camZoom = Math.min(zoomForHeight, zoomForWidth) / margin;\n        animationState.current = AnimationState.START;\n        t.current = 0;\n        onFitRef.current && onFitRef.current(this.getSize());\n        return this;\n      },\n      clip() {\n        const {\n          distance\n        } = getSize();\n        camera.near = distance / 100;\n        camera.far = distance * 100;\n        camera.updateProjectionMatrix();\n        if (controls) {\n          controls.maxDistance = distance * 10;\n          controls.update();\n        }\n        invalidate();\n        return this;\n      }\n    };\n  }, [box, camera, controls, margin, invalidate]);\n  React.useLayoutEffect(() => {\n    if (controls) {\n      // Try to prevent drag hijacking\n      const callback = () => {\n        if (controls && goal.current.target && animationState.current !== AnimationState.NONE) {\n          const front = new THREE.Vector3().setFromMatrixColumn(camera.matrix, 2);\n          const d0 = origin.current.camPos.distanceTo(controls.target);\n          const d1 = (goal.current.camPos || origin.current.camPos).distanceTo(goal.current.target);\n          const d = (1 - t.current) * d0 + t.current * d1;\n          controls.target.copy(camera.position).addScaledVector(front, -d);\n          controls.update();\n        }\n        animationState.current = AnimationState.NONE;\n      };\n      controls.addEventListener('start', callback);\n      return () => controls.removeEventListener('start', callback);\n    }\n  }, [controls]);\n\n  // Scale pointer on window resize\n  const count = React.useRef(0);\n  React.useLayoutEffect(() => {\n    if (observe || count.current++ === 0) {\n      api.refresh();\n      if (fit) api.reset().fit();\n      if (clip) api.clip();\n    }\n  }, [size, clip, fit, observe, camera, controls]);\n  useFrame((state, delta) => {\n    // This [additional animation step START] is needed to guarantee that delta used in animation isn't absurdly high (2-3 seconds) which is actually possible if rendering happens on demand...\n    if (animationState.current === AnimationState.START) {\n      animationState.current = AnimationState.ACTIVE;\n      invalidate();\n    } else if (animationState.current === AnimationState.ACTIVE) {\n      t.current += delta / maxDuration;\n      if (t.current >= 1) {\n        goal.current.camPos && camera.position.copy(goal.current.camPos);\n        goal.current.camRot && camera.quaternion.copy(goal.current.camRot);\n        goal.current.camUp && camera.up.copy(goal.current.camUp);\n        goal.current.camZoom && isOrthographic(camera) && (camera.zoom = goal.current.camZoom);\n        camera.updateMatrixWorld();\n        camera.updateProjectionMatrix();\n        if (controls && goal.current.target) {\n          controls.target.copy(goal.current.target);\n          controls.update();\n        }\n        animationState.current = AnimationState.NONE;\n      } else {\n        const k = interpolateFunc(t.current);\n        goal.current.camPos && camera.position.lerpVectors(origin.current.camPos, goal.current.camPos, k);\n        goal.current.camRot && camera.quaternion.slerpQuaternions(origin.current.camRot, goal.current.camRot, k);\n        goal.current.camUp && camera.up.set(0, 1, 0).applyQuaternion(camera.quaternion);\n        goal.current.camZoom && isOrthographic(camera) && (camera.zoom = (1 - k) * origin.current.camZoom + k * goal.current.camZoom);\n        camera.updateMatrixWorld();\n        camera.updateProjectionMatrix();\n      }\n      invalidate();\n    }\n  });\n  return /*#__PURE__*/React.createElement(\"group\", {\n    ref: ref\n  }, /*#__PURE__*/React.createElement(context.Provider, {\n    value: api\n  }, children));\n}\nfunction useBounds() {\n  return React.useContext(context);\n}\nexport { Bounds, useBounds };", "map": {"version": 3, "names": ["React", "THREE", "useThree", "useFrame", "AnimationState", "isOrthographic", "def", "isOrthographicCamera", "isBox3", "interpolate<PERSON><PERSON><PERSON><PERSON><PERSON>ault", "t", "Math", "exp", "context", "createContext", "Bounds", "children", "maxDuration", "margin", "observe", "fit", "clip", "interpolateFunc", "onFit", "ref", "useRef", "camera", "size", "invalidate", "controls", "state", "onFitRef", "current", "origin", "camPos", "Vector3", "camRot", "Quaternion", "camZoom", "goal", "undefined", "camUp", "target", "animationState", "NONE", "box", "useState", "Box3", "api", "useMemo", "getSize", "boxSize", "center", "getCenter", "maxSize", "max", "x", "y", "z", "fitHeightDistance", "atan", "PI", "fov", "fitWidthDistance", "aspect", "distance", "refresh", "object", "copy", "updateWorldMatrix", "setFromObject", "isEmpty", "position", "length", "setFromCenterAndSize", "quaternion", "zoom", "reset", "direction", "clone", "sub", "normalize", "addScaledVector", "mCamRot", "Matrix4", "lookAt", "up", "setFromRotationMatrix", "START", "moveTo", "Array", "isArray", "to", "maxHeight", "max<PERSON><PERSON><PERSON>", "vertices", "min", "pos", "mCamWInv", "setPosition", "invert", "matrixWorldInverse", "v", "applyMatrix4", "abs", "zoomForHeight", "top", "bottom", "zoomFor<PERSON><PERSON><PERSON>", "right", "left", "near", "far", "updateProjectionMatrix", "maxDistance", "update", "useLayoutEffect", "callback", "front", "setFromMatrixColumn", "matrix", "d0", "distanceTo", "d1", "d", "addEventListener", "removeEventListener", "count", "delta", "ACTIVE", "updateMatrixWorld", "k", "lerpVectors", "slerpQuaternions", "set", "applyQuaternion", "createElement", "Provider", "value", "useBounds", "useContext"], "sources": ["C:/Users/<USER>/Documents/Apps_Creations/SiahMan_Racing4Speed_WebGame/siah-man-racing/node_modules/@react-three/drei/core/Bounds.js"], "sourcesContent": ["import * as React from 'react';\nimport * as THREE from 'three';\nimport { useThree, useFrame } from '@react-three/fiber';\n\nvar AnimationState = /*#__PURE__*/function (AnimationState) {\n  AnimationState[AnimationState[\"NONE\"] = 0] = \"NONE\";\n  AnimationState[AnimationState[\"START\"] = 1] = \"START\";\n  AnimationState[AnimationState[\"ACTIVE\"] = 2] = \"ACTIVE\";\n  return AnimationState;\n}(AnimationState || {});\nconst isOrthographic = def => def && def.isOrthographicCamera;\nconst isBox3 = def => def && def.isBox3;\nconst interpolateFuncDefault = t => {\n  // Imitates the previously used THREE.MathUtils.damp\n  return 1 - Math.exp(-5 * t) + 0.007 * t;\n};\nconst context = /*#__PURE__*/React.createContext(null);\nfunction Bounds({\n  children,\n  maxDuration = 1.0,\n  margin = 1.2,\n  observe,\n  fit,\n  clip,\n  interpolateFunc = interpolateFuncDefault,\n  onFit\n}) {\n  const ref = React.useRef(null);\n  const {\n    camera,\n    size,\n    invalidate\n  } = useThree();\n  const controls = useThree(state => state.controls);\n  const onFitRef = React.useRef(onFit);\n  onFitRef.current = onFit;\n  const origin = React.useRef({\n    camPos: new THREE.Vector3(),\n    camRot: new THREE.Quaternion(),\n    camZoom: 1\n  });\n  const goal = React.useRef({\n    camPos: undefined,\n    camRot: undefined,\n    camZoom: undefined,\n    camUp: undefined,\n    target: undefined\n  });\n  const animationState = React.useRef(AnimationState.NONE);\n  const t = React.useRef(0); // represent animation state from 0 to 1\n\n  const [box] = React.useState(() => new THREE.Box3());\n  const api = React.useMemo(() => {\n    function getSize() {\n      const boxSize = box.getSize(new THREE.Vector3());\n      const center = box.getCenter(new THREE.Vector3());\n      const maxSize = Math.max(boxSize.x, boxSize.y, boxSize.z);\n      const fitHeightDistance = isOrthographic(camera) ? maxSize * 4 : maxSize / (2 * Math.atan(Math.PI * camera.fov / 360));\n      const fitWidthDistance = isOrthographic(camera) ? maxSize * 4 : fitHeightDistance / camera.aspect;\n      const distance = margin * Math.max(fitHeightDistance, fitWidthDistance);\n      return {\n        box,\n        size: boxSize,\n        center,\n        distance\n      };\n    }\n    return {\n      getSize,\n      refresh(object) {\n        if (isBox3(object)) box.copy(object);else {\n          const target = object || ref.current;\n          if (!target) return this;\n          target.updateWorldMatrix(true, true);\n          box.setFromObject(target);\n        }\n        if (box.isEmpty()) {\n          const max = camera.position.length() || 10;\n          box.setFromCenterAndSize(new THREE.Vector3(), new THREE.Vector3(max, max, max));\n        }\n        origin.current.camPos.copy(camera.position);\n        origin.current.camRot.copy(camera.quaternion);\n        isOrthographic(camera) && (origin.current.camZoom = camera.zoom);\n        goal.current.camPos = undefined;\n        goal.current.camRot = undefined;\n        goal.current.camZoom = undefined;\n        goal.current.camUp = undefined;\n        goal.current.target = undefined;\n        return this;\n      },\n      reset() {\n        const {\n          center,\n          distance\n        } = getSize();\n        const direction = camera.position.clone().sub(center).normalize();\n        goal.current.camPos = center.clone().addScaledVector(direction, distance);\n        goal.current.target = center.clone();\n        const mCamRot = new THREE.Matrix4().lookAt(goal.current.camPos, goal.current.target, camera.up);\n        goal.current.camRot = new THREE.Quaternion().setFromRotationMatrix(mCamRot);\n        animationState.current = AnimationState.START;\n        t.current = 0;\n        return this;\n      },\n      moveTo(position) {\n        goal.current.camPos = Array.isArray(position) ? new THREE.Vector3(...position) : position.clone();\n        animationState.current = AnimationState.START;\n        t.current = 0;\n        return this;\n      },\n      lookAt({\n        target,\n        up\n      }) {\n        goal.current.target = Array.isArray(target) ? new THREE.Vector3(...target) : target.clone();\n        if (up) {\n          goal.current.camUp = Array.isArray(up) ? new THREE.Vector3(...up) : up.clone();\n        } else {\n          goal.current.camUp = camera.up.clone();\n        }\n        const mCamRot = new THREE.Matrix4().lookAt(goal.current.camPos || camera.position, goal.current.target, goal.current.camUp);\n        goal.current.camRot = new THREE.Quaternion().setFromRotationMatrix(mCamRot);\n        animationState.current = AnimationState.START;\n        t.current = 0;\n        return this;\n      },\n      /**\n       * @deprecated Use moveTo and lookAt instead\n       */\n      to({\n        position,\n        target\n      }) {\n        return this.moveTo(position).lookAt({\n          target\n        });\n      },\n      fit() {\n        if (!isOrthographic(camera)) {\n          // For non-orthographic cameras, fit should behave exactly like reset\n          return this.reset();\n        }\n\n        // For orthographic cameras, fit should only modify the zoom value\n        let maxHeight = 0,\n          maxWidth = 0;\n        const vertices = [new THREE.Vector3(box.min.x, box.min.y, box.min.z), new THREE.Vector3(box.min.x, box.max.y, box.min.z), new THREE.Vector3(box.min.x, box.min.y, box.max.z), new THREE.Vector3(box.min.x, box.max.y, box.max.z), new THREE.Vector3(box.max.x, box.max.y, box.max.z), new THREE.Vector3(box.max.x, box.max.y, box.min.z), new THREE.Vector3(box.max.x, box.min.y, box.max.z), new THREE.Vector3(box.max.x, box.min.y, box.min.z)];\n\n        // Transform the center and each corner to camera space\n        const pos = goal.current.camPos || camera.position;\n        const target = goal.current.target || (controls == null ? void 0 : controls.target);\n        const up = goal.current.camUp || camera.up;\n        const mCamWInv = target ? new THREE.Matrix4().lookAt(pos, target, up).setPosition(pos).invert() : camera.matrixWorldInverse;\n        for (const v of vertices) {\n          v.applyMatrix4(mCamWInv);\n          maxHeight = Math.max(maxHeight, Math.abs(v.y));\n          maxWidth = Math.max(maxWidth, Math.abs(v.x));\n        }\n        maxHeight *= 2;\n        maxWidth *= 2;\n        const zoomForHeight = (camera.top - camera.bottom) / maxHeight;\n        const zoomForWidth = (camera.right - camera.left) / maxWidth;\n        goal.current.camZoom = Math.min(zoomForHeight, zoomForWidth) / margin;\n        animationState.current = AnimationState.START;\n        t.current = 0;\n        onFitRef.current && onFitRef.current(this.getSize());\n        return this;\n      },\n      clip() {\n        const {\n          distance\n        } = getSize();\n        camera.near = distance / 100;\n        camera.far = distance * 100;\n        camera.updateProjectionMatrix();\n        if (controls) {\n          controls.maxDistance = distance * 10;\n          controls.update();\n        }\n        invalidate();\n        return this;\n      }\n    };\n  }, [box, camera, controls, margin, invalidate]);\n  React.useLayoutEffect(() => {\n    if (controls) {\n      // Try to prevent drag hijacking\n      const callback = () => {\n        if (controls && goal.current.target && animationState.current !== AnimationState.NONE) {\n          const front = new THREE.Vector3().setFromMatrixColumn(camera.matrix, 2);\n          const d0 = origin.current.camPos.distanceTo(controls.target);\n          const d1 = (goal.current.camPos || origin.current.camPos).distanceTo(goal.current.target);\n          const d = (1 - t.current) * d0 + t.current * d1;\n          controls.target.copy(camera.position).addScaledVector(front, -d);\n          controls.update();\n        }\n        animationState.current = AnimationState.NONE;\n      };\n      controls.addEventListener('start', callback);\n      return () => controls.removeEventListener('start', callback);\n    }\n  }, [controls]);\n\n  // Scale pointer on window resize\n  const count = React.useRef(0);\n  React.useLayoutEffect(() => {\n    if (observe || count.current++ === 0) {\n      api.refresh();\n      if (fit) api.reset().fit();\n      if (clip) api.clip();\n    }\n  }, [size, clip, fit, observe, camera, controls]);\n  useFrame((state, delta) => {\n    // This [additional animation step START] is needed to guarantee that delta used in animation isn't absurdly high (2-3 seconds) which is actually possible if rendering happens on demand...\n    if (animationState.current === AnimationState.START) {\n      animationState.current = AnimationState.ACTIVE;\n      invalidate();\n    } else if (animationState.current === AnimationState.ACTIVE) {\n      t.current += delta / maxDuration;\n      if (t.current >= 1) {\n        goal.current.camPos && camera.position.copy(goal.current.camPos);\n        goal.current.camRot && camera.quaternion.copy(goal.current.camRot);\n        goal.current.camUp && camera.up.copy(goal.current.camUp);\n        goal.current.camZoom && isOrthographic(camera) && (camera.zoom = goal.current.camZoom);\n        camera.updateMatrixWorld();\n        camera.updateProjectionMatrix();\n        if (controls && goal.current.target) {\n          controls.target.copy(goal.current.target);\n          controls.update();\n        }\n        animationState.current = AnimationState.NONE;\n      } else {\n        const k = interpolateFunc(t.current);\n        goal.current.camPos && camera.position.lerpVectors(origin.current.camPos, goal.current.camPos, k);\n        goal.current.camRot && camera.quaternion.slerpQuaternions(origin.current.camRot, goal.current.camRot, k);\n        goal.current.camUp && camera.up.set(0, 1, 0).applyQuaternion(camera.quaternion);\n        goal.current.camZoom && isOrthographic(camera) && (camera.zoom = (1 - k) * origin.current.camZoom + k * goal.current.camZoom);\n        camera.updateMatrixWorld();\n        camera.updateProjectionMatrix();\n      }\n      invalidate();\n    }\n  });\n  return /*#__PURE__*/React.createElement(\"group\", {\n    ref: ref\n  }, /*#__PURE__*/React.createElement(context.Provider, {\n    value: api\n  }, children));\n}\nfunction useBounds() {\n  return React.useContext(context);\n}\n\nexport { Bounds, useBounds };\n"], "mappings": "AAAA,OAAO,KAAKA,KAAK,MAAM,OAAO;AAC9B,OAAO,KAAKC,KAAK,MAAM,OAAO;AAC9B,SAASC,QAAQ,EAAEC,QAAQ,QAAQ,oBAAoB;AAEvD,IAAIC,cAAc,GAAG,aAAa,UAAUA,cAAc,EAAE;EAC1DA,cAAc,CAACA,cAAc,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC,GAAG,MAAM;EACnDA,cAAc,CAACA,cAAc,CAAC,OAAO,CAAC,GAAG,CAAC,CAAC,GAAG,OAAO;EACrDA,cAAc,CAACA,cAAc,CAAC,QAAQ,CAAC,GAAG,CAAC,CAAC,GAAG,QAAQ;EACvD,OAAOA,cAAc;AACvB,CAAC,CAACA,cAAc,IAAI,CAAC,CAAC,CAAC;AACvB,MAAMC,cAAc,GAAGC,GAAG,IAAIA,GAAG,IAAIA,GAAG,CAACC,oBAAoB;AAC7D,MAAMC,MAAM,GAAGF,GAAG,IAAIA,GAAG,IAAIA,GAAG,CAACE,MAAM;AACvC,MAAMC,sBAAsB,GAAGC,CAAC,IAAI;EAClC;EACA,OAAO,CAAC,GAAGC,IAAI,CAACC,GAAG,CAAC,CAAC,CAAC,GAAGF,CAAC,CAAC,GAAG,KAAK,GAAGA,CAAC;AACzC,CAAC;AACD,MAAMG,OAAO,GAAG,aAAab,KAAK,CAACc,aAAa,CAAC,IAAI,CAAC;AACtD,SAASC,MAAMA,CAAC;EACdC,QAAQ;EACRC,WAAW,GAAG,GAAG;EACjBC,MAAM,GAAG,GAAG;EACZC,OAAO;EACPC,GAAG;EACHC,IAAI;EACJC,eAAe,GAAGb,sBAAsB;EACxCc;AACF,CAAC,EAAE;EACD,MAAMC,GAAG,GAAGxB,KAAK,CAACyB,MAAM,CAAC,IAAI,CAAC;EAC9B,MAAM;IACJC,MAAM;IACNC,IAAI;IACJC;EACF,CAAC,GAAG1B,QAAQ,CAAC,CAAC;EACd,MAAM2B,QAAQ,GAAG3B,QAAQ,CAAC4B,KAAK,IAAIA,KAAK,CAACD,QAAQ,CAAC;EAClD,MAAME,QAAQ,GAAG/B,KAAK,CAACyB,MAAM,CAACF,KAAK,CAAC;EACpCQ,QAAQ,CAACC,OAAO,GAAGT,KAAK;EACxB,MAAMU,MAAM,GAAGjC,KAAK,CAACyB,MAAM,CAAC;IAC1BS,MAAM,EAAE,IAAIjC,KAAK,CAACkC,OAAO,CAAC,CAAC;IAC3BC,MAAM,EAAE,IAAInC,KAAK,CAACoC,UAAU,CAAC,CAAC;IAC9BC,OAAO,EAAE;EACX,CAAC,CAAC;EACF,MAAMC,IAAI,GAAGvC,KAAK,CAACyB,MAAM,CAAC;IACxBS,MAAM,EAAEM,SAAS;IACjBJ,MAAM,EAAEI,SAAS;IACjBF,OAAO,EAAEE,SAAS;IAClBC,KAAK,EAAED,SAAS;IAChBE,MAAM,EAAEF;EACV,CAAC,CAAC;EACF,MAAMG,cAAc,GAAG3C,KAAK,CAACyB,MAAM,CAACrB,cAAc,CAACwC,IAAI,CAAC;EACxD,MAAMlC,CAAC,GAAGV,KAAK,CAACyB,MAAM,CAAC,CAAC,CAAC,CAAC,CAAC;;EAE3B,MAAM,CAACoB,GAAG,CAAC,GAAG7C,KAAK,CAAC8C,QAAQ,CAAC,MAAM,IAAI7C,KAAK,CAAC8C,IAAI,CAAC,CAAC,CAAC;EACpD,MAAMC,GAAG,GAAGhD,KAAK,CAACiD,OAAO,CAAC,MAAM;IAC9B,SAASC,OAAOA,CAAA,EAAG;MACjB,MAAMC,OAAO,GAAGN,GAAG,CAACK,OAAO,CAAC,IAAIjD,KAAK,CAACkC,OAAO,CAAC,CAAC,CAAC;MAChD,MAAMiB,MAAM,GAAGP,GAAG,CAACQ,SAAS,CAAC,IAAIpD,KAAK,CAACkC,OAAO,CAAC,CAAC,CAAC;MACjD,MAAMmB,OAAO,GAAG3C,IAAI,CAAC4C,GAAG,CAACJ,OAAO,CAACK,CAAC,EAAEL,OAAO,CAACM,CAAC,EAAEN,OAAO,CAACO,CAAC,CAAC;MACzD,MAAMC,iBAAiB,GAAGtD,cAAc,CAACqB,MAAM,CAAC,GAAG4B,OAAO,GAAG,CAAC,GAAGA,OAAO,IAAI,CAAC,GAAG3C,IAAI,CAACiD,IAAI,CAACjD,IAAI,CAACkD,EAAE,GAAGnC,MAAM,CAACoC,GAAG,GAAG,GAAG,CAAC,CAAC;MACtH,MAAMC,gBAAgB,GAAG1D,cAAc,CAACqB,MAAM,CAAC,GAAG4B,OAAO,GAAG,CAAC,GAAGK,iBAAiB,GAAGjC,MAAM,CAACsC,MAAM;MACjG,MAAMC,QAAQ,GAAG/C,MAAM,GAAGP,IAAI,CAAC4C,GAAG,CAACI,iBAAiB,EAAEI,gBAAgB,CAAC;MACvE,OAAO;QACLlB,GAAG;QACHlB,IAAI,EAAEwB,OAAO;QACbC,MAAM;QACNa;MACF,CAAC;IACH;IACA,OAAO;MACLf,OAAO;MACPgB,OAAOA,CAACC,MAAM,EAAE;QACd,IAAI3D,MAAM,CAAC2D,MAAM,CAAC,EAAEtB,GAAG,CAACuB,IAAI,CAACD,MAAM,CAAC,CAAC,KAAK;UACxC,MAAMzB,MAAM,GAAGyB,MAAM,IAAI3C,GAAG,CAACQ,OAAO;UACpC,IAAI,CAACU,MAAM,EAAE,OAAO,IAAI;UACxBA,MAAM,CAAC2B,iBAAiB,CAAC,IAAI,EAAE,IAAI,CAAC;UACpCxB,GAAG,CAACyB,aAAa,CAAC5B,MAAM,CAAC;QAC3B;QACA,IAAIG,GAAG,CAAC0B,OAAO,CAAC,CAAC,EAAE;UACjB,MAAMhB,GAAG,GAAG7B,MAAM,CAAC8C,QAAQ,CAACC,MAAM,CAAC,CAAC,IAAI,EAAE;UAC1C5B,GAAG,CAAC6B,oBAAoB,CAAC,IAAIzE,KAAK,CAACkC,OAAO,CAAC,CAAC,EAAE,IAAIlC,KAAK,CAACkC,OAAO,CAACoB,GAAG,EAAEA,GAAG,EAAEA,GAAG,CAAC,CAAC;QACjF;QACAtB,MAAM,CAACD,OAAO,CAACE,MAAM,CAACkC,IAAI,CAAC1C,MAAM,CAAC8C,QAAQ,CAAC;QAC3CvC,MAAM,CAACD,OAAO,CAACI,MAAM,CAACgC,IAAI,CAAC1C,MAAM,CAACiD,UAAU,CAAC;QAC7CtE,cAAc,CAACqB,MAAM,CAAC,KAAKO,MAAM,CAACD,OAAO,CAACM,OAAO,GAAGZ,MAAM,CAACkD,IAAI,CAAC;QAChErC,IAAI,CAACP,OAAO,CAACE,MAAM,GAAGM,SAAS;QAC/BD,IAAI,CAACP,OAAO,CAACI,MAAM,GAAGI,SAAS;QAC/BD,IAAI,CAACP,OAAO,CAACM,OAAO,GAAGE,SAAS;QAChCD,IAAI,CAACP,OAAO,CAACS,KAAK,GAAGD,SAAS;QAC9BD,IAAI,CAACP,OAAO,CAACU,MAAM,GAAGF,SAAS;QAC/B,OAAO,IAAI;MACb,CAAC;MACDqC,KAAKA,CAAA,EAAG;QACN,MAAM;UACJzB,MAAM;UACNa;QACF,CAAC,GAAGf,OAAO,CAAC,CAAC;QACb,MAAM4B,SAAS,GAAGpD,MAAM,CAAC8C,QAAQ,CAACO,KAAK,CAAC,CAAC,CAACC,GAAG,CAAC5B,MAAM,CAAC,CAAC6B,SAAS,CAAC,CAAC;QACjE1C,IAAI,CAACP,OAAO,CAACE,MAAM,GAAGkB,MAAM,CAAC2B,KAAK,CAAC,CAAC,CAACG,eAAe,CAACJ,SAAS,EAAEb,QAAQ,CAAC;QACzE1B,IAAI,CAACP,OAAO,CAACU,MAAM,GAAGU,MAAM,CAAC2B,KAAK,CAAC,CAAC;QACpC,MAAMI,OAAO,GAAG,IAAIlF,KAAK,CAACmF,OAAO,CAAC,CAAC,CAACC,MAAM,CAAC9C,IAAI,CAACP,OAAO,CAACE,MAAM,EAAEK,IAAI,CAACP,OAAO,CAACU,MAAM,EAAEhB,MAAM,CAAC4D,EAAE,CAAC;QAC/F/C,IAAI,CAACP,OAAO,CAACI,MAAM,GAAG,IAAInC,KAAK,CAACoC,UAAU,CAAC,CAAC,CAACkD,qBAAqB,CAACJ,OAAO,CAAC;QAC3ExC,cAAc,CAACX,OAAO,GAAG5B,cAAc,CAACoF,KAAK;QAC7C9E,CAAC,CAACsB,OAAO,GAAG,CAAC;QACb,OAAO,IAAI;MACb,CAAC;MACDyD,MAAMA,CAACjB,QAAQ,EAAE;QACfjC,IAAI,CAACP,OAAO,CAACE,MAAM,GAAGwD,KAAK,CAACC,OAAO,CAACnB,QAAQ,CAAC,GAAG,IAAIvE,KAAK,CAACkC,OAAO,CAAC,GAAGqC,QAAQ,CAAC,GAAGA,QAAQ,CAACO,KAAK,CAAC,CAAC;QACjGpC,cAAc,CAACX,OAAO,GAAG5B,cAAc,CAACoF,KAAK;QAC7C9E,CAAC,CAACsB,OAAO,GAAG,CAAC;QACb,OAAO,IAAI;MACb,CAAC;MACDqD,MAAMA,CAAC;QACL3C,MAAM;QACN4C;MACF,CAAC,EAAE;QACD/C,IAAI,CAACP,OAAO,CAACU,MAAM,GAAGgD,KAAK,CAACC,OAAO,CAACjD,MAAM,CAAC,GAAG,IAAIzC,KAAK,CAACkC,OAAO,CAAC,GAAGO,MAAM,CAAC,GAAGA,MAAM,CAACqC,KAAK,CAAC,CAAC;QAC3F,IAAIO,EAAE,EAAE;UACN/C,IAAI,CAACP,OAAO,CAACS,KAAK,GAAGiD,KAAK,CAACC,OAAO,CAACL,EAAE,CAAC,GAAG,IAAIrF,KAAK,CAACkC,OAAO,CAAC,GAAGmD,EAAE,CAAC,GAAGA,EAAE,CAACP,KAAK,CAAC,CAAC;QAChF,CAAC,MAAM;UACLxC,IAAI,CAACP,OAAO,CAACS,KAAK,GAAGf,MAAM,CAAC4D,EAAE,CAACP,KAAK,CAAC,CAAC;QACxC;QACA,MAAMI,OAAO,GAAG,IAAIlF,KAAK,CAACmF,OAAO,CAAC,CAAC,CAACC,MAAM,CAAC9C,IAAI,CAACP,OAAO,CAACE,MAAM,IAAIR,MAAM,CAAC8C,QAAQ,EAAEjC,IAAI,CAACP,OAAO,CAACU,MAAM,EAAEH,IAAI,CAACP,OAAO,CAACS,KAAK,CAAC;QAC3HF,IAAI,CAACP,OAAO,CAACI,MAAM,GAAG,IAAInC,KAAK,CAACoC,UAAU,CAAC,CAAC,CAACkD,qBAAqB,CAACJ,OAAO,CAAC;QAC3ExC,cAAc,CAACX,OAAO,GAAG5B,cAAc,CAACoF,KAAK;QAC7C9E,CAAC,CAACsB,OAAO,GAAG,CAAC;QACb,OAAO,IAAI;MACb,CAAC;MACD;AACN;AACA;MACM4D,EAAEA,CAAC;QACDpB,QAAQ;QACR9B;MACF,CAAC,EAAE;QACD,OAAO,IAAI,CAAC+C,MAAM,CAACjB,QAAQ,CAAC,CAACa,MAAM,CAAC;UAClC3C;QACF,CAAC,CAAC;MACJ,CAAC;MACDtB,GAAGA,CAAA,EAAG;QACJ,IAAI,CAACf,cAAc,CAACqB,MAAM,CAAC,EAAE;UAC3B;UACA,OAAO,IAAI,CAACmD,KAAK,CAAC,CAAC;QACrB;;QAEA;QACA,IAAIgB,SAAS,GAAG,CAAC;UACfC,QAAQ,GAAG,CAAC;QACd,MAAMC,QAAQ,GAAG,CAAC,IAAI9F,KAAK,CAACkC,OAAO,CAACU,GAAG,CAACmD,GAAG,CAACxC,CAAC,EAAEX,GAAG,CAACmD,GAAG,CAACvC,CAAC,EAAEZ,GAAG,CAACmD,GAAG,CAACtC,CAAC,CAAC,EAAE,IAAIzD,KAAK,CAACkC,OAAO,CAACU,GAAG,CAACmD,GAAG,CAACxC,CAAC,EAAEX,GAAG,CAACU,GAAG,CAACE,CAAC,EAAEZ,GAAG,CAACmD,GAAG,CAACtC,CAAC,CAAC,EAAE,IAAIzD,KAAK,CAACkC,OAAO,CAACU,GAAG,CAACmD,GAAG,CAACxC,CAAC,EAAEX,GAAG,CAACmD,GAAG,CAACvC,CAAC,EAAEZ,GAAG,CAACU,GAAG,CAACG,CAAC,CAAC,EAAE,IAAIzD,KAAK,CAACkC,OAAO,CAACU,GAAG,CAACmD,GAAG,CAACxC,CAAC,EAAEX,GAAG,CAACU,GAAG,CAACE,CAAC,EAAEZ,GAAG,CAACU,GAAG,CAACG,CAAC,CAAC,EAAE,IAAIzD,KAAK,CAACkC,OAAO,CAACU,GAAG,CAACU,GAAG,CAACC,CAAC,EAAEX,GAAG,CAACU,GAAG,CAACE,CAAC,EAAEZ,GAAG,CAACU,GAAG,CAACG,CAAC,CAAC,EAAE,IAAIzD,KAAK,CAACkC,OAAO,CAACU,GAAG,CAACU,GAAG,CAACC,CAAC,EAAEX,GAAG,CAACU,GAAG,CAACE,CAAC,EAAEZ,GAAG,CAACmD,GAAG,CAACtC,CAAC,CAAC,EAAE,IAAIzD,KAAK,CAACkC,OAAO,CAACU,GAAG,CAACU,GAAG,CAACC,CAAC,EAAEX,GAAG,CAACmD,GAAG,CAACvC,CAAC,EAAEZ,GAAG,CAACU,GAAG,CAACG,CAAC,CAAC,EAAE,IAAIzD,KAAK,CAACkC,OAAO,CAACU,GAAG,CAACU,GAAG,CAACC,CAAC,EAAEX,GAAG,CAACmD,GAAG,CAACvC,CAAC,EAAEZ,GAAG,CAACmD,GAAG,CAACtC,CAAC,CAAC,CAAC;;QAEjb;QACA,MAAMuC,GAAG,GAAG1D,IAAI,CAACP,OAAO,CAACE,MAAM,IAAIR,MAAM,CAAC8C,QAAQ;QAClD,MAAM9B,MAAM,GAAGH,IAAI,CAACP,OAAO,CAACU,MAAM,KAAKb,QAAQ,IAAI,IAAI,GAAG,KAAK,CAAC,GAAGA,QAAQ,CAACa,MAAM,CAAC;QACnF,MAAM4C,EAAE,GAAG/C,IAAI,CAACP,OAAO,CAACS,KAAK,IAAIf,MAAM,CAAC4D,EAAE;QAC1C,MAAMY,QAAQ,GAAGxD,MAAM,GAAG,IAAIzC,KAAK,CAACmF,OAAO,CAAC,CAAC,CAACC,MAAM,CAACY,GAAG,EAAEvD,MAAM,EAAE4C,EAAE,CAAC,CAACa,WAAW,CAACF,GAAG,CAAC,CAACG,MAAM,CAAC,CAAC,GAAG1E,MAAM,CAAC2E,kBAAkB;QAC3H,KAAK,MAAMC,CAAC,IAAIP,QAAQ,EAAE;UACxBO,CAAC,CAACC,YAAY,CAACL,QAAQ,CAAC;UACxBL,SAAS,GAAGlF,IAAI,CAAC4C,GAAG,CAACsC,SAAS,EAAElF,IAAI,CAAC6F,GAAG,CAACF,CAAC,CAAC7C,CAAC,CAAC,CAAC;UAC9CqC,QAAQ,GAAGnF,IAAI,CAAC4C,GAAG,CAACuC,QAAQ,EAAEnF,IAAI,CAAC6F,GAAG,CAACF,CAAC,CAAC9C,CAAC,CAAC,CAAC;QAC9C;QACAqC,SAAS,IAAI,CAAC;QACdC,QAAQ,IAAI,CAAC;QACb,MAAMW,aAAa,GAAG,CAAC/E,MAAM,CAACgF,GAAG,GAAGhF,MAAM,CAACiF,MAAM,IAAId,SAAS;QAC9D,MAAMe,YAAY,GAAG,CAAClF,MAAM,CAACmF,KAAK,GAAGnF,MAAM,CAACoF,IAAI,IAAIhB,QAAQ;QAC5DvD,IAAI,CAACP,OAAO,CAACM,OAAO,GAAG3B,IAAI,CAACqF,GAAG,CAACS,aAAa,EAAEG,YAAY,CAAC,GAAG1F,MAAM;QACrEyB,cAAc,CAACX,OAAO,GAAG5B,cAAc,CAACoF,KAAK;QAC7C9E,CAAC,CAACsB,OAAO,GAAG,CAAC;QACbD,QAAQ,CAACC,OAAO,IAAID,QAAQ,CAACC,OAAO,CAAC,IAAI,CAACkB,OAAO,CAAC,CAAC,CAAC;QACpD,OAAO,IAAI;MACb,CAAC;MACD7B,IAAIA,CAAA,EAAG;QACL,MAAM;UACJ4C;QACF,CAAC,GAAGf,OAAO,CAAC,CAAC;QACbxB,MAAM,CAACqF,IAAI,GAAG9C,QAAQ,GAAG,GAAG;QAC5BvC,MAAM,CAACsF,GAAG,GAAG/C,QAAQ,GAAG,GAAG;QAC3BvC,MAAM,CAACuF,sBAAsB,CAAC,CAAC;QAC/B,IAAIpF,QAAQ,EAAE;UACZA,QAAQ,CAACqF,WAAW,GAAGjD,QAAQ,GAAG,EAAE;UACpCpC,QAAQ,CAACsF,MAAM,CAAC,CAAC;QACnB;QACAvF,UAAU,CAAC,CAAC;QACZ,OAAO,IAAI;MACb;IACF,CAAC;EACH,CAAC,EAAE,CAACiB,GAAG,EAAEnB,MAAM,EAAEG,QAAQ,EAAEX,MAAM,EAAEU,UAAU,CAAC,CAAC;EAC/C5B,KAAK,CAACoH,eAAe,CAAC,MAAM;IAC1B,IAAIvF,QAAQ,EAAE;MACZ;MACA,MAAMwF,QAAQ,GAAGA,CAAA,KAAM;QACrB,IAAIxF,QAAQ,IAAIU,IAAI,CAACP,OAAO,CAACU,MAAM,IAAIC,cAAc,CAACX,OAAO,KAAK5B,cAAc,CAACwC,IAAI,EAAE;UACrF,MAAM0E,KAAK,GAAG,IAAIrH,KAAK,CAACkC,OAAO,CAAC,CAAC,CAACoF,mBAAmB,CAAC7F,MAAM,CAAC8F,MAAM,EAAE,CAAC,CAAC;UACvE,MAAMC,EAAE,GAAGxF,MAAM,CAACD,OAAO,CAACE,MAAM,CAACwF,UAAU,CAAC7F,QAAQ,CAACa,MAAM,CAAC;UAC5D,MAAMiF,EAAE,GAAG,CAACpF,IAAI,CAACP,OAAO,CAACE,MAAM,IAAID,MAAM,CAACD,OAAO,CAACE,MAAM,EAAEwF,UAAU,CAACnF,IAAI,CAACP,OAAO,CAACU,MAAM,CAAC;UACzF,MAAMkF,CAAC,GAAG,CAAC,CAAC,GAAGlH,CAAC,CAACsB,OAAO,IAAIyF,EAAE,GAAG/G,CAAC,CAACsB,OAAO,GAAG2F,EAAE;UAC/C9F,QAAQ,CAACa,MAAM,CAAC0B,IAAI,CAAC1C,MAAM,CAAC8C,QAAQ,CAAC,CAACU,eAAe,CAACoC,KAAK,EAAE,CAACM,CAAC,CAAC;UAChE/F,QAAQ,CAACsF,MAAM,CAAC,CAAC;QACnB;QACAxE,cAAc,CAACX,OAAO,GAAG5B,cAAc,CAACwC,IAAI;MAC9C,CAAC;MACDf,QAAQ,CAACgG,gBAAgB,CAAC,OAAO,EAAER,QAAQ,CAAC;MAC5C,OAAO,MAAMxF,QAAQ,CAACiG,mBAAmB,CAAC,OAAO,EAAET,QAAQ,CAAC;IAC9D;EACF,CAAC,EAAE,CAACxF,QAAQ,CAAC,CAAC;;EAEd;EACA,MAAMkG,KAAK,GAAG/H,KAAK,CAACyB,MAAM,CAAC,CAAC,CAAC;EAC7BzB,KAAK,CAACoH,eAAe,CAAC,MAAM;IAC1B,IAAIjG,OAAO,IAAI4G,KAAK,CAAC/F,OAAO,EAAE,KAAK,CAAC,EAAE;MACpCgB,GAAG,CAACkB,OAAO,CAAC,CAAC;MACb,IAAI9C,GAAG,EAAE4B,GAAG,CAAC6B,KAAK,CAAC,CAAC,CAACzD,GAAG,CAAC,CAAC;MAC1B,IAAIC,IAAI,EAAE2B,GAAG,CAAC3B,IAAI,CAAC,CAAC;IACtB;EACF,CAAC,EAAE,CAACM,IAAI,EAAEN,IAAI,EAAED,GAAG,EAAED,OAAO,EAAEO,MAAM,EAAEG,QAAQ,CAAC,CAAC;EAChD1B,QAAQ,CAAC,CAAC2B,KAAK,EAAEkG,KAAK,KAAK;IACzB;IACA,IAAIrF,cAAc,CAACX,OAAO,KAAK5B,cAAc,CAACoF,KAAK,EAAE;MACnD7C,cAAc,CAACX,OAAO,GAAG5B,cAAc,CAAC6H,MAAM;MAC9CrG,UAAU,CAAC,CAAC;IACd,CAAC,MAAM,IAAIe,cAAc,CAACX,OAAO,KAAK5B,cAAc,CAAC6H,MAAM,EAAE;MAC3DvH,CAAC,CAACsB,OAAO,IAAIgG,KAAK,GAAG/G,WAAW;MAChC,IAAIP,CAAC,CAACsB,OAAO,IAAI,CAAC,EAAE;QAClBO,IAAI,CAACP,OAAO,CAACE,MAAM,IAAIR,MAAM,CAAC8C,QAAQ,CAACJ,IAAI,CAAC7B,IAAI,CAACP,OAAO,CAACE,MAAM,CAAC;QAChEK,IAAI,CAACP,OAAO,CAACI,MAAM,IAAIV,MAAM,CAACiD,UAAU,CAACP,IAAI,CAAC7B,IAAI,CAACP,OAAO,CAACI,MAAM,CAAC;QAClEG,IAAI,CAACP,OAAO,CAACS,KAAK,IAAIf,MAAM,CAAC4D,EAAE,CAAClB,IAAI,CAAC7B,IAAI,CAACP,OAAO,CAACS,KAAK,CAAC;QACxDF,IAAI,CAACP,OAAO,CAACM,OAAO,IAAIjC,cAAc,CAACqB,MAAM,CAAC,KAAKA,MAAM,CAACkD,IAAI,GAAGrC,IAAI,CAACP,OAAO,CAACM,OAAO,CAAC;QACtFZ,MAAM,CAACwG,iBAAiB,CAAC,CAAC;QAC1BxG,MAAM,CAACuF,sBAAsB,CAAC,CAAC;QAC/B,IAAIpF,QAAQ,IAAIU,IAAI,CAACP,OAAO,CAACU,MAAM,EAAE;UACnCb,QAAQ,CAACa,MAAM,CAAC0B,IAAI,CAAC7B,IAAI,CAACP,OAAO,CAACU,MAAM,CAAC;UACzCb,QAAQ,CAACsF,MAAM,CAAC,CAAC;QACnB;QACAxE,cAAc,CAACX,OAAO,GAAG5B,cAAc,CAACwC,IAAI;MAC9C,CAAC,MAAM;QACL,MAAMuF,CAAC,GAAG7G,eAAe,CAACZ,CAAC,CAACsB,OAAO,CAAC;QACpCO,IAAI,CAACP,OAAO,CAACE,MAAM,IAAIR,MAAM,CAAC8C,QAAQ,CAAC4D,WAAW,CAACnG,MAAM,CAACD,OAAO,CAACE,MAAM,EAAEK,IAAI,CAACP,OAAO,CAACE,MAAM,EAAEiG,CAAC,CAAC;QACjG5F,IAAI,CAACP,OAAO,CAACI,MAAM,IAAIV,MAAM,CAACiD,UAAU,CAAC0D,gBAAgB,CAACpG,MAAM,CAACD,OAAO,CAACI,MAAM,EAAEG,IAAI,CAACP,OAAO,CAACI,MAAM,EAAE+F,CAAC,CAAC;QACxG5F,IAAI,CAACP,OAAO,CAACS,KAAK,IAAIf,MAAM,CAAC4D,EAAE,CAACgD,GAAG,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAACC,eAAe,CAAC7G,MAAM,CAACiD,UAAU,CAAC;QAC/EpC,IAAI,CAACP,OAAO,CAACM,OAAO,IAAIjC,cAAc,CAACqB,MAAM,CAAC,KAAKA,MAAM,CAACkD,IAAI,GAAG,CAAC,CAAC,GAAGuD,CAAC,IAAIlG,MAAM,CAACD,OAAO,CAACM,OAAO,GAAG6F,CAAC,GAAG5F,IAAI,CAACP,OAAO,CAACM,OAAO,CAAC;QAC7HZ,MAAM,CAACwG,iBAAiB,CAAC,CAAC;QAC1BxG,MAAM,CAACuF,sBAAsB,CAAC,CAAC;MACjC;MACArF,UAAU,CAAC,CAAC;IACd;EACF,CAAC,CAAC;EACF,OAAO,aAAa5B,KAAK,CAACwI,aAAa,CAAC,OAAO,EAAE;IAC/ChH,GAAG,EAAEA;EACP,CAAC,EAAE,aAAaxB,KAAK,CAACwI,aAAa,CAAC3H,OAAO,CAAC4H,QAAQ,EAAE;IACpDC,KAAK,EAAE1F;EACT,CAAC,EAAEhC,QAAQ,CAAC,CAAC;AACf;AACA,SAAS2H,SAASA,CAAA,EAAG;EACnB,OAAO3I,KAAK,CAAC4I,UAAU,CAAC/H,OAAO,CAAC;AAClC;AAEA,SAASE,MAAM,EAAE4H,SAAS", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}