{"ast": null, "code": "import _extends from '@babel/runtime/helpers/esm/extends';\nimport * as React from 'react';\nimport { Sky as Sky$1 } from 'three-stdlib';\nimport { Vector3 } from 'three';\nfunction calcPosFromAngles(inclination, azimuth, vector = new Vector3()) {\n  const theta = Math.PI * (inclination - 0.5);\n  const phi = 2 * Math.PI * (azimuth - 0.5);\n  vector.x = Math.cos(phi);\n  vector.y = Math.sin(theta);\n  vector.z = Math.sin(phi);\n  return vector;\n}\nconst Sky = /* @__PURE__ */React.forwardRef(({\n  inclination = 0.6,\n  azimuth = 0.1,\n  distance = 1000,\n  mieCoefficient = 0.005,\n  mieDirectionalG = 0.8,\n  rayleigh = 0.5,\n  turbidity = 10,\n  sunPosition = calcPosFromAngles(inclination, azimuth),\n  ...props\n}, ref) => {\n  const scale = React.useMemo(() => new Vector3().setScalar(distance), [distance]);\n  const [sky] = React.useState(() => new Sky$1());\n  return /*#__PURE__*/React.createElement(\"primitive\", _extends({\n    object: sky,\n    ref: ref,\n    \"material-uniforms-mieCoefficient-value\": mieCoefficient,\n    \"material-uniforms-mieDirectionalG-value\": mieDirectionalG,\n    \"material-uniforms-rayleigh-value\": rayleigh,\n    \"material-uniforms-sunPosition-value\": sunPosition,\n    \"material-uniforms-turbidity-value\": turbidity,\n    scale: scale\n  }, props));\n});\nexport { Sky, calcPosFromAngles };", "map": {"version": 3, "names": ["_extends", "React", "Sky", "Sky$1", "Vector3", "calcPosFromAngles", "inclination", "azimuth", "vector", "theta", "Math", "PI", "phi", "x", "cos", "y", "sin", "z", "forwardRef", "distance", "mieCoefficient", "mieDirectionalG", "<PERSON><PERSON>", "turbidity", "sunPosition", "props", "ref", "scale", "useMemo", "setScalar", "sky", "useState", "createElement", "object"], "sources": ["C:/Users/<USER>/Documents/Apps_Creations/SiahMan_Racing4Speed_WebGame/siah-man-racing/node_modules/@react-three/drei/core/Sky.js"], "sourcesContent": ["import _extends from '@babel/runtime/helpers/esm/extends';\nimport * as React from 'react';\nimport { Sky as Sky$1 } from 'three-stdlib';\nimport { Vector3 } from 'three';\n\nfunction calcPosFromAngles(inclination, azimuth, vector = new Vector3()) {\n  const theta = Math.PI * (inclination - 0.5);\n  const phi = 2 * Math.PI * (azimuth - 0.5);\n  vector.x = Math.cos(phi);\n  vector.y = Math.sin(theta);\n  vector.z = Math.sin(phi);\n  return vector;\n}\nconst Sky = /* @__PURE__ */React.forwardRef(({\n  inclination = 0.6,\n  azimuth = 0.1,\n  distance = 1000,\n  mieCoefficient = 0.005,\n  mieDirectionalG = 0.8,\n  rayleigh = 0.5,\n  turbidity = 10,\n  sunPosition = calcPosFromAngles(inclination, azimuth),\n  ...props\n}, ref) => {\n  const scale = React.useMemo(() => new Vector3().setScalar(distance), [distance]);\n  const [sky] = React.useState(() => new Sky$1());\n  return /*#__PURE__*/React.createElement(\"primitive\", _extends({\n    object: sky,\n    ref: ref,\n    \"material-uniforms-mieCoefficient-value\": mieCoefficient,\n    \"material-uniforms-mieDirectionalG-value\": mieDirectionalG,\n    \"material-uniforms-rayleigh-value\": rayleigh,\n    \"material-uniforms-sunPosition-value\": sunPosition,\n    \"material-uniforms-turbidity-value\": turbidity,\n    scale: scale\n  }, props));\n});\n\nexport { Sky, calcPosFromAngles };\n"], "mappings": "AAAA,OAAOA,QAAQ,MAAM,oCAAoC;AACzD,OAAO,KAAKC,KAAK,MAAM,OAAO;AAC9B,SAASC,GAAG,IAAIC,KAAK,QAAQ,cAAc;AAC3C,SAASC,OAAO,QAAQ,OAAO;AAE/B,SAASC,iBAAiBA,CAACC,WAAW,EAAEC,OAAO,EAAEC,MAAM,GAAG,IAAIJ,OAAO,CAAC,CAAC,EAAE;EACvE,MAAMK,KAAK,GAAGC,IAAI,CAACC,EAAE,IAAIL,WAAW,GAAG,GAAG,CAAC;EAC3C,MAAMM,GAAG,GAAG,CAAC,GAAGF,IAAI,CAACC,EAAE,IAAIJ,OAAO,GAAG,GAAG,CAAC;EACzCC,MAAM,CAACK,CAAC,GAAGH,IAAI,CAACI,GAAG,CAACF,GAAG,CAAC;EACxBJ,MAAM,CAACO,CAAC,GAAGL,IAAI,CAACM,GAAG,CAACP,KAAK,CAAC;EAC1BD,MAAM,CAACS,CAAC,GAAGP,IAAI,CAACM,GAAG,CAACJ,GAAG,CAAC;EACxB,OAAOJ,MAAM;AACf;AACA,MAAMN,GAAG,GAAG,eAAeD,KAAK,CAACiB,UAAU,CAAC,CAAC;EAC3CZ,WAAW,GAAG,GAAG;EACjBC,OAAO,GAAG,GAAG;EACbY,QAAQ,GAAG,IAAI;EACfC,cAAc,GAAG,KAAK;EACtBC,eAAe,GAAG,GAAG;EACrBC,QAAQ,GAAG,GAAG;EACdC,SAAS,GAAG,EAAE;EACdC,WAAW,GAAGnB,iBAAiB,CAACC,WAAW,EAAEC,OAAO,CAAC;EACrD,GAAGkB;AACL,CAAC,EAAEC,GAAG,KAAK;EACT,MAAMC,KAAK,GAAG1B,KAAK,CAAC2B,OAAO,CAAC,MAAM,IAAIxB,OAAO,CAAC,CAAC,CAACyB,SAAS,CAACV,QAAQ,CAAC,EAAE,CAACA,QAAQ,CAAC,CAAC;EAChF,MAAM,CAACW,GAAG,CAAC,GAAG7B,KAAK,CAAC8B,QAAQ,CAAC,MAAM,IAAI5B,KAAK,CAAC,CAAC,CAAC;EAC/C,OAAO,aAAaF,KAAK,CAAC+B,aAAa,CAAC,WAAW,EAAEhC,QAAQ,CAAC;IAC5DiC,MAAM,EAAEH,GAAG;IACXJ,GAAG,EAAEA,GAAG;IACR,wCAAwC,EAAEN,cAAc;IACxD,yCAAyC,EAAEC,eAAe;IAC1D,kCAAkC,EAAEC,QAAQ;IAC5C,qCAAqC,EAAEE,WAAW;IAClD,mCAAmC,EAAED,SAAS;IAC9CI,KAAK,EAAEA;EACT,CAAC,EAAEF,KAAK,CAAC,CAAC;AACZ,CAAC,CAAC;AAEF,SAASvB,GAAG,EAAEG,iBAAiB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}