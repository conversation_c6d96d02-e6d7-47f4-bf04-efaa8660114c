{"ast": null, "code": "const FilmShader = {\n  uniforms: {\n    tDiffuse: {\n      value: null\n    },\n    time: {\n      value: 0\n    },\n    nIntensity: {\n      value: 0.5\n    },\n    sIntensity: {\n      value: 0.05\n    },\n    sCount: {\n      value: 4096\n    },\n    grayscale: {\n      value: 1\n    }\n  },\n  vertexShader: (/* glsl */\n  `\n    varying vec2 vUv;\n\n    void main() {\n\n    \tvUv = uv;\n    \tgl_Position = projectionMatrix * modelViewMatrix * vec4( position, 1.0 );\n\n    }\n  `),\n  fragmentShader: (/* glsl */\n  `\n    #include <common>\n\n    // control parameter\n    uniform float time;\n\n    uniform bool grayscale;\n\n    // noise effect intensity value (0 = no effect, 1 = full effect)\n    uniform float nIntensity;\n\n    // scanlines effect intensity value (0 = no effect, 1 = full effect)\n    uniform float sIntensity;\n\n    // scanlines effect count value (0 = no effect, 4096 = full effect)\n    uniform float sCount;\n\n    uniform sampler2D tDiffuse;\n\n    varying vec2 vUv;\n\n    void main() {\n\n    // sample the source\n    \tvec4 cTextureScreen = texture2D( tDiffuse, vUv );\n\n    // make some noise\n    \tfloat dx = rand( vUv + time );\n\n    // add noise\n    \tvec3 cResult = cTextureScreen.rgb + cTextureScreen.rgb * clamp( 0.1 + dx, 0.0, 1.0 );\n\n    // get us a sine and cosine\n    \tvec2 sc = vec2( sin( vUv.y * sCount ), cos( vUv.y * sCount ) );\n\n    // add scanlines\n    \tcResult += cTextureScreen.rgb * vec3( sc.x, sc.y, sc.x ) * sIntensity;\n\n    // interpolate between source and result by intensity\n    \tcResult = cTextureScreen.rgb + clamp( nIntensity, 0.0,1.0 ) * ( cResult - cTextureScreen.rgb );\n\n    // convert to grayscale if desired\n    \tif( grayscale ) {\n\n    \t\tcResult = vec3( cResult.r * 0.3 + cResult.g * 0.59 + cResult.b * 0.11 );\n\n    \t}\n\n    \tgl_FragColor =  vec4( cResult, cTextureScreen.a );\n\n    }\n  `)\n};\nexport { FilmShader };", "map": {"version": 3, "names": ["FilmShader", "uniforms", "tDiffuse", "value", "time", "nIntensity", "sIntensity", "sCount", "grayscale", "vertexShader", "fragmentShader"], "sources": ["C:\\Users\\<USER>\\Documents\\Apps_Creations\\SiahMan_Racing4Speed_WebGame\\siah-man-racing\\node_modules\\src\\shaders\\FilmShader.ts"], "sourcesContent": ["/**\n * Film grain & scanlines shader\n *\n * - ported from HLSL to WebGL / GLSL\n * http://www.truevision3d.com/forums/showcase/staticnoise_colorblackwhite_scanline_shaders-t18698.0.html\n *\n * Screen Space Static Postprocessor\n *\n * Produces an analogue noise overlay similar to a film grain / TV static\n *\n * Original implementation and noise algorithm\n * Pat 'Hawthorne' Shearon\n *\n * Optimized scanlines + noise version with intensity scaling\n * Georg '<PERSON><PERSON>' <PERSON>\n *\n * This version is provided under a Creative Commons Attribution 3.0 License\n * http://creativecommons.org/licenses/by/3.0/\n */\n\nexport const FilmShader = {\n  uniforms: {\n    tDiffuse: { value: null },\n    time: { value: 0.0 },\n    nIntensity: { value: 0.5 },\n    sIntensity: { value: 0.05 },\n    sCount: { value: 4096 },\n    grayscale: { value: 1 },\n  },\n\n  vertexShader: /* glsl */ `\n    varying vec2 vUv;\n\n    void main() {\n\n    \tvUv = uv;\n    \tgl_Position = projectionMatrix * modelViewMatrix * vec4( position, 1.0 );\n\n    }\n  `,\n\n  fragmentShader: /* glsl */ `\n    #include <common>\n\n    // control parameter\n    uniform float time;\n\n    uniform bool grayscale;\n\n    // noise effect intensity value (0 = no effect, 1 = full effect)\n    uniform float nIntensity;\n\n    // scanlines effect intensity value (0 = no effect, 1 = full effect)\n    uniform float sIntensity;\n\n    // scanlines effect count value (0 = no effect, 4096 = full effect)\n    uniform float sCount;\n\n    uniform sampler2D tDiffuse;\n\n    varying vec2 vUv;\n\n    void main() {\n\n    // sample the source\n    \tvec4 cTextureScreen = texture2D( tDiffuse, vUv );\n\n    // make some noise\n    \tfloat dx = rand( vUv + time );\n\n    // add noise\n    \tvec3 cResult = cTextureScreen.rgb + cTextureScreen.rgb * clamp( 0.1 + dx, 0.0, 1.0 );\n\n    // get us a sine and cosine\n    \tvec2 sc = vec2( sin( vUv.y * sCount ), cos( vUv.y * sCount ) );\n\n    // add scanlines\n    \tcResult += cTextureScreen.rgb * vec3( sc.x, sc.y, sc.x ) * sIntensity;\n\n    // interpolate between source and result by intensity\n    \tcResult = cTextureScreen.rgb + clamp( nIntensity, 0.0,1.0 ) * ( cResult - cTextureScreen.rgb );\n\n    // convert to grayscale if desired\n    \tif( grayscale ) {\n\n    \t\tcResult = vec3( cResult.r * 0.3 + cResult.g * 0.59 + cResult.b * 0.11 );\n\n    \t}\n\n    \tgl_FragColor =  vec4( cResult, cTextureScreen.a );\n\n    }\n  `,\n}\n"], "mappings": "AAoBO,MAAMA,UAAA,GAAa;EACxBC,QAAA,EAAU;IACRC,QAAA,EAAU;MAAEC,KAAA,EAAO;IAAK;IACxBC,IAAA,EAAM;MAAED,KAAA,EAAO;IAAI;IACnBE,UAAA,EAAY;MAAEF,KAAA,EAAO;IAAI;IACzBG,UAAA,EAAY;MAAEH,KAAA,EAAO;IAAK;IAC1BI,MAAA,EAAQ;MAAEJ,KAAA,EAAO;IAAK;IACtBK,SAAA,EAAW;MAAEL,KAAA,EAAO;IAAE;EACxB;EAEAM,YAAA;EAAyB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;EAWzBC,cAAA;EAA2B;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAoD7B", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}