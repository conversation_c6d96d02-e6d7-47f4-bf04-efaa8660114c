{"ast": null, "code": "import * as THREE from 'three';\nimport { version } from '../helpers/constants.js';\nclass SpotLightMaterial extends THREE.ShaderMaterial {\n  constructor() {\n    super({\n      uniforms: {\n        depth: {\n          value: null\n        },\n        opacity: {\n          value: 1\n        },\n        attenuation: {\n          value: 2.5\n        },\n        anglePower: {\n          value: 12\n        },\n        spotPosition: {\n          value: new THREE.Vector3(0, 0, 0)\n        },\n        lightColor: {\n          value: new THREE.Color('white')\n        },\n        cameraNear: {\n          value: 0\n        },\n        cameraFar: {\n          value: 1\n        },\n        resolution: {\n          value: new THREE.Vector2(0, 0)\n        }\n      },\n      transparent: true,\n      depthWrite: false,\n      vertexShader: /* glsl */`\n        varying vec3 vNormal;\n        varying float vViewZ;\n        varying float vIntensity;\n        uniform vec3 spotPosition;\n        uniform float attenuation;\n\n        #include <common>\n        #include <logdepthbuf_pars_vertex>\n\n        void main() {\n          // compute intensity\n          vNormal = normalize(normalMatrix * normal);\n          vec4 worldPosition = modelMatrix * vec4(position, 1);\n          vec4 viewPosition = viewMatrix * worldPosition;\n          vViewZ = viewPosition.z;\n\n          vIntensity = 1.0 - saturate(distance(worldPosition.xyz, spotPosition) / attenuation);\n\n          gl_Position = projectionMatrix * viewPosition;\n\n          #include <logdepthbuf_vertex>\n        }\n      `,\n      fragmentShader: /* glsl */`\n        varying vec3 vNormal;\n        varying float vViewZ;\n        varying float vIntensity;\n\n        uniform vec3 lightColor;\n        uniform float anglePower;\n        uniform sampler2D depth;\n        uniform vec2 resolution;\n        uniform float cameraNear;\n        uniform float cameraFar;\n        uniform float opacity;\n\n        #include <packing>\n        #include <logdepthbuf_pars_fragment>\n\n        float readDepth(sampler2D depthSampler, vec2 uv) {\n          float fragCoordZ = texture(depthSampler, uv).r;\n\n          // https://github.com/mrdoob/three.js/issues/23072\n          #ifdef USE_LOGDEPTHBUF\n            float viewZ = 1.0 - exp2(fragCoordZ * log(cameraFar + 1.0) / log(2.0));\n          #else\n            float viewZ = perspectiveDepthToViewZ(fragCoordZ, cameraNear, cameraFar);\n          #endif\n\n          return viewZ;\n        }\n\n        void main() {\n          #include <logdepthbuf_fragment>\n\n          vec3 normal = vec3(vNormal.x, vNormal.y, abs(vNormal.z));\n          float angleIntensity = pow(dot(normal, vec3(0, 0, 1)), anglePower);\n          float intensity = vIntensity * angleIntensity;\n\n          // fades when z is close to sampled depth, meaning the cone is intersecting existing geometry\n          bool isSoft = resolution[0] > 0.0 && resolution[1] > 0.0;\n          if (isSoft) {\n            vec2 uv = gl_FragCoord.xy / resolution;\n            intensity *= smoothstep(0.0, 1.0, vViewZ - readDepth(depth, uv));\n          }\n\n          gl_FragColor = vec4(lightColor, intensity * opacity);\n\n          #include <tonemapping_fragment>\n          #include <${version >= 154 ? 'colorspace_fragment' : 'encodings_fragment'}>\n        }\n      `\n    });\n  }\n}\nexport { SpotLightMaterial };", "map": {"version": 3, "names": ["THREE", "version", "SpotLightMaterial", "ShaderMaterial", "constructor", "uniforms", "depth", "value", "opacity", "attenuation", "anglePower", "spotPosition", "Vector3", "lightColor", "Color", "cameraNear", "cameraFar", "resolution", "Vector2", "transparent", "depthWrite", "vertexShader", "fragmentShader"], "sources": ["C:/Users/<USER>/Documents/Apps_Creations/SiahMan_Racing4Speed_WebGame/siah-man-racing/node_modules/@react-three/drei/materials/SpotLightMaterial.js"], "sourcesContent": ["import * as THREE from 'three';\nimport { version } from '../helpers/constants.js';\n\nclass SpotLightMaterial extends THREE.ShaderMaterial {\n  constructor() {\n    super({\n      uniforms: {\n        depth: {\n          value: null\n        },\n        opacity: {\n          value: 1\n        },\n        attenuation: {\n          value: 2.5\n        },\n        anglePower: {\n          value: 12\n        },\n        spotPosition: {\n          value: new THREE.Vector3(0, 0, 0)\n        },\n        lightColor: {\n          value: new THREE.Color('white')\n        },\n        cameraNear: {\n          value: 0\n        },\n        cameraFar: {\n          value: 1\n        },\n        resolution: {\n          value: new THREE.Vector2(0, 0)\n        }\n      },\n      transparent: true,\n      depthWrite: false,\n      vertexShader: /* glsl */`\n        varying vec3 vNormal;\n        varying float vViewZ;\n        varying float vIntensity;\n        uniform vec3 spotPosition;\n        uniform float attenuation;\n\n        #include <common>\n        #include <logdepthbuf_pars_vertex>\n\n        void main() {\n          // compute intensity\n          vNormal = normalize(normalMatrix * normal);\n          vec4 worldPosition = modelMatrix * vec4(position, 1);\n          vec4 viewPosition = viewMatrix * worldPosition;\n          vViewZ = viewPosition.z;\n\n          vIntensity = 1.0 - saturate(distance(worldPosition.xyz, spotPosition) / attenuation);\n\n          gl_Position = projectionMatrix * viewPosition;\n\n          #include <logdepthbuf_vertex>\n        }\n      `,\n      fragmentShader: /* glsl */`\n        varying vec3 vNormal;\n        varying float vViewZ;\n        varying float vIntensity;\n\n        uniform vec3 lightColor;\n        uniform float anglePower;\n        uniform sampler2D depth;\n        uniform vec2 resolution;\n        uniform float cameraNear;\n        uniform float cameraFar;\n        uniform float opacity;\n\n        #include <packing>\n        #include <logdepthbuf_pars_fragment>\n\n        float readDepth(sampler2D depthSampler, vec2 uv) {\n          float fragCoordZ = texture(depthSampler, uv).r;\n\n          // https://github.com/mrdoob/three.js/issues/23072\n          #ifdef USE_LOGDEPTHBUF\n            float viewZ = 1.0 - exp2(fragCoordZ * log(cameraFar + 1.0) / log(2.0));\n          #else\n            float viewZ = perspectiveDepthToViewZ(fragCoordZ, cameraNear, cameraFar);\n          #endif\n\n          return viewZ;\n        }\n\n        void main() {\n          #include <logdepthbuf_fragment>\n\n          vec3 normal = vec3(vNormal.x, vNormal.y, abs(vNormal.z));\n          float angleIntensity = pow(dot(normal, vec3(0, 0, 1)), anglePower);\n          float intensity = vIntensity * angleIntensity;\n\n          // fades when z is close to sampled depth, meaning the cone is intersecting existing geometry\n          bool isSoft = resolution[0] > 0.0 && resolution[1] > 0.0;\n          if (isSoft) {\n            vec2 uv = gl_FragCoord.xy / resolution;\n            intensity *= smoothstep(0.0, 1.0, vViewZ - readDepth(depth, uv));\n          }\n\n          gl_FragColor = vec4(lightColor, intensity * opacity);\n\n          #include <tonemapping_fragment>\n          #include <${version >= 154 ? 'colorspace_fragment' : 'encodings_fragment'}>\n        }\n      `\n    });\n  }\n}\n\nexport { SpotLightMaterial };\n"], "mappings": "AAAA,OAAO,KAAKA,KAAK,MAAM,OAAO;AAC9B,SAASC,OAAO,QAAQ,yBAAyB;AAEjD,MAAMC,iBAAiB,SAASF,KAAK,CAACG,cAAc,CAAC;EACnDC,WAAWA,CAAA,EAAG;IACZ,KAAK,CAAC;MACJC,QAAQ,EAAE;QACRC,KAAK,EAAE;UACLC,KAAK,EAAE;QACT,CAAC;QACDC,OAAO,EAAE;UACPD,KAAK,EAAE;QACT,CAAC;QACDE,WAAW,EAAE;UACXF,KAAK,EAAE;QACT,CAAC;QACDG,UAAU,EAAE;UACVH,KAAK,EAAE;QACT,CAAC;QACDI,YAAY,EAAE;UACZJ,KAAK,EAAE,IAAIP,KAAK,CAACY,OAAO,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC;QAClC,CAAC;QACDC,UAAU,EAAE;UACVN,KAAK,EAAE,IAAIP,KAAK,CAACc,KAAK,CAAC,OAAO;QAChC,CAAC;QACDC,UAAU,EAAE;UACVR,KAAK,EAAE;QACT,CAAC;QACDS,SAAS,EAAE;UACTT,KAAK,EAAE;QACT,CAAC;QACDU,UAAU,EAAE;UACVV,KAAK,EAAE,IAAIP,KAAK,CAACkB,OAAO,CAAC,CAAC,EAAE,CAAC;QAC/B;MACF,CAAC;MACDC,WAAW,EAAE,IAAI;MACjBC,UAAU,EAAE,KAAK;MACjBC,YAAY,EAAE,UAAU;AAC9B;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO;MACDC,cAAc,EAAE,UAAU;AAChC;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,sBAAsBrB,OAAO,IAAI,GAAG,GAAG,qBAAqB,GAAG,oBAAoB;AACnF;AACA;IACI,CAAC,CAAC;EACJ;AACF;AAEA,SAASC,iBAAiB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}